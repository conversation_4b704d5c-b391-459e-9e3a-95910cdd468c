{"ast": null, "code": "/* asn1-1.0.13.js (c) 2013-2017 <PERSON><PERSON> | kjur.github.com/jsrsasign/license\n */\n/*\n * asn1.js - ASN.1 DER encoder classes\n *\n * Copyright (c) 2013-2017 <PERSON><PERSON> (<EMAIL>)\n *\n * This software is licensed under the terms of the MIT License.\n * https://kjur.github.io/jsrsasign/license\n *\n * The above copyright and license notice shall be\n * included in all copies or substantial portions of the Software.\n */\nimport { BigInteger } from \"../jsbn/jsbn\";\nimport { YAHOO } from \"./yahoo\";\n/**\n * @fileOverview\n * @name asn1-1.0.js\n * <AUTHOR> <EMAIL>\n * @version asn1 1.0.13 (2017-Jun-02)\n * @since jsrsasign 2.1\n * @license <a href=\"https://kjur.github.io/jsrsasign/license/\">MIT License</a>\n */\n/**\n * kju<PERSON>'s class library name space\n * <p>\n * This name space provides following name spaces:\n * <ul>\n * <li>{@link KJUR.asn1} - ASN.1 primitive hexadecimal encoder</li>\n * <li>{@link KJUR.asn1.x509} - ASN.1 structure for X.509 certificate and CRL</li>\n * <li>{@link KJUR.crypto} - Java Cryptographic Extension(JCE) style MessageDigest/Signature\n * class and utilities</li>\n * </ul>\n * </p>\n * NOTE: Please ignore method summary and document of this namespace. This caused by a bug of jsdoc2.\n * @name KJUR\n * @namespace kjur's class library name space\n */\nexport var KJUR = {};\n/**\n * kjur's ASN.1 class library name space\n * <p>\n * This is ITU-T X.690 ASN.1 DER encoder class library and\n * class structure and methods is very similar to\n * org.bouncycastle.asn1 package of\n * well known BouncyCaslte Cryptography Library.\n * <h4>PROVIDING ASN.1 PRIMITIVES</h4>\n * Here are ASN.1 DER primitive classes.\n * <ul>\n * <li>0x01 {@link KJUR.asn1.DERBoolean}</li>\n * <li>0x02 {@link KJUR.asn1.DERInteger}</li>\n * <li>0x03 {@link KJUR.asn1.DERBitString}</li>\n * <li>0x04 {@link KJUR.asn1.DEROctetString}</li>\n * <li>0x05 {@link KJUR.asn1.DERNull}</li>\n * <li>0x06 {@link KJUR.asn1.DERObjectIdentifier}</li>\n * <li>0x0a {@link KJUR.asn1.DEREnumerated}</li>\n * <li>0x0c {@link KJUR.asn1.DERUTF8String}</li>\n * <li>0x12 {@link KJUR.asn1.DERNumericString}</li>\n * <li>0x13 {@link KJUR.asn1.DERPrintableString}</li>\n * <li>0x14 {@link KJUR.asn1.DERTeletexString}</li>\n * <li>0x16 {@link KJUR.asn1.DERIA5String}</li>\n * <li>0x17 {@link KJUR.asn1.DERUTCTime}</li>\n * <li>0x18 {@link KJUR.asn1.DERGeneralizedTime}</li>\n * <li>0x30 {@link KJUR.asn1.DERSequence}</li>\n * <li>0x31 {@link KJUR.asn1.DERSet}</li>\n * </ul>\n * <h4>OTHER ASN.1 CLASSES</h4>\n * <ul>\n * <li>{@link KJUR.asn1.ASN1Object}</li>\n * <li>{@link KJUR.asn1.DERAbstractString}</li>\n * <li>{@link KJUR.asn1.DERAbstractTime}</li>\n * <li>{@link KJUR.asn1.DERAbstractStructured}</li>\n * <li>{@link KJUR.asn1.DERTaggedObject}</li>\n * </ul>\n * <h4>SUB NAME SPACES</h4>\n * <ul>\n * <li>{@link KJUR.asn1.cades} - CAdES long term signature format</li>\n * <li>{@link KJUR.asn1.cms} - Cryptographic Message Syntax</li>\n * <li>{@link KJUR.asn1.csr} - Certificate Signing Request (CSR/PKCS#10)</li>\n * <li>{@link KJUR.asn1.tsp} - RFC 3161 Timestamping Protocol Format</li>\n * <li>{@link KJUR.asn1.x509} - RFC 5280 X.509 certificate and CRL</li>\n * </ul>\n * </p>\n * NOTE: Please ignore method summary and document of this namespace.\n * This caused by a bug of jsdoc2.\n * @name KJUR.asn1\n * @namespace\n */\nif (typeof KJUR.asn1 == \"undefined\" || !KJUR.asn1) KJUR.asn1 = {};\n/**\n * ASN1 utilities class\n * @name KJUR.asn1.ASN1Util\n * @class ASN1 utilities class\n * @since asn1 1.0.2\n */\nKJUR.asn1.ASN1Util = new function () {\n  this.integerToByteHex = function (i) {\n    var h = i.toString(16);\n    if (h.length % 2 == 1) h = '0' + h;\n    return h;\n  };\n  this.bigIntToMinTwosComplementsHex = function (bigIntegerValue) {\n    var h = bigIntegerValue.toString(16);\n    if (h.substr(0, 1) != '-') {\n      if (h.length % 2 == 1) {\n        h = '0' + h;\n      } else {\n        if (!h.match(/^[0-7]/)) {\n          h = '00' + h;\n        }\n      }\n    } else {\n      var hPos = h.substr(1);\n      var xorLen = hPos.length;\n      if (xorLen % 2 == 1) {\n        xorLen += 1;\n      } else {\n        if (!h.match(/^[0-7]/)) {\n          xorLen += 2;\n        }\n      }\n      var hMask = '';\n      for (var i = 0; i < xorLen; i++) {\n        hMask += 'f';\n      }\n      var biMask = new BigInteger(hMask, 16);\n      var biNeg = biMask.xor(bigIntegerValue).add(BigInteger.ONE);\n      h = biNeg.toString(16).replace(/^-/, '');\n    }\n    return h;\n  };\n  /**\n   * get PEM string from hexadecimal data and header string\n   * @name getPEMStringFromHex\n   * @memberOf KJUR.asn1.ASN1Util\n   * @function\n   * @param {String} dataHex hexadecimal string of PEM body\n   * @param {String} pemHeader PEM header string (ex. 'RSA PRIVATE KEY')\n   * @return {String} PEM formatted string of input data\n   * @description\n   * This method converts a hexadecimal string to a PEM string with\n   * a specified header. Its line break will be CRLF(\"\\r\\n\").\n   * @example\n   * var pem  = KJUR.asn1.ASN1Util.getPEMStringFromHex('616161', 'RSA PRIVATE KEY');\n   * // value of pem will be:\n   * -----BEGIN PRIVATE KEY-----\n   * YWFh\n   * -----END PRIVATE KEY-----\n   */\n  this.getPEMStringFromHex = function (dataHex, pemHeader) {\n    return hextopem(dataHex, pemHeader);\n  };\n  /**\n   * generate ASN1Object specifed by JSON parameters\n   * @name newObject\n   * @memberOf KJUR.asn1.ASN1Util\n   * @function\n   * @param {Array} param JSON parameter to generate ASN1Object\n   * @return {KJUR.asn1.ASN1Object} generated object\n   * @since asn1 1.0.3\n   * @description\n   * generate any ASN1Object specified by JSON param\n   * including ASN.1 primitive or structured.\n   * Generally 'param' can be described as follows:\n   * <blockquote>\n   * {TYPE-OF-ASNOBJ: ASN1OBJ-PARAMETER}\n   * </blockquote>\n   * 'TYPE-OF-ASN1OBJ' can be one of following symbols:\n   * <ul>\n   * <li>'bool' - DERBoolean</li>\n   * <li>'int' - DERInteger</li>\n   * <li>'bitstr' - DERBitString</li>\n   * <li>'octstr' - DEROctetString</li>\n   * <li>'null' - DERNull</li>\n   * <li>'oid' - DERObjectIdentifier</li>\n   * <li>'enum' - DEREnumerated</li>\n   * <li>'utf8str' - DERUTF8String</li>\n   * <li>'numstr' - DERNumericString</li>\n   * <li>'prnstr' - DERPrintableString</li>\n   * <li>'telstr' - DERTeletexString</li>\n   * <li>'ia5str' - DERIA5String</li>\n   * <li>'utctime' - DERUTCTime</li>\n   * <li>'gentime' - DERGeneralizedTime</li>\n   * <li>'seq' - DERSequence</li>\n   * <li>'set' - DERSet</li>\n   * <li>'tag' - DERTaggedObject</li>\n   * </ul>\n   * @example\n   * newObject({'prnstr': 'aaa'});\n   * newObject({'seq': [{'int': 3}, {'prnstr': 'aaa'}]})\n   * // ASN.1 Tagged Object\n   * newObject({'tag': {'tag': 'a1',\n   *                    'explicit': true,\n   *                    'obj': {'seq': [{'int': 3}, {'prnstr': 'aaa'}]}}});\n   * // more simple representation of ASN.1 Tagged Object\n   * newObject({'tag': ['a1',\n   *                    true,\n   *                    {'seq': [\n   *                      {'int': 3},\n   *                      {'prnstr': 'aaa'}]}\n   *                   ]});\n   */\n  this.newObject = function (param) {\n    var _KJUR = KJUR,\n      _KJUR_asn1 = _KJUR.asn1,\n      _DERBoolean = _KJUR_asn1.DERBoolean,\n      _DERInteger = _KJUR_asn1.DERInteger,\n      _DERBitString = _KJUR_asn1.DERBitString,\n      _DEROctetString = _KJUR_asn1.DEROctetString,\n      _DERNull = _KJUR_asn1.DERNull,\n      _DERObjectIdentifier = _KJUR_asn1.DERObjectIdentifier,\n      _DEREnumerated = _KJUR_asn1.DEREnumerated,\n      _DERUTF8String = _KJUR_asn1.DERUTF8String,\n      _DERNumericString = _KJUR_asn1.DERNumericString,\n      _DERPrintableString = _KJUR_asn1.DERPrintableString,\n      _DERTeletexString = _KJUR_asn1.DERTeletexString,\n      _DERIA5String = _KJUR_asn1.DERIA5String,\n      _DERUTCTime = _KJUR_asn1.DERUTCTime,\n      _DERGeneralizedTime = _KJUR_asn1.DERGeneralizedTime,\n      _DERSequence = _KJUR_asn1.DERSequence,\n      _DERSet = _KJUR_asn1.DERSet,\n      _DERTaggedObject = _KJUR_asn1.DERTaggedObject,\n      _newObject = _KJUR_asn1.ASN1Util.newObject;\n    var keys = Object.keys(param);\n    if (keys.length != 1) throw \"key of param shall be only one.\";\n    var key = keys[0];\n    if (\":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:\".indexOf(\":\" + key + \":\") == -1) throw \"undefined key: \" + key;\n    if (key == \"bool\") return new _DERBoolean(param[key]);\n    if (key == \"int\") return new _DERInteger(param[key]);\n    if (key == \"bitstr\") return new _DERBitString(param[key]);\n    if (key == \"octstr\") return new _DEROctetString(param[key]);\n    if (key == \"null\") return new _DERNull(param[key]);\n    if (key == \"oid\") return new _DERObjectIdentifier(param[key]);\n    if (key == \"enum\") return new _DEREnumerated(param[key]);\n    if (key == \"utf8str\") return new _DERUTF8String(param[key]);\n    if (key == \"numstr\") return new _DERNumericString(param[key]);\n    if (key == \"prnstr\") return new _DERPrintableString(param[key]);\n    if (key == \"telstr\") return new _DERTeletexString(param[key]);\n    if (key == \"ia5str\") return new _DERIA5String(param[key]);\n    if (key == \"utctime\") return new _DERUTCTime(param[key]);\n    if (key == \"gentime\") return new _DERGeneralizedTime(param[key]);\n    if (key == \"seq\") {\n      var paramList = param[key];\n      var a = [];\n      for (var i = 0; i < paramList.length; i++) {\n        var asn1Obj = _newObject(paramList[i]);\n        a.push(asn1Obj);\n      }\n      return new _DERSequence({\n        'array': a\n      });\n    }\n    if (key == \"set\") {\n      var paramList = param[key];\n      var a = [];\n      for (var i = 0; i < paramList.length; i++) {\n        var asn1Obj = _newObject(paramList[i]);\n        a.push(asn1Obj);\n      }\n      return new _DERSet({\n        'array': a\n      });\n    }\n    if (key == \"tag\") {\n      var tagParam = param[key];\n      if (Object.prototype.toString.call(tagParam) === '[object Array]' && tagParam.length == 3) {\n        var obj = _newObject(tagParam[2]);\n        return new _DERTaggedObject({\n          tag: tagParam[0],\n          explicit: tagParam[1],\n          obj: obj\n        });\n      } else {\n        var newParam = {};\n        if (tagParam.explicit !== undefined) newParam.explicit = tagParam.explicit;\n        if (tagParam.tag !== undefined) newParam.tag = tagParam.tag;\n        if (tagParam.obj === undefined) throw \"obj shall be specified for 'tag'.\";\n        newParam.obj = _newObject(tagParam.obj);\n        return new _DERTaggedObject(newParam);\n      }\n    }\n  };\n  /**\n   * get encoded hexadecimal string of ASN1Object specifed by JSON parameters\n   * @name jsonToASN1HEX\n   * @memberOf KJUR.asn1.ASN1Util\n   * @function\n   * @param {Array} param JSON parameter to generate ASN1Object\n   * @return hexadecimal string of ASN1Object\n   * @since asn1 1.0.4\n   * @description\n   * As for ASN.1 object representation of JSON object,\n   * please see {@link newObject}.\n   * @example\n   * jsonToASN1HEX({'prnstr': 'aaa'});\n   */\n  this.jsonToASN1HEX = function (param) {\n    var asn1Obj = this.newObject(param);\n    return asn1Obj.getEncodedHex();\n  };\n}();\n/**\n * get dot noted oid number string from hexadecimal value of OID\n * @name oidHexToInt\n * @memberOf KJUR.asn1.ASN1Util\n * @function\n * @param {String} hex hexadecimal value of object identifier\n * @return {String} dot noted string of object identifier\n * @since jsrsasign 4.8.3 asn1 1.0.7\n * @description\n * This static method converts from hexadecimal string representation of\n * ASN.1 value of object identifier to oid number string.\n * @example\n * KJUR.asn1.ASN1Util.oidHexToInt('550406') &rarr; \"*******\"\n */\nKJUR.asn1.ASN1Util.oidHexToInt = function (hex) {\n  var s = \"\";\n  var i01 = parseInt(hex.substr(0, 2), 16);\n  var i0 = Math.floor(i01 / 40);\n  var i1 = i01 % 40;\n  var s = i0 + \".\" + i1;\n  var binbuf = \"\";\n  for (var i = 2; i < hex.length; i += 2) {\n    var value = parseInt(hex.substr(i, 2), 16);\n    var bin = (\"00000000\" + value.toString(2)).slice(-8);\n    binbuf = binbuf + bin.substr(1, 7);\n    if (bin.substr(0, 1) == \"0\") {\n      var bi = new BigInteger(binbuf, 2);\n      s = s + \".\" + bi.toString(10);\n      binbuf = \"\";\n    }\n  }\n  ;\n  return s;\n};\n/**\n * get hexadecimal value of object identifier from dot noted oid value\n * @name oidIntToHex\n * @memberOf KJUR.asn1.ASN1Util\n * @function\n * @param {String} oidString dot noted string of object identifier\n * @return {String} hexadecimal value of object identifier\n * @since jsrsasign 4.8.3 asn1 1.0.7\n * @description\n * This static method converts from object identifier value string.\n * to hexadecimal string representation of it.\n * @example\n * KJUR.asn1.ASN1Util.oidIntToHex(\"*******\") &rarr; \"550406\"\n */\nKJUR.asn1.ASN1Util.oidIntToHex = function (oidString) {\n  var itox = function (i) {\n    var h = i.toString(16);\n    if (h.length == 1) h = '0' + h;\n    return h;\n  };\n  var roidtox = function (roid) {\n    var h = '';\n    var bi = new BigInteger(roid, 10);\n    var b = bi.toString(2);\n    var padLen = 7 - b.length % 7;\n    if (padLen == 7) padLen = 0;\n    var bPad = '';\n    for (var i = 0; i < padLen; i++) bPad += '0';\n    b = bPad + b;\n    for (var i = 0; i < b.length - 1; i += 7) {\n      var b8 = b.substr(i, 7);\n      if (i != b.length - 7) b8 = '1' + b8;\n      h += itox(parseInt(b8, 2));\n    }\n    return h;\n  };\n  if (!oidString.match(/^[0-9.]+$/)) {\n    throw \"malformed oid string: \" + oidString;\n  }\n  var h = '';\n  var a = oidString.split('.');\n  var i0 = parseInt(a[0]) * 40 + parseInt(a[1]);\n  h += itox(i0);\n  a.splice(0, 2);\n  for (var i = 0; i < a.length; i++) {\n    h += roidtox(a[i]);\n  }\n  return h;\n};\n// ********************************************************************\n//  Abstract ASN.1 Classes\n// ********************************************************************\n// ********************************************************************\n/**\n * base class for ASN.1 DER encoder object\n * @name KJUR.asn1.ASN1Object\n * @class base class for ASN.1 DER encoder object\n * @property {Boolean} isModified flag whether internal data was changed\n * @property {String} hTLV hexadecimal string of ASN.1 TLV\n * @property {String} hT hexadecimal string of ASN.1 TLV tag(T)\n * @property {String} hL hexadecimal string of ASN.1 TLV length(L)\n * @property {String} hV hexadecimal string of ASN.1 TLV value(V)\n * @description\n */\nKJUR.asn1.ASN1Object = function () {\n  var isModified = true;\n  var hTLV = null;\n  var hT = '00';\n  var hL = '00';\n  var hV = '';\n  /**\n   * get hexadecimal ASN.1 TLV length(L) bytes from TLV value(V)\n   * @name getLengthHexFromValue\n   * @memberOf KJUR.asn1.ASN1Object#\n   * @function\n   * @return {String} hexadecimal string of ASN.1 TLV length(L)\n   */\n  this.getLengthHexFromValue = function () {\n    if (typeof this.hV == \"undefined\" || this.hV == null) {\n      throw \"this.hV is null or undefined.\";\n    }\n    if (this.hV.length % 2 == 1) {\n      throw \"value hex must be even length: n=\" + hV.length + \",v=\" + this.hV;\n    }\n    var n = this.hV.length / 2;\n    var hN = n.toString(16);\n    if (hN.length % 2 == 1) {\n      hN = \"0\" + hN;\n    }\n    if (n < 128) {\n      return hN;\n    } else {\n      var hNlen = hN.length / 2;\n      if (hNlen > 15) {\n        throw \"ASN.1 length too long to represent by 8x: n = \" + n.toString(16);\n      }\n      var head = 128 + hNlen;\n      return head.toString(16) + hN;\n    }\n  };\n  /**\n   * get hexadecimal string of ASN.1 TLV bytes\n   * @name getEncodedHex\n   * @memberOf KJUR.asn1.ASN1Object#\n   * @function\n   * @return {String} hexadecimal string of ASN.1 TLV\n   */\n  this.getEncodedHex = function () {\n    if (this.hTLV == null || this.isModified) {\n      this.hV = this.getFreshValueHex();\n      this.hL = this.getLengthHexFromValue();\n      this.hTLV = this.hT + this.hL + this.hV;\n      this.isModified = false;\n      //alert(\"first time: \" + this.hTLV);\n    }\n    return this.hTLV;\n  };\n  /**\n   * get hexadecimal string of ASN.1 TLV value(V) bytes\n   * @name getValueHex\n   * @memberOf KJUR.asn1.ASN1Object#\n   * @function\n   * @return {String} hexadecimal string of ASN.1 TLV value(V) bytes\n   */\n  this.getValueHex = function () {\n    this.getEncodedHex();\n    return this.hV;\n  };\n  this.getFreshValueHex = function () {\n    return '';\n  };\n};\n// == BEGIN DERAbstractString ================================================\n/**\n * base class for ASN.1 DER string classes\n * @name KJUR.asn1.DERAbstractString\n * @class base class for ASN.1 DER string classes\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @property {String} s internal string of value\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>str - specify initial ASN.1 value(V) by a string</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERAbstractString = function (params) {\n  KJUR.asn1.DERAbstractString.superclass.constructor.call(this);\n  var s = null;\n  var hV = null;\n  /**\n   * get string value of this string object\n   * @name getString\n   * @memberOf KJUR.asn1.DERAbstractString#\n   * @function\n   * @return {String} string value of this string object\n   */\n  this.getString = function () {\n    return this.s;\n  };\n  /**\n   * set value by a string\n   * @name setString\n   * @memberOf KJUR.asn1.DERAbstractString#\n   * @function\n   * @param {String} newS value by a string to set\n   */\n  this.setString = function (newS) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.s = newS;\n    this.hV = stohex(this.s);\n  };\n  /**\n   * set value by a hexadecimal string\n   * @name setStringHex\n   * @memberOf KJUR.asn1.DERAbstractString#\n   * @function\n   * @param {String} newHexString value by a hexadecimal string to set\n   */\n  this.setStringHex = function (newHexString) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.s = null;\n    this.hV = newHexString;\n  };\n  this.getFreshValueHex = function () {\n    return this.hV;\n  };\n  if (typeof params != \"undefined\") {\n    if (typeof params == \"string\") {\n      this.setString(params);\n    } else if (typeof params['str'] != \"undefined\") {\n      this.setString(params['str']);\n    } else if (typeof params['hex'] != \"undefined\") {\n      this.setStringHex(params['hex']);\n    }\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DERAbstractString, KJUR.asn1.ASN1Object);\n// == END   DERAbstractString ================================================\n// == BEGIN DERAbstractTime ==================================================\n/**\n * base class for ASN.1 DER Generalized/UTCTime class\n * @name KJUR.asn1.DERAbstractTime\n * @class base class for ASN.1 DER Generalized/UTCTime class\n * @param {Array} params associative array of parameters (ex. {'str': '130430235959Z'})\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERAbstractTime = function (params) {\n  KJUR.asn1.DERAbstractTime.superclass.constructor.call(this);\n  var s = null;\n  var date = null;\n  // --- PRIVATE METHODS --------------------\n  this.localDateToUTC = function (d) {\n    utc = d.getTime() + d.getTimezoneOffset() * 60000;\n    var utcDate = new Date(utc);\n    return utcDate;\n  };\n  /*\n   * format date string by Data object\n   * @name formatDate\n   * @memberOf KJUR.asn1.AbstractTime;\n   * @param {Date} dateObject\n   * @param {string} type 'utc' or 'gen'\n   * @param {boolean} withMillis flag for with millisections or not\n   * @description\n   * 'withMillis' flag is supported from asn1 1.0.6.\n   */\n  this.formatDate = function (dateObject, type, withMillis) {\n    var pad = this.zeroPadding;\n    var d = this.localDateToUTC(dateObject);\n    var year = String(d.getFullYear());\n    if (type == 'utc') year = year.substr(2, 2);\n    var month = pad(String(d.getMonth() + 1), 2);\n    var day = pad(String(d.getDate()), 2);\n    var hour = pad(String(d.getHours()), 2);\n    var min = pad(String(d.getMinutes()), 2);\n    var sec = pad(String(d.getSeconds()), 2);\n    var s = year + month + day + hour + min + sec;\n    if (withMillis === true) {\n      var millis = d.getMilliseconds();\n      if (millis != 0) {\n        var sMillis = pad(String(millis), 3);\n        sMillis = sMillis.replace(/[0]+$/, \"\");\n        s = s + \".\" + sMillis;\n      }\n    }\n    return s + \"Z\";\n  };\n  this.zeroPadding = function (s, len) {\n    if (s.length >= len) return s;\n    return new Array(len - s.length + 1).join('0') + s;\n  };\n  // --- PUBLIC METHODS --------------------\n  /**\n   * get string value of this string object\n   * @name getString\n   * @memberOf KJUR.asn1.DERAbstractTime#\n   * @function\n   * @return {String} string value of this time object\n   */\n  this.getString = function () {\n    return this.s;\n  };\n  /**\n   * set value by a string\n   * @name setString\n   * @memberOf KJUR.asn1.DERAbstractTime#\n   * @function\n   * @param {String} newS value by a string to set such like \"130430235959Z\"\n   */\n  this.setString = function (newS) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.s = newS;\n    this.hV = stohex(newS);\n  };\n  /**\n   * set value by a Date object\n   * @name setByDateValue\n   * @memberOf KJUR.asn1.DERAbstractTime#\n   * @function\n   * @param {Integer} year year of date (ex. 2013)\n   * @param {Integer} month month of date between 1 and 12 (ex. 12)\n   * @param {Integer} day day of month\n   * @param {Integer} hour hours of date\n   * @param {Integer} min minutes of date\n   * @param {Integer} sec seconds of date\n   */\n  this.setByDateValue = function (year, month, day, hour, min, sec) {\n    var dateObject = new Date(Date.UTC(year, month - 1, day, hour, min, sec, 0));\n    this.setByDate(dateObject);\n  };\n  this.getFreshValueHex = function () {\n    return this.hV;\n  };\n};\nYAHOO.lang.extend(KJUR.asn1.DERAbstractTime, KJUR.asn1.ASN1Object);\n// == END   DERAbstractTime ==================================================\n// == BEGIN DERAbstractStructured ============================================\n/**\n * base class for ASN.1 DER structured class\n * @name KJUR.asn1.DERAbstractStructured\n * @class base class for ASN.1 DER structured class\n * @property {Array} asn1Array internal array of ASN1Object\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERAbstractStructured = function (params) {\n  KJUR.asn1.DERAbstractString.superclass.constructor.call(this);\n  var asn1Array = null;\n  /**\n   * set value by array of ASN1Object\n   * @name setByASN1ObjectArray\n   * @memberOf KJUR.asn1.DERAbstractStructured#\n   * @function\n   * @param {array} asn1ObjectArray array of ASN1Object to set\n   */\n  this.setByASN1ObjectArray = function (asn1ObjectArray) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.asn1Array = asn1ObjectArray;\n  };\n  /**\n   * append an ASN1Object to internal array\n   * @name appendASN1Object\n   * @memberOf KJUR.asn1.DERAbstractStructured#\n   * @function\n   * @param {ASN1Object} asn1Object to add\n   */\n  this.appendASN1Object = function (asn1Object) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.asn1Array.push(asn1Object);\n  };\n  this.asn1Array = new Array();\n  if (typeof params != \"undefined\") {\n    if (typeof params['array'] != \"undefined\") {\n      this.asn1Array = params['array'];\n    }\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DERAbstractStructured, KJUR.asn1.ASN1Object);\n// ********************************************************************\n//  ASN.1 Object Classes\n// ********************************************************************\n// ********************************************************************\n/**\n * class for ASN.1 DER Boolean\n * @name KJUR.asn1.DERBoolean\n * @class class for ASN.1 DER Boolean\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERBoolean = function () {\n  KJUR.asn1.DERBoolean.superclass.constructor.call(this);\n  this.hT = \"01\";\n  this.hTLV = \"0101ff\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERBoolean, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER Integer\n * @name KJUR.asn1.DERInteger\n * @class class for ASN.1 DER Integer\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>int - specify initial ASN.1 value(V) by integer value</li>\n * <li>bigint - specify initial ASN.1 value(V) by BigInteger object</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERInteger = function (params) {\n  KJUR.asn1.DERInteger.superclass.constructor.call(this);\n  this.hT = \"02\";\n  /**\n   * set value by Tom Wu's BigInteger object\n   * @name setByBigInteger\n   * @memberOf KJUR.asn1.DERInteger#\n   * @function\n   * @param {BigInteger} bigIntegerValue to set\n   */\n  this.setByBigInteger = function (bigIntegerValue) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.hV = KJUR.asn1.ASN1Util.bigIntToMinTwosComplementsHex(bigIntegerValue);\n  };\n  /**\n   * set value by integer value\n   * @name setByInteger\n   * @memberOf KJUR.asn1.DERInteger\n   * @function\n   * @param {Integer} integer value to set\n   */\n  this.setByInteger = function (intValue) {\n    var bi = new BigInteger(String(intValue), 10);\n    this.setByBigInteger(bi);\n  };\n  /**\n   * set value by integer value\n   * @name setValueHex\n   * @memberOf KJUR.asn1.DERInteger#\n   * @function\n   * @param {String} hexadecimal string of integer value\n   * @description\n   * <br/>\n   * NOTE: Value shall be represented by minimum octet length of\n   * two's complement representation.\n   * @example\n   * new KJUR.asn1.DERInteger(123);\n   * new KJUR.asn1.DERInteger({'int': 123});\n   * new KJUR.asn1.DERInteger({'hex': '1fad'});\n   */\n  this.setValueHex = function (newHexString) {\n    this.hV = newHexString;\n  };\n  this.getFreshValueHex = function () {\n    return this.hV;\n  };\n  if (typeof params != \"undefined\") {\n    if (typeof params['bigint'] != \"undefined\") {\n      this.setByBigInteger(params['bigint']);\n    } else if (typeof params['int'] != \"undefined\") {\n      this.setByInteger(params['int']);\n    } else if (typeof params == \"number\") {\n      this.setByInteger(params);\n    } else if (typeof params['hex'] != \"undefined\") {\n      this.setValueHex(params['hex']);\n    }\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DERInteger, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER encoded BitString primitive\n * @name KJUR.asn1.DERBitString\n * @class class for ASN.1 DER encoded BitString primitive\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>bin - specify binary string (ex. '10111')</li>\n * <li>array - specify array of boolean (ex. [true,false,true,true])</li>\n * <li>hex - specify hexadecimal string of ASN.1 value(V) including unused bits</li>\n * <li>obj - specify {@link KJUR.asn1.ASN1Util.newObject}\n * argument for \"BitString encapsulates\" structure.</li>\n * </ul>\n * NOTE1: 'params' can be omitted.<br/>\n * NOTE2: 'obj' parameter have been supported since\n * asn1 1.0.11, jsrsasign 6.1.1 (2016-Sep-25).<br/>\n * @example\n * // default constructor\n * o = new KJUR.asn1.DERBitString();\n * // initialize with binary string\n * o = new KJUR.asn1.DERBitString({bin: \"1011\"});\n * // initialize with boolean array\n * o = new KJUR.asn1.DERBitString({array: [true,false,true,true]});\n * // initialize with hexadecimal string (04 is unused bits)\n * o = new KJUR.asn1.DEROctetString({hex: \"04bac0\"});\n * // initialize with ASN1Util.newObject argument for encapsulated\n * o = new KJUR.asn1.DERBitString({obj: {seq: [{int: 3}, {prnstr: 'aaa'}]}});\n * // above generates a ASN.1 data like this:\n * // BIT STRING, encapsulates {\n * //   SEQUENCE {\n * //     INTEGER 3\n * //     PrintableString 'aaa'\n * //     }\n * //   }\n */\nKJUR.asn1.DERBitString = function (params) {\n  if (params !== undefined && typeof params.obj !== \"undefined\") {\n    var o = KJUR.asn1.ASN1Util.newObject(params.obj);\n    params.hex = \"00\" + o.getEncodedHex();\n  }\n  KJUR.asn1.DERBitString.superclass.constructor.call(this);\n  this.hT = \"03\";\n  /**\n   * set ASN.1 value(V) by a hexadecimal string including unused bits\n   * @name setHexValueIncludingUnusedBits\n   * @memberOf KJUR.asn1.DERBitString#\n   * @function\n   * @param {String} newHexStringIncludingUnusedBits\n   */\n  this.setHexValueIncludingUnusedBits = function (newHexStringIncludingUnusedBits) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.hV = newHexStringIncludingUnusedBits;\n  };\n  /**\n   * set ASN.1 value(V) by unused bit and hexadecimal string of value\n   * @name setUnusedBitsAndHexValue\n   * @memberOf KJUR.asn1.DERBitString#\n   * @function\n   * @param {Integer} unusedBits\n   * @param {String} hValue\n   */\n  this.setUnusedBitsAndHexValue = function (unusedBits, hValue) {\n    if (unusedBits < 0 || 7 < unusedBits) {\n      throw \"unused bits shall be from 0 to 7: u = \" + unusedBits;\n    }\n    var hUnusedBits = \"0\" + unusedBits;\n    this.hTLV = null;\n    this.isModified = true;\n    this.hV = hUnusedBits + hValue;\n  };\n  /**\n   * set ASN.1 DER BitString by binary string<br/>\n   * @name setByBinaryString\n   * @memberOf KJUR.asn1.DERBitString#\n   * @function\n   * @param {String} binaryString binary value string (i.e. '10111')\n   * @description\n   * Its unused bits will be calculated automatically by length of\n   * 'binaryValue'. <br/>\n   * NOTE: Trailing zeros '0' will be ignored.\n   * @example\n   * o = new KJUR.asn1.DERBitString();\n   * o.setByBooleanArray(\"01011\");\n   */\n  this.setByBinaryString = function (binaryString) {\n    binaryString = binaryString.replace(/0+$/, '');\n    var unusedBits = 8 - binaryString.length % 8;\n    if (unusedBits == 8) unusedBits = 0;\n    for (var i = 0; i <= unusedBits; i++) {\n      binaryString += '0';\n    }\n    var h = '';\n    for (var i = 0; i < binaryString.length - 1; i += 8) {\n      var b = binaryString.substr(i, 8);\n      var x = parseInt(b, 2).toString(16);\n      if (x.length == 1) x = '0' + x;\n      h += x;\n    }\n    this.hTLV = null;\n    this.isModified = true;\n    this.hV = '0' + unusedBits + h;\n  };\n  /**\n   * set ASN.1 TLV value(V) by an array of boolean<br/>\n   * @name setByBooleanArray\n   * @memberOf KJUR.asn1.DERBitString#\n   * @function\n   * @param {array} booleanArray array of boolean (ex. [true, false, true])\n   * @description\n   * NOTE: Trailing falses will be ignored in the ASN.1 DER Object.\n   * @example\n   * o = new KJUR.asn1.DERBitString();\n   * o.setByBooleanArray([false, true, false, true, true]);\n   */\n  this.setByBooleanArray = function (booleanArray) {\n    var s = '';\n    for (var i = 0; i < booleanArray.length; i++) {\n      if (booleanArray[i] == true) {\n        s += '1';\n      } else {\n        s += '0';\n      }\n    }\n    this.setByBinaryString(s);\n  };\n  /**\n   * generate an array of falses with specified length<br/>\n   * @name newFalseArray\n   * @memberOf KJUR.asn1.DERBitString\n   * @function\n   * @param {Integer} nLength length of array to generate\n   * @return {array} array of boolean falses\n   * @description\n   * This static method may be useful to initialize boolean array.\n   * @example\n   * o = new KJUR.asn1.DERBitString();\n   * o.newFalseArray(3) &rarr; [false, false, false]\n   */\n  this.newFalseArray = function (nLength) {\n    var a = new Array(nLength);\n    for (var i = 0; i < nLength; i++) {\n      a[i] = false;\n    }\n    return a;\n  };\n  this.getFreshValueHex = function () {\n    return this.hV;\n  };\n  if (typeof params != \"undefined\") {\n    if (typeof params == \"string\" && params.toLowerCase().match(/^[0-9a-f]+$/)) {\n      this.setHexValueIncludingUnusedBits(params);\n    } else if (typeof params['hex'] != \"undefined\") {\n      this.setHexValueIncludingUnusedBits(params['hex']);\n    } else if (typeof params['bin'] != \"undefined\") {\n      this.setByBinaryString(params['bin']);\n    } else if (typeof params['array'] != \"undefined\") {\n      this.setByBooleanArray(params['array']);\n    }\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DERBitString, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER OctetString<br/>\n * @name KJUR.asn1.DEROctetString\n * @class class for ASN.1 DER OctetString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * This class provides ASN.1 OctetString simple type.<br/>\n * Supported \"params\" attributes are:\n * <ul>\n * <li>str - to set a string as a value</li>\n * <li>hex - to set a hexadecimal string as a value</li>\n * <li>obj - to set a encapsulated ASN.1 value by JSON object\n * which is defined in {@link KJUR.asn1.ASN1Util.newObject}</li>\n * </ul>\n * NOTE: A parameter 'obj' have been supported\n * for \"OCTET STRING, encapsulates\" structure.\n * since asn1 1.0.11, jsrsasign 6.1.1 (2016-Sep-25).\n * @see KJUR.asn1.DERAbstractString - superclass\n * @example\n * // default constructor\n * o = new KJUR.asn1.DEROctetString();\n * // initialize with string\n * o = new KJUR.asn1.DEROctetString({str: \"aaa\"});\n * // initialize with hexadecimal string\n * o = new KJUR.asn1.DEROctetString({hex: \"616161\"});\n * // initialize with ASN1Util.newObject argument\n * o = new KJUR.asn1.DEROctetString({obj: {seq: [{int: 3}, {prnstr: 'aaa'}]}});\n * // above generates a ASN.1 data like this:\n * // OCTET STRING, encapsulates {\n * //   SEQUENCE {\n * //     INTEGER 3\n * //     PrintableString 'aaa'\n * //     }\n * //   }\n */\nKJUR.asn1.DEROctetString = function (params) {\n  if (params !== undefined && typeof params.obj !== \"undefined\") {\n    var o = KJUR.asn1.ASN1Util.newObject(params.obj);\n    params.hex = o.getEncodedHex();\n  }\n  KJUR.asn1.DEROctetString.superclass.constructor.call(this, params);\n  this.hT = \"04\";\n};\nYAHOO.lang.extend(KJUR.asn1.DEROctetString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER Null\n * @name KJUR.asn1.DERNull\n * @class class for ASN.1 DER Null\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERNull = function () {\n  KJUR.asn1.DERNull.superclass.constructor.call(this);\n  this.hT = \"05\";\n  this.hTLV = \"0500\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERNull, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER ObjectIdentifier\n * @name KJUR.asn1.DERObjectIdentifier\n * @class class for ASN.1 DER ObjectIdentifier\n * @param {Array} params associative array of parameters (ex. {'oid': '*******'})\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>oid - specify initial ASN.1 value(V) by a oid string (ex. ********)</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERObjectIdentifier = function (params) {\n  var itox = function (i) {\n    var h = i.toString(16);\n    if (h.length == 1) h = '0' + h;\n    return h;\n  };\n  var roidtox = function (roid) {\n    var h = '';\n    var bi = new BigInteger(roid, 10);\n    var b = bi.toString(2);\n    var padLen = 7 - b.length % 7;\n    if (padLen == 7) padLen = 0;\n    var bPad = '';\n    for (var i = 0; i < padLen; i++) bPad += '0';\n    b = bPad + b;\n    for (var i = 0; i < b.length - 1; i += 7) {\n      var b8 = b.substr(i, 7);\n      if (i != b.length - 7) b8 = '1' + b8;\n      h += itox(parseInt(b8, 2));\n    }\n    return h;\n  };\n  KJUR.asn1.DERObjectIdentifier.superclass.constructor.call(this);\n  this.hT = \"06\";\n  /**\n   * set value by a hexadecimal string\n   * @name setValueHex\n   * @memberOf KJUR.asn1.DERObjectIdentifier#\n   * @function\n   * @param {String} newHexString hexadecimal value of OID bytes\n   */\n  this.setValueHex = function (newHexString) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.s = null;\n    this.hV = newHexString;\n  };\n  /**\n   * set value by a OID string<br/>\n   * @name setValueOidString\n   * @memberOf KJUR.asn1.DERObjectIdentifier#\n   * @function\n   * @param {String} oidString OID string (ex. ********)\n   * @example\n   * o = new KJUR.asn1.DERObjectIdentifier();\n   * o.setValueOidString(\"********\");\n   */\n  this.setValueOidString = function (oidString) {\n    if (!oidString.match(/^[0-9.]+$/)) {\n      throw \"malformed oid string: \" + oidString;\n    }\n    var h = '';\n    var a = oidString.split('.');\n    var i0 = parseInt(a[0]) * 40 + parseInt(a[1]);\n    h += itox(i0);\n    a.splice(0, 2);\n    for (var i = 0; i < a.length; i++) {\n      h += roidtox(a[i]);\n    }\n    this.hTLV = null;\n    this.isModified = true;\n    this.s = null;\n    this.hV = h;\n  };\n  /**\n   * set value by a OID name\n   * @name setValueName\n   * @memberOf KJUR.asn1.DERObjectIdentifier#\n   * @function\n   * @param {String} oidName OID name (ex. 'serverAuth')\n   * @since 1.0.1\n   * @description\n   * OID name shall be defined in 'KJUR.asn1.x509.OID.name2oidList'.\n   * Otherwise raise error.\n   * @example\n   * o = new KJUR.asn1.DERObjectIdentifier();\n   * o.setValueName(\"serverAuth\");\n   */\n  this.setValueName = function (oidName) {\n    var oid = KJUR.asn1.x509.OID.name2oid(oidName);\n    if (oid !== '') {\n      this.setValueOidString(oid);\n    } else {\n      throw \"DERObjectIdentifier oidName undefined: \" + oidName;\n    }\n  };\n  this.getFreshValueHex = function () {\n    return this.hV;\n  };\n  if (params !== undefined) {\n    if (typeof params === \"string\") {\n      if (params.match(/^[0-2].[0-9.]+$/)) {\n        this.setValueOidString(params);\n      } else {\n        this.setValueName(params);\n      }\n    } else if (params.oid !== undefined) {\n      this.setValueOidString(params.oid);\n    } else if (params.hex !== undefined) {\n      this.setValueHex(params.hex);\n    } else if (params.name !== undefined) {\n      this.setValueName(params.name);\n    }\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DERObjectIdentifier, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER Enumerated\n * @name KJUR.asn1.DEREnumerated\n * @class class for ASN.1 DER Enumerated\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>int - specify initial ASN.1 value(V) by integer value</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n * @example\n * new KJUR.asn1.DEREnumerated(123);\n * new KJUR.asn1.DEREnumerated({int: 123});\n * new KJUR.asn1.DEREnumerated({hex: '1fad'});\n */\nKJUR.asn1.DEREnumerated = function (params) {\n  KJUR.asn1.DEREnumerated.superclass.constructor.call(this);\n  this.hT = \"0a\";\n  /**\n   * set value by Tom Wu's BigInteger object\n   * @name setByBigInteger\n   * @memberOf KJUR.asn1.DEREnumerated#\n   * @function\n   * @param {BigInteger} bigIntegerValue to set\n   */\n  this.setByBigInteger = function (bigIntegerValue) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.hV = KJUR.asn1.ASN1Util.bigIntToMinTwosComplementsHex(bigIntegerValue);\n  };\n  /**\n   * set value by integer value\n   * @name setByInteger\n   * @memberOf KJUR.asn1.DEREnumerated#\n   * @function\n   * @param {Integer} integer value to set\n   */\n  this.setByInteger = function (intValue) {\n    var bi = new BigInteger(String(intValue), 10);\n    this.setByBigInteger(bi);\n  };\n  /**\n   * set value by integer value\n   * @name setValueHex\n   * @memberOf KJUR.asn1.DEREnumerated#\n   * @function\n   * @param {String} hexadecimal string of integer value\n   * @description\n   * <br/>\n   * NOTE: Value shall be represented by minimum octet length of\n   * two's complement representation.\n   */\n  this.setValueHex = function (newHexString) {\n    this.hV = newHexString;\n  };\n  this.getFreshValueHex = function () {\n    return this.hV;\n  };\n  if (typeof params != \"undefined\") {\n    if (typeof params['int'] != \"undefined\") {\n      this.setByInteger(params['int']);\n    } else if (typeof params == \"number\") {\n      this.setByInteger(params);\n    } else if (typeof params['hex'] != \"undefined\") {\n      this.setValueHex(params['hex']);\n    }\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DEREnumerated, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER UTF8String\n * @name KJUR.asn1.DERUTF8String\n * @class class for ASN.1 DER UTF8String\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERUTF8String = function (params) {\n  KJUR.asn1.DERUTF8String.superclass.constructor.call(this, params);\n  this.hT = \"0c\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERUTF8String, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER NumericString\n * @name KJUR.asn1.DERNumericString\n * @class class for ASN.1 DER NumericString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERNumericString = function (params) {\n  KJUR.asn1.DERNumericString.superclass.constructor.call(this, params);\n  this.hT = \"12\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERNumericString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER PrintableString\n * @name KJUR.asn1.DERPrintableString\n * @class class for ASN.1 DER PrintableString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERPrintableString = function (params) {\n  KJUR.asn1.DERPrintableString.superclass.constructor.call(this, params);\n  this.hT = \"13\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERPrintableString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER TeletexString\n * @name KJUR.asn1.DERTeletexString\n * @class class for ASN.1 DER TeletexString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERTeletexString = function (params) {\n  KJUR.asn1.DERTeletexString.superclass.constructor.call(this, params);\n  this.hT = \"14\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERTeletexString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER IA5String\n * @name KJUR.asn1.DERIA5String\n * @class class for ASN.1 DER IA5String\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERIA5String = function (params) {\n  KJUR.asn1.DERIA5String.superclass.constructor.call(this, params);\n  this.hT = \"16\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERIA5String, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER UTCTime\n * @name KJUR.asn1.DERUTCTime\n * @class class for ASN.1 DER UTCTime\n * @param {Array} params associative array of parameters (ex. {'str': '130430235959Z'})\n * @extends KJUR.asn1.DERAbstractTime\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>str - specify initial ASN.1 value(V) by a string (ex.'130430235959Z')</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * <li>date - specify Date object.</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n * <h4>EXAMPLES</h4>\n * @example\n * d1 = new KJUR.asn1.DERUTCTime();\n * d1.setString('130430125959Z');\n *\n * d2 = new KJUR.asn1.DERUTCTime({'str': '130430125959Z'});\n * d3 = new KJUR.asn1.DERUTCTime({'date': new Date(Date.UTC(2015, 0, 31, 0, 0, 0, 0))});\n * d4 = new KJUR.asn1.DERUTCTime('130430125959Z');\n */\nKJUR.asn1.DERUTCTime = function (params) {\n  KJUR.asn1.DERUTCTime.superclass.constructor.call(this, params);\n  this.hT = \"17\";\n  /**\n   * set value by a Date object<br/>\n   * @name setByDate\n   * @memberOf KJUR.asn1.DERUTCTime#\n   * @function\n   * @param {Date} dateObject Date object to set ASN.1 value(V)\n   * @example\n   * o = new KJUR.asn1.DERUTCTime();\n   * o.setByDate(new Date(\"2016/12/31\"));\n   */\n  this.setByDate = function (dateObject) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.date = dateObject;\n    this.s = this.formatDate(this.date, 'utc');\n    this.hV = stohex(this.s);\n  };\n  this.getFreshValueHex = function () {\n    if (typeof this.date == \"undefined\" && typeof this.s == \"undefined\") {\n      this.date = new Date();\n      this.s = this.formatDate(this.date, 'utc');\n      this.hV = stohex(this.s);\n    }\n    return this.hV;\n  };\n  if (params !== undefined) {\n    if (params.str !== undefined) {\n      this.setString(params.str);\n    } else if (typeof params == \"string\" && params.match(/^[0-9]{12}Z$/)) {\n      this.setString(params);\n    } else if (params.hex !== undefined) {\n      this.setStringHex(params.hex);\n    } else if (params.date !== undefined) {\n      this.setByDate(params.date);\n    }\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DERUTCTime, KJUR.asn1.DERAbstractTime);\n// ********************************************************************\n/**\n * class for ASN.1 DER GeneralizedTime\n * @name KJUR.asn1.DERGeneralizedTime\n * @class class for ASN.1 DER GeneralizedTime\n * @param {Array} params associative array of parameters (ex. {'str': '20130430235959Z'})\n * @property {Boolean} withMillis flag to show milliseconds or not\n * @extends KJUR.asn1.DERAbstractTime\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>str - specify initial ASN.1 value(V) by a string (ex.'20130430235959Z')</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * <li>date - specify Date object.</li>\n * <li>millis - specify flag to show milliseconds (from 1.0.6)</li>\n * </ul>\n * NOTE1: 'params' can be omitted.\n * NOTE2: 'withMillis' property is supported from asn1 1.0.6.\n */\nKJUR.asn1.DERGeneralizedTime = function (params) {\n  KJUR.asn1.DERGeneralizedTime.superclass.constructor.call(this, params);\n  this.hT = \"18\";\n  this.withMillis = false;\n  /**\n   * set value by a Date object\n   * @name setByDate\n   * @memberOf KJUR.asn1.DERGeneralizedTime#\n   * @function\n   * @param {Date} dateObject Date object to set ASN.1 value(V)\n   * @example\n   * When you specify UTC time, use 'Date.UTC' method like this:<br/>\n   * o1 = new DERUTCTime();\n   * o1.setByDate(date);\n   *\n   * date = new Date(Date.UTC(2015, 0, 31, 23, 59, 59, 0)); #2015JAN31 23:59:59\n   */\n  this.setByDate = function (dateObject) {\n    this.hTLV = null;\n    this.isModified = true;\n    this.date = dateObject;\n    this.s = this.formatDate(this.date, 'gen', this.withMillis);\n    this.hV = stohex(this.s);\n  };\n  this.getFreshValueHex = function () {\n    if (this.date === undefined && this.s === undefined) {\n      this.date = new Date();\n      this.s = this.formatDate(this.date, 'gen', this.withMillis);\n      this.hV = stohex(this.s);\n    }\n    return this.hV;\n  };\n  if (params !== undefined) {\n    if (params.str !== undefined) {\n      this.setString(params.str);\n    } else if (typeof params == \"string\" && params.match(/^[0-9]{14}Z$/)) {\n      this.setString(params);\n    } else if (params.hex !== undefined) {\n      this.setStringHex(params.hex);\n    } else if (params.date !== undefined) {\n      this.setByDate(params.date);\n    }\n    if (params.millis === true) {\n      this.withMillis = true;\n    }\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DERGeneralizedTime, KJUR.asn1.DERAbstractTime);\n// ********************************************************************\n/**\n * class for ASN.1 DER Sequence\n * @name KJUR.asn1.DERSequence\n * @class class for ASN.1 DER Sequence\n * @extends KJUR.asn1.DERAbstractStructured\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>array - specify array of ASN1Object to set elements of content</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERSequence = function (params) {\n  KJUR.asn1.DERSequence.superclass.constructor.call(this, params);\n  this.hT = \"30\";\n  this.getFreshValueHex = function () {\n    var h = '';\n    for (var i = 0; i < this.asn1Array.length; i++) {\n      var asn1Obj = this.asn1Array[i];\n      h += asn1Obj.getEncodedHex();\n    }\n    this.hV = h;\n    return this.hV;\n  };\n};\nYAHOO.lang.extend(KJUR.asn1.DERSequence, KJUR.asn1.DERAbstractStructured);\n// ********************************************************************\n/**\n * class for ASN.1 DER Set\n * @name KJUR.asn1.DERSet\n * @class class for ASN.1 DER Set\n * @extends KJUR.asn1.DERAbstractStructured\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>array - specify array of ASN1Object to set elements of content</li>\n * <li>sortflag - flag for sort (default: true). ASN.1 BER is not sorted in 'SET OF'.</li>\n * </ul>\n * NOTE1: 'params' can be omitted.<br/>\n * NOTE2: sortflag is supported since 1.0.5.\n */\nKJUR.asn1.DERSet = function (params) {\n  KJUR.asn1.DERSet.superclass.constructor.call(this, params);\n  this.hT = \"31\";\n  this.sortFlag = true; // item shall be sorted only in ASN.1 DER\n  this.getFreshValueHex = function () {\n    var a = new Array();\n    for (var i = 0; i < this.asn1Array.length; i++) {\n      var asn1Obj = this.asn1Array[i];\n      a.push(asn1Obj.getEncodedHex());\n    }\n    if (this.sortFlag == true) a.sort();\n    this.hV = a.join('');\n    return this.hV;\n  };\n  if (typeof params != \"undefined\") {\n    if (typeof params.sortflag != \"undefined\" && params.sortflag == false) this.sortFlag = false;\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DERSet, KJUR.asn1.DERAbstractStructured);\n// ********************************************************************\n/**\n * class for ASN.1 DER TaggedObject\n * @name KJUR.asn1.DERTaggedObject\n * @class class for ASN.1 DER TaggedObject\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * Parameter 'tagNoNex' is ASN.1 tag(T) value for this object.\n * For example, if you find '[1]' tag in a ASN.1 dump,\n * 'tagNoHex' will be 'a1'.\n * <br/>\n * As for optional argument 'params' for constructor, you can specify *ANY* of\n * following properties:\n * <ul>\n * <li>explicit - specify true if this is explicit tag otherwise false\n *     (default is 'true').</li>\n * <li>tag - specify tag (default is 'a0' which means [0])</li>\n * <li>obj - specify ASN1Object which is tagged</li>\n * </ul>\n * @example\n * d1 = new KJUR.asn1.DERUTF8String({'str':'a'});\n * d2 = new KJUR.asn1.DERTaggedObject({'obj': d1});\n * hex = d2.getEncodedHex();\n */\nKJUR.asn1.DERTaggedObject = function (params) {\n  KJUR.asn1.DERTaggedObject.superclass.constructor.call(this);\n  this.hT = \"a0\";\n  this.hV = '';\n  this.isExplicit = true;\n  this.asn1Object = null;\n  /**\n   * set value by an ASN1Object\n   * @name setString\n   * @memberOf KJUR.asn1.DERTaggedObject#\n   * @function\n   * @param {Boolean} isExplicitFlag flag for explicit/implicit tag\n   * @param {Integer} tagNoHex hexadecimal string of ASN.1 tag\n   * @param {ASN1Object} asn1Object ASN.1 to encapsulate\n   */\n  this.setASN1Object = function (isExplicitFlag, tagNoHex, asn1Object) {\n    this.hT = tagNoHex;\n    this.isExplicit = isExplicitFlag;\n    this.asn1Object = asn1Object;\n    if (this.isExplicit) {\n      this.hV = this.asn1Object.getEncodedHex();\n      this.hTLV = null;\n      this.isModified = true;\n    } else {\n      this.hV = null;\n      this.hTLV = asn1Object.getEncodedHex();\n      this.hTLV = this.hTLV.replace(/^../, tagNoHex);\n      this.isModified = false;\n    }\n  };\n  this.getFreshValueHex = function () {\n    return this.hV;\n  };\n  if (typeof params != \"undefined\") {\n    if (typeof params['tag'] != \"undefined\") {\n      this.hT = params['tag'];\n    }\n    if (typeof params['explicit'] != \"undefined\") {\n      this.isExplicit = params['explicit'];\n    }\n    if (typeof params['obj'] != \"undefined\") {\n      this.asn1Object = params['obj'];\n      this.setASN1Object(this.isExplicit, this.hT, this.asn1Object);\n    }\n  }\n};\nYAHOO.lang.extend(KJUR.asn1.DERTaggedObject, KJUR.asn1.ASN1Object);", "map": {"version": 3, "names": ["BigInteger", "YAHOO", "KJUR", "asn1", "ASN1Util", "integerToByteHex", "i", "h", "toString", "length", "bigIntToMinTwosComplementsHex", "bigIntegerValue", "substr", "match", "hPos", "xorLen", "hMask", "biMask", "biNeg", "xor", "add", "ONE", "replace", "getPEMStringFromHex", "dataHex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hextopem", "newObject", "param", "_KJUR", "_KJUR_asn1", "_DERBoolean", "DERBoolean", "_DERInteger", "DERInteger", "_DERBitString", "DERBitString", "_DEROctetString", "DEROctetString", "_DERNull", "DERNull", "_DERObjectIdentifier", "DERObjectIdentifier", "_DEREnumerated", "DEREnumerated", "_DERUTF8String", "DERUTF8String", "_DERNumericString", "DERNumericString", "_DERPrintableString", "DERPrintableString", "_DERTeletexString", "DERTeletexString", "_DERIA5String", "DERIA5String", "_DERUTCTime", "DERUTCTime", "_DERGeneralizedTime", "DERGeneralizedTime", "_DERSequence", "DERSequence", "_DERSet", "DERSet", "_DERTaggedObject", "DERTaggedObject", "_newObject", "keys", "Object", "key", "indexOf", "paramList", "a", "asn1Obj", "push", "tagParam", "prototype", "call", "obj", "tag", "explicit", "newParam", "undefined", "jsonToASN1HEX", "getEncodedHex", "oidHexToInt", "hex", "s", "i01", "parseInt", "i0", "Math", "floor", "i1", "bin<PERSON><PERSON>", "value", "bin", "slice", "bi", "oidIntToHex", "oidString", "itox", "roidtox", "roid", "b", "padLen", "bPad", "b8", "split", "splice", "ASN1Object", "isModified", "hTLV", "hT", "hL", "hV", "getLengthHexFromValue", "n", "hN", "h<PERSON>len", "head", "getFreshValueHex", "getValueHex", "DERAbstractString", "params", "superclass", "constructor", "getString", "setString", "newS", "stohex", "setStringHex", "newHexString", "lang", "extend", "DERAbstractTime", "date", "localDateToUTC", "d", "utc", "getTime", "getTimezoneOffset", "utcDate", "Date", "formatDate", "dateObject", "type", "<PERSON><PERSON><PERSON><PERSON>", "pad", "zeroPadding", "year", "String", "getFullYear", "month", "getMonth", "day", "getDate", "hour", "getHours", "min", "getMinutes", "sec", "getSeconds", "millis", "getMilliseconds", "<PERSON><PERSON><PERSON><PERSON>", "len", "Array", "join", "setByDateValue", "UTC", "setByDate", "DERAbstractStructured", "asn1Array", "setByASN1ObjectArray", "asn1ObjectArray", "appendASN1Object", "asn1Object", "setByBigInteger", "setByInteger", "intValue", "setValueHex", "o", "setHexValueIncludingUnusedBits", "newHexStringIncludingUnusedBits", "setUnusedBitsAndHexValue", "unusedBits", "hValue", "hUnusedBits", "setByBinaryString", "binaryString", "x", "setByBooleanArray", "booleanArray", "newFalseArray", "nLength", "toLowerCase", "setValueOidString", "setValueName", "oidName", "oid", "x509", "OID", "name2oid", "name", "str", "sortFlag", "sort", "sortflag", "isExplicit", "setASN1Object", "isExplicitFlag", "tagNoHex"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/jsencrypt/lib/lib/jsrsasign/asn1-1.0.js"], "sourcesContent": ["/* asn1-1.0.13.js (c) 2013-2017 <PERSON><PERSON> | kjur.github.com/jsrsasign/license\n */\n/*\n * asn1.js - ASN.1 DER encoder classes\n *\n * Copyright (c) 2013-2017 <PERSON><PERSON> (<EMAIL>)\n *\n * This software is licensed under the terms of the MIT License.\n * https://kjur.github.io/jsrsasign/license\n *\n * The above copyright and license notice shall be\n * included in all copies or substantial portions of the Software.\n */\nimport { BigInteger } from \"../jsbn/jsbn\";\nimport { YAHOO } from \"./yahoo\";\n/**\n * @fileOverview\n * @name asn1-1.0.js\n * <AUTHOR> <EMAIL>\n * @version asn1 1.0.13 (2017-Jun-02)\n * @since jsrsasign 2.1\n * @license <a href=\"https://kjur.github.io/jsrsasign/license/\">MIT License</a>\n */\n/**\n * kju<PERSON>'s class library name space\n * <p>\n * This name space provides following name spaces:\n * <ul>\n * <li>{@link KJUR.asn1} - ASN.1 primitive hexadecimal encoder</li>\n * <li>{@link KJUR.asn1.x509} - ASN.1 structure for X.509 certificate and CRL</li>\n * <li>{@link KJUR.crypto} - Java Cryptographic Extension(JCE) style MessageDigest/Signature\n * class and utilities</li>\n * </ul>\n * </p>\n * NOTE: Please ignore method summary and document of this namespace. This caused by a bug of jsdoc2.\n * @name KJUR\n * @namespace kjur's class library name space\n */\nexport var KJUR = {};\n/**\n * kjur's ASN.1 class library name space\n * <p>\n * This is ITU-T X.690 ASN.1 DER encoder class library and\n * class structure and methods is very similar to\n * org.bouncycastle.asn1 package of\n * well known BouncyCaslte Cryptography Library.\n * <h4>PROVIDING ASN.1 PRIMITIVES</h4>\n * Here are ASN.1 DER primitive classes.\n * <ul>\n * <li>0x01 {@link KJUR.asn1.DERBoolean}</li>\n * <li>0x02 {@link KJUR.asn1.DERInteger}</li>\n * <li>0x03 {@link KJUR.asn1.DERBitString}</li>\n * <li>0x04 {@link KJUR.asn1.DEROctetString}</li>\n * <li>0x05 {@link KJUR.asn1.DERNull}</li>\n * <li>0x06 {@link KJUR.asn1.DERObjectIdentifier}</li>\n * <li>0x0a {@link KJUR.asn1.DEREnumerated}</li>\n * <li>0x0c {@link KJUR.asn1.DERUTF8String}</li>\n * <li>0x12 {@link KJUR.asn1.DERNumericString}</li>\n * <li>0x13 {@link KJUR.asn1.DERPrintableString}</li>\n * <li>0x14 {@link KJUR.asn1.DERTeletexString}</li>\n * <li>0x16 {@link KJUR.asn1.DERIA5String}</li>\n * <li>0x17 {@link KJUR.asn1.DERUTCTime}</li>\n * <li>0x18 {@link KJUR.asn1.DERGeneralizedTime}</li>\n * <li>0x30 {@link KJUR.asn1.DERSequence}</li>\n * <li>0x31 {@link KJUR.asn1.DERSet}</li>\n * </ul>\n * <h4>OTHER ASN.1 CLASSES</h4>\n * <ul>\n * <li>{@link KJUR.asn1.ASN1Object}</li>\n * <li>{@link KJUR.asn1.DERAbstractString}</li>\n * <li>{@link KJUR.asn1.DERAbstractTime}</li>\n * <li>{@link KJUR.asn1.DERAbstractStructured}</li>\n * <li>{@link KJUR.asn1.DERTaggedObject}</li>\n * </ul>\n * <h4>SUB NAME SPACES</h4>\n * <ul>\n * <li>{@link KJUR.asn1.cades} - CAdES long term signature format</li>\n * <li>{@link KJUR.asn1.cms} - Cryptographic Message Syntax</li>\n * <li>{@link KJUR.asn1.csr} - Certificate Signing Request (CSR/PKCS#10)</li>\n * <li>{@link KJUR.asn1.tsp} - RFC 3161 Timestamping Protocol Format</li>\n * <li>{@link KJUR.asn1.x509} - RFC 5280 X.509 certificate and CRL</li>\n * </ul>\n * </p>\n * NOTE: Please ignore method summary and document of this namespace.\n * This caused by a bug of jsdoc2.\n * @name KJUR.asn1\n * @namespace\n */\nif (typeof KJUR.asn1 == \"undefined\" || !KJUR.asn1)\n    KJUR.asn1 = {};\n/**\n * ASN1 utilities class\n * @name KJUR.asn1.ASN1Util\n * @class ASN1 utilities class\n * @since asn1 1.0.2\n */\nKJUR.asn1.ASN1Util = new function () {\n    this.integerToByteHex = function (i) {\n        var h = i.toString(16);\n        if ((h.length % 2) == 1)\n            h = '0' + h;\n        return h;\n    };\n    this.bigIntToMinTwosComplementsHex = function (bigIntegerValue) {\n        var h = bigIntegerValue.toString(16);\n        if (h.substr(0, 1) != '-') {\n            if (h.length % 2 == 1) {\n                h = '0' + h;\n            }\n            else {\n                if (!h.match(/^[0-7]/)) {\n                    h = '00' + h;\n                }\n            }\n        }\n        else {\n            var hPos = h.substr(1);\n            var xorLen = hPos.length;\n            if (xorLen % 2 == 1) {\n                xorLen += 1;\n            }\n            else {\n                if (!h.match(/^[0-7]/)) {\n                    xorLen += 2;\n                }\n            }\n            var hMask = '';\n            for (var i = 0; i < xorLen; i++) {\n                hMask += 'f';\n            }\n            var biMask = new BigInteger(hMask, 16);\n            var biNeg = biMask.xor(bigIntegerValue).add(BigInteger.ONE);\n            h = biNeg.toString(16).replace(/^-/, '');\n        }\n        return h;\n    };\n    /**\n     * get PEM string from hexadecimal data and header string\n     * @name getPEMStringFromHex\n     * @memberOf KJUR.asn1.ASN1Util\n     * @function\n     * @param {String} dataHex hexadecimal string of PEM body\n     * @param {String} pemHeader PEM header string (ex. 'RSA PRIVATE KEY')\n     * @return {String} PEM formatted string of input data\n     * @description\n     * This method converts a hexadecimal string to a PEM string with\n     * a specified header. Its line break will be CRLF(\"\\r\\n\").\n     * @example\n     * var pem  = KJUR.asn1.ASN1Util.getPEMStringFromHex('616161', 'RSA PRIVATE KEY');\n     * // value of pem will be:\n     * -----BEGIN PRIVATE KEY-----\n     * YWFh\n     * -----END PRIVATE KEY-----\n     */\n    this.getPEMStringFromHex = function (dataHex, pemHeader) {\n        return hextopem(dataHex, pemHeader);\n    };\n    /**\n     * generate ASN1Object specifed by JSON parameters\n     * @name newObject\n     * @memberOf KJUR.asn1.ASN1Util\n     * @function\n     * @param {Array} param JSON parameter to generate ASN1Object\n     * @return {KJUR.asn1.ASN1Object} generated object\n     * @since asn1 1.0.3\n     * @description\n     * generate any ASN1Object specified by JSON param\n     * including ASN.1 primitive or structured.\n     * Generally 'param' can be described as follows:\n     * <blockquote>\n     * {TYPE-OF-ASNOBJ: ASN1OBJ-PARAMETER}\n     * </blockquote>\n     * 'TYPE-OF-ASN1OBJ' can be one of following symbols:\n     * <ul>\n     * <li>'bool' - DERBoolean</li>\n     * <li>'int' - DERInteger</li>\n     * <li>'bitstr' - DERBitString</li>\n     * <li>'octstr' - DEROctetString</li>\n     * <li>'null' - DERNull</li>\n     * <li>'oid' - DERObjectIdentifier</li>\n     * <li>'enum' - DEREnumerated</li>\n     * <li>'utf8str' - DERUTF8String</li>\n     * <li>'numstr' - DERNumericString</li>\n     * <li>'prnstr' - DERPrintableString</li>\n     * <li>'telstr' - DERTeletexString</li>\n     * <li>'ia5str' - DERIA5String</li>\n     * <li>'utctime' - DERUTCTime</li>\n     * <li>'gentime' - DERGeneralizedTime</li>\n     * <li>'seq' - DERSequence</li>\n     * <li>'set' - DERSet</li>\n     * <li>'tag' - DERTaggedObject</li>\n     * </ul>\n     * @example\n     * newObject({'prnstr': 'aaa'});\n     * newObject({'seq': [{'int': 3}, {'prnstr': 'aaa'}]})\n     * // ASN.1 Tagged Object\n     * newObject({'tag': {'tag': 'a1',\n     *                    'explicit': true,\n     *                    'obj': {'seq': [{'int': 3}, {'prnstr': 'aaa'}]}}});\n     * // more simple representation of ASN.1 Tagged Object\n     * newObject({'tag': ['a1',\n     *                    true,\n     *                    {'seq': [\n     *                      {'int': 3},\n     *                      {'prnstr': 'aaa'}]}\n     *                   ]});\n     */\n    this.newObject = function (param) {\n        var _KJUR = KJUR, _KJUR_asn1 = _KJUR.asn1, _DERBoolean = _KJUR_asn1.DERBoolean, _DERInteger = _KJUR_asn1.DERInteger, _DERBitString = _KJUR_asn1.DERBitString, _DEROctetString = _KJUR_asn1.DEROctetString, _DERNull = _KJUR_asn1.DERNull, _DERObjectIdentifier = _KJUR_asn1.DERObjectIdentifier, _DEREnumerated = _KJUR_asn1.DEREnumerated, _DERUTF8String = _KJUR_asn1.DERUTF8String, _DERNumericString = _KJUR_asn1.DERNumericString, _DERPrintableString = _KJUR_asn1.DERPrintableString, _DERTeletexString = _KJUR_asn1.DERTeletexString, _DERIA5String = _KJUR_asn1.DERIA5String, _DERUTCTime = _KJUR_asn1.DERUTCTime, _DERGeneralizedTime = _KJUR_asn1.DERGeneralizedTime, _DERSequence = _KJUR_asn1.DERSequence, _DERSet = _KJUR_asn1.DERSet, _DERTaggedObject = _KJUR_asn1.DERTaggedObject, _newObject = _KJUR_asn1.ASN1Util.newObject;\n        var keys = Object.keys(param);\n        if (keys.length != 1)\n            throw \"key of param shall be only one.\";\n        var key = keys[0];\n        if (\":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:\".indexOf(\":\" + key + \":\") == -1)\n            throw \"undefined key: \" + key;\n        if (key == \"bool\")\n            return new _DERBoolean(param[key]);\n        if (key == \"int\")\n            return new _DERInteger(param[key]);\n        if (key == \"bitstr\")\n            return new _DERBitString(param[key]);\n        if (key == \"octstr\")\n            return new _DEROctetString(param[key]);\n        if (key == \"null\")\n            return new _DERNull(param[key]);\n        if (key == \"oid\")\n            return new _DERObjectIdentifier(param[key]);\n        if (key == \"enum\")\n            return new _DEREnumerated(param[key]);\n        if (key == \"utf8str\")\n            return new _DERUTF8String(param[key]);\n        if (key == \"numstr\")\n            return new _DERNumericString(param[key]);\n        if (key == \"prnstr\")\n            return new _DERPrintableString(param[key]);\n        if (key == \"telstr\")\n            return new _DERTeletexString(param[key]);\n        if (key == \"ia5str\")\n            return new _DERIA5String(param[key]);\n        if (key == \"utctime\")\n            return new _DERUTCTime(param[key]);\n        if (key == \"gentime\")\n            return new _DERGeneralizedTime(param[key]);\n        if (key == \"seq\") {\n            var paramList = param[key];\n            var a = [];\n            for (var i = 0; i < paramList.length; i++) {\n                var asn1Obj = _newObject(paramList[i]);\n                a.push(asn1Obj);\n            }\n            return new _DERSequence({ 'array': a });\n        }\n        if (key == \"set\") {\n            var paramList = param[key];\n            var a = [];\n            for (var i = 0; i < paramList.length; i++) {\n                var asn1Obj = _newObject(paramList[i]);\n                a.push(asn1Obj);\n            }\n            return new _DERSet({ 'array': a });\n        }\n        if (key == \"tag\") {\n            var tagParam = param[key];\n            if (Object.prototype.toString.call(tagParam) === '[object Array]' &&\n                tagParam.length == 3) {\n                var obj = _newObject(tagParam[2]);\n                return new _DERTaggedObject({ tag: tagParam[0],\n                    explicit: tagParam[1],\n                    obj: obj });\n            }\n            else {\n                var newParam = {};\n                if (tagParam.explicit !== undefined)\n                    newParam.explicit = tagParam.explicit;\n                if (tagParam.tag !== undefined)\n                    newParam.tag = tagParam.tag;\n                if (tagParam.obj === undefined)\n                    throw \"obj shall be specified for 'tag'.\";\n                newParam.obj = _newObject(tagParam.obj);\n                return new _DERTaggedObject(newParam);\n            }\n        }\n    };\n    /**\n     * get encoded hexadecimal string of ASN1Object specifed by JSON parameters\n     * @name jsonToASN1HEX\n     * @memberOf KJUR.asn1.ASN1Util\n     * @function\n     * @param {Array} param JSON parameter to generate ASN1Object\n     * @return hexadecimal string of ASN1Object\n     * @since asn1 1.0.4\n     * @description\n     * As for ASN.1 object representation of JSON object,\n     * please see {@link newObject}.\n     * @example\n     * jsonToASN1HEX({'prnstr': 'aaa'});\n     */\n    this.jsonToASN1HEX = function (param) {\n        var asn1Obj = this.newObject(param);\n        return asn1Obj.getEncodedHex();\n    };\n};\n/**\n * get dot noted oid number string from hexadecimal value of OID\n * @name oidHexToInt\n * @memberOf KJUR.asn1.ASN1Util\n * @function\n * @param {String} hex hexadecimal value of object identifier\n * @return {String} dot noted string of object identifier\n * @since jsrsasign 4.8.3 asn1 1.0.7\n * @description\n * This static method converts from hexadecimal string representation of\n * ASN.1 value of object identifier to oid number string.\n * @example\n * KJUR.asn1.ASN1Util.oidHexToInt('550406') &rarr; \"*******\"\n */\nKJUR.asn1.ASN1Util.oidHexToInt = function (hex) {\n    var s = \"\";\n    var i01 = parseInt(hex.substr(0, 2), 16);\n    var i0 = Math.floor(i01 / 40);\n    var i1 = i01 % 40;\n    var s = i0 + \".\" + i1;\n    var binbuf = \"\";\n    for (var i = 2; i < hex.length; i += 2) {\n        var value = parseInt(hex.substr(i, 2), 16);\n        var bin = (\"00000000\" + value.toString(2)).slice(-8);\n        binbuf = binbuf + bin.substr(1, 7);\n        if (bin.substr(0, 1) == \"0\") {\n            var bi = new BigInteger(binbuf, 2);\n            s = s + \".\" + bi.toString(10);\n            binbuf = \"\";\n        }\n    }\n    ;\n    return s;\n};\n/**\n * get hexadecimal value of object identifier from dot noted oid value\n * @name oidIntToHex\n * @memberOf KJUR.asn1.ASN1Util\n * @function\n * @param {String} oidString dot noted string of object identifier\n * @return {String} hexadecimal value of object identifier\n * @since jsrsasign 4.8.3 asn1 1.0.7\n * @description\n * This static method converts from object identifier value string.\n * to hexadecimal string representation of it.\n * @example\n * KJUR.asn1.ASN1Util.oidIntToHex(\"*******\") &rarr; \"550406\"\n */\nKJUR.asn1.ASN1Util.oidIntToHex = function (oidString) {\n    var itox = function (i) {\n        var h = i.toString(16);\n        if (h.length == 1)\n            h = '0' + h;\n        return h;\n    };\n    var roidtox = function (roid) {\n        var h = '';\n        var bi = new BigInteger(roid, 10);\n        var b = bi.toString(2);\n        var padLen = 7 - b.length % 7;\n        if (padLen == 7)\n            padLen = 0;\n        var bPad = '';\n        for (var i = 0; i < padLen; i++)\n            bPad += '0';\n        b = bPad + b;\n        for (var i = 0; i < b.length - 1; i += 7) {\n            var b8 = b.substr(i, 7);\n            if (i != b.length - 7)\n                b8 = '1' + b8;\n            h += itox(parseInt(b8, 2));\n        }\n        return h;\n    };\n    if (!oidString.match(/^[0-9.]+$/)) {\n        throw \"malformed oid string: \" + oidString;\n    }\n    var h = '';\n    var a = oidString.split('.');\n    var i0 = parseInt(a[0]) * 40 + parseInt(a[1]);\n    h += itox(i0);\n    a.splice(0, 2);\n    for (var i = 0; i < a.length; i++) {\n        h += roidtox(a[i]);\n    }\n    return h;\n};\n// ********************************************************************\n//  Abstract ASN.1 Classes\n// ********************************************************************\n// ********************************************************************\n/**\n * base class for ASN.1 DER encoder object\n * @name KJUR.asn1.ASN1Object\n * @class base class for ASN.1 DER encoder object\n * @property {Boolean} isModified flag whether internal data was changed\n * @property {String} hTLV hexadecimal string of ASN.1 TLV\n * @property {String} hT hexadecimal string of ASN.1 TLV tag(T)\n * @property {String} hL hexadecimal string of ASN.1 TLV length(L)\n * @property {String} hV hexadecimal string of ASN.1 TLV value(V)\n * @description\n */\nKJUR.asn1.ASN1Object = function () {\n    var isModified = true;\n    var hTLV = null;\n    var hT = '00';\n    var hL = '00';\n    var hV = '';\n    /**\n     * get hexadecimal ASN.1 TLV length(L) bytes from TLV value(V)\n     * @name getLengthHexFromValue\n     * @memberOf KJUR.asn1.ASN1Object#\n     * @function\n     * @return {String} hexadecimal string of ASN.1 TLV length(L)\n     */\n    this.getLengthHexFromValue = function () {\n        if (typeof this.hV == \"undefined\" || this.hV == null) {\n            throw \"this.hV is null or undefined.\";\n        }\n        if (this.hV.length % 2 == 1) {\n            throw \"value hex must be even length: n=\" + hV.length + \",v=\" + this.hV;\n        }\n        var n = this.hV.length / 2;\n        var hN = n.toString(16);\n        if (hN.length % 2 == 1) {\n            hN = \"0\" + hN;\n        }\n        if (n < 128) {\n            return hN;\n        }\n        else {\n            var hNlen = hN.length / 2;\n            if (hNlen > 15) {\n                throw \"ASN.1 length too long to represent by 8x: n = \" + n.toString(16);\n            }\n            var head = 128 + hNlen;\n            return head.toString(16) + hN;\n        }\n    };\n    /**\n     * get hexadecimal string of ASN.1 TLV bytes\n     * @name getEncodedHex\n     * @memberOf KJUR.asn1.ASN1Object#\n     * @function\n     * @return {String} hexadecimal string of ASN.1 TLV\n     */\n    this.getEncodedHex = function () {\n        if (this.hTLV == null || this.isModified) {\n            this.hV = this.getFreshValueHex();\n            this.hL = this.getLengthHexFromValue();\n            this.hTLV = this.hT + this.hL + this.hV;\n            this.isModified = false;\n            //alert(\"first time: \" + this.hTLV);\n        }\n        return this.hTLV;\n    };\n    /**\n     * get hexadecimal string of ASN.1 TLV value(V) bytes\n     * @name getValueHex\n     * @memberOf KJUR.asn1.ASN1Object#\n     * @function\n     * @return {String} hexadecimal string of ASN.1 TLV value(V) bytes\n     */\n    this.getValueHex = function () {\n        this.getEncodedHex();\n        return this.hV;\n    };\n    this.getFreshValueHex = function () {\n        return '';\n    };\n};\n// == BEGIN DERAbstractString ================================================\n/**\n * base class for ASN.1 DER string classes\n * @name KJUR.asn1.DERAbstractString\n * @class base class for ASN.1 DER string classes\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @property {String} s internal string of value\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>str - specify initial ASN.1 value(V) by a string</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERAbstractString = function (params) {\n    KJUR.asn1.DERAbstractString.superclass.constructor.call(this);\n    var s = null;\n    var hV = null;\n    /**\n     * get string value of this string object\n     * @name getString\n     * @memberOf KJUR.asn1.DERAbstractString#\n     * @function\n     * @return {String} string value of this string object\n     */\n    this.getString = function () {\n        return this.s;\n    };\n    /**\n     * set value by a string\n     * @name setString\n     * @memberOf KJUR.asn1.DERAbstractString#\n     * @function\n     * @param {String} newS value by a string to set\n     */\n    this.setString = function (newS) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = newS;\n        this.hV = stohex(this.s);\n    };\n    /**\n     * set value by a hexadecimal string\n     * @name setStringHex\n     * @memberOf KJUR.asn1.DERAbstractString#\n     * @function\n     * @param {String} newHexString value by a hexadecimal string to set\n     */\n    this.setStringHex = function (newHexString) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = null;\n        this.hV = newHexString;\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params == \"string\") {\n            this.setString(params);\n        }\n        else if (typeof params['str'] != \"undefined\") {\n            this.setString(params['str']);\n        }\n        else if (typeof params['hex'] != \"undefined\") {\n            this.setStringHex(params['hex']);\n        }\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DERAbstractString, KJUR.asn1.ASN1Object);\n// == END   DERAbstractString ================================================\n// == BEGIN DERAbstractTime ==================================================\n/**\n * base class for ASN.1 DER Generalized/UTCTime class\n * @name KJUR.asn1.DERAbstractTime\n * @class base class for ASN.1 DER Generalized/UTCTime class\n * @param {Array} params associative array of parameters (ex. {'str': '130430235959Z'})\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERAbstractTime = function (params) {\n    KJUR.asn1.DERAbstractTime.superclass.constructor.call(this);\n    var s = null;\n    var date = null;\n    // --- PRIVATE METHODS --------------------\n    this.localDateToUTC = function (d) {\n        utc = d.getTime() + (d.getTimezoneOffset() * 60000);\n        var utcDate = new Date(utc);\n        return utcDate;\n    };\n    /*\n     * format date string by Data object\n     * @name formatDate\n     * @memberOf KJUR.asn1.AbstractTime;\n     * @param {Date} dateObject\n     * @param {string} type 'utc' or 'gen'\n     * @param {boolean} withMillis flag for with millisections or not\n     * @description\n     * 'withMillis' flag is supported from asn1 1.0.6.\n     */\n    this.formatDate = function (dateObject, type, withMillis) {\n        var pad = this.zeroPadding;\n        var d = this.localDateToUTC(dateObject);\n        var year = String(d.getFullYear());\n        if (type == 'utc')\n            year = year.substr(2, 2);\n        var month = pad(String(d.getMonth() + 1), 2);\n        var day = pad(String(d.getDate()), 2);\n        var hour = pad(String(d.getHours()), 2);\n        var min = pad(String(d.getMinutes()), 2);\n        var sec = pad(String(d.getSeconds()), 2);\n        var s = year + month + day + hour + min + sec;\n        if (withMillis === true) {\n            var millis = d.getMilliseconds();\n            if (millis != 0) {\n                var sMillis = pad(String(millis), 3);\n                sMillis = sMillis.replace(/[0]+$/, \"\");\n                s = s + \".\" + sMillis;\n            }\n        }\n        return s + \"Z\";\n    };\n    this.zeroPadding = function (s, len) {\n        if (s.length >= len)\n            return s;\n        return new Array(len - s.length + 1).join('0') + s;\n    };\n    // --- PUBLIC METHODS --------------------\n    /**\n     * get string value of this string object\n     * @name getString\n     * @memberOf KJUR.asn1.DERAbstractTime#\n     * @function\n     * @return {String} string value of this time object\n     */\n    this.getString = function () {\n        return this.s;\n    };\n    /**\n     * set value by a string\n     * @name setString\n     * @memberOf KJUR.asn1.DERAbstractTime#\n     * @function\n     * @param {String} newS value by a string to set such like \"130430235959Z\"\n     */\n    this.setString = function (newS) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = newS;\n        this.hV = stohex(newS);\n    };\n    /**\n     * set value by a Date object\n     * @name setByDateValue\n     * @memberOf KJUR.asn1.DERAbstractTime#\n     * @function\n     * @param {Integer} year year of date (ex. 2013)\n     * @param {Integer} month month of date between 1 and 12 (ex. 12)\n     * @param {Integer} day day of month\n     * @param {Integer} hour hours of date\n     * @param {Integer} min minutes of date\n     * @param {Integer} sec seconds of date\n     */\n    this.setByDateValue = function (year, month, day, hour, min, sec) {\n        var dateObject = new Date(Date.UTC(year, month - 1, day, hour, min, sec, 0));\n        this.setByDate(dateObject);\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n};\nYAHOO.lang.extend(KJUR.asn1.DERAbstractTime, KJUR.asn1.ASN1Object);\n// == END   DERAbstractTime ==================================================\n// == BEGIN DERAbstractStructured ============================================\n/**\n * base class for ASN.1 DER structured class\n * @name KJUR.asn1.DERAbstractStructured\n * @class base class for ASN.1 DER structured class\n * @property {Array} asn1Array internal array of ASN1Object\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERAbstractStructured = function (params) {\n    KJUR.asn1.DERAbstractString.superclass.constructor.call(this);\n    var asn1Array = null;\n    /**\n     * set value by array of ASN1Object\n     * @name setByASN1ObjectArray\n     * @memberOf KJUR.asn1.DERAbstractStructured#\n     * @function\n     * @param {array} asn1ObjectArray array of ASN1Object to set\n     */\n    this.setByASN1ObjectArray = function (asn1ObjectArray) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.asn1Array = asn1ObjectArray;\n    };\n    /**\n     * append an ASN1Object to internal array\n     * @name appendASN1Object\n     * @memberOf KJUR.asn1.DERAbstractStructured#\n     * @function\n     * @param {ASN1Object} asn1Object to add\n     */\n    this.appendASN1Object = function (asn1Object) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.asn1Array.push(asn1Object);\n    };\n    this.asn1Array = new Array();\n    if (typeof params != \"undefined\") {\n        if (typeof params['array'] != \"undefined\") {\n            this.asn1Array = params['array'];\n        }\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DERAbstractStructured, KJUR.asn1.ASN1Object);\n// ********************************************************************\n//  ASN.1 Object Classes\n// ********************************************************************\n// ********************************************************************\n/**\n * class for ASN.1 DER Boolean\n * @name KJUR.asn1.DERBoolean\n * @class class for ASN.1 DER Boolean\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERBoolean = function () {\n    KJUR.asn1.DERBoolean.superclass.constructor.call(this);\n    this.hT = \"01\";\n    this.hTLV = \"0101ff\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERBoolean, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER Integer\n * @name KJUR.asn1.DERInteger\n * @class class for ASN.1 DER Integer\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>int - specify initial ASN.1 value(V) by integer value</li>\n * <li>bigint - specify initial ASN.1 value(V) by BigInteger object</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERInteger = function (params) {\n    KJUR.asn1.DERInteger.superclass.constructor.call(this);\n    this.hT = \"02\";\n    /**\n     * set value by Tom Wu's BigInteger object\n     * @name setByBigInteger\n     * @memberOf KJUR.asn1.DERInteger#\n     * @function\n     * @param {BigInteger} bigIntegerValue to set\n     */\n    this.setByBigInteger = function (bigIntegerValue) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = KJUR.asn1.ASN1Util.bigIntToMinTwosComplementsHex(bigIntegerValue);\n    };\n    /**\n     * set value by integer value\n     * @name setByInteger\n     * @memberOf KJUR.asn1.DERInteger\n     * @function\n     * @param {Integer} integer value to set\n     */\n    this.setByInteger = function (intValue) {\n        var bi = new BigInteger(String(intValue), 10);\n        this.setByBigInteger(bi);\n    };\n    /**\n     * set value by integer value\n     * @name setValueHex\n     * @memberOf KJUR.asn1.DERInteger#\n     * @function\n     * @param {String} hexadecimal string of integer value\n     * @description\n     * <br/>\n     * NOTE: Value shall be represented by minimum octet length of\n     * two's complement representation.\n     * @example\n     * new KJUR.asn1.DERInteger(123);\n     * new KJUR.asn1.DERInteger({'int': 123});\n     * new KJUR.asn1.DERInteger({'hex': '1fad'});\n     */\n    this.setValueHex = function (newHexString) {\n        this.hV = newHexString;\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params['bigint'] != \"undefined\") {\n            this.setByBigInteger(params['bigint']);\n        }\n        else if (typeof params['int'] != \"undefined\") {\n            this.setByInteger(params['int']);\n        }\n        else if (typeof params == \"number\") {\n            this.setByInteger(params);\n        }\n        else if (typeof params['hex'] != \"undefined\") {\n            this.setValueHex(params['hex']);\n        }\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DERInteger, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER encoded BitString primitive\n * @name KJUR.asn1.DERBitString\n * @class class for ASN.1 DER encoded BitString primitive\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>bin - specify binary string (ex. '10111')</li>\n * <li>array - specify array of boolean (ex. [true,false,true,true])</li>\n * <li>hex - specify hexadecimal string of ASN.1 value(V) including unused bits</li>\n * <li>obj - specify {@link KJUR.asn1.ASN1Util.newObject}\n * argument for \"BitString encapsulates\" structure.</li>\n * </ul>\n * NOTE1: 'params' can be omitted.<br/>\n * NOTE2: 'obj' parameter have been supported since\n * asn1 1.0.11, jsrsasign 6.1.1 (2016-Sep-25).<br/>\n * @example\n * // default constructor\n * o = new KJUR.asn1.DERBitString();\n * // initialize with binary string\n * o = new KJUR.asn1.DERBitString({bin: \"1011\"});\n * // initialize with boolean array\n * o = new KJUR.asn1.DERBitString({array: [true,false,true,true]});\n * // initialize with hexadecimal string (04 is unused bits)\n * o = new KJUR.asn1.DEROctetString({hex: \"04bac0\"});\n * // initialize with ASN1Util.newObject argument for encapsulated\n * o = new KJUR.asn1.DERBitString({obj: {seq: [{int: 3}, {prnstr: 'aaa'}]}});\n * // above generates a ASN.1 data like this:\n * // BIT STRING, encapsulates {\n * //   SEQUENCE {\n * //     INTEGER 3\n * //     PrintableString 'aaa'\n * //     }\n * //   }\n */\nKJUR.asn1.DERBitString = function (params) {\n    if (params !== undefined && typeof params.obj !== \"undefined\") {\n        var o = KJUR.asn1.ASN1Util.newObject(params.obj);\n        params.hex = \"00\" + o.getEncodedHex();\n    }\n    KJUR.asn1.DERBitString.superclass.constructor.call(this);\n    this.hT = \"03\";\n    /**\n     * set ASN.1 value(V) by a hexadecimal string including unused bits\n     * @name setHexValueIncludingUnusedBits\n     * @memberOf KJUR.asn1.DERBitString#\n     * @function\n     * @param {String} newHexStringIncludingUnusedBits\n     */\n    this.setHexValueIncludingUnusedBits = function (newHexStringIncludingUnusedBits) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = newHexStringIncludingUnusedBits;\n    };\n    /**\n     * set ASN.1 value(V) by unused bit and hexadecimal string of value\n     * @name setUnusedBitsAndHexValue\n     * @memberOf KJUR.asn1.DERBitString#\n     * @function\n     * @param {Integer} unusedBits\n     * @param {String} hValue\n     */\n    this.setUnusedBitsAndHexValue = function (unusedBits, hValue) {\n        if (unusedBits < 0 || 7 < unusedBits) {\n            throw \"unused bits shall be from 0 to 7: u = \" + unusedBits;\n        }\n        var hUnusedBits = \"0\" + unusedBits;\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = hUnusedBits + hValue;\n    };\n    /**\n     * set ASN.1 DER BitString by binary string<br/>\n     * @name setByBinaryString\n     * @memberOf KJUR.asn1.DERBitString#\n     * @function\n     * @param {String} binaryString binary value string (i.e. '10111')\n     * @description\n     * Its unused bits will be calculated automatically by length of\n     * 'binaryValue'. <br/>\n     * NOTE: Trailing zeros '0' will be ignored.\n     * @example\n     * o = new KJUR.asn1.DERBitString();\n     * o.setByBooleanArray(\"01011\");\n     */\n    this.setByBinaryString = function (binaryString) {\n        binaryString = binaryString.replace(/0+$/, '');\n        var unusedBits = 8 - binaryString.length % 8;\n        if (unusedBits == 8)\n            unusedBits = 0;\n        for (var i = 0; i <= unusedBits; i++) {\n            binaryString += '0';\n        }\n        var h = '';\n        for (var i = 0; i < binaryString.length - 1; i += 8) {\n            var b = binaryString.substr(i, 8);\n            var x = parseInt(b, 2).toString(16);\n            if (x.length == 1)\n                x = '0' + x;\n            h += x;\n        }\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = '0' + unusedBits + h;\n    };\n    /**\n     * set ASN.1 TLV value(V) by an array of boolean<br/>\n     * @name setByBooleanArray\n     * @memberOf KJUR.asn1.DERBitString#\n     * @function\n     * @param {array} booleanArray array of boolean (ex. [true, false, true])\n     * @description\n     * NOTE: Trailing falses will be ignored in the ASN.1 DER Object.\n     * @example\n     * o = new KJUR.asn1.DERBitString();\n     * o.setByBooleanArray([false, true, false, true, true]);\n     */\n    this.setByBooleanArray = function (booleanArray) {\n        var s = '';\n        for (var i = 0; i < booleanArray.length; i++) {\n            if (booleanArray[i] == true) {\n                s += '1';\n            }\n            else {\n                s += '0';\n            }\n        }\n        this.setByBinaryString(s);\n    };\n    /**\n     * generate an array of falses with specified length<br/>\n     * @name newFalseArray\n     * @memberOf KJUR.asn1.DERBitString\n     * @function\n     * @param {Integer} nLength length of array to generate\n     * @return {array} array of boolean falses\n     * @description\n     * This static method may be useful to initialize boolean array.\n     * @example\n     * o = new KJUR.asn1.DERBitString();\n     * o.newFalseArray(3) &rarr; [false, false, false]\n     */\n    this.newFalseArray = function (nLength) {\n        var a = new Array(nLength);\n        for (var i = 0; i < nLength; i++) {\n            a[i] = false;\n        }\n        return a;\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params == \"string\" && params.toLowerCase().match(/^[0-9a-f]+$/)) {\n            this.setHexValueIncludingUnusedBits(params);\n        }\n        else if (typeof params['hex'] != \"undefined\") {\n            this.setHexValueIncludingUnusedBits(params['hex']);\n        }\n        else if (typeof params['bin'] != \"undefined\") {\n            this.setByBinaryString(params['bin']);\n        }\n        else if (typeof params['array'] != \"undefined\") {\n            this.setByBooleanArray(params['array']);\n        }\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DERBitString, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER OctetString<br/>\n * @name KJUR.asn1.DEROctetString\n * @class class for ASN.1 DER OctetString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * This class provides ASN.1 OctetString simple type.<br/>\n * Supported \"params\" attributes are:\n * <ul>\n * <li>str - to set a string as a value</li>\n * <li>hex - to set a hexadecimal string as a value</li>\n * <li>obj - to set a encapsulated ASN.1 value by JSON object\n * which is defined in {@link KJUR.asn1.ASN1Util.newObject}</li>\n * </ul>\n * NOTE: A parameter 'obj' have been supported\n * for \"OCTET STRING, encapsulates\" structure.\n * since asn1 1.0.11, jsrsasign 6.1.1 (2016-Sep-25).\n * @see KJUR.asn1.DERAbstractString - superclass\n * @example\n * // default constructor\n * o = new KJUR.asn1.DEROctetString();\n * // initialize with string\n * o = new KJUR.asn1.DEROctetString({str: \"aaa\"});\n * // initialize with hexadecimal string\n * o = new KJUR.asn1.DEROctetString({hex: \"616161\"});\n * // initialize with ASN1Util.newObject argument\n * o = new KJUR.asn1.DEROctetString({obj: {seq: [{int: 3}, {prnstr: 'aaa'}]}});\n * // above generates a ASN.1 data like this:\n * // OCTET STRING, encapsulates {\n * //   SEQUENCE {\n * //     INTEGER 3\n * //     PrintableString 'aaa'\n * //     }\n * //   }\n */\nKJUR.asn1.DEROctetString = function (params) {\n    if (params !== undefined && typeof params.obj !== \"undefined\") {\n        var o = KJUR.asn1.ASN1Util.newObject(params.obj);\n        params.hex = o.getEncodedHex();\n    }\n    KJUR.asn1.DEROctetString.superclass.constructor.call(this, params);\n    this.hT = \"04\";\n};\nYAHOO.lang.extend(KJUR.asn1.DEROctetString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER Null\n * @name KJUR.asn1.DERNull\n * @class class for ASN.1 DER Null\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERNull = function () {\n    KJUR.asn1.DERNull.superclass.constructor.call(this);\n    this.hT = \"05\";\n    this.hTLV = \"0500\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERNull, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER ObjectIdentifier\n * @name KJUR.asn1.DERObjectIdentifier\n * @class class for ASN.1 DER ObjectIdentifier\n * @param {Array} params associative array of parameters (ex. {'oid': '*******'})\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>oid - specify initial ASN.1 value(V) by a oid string (ex. ********)</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERObjectIdentifier = function (params) {\n    var itox = function (i) {\n        var h = i.toString(16);\n        if (h.length == 1)\n            h = '0' + h;\n        return h;\n    };\n    var roidtox = function (roid) {\n        var h = '';\n        var bi = new BigInteger(roid, 10);\n        var b = bi.toString(2);\n        var padLen = 7 - b.length % 7;\n        if (padLen == 7)\n            padLen = 0;\n        var bPad = '';\n        for (var i = 0; i < padLen; i++)\n            bPad += '0';\n        b = bPad + b;\n        for (var i = 0; i < b.length - 1; i += 7) {\n            var b8 = b.substr(i, 7);\n            if (i != b.length - 7)\n                b8 = '1' + b8;\n            h += itox(parseInt(b8, 2));\n        }\n        return h;\n    };\n    KJUR.asn1.DERObjectIdentifier.superclass.constructor.call(this);\n    this.hT = \"06\";\n    /**\n     * set value by a hexadecimal string\n     * @name setValueHex\n     * @memberOf KJUR.asn1.DERObjectIdentifier#\n     * @function\n     * @param {String} newHexString hexadecimal value of OID bytes\n     */\n    this.setValueHex = function (newHexString) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = null;\n        this.hV = newHexString;\n    };\n    /**\n     * set value by a OID string<br/>\n     * @name setValueOidString\n     * @memberOf KJUR.asn1.DERObjectIdentifier#\n     * @function\n     * @param {String} oidString OID string (ex. ********)\n     * @example\n     * o = new KJUR.asn1.DERObjectIdentifier();\n     * o.setValueOidString(\"********\");\n     */\n    this.setValueOidString = function (oidString) {\n        if (!oidString.match(/^[0-9.]+$/)) {\n            throw \"malformed oid string: \" + oidString;\n        }\n        var h = '';\n        var a = oidString.split('.');\n        var i0 = parseInt(a[0]) * 40 + parseInt(a[1]);\n        h += itox(i0);\n        a.splice(0, 2);\n        for (var i = 0; i < a.length; i++) {\n            h += roidtox(a[i]);\n        }\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = null;\n        this.hV = h;\n    };\n    /**\n     * set value by a OID name\n     * @name setValueName\n     * @memberOf KJUR.asn1.DERObjectIdentifier#\n     * @function\n     * @param {String} oidName OID name (ex. 'serverAuth')\n     * @since 1.0.1\n     * @description\n     * OID name shall be defined in 'KJUR.asn1.x509.OID.name2oidList'.\n     * Otherwise raise error.\n     * @example\n     * o = new KJUR.asn1.DERObjectIdentifier();\n     * o.setValueName(\"serverAuth\");\n     */\n    this.setValueName = function (oidName) {\n        var oid = KJUR.asn1.x509.OID.name2oid(oidName);\n        if (oid !== '') {\n            this.setValueOidString(oid);\n        }\n        else {\n            throw \"DERObjectIdentifier oidName undefined: \" + oidName;\n        }\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (params !== undefined) {\n        if (typeof params === \"string\") {\n            if (params.match(/^[0-2].[0-9.]+$/)) {\n                this.setValueOidString(params);\n            }\n            else {\n                this.setValueName(params);\n            }\n        }\n        else if (params.oid !== undefined) {\n            this.setValueOidString(params.oid);\n        }\n        else if (params.hex !== undefined) {\n            this.setValueHex(params.hex);\n        }\n        else if (params.name !== undefined) {\n            this.setValueName(params.name);\n        }\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DERObjectIdentifier, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER Enumerated\n * @name KJUR.asn1.DEREnumerated\n * @class class for ASN.1 DER Enumerated\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>int - specify initial ASN.1 value(V) by integer value</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n * @example\n * new KJUR.asn1.DEREnumerated(123);\n * new KJUR.asn1.DEREnumerated({int: 123});\n * new KJUR.asn1.DEREnumerated({hex: '1fad'});\n */\nKJUR.asn1.DEREnumerated = function (params) {\n    KJUR.asn1.DEREnumerated.superclass.constructor.call(this);\n    this.hT = \"0a\";\n    /**\n     * set value by Tom Wu's BigInteger object\n     * @name setByBigInteger\n     * @memberOf KJUR.asn1.DEREnumerated#\n     * @function\n     * @param {BigInteger} bigIntegerValue to set\n     */\n    this.setByBigInteger = function (bigIntegerValue) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = KJUR.asn1.ASN1Util.bigIntToMinTwosComplementsHex(bigIntegerValue);\n    };\n    /**\n     * set value by integer value\n     * @name setByInteger\n     * @memberOf KJUR.asn1.DEREnumerated#\n     * @function\n     * @param {Integer} integer value to set\n     */\n    this.setByInteger = function (intValue) {\n        var bi = new BigInteger(String(intValue), 10);\n        this.setByBigInteger(bi);\n    };\n    /**\n     * set value by integer value\n     * @name setValueHex\n     * @memberOf KJUR.asn1.DEREnumerated#\n     * @function\n     * @param {String} hexadecimal string of integer value\n     * @description\n     * <br/>\n     * NOTE: Value shall be represented by minimum octet length of\n     * two's complement representation.\n     */\n    this.setValueHex = function (newHexString) {\n        this.hV = newHexString;\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params['int'] != \"undefined\") {\n            this.setByInteger(params['int']);\n        }\n        else if (typeof params == \"number\") {\n            this.setByInteger(params);\n        }\n        else if (typeof params['hex'] != \"undefined\") {\n            this.setValueHex(params['hex']);\n        }\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DEREnumerated, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER UTF8String\n * @name KJUR.asn1.DERUTF8String\n * @class class for ASN.1 DER UTF8String\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERUTF8String = function (params) {\n    KJUR.asn1.DERUTF8String.superclass.constructor.call(this, params);\n    this.hT = \"0c\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERUTF8String, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER NumericString\n * @name KJUR.asn1.DERNumericString\n * @class class for ASN.1 DER NumericString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERNumericString = function (params) {\n    KJUR.asn1.DERNumericString.superclass.constructor.call(this, params);\n    this.hT = \"12\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERNumericString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER PrintableString\n * @name KJUR.asn1.DERPrintableString\n * @class class for ASN.1 DER PrintableString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERPrintableString = function (params) {\n    KJUR.asn1.DERPrintableString.superclass.constructor.call(this, params);\n    this.hT = \"13\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERPrintableString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER TeletexString\n * @name KJUR.asn1.DERTeletexString\n * @class class for ASN.1 DER TeletexString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERTeletexString = function (params) {\n    KJUR.asn1.DERTeletexString.superclass.constructor.call(this, params);\n    this.hT = \"14\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERTeletexString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER IA5String\n * @name KJUR.asn1.DERIA5String\n * @class class for ASN.1 DER IA5String\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERIA5String = function (params) {\n    KJUR.asn1.DERIA5String.superclass.constructor.call(this, params);\n    this.hT = \"16\";\n};\nYAHOO.lang.extend(KJUR.asn1.DERIA5String, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER UTCTime\n * @name KJUR.asn1.DERUTCTime\n * @class class for ASN.1 DER UTCTime\n * @param {Array} params associative array of parameters (ex. {'str': '130430235959Z'})\n * @extends KJUR.asn1.DERAbstractTime\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>str - specify initial ASN.1 value(V) by a string (ex.'130430235959Z')</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * <li>date - specify Date object.</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n * <h4>EXAMPLES</h4>\n * @example\n * d1 = new KJUR.asn1.DERUTCTime();\n * d1.setString('130430125959Z');\n *\n * d2 = new KJUR.asn1.DERUTCTime({'str': '130430125959Z'});\n * d3 = new KJUR.asn1.DERUTCTime({'date': new Date(Date.UTC(2015, 0, 31, 0, 0, 0, 0))});\n * d4 = new KJUR.asn1.DERUTCTime('130430125959Z');\n */\nKJUR.asn1.DERUTCTime = function (params) {\n    KJUR.asn1.DERUTCTime.superclass.constructor.call(this, params);\n    this.hT = \"17\";\n    /**\n     * set value by a Date object<br/>\n     * @name setByDate\n     * @memberOf KJUR.asn1.DERUTCTime#\n     * @function\n     * @param {Date} dateObject Date object to set ASN.1 value(V)\n     * @example\n     * o = new KJUR.asn1.DERUTCTime();\n     * o.setByDate(new Date(\"2016/12/31\"));\n     */\n    this.setByDate = function (dateObject) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.date = dateObject;\n        this.s = this.formatDate(this.date, 'utc');\n        this.hV = stohex(this.s);\n    };\n    this.getFreshValueHex = function () {\n        if (typeof this.date == \"undefined\" && typeof this.s == \"undefined\") {\n            this.date = new Date();\n            this.s = this.formatDate(this.date, 'utc');\n            this.hV = stohex(this.s);\n        }\n        return this.hV;\n    };\n    if (params !== undefined) {\n        if (params.str !== undefined) {\n            this.setString(params.str);\n        }\n        else if (typeof params == \"string\" && params.match(/^[0-9]{12}Z$/)) {\n            this.setString(params);\n        }\n        else if (params.hex !== undefined) {\n            this.setStringHex(params.hex);\n        }\n        else if (params.date !== undefined) {\n            this.setByDate(params.date);\n        }\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DERUTCTime, KJUR.asn1.DERAbstractTime);\n// ********************************************************************\n/**\n * class for ASN.1 DER GeneralizedTime\n * @name KJUR.asn1.DERGeneralizedTime\n * @class class for ASN.1 DER GeneralizedTime\n * @param {Array} params associative array of parameters (ex. {'str': '20130430235959Z'})\n * @property {Boolean} withMillis flag to show milliseconds or not\n * @extends KJUR.asn1.DERAbstractTime\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>str - specify initial ASN.1 value(V) by a string (ex.'20130430235959Z')</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * <li>date - specify Date object.</li>\n * <li>millis - specify flag to show milliseconds (from 1.0.6)</li>\n * </ul>\n * NOTE1: 'params' can be omitted.\n * NOTE2: 'withMillis' property is supported from asn1 1.0.6.\n */\nKJUR.asn1.DERGeneralizedTime = function (params) {\n    KJUR.asn1.DERGeneralizedTime.superclass.constructor.call(this, params);\n    this.hT = \"18\";\n    this.withMillis = false;\n    /**\n     * set value by a Date object\n     * @name setByDate\n     * @memberOf KJUR.asn1.DERGeneralizedTime#\n     * @function\n     * @param {Date} dateObject Date object to set ASN.1 value(V)\n     * @example\n     * When you specify UTC time, use 'Date.UTC' method like this:<br/>\n     * o1 = new DERUTCTime();\n     * o1.setByDate(date);\n     *\n     * date = new Date(Date.UTC(2015, 0, 31, 23, 59, 59, 0)); #2015JAN31 23:59:59\n     */\n    this.setByDate = function (dateObject) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.date = dateObject;\n        this.s = this.formatDate(this.date, 'gen', this.withMillis);\n        this.hV = stohex(this.s);\n    };\n    this.getFreshValueHex = function () {\n        if (this.date === undefined && this.s === undefined) {\n            this.date = new Date();\n            this.s = this.formatDate(this.date, 'gen', this.withMillis);\n            this.hV = stohex(this.s);\n        }\n        return this.hV;\n    };\n    if (params !== undefined) {\n        if (params.str !== undefined) {\n            this.setString(params.str);\n        }\n        else if (typeof params == \"string\" && params.match(/^[0-9]{14}Z$/)) {\n            this.setString(params);\n        }\n        else if (params.hex !== undefined) {\n            this.setStringHex(params.hex);\n        }\n        else if (params.date !== undefined) {\n            this.setByDate(params.date);\n        }\n        if (params.millis === true) {\n            this.withMillis = true;\n        }\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DERGeneralizedTime, KJUR.asn1.DERAbstractTime);\n// ********************************************************************\n/**\n * class for ASN.1 DER Sequence\n * @name KJUR.asn1.DERSequence\n * @class class for ASN.1 DER Sequence\n * @extends KJUR.asn1.DERAbstractStructured\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>array - specify array of ASN1Object to set elements of content</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERSequence = function (params) {\n    KJUR.asn1.DERSequence.superclass.constructor.call(this, params);\n    this.hT = \"30\";\n    this.getFreshValueHex = function () {\n        var h = '';\n        for (var i = 0; i < this.asn1Array.length; i++) {\n            var asn1Obj = this.asn1Array[i];\n            h += asn1Obj.getEncodedHex();\n        }\n        this.hV = h;\n        return this.hV;\n    };\n};\nYAHOO.lang.extend(KJUR.asn1.DERSequence, KJUR.asn1.DERAbstractStructured);\n// ********************************************************************\n/**\n * class for ASN.1 DER Set\n * @name KJUR.asn1.DERSet\n * @class class for ASN.1 DER Set\n * @extends KJUR.asn1.DERAbstractStructured\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>array - specify array of ASN1Object to set elements of content</li>\n * <li>sortflag - flag for sort (default: true). ASN.1 BER is not sorted in 'SET OF'.</li>\n * </ul>\n * NOTE1: 'params' can be omitted.<br/>\n * NOTE2: sortflag is supported since 1.0.5.\n */\nKJUR.asn1.DERSet = function (params) {\n    KJUR.asn1.DERSet.superclass.constructor.call(this, params);\n    this.hT = \"31\";\n    this.sortFlag = true; // item shall be sorted only in ASN.1 DER\n    this.getFreshValueHex = function () {\n        var a = new Array();\n        for (var i = 0; i < this.asn1Array.length; i++) {\n            var asn1Obj = this.asn1Array[i];\n            a.push(asn1Obj.getEncodedHex());\n        }\n        if (this.sortFlag == true)\n            a.sort();\n        this.hV = a.join('');\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params.sortflag != \"undefined\" &&\n            params.sortflag == false)\n            this.sortFlag = false;\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DERSet, KJUR.asn1.DERAbstractStructured);\n// ********************************************************************\n/**\n * class for ASN.1 DER TaggedObject\n * @name KJUR.asn1.DERTaggedObject\n * @class class for ASN.1 DER TaggedObject\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * Parameter 'tagNoNex' is ASN.1 tag(T) value for this object.\n * For example, if you find '[1]' tag in a ASN.1 dump,\n * 'tagNoHex' will be 'a1'.\n * <br/>\n * As for optional argument 'params' for constructor, you can specify *ANY* of\n * following properties:\n * <ul>\n * <li>explicit - specify true if this is explicit tag otherwise false\n *     (default is 'true').</li>\n * <li>tag - specify tag (default is 'a0' which means [0])</li>\n * <li>obj - specify ASN1Object which is tagged</li>\n * </ul>\n * @example\n * d1 = new KJUR.asn1.DERUTF8String({'str':'a'});\n * d2 = new KJUR.asn1.DERTaggedObject({'obj': d1});\n * hex = d2.getEncodedHex();\n */\nKJUR.asn1.DERTaggedObject = function (params) {\n    KJUR.asn1.DERTaggedObject.superclass.constructor.call(this);\n    this.hT = \"a0\";\n    this.hV = '';\n    this.isExplicit = true;\n    this.asn1Object = null;\n    /**\n     * set value by an ASN1Object\n     * @name setString\n     * @memberOf KJUR.asn1.DERTaggedObject#\n     * @function\n     * @param {Boolean} isExplicitFlag flag for explicit/implicit tag\n     * @param {Integer} tagNoHex hexadecimal string of ASN.1 tag\n     * @param {ASN1Object} asn1Object ASN.1 to encapsulate\n     */\n    this.setASN1Object = function (isExplicitFlag, tagNoHex, asn1Object) {\n        this.hT = tagNoHex;\n        this.isExplicit = isExplicitFlag;\n        this.asn1Object = asn1Object;\n        if (this.isExplicit) {\n            this.hV = this.asn1Object.getEncodedHex();\n            this.hTLV = null;\n            this.isModified = true;\n        }\n        else {\n            this.hV = null;\n            this.hTLV = asn1Object.getEncodedHex();\n            this.hTLV = this.hTLV.replace(/^../, tagNoHex);\n            this.isModified = false;\n        }\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params['tag'] != \"undefined\") {\n            this.hT = params['tag'];\n        }\n        if (typeof params['explicit'] != \"undefined\") {\n            this.isExplicit = params['explicit'];\n        }\n        if (typeof params['obj'] != \"undefined\") {\n            this.asn1Object = params['obj'];\n            this.setASN1Object(this.isExplicit, this.hT, this.asn1Object);\n        }\n    }\n};\nYAHOO.lang.extend(KJUR.asn1.DERTaggedObject, KJUR.asn1.ASN1Object);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,KAAK,QAAQ,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,IAAI,GAAG,CAAC,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAOA,IAAI,CAACC,IAAI,IAAI,WAAW,IAAI,CAACD,IAAI,CAACC,IAAI,EAC7CD,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;AAClB;AACA;AACA;AACA;AACA;AACA;AACAD,IAAI,CAACC,IAAI,CAACC,QAAQ,GAAG,IAAI,YAAY;EACjC,IAAI,CAACC,gBAAgB,GAAG,UAAUC,CAAC,EAAE;IACjC,IAAIC,CAAC,GAAGD,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC;IACtB,IAAKD,CAAC,CAACE,MAAM,GAAG,CAAC,IAAK,CAAC,EACnBF,CAAC,GAAG,GAAG,GAAGA,CAAC;IACf,OAAOA,CAAC;EACZ,CAAC;EACD,IAAI,CAACG,6BAA6B,GAAG,UAAUC,eAAe,EAAE;IAC5D,IAAIJ,CAAC,GAAGI,eAAe,CAACH,QAAQ,CAAC,EAAE,CAAC;IACpC,IAAID,CAAC,CAACK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,EAAE;MACvB,IAAIL,CAAC,CAACE,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;QACnBF,CAAC,GAAG,GAAG,GAAGA,CAAC;MACf,CAAC,MACI;QACD,IAAI,CAACA,CAAC,CAACM,KAAK,CAAC,QAAQ,CAAC,EAAE;UACpBN,CAAC,GAAG,IAAI,GAAGA,CAAC;QAChB;MACJ;IACJ,CAAC,MACI;MACD,IAAIO,IAAI,GAAGP,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC;MACtB,IAAIG,MAAM,GAAGD,IAAI,CAACL,MAAM;MACxB,IAAIM,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;QACjBA,MAAM,IAAI,CAAC;MACf,CAAC,MACI;QACD,IAAI,CAACR,CAAC,CAACM,KAAK,CAAC,QAAQ,CAAC,EAAE;UACpBE,MAAM,IAAI,CAAC;QACf;MACJ;MACA,IAAIC,KAAK,GAAG,EAAE;MACd,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,MAAM,EAAET,CAAC,EAAE,EAAE;QAC7BU,KAAK,IAAI,GAAG;MAChB;MACA,IAAIC,MAAM,GAAG,IAAIjB,UAAU,CAACgB,KAAK,EAAE,EAAE,CAAC;MACtC,IAAIE,KAAK,GAAGD,MAAM,CAACE,GAAG,CAACR,eAAe,CAAC,CAACS,GAAG,CAACpB,UAAU,CAACqB,GAAG,CAAC;MAC3Dd,CAAC,GAAGW,KAAK,CAACV,QAAQ,CAAC,EAAE,CAAC,CAACc,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAC5C;IACA,OAAOf,CAAC;EACZ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACgB,mBAAmB,GAAG,UAAUC,OAAO,EAAEC,SAAS,EAAE;IACrD,OAAOC,QAAQ,CAACF,OAAO,EAAEC,SAAS,CAAC;EACvC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACE,SAAS,GAAG,UAAUC,KAAK,EAAE;IAC9B,IAAIC,KAAK,GAAG3B,IAAI;MAAE4B,UAAU,GAAGD,KAAK,CAAC1B,IAAI;MAAE4B,WAAW,GAAGD,UAAU,CAACE,UAAU;MAAEC,WAAW,GAAGH,UAAU,CAACI,UAAU;MAAEC,aAAa,GAAGL,UAAU,CAACM,YAAY;MAAEC,eAAe,GAAGP,UAAU,CAACQ,cAAc;MAAEC,QAAQ,GAAGT,UAAU,CAACU,OAAO;MAAEC,oBAAoB,GAAGX,UAAU,CAACY,mBAAmB;MAAEC,cAAc,GAAGb,UAAU,CAACc,aAAa;MAAEC,cAAc,GAAGf,UAAU,CAACgB,aAAa;MAAEC,iBAAiB,GAAGjB,UAAU,CAACkB,gBAAgB;MAAEC,mBAAmB,GAAGnB,UAAU,CAACoB,kBAAkB;MAAEC,iBAAiB,GAAGrB,UAAU,CAACsB,gBAAgB;MAAEC,aAAa,GAAGvB,UAAU,CAACwB,YAAY;MAAEC,WAAW,GAAGzB,UAAU,CAAC0B,UAAU;MAAEC,mBAAmB,GAAG3B,UAAU,CAAC4B,kBAAkB;MAAEC,YAAY,GAAG7B,UAAU,CAAC8B,WAAW;MAAEC,OAAO,GAAG/B,UAAU,CAACgC,MAAM;MAAEC,gBAAgB,GAAGjC,UAAU,CAACkC,eAAe;MAAEC,UAAU,GAAGnC,UAAU,CAAC1B,QAAQ,CAACuB,SAAS;IAC9yB,IAAIuC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACtC,KAAK,CAAC;IAC7B,IAAIsC,IAAI,CAACzD,MAAM,IAAI,CAAC,EAChB,MAAM,iCAAiC;IAC3C,IAAI2D,GAAG,GAAGF,IAAI,CAAC,CAAC,CAAC;IACjB,IAAI,wGAAwG,CAACG,OAAO,CAAC,GAAG,GAAGD,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EACvI,MAAM,iBAAiB,GAAGA,GAAG;IACjC,IAAIA,GAAG,IAAI,MAAM,EACb,OAAO,IAAIrC,WAAW,CAACH,KAAK,CAACwC,GAAG,CAAC,CAAC;IACtC,IAAIA,GAAG,IAAI,KAAK,EACZ,OAAO,IAAInC,WAAW,CAACL,KAAK,CAACwC,GAAG,CAAC,CAAC;IACtC,IAAIA,GAAG,IAAI,QAAQ,EACf,OAAO,IAAIjC,aAAa,CAACP,KAAK,CAACwC,GAAG,CAAC,CAAC;IACxC,IAAIA,GAAG,IAAI,QAAQ,EACf,OAAO,IAAI/B,eAAe,CAACT,KAAK,CAACwC,GAAG,CAAC,CAAC;IAC1C,IAAIA,GAAG,IAAI,MAAM,EACb,OAAO,IAAI7B,QAAQ,CAACX,KAAK,CAACwC,GAAG,CAAC,CAAC;IACnC,IAAIA,GAAG,IAAI,KAAK,EACZ,OAAO,IAAI3B,oBAAoB,CAACb,KAAK,CAACwC,GAAG,CAAC,CAAC;IAC/C,IAAIA,GAAG,IAAI,MAAM,EACb,OAAO,IAAIzB,cAAc,CAACf,KAAK,CAACwC,GAAG,CAAC,CAAC;IACzC,IAAIA,GAAG,IAAI,SAAS,EAChB,OAAO,IAAIvB,cAAc,CAACjB,KAAK,CAACwC,GAAG,CAAC,CAAC;IACzC,IAAIA,GAAG,IAAI,QAAQ,EACf,OAAO,IAAIrB,iBAAiB,CAACnB,KAAK,CAACwC,GAAG,CAAC,CAAC;IAC5C,IAAIA,GAAG,IAAI,QAAQ,EACf,OAAO,IAAInB,mBAAmB,CAACrB,KAAK,CAACwC,GAAG,CAAC,CAAC;IAC9C,IAAIA,GAAG,IAAI,QAAQ,EACf,OAAO,IAAIjB,iBAAiB,CAACvB,KAAK,CAACwC,GAAG,CAAC,CAAC;IAC5C,IAAIA,GAAG,IAAI,QAAQ,EACf,OAAO,IAAIf,aAAa,CAACzB,KAAK,CAACwC,GAAG,CAAC,CAAC;IACxC,IAAIA,GAAG,IAAI,SAAS,EAChB,OAAO,IAAIb,WAAW,CAAC3B,KAAK,CAACwC,GAAG,CAAC,CAAC;IACtC,IAAIA,GAAG,IAAI,SAAS,EAChB,OAAO,IAAIX,mBAAmB,CAAC7B,KAAK,CAACwC,GAAG,CAAC,CAAC;IAC9C,IAAIA,GAAG,IAAI,KAAK,EAAE;MACd,IAAIE,SAAS,GAAG1C,KAAK,CAACwC,GAAG,CAAC;MAC1B,IAAIG,CAAC,GAAG,EAAE;MACV,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,SAAS,CAAC7D,MAAM,EAAEH,CAAC,EAAE,EAAE;QACvC,IAAIkE,OAAO,GAAGP,UAAU,CAACK,SAAS,CAAChE,CAAC,CAAC,CAAC;QACtCiE,CAAC,CAACE,IAAI,CAACD,OAAO,CAAC;MACnB;MACA,OAAO,IAAIb,YAAY,CAAC;QAAE,OAAO,EAAEY;MAAE,CAAC,CAAC;IAC3C;IACA,IAAIH,GAAG,IAAI,KAAK,EAAE;MACd,IAAIE,SAAS,GAAG1C,KAAK,CAACwC,GAAG,CAAC;MAC1B,IAAIG,CAAC,GAAG,EAAE;MACV,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,SAAS,CAAC7D,MAAM,EAAEH,CAAC,EAAE,EAAE;QACvC,IAAIkE,OAAO,GAAGP,UAAU,CAACK,SAAS,CAAChE,CAAC,CAAC,CAAC;QACtCiE,CAAC,CAACE,IAAI,CAACD,OAAO,CAAC;MACnB;MACA,OAAO,IAAIX,OAAO,CAAC;QAAE,OAAO,EAAEU;MAAE,CAAC,CAAC;IACtC;IACA,IAAIH,GAAG,IAAI,KAAK,EAAE;MACd,IAAIM,QAAQ,GAAG9C,KAAK,CAACwC,GAAG,CAAC;MACzB,IAAID,MAAM,CAACQ,SAAS,CAACnE,QAAQ,CAACoE,IAAI,CAACF,QAAQ,CAAC,KAAK,gBAAgB,IAC7DA,QAAQ,CAACjE,MAAM,IAAI,CAAC,EAAE;QACtB,IAAIoE,GAAG,GAAGZ,UAAU,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO,IAAIX,gBAAgB,CAAC;UAAEe,GAAG,EAAEJ,QAAQ,CAAC,CAAC,CAAC;UAC1CK,QAAQ,EAAEL,QAAQ,CAAC,CAAC,CAAC;UACrBG,GAAG,EAAEA;QAAI,CAAC,CAAC;MACnB,CAAC,MACI;QACD,IAAIG,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIN,QAAQ,CAACK,QAAQ,KAAKE,SAAS,EAC/BD,QAAQ,CAACD,QAAQ,GAAGL,QAAQ,CAACK,QAAQ;QACzC,IAAIL,QAAQ,CAACI,GAAG,KAAKG,SAAS,EAC1BD,QAAQ,CAACF,GAAG,GAAGJ,QAAQ,CAACI,GAAG;QAC/B,IAAIJ,QAAQ,CAACG,GAAG,KAAKI,SAAS,EAC1B,MAAM,mCAAmC;QAC7CD,QAAQ,CAACH,GAAG,GAAGZ,UAAU,CAACS,QAAQ,CAACG,GAAG,CAAC;QACvC,OAAO,IAAId,gBAAgB,CAACiB,QAAQ,CAAC;MACzC;IACJ;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACE,aAAa,GAAG,UAAUtD,KAAK,EAAE;IAClC,IAAI4C,OAAO,GAAG,IAAI,CAAC7C,SAAS,CAACC,KAAK,CAAC;IACnC,OAAO4C,OAAO,CAACW,aAAa,CAAC,CAAC;EAClC,CAAC;AACL,CAAC,CAAD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjF,IAAI,CAACC,IAAI,CAACC,QAAQ,CAACgF,WAAW,GAAG,UAAUC,GAAG,EAAE;EAC5C,IAAIC,CAAC,GAAG,EAAE;EACV,IAAIC,GAAG,GAAGC,QAAQ,CAACH,GAAG,CAACzE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,IAAI6E,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,GAAG,EAAE,CAAC;EAC7B,IAAIK,EAAE,GAAGL,GAAG,GAAG,EAAE;EACjB,IAAID,CAAC,GAAGG,EAAE,GAAG,GAAG,GAAGG,EAAE;EACrB,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIvF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+E,GAAG,CAAC5E,MAAM,EAAEH,CAAC,IAAI,CAAC,EAAE;IACpC,IAAIwF,KAAK,GAAGN,QAAQ,CAACH,GAAG,CAACzE,MAAM,CAACN,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1C,IAAIyF,GAAG,GAAG,CAAC,UAAU,GAAGD,KAAK,CAACtF,QAAQ,CAAC,CAAC,CAAC,EAAEwF,KAAK,CAAC,CAAC,CAAC,CAAC;IACpDH,MAAM,GAAGA,MAAM,GAAGE,GAAG,CAACnF,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAClC,IAAImF,GAAG,CAACnF,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,EAAE;MACzB,IAAIqF,EAAE,GAAG,IAAIjG,UAAU,CAAC6F,MAAM,EAAE,CAAC,CAAC;MAClCP,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAGW,EAAE,CAACzF,QAAQ,CAAC,EAAE,CAAC;MAC7BqF,MAAM,GAAG,EAAE;IACf;EACJ;EACA;EACA,OAAOP,CAAC;AACZ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACApF,IAAI,CAACC,IAAI,CAACC,QAAQ,CAAC8F,WAAW,GAAG,UAAUC,SAAS,EAAE;EAClD,IAAIC,IAAI,GAAG,SAAAA,CAAU9F,CAAC,EAAE;IACpB,IAAIC,CAAC,GAAGD,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC;IACtB,IAAID,CAAC,CAACE,MAAM,IAAI,CAAC,EACbF,CAAC,GAAG,GAAG,GAAGA,CAAC;IACf,OAAOA,CAAC;EACZ,CAAC;EACD,IAAI8F,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAE;IAC1B,IAAI/F,CAAC,GAAG,EAAE;IACV,IAAI0F,EAAE,GAAG,IAAIjG,UAAU,CAACsG,IAAI,EAAE,EAAE,CAAC;IACjC,IAAIC,CAAC,GAAGN,EAAE,CAACzF,QAAQ,CAAC,CAAC,CAAC;IACtB,IAAIgG,MAAM,GAAG,CAAC,GAAGD,CAAC,CAAC9F,MAAM,GAAG,CAAC;IAC7B,IAAI+F,MAAM,IAAI,CAAC,EACXA,MAAM,GAAG,CAAC;IACd,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkG,MAAM,EAAElG,CAAC,EAAE,EAC3BmG,IAAI,IAAI,GAAG;IACfF,CAAC,GAAGE,IAAI,GAAGF,CAAC;IACZ,KAAK,IAAIjG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiG,CAAC,CAAC9F,MAAM,GAAG,CAAC,EAAEH,CAAC,IAAI,CAAC,EAAE;MACtC,IAAIoG,EAAE,GAAGH,CAAC,CAAC3F,MAAM,CAACN,CAAC,EAAE,CAAC,CAAC;MACvB,IAAIA,CAAC,IAAIiG,CAAC,CAAC9F,MAAM,GAAG,CAAC,EACjBiG,EAAE,GAAG,GAAG,GAAGA,EAAE;MACjBnG,CAAC,IAAI6F,IAAI,CAACZ,QAAQ,CAACkB,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9B;IACA,OAAOnG,CAAC;EACZ,CAAC;EACD,IAAI,CAAC4F,SAAS,CAACtF,KAAK,CAAC,WAAW,CAAC,EAAE;IAC/B,MAAM,wBAAwB,GAAGsF,SAAS;EAC9C;EACA,IAAI5F,CAAC,GAAG,EAAE;EACV,IAAIgE,CAAC,GAAG4B,SAAS,CAACQ,KAAK,CAAC,GAAG,CAAC;EAC5B,IAAIlB,EAAE,GAAGD,QAAQ,CAACjB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGiB,QAAQ,CAACjB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7ChE,CAAC,IAAI6F,IAAI,CAACX,EAAE,CAAC;EACblB,CAAC,CAACqC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACd,KAAK,IAAItG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,CAAC,CAAC9D,MAAM,EAAEH,CAAC,EAAE,EAAE;IAC/BC,CAAC,IAAI8F,OAAO,CAAC9B,CAAC,CAACjE,CAAC,CAAC,CAAC;EACtB;EACA,OAAOC,CAAC;AACZ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAL,IAAI,CAACC,IAAI,CAAC0G,UAAU,GAAG,YAAY;EAC/B,IAAIC,UAAU,GAAG,IAAI;EACrB,IAAIC,IAAI,GAAG,IAAI;EACf,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,EAAE,GAAG,EAAE;EACX;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACC,qBAAqB,GAAG,YAAY;IACrC,IAAI,OAAO,IAAI,CAACD,EAAE,IAAI,WAAW,IAAI,IAAI,CAACA,EAAE,IAAI,IAAI,EAAE;MAClD,MAAM,+BAA+B;IACzC;IACA,IAAI,IAAI,CAACA,EAAE,CAACzG,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;MACzB,MAAM,mCAAmC,GAAGyG,EAAE,CAACzG,MAAM,GAAG,KAAK,GAAG,IAAI,CAACyG,EAAE;IAC3E;IACA,IAAIE,CAAC,GAAG,IAAI,CAACF,EAAE,CAACzG,MAAM,GAAG,CAAC;IAC1B,IAAI4G,EAAE,GAAGD,CAAC,CAAC5G,QAAQ,CAAC,EAAE,CAAC;IACvB,IAAI6G,EAAE,CAAC5G,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;MACpB4G,EAAE,GAAG,GAAG,GAAGA,EAAE;IACjB;IACA,IAAID,CAAC,GAAG,GAAG,EAAE;MACT,OAAOC,EAAE;IACb,CAAC,MACI;MACD,IAAIC,KAAK,GAAGD,EAAE,CAAC5G,MAAM,GAAG,CAAC;MACzB,IAAI6G,KAAK,GAAG,EAAE,EAAE;QACZ,MAAM,gDAAgD,GAAGF,CAAC,CAAC5G,QAAQ,CAAC,EAAE,CAAC;MAC3E;MACA,IAAI+G,IAAI,GAAG,GAAG,GAAGD,KAAK;MACtB,OAAOC,IAAI,CAAC/G,QAAQ,CAAC,EAAE,CAAC,GAAG6G,EAAE;IACjC;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAAClC,aAAa,GAAG,YAAY;IAC7B,IAAI,IAAI,CAAC4B,IAAI,IAAI,IAAI,IAAI,IAAI,CAACD,UAAU,EAAE;MACtC,IAAI,CAACI,EAAE,GAAG,IAAI,CAACM,gBAAgB,CAAC,CAAC;MACjC,IAAI,CAACP,EAAE,GAAG,IAAI,CAACE,qBAAqB,CAAC,CAAC;MACtC,IAAI,CAACJ,IAAI,GAAG,IAAI,CAACC,EAAE,GAAG,IAAI,CAACC,EAAE,GAAG,IAAI,CAACC,EAAE;MACvC,IAAI,CAACJ,UAAU,GAAG,KAAK;MACvB;IACJ;IACA,OAAO,IAAI,CAACC,IAAI;EACpB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACU,WAAW,GAAG,YAAY;IAC3B,IAAI,CAACtC,aAAa,CAAC,CAAC;IACpB,OAAO,IAAI,CAAC+B,EAAE;EAClB,CAAC;EACD,IAAI,CAACM,gBAAgB,GAAG,YAAY;IAChC,OAAO,EAAE;EACb,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAtH,IAAI,CAACC,IAAI,CAACuH,iBAAiB,GAAG,UAAUC,MAAM,EAAE;EAC5CzH,IAAI,CAACC,IAAI,CAACuH,iBAAiB,CAACE,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EAC7D,IAAIU,CAAC,GAAG,IAAI;EACZ,IAAI4B,EAAE,GAAG,IAAI;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACY,SAAS,GAAG,YAAY;IACzB,OAAO,IAAI,CAACxC,CAAC;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACyC,SAAS,GAAG,UAAUC,IAAI,EAAE;IAC7B,IAAI,CAACjB,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACxB,CAAC,GAAG0C,IAAI;IACb,IAAI,CAACd,EAAE,GAAGe,MAAM,CAAC,IAAI,CAAC3C,CAAC,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAAC4C,YAAY,GAAG,UAAUC,YAAY,EAAE;IACxC,IAAI,CAACpB,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACxB,CAAC,GAAG,IAAI;IACb,IAAI,CAAC4B,EAAE,GAAGiB,YAAY;EAC1B,CAAC;EACD,IAAI,CAACX,gBAAgB,GAAG,YAAY;IAChC,OAAO,IAAI,CAACN,EAAE;EAClB,CAAC;EACD,IAAI,OAAOS,MAAM,IAAI,WAAW,EAAE;IAC9B,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;MAC3B,IAAI,CAACI,SAAS,CAACJ,MAAM,CAAC;IAC1B,CAAC,MACI,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MAC1C,IAAI,CAACI,SAAS,CAACJ,MAAM,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC,MACI,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MAC1C,IAAI,CAACO,YAAY,CAACP,MAAM,CAAC,KAAK,CAAC,CAAC;IACpC;EACJ;AACJ,CAAC;AACD1H,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACuH,iBAAiB,EAAExH,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,IAAI,CAACC,IAAI,CAACmI,eAAe,GAAG,UAAUX,MAAM,EAAE;EAC1CzH,IAAI,CAACC,IAAI,CAACmI,eAAe,CAACV,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EAC3D,IAAIU,CAAC,GAAG,IAAI;EACZ,IAAIiD,IAAI,GAAG,IAAI;EACf;EACA,IAAI,CAACC,cAAc,GAAG,UAAUC,CAAC,EAAE;IAC/BC,GAAG,GAAGD,CAAC,CAACE,OAAO,CAAC,CAAC,GAAIF,CAAC,CAACG,iBAAiB,CAAC,CAAC,GAAG,KAAM;IACnD,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAACJ,GAAG,CAAC;IAC3B,OAAOG,OAAO;EAClB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACE,UAAU,GAAG,UAAUC,UAAU,EAAEC,IAAI,EAAEC,UAAU,EAAE;IACtD,IAAIC,GAAG,GAAG,IAAI,CAACC,WAAW;IAC1B,IAAIX,CAAC,GAAG,IAAI,CAACD,cAAc,CAACQ,UAAU,CAAC;IACvC,IAAIK,IAAI,GAAGC,MAAM,CAACb,CAAC,CAACc,WAAW,CAAC,CAAC,CAAC;IAClC,IAAIN,IAAI,IAAI,KAAK,EACbI,IAAI,GAAGA,IAAI,CAACzI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B,IAAI4I,KAAK,GAAGL,GAAG,CAACG,MAAM,CAACb,CAAC,CAACgB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5C,IAAIC,GAAG,GAAGP,GAAG,CAACG,MAAM,CAACb,CAAC,CAACkB,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrC,IAAIC,IAAI,GAAGT,GAAG,CAACG,MAAM,CAACb,CAAC,CAACoB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvC,IAAIC,GAAG,GAAGX,GAAG,CAACG,MAAM,CAACb,CAAC,CAACsB,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,IAAIC,GAAG,GAAGb,GAAG,CAACG,MAAM,CAACb,CAAC,CAACwB,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,IAAI3E,CAAC,GAAG+D,IAAI,GAAGG,KAAK,GAAGE,GAAG,GAAGE,IAAI,GAAGE,GAAG,GAAGE,GAAG;IAC7C,IAAId,UAAU,KAAK,IAAI,EAAE;MACrB,IAAIgB,MAAM,GAAGzB,CAAC,CAAC0B,eAAe,CAAC,CAAC;MAChC,IAAID,MAAM,IAAI,CAAC,EAAE;QACb,IAAIE,OAAO,GAAGjB,GAAG,CAACG,MAAM,CAACY,MAAM,CAAC,EAAE,CAAC,CAAC;QACpCE,OAAO,GAAGA,OAAO,CAAC9I,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QACtCgE,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG8E,OAAO;MACzB;IACJ;IACA,OAAO9E,CAAC,GAAG,GAAG;EAClB,CAAC;EACD,IAAI,CAAC8D,WAAW,GAAG,UAAU9D,CAAC,EAAE+E,GAAG,EAAE;IACjC,IAAI/E,CAAC,CAAC7E,MAAM,IAAI4J,GAAG,EACf,OAAO/E,CAAC;IACZ,OAAO,IAAIgF,KAAK,CAACD,GAAG,GAAG/E,CAAC,CAAC7E,MAAM,GAAG,CAAC,CAAC,CAAC8J,IAAI,CAAC,GAAG,CAAC,GAAGjF,CAAC;EACtD,CAAC;EACD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACwC,SAAS,GAAG,YAAY;IACzB,OAAO,IAAI,CAACxC,CAAC;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACyC,SAAS,GAAG,UAAUC,IAAI,EAAE;IAC7B,IAAI,CAACjB,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACxB,CAAC,GAAG0C,IAAI;IACb,IAAI,CAACd,EAAE,GAAGe,MAAM,CAACD,IAAI,CAAC;EAC1B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACwC,cAAc,GAAG,UAAUnB,IAAI,EAAEG,KAAK,EAAEE,GAAG,EAAEE,IAAI,EAAEE,GAAG,EAAEE,GAAG,EAAE;IAC9D,IAAIhB,UAAU,GAAG,IAAIF,IAAI,CAACA,IAAI,CAAC2B,GAAG,CAACpB,IAAI,EAAEG,KAAK,GAAG,CAAC,EAAEE,GAAG,EAAEE,IAAI,EAAEE,GAAG,EAAEE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC5E,IAAI,CAACU,SAAS,CAAC1B,UAAU,CAAC;EAC9B,CAAC;EACD,IAAI,CAACxB,gBAAgB,GAAG,YAAY;IAChC,OAAO,IAAI,CAACN,EAAE;EAClB,CAAC;AACL,CAAC;AACDjH,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACmI,eAAe,EAAEpI,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,IAAI,CAACC,IAAI,CAACwK,qBAAqB,GAAG,UAAUhD,MAAM,EAAE;EAChDzH,IAAI,CAACC,IAAI,CAACuH,iBAAiB,CAACE,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EAC7D,IAAIgG,SAAS,GAAG,IAAI;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACC,oBAAoB,GAAG,UAAUC,eAAe,EAAE;IACnD,IAAI,CAAC/D,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC8D,SAAS,GAAGE,eAAe;EACpC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACC,gBAAgB,GAAG,UAAUC,UAAU,EAAE;IAC1C,IAAI,CAACjE,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC8D,SAAS,CAACnG,IAAI,CAACuG,UAAU,CAAC;EACnC,CAAC;EACD,IAAI,CAACJ,SAAS,GAAG,IAAIN,KAAK,CAAC,CAAC;EAC5B,IAAI,OAAO3C,MAAM,IAAI,WAAW,EAAE;IAC9B,IAAI,OAAOA,MAAM,CAAC,OAAO,CAAC,IAAI,WAAW,EAAE;MACvC,IAAI,CAACiD,SAAS,GAAGjD,MAAM,CAAC,OAAO,CAAC;IACpC;EACJ;AACJ,CAAC;AACD1H,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACwK,qBAAqB,EAAEzK,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,IAAI,CAACC,IAAI,CAAC6B,UAAU,GAAG,YAAY;EAC/B9B,IAAI,CAACC,IAAI,CAAC6B,UAAU,CAAC4F,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EACtD,IAAI,CAACoC,EAAE,GAAG,IAAI;EACd,IAAI,CAACD,IAAI,GAAG,QAAQ;AACxB,CAAC;AACD9G,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAAC6B,UAAU,EAAE9B,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,IAAI,CAACC,IAAI,CAAC+B,UAAU,GAAG,UAAUyF,MAAM,EAAE;EACrCzH,IAAI,CAACC,IAAI,CAAC+B,UAAU,CAAC0F,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EACtD,IAAI,CAACoC,EAAE,GAAG,IAAI;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACiE,eAAe,GAAG,UAAUtK,eAAe,EAAE;IAC9C,IAAI,CAACoG,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,EAAE,GAAGhH,IAAI,CAACC,IAAI,CAACC,QAAQ,CAACM,6BAA6B,CAACC,eAAe,CAAC;EAC/E,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACuK,YAAY,GAAG,UAAUC,QAAQ,EAAE;IACpC,IAAIlF,EAAE,GAAG,IAAIjG,UAAU,CAACsJ,MAAM,CAAC6B,QAAQ,CAAC,EAAE,EAAE,CAAC;IAC7C,IAAI,CAACF,eAAe,CAAChF,EAAE,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACmF,WAAW,GAAG,UAAUjD,YAAY,EAAE;IACvC,IAAI,CAACjB,EAAE,GAAGiB,YAAY;EAC1B,CAAC;EACD,IAAI,CAACX,gBAAgB,GAAG,YAAY;IAChC,OAAO,IAAI,CAACN,EAAE;EAClB,CAAC;EACD,IAAI,OAAOS,MAAM,IAAI,WAAW,EAAE;IAC9B,IAAI,OAAOA,MAAM,CAAC,QAAQ,CAAC,IAAI,WAAW,EAAE;MACxC,IAAI,CAACsD,eAAe,CAACtD,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,MACI,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MAC1C,IAAI,CAACuD,YAAY,CAACvD,MAAM,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC,MACI,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;MAChC,IAAI,CAACuD,YAAY,CAACvD,MAAM,CAAC;IAC7B,CAAC,MACI,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MAC1C,IAAI,CAACyD,WAAW,CAACzD,MAAM,CAAC,KAAK,CAAC,CAAC;IACnC;EACJ;AACJ,CAAC;AACD1H,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAAC+B,UAAU,EAAEhC,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,IAAI,CAACC,IAAI,CAACiC,YAAY,GAAG,UAAUuF,MAAM,EAAE;EACvC,IAAIA,MAAM,KAAK1C,SAAS,IAAI,OAAO0C,MAAM,CAAC9C,GAAG,KAAK,WAAW,EAAE;IAC3D,IAAIwG,CAAC,GAAGnL,IAAI,CAACC,IAAI,CAACC,QAAQ,CAACuB,SAAS,CAACgG,MAAM,CAAC9C,GAAG,CAAC;IAChD8C,MAAM,CAACtC,GAAG,GAAG,IAAI,GAAGgG,CAAC,CAAClG,aAAa,CAAC,CAAC;EACzC;EACAjF,IAAI,CAACC,IAAI,CAACiC,YAAY,CAACwF,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EACxD,IAAI,CAACoC,EAAE,GAAG,IAAI;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACsE,8BAA8B,GAAG,UAAUC,+BAA+B,EAAE;IAC7E,IAAI,CAACxE,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,EAAE,GAAGqE,+BAA+B;EAC7C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACC,wBAAwB,GAAG,UAAUC,UAAU,EAAEC,MAAM,EAAE;IAC1D,IAAID,UAAU,GAAG,CAAC,IAAI,CAAC,GAAGA,UAAU,EAAE;MAClC,MAAM,wCAAwC,GAAGA,UAAU;IAC/D;IACA,IAAIE,WAAW,GAAG,GAAG,GAAGF,UAAU;IAClC,IAAI,CAAC1E,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,EAAE,GAAGyE,WAAW,GAAGD,MAAM;EAClC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACE,iBAAiB,GAAG,UAAUC,YAAY,EAAE;IAC7CA,YAAY,GAAGA,YAAY,CAACvK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9C,IAAImK,UAAU,GAAG,CAAC,GAAGI,YAAY,CAACpL,MAAM,GAAG,CAAC;IAC5C,IAAIgL,UAAU,IAAI,CAAC,EACfA,UAAU,GAAG,CAAC;IAClB,KAAK,IAAInL,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImL,UAAU,EAAEnL,CAAC,EAAE,EAAE;MAClCuL,YAAY,IAAI,GAAG;IACvB;IACA,IAAItL,CAAC,GAAG,EAAE;IACV,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuL,YAAY,CAACpL,MAAM,GAAG,CAAC,EAAEH,CAAC,IAAI,CAAC,EAAE;MACjD,IAAIiG,CAAC,GAAGsF,YAAY,CAACjL,MAAM,CAACN,CAAC,EAAE,CAAC,CAAC;MACjC,IAAIwL,CAAC,GAAGtG,QAAQ,CAACe,CAAC,EAAE,CAAC,CAAC,CAAC/F,QAAQ,CAAC,EAAE,CAAC;MACnC,IAAIsL,CAAC,CAACrL,MAAM,IAAI,CAAC,EACbqL,CAAC,GAAG,GAAG,GAAGA,CAAC;MACfvL,CAAC,IAAIuL,CAAC;IACV;IACA,IAAI,CAAC/E,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,EAAE,GAAG,GAAG,GAAGuE,UAAU,GAAGlL,CAAC;EAClC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACwL,iBAAiB,GAAG,UAAUC,YAAY,EAAE;IAC7C,IAAI1G,CAAC,GAAG,EAAE;IACV,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0L,YAAY,CAACvL,MAAM,EAAEH,CAAC,EAAE,EAAE;MAC1C,IAAI0L,YAAY,CAAC1L,CAAC,CAAC,IAAI,IAAI,EAAE;QACzBgF,CAAC,IAAI,GAAG;MACZ,CAAC,MACI;QACDA,CAAC,IAAI,GAAG;MACZ;IACJ;IACA,IAAI,CAACsG,iBAAiB,CAACtG,CAAC,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAAC2G,aAAa,GAAG,UAAUC,OAAO,EAAE;IACpC,IAAI3H,CAAC,GAAG,IAAI+F,KAAK,CAAC4B,OAAO,CAAC;IAC1B,KAAK,IAAI5L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4L,OAAO,EAAE5L,CAAC,EAAE,EAAE;MAC9BiE,CAAC,CAACjE,CAAC,CAAC,GAAG,KAAK;IAChB;IACA,OAAOiE,CAAC;EACZ,CAAC;EACD,IAAI,CAACiD,gBAAgB,GAAG,YAAY;IAChC,OAAO,IAAI,CAACN,EAAE;EAClB,CAAC;EACD,IAAI,OAAOS,MAAM,IAAI,WAAW,EAAE;IAC9B,IAAI,OAAOA,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAACwE,WAAW,CAAC,CAAC,CAACtL,KAAK,CAAC,aAAa,CAAC,EAAE;MACxE,IAAI,CAACyK,8BAA8B,CAAC3D,MAAM,CAAC;IAC/C,CAAC,MACI,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MAC1C,IAAI,CAAC2D,8BAA8B,CAAC3D,MAAM,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC,MACI,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MAC1C,IAAI,CAACiE,iBAAiB,CAACjE,MAAM,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC,MACI,IAAI,OAAOA,MAAM,CAAC,OAAO,CAAC,IAAI,WAAW,EAAE;MAC5C,IAAI,CAACoE,iBAAiB,CAACpE,MAAM,CAAC,OAAO,CAAC,CAAC;IAC3C;EACJ;AACJ,CAAC;AACD1H,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACiC,YAAY,EAAElC,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,IAAI,CAACC,IAAI,CAACmC,cAAc,GAAG,UAAUqF,MAAM,EAAE;EACzC,IAAIA,MAAM,KAAK1C,SAAS,IAAI,OAAO0C,MAAM,CAAC9C,GAAG,KAAK,WAAW,EAAE;IAC3D,IAAIwG,CAAC,GAAGnL,IAAI,CAACC,IAAI,CAACC,QAAQ,CAACuB,SAAS,CAACgG,MAAM,CAAC9C,GAAG,CAAC;IAChD8C,MAAM,CAACtC,GAAG,GAAGgG,CAAC,CAAClG,aAAa,CAAC,CAAC;EAClC;EACAjF,IAAI,CAACC,IAAI,CAACmC,cAAc,CAACsF,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EAClE,IAAI,CAACX,EAAE,GAAG,IAAI;AAClB,CAAC;AACD/G,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACmC,cAAc,EAAEpC,IAAI,CAACC,IAAI,CAACuH,iBAAiB,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxH,IAAI,CAACC,IAAI,CAACqC,OAAO,GAAG,YAAY;EAC5BtC,IAAI,CAACC,IAAI,CAACqC,OAAO,CAACoF,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EACnD,IAAI,CAACoC,EAAE,GAAG,IAAI;EACd,IAAI,CAACD,IAAI,GAAG,MAAM;AACtB,CAAC;AACD9G,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACqC,OAAO,EAAEtC,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,IAAI,CAACC,IAAI,CAACuC,mBAAmB,GAAG,UAAUiF,MAAM,EAAE;EAC9C,IAAIvB,IAAI,GAAG,SAAAA,CAAU9F,CAAC,EAAE;IACpB,IAAIC,CAAC,GAAGD,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC;IACtB,IAAID,CAAC,CAACE,MAAM,IAAI,CAAC,EACbF,CAAC,GAAG,GAAG,GAAGA,CAAC;IACf,OAAOA,CAAC;EACZ,CAAC;EACD,IAAI8F,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAE;IAC1B,IAAI/F,CAAC,GAAG,EAAE;IACV,IAAI0F,EAAE,GAAG,IAAIjG,UAAU,CAACsG,IAAI,EAAE,EAAE,CAAC;IACjC,IAAIC,CAAC,GAAGN,EAAE,CAACzF,QAAQ,CAAC,CAAC,CAAC;IACtB,IAAIgG,MAAM,GAAG,CAAC,GAAGD,CAAC,CAAC9F,MAAM,GAAG,CAAC;IAC7B,IAAI+F,MAAM,IAAI,CAAC,EACXA,MAAM,GAAG,CAAC;IACd,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkG,MAAM,EAAElG,CAAC,EAAE,EAC3BmG,IAAI,IAAI,GAAG;IACfF,CAAC,GAAGE,IAAI,GAAGF,CAAC;IACZ,KAAK,IAAIjG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiG,CAAC,CAAC9F,MAAM,GAAG,CAAC,EAAEH,CAAC,IAAI,CAAC,EAAE;MACtC,IAAIoG,EAAE,GAAGH,CAAC,CAAC3F,MAAM,CAACN,CAAC,EAAE,CAAC,CAAC;MACvB,IAAIA,CAAC,IAAIiG,CAAC,CAAC9F,MAAM,GAAG,CAAC,EACjBiG,EAAE,GAAG,GAAG,GAAGA,EAAE;MACjBnG,CAAC,IAAI6F,IAAI,CAACZ,QAAQ,CAACkB,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9B;IACA,OAAOnG,CAAC;EACZ,CAAC;EACDL,IAAI,CAACC,IAAI,CAACuC,mBAAmB,CAACkF,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EAC/D,IAAI,CAACoC,EAAE,GAAG,IAAI;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACoE,WAAW,GAAG,UAAUjD,YAAY,EAAE;IACvC,IAAI,CAACpB,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACxB,CAAC,GAAG,IAAI;IACb,IAAI,CAAC4B,EAAE,GAAGiB,YAAY;EAC1B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACiE,iBAAiB,GAAG,UAAUjG,SAAS,EAAE;IAC1C,IAAI,CAACA,SAAS,CAACtF,KAAK,CAAC,WAAW,CAAC,EAAE;MAC/B,MAAM,wBAAwB,GAAGsF,SAAS;IAC9C;IACA,IAAI5F,CAAC,GAAG,EAAE;IACV,IAAIgE,CAAC,GAAG4B,SAAS,CAACQ,KAAK,CAAC,GAAG,CAAC;IAC5B,IAAIlB,EAAE,GAAGD,QAAQ,CAACjB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGiB,QAAQ,CAACjB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7ChE,CAAC,IAAI6F,IAAI,CAACX,EAAE,CAAC;IACblB,CAAC,CAACqC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,KAAK,IAAItG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,CAAC,CAAC9D,MAAM,EAAEH,CAAC,EAAE,EAAE;MAC/BC,CAAC,IAAI8F,OAAO,CAAC9B,CAAC,CAACjE,CAAC,CAAC,CAAC;IACtB;IACA,IAAI,CAACyG,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACxB,CAAC,GAAG,IAAI;IACb,IAAI,CAAC4B,EAAE,GAAG3G,CAAC;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAAC8L,YAAY,GAAG,UAAUC,OAAO,EAAE;IACnC,IAAIC,GAAG,GAAGrM,IAAI,CAACC,IAAI,CAACqM,IAAI,CAACC,GAAG,CAACC,QAAQ,CAACJ,OAAO,CAAC;IAC9C,IAAIC,GAAG,KAAK,EAAE,EAAE;MACZ,IAAI,CAACH,iBAAiB,CAACG,GAAG,CAAC;IAC/B,CAAC,MACI;MACD,MAAM,yCAAyC,GAAGD,OAAO;IAC7D;EACJ,CAAC;EACD,IAAI,CAAC9E,gBAAgB,GAAG,YAAY;IAChC,OAAO,IAAI,CAACN,EAAE;EAClB,CAAC;EACD,IAAIS,MAAM,KAAK1C,SAAS,EAAE;IACtB,IAAI,OAAO0C,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAIA,MAAM,CAAC9G,KAAK,CAAC,iBAAiB,CAAC,EAAE;QACjC,IAAI,CAACuL,iBAAiB,CAACzE,MAAM,CAAC;MAClC,CAAC,MACI;QACD,IAAI,CAAC0E,YAAY,CAAC1E,MAAM,CAAC;MAC7B;IACJ,CAAC,MACI,IAAIA,MAAM,CAAC4E,GAAG,KAAKtH,SAAS,EAAE;MAC/B,IAAI,CAACmH,iBAAiB,CAACzE,MAAM,CAAC4E,GAAG,CAAC;IACtC,CAAC,MACI,IAAI5E,MAAM,CAACtC,GAAG,KAAKJ,SAAS,EAAE;MAC/B,IAAI,CAACmG,WAAW,CAACzD,MAAM,CAACtC,GAAG,CAAC;IAChC,CAAC,MACI,IAAIsC,MAAM,CAACgF,IAAI,KAAK1H,SAAS,EAAE;MAChC,IAAI,CAACoH,YAAY,CAAC1E,MAAM,CAACgF,IAAI,CAAC;IAClC;EACJ;AACJ,CAAC;AACD1M,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACuC,mBAAmB,EAAExC,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,IAAI,CAACC,IAAI,CAACyC,aAAa,GAAG,UAAU+E,MAAM,EAAE;EACxCzH,IAAI,CAACC,IAAI,CAACyC,aAAa,CAACgF,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EACzD,IAAI,CAACoC,EAAE,GAAG,IAAI;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACiE,eAAe,GAAG,UAAUtK,eAAe,EAAE;IAC9C,IAAI,CAACoG,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,EAAE,GAAGhH,IAAI,CAACC,IAAI,CAACC,QAAQ,CAACM,6BAA6B,CAACC,eAAe,CAAC;EAC/E,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACuK,YAAY,GAAG,UAAUC,QAAQ,EAAE;IACpC,IAAIlF,EAAE,GAAG,IAAIjG,UAAU,CAACsJ,MAAM,CAAC6B,QAAQ,CAAC,EAAE,EAAE,CAAC;IAC7C,IAAI,CAACF,eAAe,CAAChF,EAAE,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACmF,WAAW,GAAG,UAAUjD,YAAY,EAAE;IACvC,IAAI,CAACjB,EAAE,GAAGiB,YAAY;EAC1B,CAAC;EACD,IAAI,CAACX,gBAAgB,GAAG,YAAY;IAChC,OAAO,IAAI,CAACN,EAAE;EAClB,CAAC;EACD,IAAI,OAAOS,MAAM,IAAI,WAAW,EAAE;IAC9B,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MACrC,IAAI,CAACuD,YAAY,CAACvD,MAAM,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC,MACI,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;MAChC,IAAI,CAACuD,YAAY,CAACvD,MAAM,CAAC;IAC7B,CAAC,MACI,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MAC1C,IAAI,CAACyD,WAAW,CAACzD,MAAM,CAAC,KAAK,CAAC,CAAC;IACnC;EACJ;AACJ,CAAC;AACD1H,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACyC,aAAa,EAAE1C,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,IAAI,CAACC,IAAI,CAAC2C,aAAa,GAAG,UAAU6E,MAAM,EAAE;EACxCzH,IAAI,CAACC,IAAI,CAAC2C,aAAa,CAAC8E,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EACjE,IAAI,CAACX,EAAE,GAAG,IAAI;AAClB,CAAC;AACD/G,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAAC2C,aAAa,EAAE5C,IAAI,CAACC,IAAI,CAACuH,iBAAiB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxH,IAAI,CAACC,IAAI,CAAC6C,gBAAgB,GAAG,UAAU2E,MAAM,EAAE;EAC3CzH,IAAI,CAACC,IAAI,CAAC6C,gBAAgB,CAAC4E,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EACpE,IAAI,CAACX,EAAE,GAAG,IAAI;AAClB,CAAC;AACD/G,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAAC6C,gBAAgB,EAAE9C,IAAI,CAACC,IAAI,CAACuH,iBAAiB,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxH,IAAI,CAACC,IAAI,CAAC+C,kBAAkB,GAAG,UAAUyE,MAAM,EAAE;EAC7CzH,IAAI,CAACC,IAAI,CAAC+C,kBAAkB,CAAC0E,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EACtE,IAAI,CAACX,EAAE,GAAG,IAAI;AAClB,CAAC;AACD/G,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAAC+C,kBAAkB,EAAEhD,IAAI,CAACC,IAAI,CAACuH,iBAAiB,CAAC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxH,IAAI,CAACC,IAAI,CAACiD,gBAAgB,GAAG,UAAUuE,MAAM,EAAE;EAC3CzH,IAAI,CAACC,IAAI,CAACiD,gBAAgB,CAACwE,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EACpE,IAAI,CAACX,EAAE,GAAG,IAAI;AAClB,CAAC;AACD/G,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACiD,gBAAgB,EAAElD,IAAI,CAACC,IAAI,CAACuH,iBAAiB,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxH,IAAI,CAACC,IAAI,CAACmD,YAAY,GAAG,UAAUqE,MAAM,EAAE;EACvCzH,IAAI,CAACC,IAAI,CAACmD,YAAY,CAACsE,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EAChE,IAAI,CAACX,EAAE,GAAG,IAAI;AAClB,CAAC;AACD/G,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACmD,YAAY,EAAEpD,IAAI,CAACC,IAAI,CAACuH,iBAAiB,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxH,IAAI,CAACC,IAAI,CAACqD,UAAU,GAAG,UAAUmE,MAAM,EAAE;EACrCzH,IAAI,CAACC,IAAI,CAACqD,UAAU,CAACoE,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EAC9D,IAAI,CAACX,EAAE,GAAG,IAAI;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAAC0D,SAAS,GAAG,UAAU1B,UAAU,EAAE;IACnC,IAAI,CAACjC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACyB,IAAI,GAAGS,UAAU;IACtB,IAAI,CAAC1D,CAAC,GAAG,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACR,IAAI,EAAE,KAAK,CAAC;IAC1C,IAAI,CAACrB,EAAE,GAAGe,MAAM,CAAC,IAAI,CAAC3C,CAAC,CAAC;EAC5B,CAAC;EACD,IAAI,CAACkC,gBAAgB,GAAG,YAAY;IAChC,IAAI,OAAO,IAAI,CAACe,IAAI,IAAI,WAAW,IAAI,OAAO,IAAI,CAACjD,CAAC,IAAI,WAAW,EAAE;MACjE,IAAI,CAACiD,IAAI,GAAG,IAAIO,IAAI,CAAC,CAAC;MACtB,IAAI,CAACxD,CAAC,GAAG,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACR,IAAI,EAAE,KAAK,CAAC;MAC1C,IAAI,CAACrB,EAAE,GAAGe,MAAM,CAAC,IAAI,CAAC3C,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAAC4B,EAAE;EAClB,CAAC;EACD,IAAIS,MAAM,KAAK1C,SAAS,EAAE;IACtB,IAAI0C,MAAM,CAACiF,GAAG,KAAK3H,SAAS,EAAE;MAC1B,IAAI,CAAC8C,SAAS,CAACJ,MAAM,CAACiF,GAAG,CAAC;IAC9B,CAAC,MACI,IAAI,OAAOjF,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAAC9G,KAAK,CAAC,cAAc,CAAC,EAAE;MAChE,IAAI,CAACkH,SAAS,CAACJ,MAAM,CAAC;IAC1B,CAAC,MACI,IAAIA,MAAM,CAACtC,GAAG,KAAKJ,SAAS,EAAE;MAC/B,IAAI,CAACiD,YAAY,CAACP,MAAM,CAACtC,GAAG,CAAC;IACjC,CAAC,MACI,IAAIsC,MAAM,CAACY,IAAI,KAAKtD,SAAS,EAAE;MAChC,IAAI,CAACyF,SAAS,CAAC/C,MAAM,CAACY,IAAI,CAAC;IAC/B;EACJ;AACJ,CAAC;AACDtI,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACqD,UAAU,EAAEtD,IAAI,CAACC,IAAI,CAACmI,eAAe,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACApI,IAAI,CAACC,IAAI,CAACuD,kBAAkB,GAAG,UAAUiE,MAAM,EAAE;EAC7CzH,IAAI,CAACC,IAAI,CAACuD,kBAAkB,CAACkE,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EACtE,IAAI,CAACX,EAAE,GAAG,IAAI;EACd,IAAI,CAACkC,UAAU,GAAG,KAAK;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACwB,SAAS,GAAG,UAAU1B,UAAU,EAAE;IACnC,IAAI,CAACjC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACyB,IAAI,GAAGS,UAAU;IACtB,IAAI,CAAC1D,CAAC,GAAG,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACR,IAAI,EAAE,KAAK,EAAE,IAAI,CAACW,UAAU,CAAC;IAC3D,IAAI,CAAChC,EAAE,GAAGe,MAAM,CAAC,IAAI,CAAC3C,CAAC,CAAC;EAC5B,CAAC;EACD,IAAI,CAACkC,gBAAgB,GAAG,YAAY;IAChC,IAAI,IAAI,CAACe,IAAI,KAAKtD,SAAS,IAAI,IAAI,CAACK,CAAC,KAAKL,SAAS,EAAE;MACjD,IAAI,CAACsD,IAAI,GAAG,IAAIO,IAAI,CAAC,CAAC;MACtB,IAAI,CAACxD,CAAC,GAAG,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACR,IAAI,EAAE,KAAK,EAAE,IAAI,CAACW,UAAU,CAAC;MAC3D,IAAI,CAAChC,EAAE,GAAGe,MAAM,CAAC,IAAI,CAAC3C,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAAC4B,EAAE;EAClB,CAAC;EACD,IAAIS,MAAM,KAAK1C,SAAS,EAAE;IACtB,IAAI0C,MAAM,CAACiF,GAAG,KAAK3H,SAAS,EAAE;MAC1B,IAAI,CAAC8C,SAAS,CAACJ,MAAM,CAACiF,GAAG,CAAC;IAC9B,CAAC,MACI,IAAI,OAAOjF,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAAC9G,KAAK,CAAC,cAAc,CAAC,EAAE;MAChE,IAAI,CAACkH,SAAS,CAACJ,MAAM,CAAC;IAC1B,CAAC,MACI,IAAIA,MAAM,CAACtC,GAAG,KAAKJ,SAAS,EAAE;MAC/B,IAAI,CAACiD,YAAY,CAACP,MAAM,CAACtC,GAAG,CAAC;IACjC,CAAC,MACI,IAAIsC,MAAM,CAACY,IAAI,KAAKtD,SAAS,EAAE;MAChC,IAAI,CAACyF,SAAS,CAAC/C,MAAM,CAACY,IAAI,CAAC;IAC/B;IACA,IAAIZ,MAAM,CAACuC,MAAM,KAAK,IAAI,EAAE;MACxB,IAAI,CAAChB,UAAU,GAAG,IAAI;IAC1B;EACJ;AACJ,CAAC;AACDjJ,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACuD,kBAAkB,EAAExD,IAAI,CAACC,IAAI,CAACmI,eAAe,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACApI,IAAI,CAACC,IAAI,CAACyD,WAAW,GAAG,UAAU+D,MAAM,EAAE;EACtCzH,IAAI,CAACC,IAAI,CAACyD,WAAW,CAACgE,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EAC/D,IAAI,CAACX,EAAE,GAAG,IAAI;EACd,IAAI,CAACQ,gBAAgB,GAAG,YAAY;IAChC,IAAIjH,CAAC,GAAG,EAAE;IACV,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACsK,SAAS,CAACnK,MAAM,EAAEH,CAAC,EAAE,EAAE;MAC5C,IAAIkE,OAAO,GAAG,IAAI,CAACoG,SAAS,CAACtK,CAAC,CAAC;MAC/BC,CAAC,IAAIiE,OAAO,CAACW,aAAa,CAAC,CAAC;IAChC;IACA,IAAI,CAAC+B,EAAE,GAAG3G,CAAC;IACX,OAAO,IAAI,CAAC2G,EAAE;EAClB,CAAC;AACL,CAAC;AACDjH,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAACyD,WAAW,EAAE1D,IAAI,CAACC,IAAI,CAACwK,qBAAqB,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAzK,IAAI,CAACC,IAAI,CAAC2D,MAAM,GAAG,UAAU6D,MAAM,EAAE;EACjCzH,IAAI,CAACC,IAAI,CAAC2D,MAAM,CAAC8D,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,EAAE+C,MAAM,CAAC;EAC1D,IAAI,CAACX,EAAE,GAAG,IAAI;EACd,IAAI,CAAC6F,QAAQ,GAAG,IAAI,CAAC,CAAC;EACtB,IAAI,CAACrF,gBAAgB,GAAG,YAAY;IAChC,IAAIjD,CAAC,GAAG,IAAI+F,KAAK,CAAC,CAAC;IACnB,KAAK,IAAIhK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACsK,SAAS,CAACnK,MAAM,EAAEH,CAAC,EAAE,EAAE;MAC5C,IAAIkE,OAAO,GAAG,IAAI,CAACoG,SAAS,CAACtK,CAAC,CAAC;MAC/BiE,CAAC,CAACE,IAAI,CAACD,OAAO,CAACW,aAAa,CAAC,CAAC,CAAC;IACnC;IACA,IAAI,IAAI,CAAC0H,QAAQ,IAAI,IAAI,EACrBtI,CAAC,CAACuI,IAAI,CAAC,CAAC;IACZ,IAAI,CAAC5F,EAAE,GAAG3C,CAAC,CAACgG,IAAI,CAAC,EAAE,CAAC;IACpB,OAAO,IAAI,CAACrD,EAAE;EAClB,CAAC;EACD,IAAI,OAAOS,MAAM,IAAI,WAAW,EAAE;IAC9B,IAAI,OAAOA,MAAM,CAACoF,QAAQ,IAAI,WAAW,IACrCpF,MAAM,CAACoF,QAAQ,IAAI,KAAK,EACxB,IAAI,CAACF,QAAQ,GAAG,KAAK;EAC7B;AACJ,CAAC;AACD5M,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAAC2D,MAAM,EAAE5D,IAAI,CAACC,IAAI,CAACwK,qBAAqB,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAzK,IAAI,CAACC,IAAI,CAAC6D,eAAe,GAAG,UAAU2D,MAAM,EAAE;EAC1CzH,IAAI,CAACC,IAAI,CAAC6D,eAAe,CAAC4D,UAAU,CAACC,WAAW,CAACjD,IAAI,CAAC,IAAI,CAAC;EAC3D,IAAI,CAACoC,EAAE,GAAG,IAAI;EACd,IAAI,CAACE,EAAE,GAAG,EAAE;EACZ,IAAI,CAAC8F,UAAU,GAAG,IAAI;EACtB,IAAI,CAAChC,UAAU,GAAG,IAAI;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACiC,aAAa,GAAG,UAAUC,cAAc,EAAEC,QAAQ,EAAEnC,UAAU,EAAE;IACjE,IAAI,CAAChE,EAAE,GAAGmG,QAAQ;IAClB,IAAI,CAACH,UAAU,GAAGE,cAAc;IAChC,IAAI,CAAClC,UAAU,GAAGA,UAAU;IAC5B,IAAI,IAAI,CAACgC,UAAU,EAAE;MACjB,IAAI,CAAC9F,EAAE,GAAG,IAAI,CAAC8D,UAAU,CAAC7F,aAAa,CAAC,CAAC;MACzC,IAAI,CAAC4B,IAAI,GAAG,IAAI;MAChB,IAAI,CAACD,UAAU,GAAG,IAAI;IAC1B,CAAC,MACI;MACD,IAAI,CAACI,EAAE,GAAG,IAAI;MACd,IAAI,CAACH,IAAI,GAAGiE,UAAU,CAAC7F,aAAa,CAAC,CAAC;MACtC,IAAI,CAAC4B,IAAI,GAAG,IAAI,CAACA,IAAI,CAACzF,OAAO,CAAC,KAAK,EAAE6L,QAAQ,CAAC;MAC9C,IAAI,CAACrG,UAAU,GAAG,KAAK;IAC3B;EACJ,CAAC;EACD,IAAI,CAACU,gBAAgB,GAAG,YAAY;IAChC,OAAO,IAAI,CAACN,EAAE;EAClB,CAAC;EACD,IAAI,OAAOS,MAAM,IAAI,WAAW,EAAE;IAC9B,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MACrC,IAAI,CAACX,EAAE,GAAGW,MAAM,CAAC,KAAK,CAAC;IAC3B;IACA,IAAI,OAAOA,MAAM,CAAC,UAAU,CAAC,IAAI,WAAW,EAAE;MAC1C,IAAI,CAACqF,UAAU,GAAGrF,MAAM,CAAC,UAAU,CAAC;IACxC;IACA,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;MACrC,IAAI,CAACqD,UAAU,GAAGrD,MAAM,CAAC,KAAK,CAAC;MAC/B,IAAI,CAACsF,aAAa,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAAChG,EAAE,EAAE,IAAI,CAACgE,UAAU,CAAC;IACjE;EACJ;AACJ,CAAC;AACD/K,KAAK,CAACmI,IAAI,CAACC,MAAM,CAACnI,IAAI,CAACC,IAAI,CAAC6D,eAAe,EAAE9D,IAAI,CAACC,IAAI,CAAC0G,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}