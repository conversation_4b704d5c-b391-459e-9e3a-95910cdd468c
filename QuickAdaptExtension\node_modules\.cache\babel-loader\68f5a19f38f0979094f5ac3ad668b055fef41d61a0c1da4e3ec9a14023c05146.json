{"ast": null, "code": "export var calculateChange = function calculateChange(e, direction, hsl, container) {\n  var containerWidth = container.clientWidth;\n  var containerHeight = container.clientHeight;\n  var x = typeof e.pageX === 'number' ? e.pageX : e.touches[0].pageX;\n  var y = typeof e.pageY === 'number' ? e.pageY : e.touches[0].pageY;\n  var left = x - (container.getBoundingClientRect().left + window.pageXOffset);\n  var top = y - (container.getBoundingClientRect().top + window.pageYOffset);\n  if (direction === 'vertical') {\n    var h = void 0;\n    if (top < 0) {\n      h = 359;\n    } else if (top > containerHeight) {\n      h = 0;\n    } else {\n      var percent = -(top * 100 / containerHeight) + 100;\n      h = 360 * percent / 100;\n    }\n    if (hsl.h !== h) {\n      return {\n        h: h,\n        s: hsl.s,\n        l: hsl.l,\n        a: hsl.a,\n        source: 'hsl'\n      };\n    }\n  } else {\n    var _h = void 0;\n    if (left < 0) {\n      _h = 0;\n    } else if (left > containerWidth) {\n      _h = 359;\n    } else {\n      var _percent = left * 100 / containerWidth;\n      _h = 360 * _percent / 100;\n    }\n    if (hsl.h !== _h) {\n      return {\n        h: _h,\n        s: hsl.s,\n        l: hsl.l,\n        a: hsl.a,\n        source: 'hsl'\n      };\n    }\n  }\n  return null;\n};", "map": {"version": 3, "names": ["calculateChange", "e", "direction", "hsl", "container", "containerWidth", "clientWidth", "containerHeight", "clientHeight", "x", "pageX", "touches", "y", "pageY", "left", "getBoundingClientRect", "window", "pageXOffset", "top", "pageYOffset", "h", "percent", "s", "l", "a", "source", "_h", "_percent"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/react-color/es/helpers/hue.js"], "sourcesContent": ["export var calculateChange = function calculateChange(e, direction, hsl, container) {\n  var containerWidth = container.clientWidth;\n  var containerHeight = container.clientHeight;\n  var x = typeof e.pageX === 'number' ? e.pageX : e.touches[0].pageX;\n  var y = typeof e.pageY === 'number' ? e.pageY : e.touches[0].pageY;\n  var left = x - (container.getBoundingClientRect().left + window.pageXOffset);\n  var top = y - (container.getBoundingClientRect().top + window.pageYOffset);\n\n  if (direction === 'vertical') {\n    var h = void 0;\n    if (top < 0) {\n      h = 359;\n    } else if (top > containerHeight) {\n      h = 0;\n    } else {\n      var percent = -(top * 100 / containerHeight) + 100;\n      h = 360 * percent / 100;\n    }\n\n    if (hsl.h !== h) {\n      return {\n        h: h,\n        s: hsl.s,\n        l: hsl.l,\n        a: hsl.a,\n        source: 'hsl'\n      };\n    }\n  } else {\n    var _h = void 0;\n    if (left < 0) {\n      _h = 0;\n    } else if (left > containerWidth) {\n      _h = 359;\n    } else {\n      var _percent = left * 100 / containerWidth;\n      _h = 360 * _percent / 100;\n    }\n\n    if (hsl.h !== _h) {\n      return {\n        h: _h,\n        s: hsl.s,\n        l: hsl.l,\n        a: hsl.a,\n        source: 'hsl'\n      };\n    }\n  }\n  return null;\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAEC,SAAS,EAAEC,GAAG,EAAEC,SAAS,EAAE;EAClF,IAAIC,cAAc,GAAGD,SAAS,CAACE,WAAW;EAC1C,IAAIC,eAAe,GAAGH,SAAS,CAACI,YAAY;EAC5C,IAAIC,CAAC,GAAG,OAAOR,CAAC,CAACS,KAAK,KAAK,QAAQ,GAAGT,CAAC,CAACS,KAAK,GAAGT,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,CAACD,KAAK;EAClE,IAAIE,CAAC,GAAG,OAAOX,CAAC,CAACY,KAAK,KAAK,QAAQ,GAAGZ,CAAC,CAACY,KAAK,GAAGZ,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,CAACE,KAAK;EAClE,IAAIC,IAAI,GAAGL,CAAC,IAAIL,SAAS,CAACW,qBAAqB,CAAC,CAAC,CAACD,IAAI,GAAGE,MAAM,CAACC,WAAW,CAAC;EAC5E,IAAIC,GAAG,GAAGN,CAAC,IAAIR,SAAS,CAACW,qBAAqB,CAAC,CAAC,CAACG,GAAG,GAAGF,MAAM,CAACG,WAAW,CAAC;EAE1E,IAAIjB,SAAS,KAAK,UAAU,EAAE;IAC5B,IAAIkB,CAAC,GAAG,KAAK,CAAC;IACd,IAAIF,GAAG,GAAG,CAAC,EAAE;MACXE,CAAC,GAAG,GAAG;IACT,CAAC,MAAM,IAAIF,GAAG,GAAGX,eAAe,EAAE;MAChCa,CAAC,GAAG,CAAC;IACP,CAAC,MAAM;MACL,IAAIC,OAAO,GAAG,EAAEH,GAAG,GAAG,GAAG,GAAGX,eAAe,CAAC,GAAG,GAAG;MAClDa,CAAC,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG;IACzB;IAEA,IAAIlB,GAAG,CAACiB,CAAC,KAAKA,CAAC,EAAE;MACf,OAAO;QACLA,CAAC,EAAEA,CAAC;QACJE,CAAC,EAAEnB,GAAG,CAACmB,CAAC;QACRC,CAAC,EAAEpB,GAAG,CAACoB,CAAC;QACRC,CAAC,EAAErB,GAAG,CAACqB,CAAC;QACRC,MAAM,EAAE;MACV,CAAC;IACH;EACF,CAAC,MAAM;IACL,IAAIC,EAAE,GAAG,KAAK,CAAC;IACf,IAAIZ,IAAI,GAAG,CAAC,EAAE;MACZY,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAIZ,IAAI,GAAGT,cAAc,EAAE;MAChCqB,EAAE,GAAG,GAAG;IACV,CAAC,MAAM;MACL,IAAIC,QAAQ,GAAGb,IAAI,GAAG,GAAG,GAAGT,cAAc;MAC1CqB,EAAE,GAAG,GAAG,GAAGC,QAAQ,GAAG,GAAG;IAC3B;IAEA,IAAIxB,GAAG,CAACiB,CAAC,KAAKM,EAAE,EAAE;MAChB,OAAO;QACLN,CAAC,EAAEM,EAAE;QACLJ,CAAC,EAAEnB,GAAG,CAACmB,CAAC;QACRC,CAAC,EAAEpB,GAAG,CAACoB,CAAC;QACRC,CAAC,EAAErB,GAAG,CAACqB,CAAC;QACRC,MAAM,EAAE;MACV,CAAC;IACH;EACF;EACA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}