{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridDensityFactorSelector } from \"../density/index.js\";\nimport { useGridLogger, useGridSelector, useGridApiMethod, useGridApiEventHandler } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPageCountSelector, gridPaginationModelSelector } from \"./gridPaginationSelector.js\";\nimport { getPageCount, defaultPageSize, throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel, getValidPage } from \"./gridPaginationUtils.js\";\nexport const getDerivedPaginationModel = (paginationState, signature, paginationModelProp) => {\n  let paginationModel = paginationState.paginationModel;\n  const rowCount = paginationState.rowCount;\n  const pageSize = paginationModelProp?.pageSize ?? paginationModel.pageSize;\n  const page = paginationModelProp?.page ?? paginationModel.page;\n  const pageCount = getPageCount(rowCount, pageSize, page);\n  if (paginationModelProp && (paginationModelProp?.page !== paginationModel.page || paginationModelProp?.pageSize !== paginationModel.pageSize)) {\n    paginationModel = paginationModelProp;\n  }\n  const validPage = getValidPage(paginationModel.page, pageCount);\n  if (validPage !== paginationModel.page) {\n    paginationModel = _extends({}, paginationModel, {\n      page: validPage\n    });\n  }\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, signature);\n  return paginationModel;\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPaginationModel = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPaginationModel');\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const rowHeight = Math.floor(props.rowHeight * densityFactor);\n  apiRef.current.registerControlState({\n    stateId: 'paginationModel',\n    propModel: props.paginationModel,\n    propOnChange: props.onPaginationModelChange,\n    stateSelector: gridPaginationModelSelector,\n    changeEvent: 'paginationModelChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setPage = React.useCallback(page => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (page === currentModel.page) {\n      return;\n    }\n    logger.debug(`Setting page to ${page}`);\n    apiRef.current.setPaginationModel({\n      page,\n      pageSize: currentModel.pageSize\n    });\n  }, [apiRef, logger]);\n  const setPageSize = React.useCallback(pageSize => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (pageSize === currentModel.pageSize) {\n      return;\n    }\n    logger.debug(`Setting page size to ${pageSize}`);\n    apiRef.current.setPaginationModel({\n      pageSize,\n      page: currentModel.page\n    });\n  }, [apiRef, logger]);\n  const setPaginationModel = React.useCallback(paginationModel => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel === currentModel) {\n      return;\n    }\n    logger.debug(\"Setting 'paginationModel' to\", paginationModel);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }));\n  }, [apiRef, logger, props.signature]);\n  const paginationModelApi = {\n    setPage,\n    setPageSize,\n    setPaginationModel\n  };\n  useGridApiMethod(apiRef, paginationModelApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    const shouldExportPaginationModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `paginationModel` is controlled\n    props.paginationModel != null ||\n    // Always export if the `paginationModel` has been initialized\n    props.initialState?.pagination?.paginationModel != null ||\n    // Export if `page` or `pageSize` is not equal to the default value\n    paginationModel.page !== 0 && paginationModel.pageSize !== defaultPageSize(props.autoPageSize);\n    if (!shouldExportPaginationModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        paginationModel\n      })\n    });\n  }, [apiRef, props.paginationModel, props.initialState?.pagination?.paginationModel, props.autoPageSize]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const paginationModel = context.stateToRestore.pagination?.paginationModel ? _extends({}, getDefaultGridPaginationModel(props.autoPageSize), context.stateToRestore.pagination?.paginationModel) : gridPaginationModelSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }));\n    return params;\n  }, [apiRef, props.autoPageSize, props.signature]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const handlePaginationModelChange = () => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (apiRef.current.virtualScrollerRef?.current) {\n      apiRef.current.scrollToIndexes({\n        rowIndex: paginationModel.page * paginationModel.pageSize\n      });\n    }\n  };\n  const handleUpdateAutoPageSize = React.useCallback(() => {\n    if (!props.autoPageSize) {\n      return;\n    }\n    const dimensions = apiRef.current.getRootDimensions();\n    const maximumPageSizeWithoutScrollBar = Math.floor(dimensions.viewportInnerSize.height / rowHeight);\n    apiRef.current.setPageSize(maximumPageSizeWithoutScrollBar);\n  }, [apiRef, props.autoPageSize, rowHeight]);\n  const handleRowCountChange = React.useCallback(newRowCount => {\n    if (newRowCount == null) {\n      return;\n    }\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    const pageCount = gridPageCountSelector(apiRef);\n    if (paginationModel.page > pageCount - 1) {\n      apiRef.current.setPage(Math.max(0, pageCount - 1));\n    }\n  }, [apiRef]);\n  useGridApiEventHandler(apiRef, 'viewportInnerSizeChange', handleUpdateAutoPageSize);\n  useGridApiEventHandler(apiRef, 'paginationModelChange', handlePaginationModelChange);\n  useGridApiEventHandler(apiRef, 'rowCountChange', handleRowCountChange);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, props.paginationModel)\n      })\n    }));\n  }, [apiRef, props.paginationModel, props.paginationMode, props.signature]);\n  React.useEffect(handleUpdateAutoPageSize, [handleUpdateAutoPageSize]);\n};", "map": {"version": 3, "names": ["_extends", "React", "gridDensityFactorSelector", "useGridLogger", "useGridSelector", "useGridApiMethod", "useGridApiEventHandler", "useGridRegisterPipeProcessor", "gridPageCountSelector", "gridPaginationModelSelector", "getPageCount", "defaultPageSize", "throwIfPageSizeExceedsTheLimit", "getDefaultGridPaginationModel", "getValidPage", "getDerivedPaginationModel", "paginationState", "signature", "paginationModelProp", "paginationModel", "rowCount", "pageSize", "page", "pageCount", "validPage", "useGridPaginationModel", "apiRef", "props", "logger", "densityFactor", "rowHeight", "Math", "floor", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onPaginationModelChange", "stateSelector", "changeEvent", "setPage", "useCallback", "currentModel", "debug", "setPaginationModel", "setPageSize", "setState", "state", "pagination", "paginationModelApi", "stateExportPreProcessing", "prevState", "context", "shouldExportPaginationModel", "exportOnlyDirtyModels", "initialState", "autoPageSize", "stateRestorePreProcessing", "params", "stateToRestore", "handlePaginationModelChange", "virtualScrollerRef", "scrollToIndexes", "rowIndex", "handleUpdateAutoPageSize", "dimensions", "getRootDimensions", "maximumPageSizeWithoutScrollBar", "viewportInnerSize", "height", "handleRowCountChange", "newRowCount", "max", "useEffect", "paginationMode"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/pagination/useGridPaginationModel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridDensityFactorSelector } from \"../density/index.js\";\nimport { useGridLogger, useGridSelector, useGridApiMethod, useGridApiEventHandler } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPageCountSelector, gridPaginationModelSelector } from \"./gridPaginationSelector.js\";\nimport { getPageCount, defaultPageSize, throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel, getValidPage } from \"./gridPaginationUtils.js\";\nexport const getDerivedPaginationModel = (paginationState, signature, paginationModelProp) => {\n  let paginationModel = paginationState.paginationModel;\n  const rowCount = paginationState.rowCount;\n  const pageSize = paginationModelProp?.pageSize ?? paginationModel.pageSize;\n  const page = paginationModelProp?.page ?? paginationModel.page;\n  const pageCount = getPageCount(rowCount, pageSize, page);\n  if (paginationModelProp && (paginationModelProp?.page !== paginationModel.page || paginationModelProp?.pageSize !== paginationModel.pageSize)) {\n    paginationModel = paginationModelProp;\n  }\n  const validPage = getValidPage(paginationModel.page, pageCount);\n  if (validPage !== paginationModel.page) {\n    paginationModel = _extends({}, paginationModel, {\n      page: validPage\n    });\n  }\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, signature);\n  return paginationModel;\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPaginationModel = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPaginationModel');\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const rowHeight = Math.floor(props.rowHeight * densityFactor);\n  apiRef.current.registerControlState({\n    stateId: 'paginationModel',\n    propModel: props.paginationModel,\n    propOnChange: props.onPaginationModelChange,\n    stateSelector: gridPaginationModelSelector,\n    changeEvent: 'paginationModelChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setPage = React.useCallback(page => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (page === currentModel.page) {\n      return;\n    }\n    logger.debug(`Setting page to ${page}`);\n    apiRef.current.setPaginationModel({\n      page,\n      pageSize: currentModel.pageSize\n    });\n  }, [apiRef, logger]);\n  const setPageSize = React.useCallback(pageSize => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (pageSize === currentModel.pageSize) {\n      return;\n    }\n    logger.debug(`Setting page size to ${pageSize}`);\n    apiRef.current.setPaginationModel({\n      pageSize,\n      page: currentModel.page\n    });\n  }, [apiRef, logger]);\n  const setPaginationModel = React.useCallback(paginationModel => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel === currentModel) {\n      return;\n    }\n    logger.debug(\"Setting 'paginationModel' to\", paginationModel);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }));\n  }, [apiRef, logger, props.signature]);\n  const paginationModelApi = {\n    setPage,\n    setPageSize,\n    setPaginationModel\n  };\n  useGridApiMethod(apiRef, paginationModelApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    const shouldExportPaginationModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `paginationModel` is controlled\n    props.paginationModel != null ||\n    // Always export if the `paginationModel` has been initialized\n    props.initialState?.pagination?.paginationModel != null ||\n    // Export if `page` or `pageSize` is not equal to the default value\n    paginationModel.page !== 0 && paginationModel.pageSize !== defaultPageSize(props.autoPageSize);\n    if (!shouldExportPaginationModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        paginationModel\n      })\n    });\n  }, [apiRef, props.paginationModel, props.initialState?.pagination?.paginationModel, props.autoPageSize]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const paginationModel = context.stateToRestore.pagination?.paginationModel ? _extends({}, getDefaultGridPaginationModel(props.autoPageSize), context.stateToRestore.pagination?.paginationModel) : gridPaginationModelSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }));\n    return params;\n  }, [apiRef, props.autoPageSize, props.signature]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const handlePaginationModelChange = () => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (apiRef.current.virtualScrollerRef?.current) {\n      apiRef.current.scrollToIndexes({\n        rowIndex: paginationModel.page * paginationModel.pageSize\n      });\n    }\n  };\n  const handleUpdateAutoPageSize = React.useCallback(() => {\n    if (!props.autoPageSize) {\n      return;\n    }\n    const dimensions = apiRef.current.getRootDimensions();\n    const maximumPageSizeWithoutScrollBar = Math.floor(dimensions.viewportInnerSize.height / rowHeight);\n    apiRef.current.setPageSize(maximumPageSizeWithoutScrollBar);\n  }, [apiRef, props.autoPageSize, rowHeight]);\n  const handleRowCountChange = React.useCallback(newRowCount => {\n    if (newRowCount == null) {\n      return;\n    }\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    const pageCount = gridPageCountSelector(apiRef);\n    if (paginationModel.page > pageCount - 1) {\n      apiRef.current.setPage(Math.max(0, pageCount - 1));\n    }\n  }, [apiRef]);\n  useGridApiEventHandler(apiRef, 'viewportInnerSizeChange', handleUpdateAutoPageSize);\n  useGridApiEventHandler(apiRef, 'paginationModelChange', handlePaginationModelChange);\n  useGridApiEventHandler(apiRef, 'rowCountChange', handleRowCountChange);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, props.paginationModel)\n      })\n    }));\n  }, [apiRef, props.paginationModel, props.paginationMode, props.signature]);\n  React.useEffect(handleUpdateAutoPageSize, [handleUpdateAutoPageSize]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,sBAAsB,QAAQ,sBAAsB;AAC/G,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,qBAAqB,EAAEC,2BAA2B,QAAQ,6BAA6B;AAChG,SAASC,YAAY,EAAEC,eAAe,EAAEC,8BAA8B,EAAEC,6BAA6B,EAAEC,YAAY,QAAQ,0BAA0B;AACrJ,OAAO,MAAMC,yBAAyB,GAAGA,CAACC,eAAe,EAAEC,SAAS,EAAEC,mBAAmB,KAAK;EAC5F,IAAIC,eAAe,GAAGH,eAAe,CAACG,eAAe;EACrD,MAAMC,QAAQ,GAAGJ,eAAe,CAACI,QAAQ;EACzC,MAAMC,QAAQ,GAAGH,mBAAmB,EAAEG,QAAQ,IAAIF,eAAe,CAACE,QAAQ;EAC1E,MAAMC,IAAI,GAAGJ,mBAAmB,EAAEI,IAAI,IAAIH,eAAe,CAACG,IAAI;EAC9D,MAAMC,SAAS,GAAGb,YAAY,CAACU,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,CAAC;EACxD,IAAIJ,mBAAmB,KAAKA,mBAAmB,EAAEI,IAAI,KAAKH,eAAe,CAACG,IAAI,IAAIJ,mBAAmB,EAAEG,QAAQ,KAAKF,eAAe,CAACE,QAAQ,CAAC,EAAE;IAC7IF,eAAe,GAAGD,mBAAmB;EACvC;EACA,MAAMM,SAAS,GAAGV,YAAY,CAACK,eAAe,CAACG,IAAI,EAAEC,SAAS,CAAC;EAC/D,IAAIC,SAAS,KAAKL,eAAe,CAACG,IAAI,EAAE;IACtCH,eAAe,GAAGnB,QAAQ,CAAC,CAAC,CAAC,EAAEmB,eAAe,EAAE;MAC9CG,IAAI,EAAEE;IACR,CAAC,CAAC;EACJ;EACAZ,8BAA8B,CAACO,eAAe,CAACE,QAAQ,EAAEJ,SAAS,CAAC;EACnE,OAAOE,eAAe;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMM,sBAAsB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACvD,MAAMC,MAAM,GAAGzB,aAAa,CAACuB,MAAM,EAAE,wBAAwB,CAAC;EAC9D,MAAMG,aAAa,GAAGzB,eAAe,CAACsB,MAAM,EAAExB,yBAAyB,CAAC;EACxE,MAAM4B,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACL,KAAK,CAACG,SAAS,GAAGD,aAAa,CAAC;EAC7DH,MAAM,CAACO,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,iBAAiB;IAC1BC,SAAS,EAAET,KAAK,CAACR,eAAe;IAChCkB,YAAY,EAAEV,KAAK,CAACW,uBAAuB;IAC3CC,aAAa,EAAE9B,2BAA2B;IAC1C+B,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;AACF;AACA;EACE,MAAMC,OAAO,GAAGxC,KAAK,CAACyC,WAAW,CAACpB,IAAI,IAAI;IACxC,MAAMqB,YAAY,GAAGlC,2BAA2B,CAACiB,MAAM,CAAC;IACxD,IAAIJ,IAAI,KAAKqB,YAAY,CAACrB,IAAI,EAAE;MAC9B;IACF;IACAM,MAAM,CAACgB,KAAK,CAAC,mBAAmBtB,IAAI,EAAE,CAAC;IACvCI,MAAM,CAACO,OAAO,CAACY,kBAAkB,CAAC;MAChCvB,IAAI;MACJD,QAAQ,EAAEsB,YAAY,CAACtB;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACK,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMkB,WAAW,GAAG7C,KAAK,CAACyC,WAAW,CAACrB,QAAQ,IAAI;IAChD,MAAMsB,YAAY,GAAGlC,2BAA2B,CAACiB,MAAM,CAAC;IACxD,IAAIL,QAAQ,KAAKsB,YAAY,CAACtB,QAAQ,EAAE;MACtC;IACF;IACAO,MAAM,CAACgB,KAAK,CAAC,wBAAwBvB,QAAQ,EAAE,CAAC;IAChDK,MAAM,CAACO,OAAO,CAACY,kBAAkB,CAAC;MAChCxB,QAAQ;MACRC,IAAI,EAAEqB,YAAY,CAACrB;IACrB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACI,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMiB,kBAAkB,GAAG5C,KAAK,CAACyC,WAAW,CAACvB,eAAe,IAAI;IAC9D,MAAMwB,YAAY,GAAGlC,2BAA2B,CAACiB,MAAM,CAAC;IACxD,IAAIP,eAAe,KAAKwB,YAAY,EAAE;MACpC;IACF;IACAf,MAAM,CAACgB,KAAK,CAAC,8BAA8B,EAAEzB,eAAe,CAAC;IAC7DO,MAAM,CAACO,OAAO,CAACc,QAAQ,CAACC,KAAK,IAAIhD,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,EAAE;MACnDC,UAAU,EAAEjD,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,CAACC,UAAU,EAAE;QACzC9B,eAAe,EAAEJ,yBAAyB,CAACiC,KAAK,CAACC,UAAU,EAAEtB,KAAK,CAACV,SAAS,EAAEE,eAAe;MAC/F,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACO,MAAM,EAAEE,MAAM,EAAED,KAAK,CAACV,SAAS,CAAC,CAAC;EACrC,MAAMiC,kBAAkB,GAAG;IACzBT,OAAO;IACPK,WAAW;IACXD;EACF,CAAC;EACDxC,gBAAgB,CAACqB,MAAM,EAAEwB,kBAAkB,EAAE,QAAQ,CAAC;;EAEtD;AACF;AACA;EACE,MAAMC,wBAAwB,GAAGlD,KAAK,CAACyC,WAAW,CAAC,CAACU,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMlC,eAAe,GAAGV,2BAA2B,CAACiB,MAAM,CAAC;IAC3D,MAAM4B,2BAA2B;IACjC;IACA,CAACD,OAAO,CAACE,qBAAqB;IAC9B;IACA5B,KAAK,CAACR,eAAe,IAAI,IAAI;IAC7B;IACAQ,KAAK,CAAC6B,YAAY,EAAEP,UAAU,EAAE9B,eAAe,IAAI,IAAI;IACvD;IACAA,eAAe,CAACG,IAAI,KAAK,CAAC,IAAIH,eAAe,CAACE,QAAQ,KAAKV,eAAe,CAACgB,KAAK,CAAC8B,YAAY,CAAC;IAC9F,IAAI,CAACH,2BAA2B,EAAE;MAChC,OAAOF,SAAS;IAClB;IACA,OAAOpD,QAAQ,CAAC,CAAC,CAAC,EAAEoD,SAAS,EAAE;MAC7BH,UAAU,EAAEjD,QAAQ,CAAC,CAAC,CAAC,EAAEoD,SAAS,CAACH,UAAU,EAAE;QAC7C9B;MACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACO,MAAM,EAAEC,KAAK,CAACR,eAAe,EAAEQ,KAAK,CAAC6B,YAAY,EAAEP,UAAU,EAAE9B,eAAe,EAAEQ,KAAK,CAAC8B,YAAY,CAAC,CAAC;EACxG,MAAMC,yBAAyB,GAAGzD,KAAK,CAACyC,WAAW,CAAC,CAACiB,MAAM,EAAEN,OAAO,KAAK;IACvE,MAAMlC,eAAe,GAAGkC,OAAO,CAACO,cAAc,CAACX,UAAU,EAAE9B,eAAe,GAAGnB,QAAQ,CAAC,CAAC,CAAC,EAAEa,6BAA6B,CAACc,KAAK,CAAC8B,YAAY,CAAC,EAAEJ,OAAO,CAACO,cAAc,CAACX,UAAU,EAAE9B,eAAe,CAAC,GAAGV,2BAA2B,CAACiB,MAAM,CAAC;IACtOA,MAAM,CAACO,OAAO,CAACc,QAAQ,CAACC,KAAK,IAAIhD,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,EAAE;MACnDC,UAAU,EAAEjD,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,CAACC,UAAU,EAAE;QACzC9B,eAAe,EAAEJ,yBAAyB,CAACiC,KAAK,CAACC,UAAU,EAAEtB,KAAK,CAACV,SAAS,EAAEE,eAAe;MAC/F,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAOwC,MAAM;EACf,CAAC,EAAE,CAACjC,MAAM,EAAEC,KAAK,CAAC8B,YAAY,EAAE9B,KAAK,CAACV,SAAS,CAAC,CAAC;EACjDV,4BAA4B,CAACmB,MAAM,EAAE,aAAa,EAAEyB,wBAAwB,CAAC;EAC7E5C,4BAA4B,CAACmB,MAAM,EAAE,cAAc,EAAEgC,yBAAyB,CAAC;;EAE/E;AACF;AACA;EACE,MAAMG,2BAA2B,GAAGA,CAAA,KAAM;IACxC,MAAM1C,eAAe,GAAGV,2BAA2B,CAACiB,MAAM,CAAC;IAC3D,IAAIA,MAAM,CAACO,OAAO,CAAC6B,kBAAkB,EAAE7B,OAAO,EAAE;MAC9CP,MAAM,CAACO,OAAO,CAAC8B,eAAe,CAAC;QAC7BC,QAAQ,EAAE7C,eAAe,CAACG,IAAI,GAAGH,eAAe,CAACE;MACnD,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM4C,wBAAwB,GAAGhE,KAAK,CAACyC,WAAW,CAAC,MAAM;IACvD,IAAI,CAACf,KAAK,CAAC8B,YAAY,EAAE;MACvB;IACF;IACA,MAAMS,UAAU,GAAGxC,MAAM,CAACO,OAAO,CAACkC,iBAAiB,CAAC,CAAC;IACrD,MAAMC,+BAA+B,GAAGrC,IAAI,CAACC,KAAK,CAACkC,UAAU,CAACG,iBAAiB,CAACC,MAAM,GAAGxC,SAAS,CAAC;IACnGJ,MAAM,CAACO,OAAO,CAACa,WAAW,CAACsB,+BAA+B,CAAC;EAC7D,CAAC,EAAE,CAAC1C,MAAM,EAAEC,KAAK,CAAC8B,YAAY,EAAE3B,SAAS,CAAC,CAAC;EAC3C,MAAMyC,oBAAoB,GAAGtE,KAAK,CAACyC,WAAW,CAAC8B,WAAW,IAAI;IAC5D,IAAIA,WAAW,IAAI,IAAI,EAAE;MACvB;IACF;IACA,MAAMrD,eAAe,GAAGV,2BAA2B,CAACiB,MAAM,CAAC;IAC3D,MAAMH,SAAS,GAAGf,qBAAqB,CAACkB,MAAM,CAAC;IAC/C,IAAIP,eAAe,CAACG,IAAI,GAAGC,SAAS,GAAG,CAAC,EAAE;MACxCG,MAAM,CAACO,OAAO,CAACQ,OAAO,CAACV,IAAI,CAAC0C,GAAG,CAAC,CAAC,EAAElD,SAAS,GAAG,CAAC,CAAC,CAAC;IACpD;EACF,CAAC,EAAE,CAACG,MAAM,CAAC,CAAC;EACZpB,sBAAsB,CAACoB,MAAM,EAAE,yBAAyB,EAAEuC,wBAAwB,CAAC;EACnF3D,sBAAsB,CAACoB,MAAM,EAAE,uBAAuB,EAAEmC,2BAA2B,CAAC;EACpFvD,sBAAsB,CAACoB,MAAM,EAAE,gBAAgB,EAAE6C,oBAAoB,CAAC;;EAEtE;AACF;AACA;EACEtE,KAAK,CAACyE,SAAS,CAAC,MAAM;IACpBhD,MAAM,CAACO,OAAO,CAACc,QAAQ,CAACC,KAAK,IAAIhD,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,EAAE;MACnDC,UAAU,EAAEjD,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,CAACC,UAAU,EAAE;QACzC9B,eAAe,EAAEJ,yBAAyB,CAACiC,KAAK,CAACC,UAAU,EAAEtB,KAAK,CAACV,SAAS,EAAEU,KAAK,CAACR,eAAe;MACrG,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACO,MAAM,EAAEC,KAAK,CAACR,eAAe,EAAEQ,KAAK,CAACgD,cAAc,EAAEhD,KAAK,CAACV,SAAS,CAAC,CAAC;EAC1EhB,KAAK,CAACyE,SAAS,CAACT,wBAAwB,EAAE,CAACA,wBAAwB,CAAC,CAAC;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}