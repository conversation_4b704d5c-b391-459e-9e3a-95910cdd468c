{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { gridFilteredTopLevelRowCountSelector } from \"../filter/index.js\";\nimport { useGridLogger, useGridSelector, useGridApiMethod, useGridApiEventHandler } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPaginationRowCountSelector, gridPaginationMetaSelector, gridPaginationModelSelector } from \"./gridPaginationSelector.js\";\nexport const useGridRowCount = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridRowCount');\n  const visibleTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);\n  const rowCountState = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  const paginationMeta = useGridSelector(apiRef, gridPaginationMetaSelector);\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const previousPageSize = useLazyRef(() => gridPaginationModelSelector(apiRef).pageSize);\n  apiRef.current.registerControlState({\n    stateId: 'paginationRowCount',\n    propModel: props.rowCount,\n    propOnChange: props.onRowCountChange,\n    stateSelector: gridPaginationRowCountSelector,\n    changeEvent: 'rowCountChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setRowCount = React.useCallback(newRowCount => {\n    if (rowCountState === newRowCount) {\n      return;\n    }\n    logger.debug(\"Setting 'rowCount' to\", newRowCount);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: newRowCount\n      })\n    }));\n  }, [apiRef, logger, rowCountState]);\n  const paginationRowCountApi = {\n    setRowCount\n  };\n  useGridApiMethod(apiRef, paginationRowCountApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const exportedRowCount = gridPaginationRowCountSelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `rowCount` is controlled\n    props.rowCount != null ||\n    // Always export if the `rowCount` has been initialized\n    props.initialState?.pagination?.rowCount != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        rowCount: exportedRowCount\n      })\n    });\n  }, [apiRef, props.rowCount, props.initialState?.pagination?.rowCount]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const restoredRowCount = context.stateToRestore.pagination?.rowCount ? context.stateToRestore.pagination.rowCount : gridPaginationRowCountSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: restoredRowCount\n      })\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const handlePaginationModelChange = React.useCallback(model => {\n    if (props.paginationMode === 'client' || !previousPageSize.current) {\n      return;\n    }\n    if (model.pageSize !== previousPageSize.current) {\n      previousPageSize.current = model.pageSize;\n      if (rowCountState === -1) {\n        // Row count unknown and page size changed, reset the page\n        apiRef.current.setPage(0);\n      }\n    }\n  }, [props.paginationMode, previousPageSize, rowCountState, apiRef]);\n  useGridApiEventHandler(apiRef, 'paginationModelChange', handlePaginationModelChange);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (props.paginationMode === 'client') {\n      apiRef.current.setRowCount(visibleTopLevelRowCount);\n    } else if (props.rowCount != null) {\n      apiRef.current.setRowCount(props.rowCount);\n    }\n  }, [apiRef, props.paginationMode, visibleTopLevelRowCount, props.rowCount]);\n  const isLastPage = paginationMeta.hasNextPage === false;\n  React.useEffect(() => {\n    if (isLastPage && rowCountState === -1) {\n      apiRef.current.setRowCount(paginationModel.pageSize * paginationModel.page + visibleTopLevelRowCount);\n    }\n  }, [apiRef, visibleTopLevelRowCount, isLastPage, rowCountState, paginationModel]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useLazyRef", "gridFilteredTopLevelRowCountSelector", "useGridLogger", "useGridSelector", "useGridApiMethod", "useGridApiEventHandler", "useGridRegisterPipeProcessor", "gridPaginationRowCountSelector", "gridPaginationMetaSelector", "gridPaginationModelSelector", "useGridRowCount", "apiRef", "props", "logger", "visibleTopLevelRowCount", "rowCountState", "paginationMeta", "paginationModel", "previousPageSize", "pageSize", "current", "registerControlState", "stateId", "propModel", "rowCount", "propOnChange", "onRowCountChange", "stateSelector", "changeEvent", "setRowCount", "useCallback", "newRowCount", "debug", "setState", "state", "pagination", "paginationRowCountApi", "stateExportPreProcessing", "prevState", "context", "exportedRowCount", "shouldExportRowCount", "exportOnlyDirtyModels", "initialState", "stateRestorePreProcessing", "params", "restoredRowCount", "stateToRestore", "handlePaginationModelChange", "model", "paginationMode", "setPage", "useEffect", "isLastPage", "hasNextPage", "page"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/pagination/useGridRowCount.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { gridFilteredTopLevelRowCountSelector } from \"../filter/index.js\";\nimport { useGridLogger, useGridSelector, useGridApiMethod, useGridApiEventHandler } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPaginationRowCountSelector, gridPaginationMetaSelector, gridPaginationModelSelector } from \"./gridPaginationSelector.js\";\nexport const useGridRowCount = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridRowCount');\n  const visibleTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);\n  const rowCountState = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  const paginationMeta = useGridSelector(apiRef, gridPaginationMetaSelector);\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const previousPageSize = useLazyRef(() => gridPaginationModelSelector(apiRef).pageSize);\n  apiRef.current.registerControlState({\n    stateId: 'paginationRowCount',\n    propModel: props.rowCount,\n    propOnChange: props.onRowCountChange,\n    stateSelector: gridPaginationRowCountSelector,\n    changeEvent: 'rowCountChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setRowCount = React.useCallback(newRowCount => {\n    if (rowCountState === newRowCount) {\n      return;\n    }\n    logger.debug(\"Setting 'rowCount' to\", newRowCount);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: newRowCount\n      })\n    }));\n  }, [apiRef, logger, rowCountState]);\n  const paginationRowCountApi = {\n    setRowCount\n  };\n  useGridApiMethod(apiRef, paginationRowCountApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const exportedRowCount = gridPaginationRowCountSelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `rowCount` is controlled\n    props.rowCount != null ||\n    // Always export if the `rowCount` has been initialized\n    props.initialState?.pagination?.rowCount != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        rowCount: exportedRowCount\n      })\n    });\n  }, [apiRef, props.rowCount, props.initialState?.pagination?.rowCount]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const restoredRowCount = context.stateToRestore.pagination?.rowCount ? context.stateToRestore.pagination.rowCount : gridPaginationRowCountSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: restoredRowCount\n      })\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const handlePaginationModelChange = React.useCallback(model => {\n    if (props.paginationMode === 'client' || !previousPageSize.current) {\n      return;\n    }\n    if (model.pageSize !== previousPageSize.current) {\n      previousPageSize.current = model.pageSize;\n      if (rowCountState === -1) {\n        // Row count unknown and page size changed, reset the page\n        apiRef.current.setPage(0);\n      }\n    }\n  }, [props.paginationMode, previousPageSize, rowCountState, apiRef]);\n  useGridApiEventHandler(apiRef, 'paginationModelChange', handlePaginationModelChange);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (props.paginationMode === 'client') {\n      apiRef.current.setRowCount(visibleTopLevelRowCount);\n    } else if (props.rowCount != null) {\n      apiRef.current.setRowCount(props.rowCount);\n    }\n  }, [apiRef, props.paginationMode, visibleTopLevelRowCount, props.rowCount]);\n  const isLastPage = paginationMeta.hasNextPage === false;\n  React.useEffect(() => {\n    if (isLastPage && rowCountState === -1) {\n      apiRef.current.setRowCount(paginationModel.pageSize * paginationModel.page + visibleTopLevelRowCount);\n    }\n  }, [apiRef, visibleTopLevelRowCount, isLastPage, rowCountState, paginationModel]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,oCAAoC,QAAQ,oBAAoB;AACzE,SAASC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,sBAAsB,QAAQ,sBAAsB;AAC/G,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,8BAA8B,EAAEC,0BAA0B,EAAEC,2BAA2B,QAAQ,6BAA6B;AACrI,OAAO,MAAMC,eAAe,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAChD,MAAMC,MAAM,GAAGX,aAAa,CAACS,MAAM,EAAE,iBAAiB,CAAC;EACvD,MAAMG,uBAAuB,GAAGX,eAAe,CAACQ,MAAM,EAAEV,oCAAoC,CAAC;EAC7F,MAAMc,aAAa,GAAGZ,eAAe,CAACQ,MAAM,EAAEJ,8BAA8B,CAAC;EAC7E,MAAMS,cAAc,GAAGb,eAAe,CAACQ,MAAM,EAAEH,0BAA0B,CAAC;EAC1E,MAAMS,eAAe,GAAGd,eAAe,CAACQ,MAAM,EAAEF,2BAA2B,CAAC;EAC5E,MAAMS,gBAAgB,GAAGlB,UAAU,CAAC,MAAMS,2BAA2B,CAACE,MAAM,CAAC,CAACQ,QAAQ,CAAC;EACvFR,MAAM,CAACS,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,oBAAoB;IAC7BC,SAAS,EAAEX,KAAK,CAACY,QAAQ;IACzBC,YAAY,EAAEb,KAAK,CAACc,gBAAgB;IACpCC,aAAa,EAAEpB,8BAA8B;IAC7CqB,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;AACF;AACA;EACE,MAAMC,WAAW,GAAG9B,KAAK,CAAC+B,WAAW,CAACC,WAAW,IAAI;IACnD,IAAIhB,aAAa,KAAKgB,WAAW,EAAE;MACjC;IACF;IACAlB,MAAM,CAACmB,KAAK,CAAC,uBAAuB,EAAED,WAAW,CAAC;IAClDpB,MAAM,CAACS,OAAO,CAACa,QAAQ,CAACC,KAAK,IAAIpC,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;MACnDC,UAAU,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,CAACC,UAAU,EAAE;QACzCX,QAAQ,EAAEO;MACZ,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACpB,MAAM,EAAEE,MAAM,EAAEE,aAAa,CAAC,CAAC;EACnC,MAAMqB,qBAAqB,GAAG;IAC5BP;EACF,CAAC;EACDzB,gBAAgB,CAACO,MAAM,EAAEyB,qBAAqB,EAAE,QAAQ,CAAC;;EAEzD;AACF;AACA;EACE,MAAMC,wBAAwB,GAAGtC,KAAK,CAAC+B,WAAW,CAAC,CAACQ,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMC,gBAAgB,GAAGjC,8BAA8B,CAACI,MAAM,CAAC;IAC/D,MAAM8B,oBAAoB;IAC1B;IACA,CAACF,OAAO,CAACG,qBAAqB;IAC9B;IACA9B,KAAK,CAACY,QAAQ,IAAI,IAAI;IACtB;IACAZ,KAAK,CAAC+B,YAAY,EAAER,UAAU,EAAEX,QAAQ,IAAI,IAAI;IAChD,IAAI,CAACiB,oBAAoB,EAAE;MACzB,OAAOH,SAAS;IAClB;IACA,OAAOxC,QAAQ,CAAC,CAAC,CAAC,EAAEwC,SAAS,EAAE;MAC7BH,UAAU,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAEwC,SAAS,CAACH,UAAU,EAAE;QAC7CX,QAAQ,EAAEgB;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,MAAM,EAAEC,KAAK,CAACY,QAAQ,EAAEZ,KAAK,CAAC+B,YAAY,EAAER,UAAU,EAAEX,QAAQ,CAAC,CAAC;EACtE,MAAMoB,yBAAyB,GAAG7C,KAAK,CAAC+B,WAAW,CAAC,CAACe,MAAM,EAAEN,OAAO,KAAK;IACvE,MAAMO,gBAAgB,GAAGP,OAAO,CAACQ,cAAc,CAACZ,UAAU,EAAEX,QAAQ,GAAGe,OAAO,CAACQ,cAAc,CAACZ,UAAU,CAACX,QAAQ,GAAGjB,8BAA8B,CAACI,MAAM,CAAC;IAC1JA,MAAM,CAACS,OAAO,CAACa,QAAQ,CAACC,KAAK,IAAIpC,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;MACnDC,UAAU,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,CAACC,UAAU,EAAE;QACzCX,QAAQ,EAAEsB;MACZ,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAOD,MAAM;EACf,CAAC,EAAE,CAAClC,MAAM,CAAC,CAAC;EACZL,4BAA4B,CAACK,MAAM,EAAE,aAAa,EAAE0B,wBAAwB,CAAC;EAC7E/B,4BAA4B,CAACK,MAAM,EAAE,cAAc,EAAEiC,yBAAyB,CAAC;;EAE/E;AACF;AACA;EACE,MAAMI,2BAA2B,GAAGjD,KAAK,CAAC+B,WAAW,CAACmB,KAAK,IAAI;IAC7D,IAAIrC,KAAK,CAACsC,cAAc,KAAK,QAAQ,IAAI,CAAChC,gBAAgB,CAACE,OAAO,EAAE;MAClE;IACF;IACA,IAAI6B,KAAK,CAAC9B,QAAQ,KAAKD,gBAAgB,CAACE,OAAO,EAAE;MAC/CF,gBAAgB,CAACE,OAAO,GAAG6B,KAAK,CAAC9B,QAAQ;MACzC,IAAIJ,aAAa,KAAK,CAAC,CAAC,EAAE;QACxB;QACAJ,MAAM,CAACS,OAAO,CAAC+B,OAAO,CAAC,CAAC,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACvC,KAAK,CAACsC,cAAc,EAAEhC,gBAAgB,EAAEH,aAAa,EAAEJ,MAAM,CAAC,CAAC;EACnEN,sBAAsB,CAACM,MAAM,EAAE,uBAAuB,EAAEqC,2BAA2B,CAAC;;EAEpF;AACF;AACA;EACEjD,KAAK,CAACqD,SAAS,CAAC,MAAM;IACpB,IAAIxC,KAAK,CAACsC,cAAc,KAAK,QAAQ,EAAE;MACrCvC,MAAM,CAACS,OAAO,CAACS,WAAW,CAACf,uBAAuB,CAAC;IACrD,CAAC,MAAM,IAAIF,KAAK,CAACY,QAAQ,IAAI,IAAI,EAAE;MACjCb,MAAM,CAACS,OAAO,CAACS,WAAW,CAACjB,KAAK,CAACY,QAAQ,CAAC;IAC5C;EACF,CAAC,EAAE,CAACb,MAAM,EAAEC,KAAK,CAACsC,cAAc,EAAEpC,uBAAuB,EAAEF,KAAK,CAACY,QAAQ,CAAC,CAAC;EAC3E,MAAM6B,UAAU,GAAGrC,cAAc,CAACsC,WAAW,KAAK,KAAK;EACvDvD,KAAK,CAACqD,SAAS,CAAC,MAAM;IACpB,IAAIC,UAAU,IAAItC,aAAa,KAAK,CAAC,CAAC,EAAE;MACtCJ,MAAM,CAACS,OAAO,CAACS,WAAW,CAACZ,eAAe,CAACE,QAAQ,GAAGF,eAAe,CAACsC,IAAI,GAAGzC,uBAAuB,CAAC;IACvG;EACF,CAAC,EAAE,CAACH,MAAM,EAAEG,uBAAuB,EAAEuC,UAAU,EAAEtC,aAAa,EAAEE,eAAe,CAAC,CAAC;AACnF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}