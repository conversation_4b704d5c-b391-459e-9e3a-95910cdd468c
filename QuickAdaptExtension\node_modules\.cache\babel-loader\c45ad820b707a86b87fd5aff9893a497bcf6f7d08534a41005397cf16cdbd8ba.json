{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnFieldsSelector, gridColumnDefinitionsSelector, gridColumnLookupSelector, gridColumnsStateSelector, gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector } from \"./gridColumnsSelector.js\";\nimport { GridSignature, useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridRegisterPipeProcessor, useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { EMPTY_PINNED_COLUMN_FIELDS } from \"./gridColumnsInterfaces.js\";\nimport { hydrateColumnsWidth, createColumnsState, COLUMNS_DIMENSION_PROPERTIES } from \"./gridColumnsUtils.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const columnsStateInitializer = (state, props, apiRef) => {\n  const columnsState = createColumnsState({\n    apiRef,\n    columnsToUpsert: props.columns,\n    initialState: props.initialState?.columns,\n    columnVisibilityModel: props.columnVisibilityModel ?? props.initialState?.columns?.columnVisibilityModel ?? {},\n    keepOnlyColumnsToUpsert: true\n  });\n  return _extends({}, state, {\n    columns: columnsState,\n    // In pro/premium, this part of the state is defined. We give it an empty but defined value\n    // for the community version.\n    pinnedColumns: state.pinnedColumns ?? EMPTY_PINNED_COLUMN_FIELDS\n  });\n};\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridDimensions (method, event) - can be after\n * TODO: Impossible priority - useGridParamsApi also needs to be after useGridColumns\n */\nexport function useGridColumns(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useGridColumns');\n  const previousColumnsProp = React.useRef(props.columns);\n  apiRef.current.registerControlState({\n    stateId: 'visibleColumns',\n    propModel: props.columnVisibilityModel,\n    propOnChange: props.onColumnVisibilityModelChange,\n    stateSelector: gridColumnVisibilityModelSelector,\n    changeEvent: 'columnVisibilityModelChange'\n  });\n  const setGridColumnsState = React.useCallback(columnsState => {\n    logger.debug('Updating columns state.');\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    apiRef.current.updateRenderContext?.();\n    apiRef.current.forceUpdate();\n  }, [logger, apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const getColumn = React.useCallback(field => gridColumnLookupSelector(apiRef)[field], [apiRef]);\n  const getAllColumns = React.useCallback(() => gridColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getVisibleColumns = React.useCallback(() => gridVisibleColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getColumnIndex = React.useCallback(function (field) {\n    let useVisibleColumns = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    const columns = useVisibleColumns ? gridVisibleColumnDefinitionsSelector(apiRef) : gridColumnDefinitionsSelector(apiRef);\n    return columns.findIndex(col => col.field === field);\n  }, [apiRef]);\n  const getColumnPosition = React.useCallback(field => {\n    const index = getColumnIndex(field);\n    return gridColumnPositionsSelector(apiRef)[index];\n  }, [apiRef, getColumnIndex]);\n  const setColumnVisibilityModel = React.useCallback(model => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    if (currentModel !== model) {\n      apiRef.current.setState(state => _extends({}, state, {\n        columns: createColumnsState({\n          apiRef,\n          columnsToUpsert: [],\n          initialState: undefined,\n          columnVisibilityModel: model,\n          keepOnlyColumnsToUpsert: false\n        })\n      }));\n      apiRef.current.updateRenderContext?.();\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef]);\n  const updateColumns = React.useCallback(columns => {\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: columns,\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, setGridColumnsState]);\n  const setColumnVisibility = React.useCallback((field, isVisible) => {\n    const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n    const isCurrentlyVisible = columnVisibilityModel[field] ?? true;\n    if (isVisible !== isCurrentlyVisible) {\n      const newModel = _extends({}, columnVisibilityModel, {\n        [field]: isVisible\n      });\n      apiRef.current.setColumnVisibilityModel(newModel);\n    }\n  }, [apiRef]);\n  const getColumnIndexRelativeToVisibleColumns = React.useCallback(field => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    return allColumns.findIndex(col => col === field);\n  }, [apiRef]);\n  const setColumnIndex = React.useCallback((field, targetIndexPosition) => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    const oldIndexPosition = getColumnIndexRelativeToVisibleColumns(field);\n    if (oldIndexPosition === targetIndexPosition) {\n      return;\n    }\n    logger.debug(`Moving column ${field} to index ${targetIndexPosition}`);\n    const updatedColumns = [...allColumns];\n    const fieldRemoved = updatedColumns.splice(oldIndexPosition, 1)[0];\n    updatedColumns.splice(targetIndexPosition, 0, fieldRemoved);\n    setGridColumnsState(_extends({}, gridColumnsStateSelector(apiRef.current.state), {\n      orderedFields: updatedColumns\n    }));\n    const params = {\n      column: apiRef.current.getColumn(field),\n      targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(field),\n      oldIndex: oldIndexPosition\n    };\n    apiRef.current.publishEvent('columnIndexChange', params);\n  }, [apiRef, logger, setGridColumnsState, getColumnIndexRelativeToVisibleColumns]);\n  const setColumnWidth = React.useCallback((field, width) => {\n    logger.debug(`Updating column ${field} width to ${width}`);\n    const columnsState = gridColumnsStateSelector(apiRef.current.state);\n    const column = columnsState.lookup[field];\n    const newColumn = _extends({}, column, {\n      width,\n      hasBeenResized: true\n    });\n    setGridColumnsState(hydrateColumnsWidth(_extends({}, columnsState, {\n      lookup: _extends({}, columnsState.lookup, {\n        [field]: newColumn\n      })\n    }), apiRef.current.getRootDimensions()));\n    apiRef.current.publishEvent('columnWidthChange', {\n      element: apiRef.current.getColumnHeaderElement(field),\n      colDef: newColumn,\n      width\n    });\n  }, [apiRef, logger, setGridColumnsState]);\n  const columnApi = {\n    getColumn,\n    getAllColumns,\n    getColumnIndex,\n    getColumnPosition,\n    getVisibleColumns,\n    getColumnIndexRelativeToVisibleColumns,\n    updateColumns,\n    setColumnVisibilityModel,\n    setColumnVisibility,\n    setColumnWidth\n  };\n  const columnReorderApi = {\n    setColumnIndex\n  };\n  useGridApiMethod(apiRef, columnApi, 'public');\n  useGridApiMethod(apiRef, columnReorderApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const columnsStateToExport = {};\n    const columnVisibilityModelToExport = gridColumnVisibilityModelSelector(apiRef);\n    const shouldExportColumnVisibilityModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.columnVisibilityModel != null ||\n    // Always export if the model has been initialized\n    // TODO v6 Do a nullish check instead to export even if the initial model equals \"{}\"\n    Object.keys(props.initialState?.columns?.columnVisibilityModel ?? {}).length > 0 ||\n    // Always export if the model is not empty\n    Object.keys(columnVisibilityModelToExport).length > 0;\n    if (shouldExportColumnVisibilityModel) {\n      columnsStateToExport.columnVisibilityModel = columnVisibilityModelToExport;\n    }\n    columnsStateToExport.orderedFields = gridColumnFieldsSelector(apiRef);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const dimensions = {};\n    columns.forEach(colDef => {\n      if (colDef.hasBeenResized) {\n        const colDefDimensions = {};\n        COLUMNS_DIMENSION_PROPERTIES.forEach(propertyName => {\n          let propertyValue = colDef[propertyName];\n          if (propertyValue === Infinity) {\n            propertyValue = -1;\n          }\n          colDefDimensions[propertyName] = propertyValue;\n        });\n        dimensions[colDef.field] = colDefDimensions;\n      }\n    });\n    if (Object.keys(dimensions).length > 0) {\n      columnsStateToExport.dimensions = dimensions;\n    }\n    return _extends({}, prevState, {\n      columns: columnsStateToExport\n    });\n  }, [apiRef, props.columnVisibilityModel, props.initialState?.columns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const columnVisibilityModelToImport = context.stateToRestore.columns?.columnVisibilityModel;\n    const initialState = context.stateToRestore.columns;\n    if (columnVisibilityModelToImport == null && initialState == null) {\n      return params;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState,\n      columnVisibilityModel: columnVisibilityModelToImport,\n      keepOnlyColumnsToUpsert: false\n    });\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    if (initialState != null) {\n      apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    }\n    return params;\n  }, [apiRef]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.columns) {\n      const ColumnsPanel = props.slots.columnsPanel;\n      return /*#__PURE__*/_jsx(ColumnsPanel, _extends({}, props.slotProps?.columnsPanel));\n    }\n    return initialValue;\n  }, [props.slots.columnsPanel, props.slotProps?.columnsPanel]);\n  const addColumnMenuItems = React.useCallback(columnMenuItems => {\n    if (props.disableColumnSelector) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuColumnsItem'];\n  }, [props.disableColumnSelector]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n\n  /*\n   * EVENTS\n   */\n\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = viewportInnerSize => {\n    if (prevInnerWidth.current !== viewportInnerSize.width) {\n      prevInnerWidth.current = viewportInnerSize.width;\n      setGridColumnsState(hydrateColumnsWidth(gridColumnsStateSelector(apiRef.current.state), apiRef.current.getRootDimensions()));\n    }\n  };\n  useGridApiEventHandler(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n\n  /**\n   * APPLIERS\n   */\n  const hydrateColumns = React.useCallback(() => {\n    logger.info(`Columns pipe processing have changed, regenerating the columns`);\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, logger, setGridColumnsState]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateColumns', hydrateColumns);\n\n  /*\n   * EFFECTS\n   */\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridColumns`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    logger.info(`GridColumns have changed, new length ${props.columns.length}`);\n    if (previousColumnsProp.current === props.columns) {\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      initialState: undefined,\n      // If the user provides a model, we don't want to set it in the state here because it has it's dedicated `useEffect` which calls `setColumnVisibilityModel`\n      columnsToUpsert: props.columns,\n      keepOnlyColumnsToUpsert: true\n    });\n    previousColumnsProp.current = props.columns;\n    setGridColumnsState(columnsState);\n  }, [logger, apiRef, setGridColumnsState, props.columns]);\n  React.useEffect(() => {\n    if (props.columnVisibilityModel !== undefined) {\n      apiRef.current.setColumnVisibilityModel(props.columnVisibilityModel);\n    }\n  }, [apiRef, logger, props.columnVisibilityModel]);\n}\nfunction mergeColumnsState(columnsState) {\n  return state => _extends({}, state, {\n    columns: columnsState\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "useGridLogger", "gridColumnFieldsSelector", "gridColumnDefinitionsSelector", "gridColumnLookupSelector", "gridColumnsStateSelector", "gridColumnVisibilityModelSelector", "gridVisibleColumnDefinitionsSelector", "gridColumnPositionsSelector", "GridSignature", "useGridApiEventHandler", "useGridRegisterPipeProcessor", "useGridRegisterPipeApplier", "EMPTY_PINNED_COLUMN_FIELDS", "hydrateColumnsWidth", "createColumnsState", "COLUMNS_DIMENSION_PROPERTIES", "GridPreferencePanelsValue", "jsx", "_jsx", "columnsStateInitializer", "state", "props", "apiRef", "columnsState", "columnsToUpsert", "columns", "initialState", "columnVisibilityModel", "keepOnlyColumnsToUpsert", "pinnedColumns", "useGridColumns", "logger", "previousColumnsProp", "useRef", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onColumnVisibilityModelChange", "stateSelector", "changeEvent", "setGridColumnsState", "useCallback", "debug", "setState", "mergeColumnsState", "publishEvent", "orderedFields", "updateRenderContext", "forceUpdate", "getColumn", "field", "getAllColumns", "getVisibleColumns", "getColumnIndex", "useVisibleColumns", "arguments", "length", "undefined", "findIndex", "col", "getColumnPosition", "index", "setColumnVisibilityModel", "model", "currentModel", "updateColumns", "setColumnVisibility", "isVisible", "isCurrentlyVisible", "newModel", "getColumnIndexRelativeToVisibleColumns", "allColumns", "setColumnIndex", "targetIndexPosition", "oldIndexPosition", "updatedColumns", "fieldRemoved", "splice", "params", "column", "targetIndex", "oldIndex", "setColumn<PERSON><PERSON><PERSON>", "width", "lookup", "newColumn", "hasBeenResized", "getRootDimensions", "element", "getColumnHeaderElement", "colDef", "columnApi", "columnReorderApi", "signature", "DataGrid", "stateExportPreProcessing", "prevState", "context", "columnsStateToExport", "columnVisibilityModelToExport", "shouldExportColumnVisibilityModel", "exportOnlyDirtyModels", "Object", "keys", "dimensions", "for<PERSON>ach", "colDefDimensions", "propertyName", "propertyValue", "Infinity", "stateRestorePreProcessing", "columnVisibilityModelToImport", "stateToRestore", "preferencePanelPreProcessing", "initialValue", "value", "ColumnsPanel", "slots", "columnsPanel", "slotProps", "addColumnMenuItems", "columnMenuItems", "disableColumnSelector", "prevInnerWidth", "handleGridSizeChange", "viewportInnerSize", "hydrateColumns", "info", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/columns/useGridColumns.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnFieldsSelector, gridColumnDefinitionsSelector, gridColumnLookupSelector, gridColumnsStateSelector, gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector } from \"./gridColumnsSelector.js\";\nimport { GridSignature, useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridRegisterPipeProcessor, useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { EMPTY_PINNED_COLUMN_FIELDS } from \"./gridColumnsInterfaces.js\";\nimport { hydrateColumnsWidth, createColumnsState, COLUMNS_DIMENSION_PROPERTIES } from \"./gridColumnsUtils.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const columnsStateInitializer = (state, props, apiRef) => {\n  const columnsState = createColumnsState({\n    apiRef,\n    columnsToUpsert: props.columns,\n    initialState: props.initialState?.columns,\n    columnVisibilityModel: props.columnVisibilityModel ?? props.initialState?.columns?.columnVisibilityModel ?? {},\n    keepOnlyColumnsToUpsert: true\n  });\n  return _extends({}, state, {\n    columns: columnsState,\n    // In pro/premium, this part of the state is defined. We give it an empty but defined value\n    // for the community version.\n    pinnedColumns: state.pinnedColumns ?? EMPTY_PINNED_COLUMN_FIELDS\n  });\n};\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridDimensions (method, event) - can be after\n * TODO: Impossible priority - useGridParamsApi also needs to be after useGridColumns\n */\nexport function useGridColumns(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useGridColumns');\n  const previousColumnsProp = React.useRef(props.columns);\n  apiRef.current.registerControlState({\n    stateId: 'visibleColumns',\n    propModel: props.columnVisibilityModel,\n    propOnChange: props.onColumnVisibilityModelChange,\n    stateSelector: gridColumnVisibilityModelSelector,\n    changeEvent: 'columnVisibilityModelChange'\n  });\n  const setGridColumnsState = React.useCallback(columnsState => {\n    logger.debug('Updating columns state.');\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    apiRef.current.updateRenderContext?.();\n    apiRef.current.forceUpdate();\n  }, [logger, apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const getColumn = React.useCallback(field => gridColumnLookupSelector(apiRef)[field], [apiRef]);\n  const getAllColumns = React.useCallback(() => gridColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getVisibleColumns = React.useCallback(() => gridVisibleColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getColumnIndex = React.useCallback((field, useVisibleColumns = true) => {\n    const columns = useVisibleColumns ? gridVisibleColumnDefinitionsSelector(apiRef) : gridColumnDefinitionsSelector(apiRef);\n    return columns.findIndex(col => col.field === field);\n  }, [apiRef]);\n  const getColumnPosition = React.useCallback(field => {\n    const index = getColumnIndex(field);\n    return gridColumnPositionsSelector(apiRef)[index];\n  }, [apiRef, getColumnIndex]);\n  const setColumnVisibilityModel = React.useCallback(model => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    if (currentModel !== model) {\n      apiRef.current.setState(state => _extends({}, state, {\n        columns: createColumnsState({\n          apiRef,\n          columnsToUpsert: [],\n          initialState: undefined,\n          columnVisibilityModel: model,\n          keepOnlyColumnsToUpsert: false\n        })\n      }));\n      apiRef.current.updateRenderContext?.();\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef]);\n  const updateColumns = React.useCallback(columns => {\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: columns,\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, setGridColumnsState]);\n  const setColumnVisibility = React.useCallback((field, isVisible) => {\n    const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n    const isCurrentlyVisible = columnVisibilityModel[field] ?? true;\n    if (isVisible !== isCurrentlyVisible) {\n      const newModel = _extends({}, columnVisibilityModel, {\n        [field]: isVisible\n      });\n      apiRef.current.setColumnVisibilityModel(newModel);\n    }\n  }, [apiRef]);\n  const getColumnIndexRelativeToVisibleColumns = React.useCallback(field => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    return allColumns.findIndex(col => col === field);\n  }, [apiRef]);\n  const setColumnIndex = React.useCallback((field, targetIndexPosition) => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    const oldIndexPosition = getColumnIndexRelativeToVisibleColumns(field);\n    if (oldIndexPosition === targetIndexPosition) {\n      return;\n    }\n    logger.debug(`Moving column ${field} to index ${targetIndexPosition}`);\n    const updatedColumns = [...allColumns];\n    const fieldRemoved = updatedColumns.splice(oldIndexPosition, 1)[0];\n    updatedColumns.splice(targetIndexPosition, 0, fieldRemoved);\n    setGridColumnsState(_extends({}, gridColumnsStateSelector(apiRef.current.state), {\n      orderedFields: updatedColumns\n    }));\n    const params = {\n      column: apiRef.current.getColumn(field),\n      targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(field),\n      oldIndex: oldIndexPosition\n    };\n    apiRef.current.publishEvent('columnIndexChange', params);\n  }, [apiRef, logger, setGridColumnsState, getColumnIndexRelativeToVisibleColumns]);\n  const setColumnWidth = React.useCallback((field, width) => {\n    logger.debug(`Updating column ${field} width to ${width}`);\n    const columnsState = gridColumnsStateSelector(apiRef.current.state);\n    const column = columnsState.lookup[field];\n    const newColumn = _extends({}, column, {\n      width,\n      hasBeenResized: true\n    });\n    setGridColumnsState(hydrateColumnsWidth(_extends({}, columnsState, {\n      lookup: _extends({}, columnsState.lookup, {\n        [field]: newColumn\n      })\n    }), apiRef.current.getRootDimensions()));\n    apiRef.current.publishEvent('columnWidthChange', {\n      element: apiRef.current.getColumnHeaderElement(field),\n      colDef: newColumn,\n      width\n    });\n  }, [apiRef, logger, setGridColumnsState]);\n  const columnApi = {\n    getColumn,\n    getAllColumns,\n    getColumnIndex,\n    getColumnPosition,\n    getVisibleColumns,\n    getColumnIndexRelativeToVisibleColumns,\n    updateColumns,\n    setColumnVisibilityModel,\n    setColumnVisibility,\n    setColumnWidth\n  };\n  const columnReorderApi = {\n    setColumnIndex\n  };\n  useGridApiMethod(apiRef, columnApi, 'public');\n  useGridApiMethod(apiRef, columnReorderApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const columnsStateToExport = {};\n    const columnVisibilityModelToExport = gridColumnVisibilityModelSelector(apiRef);\n    const shouldExportColumnVisibilityModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.columnVisibilityModel != null ||\n    // Always export if the model has been initialized\n    // TODO v6 Do a nullish check instead to export even if the initial model equals \"{}\"\n    Object.keys(props.initialState?.columns?.columnVisibilityModel ?? {}).length > 0 ||\n    // Always export if the model is not empty\n    Object.keys(columnVisibilityModelToExport).length > 0;\n    if (shouldExportColumnVisibilityModel) {\n      columnsStateToExport.columnVisibilityModel = columnVisibilityModelToExport;\n    }\n    columnsStateToExport.orderedFields = gridColumnFieldsSelector(apiRef);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const dimensions = {};\n    columns.forEach(colDef => {\n      if (colDef.hasBeenResized) {\n        const colDefDimensions = {};\n        COLUMNS_DIMENSION_PROPERTIES.forEach(propertyName => {\n          let propertyValue = colDef[propertyName];\n          if (propertyValue === Infinity) {\n            propertyValue = -1;\n          }\n          colDefDimensions[propertyName] = propertyValue;\n        });\n        dimensions[colDef.field] = colDefDimensions;\n      }\n    });\n    if (Object.keys(dimensions).length > 0) {\n      columnsStateToExport.dimensions = dimensions;\n    }\n    return _extends({}, prevState, {\n      columns: columnsStateToExport\n    });\n  }, [apiRef, props.columnVisibilityModel, props.initialState?.columns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const columnVisibilityModelToImport = context.stateToRestore.columns?.columnVisibilityModel;\n    const initialState = context.stateToRestore.columns;\n    if (columnVisibilityModelToImport == null && initialState == null) {\n      return params;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState,\n      columnVisibilityModel: columnVisibilityModelToImport,\n      keepOnlyColumnsToUpsert: false\n    });\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    if (initialState != null) {\n      apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    }\n    return params;\n  }, [apiRef]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.columns) {\n      const ColumnsPanel = props.slots.columnsPanel;\n      return /*#__PURE__*/_jsx(ColumnsPanel, _extends({}, props.slotProps?.columnsPanel));\n    }\n    return initialValue;\n  }, [props.slots.columnsPanel, props.slotProps?.columnsPanel]);\n  const addColumnMenuItems = React.useCallback(columnMenuItems => {\n    if (props.disableColumnSelector) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuColumnsItem'];\n  }, [props.disableColumnSelector]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n\n  /*\n   * EVENTS\n   */\n\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = viewportInnerSize => {\n    if (prevInnerWidth.current !== viewportInnerSize.width) {\n      prevInnerWidth.current = viewportInnerSize.width;\n      setGridColumnsState(hydrateColumnsWidth(gridColumnsStateSelector(apiRef.current.state), apiRef.current.getRootDimensions()));\n    }\n  };\n  useGridApiEventHandler(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n\n  /**\n   * APPLIERS\n   */\n  const hydrateColumns = React.useCallback(() => {\n    logger.info(`Columns pipe processing have changed, regenerating the columns`);\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, logger, setGridColumnsState]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateColumns', hydrateColumns);\n\n  /*\n   * EFFECTS\n   */\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridColumns`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    logger.info(`GridColumns have changed, new length ${props.columns.length}`);\n    if (previousColumnsProp.current === props.columns) {\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      initialState: undefined,\n      // If the user provides a model, we don't want to set it in the state here because it has it's dedicated `useEffect` which calls `setColumnVisibilityModel`\n      columnsToUpsert: props.columns,\n      keepOnlyColumnsToUpsert: true\n    });\n    previousColumnsProp.current = props.columns;\n    setGridColumnsState(columnsState);\n  }, [logger, apiRef, setGridColumnsState, props.columns]);\n  React.useEffect(() => {\n    if (props.columnVisibilityModel !== undefined) {\n      apiRef.current.setColumnVisibilityModel(props.columnVisibilityModel);\n    }\n  }, [apiRef, logger, props.columnVisibilityModel]);\n}\nfunction mergeColumnsState(columnsState) {\n  return state => _extends({}, state, {\n    columns: columnsState\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,wBAAwB,EAAEC,6BAA6B,EAAEC,wBAAwB,EAAEC,wBAAwB,EAAEC,iCAAiC,EAAEC,oCAAoC,EAAEC,2BAA2B,QAAQ,0BAA0B;AAC5P,SAASC,aAAa,EAAEC,sBAAsB,QAAQ,uCAAuC;AAC7F,SAASC,4BAA4B,EAAEC,0BAA0B,QAAQ,oCAAoC;AAC7G,SAASC,0BAA0B,QAAQ,4BAA4B;AACvE,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,4BAA4B,QAAQ,uBAAuB;AAC7G,SAASC,yBAAyB,QAAQ,8BAA8B;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC/D,MAAMC,YAAY,GAAGT,kBAAkB,CAAC;IACtCQ,MAAM;IACNE,eAAe,EAAEH,KAAK,CAACI,OAAO;IAC9BC,YAAY,EAAEL,KAAK,CAACK,YAAY,EAAED,OAAO;IACzCE,qBAAqB,EAAEN,KAAK,CAACM,qBAAqB,IAAIN,KAAK,CAACK,YAAY,EAAED,OAAO,EAAEE,qBAAqB,IAAI,CAAC,CAAC;IAC9GC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;EACF,OAAO/B,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IACzBK,OAAO,EAAEF,YAAY;IACrB;IACA;IACAM,aAAa,EAAET,KAAK,CAACS,aAAa,IAAIjB;EACxC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASkB,cAAcA,CAACR,MAAM,EAAED,KAAK,EAAE;EAC5C,MAAMU,MAAM,GAAG/B,aAAa,CAACsB,MAAM,EAAE,gBAAgB,CAAC;EACtD,MAAMU,mBAAmB,GAAGlC,KAAK,CAACmC,MAAM,CAACZ,KAAK,CAACI,OAAO,CAAC;EACvDH,MAAM,CAACY,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAEhB,KAAK,CAACM,qBAAqB;IACtCW,YAAY,EAAEjB,KAAK,CAACkB,6BAA6B;IACjDC,aAAa,EAAEnC,iCAAiC;IAChDoC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,mBAAmB,GAAG5C,KAAK,CAAC6C,WAAW,CAACpB,YAAY,IAAI;IAC5DQ,MAAM,CAACa,KAAK,CAAC,yBAAyB,CAAC;IACvCtB,MAAM,CAACY,OAAO,CAACW,QAAQ,CAACC,iBAAiB,CAACvB,YAAY,CAAC,CAAC;IACxDD,MAAM,CAACY,OAAO,CAACa,YAAY,CAAC,eAAe,EAAExB,YAAY,CAACyB,aAAa,CAAC;IACxE1B,MAAM,CAACY,OAAO,CAACe,mBAAmB,GAAG,CAAC;IACtC3B,MAAM,CAACY,OAAO,CAACgB,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACnB,MAAM,EAAET,MAAM,CAAC,CAAC;;EAEpB;AACF;AACA;EACE,MAAM6B,SAAS,GAAGrD,KAAK,CAAC6C,WAAW,CAACS,KAAK,IAAIjD,wBAAwB,CAACmB,MAAM,CAAC,CAAC8B,KAAK,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EAC/F,MAAM+B,aAAa,GAAGvD,KAAK,CAAC6C,WAAW,CAAC,MAAMzC,6BAA6B,CAACoB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC9F,MAAMgC,iBAAiB,GAAGxD,KAAK,CAAC6C,WAAW,CAAC,MAAMrC,oCAAoC,CAACgB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACzG,MAAMiC,cAAc,GAAGzD,KAAK,CAAC6C,WAAW,CAAC,UAACS,KAAK,EAA+B;IAAA,IAA7BI,iBAAiB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACvE,MAAMhC,OAAO,GAAG+B,iBAAiB,GAAGlD,oCAAoC,CAACgB,MAAM,CAAC,GAAGpB,6BAA6B,CAACoB,MAAM,CAAC;IACxH,OAAOG,OAAO,CAACmC,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACT,KAAK,KAAKA,KAAK,CAAC;EACtD,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMwC,iBAAiB,GAAGhE,KAAK,CAAC6C,WAAW,CAACS,KAAK,IAAI;IACnD,MAAMW,KAAK,GAAGR,cAAc,CAACH,KAAK,CAAC;IACnC,OAAO7C,2BAA2B,CAACe,MAAM,CAAC,CAACyC,KAAK,CAAC;EACnD,CAAC,EAAE,CAACzC,MAAM,EAAEiC,cAAc,CAAC,CAAC;EAC5B,MAAMS,wBAAwB,GAAGlE,KAAK,CAAC6C,WAAW,CAACsB,KAAK,IAAI;IAC1D,MAAMC,YAAY,GAAG7D,iCAAiC,CAACiB,MAAM,CAAC;IAC9D,IAAI4C,YAAY,KAAKD,KAAK,EAAE;MAC1B3C,MAAM,CAACY,OAAO,CAACW,QAAQ,CAACzB,KAAK,IAAIvB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;QACnDK,OAAO,EAAEX,kBAAkB,CAAC;UAC1BQ,MAAM;UACNE,eAAe,EAAE,EAAE;UACnBE,YAAY,EAAEiC,SAAS;UACvBhC,qBAAqB,EAAEsC,KAAK;UAC5BrC,uBAAuB,EAAE;QAC3B,CAAC;MACH,CAAC,CAAC,CAAC;MACHN,MAAM,CAACY,OAAO,CAACe,mBAAmB,GAAG,CAAC;MACtC3B,MAAM,CAACY,OAAO,CAACgB,WAAW,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC5B,MAAM,CAAC,CAAC;EACZ,MAAM6C,aAAa,GAAGrE,KAAK,CAAC6C,WAAW,CAAClB,OAAO,IAAI;IACjD,MAAMF,YAAY,GAAGT,kBAAkB,CAAC;MACtCQ,MAAM;MACNE,eAAe,EAAEC,OAAO;MACxBC,YAAY,EAAEiC,SAAS;MACvB/B,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFc,mBAAmB,CAACnB,YAAY,CAAC;EACnC,CAAC,EAAE,CAACD,MAAM,EAAEoB,mBAAmB,CAAC,CAAC;EACjC,MAAM0B,mBAAmB,GAAGtE,KAAK,CAAC6C,WAAW,CAAC,CAACS,KAAK,EAAEiB,SAAS,KAAK;IAClE,MAAM1C,qBAAqB,GAAGtB,iCAAiC,CAACiB,MAAM,CAAC;IACvE,MAAMgD,kBAAkB,GAAG3C,qBAAqB,CAACyB,KAAK,CAAC,IAAI,IAAI;IAC/D,IAAIiB,SAAS,KAAKC,kBAAkB,EAAE;MACpC,MAAMC,QAAQ,GAAG1E,QAAQ,CAAC,CAAC,CAAC,EAAE8B,qBAAqB,EAAE;QACnD,CAACyB,KAAK,GAAGiB;MACX,CAAC,CAAC;MACF/C,MAAM,CAACY,OAAO,CAAC8B,wBAAwB,CAACO,QAAQ,CAAC;IACnD;EACF,CAAC,EAAE,CAACjD,MAAM,CAAC,CAAC;EACZ,MAAMkD,sCAAsC,GAAG1E,KAAK,CAAC6C,WAAW,CAACS,KAAK,IAAI;IACxE,MAAMqB,UAAU,GAAGxE,wBAAwB,CAACqB,MAAM,CAAC;IACnD,OAAOmD,UAAU,CAACb,SAAS,CAACC,GAAG,IAAIA,GAAG,KAAKT,KAAK,CAAC;EACnD,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMoD,cAAc,GAAG5E,KAAK,CAAC6C,WAAW,CAAC,CAACS,KAAK,EAAEuB,mBAAmB,KAAK;IACvE,MAAMF,UAAU,GAAGxE,wBAAwB,CAACqB,MAAM,CAAC;IACnD,MAAMsD,gBAAgB,GAAGJ,sCAAsC,CAACpB,KAAK,CAAC;IACtE,IAAIwB,gBAAgB,KAAKD,mBAAmB,EAAE;MAC5C;IACF;IACA5C,MAAM,CAACa,KAAK,CAAC,iBAAiBQ,KAAK,aAAauB,mBAAmB,EAAE,CAAC;IACtE,MAAME,cAAc,GAAG,CAAC,GAAGJ,UAAU,CAAC;IACtC,MAAMK,YAAY,GAAGD,cAAc,CAACE,MAAM,CAACH,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClEC,cAAc,CAACE,MAAM,CAACJ,mBAAmB,EAAE,CAAC,EAAEG,YAAY,CAAC;IAC3DpC,mBAAmB,CAAC7C,QAAQ,CAAC,CAAC,CAAC,EAAEO,wBAAwB,CAACkB,MAAM,CAACY,OAAO,CAACd,KAAK,CAAC,EAAE;MAC/E4B,aAAa,EAAE6B;IACjB,CAAC,CAAC,CAAC;IACH,MAAMG,MAAM,GAAG;MACbC,MAAM,EAAE3D,MAAM,CAACY,OAAO,CAACiB,SAAS,CAACC,KAAK,CAAC;MACvC8B,WAAW,EAAE5D,MAAM,CAACY,OAAO,CAACsC,sCAAsC,CAACpB,KAAK,CAAC;MACzE+B,QAAQ,EAAEP;IACZ,CAAC;IACDtD,MAAM,CAACY,OAAO,CAACa,YAAY,CAAC,mBAAmB,EAAEiC,MAAM,CAAC;EAC1D,CAAC,EAAE,CAAC1D,MAAM,EAAES,MAAM,EAAEW,mBAAmB,EAAE8B,sCAAsC,CAAC,CAAC;EACjF,MAAMY,cAAc,GAAGtF,KAAK,CAAC6C,WAAW,CAAC,CAACS,KAAK,EAAEiC,KAAK,KAAK;IACzDtD,MAAM,CAACa,KAAK,CAAC,mBAAmBQ,KAAK,aAAaiC,KAAK,EAAE,CAAC;IAC1D,MAAM9D,YAAY,GAAGnB,wBAAwB,CAACkB,MAAM,CAACY,OAAO,CAACd,KAAK,CAAC;IACnE,MAAM6D,MAAM,GAAG1D,YAAY,CAAC+D,MAAM,CAAClC,KAAK,CAAC;IACzC,MAAMmC,SAAS,GAAG1F,QAAQ,CAAC,CAAC,CAAC,EAAEoF,MAAM,EAAE;MACrCI,KAAK;MACLG,cAAc,EAAE;IAClB,CAAC,CAAC;IACF9C,mBAAmB,CAAC7B,mBAAmB,CAAChB,QAAQ,CAAC,CAAC,CAAC,EAAE0B,YAAY,EAAE;MACjE+D,MAAM,EAAEzF,QAAQ,CAAC,CAAC,CAAC,EAAE0B,YAAY,CAAC+D,MAAM,EAAE;QACxC,CAAClC,KAAK,GAAGmC;MACX,CAAC;IACH,CAAC,CAAC,EAAEjE,MAAM,CAACY,OAAO,CAACuD,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACxCnE,MAAM,CAACY,OAAO,CAACa,YAAY,CAAC,mBAAmB,EAAE;MAC/C2C,OAAO,EAAEpE,MAAM,CAACY,OAAO,CAACyD,sBAAsB,CAACvC,KAAK,CAAC;MACrDwC,MAAM,EAAEL,SAAS;MACjBF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/D,MAAM,EAAES,MAAM,EAAEW,mBAAmB,CAAC,CAAC;EACzC,MAAMmD,SAAS,GAAG;IAChB1C,SAAS;IACTE,aAAa;IACbE,cAAc;IACdO,iBAAiB;IACjBR,iBAAiB;IACjBkB,sCAAsC;IACtCL,aAAa;IACbH,wBAAwB;IACxBI,mBAAmB;IACnBgB;EACF,CAAC;EACD,MAAMU,gBAAgB,GAAG;IACvBpB;EACF,CAAC;EACD3E,gBAAgB,CAACuB,MAAM,EAAEuE,SAAS,EAAE,QAAQ,CAAC;EAC7C9F,gBAAgB,CAACuB,MAAM,EAAEwE,gBAAgB,EAAEzE,KAAK,CAAC0E,SAAS,KAAKvF,aAAa,CAACwF,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;;EAE7G;AACF;AACA;EACE,MAAMC,wBAAwB,GAAGnG,KAAK,CAAC6C,WAAW,CAAC,CAACuD,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMC,oBAAoB,GAAG,CAAC,CAAC;IAC/B,MAAMC,6BAA6B,GAAGhG,iCAAiC,CAACiB,MAAM,CAAC;IAC/E,MAAMgF,iCAAiC;IACvC;IACA,CAACH,OAAO,CAACI,qBAAqB;IAC9B;IACAlF,KAAK,CAACM,qBAAqB,IAAI,IAAI;IACnC;IACA;IACA6E,MAAM,CAACC,IAAI,CAACpF,KAAK,CAACK,YAAY,EAAED,OAAO,EAAEE,qBAAqB,IAAI,CAAC,CAAC,CAAC,CAAC+B,MAAM,GAAG,CAAC;IAChF;IACA8C,MAAM,CAACC,IAAI,CAACJ,6BAA6B,CAAC,CAAC3C,MAAM,GAAG,CAAC;IACrD,IAAI4C,iCAAiC,EAAE;MACrCF,oBAAoB,CAACzE,qBAAqB,GAAG0E,6BAA6B;IAC5E;IACAD,oBAAoB,CAACpD,aAAa,GAAG/C,wBAAwB,CAACqB,MAAM,CAAC;IACrE,MAAMG,OAAO,GAAGvB,6BAA6B,CAACoB,MAAM,CAAC;IACrD,MAAMoF,UAAU,GAAG,CAAC,CAAC;IACrBjF,OAAO,CAACkF,OAAO,CAACf,MAAM,IAAI;MACxB,IAAIA,MAAM,CAACJ,cAAc,EAAE;QACzB,MAAMoB,gBAAgB,GAAG,CAAC,CAAC;QAC3B7F,4BAA4B,CAAC4F,OAAO,CAACE,YAAY,IAAI;UACnD,IAAIC,aAAa,GAAGlB,MAAM,CAACiB,YAAY,CAAC;UACxC,IAAIC,aAAa,KAAKC,QAAQ,EAAE;YAC9BD,aAAa,GAAG,CAAC,CAAC;UACpB;UACAF,gBAAgB,CAACC,YAAY,CAAC,GAAGC,aAAa;QAChD,CAAC,CAAC;QACFJ,UAAU,CAACd,MAAM,CAACxC,KAAK,CAAC,GAAGwD,gBAAgB;MAC7C;IACF,CAAC,CAAC;IACF,IAAIJ,MAAM,CAACC,IAAI,CAACC,UAAU,CAAC,CAAChD,MAAM,GAAG,CAAC,EAAE;MACtC0C,oBAAoB,CAACM,UAAU,GAAGA,UAAU;IAC9C;IACA,OAAO7G,QAAQ,CAAC,CAAC,CAAC,EAAEqG,SAAS,EAAE;MAC7BzE,OAAO,EAAE2E;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9E,MAAM,EAAED,KAAK,CAACM,qBAAqB,EAAEN,KAAK,CAACK,YAAY,EAAED,OAAO,CAAC,CAAC;EACtE,MAAMuF,yBAAyB,GAAGlH,KAAK,CAAC6C,WAAW,CAAC,CAACqC,MAAM,EAAEmB,OAAO,KAAK;IACvE,MAAMc,6BAA6B,GAAGd,OAAO,CAACe,cAAc,CAACzF,OAAO,EAAEE,qBAAqB;IAC3F,MAAMD,YAAY,GAAGyE,OAAO,CAACe,cAAc,CAACzF,OAAO;IACnD,IAAIwF,6BAA6B,IAAI,IAAI,IAAIvF,YAAY,IAAI,IAAI,EAAE;MACjE,OAAOsD,MAAM;IACf;IACA,MAAMzD,YAAY,GAAGT,kBAAkB,CAAC;MACtCQ,MAAM;MACNE,eAAe,EAAE,EAAE;MACnBE,YAAY;MACZC,qBAAqB,EAAEsF,6BAA6B;MACpDrF,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFN,MAAM,CAACY,OAAO,CAACW,QAAQ,CAACC,iBAAiB,CAACvB,YAAY,CAAC,CAAC;IACxD,IAAIG,YAAY,IAAI,IAAI,EAAE;MACxBJ,MAAM,CAACY,OAAO,CAACa,YAAY,CAAC,eAAe,EAAExB,YAAY,CAACyB,aAAa,CAAC;IAC1E;IACA,OAAOgC,MAAM;EACf,CAAC,EAAE,CAAC1D,MAAM,CAAC,CAAC;EACZ,MAAM6F,4BAA4B,GAAGrH,KAAK,CAAC6C,WAAW,CAAC,CAACyE,YAAY,EAAEC,KAAK,KAAK;IAC9E,IAAIA,KAAK,KAAKrG,yBAAyB,CAACS,OAAO,EAAE;MAC/C,MAAM6F,YAAY,GAAGjG,KAAK,CAACkG,KAAK,CAACC,YAAY;MAC7C,OAAO,aAAatG,IAAI,CAACoG,YAAY,EAAEzH,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,CAACoG,SAAS,EAAED,YAAY,CAAC,CAAC;IACrF;IACA,OAAOJ,YAAY;EACrB,CAAC,EAAE,CAAC/F,KAAK,CAACkG,KAAK,CAACC,YAAY,EAAEnG,KAAK,CAACoG,SAAS,EAAED,YAAY,CAAC,CAAC;EAC7D,MAAME,kBAAkB,GAAG5H,KAAK,CAAC6C,WAAW,CAACgF,eAAe,IAAI;IAC9D,IAAItG,KAAK,CAACuG,qBAAqB,EAAE;MAC/B,OAAOD,eAAe;IACxB;IACA,OAAO,CAAC,GAAGA,eAAe,EAAE,uBAAuB,CAAC;EACtD,CAAC,EAAE,CAACtG,KAAK,CAACuG,qBAAqB,CAAC,CAAC;EACjClH,4BAA4B,CAACY,MAAM,EAAE,YAAY,EAAEoG,kBAAkB,CAAC;EACtEhH,4BAA4B,CAACY,MAAM,EAAE,aAAa,EAAE2E,wBAAwB,CAAC;EAC7EvF,4BAA4B,CAACY,MAAM,EAAE,cAAc,EAAE0F,yBAAyB,CAAC;EAC/EtG,4BAA4B,CAACY,MAAM,EAAE,iBAAiB,EAAE6F,4BAA4B,CAAC;;EAErF;AACF;AACA;;EAEE,MAAMU,cAAc,GAAG/H,KAAK,CAACmC,MAAM,CAAC,IAAI,CAAC;EACzC,MAAM6F,oBAAoB,GAAGC,iBAAiB,IAAI;IAChD,IAAIF,cAAc,CAAC3F,OAAO,KAAK6F,iBAAiB,CAAC1C,KAAK,EAAE;MACtDwC,cAAc,CAAC3F,OAAO,GAAG6F,iBAAiB,CAAC1C,KAAK;MAChD3C,mBAAmB,CAAC7B,mBAAmB,CAACT,wBAAwB,CAACkB,MAAM,CAACY,OAAO,CAACd,KAAK,CAAC,EAAEE,MAAM,CAACY,OAAO,CAACuD,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC9H;EACF,CAAC;EACDhF,sBAAsB,CAACa,MAAM,EAAE,yBAAyB,EAAEwG,oBAAoB,CAAC;;EAE/E;AACF;AACA;EACE,MAAME,cAAc,GAAGlI,KAAK,CAAC6C,WAAW,CAAC,MAAM;IAC7CZ,MAAM,CAACkG,IAAI,CAAC,gEAAgE,CAAC;IAC7E,MAAM1G,YAAY,GAAGT,kBAAkB,CAAC;MACtCQ,MAAM;MACNE,eAAe,EAAE,EAAE;MACnBE,YAAY,EAAEiC,SAAS;MACvB/B,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFc,mBAAmB,CAACnB,YAAY,CAAC;EACnC,CAAC,EAAE,CAACD,MAAM,EAAES,MAAM,EAAEW,mBAAmB,CAAC,CAAC;EACzC/B,0BAA0B,CAACW,MAAM,EAAE,gBAAgB,EAAE0G,cAAc,CAAC;;EAEpE;AACF;AACA;EACE;EACA;EACA,MAAME,aAAa,GAAGpI,KAAK,CAACmC,MAAM,CAAC,IAAI,CAAC;EACxCnC,KAAK,CAACqI,SAAS,CAAC,MAAM;IACpB,IAAID,aAAa,CAAChG,OAAO,EAAE;MACzBgG,aAAa,CAAChG,OAAO,GAAG,KAAK;MAC7B;IACF;IACAH,MAAM,CAACkG,IAAI,CAAC,wCAAwC5G,KAAK,CAACI,OAAO,CAACiC,MAAM,EAAE,CAAC;IAC3E,IAAI1B,mBAAmB,CAACE,OAAO,KAAKb,KAAK,CAACI,OAAO,EAAE;MACjD;IACF;IACA,MAAMF,YAAY,GAAGT,kBAAkB,CAAC;MACtCQ,MAAM;MACNI,YAAY,EAAEiC,SAAS;MACvB;MACAnC,eAAe,EAAEH,KAAK,CAACI,OAAO;MAC9BG,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFI,mBAAmB,CAACE,OAAO,GAAGb,KAAK,CAACI,OAAO;IAC3CiB,mBAAmB,CAACnB,YAAY,CAAC;EACnC,CAAC,EAAE,CAACQ,MAAM,EAAET,MAAM,EAAEoB,mBAAmB,EAAErB,KAAK,CAACI,OAAO,CAAC,CAAC;EACxD3B,KAAK,CAACqI,SAAS,CAAC,MAAM;IACpB,IAAI9G,KAAK,CAACM,qBAAqB,KAAKgC,SAAS,EAAE;MAC7CrC,MAAM,CAACY,OAAO,CAAC8B,wBAAwB,CAAC3C,KAAK,CAACM,qBAAqB,CAAC;IACtE;EACF,CAAC,EAAE,CAACL,MAAM,EAAES,MAAM,EAAEV,KAAK,CAACM,qBAAqB,CAAC,CAAC;AACnD;AACA,SAASmB,iBAAiBA,CAACvB,YAAY,EAAE;EACvC,OAAOH,KAAK,IAAIvB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IAClCK,OAAO,EAAEF;EACX,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}