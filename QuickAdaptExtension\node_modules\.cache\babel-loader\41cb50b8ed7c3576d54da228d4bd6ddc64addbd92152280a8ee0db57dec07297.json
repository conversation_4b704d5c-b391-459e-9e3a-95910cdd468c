{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_debounce as debounce } from '@mui/utils';\nimport { useGridVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridDensityFactorSelector } from \"../density/densitySelector.js\";\nimport { gridFilterModelSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridPaginationSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridSortModelSelector } from \"../sorting/gridSortingSelector.js\";\nimport { useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { gridPinnedRowsSelector } from \"./gridRowsSelector.js\";\nimport { DATA_GRID_PROPS_DEFAULT_VALUES } from \"../../../DataGrid/useDataGridProps.js\";\n\n// TODO: I think the row heights can now be encoded as a single `size` instead of `sizes.baseXxxx`\n\nexport const rowsMetaStateInitializer = state => _extends({}, state, {\n  rowsMeta: {\n    currentPageTotalHeight: 0,\n    positions: []\n  }\n});\nlet warnedOnceInvalidRowHeight = false;\nconst getValidRowHeight = (rowHeightProp, defaultRowHeight, warningMessage) => {\n  if (typeof rowHeightProp === 'number' && rowHeightProp > 0) {\n    return rowHeightProp;\n  }\n  if (process.env.NODE_ENV !== 'production' && !warnedOnceInvalidRowHeight && typeof rowHeightProp !== 'undefined' && rowHeightProp !== null) {\n    console.warn(warningMessage);\n    warnedOnceInvalidRowHeight = true;\n  }\n  return defaultRowHeight;\n};\nconst rowHeightWarning = [`MUI X: The \\`rowHeight\\` prop should be a number greater than 0.`, `The default value will be used instead.`].join('\\n');\nconst getRowHeightWarning = [`MUI X: The \\`getRowHeight\\` prop should return a number greater than 0 or 'auto'.`, `The default value will be used instead.`].join('\\n');\n\n/**\n * @requires useGridPageSize (method)\n * @requires useGridPage (method)\n */\nexport const useGridRowsMeta = (apiRef, props) => {\n  const {\n    getRowHeight: getRowHeightProp,\n    getRowSpacing,\n    getEstimatedRowHeight\n  } = props;\n  const rowsHeightLookup = React.useRef(Object.create(null));\n\n  // Inspired by https://github.com/bvaughn/react-virtualized/blob/master/source/Grid/utils/CellSizeAndPositionManager.js\n  const lastMeasuredRowIndex = React.useRef(-1);\n  const hasRowWithAutoHeight = React.useRef(false);\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const filterModel = useGridSelector(apiRef, gridFilterModelSelector);\n  const paginationState = useGridSelector(apiRef, gridPaginationSelector);\n  const sortModel = useGridSelector(apiRef, gridSortModelSelector);\n  const currentPage = useGridVisibleRows(apiRef, props);\n  const pinnedRows = useGridSelector(apiRef, gridPinnedRowsSelector);\n  const validRowHeight = getValidRowHeight(props.rowHeight, DATA_GRID_PROPS_DEFAULT_VALUES.rowHeight, rowHeightWarning);\n  const rowHeight = Math.floor(validRowHeight * densityFactor);\n  const hydrateRowsMeta = React.useCallback(() => {\n    hasRowWithAutoHeight.current = false;\n    const calculateRowProcessedSizes = row => {\n      if (!rowsHeightLookup.current[row.id]) {\n        rowsHeightLookup.current[row.id] = {\n          sizes: {\n            baseCenter: rowHeight\n          },\n          isResized: false,\n          autoHeight: false,\n          needsFirstMeasurement: true // Assume all rows will need to be measured by default\n        };\n      }\n      const {\n        isResized,\n        needsFirstMeasurement,\n        sizes\n      } = rowsHeightLookup.current[row.id];\n      let baseRowHeight = typeof rowHeight === 'number' && rowHeight > 0 ? rowHeight : 52;\n      const existingBaseRowHeight = sizes.baseCenter;\n      if (isResized) {\n        // Do not recalculate resized row height and use the value from the lookup\n        baseRowHeight = existingBaseRowHeight;\n      } else if (getRowHeightProp) {\n        const rowHeightFromUser = getRowHeightProp(_extends({}, row, {\n          densityFactor\n        }));\n        if (rowHeightFromUser === 'auto') {\n          if (needsFirstMeasurement) {\n            const estimatedRowHeight = getEstimatedRowHeight ? getEstimatedRowHeight(_extends({}, row, {\n              densityFactor\n            })) : rowHeight;\n\n            // If the row was not measured yet use the estimated row height\n            baseRowHeight = estimatedRowHeight ?? rowHeight;\n          } else {\n            baseRowHeight = existingBaseRowHeight;\n          }\n          hasRowWithAutoHeight.current = true;\n          rowsHeightLookup.current[row.id].autoHeight = true;\n        } else {\n          // Default back to base rowHeight if getRowHeight returns invalid value.\n          baseRowHeight = getValidRowHeight(rowHeightFromUser, rowHeight, getRowHeightWarning);\n          rowsHeightLookup.current[row.id].needsFirstMeasurement = false;\n          rowsHeightLookup.current[row.id].autoHeight = false;\n        }\n      } else {\n        rowsHeightLookup.current[row.id].needsFirstMeasurement = false;\n      }\n      const initialHeights = {\n        baseCenter: baseRowHeight\n      };\n      if (getRowSpacing) {\n        const indexRelativeToCurrentPage = apiRef.current.getRowIndexRelativeToVisibleRows(row.id);\n        const spacing = getRowSpacing(_extends({}, row, {\n          isFirstVisible: indexRelativeToCurrentPage === 0,\n          isLastVisible: indexRelativeToCurrentPage === currentPage.rows.length - 1,\n          indexRelativeToCurrentPage\n        }));\n        initialHeights.spacingTop = spacing.top ?? 0;\n        initialHeights.spacingBottom = spacing.bottom ?? 0;\n      }\n      const processedSizes = apiRef.current.unstable_applyPipeProcessors('rowHeight', initialHeights, row);\n      rowsHeightLookup.current[row.id].sizes = processedSizes;\n      return processedSizes;\n    };\n    const positions = [];\n    const currentPageTotalHeight = currentPage.rows.reduce((acc, row) => {\n      positions.push(acc);\n      let otherSizes = 0;\n      const processedSizes = calculateRowProcessedSizes(row);\n      /* eslint-disable-next-line guard-for-in */\n      for (const key in processedSizes) {\n        const value = processedSizes[key];\n        if (key !== 'baseCenter') {\n          otherSizes += value;\n        }\n      }\n      return acc + processedSizes.baseCenter + otherSizes;\n    }, 0);\n    pinnedRows?.top?.forEach(row => {\n      calculateRowProcessedSizes(row);\n    });\n    pinnedRows?.bottom?.forEach(row => {\n      calculateRowProcessedSizes(row);\n    });\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rowsMeta: {\n          currentPageTotalHeight,\n          positions\n        }\n      });\n    });\n    if (!hasRowWithAutoHeight.current) {\n      // No row has height=auto, so all rows are already measured\n      lastMeasuredRowIndex.current = Infinity;\n    }\n    apiRef.current.forceUpdate();\n  }, [apiRef, currentPage.rows, rowHeight, getRowHeightProp, getRowSpacing, getEstimatedRowHeight, pinnedRows, densityFactor]);\n  const getRowHeight = React.useCallback(rowId => {\n    const height = rowsHeightLookup.current[rowId];\n    return height ? height.sizes.baseCenter : rowHeight;\n  }, [rowHeight]);\n  const getRowInternalSizes = rowId => rowsHeightLookup.current[rowId]?.sizes;\n  const setRowHeight = React.useCallback((id, height) => {\n    rowsHeightLookup.current[id].sizes.baseCenter = height;\n    rowsHeightLookup.current[id].isResized = true;\n    rowsHeightLookup.current[id].needsFirstMeasurement = false;\n    hydrateRowsMeta();\n  }, [hydrateRowsMeta]);\n  const debouncedHydrateRowsMeta = React.useMemo(() => debounce(hydrateRowsMeta, props.rowPositionsDebounceMs), [hydrateRowsMeta, props.rowPositionsDebounceMs]);\n  const storeMeasuredRowHeight = React.useCallback((id, height) => {\n    if (!rowsHeightLookup.current[id] || !rowsHeightLookup.current[id].autoHeight) {\n      return;\n    }\n\n    // Only trigger hydration if the value is different, otherwise we trigger a loop\n    const needsHydration = rowsHeightLookup.current[id].sizes.baseCenter !== height;\n    rowsHeightLookup.current[id].needsFirstMeasurement = false;\n    rowsHeightLookup.current[id].sizes.baseCenter = height;\n    if (needsHydration) {\n      debouncedHydrateRowsMeta();\n    }\n  }, [debouncedHydrateRowsMeta]);\n  const rowHasAutoHeight = React.useCallback(id => {\n    return rowsHeightLookup.current[id]?.autoHeight || false;\n  }, []);\n  const getLastMeasuredRowIndex = React.useCallback(() => {\n    return lastMeasuredRowIndex.current;\n  }, []);\n  const setLastMeasuredRowIndex = React.useCallback(index => {\n    if (hasRowWithAutoHeight.current && index > lastMeasuredRowIndex.current) {\n      lastMeasuredRowIndex.current = index;\n    }\n  }, []);\n  const resetRowHeights = React.useCallback(() => {\n    rowsHeightLookup.current = {};\n    hydrateRowsMeta();\n  }, [hydrateRowsMeta]);\n\n  // The effect is used to build the rows meta data - currentPageTotalHeight and positions.\n  // Because of variable row height this is needed for the virtualization\n  React.useEffect(() => {\n    hydrateRowsMeta();\n  }, [rowHeight, filterModel, paginationState, sortModel, hydrateRowsMeta]);\n  useGridRegisterPipeApplier(apiRef, 'rowHeight', hydrateRowsMeta);\n  const rowsMetaApi = {\n    unstable_setLastMeasuredRowIndex: setLastMeasuredRowIndex,\n    unstable_getRowHeight: getRowHeight,\n    unstable_getRowInternalSizes: getRowInternalSizes,\n    unstable_setRowHeight: setRowHeight,\n    unstable_storeRowHeightMeasurement: storeMeasuredRowHeight,\n    resetRowHeights\n  };\n  const rowsMetaPrivateApi = {\n    getLastMeasuredRowIndex,\n    rowHasAutoHeight\n  };\n  useGridApiMethod(apiRef, rowsMetaApi, 'public');\n  useGridApiMethod(apiRef, rowsMetaPrivateApi, 'private');\n};", "map": {"version": 3, "names": ["_extends", "React", "unstable_debounce", "debounce", "useGridVisibleRows", "useGridApiMethod", "useGridSelector", "gridDensityFactorSelector", "gridFilterModelSelector", "gridPaginationSelector", "gridSortModelSelector", "useGridRegisterPipeApplier", "gridPinnedRowsSelector", "DATA_GRID_PROPS_DEFAULT_VALUES", "rowsMetaStateInitializer", "state", "rowsMeta", "currentPageTotalHeight", "positions", "warnedOnceInvalidRowHeight", "getValidRowHeight", "rowHeightProp", "defaultRowHeight", "warningMessage", "process", "env", "NODE_ENV", "console", "warn", "rowHeightWarning", "join", "getRowHeightWarning", "useGridRowsMeta", "apiRef", "props", "getRowHeight", "getRowHeightProp", "getRowSpacing", "getEstimatedRowHeight", "rowsHeightLookup", "useRef", "Object", "create", "lastMeasuredRowIndex", "hasRowWithAutoHeight", "densityFactor", "filterModel", "paginationState", "sortModel", "currentPage", "pinnedRows", "validRowHeight", "rowHeight", "Math", "floor", "hydrateRowsMeta", "useCallback", "current", "calculateRowProcessedSizes", "row", "id", "sizes", "baseCenter", "isResized", "autoHeight", "needsFirstMeasurement", "baseRowHeight", "existingBaseRowHeight", "rowHeightFromUser", "estimatedRowHeight", "initialHeights", "indexRelativeToCurrentPage", "getRowIndexRelativeToVisibleRows", "spacing", "isFirstVisible", "isLastVisible", "rows", "length", "spacingTop", "top", "spacingBottom", "bottom", "processedSizes", "unstable_applyPipeProcessors", "reduce", "acc", "push", "otherSizes", "key", "value", "for<PERSON>ach", "setState", "Infinity", "forceUpdate", "rowId", "height", "getRowInternalSizes", "setRowHeight", "debouncedHydrateRowsMeta", "useMemo", "rowPositionsDebounceMs", "storeMeasuredRowHeight", "needsHydration", "rowHasAutoHeight", "getLastMeasuredRowIndex", "setLastMeasuredRowIndex", "index", "resetRowHeights", "useEffect", "rowsMetaApi", "unstable_setLastMeasuredRowIndex", "unstable_getRowHeight", "unstable_getRowInternalSizes", "unstable_setRowHeight", "unstable_storeRowHeightMeasurement", "rowsMetaPrivateApi"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/rows/useGridRowsMeta.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_debounce as debounce } from '@mui/utils';\nimport { useGridVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridDensityFactorSelector } from \"../density/densitySelector.js\";\nimport { gridFilterModelSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridPaginationSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridSortModelSelector } from \"../sorting/gridSortingSelector.js\";\nimport { useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { gridPinnedRowsSelector } from \"./gridRowsSelector.js\";\nimport { DATA_GRID_PROPS_DEFAULT_VALUES } from \"../../../DataGrid/useDataGridProps.js\";\n\n// TODO: I think the row heights can now be encoded as a single `size` instead of `sizes.baseXxxx`\n\nexport const rowsMetaStateInitializer = state => _extends({}, state, {\n  rowsMeta: {\n    currentPageTotalHeight: 0,\n    positions: []\n  }\n});\nlet warnedOnceInvalidRowHeight = false;\nconst getValidRowHeight = (rowHeightProp, defaultRowHeight, warningMessage) => {\n  if (typeof rowHeightProp === 'number' && rowHeightProp > 0) {\n    return rowHeightProp;\n  }\n  if (process.env.NODE_ENV !== 'production' && !warnedOnceInvalidRowHeight && typeof rowHeightProp !== 'undefined' && rowHeightProp !== null) {\n    console.warn(warningMessage);\n    warnedOnceInvalidRowHeight = true;\n  }\n  return defaultRowHeight;\n};\nconst rowHeightWarning = [`MUI X: The \\`rowHeight\\` prop should be a number greater than 0.`, `The default value will be used instead.`].join('\\n');\nconst getRowHeightWarning = [`MUI X: The \\`getRowHeight\\` prop should return a number greater than 0 or 'auto'.`, `The default value will be used instead.`].join('\\n');\n\n/**\n * @requires useGridPageSize (method)\n * @requires useGridPage (method)\n */\nexport const useGridRowsMeta = (apiRef, props) => {\n  const {\n    getRowHeight: getRowHeightProp,\n    getRowSpacing,\n    getEstimatedRowHeight\n  } = props;\n  const rowsHeightLookup = React.useRef(Object.create(null));\n\n  // Inspired by https://github.com/bvaughn/react-virtualized/blob/master/source/Grid/utils/CellSizeAndPositionManager.js\n  const lastMeasuredRowIndex = React.useRef(-1);\n  const hasRowWithAutoHeight = React.useRef(false);\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const filterModel = useGridSelector(apiRef, gridFilterModelSelector);\n  const paginationState = useGridSelector(apiRef, gridPaginationSelector);\n  const sortModel = useGridSelector(apiRef, gridSortModelSelector);\n  const currentPage = useGridVisibleRows(apiRef, props);\n  const pinnedRows = useGridSelector(apiRef, gridPinnedRowsSelector);\n  const validRowHeight = getValidRowHeight(props.rowHeight, DATA_GRID_PROPS_DEFAULT_VALUES.rowHeight, rowHeightWarning);\n  const rowHeight = Math.floor(validRowHeight * densityFactor);\n  const hydrateRowsMeta = React.useCallback(() => {\n    hasRowWithAutoHeight.current = false;\n    const calculateRowProcessedSizes = row => {\n      if (!rowsHeightLookup.current[row.id]) {\n        rowsHeightLookup.current[row.id] = {\n          sizes: {\n            baseCenter: rowHeight\n          },\n          isResized: false,\n          autoHeight: false,\n          needsFirstMeasurement: true // Assume all rows will need to be measured by default\n        };\n      }\n      const {\n        isResized,\n        needsFirstMeasurement,\n        sizes\n      } = rowsHeightLookup.current[row.id];\n      let baseRowHeight = typeof rowHeight === 'number' && rowHeight > 0 ? rowHeight : 52;\n      const existingBaseRowHeight = sizes.baseCenter;\n      if (isResized) {\n        // Do not recalculate resized row height and use the value from the lookup\n        baseRowHeight = existingBaseRowHeight;\n      } else if (getRowHeightProp) {\n        const rowHeightFromUser = getRowHeightProp(_extends({}, row, {\n          densityFactor\n        }));\n        if (rowHeightFromUser === 'auto') {\n          if (needsFirstMeasurement) {\n            const estimatedRowHeight = getEstimatedRowHeight ? getEstimatedRowHeight(_extends({}, row, {\n              densityFactor\n            })) : rowHeight;\n\n            // If the row was not measured yet use the estimated row height\n            baseRowHeight = estimatedRowHeight ?? rowHeight;\n          } else {\n            baseRowHeight = existingBaseRowHeight;\n          }\n          hasRowWithAutoHeight.current = true;\n          rowsHeightLookup.current[row.id].autoHeight = true;\n        } else {\n          // Default back to base rowHeight if getRowHeight returns invalid value.\n          baseRowHeight = getValidRowHeight(rowHeightFromUser, rowHeight, getRowHeightWarning);\n          rowsHeightLookup.current[row.id].needsFirstMeasurement = false;\n          rowsHeightLookup.current[row.id].autoHeight = false;\n        }\n      } else {\n        rowsHeightLookup.current[row.id].needsFirstMeasurement = false;\n      }\n      const initialHeights = {\n        baseCenter: baseRowHeight\n      };\n      if (getRowSpacing) {\n        const indexRelativeToCurrentPage = apiRef.current.getRowIndexRelativeToVisibleRows(row.id);\n        const spacing = getRowSpacing(_extends({}, row, {\n          isFirstVisible: indexRelativeToCurrentPage === 0,\n          isLastVisible: indexRelativeToCurrentPage === currentPage.rows.length - 1,\n          indexRelativeToCurrentPage\n        }));\n        initialHeights.spacingTop = spacing.top ?? 0;\n        initialHeights.spacingBottom = spacing.bottom ?? 0;\n      }\n      const processedSizes = apiRef.current.unstable_applyPipeProcessors('rowHeight', initialHeights, row);\n      rowsHeightLookup.current[row.id].sizes = processedSizes;\n      return processedSizes;\n    };\n    const positions = [];\n    const currentPageTotalHeight = currentPage.rows.reduce((acc, row) => {\n      positions.push(acc);\n      let otherSizes = 0;\n      const processedSizes = calculateRowProcessedSizes(row);\n      /* eslint-disable-next-line guard-for-in */\n      for (const key in processedSizes) {\n        const value = processedSizes[key];\n        if (key !== 'baseCenter') {\n          otherSizes += value;\n        }\n      }\n      return acc + processedSizes.baseCenter + otherSizes;\n    }, 0);\n    pinnedRows?.top?.forEach(row => {\n      calculateRowProcessedSizes(row);\n    });\n    pinnedRows?.bottom?.forEach(row => {\n      calculateRowProcessedSizes(row);\n    });\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rowsMeta: {\n          currentPageTotalHeight,\n          positions\n        }\n      });\n    });\n    if (!hasRowWithAutoHeight.current) {\n      // No row has height=auto, so all rows are already measured\n      lastMeasuredRowIndex.current = Infinity;\n    }\n    apiRef.current.forceUpdate();\n  }, [apiRef, currentPage.rows, rowHeight, getRowHeightProp, getRowSpacing, getEstimatedRowHeight, pinnedRows, densityFactor]);\n  const getRowHeight = React.useCallback(rowId => {\n    const height = rowsHeightLookup.current[rowId];\n    return height ? height.sizes.baseCenter : rowHeight;\n  }, [rowHeight]);\n  const getRowInternalSizes = rowId => rowsHeightLookup.current[rowId]?.sizes;\n  const setRowHeight = React.useCallback((id, height) => {\n    rowsHeightLookup.current[id].sizes.baseCenter = height;\n    rowsHeightLookup.current[id].isResized = true;\n    rowsHeightLookup.current[id].needsFirstMeasurement = false;\n    hydrateRowsMeta();\n  }, [hydrateRowsMeta]);\n  const debouncedHydrateRowsMeta = React.useMemo(() => debounce(hydrateRowsMeta, props.rowPositionsDebounceMs), [hydrateRowsMeta, props.rowPositionsDebounceMs]);\n  const storeMeasuredRowHeight = React.useCallback((id, height) => {\n    if (!rowsHeightLookup.current[id] || !rowsHeightLookup.current[id].autoHeight) {\n      return;\n    }\n\n    // Only trigger hydration if the value is different, otherwise we trigger a loop\n    const needsHydration = rowsHeightLookup.current[id].sizes.baseCenter !== height;\n    rowsHeightLookup.current[id].needsFirstMeasurement = false;\n    rowsHeightLookup.current[id].sizes.baseCenter = height;\n    if (needsHydration) {\n      debouncedHydrateRowsMeta();\n    }\n  }, [debouncedHydrateRowsMeta]);\n  const rowHasAutoHeight = React.useCallback(id => {\n    return rowsHeightLookup.current[id]?.autoHeight || false;\n  }, []);\n  const getLastMeasuredRowIndex = React.useCallback(() => {\n    return lastMeasuredRowIndex.current;\n  }, []);\n  const setLastMeasuredRowIndex = React.useCallback(index => {\n    if (hasRowWithAutoHeight.current && index > lastMeasuredRowIndex.current) {\n      lastMeasuredRowIndex.current = index;\n    }\n  }, []);\n  const resetRowHeights = React.useCallback(() => {\n    rowsHeightLookup.current = {};\n    hydrateRowsMeta();\n  }, [hydrateRowsMeta]);\n\n  // The effect is used to build the rows meta data - currentPageTotalHeight and positions.\n  // Because of variable row height this is needed for the virtualization\n  React.useEffect(() => {\n    hydrateRowsMeta();\n  }, [rowHeight, filterModel, paginationState, sortModel, hydrateRowsMeta]);\n  useGridRegisterPipeApplier(apiRef, 'rowHeight', hydrateRowsMeta);\n  const rowsMetaApi = {\n    unstable_setLastMeasuredRowIndex: setLastMeasuredRowIndex,\n    unstable_getRowHeight: getRowHeight,\n    unstable_getRowInternalSizes: getRowInternalSizes,\n    unstable_setRowHeight: setRowHeight,\n    unstable_storeRowHeightMeasurement: storeMeasuredRowHeight,\n    resetRowHeights\n  };\n  const rowsMetaPrivateApi = {\n    getLastMeasuredRowIndex,\n    rowHasAutoHeight\n  };\n  useGridApiMethod(apiRef, rowsMetaApi, 'public');\n  useGridApiMethod(apiRef, rowsMetaPrivateApi, 'private');\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,IAAIC,QAAQ,QAAQ,YAAY;AAC1D,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,uBAAuB,QAAQ,iCAAiC;AACzE,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,0BAA0B,QAAQ,oCAAoC;AAC/E,SAASC,sBAAsB,QAAQ,uBAAuB;AAC9D,SAASC,8BAA8B,QAAQ,uCAAuC;;AAEtF;;AAEA,OAAO,MAAMC,wBAAwB,GAAGC,KAAK,IAAIf,QAAQ,CAAC,CAAC,CAAC,EAAEe,KAAK,EAAE;EACnEC,QAAQ,EAAE;IACRC,sBAAsB,EAAE,CAAC;IACzBC,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AACF,IAAIC,0BAA0B,GAAG,KAAK;AACtC,MAAMC,iBAAiB,GAAGA,CAACC,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,KAAK;EAC7E,IAAI,OAAOF,aAAa,KAAK,QAAQ,IAAIA,aAAa,GAAG,CAAC,EAAE;IAC1D,OAAOA,aAAa;EACtB;EACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACP,0BAA0B,IAAI,OAAOE,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,IAAI,EAAE;IAC1IM,OAAO,CAACC,IAAI,CAACL,cAAc,CAAC;IAC5BJ,0BAA0B,GAAG,IAAI;EACnC;EACA,OAAOG,gBAAgB;AACzB,CAAC;AACD,MAAMO,gBAAgB,GAAG,CAAC,kEAAkE,EAAE,yCAAyC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AACnJ,MAAMC,mBAAmB,GAAG,CAAC,mFAAmF,EAAE,yCAAyC,CAAC,CAACD,IAAI,CAAC,IAAI,CAAC;;AAEvK;AACA;AACA;AACA;AACA,OAAO,MAAME,eAAe,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAChD,MAAM;IACJC,YAAY,EAAEC,gBAAgB;IAC9BC,aAAa;IACbC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,gBAAgB,GAAGtC,KAAK,CAACuC,MAAM,CAACC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;;EAE1D;EACA,MAAMC,oBAAoB,GAAG1C,KAAK,CAACuC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7C,MAAMI,oBAAoB,GAAG3C,KAAK,CAACuC,MAAM,CAAC,KAAK,CAAC;EAChD,MAAMK,aAAa,GAAGvC,eAAe,CAAC2B,MAAM,EAAE1B,yBAAyB,CAAC;EACxE,MAAMuC,WAAW,GAAGxC,eAAe,CAAC2B,MAAM,EAAEzB,uBAAuB,CAAC;EACpE,MAAMuC,eAAe,GAAGzC,eAAe,CAAC2B,MAAM,EAAExB,sBAAsB,CAAC;EACvE,MAAMuC,SAAS,GAAG1C,eAAe,CAAC2B,MAAM,EAAEvB,qBAAqB,CAAC;EAChE,MAAMuC,WAAW,GAAG7C,kBAAkB,CAAC6B,MAAM,EAAEC,KAAK,CAAC;EACrD,MAAMgB,UAAU,GAAG5C,eAAe,CAAC2B,MAAM,EAAErB,sBAAsB,CAAC;EAClE,MAAMuC,cAAc,GAAG/B,iBAAiB,CAACc,KAAK,CAACkB,SAAS,EAAEvC,8BAA8B,CAACuC,SAAS,EAAEvB,gBAAgB,CAAC;EACrH,MAAMuB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACH,cAAc,GAAGN,aAAa,CAAC;EAC5D,MAAMU,eAAe,GAAGtD,KAAK,CAACuD,WAAW,CAAC,MAAM;IAC9CZ,oBAAoB,CAACa,OAAO,GAAG,KAAK;IACpC,MAAMC,0BAA0B,GAAGC,GAAG,IAAI;MACxC,IAAI,CAACpB,gBAAgB,CAACkB,OAAO,CAACE,GAAG,CAACC,EAAE,CAAC,EAAE;QACrCrB,gBAAgB,CAACkB,OAAO,CAACE,GAAG,CAACC,EAAE,CAAC,GAAG;UACjCC,KAAK,EAAE;YACLC,UAAU,EAAEV;UACd,CAAC;UACDW,SAAS,EAAE,KAAK;UAChBC,UAAU,EAAE,KAAK;UACjBC,qBAAqB,EAAE,IAAI,CAAC;QAC9B,CAAC;MACH;MACA,MAAM;QACJF,SAAS;QACTE,qBAAqB;QACrBJ;MACF,CAAC,GAAGtB,gBAAgB,CAACkB,OAAO,CAACE,GAAG,CAACC,EAAE,CAAC;MACpC,IAAIM,aAAa,GAAG,OAAOd,SAAS,KAAK,QAAQ,IAAIA,SAAS,GAAG,CAAC,GAAGA,SAAS,GAAG,EAAE;MACnF,MAAMe,qBAAqB,GAAGN,KAAK,CAACC,UAAU;MAC9C,IAAIC,SAAS,EAAE;QACb;QACAG,aAAa,GAAGC,qBAAqB;MACvC,CAAC,MAAM,IAAI/B,gBAAgB,EAAE;QAC3B,MAAMgC,iBAAiB,GAAGhC,gBAAgB,CAACpC,QAAQ,CAAC,CAAC,CAAC,EAAE2D,GAAG,EAAE;UAC3Dd;QACF,CAAC,CAAC,CAAC;QACH,IAAIuB,iBAAiB,KAAK,MAAM,EAAE;UAChC,IAAIH,qBAAqB,EAAE;YACzB,MAAMI,kBAAkB,GAAG/B,qBAAqB,GAAGA,qBAAqB,CAACtC,QAAQ,CAAC,CAAC,CAAC,EAAE2D,GAAG,EAAE;cACzFd;YACF,CAAC,CAAC,CAAC,GAAGO,SAAS;;YAEf;YACAc,aAAa,GAAGG,kBAAkB,IAAIjB,SAAS;UACjD,CAAC,MAAM;YACLc,aAAa,GAAGC,qBAAqB;UACvC;UACAvB,oBAAoB,CAACa,OAAO,GAAG,IAAI;UACnClB,gBAAgB,CAACkB,OAAO,CAACE,GAAG,CAACC,EAAE,CAAC,CAACI,UAAU,GAAG,IAAI;QACpD,CAAC,MAAM;UACL;UACAE,aAAa,GAAG9C,iBAAiB,CAACgD,iBAAiB,EAAEhB,SAAS,EAAErB,mBAAmB,CAAC;UACpFQ,gBAAgB,CAACkB,OAAO,CAACE,GAAG,CAACC,EAAE,CAAC,CAACK,qBAAqB,GAAG,KAAK;UAC9D1B,gBAAgB,CAACkB,OAAO,CAACE,GAAG,CAACC,EAAE,CAAC,CAACI,UAAU,GAAG,KAAK;QACrD;MACF,CAAC,MAAM;QACLzB,gBAAgB,CAACkB,OAAO,CAACE,GAAG,CAACC,EAAE,CAAC,CAACK,qBAAqB,GAAG,KAAK;MAChE;MACA,MAAMK,cAAc,GAAG;QACrBR,UAAU,EAAEI;MACd,CAAC;MACD,IAAI7B,aAAa,EAAE;QACjB,MAAMkC,0BAA0B,GAAGtC,MAAM,CAACwB,OAAO,CAACe,gCAAgC,CAACb,GAAG,CAACC,EAAE,CAAC;QAC1F,MAAMa,OAAO,GAAGpC,aAAa,CAACrC,QAAQ,CAAC,CAAC,CAAC,EAAE2D,GAAG,EAAE;UAC9Ce,cAAc,EAAEH,0BAA0B,KAAK,CAAC;UAChDI,aAAa,EAAEJ,0BAA0B,KAAKtB,WAAW,CAAC2B,IAAI,CAACC,MAAM,GAAG,CAAC;UACzEN;QACF,CAAC,CAAC,CAAC;QACHD,cAAc,CAACQ,UAAU,GAAGL,OAAO,CAACM,GAAG,IAAI,CAAC;QAC5CT,cAAc,CAACU,aAAa,GAAGP,OAAO,CAACQ,MAAM,IAAI,CAAC;MACpD;MACA,MAAMC,cAAc,GAAGjD,MAAM,CAACwB,OAAO,CAAC0B,4BAA4B,CAAC,WAAW,EAAEb,cAAc,EAAEX,GAAG,CAAC;MACpGpB,gBAAgB,CAACkB,OAAO,CAACE,GAAG,CAACC,EAAE,CAAC,CAACC,KAAK,GAAGqB,cAAc;MACvD,OAAOA,cAAc;IACvB,CAAC;IACD,MAAMhE,SAAS,GAAG,EAAE;IACpB,MAAMD,sBAAsB,GAAGgC,WAAW,CAAC2B,IAAI,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAE1B,GAAG,KAAK;MACnEzC,SAAS,CAACoE,IAAI,CAACD,GAAG,CAAC;MACnB,IAAIE,UAAU,GAAG,CAAC;MAClB,MAAML,cAAc,GAAGxB,0BAA0B,CAACC,GAAG,CAAC;MACtD;MACA,KAAK,MAAM6B,GAAG,IAAIN,cAAc,EAAE;QAChC,MAAMO,KAAK,GAAGP,cAAc,CAACM,GAAG,CAAC;QACjC,IAAIA,GAAG,KAAK,YAAY,EAAE;UACxBD,UAAU,IAAIE,KAAK;QACrB;MACF;MACA,OAAOJ,GAAG,GAAGH,cAAc,CAACpB,UAAU,GAAGyB,UAAU;IACrD,CAAC,EAAE,CAAC,CAAC;IACLrC,UAAU,EAAE6B,GAAG,EAAEW,OAAO,CAAC/B,GAAG,IAAI;MAC9BD,0BAA0B,CAACC,GAAG,CAAC;IACjC,CAAC,CAAC;IACFT,UAAU,EAAE+B,MAAM,EAAES,OAAO,CAAC/B,GAAG,IAAI;MACjCD,0BAA0B,CAACC,GAAG,CAAC;IACjC,CAAC,CAAC;IACF1B,MAAM,CAACwB,OAAO,CAACkC,QAAQ,CAAC5E,KAAK,IAAI;MAC/B,OAAOf,QAAQ,CAAC,CAAC,CAAC,EAAEe,KAAK,EAAE;QACzBC,QAAQ,EAAE;UACRC,sBAAsB;UACtBC;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAC0B,oBAAoB,CAACa,OAAO,EAAE;MACjC;MACAd,oBAAoB,CAACc,OAAO,GAAGmC,QAAQ;IACzC;IACA3D,MAAM,CAACwB,OAAO,CAACoC,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAAC5D,MAAM,EAAEgB,WAAW,CAAC2B,IAAI,EAAExB,SAAS,EAAEhB,gBAAgB,EAAEC,aAAa,EAAEC,qBAAqB,EAAEY,UAAU,EAAEL,aAAa,CAAC,CAAC;EAC5H,MAAMV,YAAY,GAAGlC,KAAK,CAACuD,WAAW,CAACsC,KAAK,IAAI;IAC9C,MAAMC,MAAM,GAAGxD,gBAAgB,CAACkB,OAAO,CAACqC,KAAK,CAAC;IAC9C,OAAOC,MAAM,GAAGA,MAAM,CAAClC,KAAK,CAACC,UAAU,GAAGV,SAAS;EACrD,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACf,MAAM4C,mBAAmB,GAAGF,KAAK,IAAIvD,gBAAgB,CAACkB,OAAO,CAACqC,KAAK,CAAC,EAAEjC,KAAK;EAC3E,MAAMoC,YAAY,GAAGhG,KAAK,CAACuD,WAAW,CAAC,CAACI,EAAE,EAAEmC,MAAM,KAAK;IACrDxD,gBAAgB,CAACkB,OAAO,CAACG,EAAE,CAAC,CAACC,KAAK,CAACC,UAAU,GAAGiC,MAAM;IACtDxD,gBAAgB,CAACkB,OAAO,CAACG,EAAE,CAAC,CAACG,SAAS,GAAG,IAAI;IAC7CxB,gBAAgB,CAACkB,OAAO,CAACG,EAAE,CAAC,CAACK,qBAAqB,GAAG,KAAK;IAC1DV,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EACrB,MAAM2C,wBAAwB,GAAGjG,KAAK,CAACkG,OAAO,CAAC,MAAMhG,QAAQ,CAACoD,eAAe,EAAErB,KAAK,CAACkE,sBAAsB,CAAC,EAAE,CAAC7C,eAAe,EAAErB,KAAK,CAACkE,sBAAsB,CAAC,CAAC;EAC9J,MAAMC,sBAAsB,GAAGpG,KAAK,CAACuD,WAAW,CAAC,CAACI,EAAE,EAAEmC,MAAM,KAAK;IAC/D,IAAI,CAACxD,gBAAgB,CAACkB,OAAO,CAACG,EAAE,CAAC,IAAI,CAACrB,gBAAgB,CAACkB,OAAO,CAACG,EAAE,CAAC,CAACI,UAAU,EAAE;MAC7E;IACF;;IAEA;IACA,MAAMsC,cAAc,GAAG/D,gBAAgB,CAACkB,OAAO,CAACG,EAAE,CAAC,CAACC,KAAK,CAACC,UAAU,KAAKiC,MAAM;IAC/ExD,gBAAgB,CAACkB,OAAO,CAACG,EAAE,CAAC,CAACK,qBAAqB,GAAG,KAAK;IAC1D1B,gBAAgB,CAACkB,OAAO,CAACG,EAAE,CAAC,CAACC,KAAK,CAACC,UAAU,GAAGiC,MAAM;IACtD,IAAIO,cAAc,EAAE;MAClBJ,wBAAwB,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACA,wBAAwB,CAAC,CAAC;EAC9B,MAAMK,gBAAgB,GAAGtG,KAAK,CAACuD,WAAW,CAACI,EAAE,IAAI;IAC/C,OAAOrB,gBAAgB,CAACkB,OAAO,CAACG,EAAE,CAAC,EAAEI,UAAU,IAAI,KAAK;EAC1D,CAAC,EAAE,EAAE,CAAC;EACN,MAAMwC,uBAAuB,GAAGvG,KAAK,CAACuD,WAAW,CAAC,MAAM;IACtD,OAAOb,oBAAoB,CAACc,OAAO;EACrC,CAAC,EAAE,EAAE,CAAC;EACN,MAAMgD,uBAAuB,GAAGxG,KAAK,CAACuD,WAAW,CAACkD,KAAK,IAAI;IACzD,IAAI9D,oBAAoB,CAACa,OAAO,IAAIiD,KAAK,GAAG/D,oBAAoB,CAACc,OAAO,EAAE;MACxEd,oBAAoB,CAACc,OAAO,GAAGiD,KAAK;IACtC;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,eAAe,GAAG1G,KAAK,CAACuD,WAAW,CAAC,MAAM;IAC9CjB,gBAAgB,CAACkB,OAAO,GAAG,CAAC,CAAC;IAC7BF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA;EACAtD,KAAK,CAAC2G,SAAS,CAAC,MAAM;IACpBrD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACH,SAAS,EAAEN,WAAW,EAAEC,eAAe,EAAEC,SAAS,EAAEO,eAAe,CAAC,CAAC;EACzE5C,0BAA0B,CAACsB,MAAM,EAAE,WAAW,EAAEsB,eAAe,CAAC;EAChE,MAAMsD,WAAW,GAAG;IAClBC,gCAAgC,EAAEL,uBAAuB;IACzDM,qBAAqB,EAAE5E,YAAY;IACnC6E,4BAA4B,EAAEhB,mBAAmB;IACjDiB,qBAAqB,EAAEhB,YAAY;IACnCiB,kCAAkC,EAAEb,sBAAsB;IAC1DM;EACF,CAAC;EACD,MAAMQ,kBAAkB,GAAG;IACzBX,uBAAuB;IACvBD;EACF,CAAC;EACDlG,gBAAgB,CAAC4B,MAAM,EAAE4E,WAAW,EAAE,QAAQ,CAAC;EAC/CxG,gBAAgB,CAAC4B,MAAM,EAAEkF,kBAAkB,EAAE,SAAS,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}