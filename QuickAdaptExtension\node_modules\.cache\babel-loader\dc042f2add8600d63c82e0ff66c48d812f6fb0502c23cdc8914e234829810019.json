{"ast": null, "code": "export class Store {\n  static create(value) {\n    return new Store(value);\n  }\n  constructor(_value) {\n    this.value = void 0;\n    this.listeners = void 0;\n    this.subscribe = fn => {\n      this.listeners.add(fn);\n      return () => {\n        this.listeners.delete(fn);\n      };\n    };\n    this.getSnapshot = () => {\n      return this.value;\n    };\n    this.update = value => {\n      this.value = value;\n      this.listeners.forEach(l => l(value));\n    };\n    this.value = _value;\n    this.listeners = new Set();\n  }\n}", "map": {"version": 3, "names": ["Store", "create", "value", "constructor", "_value", "listeners", "subscribe", "fn", "add", "delete", "getSnapshot", "update", "for<PERSON>ach", "l", "Set"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/utils/Store.js"], "sourcesContent": ["export class Store {\n  static create(value) {\n    return new Store(value);\n  }\n  constructor(_value) {\n    this.value = void 0;\n    this.listeners = void 0;\n    this.subscribe = fn => {\n      this.listeners.add(fn);\n      return () => {\n        this.listeners.delete(fn);\n      };\n    };\n    this.getSnapshot = () => {\n      return this.value;\n    };\n    this.update = value => {\n      this.value = value;\n      this.listeners.forEach(l => l(value));\n    };\n    this.value = _value;\n    this.listeners = new Set();\n  }\n}"], "mappings": "AAAA,OAAO,MAAMA,KAAK,CAAC;EACjB,OAAOC,MAAMA,CAACC,KAAK,EAAE;IACnB,OAAO,IAAIF,KAAK,CAACE,KAAK,CAAC;EACzB;EACAC,WAAWA,CAACC,MAAM,EAAE;IAClB,IAAI,CAACF,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAACG,SAAS,GAAG,KAAK,CAAC;IACvB,IAAI,CAACC,SAAS,GAAGC,EAAE,IAAI;MACrB,IAAI,CAACF,SAAS,CAACG,GAAG,CAACD,EAAE,CAAC;MACtB,OAAO,MAAM;QACX,IAAI,CAACF,SAAS,CAACI,MAAM,CAACF,EAAE,CAAC;MAC3B,CAAC;IACH,CAAC;IACD,IAAI,CAACG,WAAW,GAAG,MAAM;MACvB,OAAO,IAAI,CAACR,KAAK;IACnB,CAAC;IACD,IAAI,CAACS,MAAM,GAAGT,KAAK,IAAI;MACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACG,SAAS,CAACO,OAAO,CAACC,CAAC,IAAIA,CAAC,CAACX,KAAK,CAAC,CAAC;IACvC,CAAC;IACD,IAAI,CAACA,KAAK,GAAGE,MAAM;IACnB,IAAI,CAACC,SAAS,GAAG,IAAIS,GAAG,CAAC,CAAC;EAC5B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}