{"ast": null, "code": "import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnPositionsSelector, gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridPageSelector, gridPageSizeSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridRowCountSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridDimensionsSelector } from \"../dimensions/index.js\";\n\n// Logic copied from https://www.w3.org/TR/wai-aria-practices/examples/listbox/js/listbox.js\n// Similar to https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView\nfunction scrollIntoView(dimensions) {\n  const {\n    containerSize,\n    scrollPosition,\n    elementSize,\n    elementOffset\n  } = dimensions;\n  const elementEnd = elementOffset + elementSize;\n  // Always scroll to top when cell is higher than viewport to avoid scroll jump\n  // See https://github.com/mui/mui-x/issues/4513 and https://github.com/mui/mui-x/issues/4514\n  if (elementSize > containerSize) {\n    return elementOffset;\n  }\n  if (elementEnd - containerSize > scrollPosition) {\n    return elementEnd - containerSize;\n  }\n  if (elementOffset < scrollPosition) {\n    return elementOffset;\n  }\n  return undefined;\n}\n\n/**\n * @requires useGridPagination (state) - can be after, async only\n * @requires useGridColumns (state) - can be after, async only\n * @requires useGridRows (state) - can be after, async only\n * @requires useGridRowsMeta (state) - can be after, async only\n * @requires useGridFilter (state)\n * @requires useGridColumnSpanning (method)\n */\nexport const useGridScroll = (apiRef, props) => {\n  const isRtl = useRtl();\n  const logger = useGridLogger(apiRef, 'useGridScroll');\n  const colRef = apiRef.current.columnHeadersContainerRef;\n  const virtualScrollerRef = apiRef.current.virtualScrollerRef;\n  const virtualScrollbarHorizontalRef = apiRef.current.virtualScrollbarHorizontalRef;\n  const virtualScrollbarVerticalRef = apiRef.current.virtualScrollbarVerticalRef;\n  const visibleSortedRows = useGridSelector(apiRef, gridExpandedSortedRowEntriesSelector);\n  const scrollToIndexes = React.useCallback(params => {\n    const dimensions = gridDimensionsSelector(apiRef.current.state);\n    const totalRowCount = gridRowCountSelector(apiRef);\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const scrollToHeader = params.rowIndex == null;\n    if (!scrollToHeader && totalRowCount === 0 || visibleColumns.length === 0) {\n      return false;\n    }\n    logger.debug(`Scrolling to cell at row ${params.rowIndex}, col: ${params.colIndex} `);\n    let scrollCoordinates = {};\n    if (params.colIndex !== undefined) {\n      const columnPositions = gridColumnPositionsSelector(apiRef);\n      let cellWidth;\n      if (typeof params.rowIndex !== 'undefined') {\n        const rowId = visibleSortedRows[params.rowIndex]?.id;\n        const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, params.colIndex);\n        if (cellColSpanInfo && !cellColSpanInfo.spannedByColSpan) {\n          cellWidth = cellColSpanInfo.cellProps.width;\n        }\n      }\n      if (typeof cellWidth === 'undefined') {\n        cellWidth = visibleColumns[params.colIndex].computedWidth;\n      }\n      // When using RTL, `scrollLeft` becomes negative, so we must ensure that we only compare values.\n      scrollCoordinates.left = scrollIntoView({\n        containerSize: dimensions.viewportOuterSize.width,\n        scrollPosition: Math.abs(virtualScrollerRef.current.scrollLeft),\n        elementSize: cellWidth,\n        elementOffset: columnPositions[params.colIndex]\n      });\n    }\n    if (params.rowIndex !== undefined) {\n      const rowsMeta = gridRowsMetaSelector(apiRef.current.state);\n      const page = gridPageSelector(apiRef);\n      const pageSize = gridPageSizeSelector(apiRef);\n      const elementIndex = !props.pagination ? params.rowIndex : params.rowIndex - page * pageSize;\n      const targetOffsetHeight = rowsMeta.positions[elementIndex + 1] ? rowsMeta.positions[elementIndex + 1] - rowsMeta.positions[elementIndex] : rowsMeta.currentPageTotalHeight - rowsMeta.positions[elementIndex];\n      scrollCoordinates.top = scrollIntoView({\n        containerSize: dimensions.viewportInnerSize.height,\n        scrollPosition: virtualScrollerRef.current.scrollTop,\n        elementSize: targetOffsetHeight,\n        elementOffset: rowsMeta.positions[elementIndex]\n      });\n    }\n    scrollCoordinates = apiRef.current.unstable_applyPipeProcessors('scrollToIndexes', scrollCoordinates, params);\n    if (typeof scrollCoordinates.left !== undefined || typeof scrollCoordinates.top !== undefined) {\n      apiRef.current.scroll(scrollCoordinates);\n      return true;\n    }\n    return false;\n  }, [logger, apiRef, virtualScrollerRef, props.pagination, visibleSortedRows]);\n  const scroll = React.useCallback(params => {\n    if (virtualScrollerRef.current && virtualScrollbarHorizontalRef.current && params.left !== undefined && colRef.current) {\n      const direction = isRtl ? -1 : 1;\n      colRef.current.scrollLeft = params.left;\n      virtualScrollerRef.current.scrollLeft = direction * params.left;\n      virtualScrollbarHorizontalRef.current.scrollLeft = direction * params.left;\n      logger.debug(`Scrolling left: ${params.left}`);\n    }\n    if (virtualScrollerRef.current && virtualScrollbarVerticalRef.current && params.top !== undefined) {\n      virtualScrollerRef.current.scrollTop = params.top;\n      virtualScrollbarVerticalRef.current.scrollTop = params.top;\n      logger.debug(`Scrolling top: ${params.top}`);\n    }\n    logger.debug(`Scrolling, updating container, and viewport`);\n  }, [virtualScrollerRef, virtualScrollbarHorizontalRef, virtualScrollbarVerticalRef, isRtl, colRef, logger]);\n  const getScrollPosition = React.useCallback(() => {\n    if (!virtualScrollerRef?.current) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    return {\n      top: virtualScrollerRef.current.scrollTop,\n      left: virtualScrollerRef.current.scrollLeft\n    };\n  }, [virtualScrollerRef]);\n  const scrollApi = {\n    scroll,\n    scrollToIndexes,\n    getScrollPosition\n  };\n  useGridApiMethod(apiRef, scrollApi, 'public');\n};", "map": {"version": 3, "names": ["React", "useRtl", "useGridLogger", "gridColumnPositionsSelector", "gridVisibleColumnDefinitionsSelector", "useGridSelector", "gridPageSelector", "gridPageSizeSelector", "gridRowCountSelector", "gridRowsMetaSelector", "useGridApiMethod", "gridExpandedSortedRowEntriesSelector", "gridDimensionsSelector", "scrollIntoView", "dimensions", "containerSize", "scrollPosition", "elementSize", "elementOffset", "elementEnd", "undefined", "useGridScroll", "apiRef", "props", "isRtl", "logger", "colRef", "current", "columnHeadersContainerRef", "virtualScrollerRef", "virtualScrollbarHorizontalRef", "virtualScrollbarVerticalRef", "visibleSortedRows", "scrollToIndexes", "useCallback", "params", "state", "totalRowCount", "visibleColumns", "scrollToHeader", "rowIndex", "length", "debug", "colIndex", "scrollCoordinates", "columnPositions", "cellWidth", "rowId", "id", "cellColSpanInfo", "unstable_getCellColSpanInfo", "spannedByColSpan", "cellProps", "width", "computedWidth", "left", "viewportOuterSize", "Math", "abs", "scrollLeft", "rowsMeta", "page", "pageSize", "elementIndex", "pagination", "targetOffsetHeight", "positions", "currentPageTotalHeight", "top", "viewportInnerSize", "height", "scrollTop", "unstable_applyPipeProcessors", "scroll", "direction", "getScrollPosition", "scrollApi"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/scroll/useGridScroll.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnPositionsSelector, gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridPageSelector, gridPageSizeSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridRowCountSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridDimensionsSelector } from \"../dimensions/index.js\";\n\n// Logic copied from https://www.w3.org/TR/wai-aria-practices/examples/listbox/js/listbox.js\n// Similar to https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView\nfunction scrollIntoView(dimensions) {\n  const {\n    containerSize,\n    scrollPosition,\n    elementSize,\n    elementOffset\n  } = dimensions;\n  const elementEnd = elementOffset + elementSize;\n  // Always scroll to top when cell is higher than viewport to avoid scroll jump\n  // See https://github.com/mui/mui-x/issues/4513 and https://github.com/mui/mui-x/issues/4514\n  if (elementSize > containerSize) {\n    return elementOffset;\n  }\n  if (elementEnd - containerSize > scrollPosition) {\n    return elementEnd - containerSize;\n  }\n  if (elementOffset < scrollPosition) {\n    return elementOffset;\n  }\n  return undefined;\n}\n\n/**\n * @requires useGridPagination (state) - can be after, async only\n * @requires useGridColumns (state) - can be after, async only\n * @requires useGridRows (state) - can be after, async only\n * @requires useGridRowsMeta (state) - can be after, async only\n * @requires useGridFilter (state)\n * @requires useGridColumnSpanning (method)\n */\nexport const useGridScroll = (apiRef, props) => {\n  const isRtl = useRtl();\n  const logger = useGridLogger(apiRef, 'useGridScroll');\n  const colRef = apiRef.current.columnHeadersContainerRef;\n  const virtualScrollerRef = apiRef.current.virtualScrollerRef;\n  const virtualScrollbarHorizontalRef = apiRef.current.virtualScrollbarHorizontalRef;\n  const virtualScrollbarVerticalRef = apiRef.current.virtualScrollbarVerticalRef;\n  const visibleSortedRows = useGridSelector(apiRef, gridExpandedSortedRowEntriesSelector);\n  const scrollToIndexes = React.useCallback(params => {\n    const dimensions = gridDimensionsSelector(apiRef.current.state);\n    const totalRowCount = gridRowCountSelector(apiRef);\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const scrollToHeader = params.rowIndex == null;\n    if (!scrollToHeader && totalRowCount === 0 || visibleColumns.length === 0) {\n      return false;\n    }\n    logger.debug(`Scrolling to cell at row ${params.rowIndex}, col: ${params.colIndex} `);\n    let scrollCoordinates = {};\n    if (params.colIndex !== undefined) {\n      const columnPositions = gridColumnPositionsSelector(apiRef);\n      let cellWidth;\n      if (typeof params.rowIndex !== 'undefined') {\n        const rowId = visibleSortedRows[params.rowIndex]?.id;\n        const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, params.colIndex);\n        if (cellColSpanInfo && !cellColSpanInfo.spannedByColSpan) {\n          cellWidth = cellColSpanInfo.cellProps.width;\n        }\n      }\n      if (typeof cellWidth === 'undefined') {\n        cellWidth = visibleColumns[params.colIndex].computedWidth;\n      }\n      // When using RTL, `scrollLeft` becomes negative, so we must ensure that we only compare values.\n      scrollCoordinates.left = scrollIntoView({\n        containerSize: dimensions.viewportOuterSize.width,\n        scrollPosition: Math.abs(virtualScrollerRef.current.scrollLeft),\n        elementSize: cellWidth,\n        elementOffset: columnPositions[params.colIndex]\n      });\n    }\n    if (params.rowIndex !== undefined) {\n      const rowsMeta = gridRowsMetaSelector(apiRef.current.state);\n      const page = gridPageSelector(apiRef);\n      const pageSize = gridPageSizeSelector(apiRef);\n      const elementIndex = !props.pagination ? params.rowIndex : params.rowIndex - page * pageSize;\n      const targetOffsetHeight = rowsMeta.positions[elementIndex + 1] ? rowsMeta.positions[elementIndex + 1] - rowsMeta.positions[elementIndex] : rowsMeta.currentPageTotalHeight - rowsMeta.positions[elementIndex];\n      scrollCoordinates.top = scrollIntoView({\n        containerSize: dimensions.viewportInnerSize.height,\n        scrollPosition: virtualScrollerRef.current.scrollTop,\n        elementSize: targetOffsetHeight,\n        elementOffset: rowsMeta.positions[elementIndex]\n      });\n    }\n    scrollCoordinates = apiRef.current.unstable_applyPipeProcessors('scrollToIndexes', scrollCoordinates, params);\n    if (typeof scrollCoordinates.left !== undefined || typeof scrollCoordinates.top !== undefined) {\n      apiRef.current.scroll(scrollCoordinates);\n      return true;\n    }\n    return false;\n  }, [logger, apiRef, virtualScrollerRef, props.pagination, visibleSortedRows]);\n  const scroll = React.useCallback(params => {\n    if (virtualScrollerRef.current && virtualScrollbarHorizontalRef.current && params.left !== undefined && colRef.current) {\n      const direction = isRtl ? -1 : 1;\n      colRef.current.scrollLeft = params.left;\n      virtualScrollerRef.current.scrollLeft = direction * params.left;\n      virtualScrollbarHorizontalRef.current.scrollLeft = direction * params.left;\n      logger.debug(`Scrolling left: ${params.left}`);\n    }\n    if (virtualScrollerRef.current && virtualScrollbarVerticalRef.current && params.top !== undefined) {\n      virtualScrollerRef.current.scrollTop = params.top;\n      virtualScrollbarVerticalRef.current.scrollTop = params.top;\n      logger.debug(`Scrolling top: ${params.top}`);\n    }\n    logger.debug(`Scrolling, updating container, and viewport`);\n  }, [virtualScrollerRef, virtualScrollbarHorizontalRef, virtualScrollbarVerticalRef, isRtl, colRef, logger]);\n  const getScrollPosition = React.useCallback(() => {\n    if (!virtualScrollerRef?.current) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    return {\n      top: virtualScrollerRef.current.scrollTop,\n      left: virtualScrollerRef.current.scrollLeft\n    };\n  }, [virtualScrollerRef]);\n  const scrollApi = {\n    scroll,\n    scrollToIndexes,\n    getScrollPosition\n  };\n  useGridApiMethod(apiRef, scrollApi, 'public');\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,2BAA2B,EAAEC,oCAAoC,QAAQ,mCAAmC;AACrH,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,gBAAgB,EAAEC,oBAAoB,QAAQ,yCAAyC;AAChG,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,oCAAoC,QAAQ,iCAAiC;AACtF,SAASC,sBAAsB,QAAQ,wBAAwB;;AAE/D;AACA;AACA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAClC,MAAM;IACJC,aAAa;IACbC,cAAc;IACdC,WAAW;IACXC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,UAAU,GAAGD,aAAa,GAAGD,WAAW;EAC9C;EACA;EACA,IAAIA,WAAW,GAAGF,aAAa,EAAE;IAC/B,OAAOG,aAAa;EACtB;EACA,IAAIC,UAAU,GAAGJ,aAAa,GAAGC,cAAc,EAAE;IAC/C,OAAOG,UAAU,GAAGJ,aAAa;EACnC;EACA,IAAIG,aAAa,GAAGF,cAAc,EAAE;IAClC,OAAOE,aAAa;EACtB;EACA,OAAOE,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC9C,MAAMC,KAAK,GAAGvB,MAAM,CAAC,CAAC;EACtB,MAAMwB,MAAM,GAAGvB,aAAa,CAACoB,MAAM,EAAE,eAAe,CAAC;EACrD,MAAMI,MAAM,GAAGJ,MAAM,CAACK,OAAO,CAACC,yBAAyB;EACvD,MAAMC,kBAAkB,GAAGP,MAAM,CAACK,OAAO,CAACE,kBAAkB;EAC5D,MAAMC,6BAA6B,GAAGR,MAAM,CAACK,OAAO,CAACG,6BAA6B;EAClF,MAAMC,2BAA2B,GAAGT,MAAM,CAACK,OAAO,CAACI,2BAA2B;EAC9E,MAAMC,iBAAiB,GAAG3B,eAAe,CAACiB,MAAM,EAAEX,oCAAoC,CAAC;EACvF,MAAMsB,eAAe,GAAGjC,KAAK,CAACkC,WAAW,CAACC,MAAM,IAAI;IAClD,MAAMrB,UAAU,GAAGF,sBAAsB,CAACU,MAAM,CAACK,OAAO,CAACS,KAAK,CAAC;IAC/D,MAAMC,aAAa,GAAG7B,oBAAoB,CAACc,MAAM,CAAC;IAClD,MAAMgB,cAAc,GAAGlC,oCAAoC,CAACkB,MAAM,CAAC;IACnE,MAAMiB,cAAc,GAAGJ,MAAM,CAACK,QAAQ,IAAI,IAAI;IAC9C,IAAI,CAACD,cAAc,IAAIF,aAAa,KAAK,CAAC,IAAIC,cAAc,CAACG,MAAM,KAAK,CAAC,EAAE;MACzE,OAAO,KAAK;IACd;IACAhB,MAAM,CAACiB,KAAK,CAAC,4BAA4BP,MAAM,CAACK,QAAQ,UAAUL,MAAM,CAACQ,QAAQ,GAAG,CAAC;IACrF,IAAIC,iBAAiB,GAAG,CAAC,CAAC;IAC1B,IAAIT,MAAM,CAACQ,QAAQ,KAAKvB,SAAS,EAAE;MACjC,MAAMyB,eAAe,GAAG1C,2BAA2B,CAACmB,MAAM,CAAC;MAC3D,IAAIwB,SAAS;MACb,IAAI,OAAOX,MAAM,CAACK,QAAQ,KAAK,WAAW,EAAE;QAC1C,MAAMO,KAAK,GAAGf,iBAAiB,CAACG,MAAM,CAACK,QAAQ,CAAC,EAAEQ,EAAE;QACpD,MAAMC,eAAe,GAAG3B,MAAM,CAACK,OAAO,CAACuB,2BAA2B,CAACH,KAAK,EAAEZ,MAAM,CAACQ,QAAQ,CAAC;QAC1F,IAAIM,eAAe,IAAI,CAACA,eAAe,CAACE,gBAAgB,EAAE;UACxDL,SAAS,GAAGG,eAAe,CAACG,SAAS,CAACC,KAAK;QAC7C;MACF;MACA,IAAI,OAAOP,SAAS,KAAK,WAAW,EAAE;QACpCA,SAAS,GAAGR,cAAc,CAACH,MAAM,CAACQ,QAAQ,CAAC,CAACW,aAAa;MAC3D;MACA;MACAV,iBAAiB,CAACW,IAAI,GAAG1C,cAAc,CAAC;QACtCE,aAAa,EAAED,UAAU,CAAC0C,iBAAiB,CAACH,KAAK;QACjDrC,cAAc,EAAEyC,IAAI,CAACC,GAAG,CAAC7B,kBAAkB,CAACF,OAAO,CAACgC,UAAU,CAAC;QAC/D1C,WAAW,EAAE6B,SAAS;QACtB5B,aAAa,EAAE2B,eAAe,CAACV,MAAM,CAACQ,QAAQ;MAChD,CAAC,CAAC;IACJ;IACA,IAAIR,MAAM,CAACK,QAAQ,KAAKpB,SAAS,EAAE;MACjC,MAAMwC,QAAQ,GAAGnD,oBAAoB,CAACa,MAAM,CAACK,OAAO,CAACS,KAAK,CAAC;MAC3D,MAAMyB,IAAI,GAAGvD,gBAAgB,CAACgB,MAAM,CAAC;MACrC,MAAMwC,QAAQ,GAAGvD,oBAAoB,CAACe,MAAM,CAAC;MAC7C,MAAMyC,YAAY,GAAG,CAACxC,KAAK,CAACyC,UAAU,GAAG7B,MAAM,CAACK,QAAQ,GAAGL,MAAM,CAACK,QAAQ,GAAGqB,IAAI,GAAGC,QAAQ;MAC5F,MAAMG,kBAAkB,GAAGL,QAAQ,CAACM,SAAS,CAACH,YAAY,GAAG,CAAC,CAAC,GAAGH,QAAQ,CAACM,SAAS,CAACH,YAAY,GAAG,CAAC,CAAC,GAAGH,QAAQ,CAACM,SAAS,CAACH,YAAY,CAAC,GAAGH,QAAQ,CAACO,sBAAsB,GAAGP,QAAQ,CAACM,SAAS,CAACH,YAAY,CAAC;MAC9MnB,iBAAiB,CAACwB,GAAG,GAAGvD,cAAc,CAAC;QACrCE,aAAa,EAAED,UAAU,CAACuD,iBAAiB,CAACC,MAAM;QAClDtD,cAAc,EAAEa,kBAAkB,CAACF,OAAO,CAAC4C,SAAS;QACpDtD,WAAW,EAAEgD,kBAAkB;QAC/B/C,aAAa,EAAE0C,QAAQ,CAACM,SAAS,CAACH,YAAY;MAChD,CAAC,CAAC;IACJ;IACAnB,iBAAiB,GAAGtB,MAAM,CAACK,OAAO,CAAC6C,4BAA4B,CAAC,iBAAiB,EAAE5B,iBAAiB,EAAET,MAAM,CAAC;IAC7G,IAAI,OAAOS,iBAAiB,CAACW,IAAI,KAAKnC,SAAS,IAAI,OAAOwB,iBAAiB,CAACwB,GAAG,KAAKhD,SAAS,EAAE;MAC7FE,MAAM,CAACK,OAAO,CAAC8C,MAAM,CAAC7B,iBAAiB,CAAC;MACxC,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,EAAE,CAACnB,MAAM,EAAEH,MAAM,EAAEO,kBAAkB,EAAEN,KAAK,CAACyC,UAAU,EAAEhC,iBAAiB,CAAC,CAAC;EAC7E,MAAMyC,MAAM,GAAGzE,KAAK,CAACkC,WAAW,CAACC,MAAM,IAAI;IACzC,IAAIN,kBAAkB,CAACF,OAAO,IAAIG,6BAA6B,CAACH,OAAO,IAAIQ,MAAM,CAACoB,IAAI,KAAKnC,SAAS,IAAIM,MAAM,CAACC,OAAO,EAAE;MACtH,MAAM+C,SAAS,GAAGlD,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAChCE,MAAM,CAACC,OAAO,CAACgC,UAAU,GAAGxB,MAAM,CAACoB,IAAI;MACvC1B,kBAAkB,CAACF,OAAO,CAACgC,UAAU,GAAGe,SAAS,GAAGvC,MAAM,CAACoB,IAAI;MAC/DzB,6BAA6B,CAACH,OAAO,CAACgC,UAAU,GAAGe,SAAS,GAAGvC,MAAM,CAACoB,IAAI;MAC1E9B,MAAM,CAACiB,KAAK,CAAC,mBAAmBP,MAAM,CAACoB,IAAI,EAAE,CAAC;IAChD;IACA,IAAI1B,kBAAkB,CAACF,OAAO,IAAII,2BAA2B,CAACJ,OAAO,IAAIQ,MAAM,CAACiC,GAAG,KAAKhD,SAAS,EAAE;MACjGS,kBAAkB,CAACF,OAAO,CAAC4C,SAAS,GAAGpC,MAAM,CAACiC,GAAG;MACjDrC,2BAA2B,CAACJ,OAAO,CAAC4C,SAAS,GAAGpC,MAAM,CAACiC,GAAG;MAC1D3C,MAAM,CAACiB,KAAK,CAAC,kBAAkBP,MAAM,CAACiC,GAAG,EAAE,CAAC;IAC9C;IACA3C,MAAM,CAACiB,KAAK,CAAC,6CAA6C,CAAC;EAC7D,CAAC,EAAE,CAACb,kBAAkB,EAAEC,6BAA6B,EAAEC,2BAA2B,EAAEP,KAAK,EAAEE,MAAM,EAAED,MAAM,CAAC,CAAC;EAC3G,MAAMkD,iBAAiB,GAAG3E,KAAK,CAACkC,WAAW,CAAC,MAAM;IAChD,IAAI,CAACL,kBAAkB,EAAEF,OAAO,EAAE;MAChC,OAAO;QACLyC,GAAG,EAAE,CAAC;QACNb,IAAI,EAAE;MACR,CAAC;IACH;IACA,OAAO;MACLa,GAAG,EAAEvC,kBAAkB,CAACF,OAAO,CAAC4C,SAAS;MACzChB,IAAI,EAAE1B,kBAAkB,CAACF,OAAO,CAACgC;IACnC,CAAC;EACH,CAAC,EAAE,CAAC9B,kBAAkB,CAAC,CAAC;EACxB,MAAM+C,SAAS,GAAG;IAChBH,MAAM;IACNxC,eAAe;IACf0C;EACF,CAAC;EACDjE,gBAAgB,CAACY,MAAM,EAAEsD,SAAS,EAAE,QAAQ,CAAC;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}