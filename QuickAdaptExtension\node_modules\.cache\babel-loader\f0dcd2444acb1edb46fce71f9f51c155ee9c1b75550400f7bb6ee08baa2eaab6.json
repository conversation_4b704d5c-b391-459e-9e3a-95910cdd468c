{"ast": null, "code": "import * as React from 'react';\n\n/** Gathers props for the root element into a single `.forwardedProps` field */\nfunction groupForwardedProps(props) {\n  const keys = Object.keys(props);\n  if (!keys.some(key => key.startsWith('aria-') || key.startsWith('data-'))) {\n    return props;\n  }\n  const newProps = {};\n  const forwardedProps = props.forwardedProps ?? {};\n  for (let i = 0; i < keys.length; i += 1) {\n    const key = keys[i];\n    if (key.startsWith('aria-') || key.startsWith('data-')) {\n      forwardedProps[key] = props[key];\n    } else {\n      newProps[key] = props[key];\n    }\n  }\n  newProps.forwardedProps = forwardedProps;\n  return newProps;\n}\nexport function useProps(allProps) {\n  return React.useMemo(() => groupForwardedProps(allProps), [allProps]);\n}", "map": {"version": 3, "names": ["React", "groupForwardedProps", "props", "keys", "Object", "some", "key", "startsWith", "newProps", "forwardedProps", "i", "length", "useProps", "allProps", "useMemo"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/internals/utils/useProps.js"], "sourcesContent": ["import * as React from 'react';\n\n/** Gathers props for the root element into a single `.forwardedProps` field */\nfunction groupForwardedProps(props) {\n  const keys = Object.keys(props);\n  if (!keys.some(key => key.startsWith('aria-') || key.startsWith('data-'))) {\n    return props;\n  }\n  const newProps = {};\n  const forwardedProps = props.forwardedProps ?? {};\n  for (let i = 0; i < keys.length; i += 1) {\n    const key = keys[i];\n    if (key.startsWith('aria-') || key.startsWith('data-')) {\n      forwardedProps[key] = props[key];\n    } else {\n      newProps[key] = props[key];\n    }\n  }\n  newProps.forwardedProps = forwardedProps;\n  return newProps;\n}\nexport function useProps(allProps) {\n  return React.useMemo(() => groupForwardedProps(allProps), [allProps]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,KAAK,CAAC;EAC/B,IAAI,CAACC,IAAI,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IAAID,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE;IACzE,OAAOL,KAAK;EACd;EACA,MAAMM,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAMC,cAAc,GAAGP,KAAK,CAACO,cAAc,IAAI,CAAC,CAAC;EACjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,CAACQ,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACvC,MAAMJ,GAAG,GAAGH,IAAI,CAACO,CAAC,CAAC;IACnB,IAAIJ,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IAAID,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;MACtDE,cAAc,CAACH,GAAG,CAAC,GAAGJ,KAAK,CAACI,GAAG,CAAC;IAClC,CAAC,MAAM;MACLE,QAAQ,CAACF,GAAG,CAAC,GAAGJ,KAAK,CAACI,GAAG,CAAC;IAC5B;EACF;EACAE,QAAQ,CAACC,cAAc,GAAGA,cAAc;EACxC,OAAOD,QAAQ;AACjB;AACA,OAAO,SAASI,QAAQA,CAACC,QAAQ,EAAE;EACjC,OAAOb,KAAK,CAACc,OAAO,CAAC,MAAMb,mBAAmB,CAACY,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}