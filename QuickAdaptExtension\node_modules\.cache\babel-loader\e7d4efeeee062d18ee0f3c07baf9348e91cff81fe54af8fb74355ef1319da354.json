{"ast": null, "code": "import { gridColumnDefinitionsSelector, gridVisibleColumnDefinitionsSelector } from \"../columns/index.js\";\nimport { gridFilteredSortedRowIdsSelector } from \"../filter/index.js\";\nimport { gridPinnedRowsSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nexport const getColumnsToExport = _ref => {\n  let {\n    apiRef,\n    options\n  } = _ref;\n  const columns = gridColumnDefinitionsSelector(apiRef);\n  if (options.fields) {\n    return options.fields.reduce((currentColumns, field) => {\n      const column = columns.find(col => col.field === field);\n      if (column) {\n        currentColumns.push(column);\n      }\n      return currentColumns;\n    }, []);\n  }\n  const validColumns = options.allColumns ? columns : gridVisibleColumnDefinitionsSelector(apiRef);\n  return validColumns.filter(column => !column.disableExport);\n};\nexport const defaultGetRowsToExport = _ref2 => {\n  let {\n    apiRef\n  } = _ref2;\n  const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(apiRef);\n  const rowTree = gridRowTreeSelector(apiRef);\n  const selectedRows = apiRef.current.getSelectedRows();\n  const bodyRows = filteredSortedRowIds.filter(id => rowTree[id].type !== 'footer');\n  const pinnedRows = gridPinnedRowsSelector(apiRef);\n  const topPinnedRowsIds = pinnedRows?.top?.map(row => row.id) || [];\n  const bottomPinnedRowsIds = pinnedRows?.bottom?.map(row => row.id) || [];\n  bodyRows.unshift(...topPinnedRowsIds);\n  bodyRows.push(...bottomPinnedRowsIds);\n  if (selectedRows.size > 0) {\n    return bodyRows.filter(id => selectedRows.has(id));\n  }\n  return bodyRows;\n};", "map": {"version": 3, "names": ["gridColumnDefinitionsSelector", "gridVisibleColumnDefinitionsSelector", "gridFilteredSortedRowIdsSelector", "gridPinnedRowsSelector", "gridRowTreeSelector", "getColumnsToExport", "_ref", "apiRef", "options", "columns", "fields", "reduce", "currentColumns", "field", "column", "find", "col", "push", "validColumns", "allColumns", "filter", "disableExport", "defaultGetRowsToExport", "_ref2", "filteredSortedRowIds", "rowTree", "selectedRows", "current", "getSelectedRows", "bodyRows", "id", "type", "pinnedRows", "topPinnedRowsIds", "top", "map", "row", "bottomPinnedRowsIds", "bottom", "unshift", "size", "has"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/export/utils.js"], "sourcesContent": ["import { gridColumnDefinitionsSelector, gridVisibleColumnDefinitionsSelector } from \"../columns/index.js\";\nimport { gridFilteredSortedRowIdsSelector } from \"../filter/index.js\";\nimport { gridPinnedRowsSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nexport const getColumnsToExport = ({\n  apiRef,\n  options\n}) => {\n  const columns = gridColumnDefinitionsSelector(apiRef);\n  if (options.fields) {\n    return options.fields.reduce((currentColumns, field) => {\n      const column = columns.find(col => col.field === field);\n      if (column) {\n        currentColumns.push(column);\n      }\n      return currentColumns;\n    }, []);\n  }\n  const validColumns = options.allColumns ? columns : gridVisibleColumnDefinitionsSelector(apiRef);\n  return validColumns.filter(column => !column.disableExport);\n};\nexport const defaultGetRowsToExport = ({\n  apiRef\n}) => {\n  const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(apiRef);\n  const rowTree = gridRowTreeSelector(apiRef);\n  const selectedRows = apiRef.current.getSelectedRows();\n  const bodyRows = filteredSortedRowIds.filter(id => rowTree[id].type !== 'footer');\n  const pinnedRows = gridPinnedRowsSelector(apiRef);\n  const topPinnedRowsIds = pinnedRows?.top?.map(row => row.id) || [];\n  const bottomPinnedRowsIds = pinnedRows?.bottom?.map(row => row.id) || [];\n  bodyRows.unshift(...topPinnedRowsIds);\n  bodyRows.push(...bottomPinnedRowsIds);\n  if (selectedRows.size > 0) {\n    return bodyRows.filter(id => selectedRows.has(id));\n  }\n  return bodyRows;\n};"], "mappings": "AAAA,SAASA,6BAA6B,EAAEC,oCAAoC,QAAQ,qBAAqB;AACzG,SAASC,gCAAgC,QAAQ,oBAAoB;AACrE,SAASC,sBAAsB,EAAEC,mBAAmB,QAAQ,6BAA6B;AACzF,OAAO,MAAMC,kBAAkB,GAAGC,IAAA,IAG5B;EAAA,IAH6B;IACjCC,MAAM;IACNC;EACF,CAAC,GAAAF,IAAA;EACC,MAAMG,OAAO,GAAGT,6BAA6B,CAACO,MAAM,CAAC;EACrD,IAAIC,OAAO,CAACE,MAAM,EAAE;IAClB,OAAOF,OAAO,CAACE,MAAM,CAACC,MAAM,CAAC,CAACC,cAAc,EAAEC,KAAK,KAAK;MACtD,MAAMC,MAAM,GAAGL,OAAO,CAACM,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,KAAK,KAAKA,KAAK,CAAC;MACvD,IAAIC,MAAM,EAAE;QACVF,cAAc,CAACK,IAAI,CAACH,MAAM,CAAC;MAC7B;MACA,OAAOF,cAAc;IACvB,CAAC,EAAE,EAAE,CAAC;EACR;EACA,MAAMM,YAAY,GAAGV,OAAO,CAACW,UAAU,GAAGV,OAAO,GAAGR,oCAAoC,CAACM,MAAM,CAAC;EAChG,OAAOW,YAAY,CAACE,MAAM,CAACN,MAAM,IAAI,CAACA,MAAM,CAACO,aAAa,CAAC;AAC7D,CAAC;AACD,OAAO,MAAMC,sBAAsB,GAAGC,KAAA,IAEhC;EAAA,IAFiC;IACrChB;EACF,CAAC,GAAAgB,KAAA;EACC,MAAMC,oBAAoB,GAAGtB,gCAAgC,CAACK,MAAM,CAAC;EACrE,MAAMkB,OAAO,GAAGrB,mBAAmB,CAACG,MAAM,CAAC;EAC3C,MAAMmB,YAAY,GAAGnB,MAAM,CAACoB,OAAO,CAACC,eAAe,CAAC,CAAC;EACrD,MAAMC,QAAQ,GAAGL,oBAAoB,CAACJ,MAAM,CAACU,EAAE,IAAIL,OAAO,CAACK,EAAE,CAAC,CAACC,IAAI,KAAK,QAAQ,CAAC;EACjF,MAAMC,UAAU,GAAG7B,sBAAsB,CAACI,MAAM,CAAC;EACjD,MAAM0B,gBAAgB,GAAGD,UAAU,EAAEE,GAAG,EAAEC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACN,EAAE,CAAC,IAAI,EAAE;EAClE,MAAMO,mBAAmB,GAAGL,UAAU,EAAEM,MAAM,EAAEH,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACN,EAAE,CAAC,IAAI,EAAE;EACxED,QAAQ,CAACU,OAAO,CAAC,GAAGN,gBAAgB,CAAC;EACrCJ,QAAQ,CAACZ,IAAI,CAAC,GAAGoB,mBAAmB,CAAC;EACrC,IAAIX,YAAY,CAACc,IAAI,GAAG,CAAC,EAAE;IACzB,OAAOX,QAAQ,CAACT,MAAM,CAACU,EAAE,IAAIJ,YAAY,CAACe,GAAG,CAACX,EAAE,CAAC,CAAC;EACpD;EACA,OAAOD,QAAQ;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}