{"ast": null, "code": "import _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\"],\n  _excluded2 = [\"id\"];\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { GridEditModes, GridRowModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridColumnFieldsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridRowsDataRowIdToIdLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridRowEditStopReasons, GridRowEditStartReasons } from \"../../../models/params/gridRowParams.js\";\nimport { GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridRowEditing = (apiRef, props) => {\n  const [rowModesModel, setRowModesModel] = React.useState({});\n  const rowModesModelRef = React.useRef(rowModesModel);\n  const prevRowModesModel = React.useRef({});\n  const focusTimeout = React.useRef();\n  const nextFocusedCell = React.useRef(null);\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    rowModesModel: rowModesModelProp,\n    onRowModesModelChange\n  } = props;\n  const runIfEditModeIsRow = callback => function () {\n    if (props.editMode === GridEditModes.Row) {\n      callback(...arguments);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, mode) => {\n    if (apiRef.current.getRowMode(id) !== mode) {\n      throw new Error(`MUI X: The row with id=${id} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const hasFieldsWithErrors = React.useCallback(rowId => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    return Object.values(editingState[rowId]).some(fieldProps => fieldProps.error);\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.Edit) {\n      return;\n    }\n    const rowParams = apiRef.current.getRowParams(params.id);\n    const newParams = _extends({}, rowParams, {\n      field: params.field,\n      reason: GridRowEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('rowEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusIn = React.useCallback(params => {\n    nextFocusedCell.current = params;\n  }, []);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n      return;\n    }\n    // The mechanism to detect if we can stop editing a row is different from\n    // the cell editing. Instead of triggering it when clicking outside a cell,\n    // we must check if another cell in the same row was not clicked. To achieve\n    // that, first we keep track of all cells that gained focus. When a cell loses\n    // focus we check if the next cell that received focus is from a different row.\n    nextFocusedCell.current = null;\n    focusTimeout.current = setTimeout(() => {\n      if (nextFocusedCell.current?.id !== params.id) {\n        // The row might have been deleted during the click\n        if (!apiRef.current.getRow(params.id)) {\n          return;\n        }\n\n        // The row may already changed its mode\n        if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n          return;\n        }\n        if (hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason: GridRowEditStopReasons.rowFocusOut\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    });\n  }, [apiRef, hasFieldsWithErrors]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(focusTimeout.current);\n    };\n  }, []);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridRowModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridRowEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        const columnFields = gridVisibleColumnFieldsSelector(apiRef).filter(field => {\n          const column = apiRef.current.getColumn(field);\n          if (column.type === GRID_ACTIONS_COLUMN_TYPE) {\n            return true;\n          }\n          return apiRef.current.isCellEditable(apiRef.current.getCellParams(params.id, field));\n        });\n        if (event.shiftKey) {\n          if (params.field === columnFields[0]) {\n            // Exit if user pressed Shift+Tab on the first field\n            reason = GridRowEditStopReasons.shiftTabKeyDown;\n          }\n        } else if (params.field === columnFields[columnFields.length - 1]) {\n          // Exit if user pressed Tab on the last field\n          reason = GridRowEditStopReasons.tabKeyDown;\n        }\n\n        // Always prevent going to the next element in the tab sequence because the focus is\n        // handled manually to support edit components rendered inside Portals\n        event.preventDefault();\n        if (!reason) {\n          const index = columnFields.findIndex(field => field === params.field);\n          const nextFieldToFocus = columnFields[event.shiftKey ? index - 1 : index + 1];\n          apiRef.current.setCellFocus(params.id, nextFieldToFocus);\n        }\n      }\n      if (reason) {\n        if (reason !== GridRowEditStopReasons.escapeKeyDown && hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const newParams = _extends({}, apiRef.current.getRowParams(params.id), {\n          reason,\n          field: params.field\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'row'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridRowEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason\n        });\n        apiRef.current.publishEvent('rowEditStart', newParams, event);\n      }\n    }\n  }, [apiRef, hasFieldsWithErrors]);\n  const handleRowEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startRowEditModeParams = {\n      id,\n      fieldToFocus: field\n    };\n    if (reason === GridRowEditStartReasons.printableKeyDown || reason === GridRowEditStartReasons.deleteKeyDown) {\n      startRowEditModeParams.deleteValue = !!field;\n    }\n    apiRef.current.startRowEditMode(startRowEditModeParams);\n  }, [apiRef]);\n  const handleRowEditStop = React.useCallback(params => {\n    const {\n      id,\n      reason,\n      field\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    let cellToFocusAfter;\n    if (reason === GridRowEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridRowEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridRowEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopRowEditMode({\n      id,\n      ignoreModifications,\n      field,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', runIfEditModeIsRow(handleCellDoubleClick));\n  useGridApiEventHandler(apiRef, 'cellFocusIn', runIfEditModeIsRow(handleCellFocusIn));\n  useGridApiEventHandler(apiRef, 'cellFocusOut', runIfEditModeIsRow(handleCellFocusOut));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfEditModeIsRow(handleCellKeyDown));\n  useGridApiEventHandler(apiRef, 'rowEditStart', runIfEditModeIsRow(handleRowEditStart));\n  useGridApiEventHandler(apiRef, 'rowEditStop', runIfEditModeIsRow(handleRowEditStop));\n  useGridApiOptionHandler(apiRef, 'rowEditStart', props.onRowEditStart);\n  useGridApiOptionHandler(apiRef, 'rowEditStop', props.onRowEditStop);\n  const getRowMode = React.useCallback(id => {\n    if (props.editMode === GridEditModes.Cell) {\n      return GridRowModes.View;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const isEditing = editingState[id] && Object.keys(editingState[id]).length > 0;\n    return isEditing ? GridRowModes.Edit : GridRowModes.View;\n  }, [apiRef, props.editMode]);\n  const updateRowModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.rowModesModel;\n    if (onRowModesModelChange && isNewModelDifferentFromProp) {\n      onRowModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.rowModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setRowModesModel(newModel);\n    rowModesModelRef.current = newModel;\n    apiRef.current.publishEvent('rowModesModelChange', newModel);\n  });\n  const updateRowInRowModesModel = React.useCallback((id, newProps) => {\n    const newModel = _extends({}, rowModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newProps);\n    } else {\n      delete newModel[id];\n    }\n    updateRowModesModel(newModel);\n  }, [updateRowModesModel]);\n  const updateOrDeleteRowState = React.useCallback((id, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = newProps;\n      } else {\n        delete newEditingState[id];\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const startRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotInMode(id, GridRowModes.View);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.Edit\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStartRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      fieldToFocus,\n      deleteValue,\n      initialValue\n    } = params;\n    const columnFields = gridColumnFieldsSelector(apiRef);\n    const newProps = columnFields.reduce((acc, field) => {\n      const cellParams = apiRef.current.getCellParams(id, field);\n      if (!cellParams.isEditable) {\n        return acc;\n      }\n      let newValue = apiRef.current.getCellValue(id, field);\n      if (fieldToFocus === field && (deleteValue || initialValue)) {\n        if (deleteValue) {\n          newValue = getDefaultCellValue(apiRef.current.getColumn(field));\n        } else if (initialValue) {\n          newValue = initialValue;\n        }\n      }\n      acc[field] = {\n        value: newValue,\n        error: false,\n        isProcessingProps: false\n      };\n      return acc;\n    }, {});\n    updateOrDeleteRowState(id, newProps);\n    if (fieldToFocus) {\n      apiRef.current.setCellFocus(id, fieldToFocus);\n    }\n  });\n  const stopRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, GridRowModes.Edit);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.View\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStopRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      ignoreModifications,\n      field: focusedField,\n      cellToFocusAfter = 'none'\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    const finishRowEditMode = () => {\n      if (cellToFocusAfter !== 'none' && focusedField) {\n        apiRef.current.moveFocusToRelativeCell(id, focusedField, cellToFocusAfter);\n      }\n      updateOrDeleteRowState(id, null);\n      updateRowInRowModesModel(id, null);\n    };\n    if (ignoreModifications) {\n      finishRowEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    const isSomeFieldProcessingProps = Object.values(editingState[id]).some(fieldProps => fieldProps.isProcessingProps);\n    if (isSomeFieldProcessingProps) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      return;\n    }\n    if (hasFieldsWithErrors(id)) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      // Revert the mode in the rowModesModel prop back to \"edit\"\n      updateRowInRowModesModel(id, {\n        mode: GridRowModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n    if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevRowModesModel.current[id].mode = GridRowModes.Edit;\n        // Revert the mode in the rowModesModel prop back to \"edit\"\n        updateRowInRowModesModel(id, {\n          mode: GridRowModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/#server-side-persistence.'], 'error');\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishRowEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishRowEditMode();\n    }\n  });\n  const setRowEditingEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef.current.state);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (!column.preProcessEditCellProps) {\n      updateOrDeleteFieldState(id, field, newProps);\n    }\n    return new Promise(resolve => {\n      const promises = [];\n      if (column.preProcessEditCellProps) {\n        const hasChanged = newProps.value !== editingState[id][field].value;\n        newProps = _extends({}, newProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, field, newProps);\n        const _editingState$id = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id, [field].map(_toPropertyKey));\n        const promise = Promise.resolve(column.preProcessEditCellProps({\n          id,\n          row,\n          props: newProps,\n          hasChanged,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          editingState = gridEditRowsStateSelector(apiRef.current.state);\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          // We don't reuse the value from the props pre-processing because when the\n          // promise resolves it may be already outdated. The only exception to this rule\n          // is when there's no pre-processing.\n          processedProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n          updateOrDeleteFieldState(id, field, processedProps);\n        });\n        promises.push(promise);\n      }\n      Object.entries(editingState[id]).forEach(_ref => {\n        let [thisField, fieldProps] = _ref;\n        if (thisField === field) {\n          return;\n        }\n        const fieldColumn = apiRef.current.getColumn(thisField);\n        if (!fieldColumn.preProcessEditCellProps) {\n          return;\n        }\n        fieldProps = _extends({}, fieldProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, thisField, fieldProps);\n        editingState = gridEditRowsStateSelector(apiRef.current.state);\n        const _editingState$id2 = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id2, [thisField].map(_toPropertyKey));\n        const promise = Promise.resolve(fieldColumn.preProcessEditCellProps({\n          id,\n          row,\n          props: fieldProps,\n          hasChanged: false,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          updateOrDeleteFieldState(id, thisField, processedProps);\n        });\n        promises.push(promise);\n      });\n      Promise.all(promises).then(() => {\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          editingState = gridEditRowsStateSelector(apiRef.current.state);\n          resolve(!editingState[id][field].error);\n        } else {\n          resolve(false);\n        }\n      });\n    });\n  }, [apiRef, throwIfNotEditable, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromRowEditing = React.useCallback(id => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id]) {\n      return apiRef.current.getRow(id);\n    }\n    let rowUpdate = _extends({}, row);\n    Object.entries(editingState[id]).forEach(_ref2 => {\n      let [field, fieldProps] = _ref2;\n      const column = apiRef.current.getColumn(field);\n      if (column.valueSetter) {\n        rowUpdate = column.valueSetter(fieldProps.value, rowUpdate, column, apiRef);\n      } else {\n        rowUpdate[field] = fieldProps.value;\n      }\n    });\n    return rowUpdate;\n  }, [apiRef]);\n  const editingApi = {\n    getRowMode,\n    startRowEditMode,\n    stopRowEditMode\n  };\n  const editingPrivateApi = {\n    setRowEditingEditCellValue,\n    getRowWithUpdatedValuesFromRowEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (rowModesModelProp) {\n      updateRowModesModel(rowModesModelProp);\n    }\n  }, [rowModesModelProp, updateRowModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const idToIdLookup = gridRowsDataRowIdToIdLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopRowEditMode may change it later\n    const copyOfPrevRowModesModel = prevRowModesModel.current;\n    prevRowModesModel.current = deepClone(rowModesModel); // Do a deep-clone because the attributes might be changed later\n\n    const ids = new Set([...Object.keys(rowModesModel), ...Object.keys(copyOfPrevRowModesModel)]);\n    Array.from(ids).forEach(id => {\n      const params = rowModesModel[id] ?? {\n        mode: GridRowModes.View\n      };\n      const prevMode = copyOfPrevRowModesModel[id]?.mode || GridRowModes.View;\n      const originalId = idToIdLookup[id] ?? id;\n      if (params.mode === GridRowModes.Edit && prevMode === GridRowModes.View) {\n        updateStateToStartRowEditMode(_extends({\n          id: originalId\n        }, params));\n      } else if (params.mode === GridRowModes.View && prevMode === GridRowModes.Edit) {\n        updateStateToStopRowEditMode(_extends({\n          id: originalId\n        }, params));\n      }\n    });\n  }, [apiRef, rowModesModel, updateStateToStartRowEditMode, updateStateToStopRowEditMode]);\n};", "map": {"version": 3, "names": ["_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "unstable_useEventCallback", "useEventCallback", "unstable_useEnhancedEffect", "useEnhancedEffect", "warnOnce", "useGridApiEventHandler", "useGridApiOptionHandler", "GridEditModes", "GridRowModes", "useGridApiMethod", "gridEditRowsStateSelector", "isPrintableKey", "isPasteShortcut", "gridColumnFieldsSelector", "gridVisibleColumnFieldsSelector", "gridRowsDataRowIdToIdLookupSelector", "deepClone", "GridRowEditStopReasons", "GridRowEditStartReasons", "GRID_ACTIONS_COLUMN_TYPE", "getDefaultCellValue", "useGridRowEditing", "apiRef", "props", "rowModesModel", "setRowModesModel", "useState", "rowModesModelRef", "useRef", "prevRowModesModel", "focusTimeout", "nextFocusedCell", "processRowUpdate", "onProcessRowUpdateError", "rowModesModelProp", "onRowModesModelChange", "runIfEditModeIsRow", "callback", "editMode", "Row", "arguments", "throwIfNotEditable", "useCallback", "id", "field", "params", "current", "getCellParams", "isCellEditable", "Error", "throwIfNotInMode", "mode", "getRowMode", "hasFieldsWithErrors", "rowId", "editingState", "state", "Object", "values", "some", "fieldProps", "error", "handleCellDoubleClick", "event", "isEditable", "Edit", "rowParams", "getRowParams", "newParams", "reason", "cellDoubleClick", "publishEvent", "handleCellFocusIn", "handleCellFocusOut", "View", "setTimeout", "getRow", "rowFocusOut", "useEffect", "clearTimeout", "handleCellKeyDown", "cellMode", "which", "key", "escapeKeyDown", "enterKeyDown", "columnFields", "filter", "column", "getColumn", "type", "shift<PERSON>ey", "shiftTabKeyDown", "length", "tabKeyDown", "preventDefault", "index", "findIndex", "nextFieldToFocus", "setCellFocus", "canStartEditing", "unstable_applyPipeProcessors", "cellParams", "printableKeyDown", "deleteKeyDown", "handleRowEditStart", "startRowEditModeParams", "fieldToFocus", "deleteValue", "startRowEditMode", "handleRowEditStop", "runPendingEditCellValueMutation", "cellToFocusAfter", "ignoreModifications", "stopRowEditMode", "onRowEditStart", "onRowEditStop", "Cell", "isEditing", "keys", "updateRowModesModel", "newModel", "isNewModelDifferentFromProp", "api", "updateRowInRowModesModel", "newProps", "updateOrDeleteRowState", "setState", "newEditingState", "editRows", "forceUpdate", "updateOrDeleteFieldState", "other", "updateStateToStartRowEditMode", "initialValue", "reduce", "acc", "newValue", "getCellValue", "value", "isProcessingProps", "updateStateToStopRowEditMode", "focusedField", "finishRowEditMode", "moveFocusToRelativeCell", "row", "isSomeFieldProcessingProps", "rowUpdate", "getRowWithUpdatedValuesFromRowEditing", "handleError", "errorThrown", "process", "env", "NODE_ENV", "Promise", "resolve", "then", "finalRowUpdate", "updateRows", "catch", "setRowEditingEditCellValue", "debounceMs", "unstable_skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsedValue", "valueParser", "changeReason", "preProcessEditCellProps", "promises", "has<PERSON><PERSON>ed", "_editingState$id", "otherFieldsProps", "map", "promise", "processedProps", "push", "entries", "for<PERSON>ach", "_ref", "thisField", "fieldColumn", "_editingState$id2", "all", "_ref2", "valueSetter", "editingApi", "editingPrivateApi", "idToIdLookup", "copyOfPrevRowModesModel", "ids", "Set", "Array", "from", "prevMode", "originalId"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/editing/useGridRowEditing.js"], "sourcesContent": ["import _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\"],\n  _excluded2 = [\"id\"];\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { GridEditModes, GridRowModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridColumnFieldsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridRowsDataRowIdToIdLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridRowEditStopReasons, GridRowEditStartReasons } from \"../../../models/params/gridRowParams.js\";\nimport { GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridRowEditing = (apiRef, props) => {\n  const [rowModesModel, setRowModesModel] = React.useState({});\n  const rowModesModelRef = React.useRef(rowModesModel);\n  const prevRowModesModel = React.useRef({});\n  const focusTimeout = React.useRef();\n  const nextFocusedCell = React.useRef(null);\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    rowModesModel: rowModesModelProp,\n    onRowModesModelChange\n  } = props;\n  const runIfEditModeIsRow = callback => (...args) => {\n    if (props.editMode === GridEditModes.Row) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, mode) => {\n    if (apiRef.current.getRowMode(id) !== mode) {\n      throw new Error(`MUI X: The row with id=${id} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const hasFieldsWithErrors = React.useCallback(rowId => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    return Object.values(editingState[rowId]).some(fieldProps => fieldProps.error);\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.Edit) {\n      return;\n    }\n    const rowParams = apiRef.current.getRowParams(params.id);\n    const newParams = _extends({}, rowParams, {\n      field: params.field,\n      reason: GridRowEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('rowEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusIn = React.useCallback(params => {\n    nextFocusedCell.current = params;\n  }, []);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n      return;\n    }\n    // The mechanism to detect if we can stop editing a row is different from\n    // the cell editing. Instead of triggering it when clicking outside a cell,\n    // we must check if another cell in the same row was not clicked. To achieve\n    // that, first we keep track of all cells that gained focus. When a cell loses\n    // focus we check if the next cell that received focus is from a different row.\n    nextFocusedCell.current = null;\n    focusTimeout.current = setTimeout(() => {\n      if (nextFocusedCell.current?.id !== params.id) {\n        // The row might have been deleted during the click\n        if (!apiRef.current.getRow(params.id)) {\n          return;\n        }\n\n        // The row may already changed its mode\n        if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n          return;\n        }\n        if (hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason: GridRowEditStopReasons.rowFocusOut\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    });\n  }, [apiRef, hasFieldsWithErrors]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(focusTimeout.current);\n    };\n  }, []);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridRowModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridRowEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        const columnFields = gridVisibleColumnFieldsSelector(apiRef).filter(field => {\n          const column = apiRef.current.getColumn(field);\n          if (column.type === GRID_ACTIONS_COLUMN_TYPE) {\n            return true;\n          }\n          return apiRef.current.isCellEditable(apiRef.current.getCellParams(params.id, field));\n        });\n        if (event.shiftKey) {\n          if (params.field === columnFields[0]) {\n            // Exit if user pressed Shift+Tab on the first field\n            reason = GridRowEditStopReasons.shiftTabKeyDown;\n          }\n        } else if (params.field === columnFields[columnFields.length - 1]) {\n          // Exit if user pressed Tab on the last field\n          reason = GridRowEditStopReasons.tabKeyDown;\n        }\n\n        // Always prevent going to the next element in the tab sequence because the focus is\n        // handled manually to support edit components rendered inside Portals\n        event.preventDefault();\n        if (!reason) {\n          const index = columnFields.findIndex(field => field === params.field);\n          const nextFieldToFocus = columnFields[event.shiftKey ? index - 1 : index + 1];\n          apiRef.current.setCellFocus(params.id, nextFieldToFocus);\n        }\n      }\n      if (reason) {\n        if (reason !== GridRowEditStopReasons.escapeKeyDown && hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const newParams = _extends({}, apiRef.current.getRowParams(params.id), {\n          reason,\n          field: params.field\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'row'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridRowEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason\n        });\n        apiRef.current.publishEvent('rowEditStart', newParams, event);\n      }\n    }\n  }, [apiRef, hasFieldsWithErrors]);\n  const handleRowEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startRowEditModeParams = {\n      id,\n      fieldToFocus: field\n    };\n    if (reason === GridRowEditStartReasons.printableKeyDown || reason === GridRowEditStartReasons.deleteKeyDown) {\n      startRowEditModeParams.deleteValue = !!field;\n    }\n    apiRef.current.startRowEditMode(startRowEditModeParams);\n  }, [apiRef]);\n  const handleRowEditStop = React.useCallback(params => {\n    const {\n      id,\n      reason,\n      field\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    let cellToFocusAfter;\n    if (reason === GridRowEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridRowEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridRowEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopRowEditMode({\n      id,\n      ignoreModifications,\n      field,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', runIfEditModeIsRow(handleCellDoubleClick));\n  useGridApiEventHandler(apiRef, 'cellFocusIn', runIfEditModeIsRow(handleCellFocusIn));\n  useGridApiEventHandler(apiRef, 'cellFocusOut', runIfEditModeIsRow(handleCellFocusOut));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfEditModeIsRow(handleCellKeyDown));\n  useGridApiEventHandler(apiRef, 'rowEditStart', runIfEditModeIsRow(handleRowEditStart));\n  useGridApiEventHandler(apiRef, 'rowEditStop', runIfEditModeIsRow(handleRowEditStop));\n  useGridApiOptionHandler(apiRef, 'rowEditStart', props.onRowEditStart);\n  useGridApiOptionHandler(apiRef, 'rowEditStop', props.onRowEditStop);\n  const getRowMode = React.useCallback(id => {\n    if (props.editMode === GridEditModes.Cell) {\n      return GridRowModes.View;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const isEditing = editingState[id] && Object.keys(editingState[id]).length > 0;\n    return isEditing ? GridRowModes.Edit : GridRowModes.View;\n  }, [apiRef, props.editMode]);\n  const updateRowModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.rowModesModel;\n    if (onRowModesModelChange && isNewModelDifferentFromProp) {\n      onRowModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.rowModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setRowModesModel(newModel);\n    rowModesModelRef.current = newModel;\n    apiRef.current.publishEvent('rowModesModelChange', newModel);\n  });\n  const updateRowInRowModesModel = React.useCallback((id, newProps) => {\n    const newModel = _extends({}, rowModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newProps);\n    } else {\n      delete newModel[id];\n    }\n    updateRowModesModel(newModel);\n  }, [updateRowModesModel]);\n  const updateOrDeleteRowState = React.useCallback((id, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = newProps;\n      } else {\n        delete newEditingState[id];\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const startRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotInMode(id, GridRowModes.View);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.Edit\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStartRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      fieldToFocus,\n      deleteValue,\n      initialValue\n    } = params;\n    const columnFields = gridColumnFieldsSelector(apiRef);\n    const newProps = columnFields.reduce((acc, field) => {\n      const cellParams = apiRef.current.getCellParams(id, field);\n      if (!cellParams.isEditable) {\n        return acc;\n      }\n      let newValue = apiRef.current.getCellValue(id, field);\n      if (fieldToFocus === field && (deleteValue || initialValue)) {\n        if (deleteValue) {\n          newValue = getDefaultCellValue(apiRef.current.getColumn(field));\n        } else if (initialValue) {\n          newValue = initialValue;\n        }\n      }\n      acc[field] = {\n        value: newValue,\n        error: false,\n        isProcessingProps: false\n      };\n      return acc;\n    }, {});\n    updateOrDeleteRowState(id, newProps);\n    if (fieldToFocus) {\n      apiRef.current.setCellFocus(id, fieldToFocus);\n    }\n  });\n  const stopRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, GridRowModes.Edit);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.View\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStopRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      ignoreModifications,\n      field: focusedField,\n      cellToFocusAfter = 'none'\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    const finishRowEditMode = () => {\n      if (cellToFocusAfter !== 'none' && focusedField) {\n        apiRef.current.moveFocusToRelativeCell(id, focusedField, cellToFocusAfter);\n      }\n      updateOrDeleteRowState(id, null);\n      updateRowInRowModesModel(id, null);\n    };\n    if (ignoreModifications) {\n      finishRowEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    const isSomeFieldProcessingProps = Object.values(editingState[id]).some(fieldProps => fieldProps.isProcessingProps);\n    if (isSomeFieldProcessingProps) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      return;\n    }\n    if (hasFieldsWithErrors(id)) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      // Revert the mode in the rowModesModel prop back to \"edit\"\n      updateRowInRowModesModel(id, {\n        mode: GridRowModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n    if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevRowModesModel.current[id].mode = GridRowModes.Edit;\n        // Revert the mode in the rowModesModel prop back to \"edit\"\n        updateRowInRowModesModel(id, {\n          mode: GridRowModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/#server-side-persistence.'], 'error');\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishRowEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishRowEditMode();\n    }\n  });\n  const setRowEditingEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef.current.state);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (!column.preProcessEditCellProps) {\n      updateOrDeleteFieldState(id, field, newProps);\n    }\n    return new Promise(resolve => {\n      const promises = [];\n      if (column.preProcessEditCellProps) {\n        const hasChanged = newProps.value !== editingState[id][field].value;\n        newProps = _extends({}, newProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, field, newProps);\n        const _editingState$id = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id, [field].map(_toPropertyKey));\n        const promise = Promise.resolve(column.preProcessEditCellProps({\n          id,\n          row,\n          props: newProps,\n          hasChanged,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          editingState = gridEditRowsStateSelector(apiRef.current.state);\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          // We don't reuse the value from the props pre-processing because when the\n          // promise resolves it may be already outdated. The only exception to this rule\n          // is when there's no pre-processing.\n          processedProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n          updateOrDeleteFieldState(id, field, processedProps);\n        });\n        promises.push(promise);\n      }\n      Object.entries(editingState[id]).forEach(([thisField, fieldProps]) => {\n        if (thisField === field) {\n          return;\n        }\n        const fieldColumn = apiRef.current.getColumn(thisField);\n        if (!fieldColumn.preProcessEditCellProps) {\n          return;\n        }\n        fieldProps = _extends({}, fieldProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, thisField, fieldProps);\n        editingState = gridEditRowsStateSelector(apiRef.current.state);\n        const _editingState$id2 = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id2, [thisField].map(_toPropertyKey));\n        const promise = Promise.resolve(fieldColumn.preProcessEditCellProps({\n          id,\n          row,\n          props: fieldProps,\n          hasChanged: false,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          updateOrDeleteFieldState(id, thisField, processedProps);\n        });\n        promises.push(promise);\n      });\n      Promise.all(promises).then(() => {\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          editingState = gridEditRowsStateSelector(apiRef.current.state);\n          resolve(!editingState[id][field].error);\n        } else {\n          resolve(false);\n        }\n      });\n    });\n  }, [apiRef, throwIfNotEditable, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromRowEditing = React.useCallback(id => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id]) {\n      return apiRef.current.getRow(id);\n    }\n    let rowUpdate = _extends({}, row);\n    Object.entries(editingState[id]).forEach(([field, fieldProps]) => {\n      const column = apiRef.current.getColumn(field);\n      if (column.valueSetter) {\n        rowUpdate = column.valueSetter(fieldProps.value, rowUpdate, column, apiRef);\n      } else {\n        rowUpdate[field] = fieldProps.value;\n      }\n    });\n    return rowUpdate;\n  }, [apiRef]);\n  const editingApi = {\n    getRowMode,\n    startRowEditMode,\n    stopRowEditMode\n  };\n  const editingPrivateApi = {\n    setRowEditingEditCellValue,\n    getRowWithUpdatedValuesFromRowEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (rowModesModelProp) {\n      updateRowModesModel(rowModesModelProp);\n    }\n  }, [rowModesModelProp, updateRowModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const idToIdLookup = gridRowsDataRowIdToIdLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopRowEditMode may change it later\n    const copyOfPrevRowModesModel = prevRowModesModel.current;\n    prevRowModesModel.current = deepClone(rowModesModel); // Do a deep-clone because the attributes might be changed later\n\n    const ids = new Set([...Object.keys(rowModesModel), ...Object.keys(copyOfPrevRowModesModel)]);\n    Array.from(ids).forEach(id => {\n      const params = rowModesModel[id] ?? {\n        mode: GridRowModes.View\n      };\n      const prevMode = copyOfPrevRowModesModel[id]?.mode || GridRowModes.View;\n      const originalId = idToIdLookup[id] ?? id;\n      if (params.mode === GridRowModes.Edit && prevMode === GridRowModes.View) {\n        updateStateToStartRowEditMode(_extends({\n          id: originalId\n        }, params));\n      } else if (params.mode === GridRowModes.View && prevMode === GridRowModes.Edit) {\n        updateStateToStopRowEditMode(_extends({\n          id: originalId\n        }, params));\n      }\n    });\n  }, [apiRef, rowModesModel, updateStateToStartRowEditMode, updateStateToStopRowEditMode]);\n};"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,IAAI,CAAC;EACtBC,UAAU,GAAG,CAAC,IAAI,CAAC;AACrB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,IAAIC,gBAAgB,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC3H,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,uCAAuC;AACvG,SAASC,aAAa,EAAEC,YAAY,QAAQ,qCAAqC;AACjF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,yBAAyB,QAAQ,2BAA2B;AACrE,SAASC,cAAc,EAAEC,eAAe,QAAQ,iCAAiC;AACjF,SAASC,wBAAwB,EAAEC,+BAA+B,QAAQ,mCAAmC;AAC7G,SAASC,mCAAmC,QAAQ,6BAA6B;AACjF,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,yCAAyC;AACzG,SAASC,wBAAwB,QAAQ,0BAA0B;AACnE,SAASC,mBAAmB,QAAQ,YAAY;AAChD,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAClD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,KAAK,CAAC2B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMC,gBAAgB,GAAG5B,KAAK,CAAC6B,MAAM,CAACJ,aAAa,CAAC;EACpD,MAAMK,iBAAiB,GAAG9B,KAAK,CAAC6B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAME,YAAY,GAAG/B,KAAK,CAAC6B,MAAM,CAAC,CAAC;EACnC,MAAMG,eAAe,GAAGhC,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAM;IACJI,gBAAgB;IAChBC,uBAAuB;IACvBT,aAAa,EAAEU,iBAAiB;IAChCC;EACF,CAAC,GAAGZ,KAAK;EACT,MAAMa,kBAAkB,GAAGC,QAAQ,IAAI,YAAa;IAClD,IAAId,KAAK,CAACe,QAAQ,KAAK/B,aAAa,CAACgC,GAAG,EAAE;MACxCF,QAAQ,CAAC,GAAAG,SAAO,CAAC;IACnB;EACF,CAAC;EACD,MAAMC,kBAAkB,GAAG1C,KAAK,CAAC2C,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC1D,MAAMC,MAAM,GAAGvB,MAAM,CAACwB,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC;IACtD,IAAI,CAACtB,MAAM,CAACwB,OAAO,CAACE,cAAc,CAACH,MAAM,CAAC,EAAE;MAC1C,MAAM,IAAII,KAAK,CAAC,2BAA2BN,EAAE,cAAcC,KAAK,mBAAmB,CAAC;IACtF;EACF,CAAC,EAAE,CAACtB,MAAM,CAAC,CAAC;EACZ,MAAM4B,gBAAgB,GAAGnD,KAAK,CAAC2C,WAAW,CAAC,CAACC,EAAE,EAAEQ,IAAI,KAAK;IACvD,IAAI7B,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKQ,IAAI,EAAE;MAC1C,MAAM,IAAIF,KAAK,CAAC,0BAA0BN,EAAE,cAAcQ,IAAI,QAAQ,CAAC;IACzE;EACF,CAAC,EAAE,CAAC7B,MAAM,CAAC,CAAC;EACZ,MAAM+B,mBAAmB,GAAGtD,KAAK,CAAC2C,WAAW,CAACY,KAAK,IAAI;IACrD,MAAMC,YAAY,GAAG7C,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACU,KAAK,CAAC;IACpE,OAAOC,MAAM,CAACC,MAAM,CAACH,YAAY,CAACD,KAAK,CAAC,CAAC,CAACK,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACC,KAAK,CAAC;EAChF,CAAC,EAAE,CAACvC,MAAM,CAAC,CAAC;EACZ,MAAMwC,qBAAqB,GAAG/D,KAAK,CAAC2C,WAAW,CAAC,CAACG,MAAM,EAAEkB,KAAK,KAAK;IACjE,IAAI,CAAClB,MAAM,CAACmB,UAAU,EAAE;MACtB;IACF;IACA,IAAI1C,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACP,MAAM,CAACF,EAAE,CAAC,KAAKnC,YAAY,CAACyD,IAAI,EAAE;MAC9D;IACF;IACA,MAAMC,SAAS,GAAG5C,MAAM,CAACwB,OAAO,CAACqB,YAAY,CAACtB,MAAM,CAACF,EAAE,CAAC;IACxD,MAAMyB,SAAS,GAAGxE,QAAQ,CAAC,CAAC,CAAC,EAAEsE,SAAS,EAAE;MACxCtB,KAAK,EAAEC,MAAM,CAACD,KAAK;MACnByB,MAAM,EAAEnD,uBAAuB,CAACoD;IAClC,CAAC,CAAC;IACFhD,MAAM,CAACwB,OAAO,CAACyB,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEL,KAAK,CAAC;EAC/D,CAAC,EAAE,CAACzC,MAAM,CAAC,CAAC;EACZ,MAAMkD,iBAAiB,GAAGzE,KAAK,CAAC2C,WAAW,CAACG,MAAM,IAAI;IACpDd,eAAe,CAACe,OAAO,GAAGD,MAAM;EAClC,CAAC,EAAE,EAAE,CAAC;EACN,MAAM4B,kBAAkB,GAAG1E,KAAK,CAAC2C,WAAW,CAAC,CAACG,MAAM,EAAEkB,KAAK,KAAK;IAC9D,IAAI,CAAClB,MAAM,CAACmB,UAAU,EAAE;MACtB;IACF;IACA,IAAI1C,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACP,MAAM,CAACF,EAAE,CAAC,KAAKnC,YAAY,CAACkE,IAAI,EAAE;MAC9D;IACF;IACA;IACA;IACA;IACA;IACA;IACA3C,eAAe,CAACe,OAAO,GAAG,IAAI;IAC9BhB,YAAY,CAACgB,OAAO,GAAG6B,UAAU,CAAC,MAAM;MACtC,IAAI5C,eAAe,CAACe,OAAO,EAAEH,EAAE,KAAKE,MAAM,CAACF,EAAE,EAAE;QAC7C;QACA,IAAI,CAACrB,MAAM,CAACwB,OAAO,CAAC8B,MAAM,CAAC/B,MAAM,CAACF,EAAE,CAAC,EAAE;UACrC;QACF;;QAEA;QACA,IAAIrB,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACP,MAAM,CAACF,EAAE,CAAC,KAAKnC,YAAY,CAACkE,IAAI,EAAE;UAC9D;QACF;QACA,IAAIrB,mBAAmB,CAACR,MAAM,CAACF,EAAE,CAAC,EAAE;UAClC;QACF;QACA,MAAMuB,SAAS,GAAG5C,MAAM,CAACwB,OAAO,CAACqB,YAAY,CAACtB,MAAM,CAACF,EAAE,CAAC;QACxD,MAAMyB,SAAS,GAAGxE,QAAQ,CAAC,CAAC,CAAC,EAAEsE,SAAS,EAAE;UACxCtB,KAAK,EAAEC,MAAM,CAACD,KAAK;UACnByB,MAAM,EAAEpD,sBAAsB,CAAC4D;QACjC,CAAC,CAAC;QACFvD,MAAM,CAACwB,OAAO,CAACyB,YAAY,CAAC,aAAa,EAAEH,SAAS,EAAEL,KAAK,CAAC;MAC9D;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzC,MAAM,EAAE+B,mBAAmB,CAAC,CAAC;EACjCtD,KAAK,CAAC+E,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXC,YAAY,CAACjD,YAAY,CAACgB,OAAO,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAMkC,iBAAiB,GAAGjF,KAAK,CAAC2C,WAAW,CAAC,CAACG,MAAM,EAAEkB,KAAK,KAAK;IAC7D,IAAIlB,MAAM,CAACoC,QAAQ,KAAKzE,YAAY,CAACyD,IAAI,EAAE;MACzC;MACA;MACA,IAAIF,KAAK,CAACmB,KAAK,KAAK,GAAG,EAAE;QACvB;MACF;MACA,IAAIb,MAAM;MACV,IAAIN,KAAK,CAACoB,GAAG,KAAK,QAAQ,EAAE;QAC1Bd,MAAM,GAAGpD,sBAAsB,CAACmE,aAAa;MAC/C,CAAC,MAAM,IAAIrB,KAAK,CAACoB,GAAG,KAAK,OAAO,EAAE;QAChCd,MAAM,GAAGpD,sBAAsB,CAACoE,YAAY;MAC9C,CAAC,MAAM,IAAItB,KAAK,CAACoB,GAAG,KAAK,KAAK,EAAE;QAC9B,MAAMG,YAAY,GAAGxE,+BAA+B,CAACQ,MAAM,CAAC,CAACiE,MAAM,CAAC3C,KAAK,IAAI;UAC3E,MAAM4C,MAAM,GAAGlE,MAAM,CAACwB,OAAO,CAAC2C,SAAS,CAAC7C,KAAK,CAAC;UAC9C,IAAI4C,MAAM,CAACE,IAAI,KAAKvE,wBAAwB,EAAE;YAC5C,OAAO,IAAI;UACb;UACA,OAAOG,MAAM,CAACwB,OAAO,CAACE,cAAc,CAAC1B,MAAM,CAACwB,OAAO,CAACC,aAAa,CAACF,MAAM,CAACF,EAAE,EAAEC,KAAK,CAAC,CAAC;QACtF,CAAC,CAAC;QACF,IAAImB,KAAK,CAAC4B,QAAQ,EAAE;UAClB,IAAI9C,MAAM,CAACD,KAAK,KAAK0C,YAAY,CAAC,CAAC,CAAC,EAAE;YACpC;YACAjB,MAAM,GAAGpD,sBAAsB,CAAC2E,eAAe;UACjD;QACF,CAAC,MAAM,IAAI/C,MAAM,CAACD,KAAK,KAAK0C,YAAY,CAACA,YAAY,CAACO,MAAM,GAAG,CAAC,CAAC,EAAE;UACjE;UACAxB,MAAM,GAAGpD,sBAAsB,CAAC6E,UAAU;QAC5C;;QAEA;QACA;QACA/B,KAAK,CAACgC,cAAc,CAAC,CAAC;QACtB,IAAI,CAAC1B,MAAM,EAAE;UACX,MAAM2B,KAAK,GAAGV,YAAY,CAACW,SAAS,CAACrD,KAAK,IAAIA,KAAK,KAAKC,MAAM,CAACD,KAAK,CAAC;UACrE,MAAMsD,gBAAgB,GAAGZ,YAAY,CAACvB,KAAK,CAAC4B,QAAQ,GAAGK,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC;UAC7E1E,MAAM,CAACwB,OAAO,CAACqD,YAAY,CAACtD,MAAM,CAACF,EAAE,EAAEuD,gBAAgB,CAAC;QAC1D;MACF;MACA,IAAI7B,MAAM,EAAE;QACV,IAAIA,MAAM,KAAKpD,sBAAsB,CAACmE,aAAa,IAAI/B,mBAAmB,CAACR,MAAM,CAACF,EAAE,CAAC,EAAE;UACrF;QACF;QACA,MAAMyB,SAAS,GAAGxE,QAAQ,CAAC,CAAC,CAAC,EAAE0B,MAAM,CAACwB,OAAO,CAACqB,YAAY,CAACtB,MAAM,CAACF,EAAE,CAAC,EAAE;UACrE0B,MAAM;UACNzB,KAAK,EAAEC,MAAM,CAACD;QAChB,CAAC,CAAC;QACFtB,MAAM,CAACwB,OAAO,CAACyB,YAAY,CAAC,aAAa,EAAEH,SAAS,EAAEL,KAAK,CAAC;MAC9D;IACF,CAAC,MAAM,IAAIlB,MAAM,CAACmB,UAAU,EAAE;MAC5B,IAAIK,MAAM;MACV,MAAM+B,eAAe,GAAG9E,MAAM,CAACwB,OAAO,CAACuD,4BAA4B,CAAC,iBAAiB,EAAE,IAAI,EAAE;QAC3FtC,KAAK;QACLuC,UAAU,EAAEzD,MAAM;QAClBP,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAAC8D,eAAe,EAAE;QACpB;MACF;MACA,IAAIzF,cAAc,CAACoD,KAAK,CAAC,EAAE;QACzBM,MAAM,GAAGnD,uBAAuB,CAACqF,gBAAgB;MACnD,CAAC,MAAM,IAAI3F,eAAe,CAACmD,KAAK,CAAC,EAAE;QACjCM,MAAM,GAAGnD,uBAAuB,CAACqF,gBAAgB;MACnD,CAAC,MAAM,IAAIxC,KAAK,CAACoB,GAAG,KAAK,OAAO,EAAE;QAChCd,MAAM,GAAGnD,uBAAuB,CAACmE,YAAY;MAC/C,CAAC,MAAM,IAAItB,KAAK,CAACoB,GAAG,KAAK,WAAW,IAAIpB,KAAK,CAACoB,GAAG,KAAK,QAAQ,EAAE;QAC9Dd,MAAM,GAAGnD,uBAAuB,CAACsF,aAAa;MAChD;MACA,IAAInC,MAAM,EAAE;QACV,MAAMH,SAAS,GAAG5C,MAAM,CAACwB,OAAO,CAACqB,YAAY,CAACtB,MAAM,CAACF,EAAE,CAAC;QACxD,MAAMyB,SAAS,GAAGxE,QAAQ,CAAC,CAAC,CAAC,EAAEsE,SAAS,EAAE;UACxCtB,KAAK,EAAEC,MAAM,CAACD,KAAK;UACnByB;QACF,CAAC,CAAC;QACF/C,MAAM,CAACwB,OAAO,CAACyB,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEL,KAAK,CAAC;MAC/D;IACF;EACF,CAAC,EAAE,CAACzC,MAAM,EAAE+B,mBAAmB,CAAC,CAAC;EACjC,MAAMoD,kBAAkB,GAAG1G,KAAK,CAAC2C,WAAW,CAACG,MAAM,IAAI;IACrD,MAAM;MACJF,EAAE;MACFC,KAAK;MACLyB;IACF,CAAC,GAAGxB,MAAM;IACV,MAAM6D,sBAAsB,GAAG;MAC7B/D,EAAE;MACFgE,YAAY,EAAE/D;IAChB,CAAC;IACD,IAAIyB,MAAM,KAAKnD,uBAAuB,CAACqF,gBAAgB,IAAIlC,MAAM,KAAKnD,uBAAuB,CAACsF,aAAa,EAAE;MAC3GE,sBAAsB,CAACE,WAAW,GAAG,CAAC,CAAChE,KAAK;IAC9C;IACAtB,MAAM,CAACwB,OAAO,CAAC+D,gBAAgB,CAACH,sBAAsB,CAAC;EACzD,CAAC,EAAE,CAACpF,MAAM,CAAC,CAAC;EACZ,MAAMwF,iBAAiB,GAAG/G,KAAK,CAAC2C,WAAW,CAACG,MAAM,IAAI;IACpD,MAAM;MACJF,EAAE;MACF0B,MAAM;MACNzB;IACF,CAAC,GAAGC,MAAM;IACVvB,MAAM,CAACwB,OAAO,CAACiE,+BAA+B,CAACpE,EAAE,CAAC;IAClD,IAAIqE,gBAAgB;IACpB,IAAI3C,MAAM,KAAKpD,sBAAsB,CAACoE,YAAY,EAAE;MAClD2B,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI3C,MAAM,KAAKpD,sBAAsB,CAAC6E,UAAU,EAAE;MACvDkB,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI3C,MAAM,KAAKpD,sBAAsB,CAAC2E,eAAe,EAAE;MAC5DoB,gBAAgB,GAAG,MAAM;IAC3B;IACA,MAAMC,mBAAmB,GAAG5C,MAAM,KAAK,eAAe;IACtD/C,MAAM,CAACwB,OAAO,CAACoE,eAAe,CAAC;MAC7BvE,EAAE;MACFsE,mBAAmB;MACnBrE,KAAK;MACLoE;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1F,MAAM,CAAC,CAAC;EACZjB,sBAAsB,CAACiB,MAAM,EAAE,iBAAiB,EAAEc,kBAAkB,CAAC0B,qBAAqB,CAAC,CAAC;EAC5FzD,sBAAsB,CAACiB,MAAM,EAAE,aAAa,EAAEc,kBAAkB,CAACoC,iBAAiB,CAAC,CAAC;EACpFnE,sBAAsB,CAACiB,MAAM,EAAE,cAAc,EAAEc,kBAAkB,CAACqC,kBAAkB,CAAC,CAAC;EACtFpE,sBAAsB,CAACiB,MAAM,EAAE,aAAa,EAAEc,kBAAkB,CAAC4C,iBAAiB,CAAC,CAAC;EACpF3E,sBAAsB,CAACiB,MAAM,EAAE,cAAc,EAAEc,kBAAkB,CAACqE,kBAAkB,CAAC,CAAC;EACtFpG,sBAAsB,CAACiB,MAAM,EAAE,aAAa,EAAEc,kBAAkB,CAAC0E,iBAAiB,CAAC,CAAC;EACpFxG,uBAAuB,CAACgB,MAAM,EAAE,cAAc,EAAEC,KAAK,CAAC4F,cAAc,CAAC;EACrE7G,uBAAuB,CAACgB,MAAM,EAAE,aAAa,EAAEC,KAAK,CAAC6F,aAAa,CAAC;EACnE,MAAMhE,UAAU,GAAGrD,KAAK,CAAC2C,WAAW,CAACC,EAAE,IAAI;IACzC,IAAIpB,KAAK,CAACe,QAAQ,KAAK/B,aAAa,CAAC8G,IAAI,EAAE;MACzC,OAAO7G,YAAY,CAACkE,IAAI;IAC1B;IACA,MAAMnB,YAAY,GAAG7C,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACU,KAAK,CAAC;IACpE,MAAM8D,SAAS,GAAG/D,YAAY,CAACZ,EAAE,CAAC,IAAIc,MAAM,CAAC8D,IAAI,CAAChE,YAAY,CAACZ,EAAE,CAAC,CAAC,CAACkD,MAAM,GAAG,CAAC;IAC9E,OAAOyB,SAAS,GAAG9G,YAAY,CAACyD,IAAI,GAAGzD,YAAY,CAACkE,IAAI;EAC1D,CAAC,EAAE,CAACpD,MAAM,EAAEC,KAAK,CAACe,QAAQ,CAAC,CAAC;EAC5B,MAAMkF,mBAAmB,GAAGvH,gBAAgB,CAACwH,QAAQ,IAAI;IACvD,MAAMC,2BAA2B,GAAGD,QAAQ,KAAKlG,KAAK,CAACC,aAAa;IACpE,IAAIW,qBAAqB,IAAIuF,2BAA2B,EAAE;MACxDvF,qBAAqB,CAACsF,QAAQ,EAAE;QAC9BE,GAAG,EAAErG,MAAM,CAACwB;MACd,CAAC,CAAC;IACJ;IACA,IAAIvB,KAAK,CAACC,aAAa,IAAIkG,2BAA2B,EAAE;MACtD,OAAO,CAAC;IACV;IACAjG,gBAAgB,CAACgG,QAAQ,CAAC;IAC1B9F,gBAAgB,CAACmB,OAAO,GAAG2E,QAAQ;IACnCnG,MAAM,CAACwB,OAAO,CAACyB,YAAY,CAAC,qBAAqB,EAAEkD,QAAQ,CAAC;EAC9D,CAAC,CAAC;EACF,MAAMG,wBAAwB,GAAG7H,KAAK,CAAC2C,WAAW,CAAC,CAACC,EAAE,EAAEkF,QAAQ,KAAK;IACnE,MAAMJ,QAAQ,GAAG7H,QAAQ,CAAC,CAAC,CAAC,EAAE+B,gBAAgB,CAACmB,OAAO,CAAC;IACvD,IAAI+E,QAAQ,KAAK,IAAI,EAAE;MACrBJ,QAAQ,CAAC9E,EAAE,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC,EAAEiI,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL,OAAOJ,QAAQ,CAAC9E,EAAE,CAAC;IACrB;IACA6E,mBAAmB,CAACC,QAAQ,CAAC;EAC/B,CAAC,EAAE,CAACD,mBAAmB,CAAC,CAAC;EACzB,MAAMM,sBAAsB,GAAG/H,KAAK,CAAC2C,WAAW,CAAC,CAACC,EAAE,EAAEkF,QAAQ,KAAK;IACjEvG,MAAM,CAACwB,OAAO,CAACiF,QAAQ,CAACvE,KAAK,IAAI;MAC/B,MAAMwE,eAAe,GAAGpI,QAAQ,CAAC,CAAC,CAAC,EAAE4D,KAAK,CAACyE,QAAQ,CAAC;MACpD,IAAIJ,QAAQ,KAAK,IAAI,EAAE;QACrBG,eAAe,CAACrF,EAAE,CAAC,GAAGkF,QAAQ;MAChC,CAAC,MAAM;QACL,OAAOG,eAAe,CAACrF,EAAE,CAAC;MAC5B;MACA,OAAO/C,QAAQ,CAAC,CAAC,CAAC,EAAE4D,KAAK,EAAE;QACzByE,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF1G,MAAM,CAACwB,OAAO,CAACoF,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAAC5G,MAAM,CAAC,CAAC;EACZ,MAAM6G,wBAAwB,GAAGpI,KAAK,CAAC2C,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAEiF,QAAQ,KAAK;IAC1EvG,MAAM,CAACwB,OAAO,CAACiF,QAAQ,CAACvE,KAAK,IAAI;MAC/B,MAAMwE,eAAe,GAAGpI,QAAQ,CAAC,CAAC,CAAC,EAAE4D,KAAK,CAACyE,QAAQ,CAAC;MACpD,IAAIJ,QAAQ,KAAK,IAAI,EAAE;QACrBG,eAAe,CAACrF,EAAE,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC,EAAEoI,eAAe,CAACrF,EAAE,CAAC,EAAE;UACtD,CAACC,KAAK,GAAGhD,QAAQ,CAAC,CAAC,CAAC,EAAEiI,QAAQ;QAChC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAOG,eAAe,CAACrF,EAAE,CAAC,CAACC,KAAK,CAAC;QACjC,IAAIa,MAAM,CAAC8D,IAAI,CAACS,eAAe,CAACrF,EAAE,CAAC,CAAC,CAACkD,MAAM,KAAK,CAAC,EAAE;UACjD,OAAOmC,eAAe,CAACrF,EAAE,CAAC;QAC5B;MACF;MACA,OAAO/C,QAAQ,CAAC,CAAC,CAAC,EAAE4D,KAAK,EAAE;QACzByE,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF1G,MAAM,CAACwB,OAAO,CAACoF,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAAC5G,MAAM,CAAC,CAAC;EACZ,MAAMuF,gBAAgB,GAAG9G,KAAK,CAAC2C,WAAW,CAACG,MAAM,IAAI;IACnD,MAAM;QACFF;MACF,CAAC,GAAGE,MAAM;MACVuF,KAAK,GAAGzI,6BAA6B,CAACkD,MAAM,EAAEhD,SAAS,CAAC;IAC1DqD,gBAAgB,CAACP,EAAE,EAAEnC,YAAY,CAACkE,IAAI,CAAC;IACvCkD,wBAAwB,CAACjF,EAAE,EAAE/C,QAAQ,CAAC;MACpCuD,IAAI,EAAE3C,YAAY,CAACyD;IACrB,CAAC,EAAEmE,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAClF,gBAAgB,EAAE0E,wBAAwB,CAAC,CAAC;EAChD,MAAMS,6BAA6B,GAAGpI,gBAAgB,CAAC4C,MAAM,IAAI;IAC/D,MAAM;MACJF,EAAE;MACFgE,YAAY;MACZC,WAAW;MACX0B;IACF,CAAC,GAAGzF,MAAM;IACV,MAAMyC,YAAY,GAAGzE,wBAAwB,CAACS,MAAM,CAAC;IACrD,MAAMuG,QAAQ,GAAGvC,YAAY,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAE5F,KAAK,KAAK;MACnD,MAAM0D,UAAU,GAAGhF,MAAM,CAACwB,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC;MAC1D,IAAI,CAAC0D,UAAU,CAACtC,UAAU,EAAE;QAC1B,OAAOwE,GAAG;MACZ;MACA,IAAIC,QAAQ,GAAGnH,MAAM,CAACwB,OAAO,CAAC4F,YAAY,CAAC/F,EAAE,EAAEC,KAAK,CAAC;MACrD,IAAI+D,YAAY,KAAK/D,KAAK,KAAKgE,WAAW,IAAI0B,YAAY,CAAC,EAAE;QAC3D,IAAI1B,WAAW,EAAE;UACf6B,QAAQ,GAAGrH,mBAAmB,CAACE,MAAM,CAACwB,OAAO,CAAC2C,SAAS,CAAC7C,KAAK,CAAC,CAAC;QACjE,CAAC,MAAM,IAAI0F,YAAY,EAAE;UACvBG,QAAQ,GAAGH,YAAY;QACzB;MACF;MACAE,GAAG,CAAC5F,KAAK,CAAC,GAAG;QACX+F,KAAK,EAAEF,QAAQ;QACf5E,KAAK,EAAE,KAAK;QACZ+E,iBAAiB,EAAE;MACrB,CAAC;MACD,OAAOJ,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACNV,sBAAsB,CAACnF,EAAE,EAAEkF,QAAQ,CAAC;IACpC,IAAIlB,YAAY,EAAE;MAChBrF,MAAM,CAACwB,OAAO,CAACqD,YAAY,CAACxD,EAAE,EAAEgE,YAAY,CAAC;IAC/C;EACF,CAAC,CAAC;EACF,MAAMO,eAAe,GAAGnH,KAAK,CAAC2C,WAAW,CAACG,MAAM,IAAI;IAClD,MAAM;QACFF;MACF,CAAC,GAAGE,MAAM;MACVuF,KAAK,GAAGzI,6BAA6B,CAACkD,MAAM,EAAE/C,UAAU,CAAC;IAC3DoD,gBAAgB,CAACP,EAAE,EAAEnC,YAAY,CAACyD,IAAI,CAAC;IACvC2D,wBAAwB,CAACjF,EAAE,EAAE/C,QAAQ,CAAC;MACpCuD,IAAI,EAAE3C,YAAY,CAACkE;IACrB,CAAC,EAAE0D,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAClF,gBAAgB,EAAE0E,wBAAwB,CAAC,CAAC;EAChD,MAAMiB,4BAA4B,GAAG5I,gBAAgB,CAAC4C,MAAM,IAAI;IAC9D,MAAM;MACJF,EAAE;MACFsE,mBAAmB;MACnBrE,KAAK,EAAEkG,YAAY;MACnB9B,gBAAgB,GAAG;IACrB,CAAC,GAAGnE,MAAM;IACVvB,MAAM,CAACwB,OAAO,CAACiE,+BAA+B,CAACpE,EAAE,CAAC;IAClD,MAAMoG,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAI/B,gBAAgB,KAAK,MAAM,IAAI8B,YAAY,EAAE;QAC/CxH,MAAM,CAACwB,OAAO,CAACkG,uBAAuB,CAACrG,EAAE,EAAEmG,YAAY,EAAE9B,gBAAgB,CAAC;MAC5E;MACAc,sBAAsB,CAACnF,EAAE,EAAE,IAAI,CAAC;MAChCiF,wBAAwB,CAACjF,EAAE,EAAE,IAAI,CAAC;IACpC,CAAC;IACD,IAAIsE,mBAAmB,EAAE;MACvB8B,iBAAiB,CAAC,CAAC;MACnB;IACF;IACA,MAAMxF,YAAY,GAAG7C,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACU,KAAK,CAAC;IACpE,MAAMyF,GAAG,GAAG3H,MAAM,CAACwB,OAAO,CAAC8B,MAAM,CAACjC,EAAE,CAAC;IACrC,MAAMuG,0BAA0B,GAAGzF,MAAM,CAACC,MAAM,CAACH,YAAY,CAACZ,EAAE,CAAC,CAAC,CAACgB,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACgF,iBAAiB,CAAC;IACnH,IAAIM,0BAA0B,EAAE;MAC9BrH,iBAAiB,CAACiB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG3C,YAAY,CAACyD,IAAI;MACtD;IACF;IACA,IAAIZ,mBAAmB,CAACV,EAAE,CAAC,EAAE;MAC3Bd,iBAAiB,CAACiB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG3C,YAAY,CAACyD,IAAI;MACtD;MACA2D,wBAAwB,CAACjF,EAAE,EAAE;QAC3BQ,IAAI,EAAE3C,YAAY,CAACyD;MACrB,CAAC,CAAC;MACF;IACF;IACA,MAAMkF,SAAS,GAAG7H,MAAM,CAACwB,OAAO,CAACsG,qCAAqC,CAACzG,EAAE,CAAC;IAC1E,IAAIX,gBAAgB,EAAE;MACpB,MAAMqH,WAAW,GAAGC,WAAW,IAAI;QACjCzH,iBAAiB,CAACiB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG3C,YAAY,CAACyD,IAAI;QACtD;QACA2D,wBAAwB,CAACjF,EAAE,EAAE;UAC3BQ,IAAI,EAAE3C,YAAY,CAACyD;QACrB,CAAC,CAAC;QACF,IAAIhC,uBAAuB,EAAE;UAC3BA,uBAAuB,CAACqH,WAAW,CAAC;QACtC,CAAC,MAAM,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UAChDrJ,QAAQ,CAAC,CAAC,wHAAwH,EAAE,iJAAiJ,EAAE,0FAA0F,CAAC,EAAE,OAAO,CAAC;QAC9X;MACF,CAAC;MACD,IAAI;QACFsJ,OAAO,CAACC,OAAO,CAAC3H,gBAAgB,CAACmH,SAAS,EAAEF,GAAG,EAAE;UAC/C3F,KAAK,EAAEX;QACT,CAAC,CAAC,CAAC,CAACiH,IAAI,CAACC,cAAc,IAAI;UACzBvI,MAAM,CAACwB,OAAO,CAACgH,UAAU,CAAC,CAACD,cAAc,CAAC,CAAC;UAC3Cd,iBAAiB,CAAC,CAAC;QACrB,CAAC,CAAC,CAACgB,KAAK,CAACV,WAAW,CAAC;MACvB,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpBD,WAAW,CAACC,WAAW,CAAC;MAC1B;IACF,CAAC,MAAM;MACLhI,MAAM,CAACwB,OAAO,CAACgH,UAAU,CAAC,CAACX,SAAS,CAAC,CAAC;MACtCJ,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACF,MAAMiB,0BAA0B,GAAGjK,KAAK,CAAC2C,WAAW,CAACG,MAAM,IAAI;IAC7D,MAAM;MACJF,EAAE;MACFC,KAAK;MACL+F,KAAK;MACLsB,UAAU;MACVC,wBAAwB,EAAEC;IAC5B,CAAC,GAAGtH,MAAM;IACVJ,kBAAkB,CAACE,EAAE,EAAEC,KAAK,CAAC;IAC7B,MAAM4C,MAAM,GAAGlE,MAAM,CAACwB,OAAO,CAAC2C,SAAS,CAAC7C,KAAK,CAAC;IAC9C,MAAMqG,GAAG,GAAG3H,MAAM,CAACwB,OAAO,CAAC8B,MAAM,CAACjC,EAAE,CAAC;IACrC,IAAIyH,WAAW,GAAGzB,KAAK;IACvB,IAAInD,MAAM,CAAC6E,WAAW,IAAI,CAACF,eAAe,EAAE;MAC1CC,WAAW,GAAG5E,MAAM,CAAC6E,WAAW,CAAC1B,KAAK,EAAEM,GAAG,EAAEzD,MAAM,EAAElE,MAAM,CAAC;IAC9D;IACA,IAAIiC,YAAY,GAAG7C,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACU,KAAK,CAAC;IAClE,IAAIqE,QAAQ,GAAGjI,QAAQ,CAAC,CAAC,CAAC,EAAE2D,YAAY,CAACZ,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACnD+F,KAAK,EAAEyB,WAAW;MAClBE,YAAY,EAAEL,UAAU,GAAG,2BAA2B,GAAG;IAC3D,CAAC,CAAC;IACF,IAAI,CAACzE,MAAM,CAAC+E,uBAAuB,EAAE;MACnCpC,wBAAwB,CAACxF,EAAE,EAAEC,KAAK,EAAEiF,QAAQ,CAAC;IAC/C;IACA,OAAO,IAAI6B,OAAO,CAACC,OAAO,IAAI;MAC5B,MAAMa,QAAQ,GAAG,EAAE;MACnB,IAAIhF,MAAM,CAAC+E,uBAAuB,EAAE;QAClC,MAAME,UAAU,GAAG5C,QAAQ,CAACc,KAAK,KAAKpF,YAAY,CAACZ,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC+F,KAAK;QACnEd,QAAQ,GAAGjI,QAAQ,CAAC,CAAC,CAAC,EAAEiI,QAAQ,EAAE;UAChCe,iBAAiB,EAAE;QACrB,CAAC,CAAC;QACFT,wBAAwB,CAACxF,EAAE,EAAEC,KAAK,EAAEiF,QAAQ,CAAC;QAC7C,MAAM6C,gBAAgB,GAAGnH,YAAY,CAACZ,EAAE,CAAC;UACvCgI,gBAAgB,GAAGhL,6BAA6B,CAAC+K,gBAAgB,EAAE,CAAC9H,KAAK,CAAC,CAACgI,GAAG,CAAClL,cAAc,CAAC,CAAC;QACjG,MAAMmL,OAAO,GAAGnB,OAAO,CAACC,OAAO,CAACnE,MAAM,CAAC+E,uBAAuB,CAAC;UAC7D5H,EAAE;UACFsG,GAAG;UACH1H,KAAK,EAAEsG,QAAQ;UACf4C,UAAU;UACVE;QACF,CAAC,CAAC,CAAC,CAACf,IAAI,CAACkB,cAAc,IAAI;UACzB;UACA;UACA,IAAIxJ,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKnC,YAAY,CAACkE,IAAI,EAAE;YACvDiF,OAAO,CAAC,KAAK,CAAC;YACd;UACF;UACApG,YAAY,GAAG7C,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACU,KAAK,CAAC;UAC9DsH,cAAc,GAAGlL,QAAQ,CAAC,CAAC,CAAC,EAAEkL,cAAc,EAAE;YAC5ClC,iBAAiB,EAAE;UACrB,CAAC,CAAC;UACF;UACA;UACA;UACAkC,cAAc,CAACnC,KAAK,GAAGnD,MAAM,CAAC+E,uBAAuB,GAAGhH,YAAY,CAACZ,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC+F,KAAK,GAAGyB,WAAW;UACnGjC,wBAAwB,CAACxF,EAAE,EAAEC,KAAK,EAAEkI,cAAc,CAAC;QACrD,CAAC,CAAC;QACFN,QAAQ,CAACO,IAAI,CAACF,OAAO,CAAC;MACxB;MACApH,MAAM,CAACuH,OAAO,CAACzH,YAAY,CAACZ,EAAE,CAAC,CAAC,CAACsI,OAAO,CAACC,IAAA,IAA6B;QAAA,IAA5B,CAACC,SAAS,EAAEvH,UAAU,CAAC,GAAAsH,IAAA;QAC/D,IAAIC,SAAS,KAAKvI,KAAK,EAAE;UACvB;QACF;QACA,MAAMwI,WAAW,GAAG9J,MAAM,CAACwB,OAAO,CAAC2C,SAAS,CAAC0F,SAAS,CAAC;QACvD,IAAI,CAACC,WAAW,CAACb,uBAAuB,EAAE;UACxC;QACF;QACA3G,UAAU,GAAGhE,QAAQ,CAAC,CAAC,CAAC,EAAEgE,UAAU,EAAE;UACpCgF,iBAAiB,EAAE;QACrB,CAAC,CAAC;QACFT,wBAAwB,CAACxF,EAAE,EAAEwI,SAAS,EAAEvH,UAAU,CAAC;QACnDL,YAAY,GAAG7C,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACU,KAAK,CAAC;QAC9D,MAAM6H,iBAAiB,GAAG9H,YAAY,CAACZ,EAAE,CAAC;UACxCgI,gBAAgB,GAAGhL,6BAA6B,CAAC0L,iBAAiB,EAAE,CAACF,SAAS,CAAC,CAACP,GAAG,CAAClL,cAAc,CAAC,CAAC;QACtG,MAAMmL,OAAO,GAAGnB,OAAO,CAACC,OAAO,CAACyB,WAAW,CAACb,uBAAuB,CAAC;UAClE5H,EAAE;UACFsG,GAAG;UACH1H,KAAK,EAAEqC,UAAU;UACjB6G,UAAU,EAAE,KAAK;UACjBE;QACF,CAAC,CAAC,CAAC,CAACf,IAAI,CAACkB,cAAc,IAAI;UACzB;UACA;UACA,IAAIxJ,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKnC,YAAY,CAACkE,IAAI,EAAE;YACvDiF,OAAO,CAAC,KAAK,CAAC;YACd;UACF;UACAmB,cAAc,GAAGlL,QAAQ,CAAC,CAAC,CAAC,EAAEkL,cAAc,EAAE;YAC5ClC,iBAAiB,EAAE;UACrB,CAAC,CAAC;UACFT,wBAAwB,CAACxF,EAAE,EAAEwI,SAAS,EAAEL,cAAc,CAAC;QACzD,CAAC,CAAC;QACFN,QAAQ,CAACO,IAAI,CAACF,OAAO,CAAC;MACxB,CAAC,CAAC;MACFnB,OAAO,CAAC4B,GAAG,CAACd,QAAQ,CAAC,CAACZ,IAAI,CAAC,MAAM;QAC/B,IAAItI,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKnC,YAAY,CAACyD,IAAI,EAAE;UACvDV,YAAY,GAAG7C,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACU,KAAK,CAAC;UAC9DmG,OAAO,CAAC,CAACpG,YAAY,CAACZ,EAAE,CAAC,CAACC,KAAK,CAAC,CAACiB,KAAK,CAAC;QACzC,CAAC,MAAM;UACL8F,OAAO,CAAC,KAAK,CAAC;QAChB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrI,MAAM,EAAEmB,kBAAkB,EAAE0F,wBAAwB,CAAC,CAAC;EAC1D,MAAMiB,qCAAqC,GAAGrJ,KAAK,CAAC2C,WAAW,CAACC,EAAE,IAAI;IACpE,MAAMY,YAAY,GAAG7C,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACU,KAAK,CAAC;IACpE,MAAMyF,GAAG,GAAG3H,MAAM,CAACwB,OAAO,CAAC8B,MAAM,CAACjC,EAAE,CAAC;IACrC,IAAI,CAACY,YAAY,CAACZ,EAAE,CAAC,EAAE;MACrB,OAAOrB,MAAM,CAACwB,OAAO,CAAC8B,MAAM,CAACjC,EAAE,CAAC;IAClC;IACA,IAAIwG,SAAS,GAAGvJ,QAAQ,CAAC,CAAC,CAAC,EAAEqJ,GAAG,CAAC;IACjCxF,MAAM,CAACuH,OAAO,CAACzH,YAAY,CAACZ,EAAE,CAAC,CAAC,CAACsI,OAAO,CAACM,KAAA,IAAyB;MAAA,IAAxB,CAAC3I,KAAK,EAAEgB,UAAU,CAAC,GAAA2H,KAAA;MAC3D,MAAM/F,MAAM,GAAGlE,MAAM,CAACwB,OAAO,CAAC2C,SAAS,CAAC7C,KAAK,CAAC;MAC9C,IAAI4C,MAAM,CAACgG,WAAW,EAAE;QACtBrC,SAAS,GAAG3D,MAAM,CAACgG,WAAW,CAAC5H,UAAU,CAAC+E,KAAK,EAAEQ,SAAS,EAAE3D,MAAM,EAAElE,MAAM,CAAC;MAC7E,CAAC,MAAM;QACL6H,SAAS,CAACvG,KAAK,CAAC,GAAGgB,UAAU,CAAC+E,KAAK;MACrC;IACF,CAAC,CAAC;IACF,OAAOQ,SAAS;EAClB,CAAC,EAAE,CAAC7H,MAAM,CAAC,CAAC;EACZ,MAAMmK,UAAU,GAAG;IACjBrI,UAAU;IACVyD,gBAAgB;IAChBK;EACF,CAAC;EACD,MAAMwE,iBAAiB,GAAG;IACxB1B,0BAA0B;IAC1BZ;EACF,CAAC;EACD3I,gBAAgB,CAACa,MAAM,EAAEmK,UAAU,EAAE,QAAQ,CAAC;EAC9ChL,gBAAgB,CAACa,MAAM,EAAEoK,iBAAiB,EAAE,SAAS,CAAC;EACtD3L,KAAK,CAAC+E,SAAS,CAAC,MAAM;IACpB,IAAI5C,iBAAiB,EAAE;MACrBsF,mBAAmB,CAACtF,iBAAiB,CAAC;IACxC;EACF,CAAC,EAAE,CAACA,iBAAiB,EAAEsF,mBAAmB,CAAC,CAAC;;EAE5C;EACArH,iBAAiB,CAAC,MAAM;IACtB,MAAMwL,YAAY,GAAG5K,mCAAmC,CAACO,MAAM,CAAC;;IAEhE;IACA,MAAMsK,uBAAuB,GAAG/J,iBAAiB,CAACiB,OAAO;IACzDjB,iBAAiB,CAACiB,OAAO,GAAG9B,SAAS,CAACQ,aAAa,CAAC,CAAC,CAAC;;IAEtD,MAAMqK,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGrI,MAAM,CAAC8D,IAAI,CAAC/F,aAAa,CAAC,EAAE,GAAGiC,MAAM,CAAC8D,IAAI,CAACqE,uBAAuB,CAAC,CAAC,CAAC;IAC7FG,KAAK,CAACC,IAAI,CAACH,GAAG,CAAC,CAACZ,OAAO,CAACtI,EAAE,IAAI;MAC5B,MAAME,MAAM,GAAGrB,aAAa,CAACmB,EAAE,CAAC,IAAI;QAClCQ,IAAI,EAAE3C,YAAY,CAACkE;MACrB,CAAC;MACD,MAAMuH,QAAQ,GAAGL,uBAAuB,CAACjJ,EAAE,CAAC,EAAEQ,IAAI,IAAI3C,YAAY,CAACkE,IAAI;MACvE,MAAMwH,UAAU,GAAGP,YAAY,CAAChJ,EAAE,CAAC,IAAIA,EAAE;MACzC,IAAIE,MAAM,CAACM,IAAI,KAAK3C,YAAY,CAACyD,IAAI,IAAIgI,QAAQ,KAAKzL,YAAY,CAACkE,IAAI,EAAE;QACvE2D,6BAA6B,CAACzI,QAAQ,CAAC;UACrC+C,EAAE,EAAEuJ;QACN,CAAC,EAAErJ,MAAM,CAAC,CAAC;MACb,CAAC,MAAM,IAAIA,MAAM,CAACM,IAAI,KAAK3C,YAAY,CAACkE,IAAI,IAAIuH,QAAQ,KAAKzL,YAAY,CAACyD,IAAI,EAAE;QAC9E4E,4BAA4B,CAACjJ,QAAQ,CAAC;UACpC+C,EAAE,EAAEuJ;QACN,CAAC,EAAErJ,MAAM,CAAC,CAAC;MACb;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvB,MAAM,EAAEE,aAAa,EAAE6G,6BAA6B,EAAEQ,4BAA4B,CAAC,CAAC;AAC1F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}