{"ast": null, "code": "var isKeyable = require('./_isKeyable');\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key) ? data[typeof key == 'string' ? 'string' : 'hash'] : data.map;\n}\nmodule.exports = getMapData;", "map": {"version": 3, "names": ["isKeyable", "require", "getMapData", "map", "key", "data", "__data__", "module", "exports"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash/_getMapData.js"], "sourcesContent": ["var isKeyable = require('./_isKeyable');\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\nmodule.exports = getMapData;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,GAAG,EAAEC,GAAG,EAAE;EAC5B,IAAIC,IAAI,GAAGF,GAAG,CAACG,QAAQ;EACvB,OAAON,SAAS,CAACI,GAAG,CAAC,GACjBC,IAAI,CAAC,OAAOD,GAAG,IAAI,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAC,GAChDC,IAAI,CAACF,GAAG;AACd;AAEAI,MAAM,CAACC,OAAO,GAAGN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}