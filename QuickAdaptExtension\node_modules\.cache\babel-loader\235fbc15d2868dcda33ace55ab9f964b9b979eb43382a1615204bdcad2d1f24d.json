{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nexport const useGridIsRtl = apiRef => {\n  const isRtl = useRtl();\n  if (apiRef.current.state.isRtl === undefined) {\n    apiRef.current.state.isRtl = isRtl;\n  }\n  const isFirstEffect = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstEffect.current) {\n      isFirstEffect.current = false;\n    } else {\n      apiRef.current.setState(state => _extends({}, state, {\n        isRtl\n      }));\n    }\n  }, [apiRef, isRtl]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useRtl", "useGridIsRtl", "apiRef", "isRtl", "current", "state", "undefined", "isFirstEffect", "useRef", "useEffect", "setState"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/core/useGridIsRtl.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nexport const useGridIsRtl = apiRef => {\n  const isRtl = useRtl();\n  if (apiRef.current.state.isRtl === undefined) {\n    apiRef.current.state.isRtl = isRtl;\n  }\n  const isFirstEffect = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstEffect.current) {\n      isFirstEffect.current = false;\n    } else {\n      apiRef.current.setState(state => _extends({}, state, {\n        isRtl\n      }));\n    }\n  }, [apiRef, isRtl]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAO,MAAMC,YAAY,GAAGC,MAAM,IAAI;EACpC,MAAMC,KAAK,GAAGH,MAAM,CAAC,CAAC;EACtB,IAAIE,MAAM,CAACE,OAAO,CAACC,KAAK,CAACF,KAAK,KAAKG,SAAS,EAAE;IAC5CJ,MAAM,CAACE,OAAO,CAACC,KAAK,CAACF,KAAK,GAAGA,KAAK;EACpC;EACA,MAAMI,aAAa,GAAGR,KAAK,CAACS,MAAM,CAAC,IAAI,CAAC;EACxCT,KAAK,CAACU,SAAS,CAAC,MAAM;IACpB,IAAIF,aAAa,CAACH,OAAO,EAAE;MACzBG,aAAa,CAACH,OAAO,GAAG,KAAK;IAC/B,CAAC,MAAM;MACLF,MAAM,CAACE,OAAO,CAACM,QAAQ,CAACL,KAAK,IAAIP,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;QACnDF;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACD,MAAM,EAAEC,KAAK,CAAC,CAAC;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}