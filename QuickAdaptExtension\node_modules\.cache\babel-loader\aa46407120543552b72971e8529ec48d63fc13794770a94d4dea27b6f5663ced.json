{"ast": null, "code": "import * as React from 'react';\nimport { useGridApiOption<PERSON><PERSON><PERSON>, useGridNativeEventListener } from \"../../utils/index.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { serializeCellValue } from \"../export/serializers/csvSerializer.js\";\nfunction writeToClipboardPolyfill(data) {\n  const span = document.createElement('span');\n  span.style.whiteSpace = 'pre';\n  span.style.userSelect = 'all';\n  span.style.opacity = '0px';\n  span.textContent = data;\n  document.body.appendChild(span);\n  const range = document.createRange();\n  range.selectNode(span);\n  const selection = window.getSelection();\n  selection.removeAllRanges();\n  selection.addRange(range);\n  try {\n    document.execCommand('copy');\n  } finally {\n    document.body.removeChild(span);\n  }\n}\nfunction copyToClipboard(data) {\n  if (navigator.clipboard) {\n    navigator.clipboard.writeText(data).catch(() => {\n      writeToClipboardPolyfill(data);\n    });\n  } else {\n    writeToClipboardPolyfill(data);\n  }\n}\nfunction hasNativeSelection(element) {\n  // When getSelection is called on an <iframe> that is not displayed Firefox will return null.\n  if (window.getSelection()?.toString()) {\n    return true;\n  }\n\n  // window.getSelection() returns an empty string in Firefox for selections inside a form element.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=85686.\n  // Instead, we can use element.selectionStart that is only defined on form elements.\n  if (element && (element.selectionEnd || 0) - (element.selectionStart || 0) > 0) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * @requires useGridCsvExport (method)\n * @requires useGridSelection (method)\n */\nexport const useGridClipboard = (apiRef, props) => {\n  const ignoreValueFormatterProp = props.ignoreValueFormatterDuringExport;\n  const ignoreValueFormatter = (typeof ignoreValueFormatterProp === 'object' ? ignoreValueFormatterProp?.clipboardExport : ignoreValueFormatterProp) || false;\n  const clipboardCopyCellDelimiter = props.clipboardCopyCellDelimiter;\n  const handleCopy = React.useCallback(event => {\n    if (!((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'c' && !event.shiftKey && !event.altKey)) {\n      return;\n    }\n\n    // Do nothing if there's a native selection\n    if (hasNativeSelection(event.target)) {\n      return;\n    }\n    let textToCopy = '';\n    const selectedRows = apiRef.current.getSelectedRows();\n    if (selectedRows.size > 0) {\n      textToCopy = apiRef.current.getDataAsCsv({\n        includeHeaders: false,\n        delimiter: clipboardCopyCellDelimiter,\n        shouldAppendQuotes: false,\n        escapeFormulas: false\n      });\n    } else {\n      const focusedCell = gridFocusCellSelector(apiRef);\n      if (focusedCell) {\n        const cellParams = apiRef.current.getCellParams(focusedCell.id, focusedCell.field);\n        textToCopy = serializeCellValue(cellParams, {\n          csvOptions: {\n            delimiter: clipboardCopyCellDelimiter,\n            shouldAppendQuotes: false,\n            escapeFormulas: false\n          },\n          ignoreValueFormatter\n        });\n      }\n    }\n    textToCopy = apiRef.current.unstable_applyPipeProcessors('clipboardCopy', textToCopy);\n    if (textToCopy) {\n      copyToClipboard(textToCopy);\n      apiRef.current.publishEvent('clipboardCopy', textToCopy);\n    }\n  }, [apiRef, ignoreValueFormatter, clipboardCopyCellDelimiter]);\n  useGridNativeEventListener(apiRef, apiRef.current.rootElementRef, 'keydown', handleCopy);\n  useGridApiOptionHandler(apiRef, 'clipboardCopy', props.onClipboardCopy);\n};", "map": {"version": 3, "names": ["React", "useGridApiOptionHandler", "useGridNativeEventListener", "gridFocusCellSelector", "serializeCellValue", "writeToClipboardPolyfill", "data", "span", "document", "createElement", "style", "whiteSpace", "userSelect", "opacity", "textContent", "body", "append<PERSON><PERSON><PERSON>", "range", "createRange", "selectNode", "selection", "window", "getSelection", "removeAllRanges", "addRange", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "copyToClipboard", "navigator", "clipboard", "writeText", "catch", "hasNativeSelection", "element", "toString", "selectionEnd", "selectionStart", "useGridClipboard", "apiRef", "props", "ignoreValueFormatterProp", "ignoreValueFormatterDuringExport", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clipboardExport", "clipboardCopyCellDelimiter", "handleCopy", "useCallback", "event", "ctrl<PERSON>ey", "metaKey", "key", "toLowerCase", "shift<PERSON>ey", "altKey", "target", "textToCopy", "selectedRows", "current", "getSelectedRows", "size", "getDataAsCsv", "includeHeaders", "delimiter", "shouldAppendQuotes", "escapeFormulas", "focusedCell", "cellParams", "getCellParams", "id", "field", "csvOptions", "unstable_applyPipeProcessors", "publishEvent", "rootElementRef", "onClipboardCopy"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/clipboard/useGridClipboard.js"], "sourcesContent": ["import * as React from 'react';\nimport { useGridApiOption<PERSON><PERSON><PERSON>, useGridNativeEventListener } from \"../../utils/index.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { serializeCellValue } from \"../export/serializers/csvSerializer.js\";\nfunction writeToClipboardPolyfill(data) {\n  const span = document.createElement('span');\n  span.style.whiteSpace = 'pre';\n  span.style.userSelect = 'all';\n  span.style.opacity = '0px';\n  span.textContent = data;\n  document.body.appendChild(span);\n  const range = document.createRange();\n  range.selectNode(span);\n  const selection = window.getSelection();\n  selection.removeAllRanges();\n  selection.addRange(range);\n  try {\n    document.execCommand('copy');\n  } finally {\n    document.body.removeChild(span);\n  }\n}\nfunction copyToClipboard(data) {\n  if (navigator.clipboard) {\n    navigator.clipboard.writeText(data).catch(() => {\n      writeToClipboardPolyfill(data);\n    });\n  } else {\n    writeToClipboardPolyfill(data);\n  }\n}\nfunction hasNativeSelection(element) {\n  // When getSelection is called on an <iframe> that is not displayed Firefox will return null.\n  if (window.getSelection()?.toString()) {\n    return true;\n  }\n\n  // window.getSelection() returns an empty string in Firefox for selections inside a form element.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=85686.\n  // Instead, we can use element.selectionStart that is only defined on form elements.\n  if (element && (element.selectionEnd || 0) - (element.selectionStart || 0) > 0) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * @requires useGridCsvExport (method)\n * @requires useGridSelection (method)\n */\nexport const useGridClipboard = (apiRef, props) => {\n  const ignoreValueFormatterProp = props.ignoreValueFormatterDuringExport;\n  const ignoreValueFormatter = (typeof ignoreValueFormatterProp === 'object' ? ignoreValueFormatterProp?.clipboardExport : ignoreValueFormatterProp) || false;\n  const clipboardCopyCellDelimiter = props.clipboardCopyCellDelimiter;\n  const handleCopy = React.useCallback(event => {\n    if (!((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'c' && !event.shiftKey && !event.altKey)) {\n      return;\n    }\n\n    // Do nothing if there's a native selection\n    if (hasNativeSelection(event.target)) {\n      return;\n    }\n    let textToCopy = '';\n    const selectedRows = apiRef.current.getSelectedRows();\n    if (selectedRows.size > 0) {\n      textToCopy = apiRef.current.getDataAsCsv({\n        includeHeaders: false,\n        delimiter: clipboardCopyCellDelimiter,\n        shouldAppendQuotes: false,\n        escapeFormulas: false\n      });\n    } else {\n      const focusedCell = gridFocusCellSelector(apiRef);\n      if (focusedCell) {\n        const cellParams = apiRef.current.getCellParams(focusedCell.id, focusedCell.field);\n        textToCopy = serializeCellValue(cellParams, {\n          csvOptions: {\n            delimiter: clipboardCopyCellDelimiter,\n            shouldAppendQuotes: false,\n            escapeFormulas: false\n          },\n          ignoreValueFormatter\n        });\n      }\n    }\n    textToCopy = apiRef.current.unstable_applyPipeProcessors('clipboardCopy', textToCopy);\n    if (textToCopy) {\n      copyToClipboard(textToCopy);\n      apiRef.current.publishEvent('clipboardCopy', textToCopy);\n    }\n  }, [apiRef, ignoreValueFormatter, clipboardCopyCellDelimiter]);\n  useGridNativeEventListener(apiRef, apiRef.current.rootElementRef, 'keydown', handleCopy);\n  useGridApiOptionHandler(apiRef, 'clipboardCopy', props.onClipboardCopy);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,uBAAuB,EAAEC,0BAA0B,QAAQ,sBAAsB;AAC1F,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EACtC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;EAC3CF,IAAI,CAACG,KAAK,CAACC,UAAU,GAAG,KAAK;EAC7BJ,IAAI,CAACG,KAAK,CAACE,UAAU,GAAG,KAAK;EAC7BL,IAAI,CAACG,KAAK,CAACG,OAAO,GAAG,KAAK;EAC1BN,IAAI,CAACO,WAAW,GAAGR,IAAI;EACvBE,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;EAC/B,MAAMU,KAAK,GAAGT,QAAQ,CAACU,WAAW,CAAC,CAAC;EACpCD,KAAK,CAACE,UAAU,CAACZ,IAAI,CAAC;EACtB,MAAMa,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;EACvCF,SAAS,CAACG,eAAe,CAAC,CAAC;EAC3BH,SAAS,CAACI,QAAQ,CAACP,KAAK,CAAC;EACzB,IAAI;IACFT,QAAQ,CAACiB,WAAW,CAAC,MAAM,CAAC;EAC9B,CAAC,SAAS;IACRjB,QAAQ,CAACO,IAAI,CAACW,WAAW,CAACnB,IAAI,CAAC;EACjC;AACF;AACA,SAASoB,eAAeA,CAACrB,IAAI,EAAE;EAC7B,IAAIsB,SAAS,CAACC,SAAS,EAAE;IACvBD,SAAS,CAACC,SAAS,CAACC,SAAS,CAACxB,IAAI,CAAC,CAACyB,KAAK,CAAC,MAAM;MAC9C1B,wBAAwB,CAACC,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,MAAM;IACLD,wBAAwB,CAACC,IAAI,CAAC;EAChC;AACF;AACA,SAAS0B,kBAAkBA,CAACC,OAAO,EAAE;EACnC;EACA,IAAIZ,MAAM,CAACC,YAAY,CAAC,CAAC,EAAEY,QAAQ,CAAC,CAAC,EAAE;IACrC,OAAO,IAAI;EACb;;EAEA;EACA;EACA;EACA,IAAID,OAAO,IAAI,CAACA,OAAO,CAACE,YAAY,IAAI,CAAC,KAAKF,OAAO,CAACG,cAAc,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IAC9E,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACjD,MAAMC,wBAAwB,GAAGD,KAAK,CAACE,gCAAgC;EACvE,MAAMC,oBAAoB,GAAG,CAAC,OAAOF,wBAAwB,KAAK,QAAQ,GAAGA,wBAAwB,EAAEG,eAAe,GAAGH,wBAAwB,KAAK,KAAK;EAC3J,MAAMI,0BAA0B,GAAGL,KAAK,CAACK,0BAA0B;EACnE,MAAMC,UAAU,GAAG7C,KAAK,CAAC8C,WAAW,CAACC,KAAK,IAAI;IAC5C,IAAI,EAAE,CAACA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,OAAO,KAAKF,KAAK,CAACG,GAAG,CAACC,WAAW,CAAC,CAAC,KAAK,GAAG,IAAI,CAACJ,KAAK,CAACK,QAAQ,IAAI,CAACL,KAAK,CAACM,MAAM,CAAC,EAAE;MAC9G;IACF;;IAEA;IACA,IAAIrB,kBAAkB,CAACe,KAAK,CAACO,MAAM,CAAC,EAAE;MACpC;IACF;IACA,IAAIC,UAAU,GAAG,EAAE;IACnB,MAAMC,YAAY,GAAGlB,MAAM,CAACmB,OAAO,CAACC,eAAe,CAAC,CAAC;IACrD,IAAIF,YAAY,CAACG,IAAI,GAAG,CAAC,EAAE;MACzBJ,UAAU,GAAGjB,MAAM,CAACmB,OAAO,CAACG,YAAY,CAAC;QACvCC,cAAc,EAAE,KAAK;QACrBC,SAAS,EAAElB,0BAA0B;QACrCmB,kBAAkB,EAAE,KAAK;QACzBC,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMC,WAAW,GAAG9D,qBAAqB,CAACmC,MAAM,CAAC;MACjD,IAAI2B,WAAW,EAAE;QACf,MAAMC,UAAU,GAAG5B,MAAM,CAACmB,OAAO,CAACU,aAAa,CAACF,WAAW,CAACG,EAAE,EAAEH,WAAW,CAACI,KAAK,CAAC;QAClFd,UAAU,GAAGnD,kBAAkB,CAAC8D,UAAU,EAAE;UAC1CI,UAAU,EAAE;YACVR,SAAS,EAAElB,0BAA0B;YACrCmB,kBAAkB,EAAE,KAAK;YACzBC,cAAc,EAAE;UAClB,CAAC;UACDtB;QACF,CAAC,CAAC;MACJ;IACF;IACAa,UAAU,GAAGjB,MAAM,CAACmB,OAAO,CAACc,4BAA4B,CAAC,eAAe,EAAEhB,UAAU,CAAC;IACrF,IAAIA,UAAU,EAAE;MACd5B,eAAe,CAAC4B,UAAU,CAAC;MAC3BjB,MAAM,CAACmB,OAAO,CAACe,YAAY,CAAC,eAAe,EAAEjB,UAAU,CAAC;IAC1D;EACF,CAAC,EAAE,CAACjB,MAAM,EAAEI,oBAAoB,EAAEE,0BAA0B,CAAC,CAAC;EAC9D1C,0BAA0B,CAACoC,MAAM,EAAEA,MAAM,CAACmB,OAAO,CAACgB,cAAc,EAAE,SAAS,EAAE5B,UAAU,CAAC;EACxF5C,uBAAuB,CAACqC,MAAM,EAAE,eAAe,EAAEC,KAAK,CAACmC,eAAe,CAAC;AACzE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}