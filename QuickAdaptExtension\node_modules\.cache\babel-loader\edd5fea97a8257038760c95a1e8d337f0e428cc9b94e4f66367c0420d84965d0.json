{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { GridPreferencesPanel } from \"./panel/GridPreferencesPanel.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function GridHeader() {\n  const rootProps = useGridRootProps();\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GridPreferencesPanel, {}), rootProps.slots.toolbar && /*#__PURE__*/_jsx(rootProps.slots.toolbar, _extends({}, rootProps.slotProps?.toolbar))]\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useGridRootProps", "GridPreferencesPanel", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rootProps", "Fragment", "children", "slots", "toolbar", "slotProps"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/GridHeader.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { GridPreferencesPanel } from \"./panel/GridPreferencesPanel.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function GridHeader() {\n  const rootProps = useGridRootProps();\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GridPreferencesPanel, {}), rootProps.slots.toolbar && /*#__PURE__*/_jsx(rootProps.slots.toolbar, _extends({}, rootProps.slotProps?.toolbar))]\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B,MAAMC,SAAS,GAAGP,gBAAgB,CAAC,CAAC;EACpC,OAAO,aAAaK,KAAK,CAACN,KAAK,CAACS,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,aAAaN,IAAI,CAACF,oBAAoB,EAAE,CAAC,CAAC,CAAC,EAAEM,SAAS,CAACG,KAAK,CAACC,OAAO,IAAI,aAAaR,IAAI,CAACI,SAAS,CAACG,KAAK,CAACC,OAAO,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAES,SAAS,CAACK,SAAS,EAAED,OAAO,CAAC,CAAC;EAC3K,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}