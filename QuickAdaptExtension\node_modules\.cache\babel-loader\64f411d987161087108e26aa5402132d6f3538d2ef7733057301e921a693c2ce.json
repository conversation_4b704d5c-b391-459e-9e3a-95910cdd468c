{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"classes\", \"columnMenuOpen\", \"colIndex\", \"height\", \"isResizing\", \"sortDirection\", \"hasFocus\", \"tabIndex\", \"separatorSide\", \"isDraggable\", \"headerComponent\", \"description\", \"elementId\", \"width\", \"columnMenuIconButton\", \"columnMenu\", \"columnTitleIconButtons\", \"headerClassName\", \"label\", \"resizable\", \"draggableContainerProps\", \"columnHeaderSeparatorProps\", \"style\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { GridColumnHeaderTitle } from \"./GridColumnHeaderTitle.js\";\nimport { GridColumnHeaderSeparator } from \"./GridColumnHeaderSeparator.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridGenericColumnHeaderItem = /*#__PURE__*/React.forwardRef(function GridGenericColumnHeaderItem(props, ref) {\n  const {\n      classes,\n      colIndex,\n      height,\n      isResizing,\n      sortDirection,\n      hasFocus,\n      tabIndex,\n      separatorSide,\n      isDraggable,\n      headerComponent,\n      description,\n      width,\n      columnMenuIconButton = null,\n      columnMenu = null,\n      columnTitleIconButtons = null,\n      headerClassName,\n      label,\n      resizable,\n      draggableContainerProps,\n      columnHeaderSeparatorProps,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const headerCellRef = React.useRef(null);\n  const handleRef = useForkRef(headerCellRef, ref);\n  let ariaSort = 'none';\n  if (sortDirection != null) {\n    ariaSort = sortDirection === 'asc' ? 'ascending' : 'descending';\n  }\n  React.useLayoutEffect(() => {\n    const columnMenuState = apiRef.current.state.columnMenu;\n    if (hasFocus && !columnMenuState.open) {\n      const focusableElement = headerCellRef.current.querySelector('[tabindex=\"0\"]');\n      const elementToFocus = focusableElement || headerCellRef.current;\n      elementToFocus?.focus();\n      if (apiRef.current.columnHeadersContainerRef?.current) {\n        apiRef.current.columnHeadersContainerRef.current.scrollLeft = 0;\n      }\n    }\n  }, [apiRef, hasFocus]);\n  return /*#__PURE__*/_jsxs(\"div\", _extends({\n    ref: handleRef,\n    className: clsx(classes.root, headerClassName),\n    style: _extends({}, style, {\n      height,\n      width\n    }),\n    role: \"columnheader\",\n    tabIndex: tabIndex,\n    \"aria-colindex\": colIndex + 1,\n    \"aria-sort\": ariaSort\n  }, other, {\n    children: [/*#__PURE__*/_jsxs(\"div\", _extends({\n      className: classes.draggableContainer,\n      draggable: isDraggable,\n      role: \"presentation\"\n    }, draggableContainerProps, {\n      children: [/*#__PURE__*/_jsxs(\"div\", {\n        className: classes.titleContainer,\n        role: \"presentation\",\n        children: [/*#__PURE__*/_jsx(\"div\", {\n          className: classes.titleContainerContent,\n          children: headerComponent !== undefined ? headerComponent : /*#__PURE__*/_jsx(GridColumnHeaderTitle, {\n            label: label,\n            description: description,\n            columnWidth: width\n          })\n        }), columnTitleIconButtons]\n      }), columnMenuIconButton]\n    })), /*#__PURE__*/_jsx(GridColumnHeaderSeparator, _extends({\n      resizable: !rootProps.disableColumnResize && !!resizable,\n      resizing: isResizing,\n      height: height,\n      side: separatorSide\n    }, columnHeaderSeparatorProps)), columnMenu]\n  }));\n});\nexport { GridGenericColumnHeaderItem };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "unstable_useForkRef", "useForkRef", "useGridPrivateApiContext", "GridColumnHeaderTitle", "GridColumnHeaderSeparator", "useGridRootProps", "jsx", "_jsx", "jsxs", "_jsxs", "GridGenericColumnHeaderItem", "forwardRef", "props", "ref", "classes", "colIndex", "height", "isResizing", "sortDirection", "hasFocus", "tabIndex", "separatorSide", "isDraggable", "headerComponent", "description", "width", "columnMenuIconButton", "columnMenu", "columnTitleIconButtons", "headerClassName", "label", "resizable", "draggableContainerProps", "columnHeaderSeparatorProps", "style", "other", "apiRef", "rootProps", "headerCellRef", "useRef", "handleRef", "ariaSort", "useLayoutEffect", "columnMenuState", "current", "state", "open", "focusableElement", "querySelector", "elementToFocus", "focus", "columnHeadersContainerRef", "scrollLeft", "className", "root", "role", "children", "draggableContainer", "draggable", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "columnWidth", "disableColumnResize", "resizing", "side"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/columnHeaders/GridGenericColumnHeaderItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"classes\", \"columnMenuOpen\", \"colIndex\", \"height\", \"isResizing\", \"sortDirection\", \"hasFocus\", \"tabIndex\", \"separatorSide\", \"isDraggable\", \"headerComponent\", \"description\", \"elementId\", \"width\", \"columnMenuIconButton\", \"columnMenu\", \"columnTitleIconButtons\", \"headerClassName\", \"label\", \"resizable\", \"draggableContainerProps\", \"columnHeaderSeparatorProps\", \"style\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { GridColumnHeaderTitle } from \"./GridColumnHeaderTitle.js\";\nimport { GridColumnHeaderSeparator } from \"./GridColumnHeaderSeparator.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridGenericColumnHeaderItem = /*#__PURE__*/React.forwardRef(function GridGenericColumnHeaderItem(props, ref) {\n  const {\n      classes,\n      colIndex,\n      height,\n      isResizing,\n      sortDirection,\n      hasFocus,\n      tabIndex,\n      separatorSide,\n      isDraggable,\n      headerComponent,\n      description,\n      width,\n      columnMenuIconButton = null,\n      columnMenu = null,\n      columnTitleIconButtons = null,\n      headerClassName,\n      label,\n      resizable,\n      draggableContainerProps,\n      columnHeaderSeparatorProps,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const headerCellRef = React.useRef(null);\n  const handleRef = useForkRef(headerCellRef, ref);\n  let ariaSort = 'none';\n  if (sortDirection != null) {\n    ariaSort = sortDirection === 'asc' ? 'ascending' : 'descending';\n  }\n  React.useLayoutEffect(() => {\n    const columnMenuState = apiRef.current.state.columnMenu;\n    if (hasFocus && !columnMenuState.open) {\n      const focusableElement = headerCellRef.current.querySelector('[tabindex=\"0\"]');\n      const elementToFocus = focusableElement || headerCellRef.current;\n      elementToFocus?.focus();\n      if (apiRef.current.columnHeadersContainerRef?.current) {\n        apiRef.current.columnHeadersContainerRef.current.scrollLeft = 0;\n      }\n    }\n  }, [apiRef, hasFocus]);\n  return /*#__PURE__*/_jsxs(\"div\", _extends({\n    ref: handleRef,\n    className: clsx(classes.root, headerClassName),\n    style: _extends({}, style, {\n      height,\n      width\n    }),\n    role: \"columnheader\",\n    tabIndex: tabIndex,\n    \"aria-colindex\": colIndex + 1,\n    \"aria-sort\": ariaSort\n  }, other, {\n    children: [/*#__PURE__*/_jsxs(\"div\", _extends({\n      className: classes.draggableContainer,\n      draggable: isDraggable,\n      role: \"presentation\"\n    }, draggableContainerProps, {\n      children: [/*#__PURE__*/_jsxs(\"div\", {\n        className: classes.titleContainer,\n        role: \"presentation\",\n        children: [/*#__PURE__*/_jsx(\"div\", {\n          className: classes.titleContainerContent,\n          children: headerComponent !== undefined ? headerComponent : /*#__PURE__*/_jsx(GridColumnHeaderTitle, {\n            label: label,\n            description: description,\n            columnWidth: width\n          })\n        }), columnTitleIconButtons]\n      }), columnMenuIconButton]\n    })), /*#__PURE__*/_jsx(GridColumnHeaderSeparator, _extends({\n      resizable: !rootProps.disableColumnResize && !!resizable,\n      resizing: isResizing,\n      height: height,\n      side: separatorSide\n    }, columnHeaderSeparatorProps)), columnMenu]\n  }));\n});\nexport { GridGenericColumnHeaderItem };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,sBAAsB,EAAE,YAAY,EAAE,wBAAwB,EAAE,iBAAiB,EAAE,OAAO,EAAE,WAAW,EAAE,yBAAyB,EAAE,4BAA4B,EAAE,OAAO,CAAC;AAC/X,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,2BAA2B,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,SAASD,2BAA2BA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACjH,MAAM;MACFC,OAAO;MACPC,QAAQ;MACRC,MAAM;MACNC,UAAU;MACVC,aAAa;MACbC,QAAQ;MACRC,QAAQ;MACRC,aAAa;MACbC,WAAW;MACXC,eAAe;MACfC,WAAW;MACXC,KAAK;MACLC,oBAAoB,GAAG,IAAI;MAC3BC,UAAU,GAAG,IAAI;MACjBC,sBAAsB,GAAG,IAAI;MAC7BC,eAAe;MACfC,KAAK;MACLC,SAAS;MACTC,uBAAuB;MACvBC,0BAA0B;MAC1BC;IACF,CAAC,GAAGtB,KAAK;IACTuB,KAAK,GAAGvC,6BAA6B,CAACgB,KAAK,EAAEf,SAAS,CAAC;EACzD,MAAMuC,MAAM,GAAGlC,wBAAwB,CAAC,CAAC;EACzC,MAAMmC,SAAS,GAAGhC,gBAAgB,CAAC,CAAC;EACpC,MAAMiC,aAAa,GAAGxC,KAAK,CAACyC,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMC,SAAS,GAAGvC,UAAU,CAACqC,aAAa,EAAEzB,GAAG,CAAC;EAChD,IAAI4B,QAAQ,GAAG,MAAM;EACrB,IAAIvB,aAAa,IAAI,IAAI,EAAE;IACzBuB,QAAQ,GAAGvB,aAAa,KAAK,KAAK,GAAG,WAAW,GAAG,YAAY;EACjE;EACApB,KAAK,CAAC4C,eAAe,CAAC,MAAM;IAC1B,MAAMC,eAAe,GAAGP,MAAM,CAACQ,OAAO,CAACC,KAAK,CAAClB,UAAU;IACvD,IAAIR,QAAQ,IAAI,CAACwB,eAAe,CAACG,IAAI,EAAE;MACrC,MAAMC,gBAAgB,GAAGT,aAAa,CAACM,OAAO,CAACI,aAAa,CAAC,gBAAgB,CAAC;MAC9E,MAAMC,cAAc,GAAGF,gBAAgB,IAAIT,aAAa,CAACM,OAAO;MAChEK,cAAc,EAAEC,KAAK,CAAC,CAAC;MACvB,IAAId,MAAM,CAACQ,OAAO,CAACO,yBAAyB,EAAEP,OAAO,EAAE;QACrDR,MAAM,CAACQ,OAAO,CAACO,yBAAyB,CAACP,OAAO,CAACQ,UAAU,GAAG,CAAC;MACjE;IACF;EACF,CAAC,EAAE,CAAChB,MAAM,EAAEjB,QAAQ,CAAC,CAAC;EACtB,OAAO,aAAaV,KAAK,CAAC,KAAK,EAAEd,QAAQ,CAAC;IACxCkB,GAAG,EAAE2B,SAAS;IACda,SAAS,EAAEtD,IAAI,CAACe,OAAO,CAACwC,IAAI,EAAEzB,eAAe,CAAC;IAC9CK,KAAK,EAAEvC,QAAQ,CAAC,CAAC,CAAC,EAAEuC,KAAK,EAAE;MACzBlB,MAAM;MACNS;IACF,CAAC,CAAC;IACF8B,IAAI,EAAE,cAAc;IACpBnC,QAAQ,EAAEA,QAAQ;IAClB,eAAe,EAAEL,QAAQ,GAAG,CAAC;IAC7B,WAAW,EAAE0B;EACf,CAAC,EAAEN,KAAK,EAAE;IACRqB,QAAQ,EAAE,CAAC,aAAa/C,KAAK,CAAC,KAAK,EAAEd,QAAQ,CAAC;MAC5C0D,SAAS,EAAEvC,OAAO,CAAC2C,kBAAkB;MACrCC,SAAS,EAAEpC,WAAW;MACtBiC,IAAI,EAAE;IACR,CAAC,EAAEvB,uBAAuB,EAAE;MAC1BwB,QAAQ,EAAE,CAAC,aAAa/C,KAAK,CAAC,KAAK,EAAE;QACnC4C,SAAS,EAAEvC,OAAO,CAAC6C,cAAc;QACjCJ,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,CAAC,aAAajD,IAAI,CAAC,KAAK,EAAE;UAClC8C,SAAS,EAAEvC,OAAO,CAAC8C,qBAAqB;UACxCJ,QAAQ,EAAEjC,eAAe,KAAKsC,SAAS,GAAGtC,eAAe,GAAG,aAAahB,IAAI,CAACJ,qBAAqB,EAAE;YACnG2B,KAAK,EAAEA,KAAK;YACZN,WAAW,EAAEA,WAAW;YACxBsC,WAAW,EAAErC;UACf,CAAC;QACH,CAAC,CAAC,EAAEG,sBAAsB;MAC5B,CAAC,CAAC,EAAEF,oBAAoB;IAC1B,CAAC,CAAC,CAAC,EAAE,aAAanB,IAAI,CAACH,yBAAyB,EAAET,QAAQ,CAAC;MACzDoC,SAAS,EAAE,CAACM,SAAS,CAAC0B,mBAAmB,IAAI,CAAC,CAAChC,SAAS;MACxDiC,QAAQ,EAAE/C,UAAU;MACpBD,MAAM,EAAEA,MAAM;MACdiD,IAAI,EAAE5C;IACR,CAAC,EAAEY,0BAA0B,CAAC,CAAC,EAAEN,UAAU;EAC7C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,SAASjB,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}