{"ast": null, "code": "var checkboardCache = {};\nexport var render = function render(c1, c2, size, serverCanvas) {\n  if (typeof document === 'undefined' && !serverCanvas) {\n    return null;\n  }\n  var canvas = serverCanvas ? new serverCanvas() : document.createElement('canvas');\n  canvas.width = size * 2;\n  canvas.height = size * 2;\n  var ctx = canvas.getContext('2d');\n  if (!ctx) {\n    return null;\n  } // If no context can be found, return early.\n  ctx.fillStyle = c1;\n  ctx.fillRect(0, 0, canvas.width, canvas.height);\n  ctx.fillStyle = c2;\n  ctx.fillRect(0, 0, size, size);\n  ctx.translate(size, size);\n  ctx.fillRect(0, 0, size, size);\n  return canvas.toDataURL();\n};\nexport var get = function get(c1, c2, size, serverCanvas) {\n  var key = c1 + '-' + c2 + '-' + size + (serverCanvas ? '-server' : '');\n  if (checkboardCache[key]) {\n    return checkboardCache[key];\n  }\n  var checkboard = render(c1, c2, size, serverCanvas);\n  checkboardCache[key] = checkboard;\n  return checkboard;\n};", "map": {"version": 3, "names": ["checkboardCache", "render", "c1", "c2", "size", "serverCanvas", "document", "canvas", "createElement", "width", "height", "ctx", "getContext", "fillStyle", "fillRect", "translate", "toDataURL", "get", "key", "checkboard"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/react-color/es/helpers/checkboard.js"], "sourcesContent": ["var checkboardCache = {};\n\nexport var render = function render(c1, c2, size, serverCanvas) {\n  if (typeof document === 'undefined' && !serverCanvas) {\n    return null;\n  }\n  var canvas = serverCanvas ? new serverCanvas() : document.createElement('canvas');\n  canvas.width = size * 2;\n  canvas.height = size * 2;\n  var ctx = canvas.getContext('2d');\n  if (!ctx) {\n    return null;\n  } // If no context can be found, return early.\n  ctx.fillStyle = c1;\n  ctx.fillRect(0, 0, canvas.width, canvas.height);\n  ctx.fillStyle = c2;\n  ctx.fillRect(0, 0, size, size);\n  ctx.translate(size, size);\n  ctx.fillRect(0, 0, size, size);\n  return canvas.toDataURL();\n};\n\nexport var get = function get(c1, c2, size, serverCanvas) {\n  var key = c1 + '-' + c2 + '-' + size + (serverCanvas ? '-server' : '');\n\n  if (checkboardCache[key]) {\n    return checkboardCache[key];\n  }\n\n  var checkboard = render(c1, c2, size, serverCanvas);\n  checkboardCache[key] = checkboard;\n  return checkboard;\n};"], "mappings": "AAAA,IAAIA,eAAe,GAAG,CAAC,CAAC;AAExB,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAEC,IAAI,EAAEC,YAAY,EAAE;EAC9D,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACD,YAAY,EAAE;IACpD,OAAO,IAAI;EACb;EACA,IAAIE,MAAM,GAAGF,YAAY,GAAG,IAAIA,YAAY,CAAC,CAAC,GAAGC,QAAQ,CAACE,aAAa,CAAC,QAAQ,CAAC;EACjFD,MAAM,CAACE,KAAK,GAAGL,IAAI,GAAG,CAAC;EACvBG,MAAM,CAACG,MAAM,GAAGN,IAAI,GAAG,CAAC;EACxB,IAAIO,GAAG,GAAGJ,MAAM,CAACK,UAAU,CAAC,IAAI,CAAC;EACjC,IAAI,CAACD,GAAG,EAAE;IACR,OAAO,IAAI;EACb,CAAC,CAAC;EACFA,GAAG,CAACE,SAAS,GAAGX,EAAE;EAClBS,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEP,MAAM,CAACE,KAAK,EAAEF,MAAM,CAACG,MAAM,CAAC;EAC/CC,GAAG,CAACE,SAAS,GAAGV,EAAE;EAClBQ,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEV,IAAI,EAAEA,IAAI,CAAC;EAC9BO,GAAG,CAACI,SAAS,CAACX,IAAI,EAAEA,IAAI,CAAC;EACzBO,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEV,IAAI,EAAEA,IAAI,CAAC;EAC9B,OAAOG,MAAM,CAACS,SAAS,CAAC,CAAC;AAC3B,CAAC;AAED,OAAO,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAACf,EAAE,EAAEC,EAAE,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACxD,IAAIa,GAAG,GAAGhB,EAAE,GAAG,GAAG,GAAGC,EAAE,GAAG,GAAG,GAAGC,IAAI,IAAIC,YAAY,GAAG,SAAS,GAAG,EAAE,CAAC;EAEtE,IAAIL,eAAe,CAACkB,GAAG,CAAC,EAAE;IACxB,OAAOlB,eAAe,CAACkB,GAAG,CAAC;EAC7B;EAEA,IAAIC,UAAU,GAAGlB,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,IAAI,EAAEC,YAAY,CAAC;EACnDL,eAAe,CAACkB,GAAG,CAAC,GAAGC,UAAU;EACjC,OAAOA,UAAU;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}