{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/index.js\";\nimport { buildRootGroup, GRID_ROOT_GROUP_ID } from \"./gridRowsUtils.js\";\nconst createFlatRowTree = rows => {\n  const tree = {\n    [GRID_ROOT_GROUP_ID]: _extends({}, buildRootGroup(), {\n      children: rows\n    })\n  };\n  for (let i = 0; i < rows.length; i += 1) {\n    const rowId = rows[i];\n    tree[rowId] = {\n      id: rowId,\n      depth: 0,\n      parent: GRID_ROOT_GROUP_ID,\n      type: 'leaf',\n      groupingKey: null\n    };\n  }\n  return {\n    groupingName: GRID_DEFAULT_STRATEGY,\n    tree,\n    treeDepths: {\n      0: rows.length\n    },\n    dataRowIds: rows\n  };\n};\nconst updateFlatRowTree = _ref => {\n  let {\n    previousTree,\n    actions\n  } = _ref;\n  const tree = _extends({}, previousTree);\n  const idsToRemoveFromRootGroup = {};\n  for (let i = 0; i < actions.remove.length; i += 1) {\n    const idToDelete = actions.remove[i];\n    idsToRemoveFromRootGroup[idToDelete] = true;\n    delete tree[idToDelete];\n  }\n  for (let i = 0; i < actions.insert.length; i += 1) {\n    const idToInsert = actions.insert[i];\n    tree[idToInsert] = {\n      id: idToInsert,\n      depth: 0,\n      parent: GRID_ROOT_GROUP_ID,\n      type: 'leaf',\n      groupingKey: null\n    };\n  }\n\n  // TODO rows v6: Support row unpinning\n\n  const rootGroup = tree[GRID_ROOT_GROUP_ID];\n  let rootGroupChildren = [...rootGroup.children, ...actions.insert];\n  if (Object.values(idsToRemoveFromRootGroup).length) {\n    rootGroupChildren = rootGroupChildren.filter(id => !idsToRemoveFromRootGroup[id]);\n  }\n  tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n    children: rootGroupChildren\n  });\n  return {\n    groupingName: GRID_DEFAULT_STRATEGY,\n    tree,\n    treeDepths: {\n      0: rootGroupChildren.length\n    },\n    dataRowIds: rootGroupChildren\n  };\n};\nconst flatRowTreeCreationMethod = params => {\n  if (params.updates.type === 'full') {\n    return createFlatRowTree(params.updates.rows);\n  }\n  return updateFlatRowTree({\n    previousTree: params.previousTree,\n    actions: params.updates.actions\n  });\n};\nexport const useGridRowsPreProcessors = apiRef => {\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'rowTreeCreation', flatRowTreeCreationMethod);\n};", "map": {"version": 3, "names": ["_extends", "GRID_DEFAULT_STRATEGY", "useGridRegisterStrategyProcessor", "buildRootGroup", "GRID_ROOT_GROUP_ID", "createFlatRowTree", "rows", "tree", "children", "i", "length", "rowId", "id", "depth", "parent", "type", "grouping<PERSON>ey", "groupingName", "treeDepths", "dataRowIds", "updateFlatRowTree", "_ref", "previousTree", "actions", "idsToRemoveFromRootGroup", "remove", "idToDelete", "insert", "idToInsert", "rootGroup", "rootGroupChildren", "Object", "values", "filter", "flatRowTreeCreationMethod", "params", "updates", "useGridRowsPreProcessors", "apiRef"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/rows/useGridRowsPreProcessors.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/index.js\";\nimport { buildRootGroup, GRID_ROOT_GROUP_ID } from \"./gridRowsUtils.js\";\nconst createFlatRowTree = rows => {\n  const tree = {\n    [GRID_ROOT_GROUP_ID]: _extends({}, buildRootGroup(), {\n      children: rows\n    })\n  };\n  for (let i = 0; i < rows.length; i += 1) {\n    const rowId = rows[i];\n    tree[rowId] = {\n      id: rowId,\n      depth: 0,\n      parent: GRID_ROOT_GROUP_ID,\n      type: 'leaf',\n      groupingKey: null\n    };\n  }\n  return {\n    groupingName: GRID_DEFAULT_STRATEGY,\n    tree,\n    treeDepths: {\n      0: rows.length\n    },\n    dataRowIds: rows\n  };\n};\nconst updateFlatRowTree = ({\n  previousTree,\n  actions\n}) => {\n  const tree = _extends({}, previousTree);\n  const idsToRemoveFromRootGroup = {};\n  for (let i = 0; i < actions.remove.length; i += 1) {\n    const idToDelete = actions.remove[i];\n    idsToRemoveFromRootGroup[idToDelete] = true;\n    delete tree[idToDelete];\n  }\n  for (let i = 0; i < actions.insert.length; i += 1) {\n    const idToInsert = actions.insert[i];\n    tree[idToInsert] = {\n      id: idToInsert,\n      depth: 0,\n      parent: GRID_ROOT_GROUP_ID,\n      type: 'leaf',\n      groupingKey: null\n    };\n  }\n\n  // TODO rows v6: Support row unpinning\n\n  const rootGroup = tree[GRID_ROOT_GROUP_ID];\n  let rootGroupChildren = [...rootGroup.children, ...actions.insert];\n  if (Object.values(idsToRemoveFromRootGroup).length) {\n    rootGroupChildren = rootGroupChildren.filter(id => !idsToRemoveFromRootGroup[id]);\n  }\n  tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n    children: rootGroupChildren\n  });\n  return {\n    groupingName: GRID_DEFAULT_STRATEGY,\n    tree,\n    treeDepths: {\n      0: rootGroupChildren.length\n    },\n    dataRowIds: rootGroupChildren\n  };\n};\nconst flatRowTreeCreationMethod = params => {\n  if (params.updates.type === 'full') {\n    return createFlatRowTree(params.updates.rows);\n  }\n  return updateFlatRowTree({\n    previousTree: params.previousTree,\n    actions: params.updates.actions\n  });\n};\nexport const useGridRowsPreProcessors = apiRef => {\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'rowTreeCreation', flatRowTreeCreationMethod);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,qBAAqB,EAAEC,gCAAgC,QAAQ,wCAAwC;AAChH,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,oBAAoB;AACvE,MAAMC,iBAAiB,GAAGC,IAAI,IAAI;EAChC,MAAMC,IAAI,GAAG;IACX,CAACH,kBAAkB,GAAGJ,QAAQ,CAAC,CAAC,CAAC,EAAEG,cAAc,CAAC,CAAC,EAAE;MACnDK,QAAQ,EAAEF;IACZ,CAAC;EACH,CAAC;EACD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACvC,MAAME,KAAK,GAAGL,IAAI,CAACG,CAAC,CAAC;IACrBF,IAAI,CAACI,KAAK,CAAC,GAAG;MACZC,EAAE,EAAED,KAAK;MACTE,KAAK,EAAE,CAAC;MACRC,MAAM,EAAEV,kBAAkB;MAC1BW,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE;IACf,CAAC;EACH;EACA,OAAO;IACLC,YAAY,EAAEhB,qBAAqB;IACnCM,IAAI;IACJW,UAAU,EAAE;MACV,CAAC,EAAEZ,IAAI,CAACI;IACV,CAAC;IACDS,UAAU,EAAEb;EACd,CAAC;AACH,CAAC;AACD,MAAMc,iBAAiB,GAAGC,IAAA,IAGpB;EAAA,IAHqB;IACzBC,YAAY;IACZC;EACF,CAAC,GAAAF,IAAA;EACC,MAAMd,IAAI,GAAGP,QAAQ,CAAC,CAAC,CAAC,EAAEsB,YAAY,CAAC;EACvC,MAAME,wBAAwB,GAAG,CAAC,CAAC;EACnC,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,OAAO,CAACE,MAAM,CAACf,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACjD,MAAMiB,UAAU,GAAGH,OAAO,CAACE,MAAM,CAAChB,CAAC,CAAC;IACpCe,wBAAwB,CAACE,UAAU,CAAC,GAAG,IAAI;IAC3C,OAAOnB,IAAI,CAACmB,UAAU,CAAC;EACzB;EACA,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,OAAO,CAACI,MAAM,CAACjB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACjD,MAAMmB,UAAU,GAAGL,OAAO,CAACI,MAAM,CAAClB,CAAC,CAAC;IACpCF,IAAI,CAACqB,UAAU,CAAC,GAAG;MACjBhB,EAAE,EAAEgB,UAAU;MACdf,KAAK,EAAE,CAAC;MACRC,MAAM,EAAEV,kBAAkB;MAC1BW,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE;IACf,CAAC;EACH;;EAEA;;EAEA,MAAMa,SAAS,GAAGtB,IAAI,CAACH,kBAAkB,CAAC;EAC1C,IAAI0B,iBAAiB,GAAG,CAAC,GAAGD,SAAS,CAACrB,QAAQ,EAAE,GAAGe,OAAO,CAACI,MAAM,CAAC;EAClE,IAAII,MAAM,CAACC,MAAM,CAACR,wBAAwB,CAAC,CAACd,MAAM,EAAE;IAClDoB,iBAAiB,GAAGA,iBAAiB,CAACG,MAAM,CAACrB,EAAE,IAAI,CAACY,wBAAwB,CAACZ,EAAE,CAAC,CAAC;EACnF;EACAL,IAAI,CAACH,kBAAkB,CAAC,GAAGJ,QAAQ,CAAC,CAAC,CAAC,EAAE6B,SAAS,EAAE;IACjDrB,QAAQ,EAAEsB;EACZ,CAAC,CAAC;EACF,OAAO;IACLb,YAAY,EAAEhB,qBAAqB;IACnCM,IAAI;IACJW,UAAU,EAAE;MACV,CAAC,EAAEY,iBAAiB,CAACpB;IACvB,CAAC;IACDS,UAAU,EAAEW;EACd,CAAC;AACH,CAAC;AACD,MAAMI,yBAAyB,GAAGC,MAAM,IAAI;EAC1C,IAAIA,MAAM,CAACC,OAAO,CAACrB,IAAI,KAAK,MAAM,EAAE;IAClC,OAAOV,iBAAiB,CAAC8B,MAAM,CAACC,OAAO,CAAC9B,IAAI,CAAC;EAC/C;EACA,OAAOc,iBAAiB,CAAC;IACvBE,YAAY,EAAEa,MAAM,CAACb,YAAY;IACjCC,OAAO,EAAEY,MAAM,CAACC,OAAO,CAACb;EAC1B,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMc,wBAAwB,GAAGC,MAAM,IAAI;EAChDpC,gCAAgC,CAACoC,MAAM,EAAErC,qBAAqB,EAAE,iBAAiB,EAAEiC,yBAAyB,CAAC;AAC/G,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}