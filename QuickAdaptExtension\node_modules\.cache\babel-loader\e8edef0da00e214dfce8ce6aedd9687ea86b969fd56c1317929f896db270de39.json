{"ast": null, "code": "import { useGridSelector } from \"../../utils/index.js\";\nimport { useGridApiContext } from \"../../utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../utils/useGridRootProps.js\";\nimport { gridExpandedRowCountSelector } from \"../filter/index.js\";\nimport { gridRowCountSelector, gridRowsLoadingSelector } from \"../rows/index.js\";\n/**\n * Uses the grid state to determine which overlay to display.\n * Returns the active overlay type and the active loading overlay variant.\n */\nexport const useGridOverlays = () => {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const totalRowCount = useGridSelector(apiRef, gridRowCountSelector);\n  const visibleRowCount = useGridSelector(apiRef, gridExpandedRowCountSelector);\n  const noRows = totalRowCount === 0;\n  const loading = useGridSelector(apiRef, gridRowsLoadingSelector);\n  const showNoRowsOverlay = !loading && noRows;\n  const showNoResultsOverlay = !loading && totalRowCount > 0 && visibleRowCount === 0;\n  let overlayType = null;\n  let loadingOverlayVariant = null;\n  if (showNoRowsOverlay) {\n    overlayType = 'noRowsOverlay';\n  }\n  if (showNoResultsOverlay) {\n    overlayType = 'noResultsOverlay';\n  }\n  if (loading) {\n    overlayType = 'loadingOverlay';\n    loadingOverlayVariant = rootProps.slotProps?.loadingOverlay?.[noRows ? 'noRowsVariant' : 'variant'] || null;\n  }\n  return {\n    overlayType,\n    loadingOverlayVariant\n  };\n};", "map": {"version": 3, "names": ["useGridSelector", "useGridApiContext", "useGridRootProps", "gridExpandedRowCountSelector", "gridRowCountSelector", "gridRowsLoadingSelector", "useGridOverlays", "apiRef", "rootProps", "totalRowCount", "visibleRowCount", "noRows", "loading", "showNoRowsOverlay", "showNoResultsOverlay", "overlayType", "loadingOverlayVariant", "slotProps", "loadingOverlay"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/overlays/useGridOverlays.js"], "sourcesContent": ["import { useGridSelector } from \"../../utils/index.js\";\nimport { useGridApiContext } from \"../../utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../utils/useGridRootProps.js\";\nimport { gridExpandedRowCountSelector } from \"../filter/index.js\";\nimport { gridRowCountSelector, gridRowsLoadingSelector } from \"../rows/index.js\";\n/**\n * Uses the grid state to determine which overlay to display.\n * Returns the active overlay type and the active loading overlay variant.\n */\nexport const useGridOverlays = () => {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const totalRowCount = useGridSelector(apiRef, gridRowCountSelector);\n  const visibleRowCount = useGridSelector(apiRef, gridExpandedRowCountSelector);\n  const noRows = totalRowCount === 0;\n  const loading = useGridSelector(apiRef, gridRowsLoadingSelector);\n  const showNoRowsOverlay = !loading && noRows;\n  const showNoResultsOverlay = !loading && totalRowCount > 0 && visibleRowCount === 0;\n  let overlayType = null;\n  let loadingOverlayVariant = null;\n  if (showNoRowsOverlay) {\n    overlayType = 'noRowsOverlay';\n  }\n  if (showNoResultsOverlay) {\n    overlayType = 'noResultsOverlay';\n  }\n  if (loading) {\n    overlayType = 'loadingOverlay';\n    loadingOverlayVariant = rootProps.slotProps?.loadingOverlay?.[noRows ? 'noRowsVariant' : 'variant'] || null;\n  }\n  return {\n    overlayType,\n    loadingOverlayVariant\n  };\n};"], "mappings": "AAAA,SAASA,eAAe,QAAQ,sBAAsB;AACtD,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,4BAA4B,QAAQ,oBAAoB;AACjE,SAASC,oBAAoB,EAAEC,uBAAuB,QAAQ,kBAAkB;AAChF;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EACnC,MAAMC,MAAM,GAAGN,iBAAiB,CAAC,CAAC;EAClC,MAAMO,SAAS,GAAGN,gBAAgB,CAAC,CAAC;EACpC,MAAMO,aAAa,GAAGT,eAAe,CAACO,MAAM,EAAEH,oBAAoB,CAAC;EACnE,MAAMM,eAAe,GAAGV,eAAe,CAACO,MAAM,EAAEJ,4BAA4B,CAAC;EAC7E,MAAMQ,MAAM,GAAGF,aAAa,KAAK,CAAC;EAClC,MAAMG,OAAO,GAAGZ,eAAe,CAACO,MAAM,EAAEF,uBAAuB,CAAC;EAChE,MAAMQ,iBAAiB,GAAG,CAACD,OAAO,IAAID,MAAM;EAC5C,MAAMG,oBAAoB,GAAG,CAACF,OAAO,IAAIH,aAAa,GAAG,CAAC,IAAIC,eAAe,KAAK,CAAC;EACnF,IAAIK,WAAW,GAAG,IAAI;EACtB,IAAIC,qBAAqB,GAAG,IAAI;EAChC,IAAIH,iBAAiB,EAAE;IACrBE,WAAW,GAAG,eAAe;EAC/B;EACA,IAAID,oBAAoB,EAAE;IACxBC,WAAW,GAAG,kBAAkB;EAClC;EACA,IAAIH,OAAO,EAAE;IACXG,WAAW,GAAG,gBAAgB;IAC9BC,qBAAqB,GAAGR,SAAS,CAACS,SAAS,EAAEC,cAAc,GAAGP,MAAM,GAAG,eAAe,GAAG,SAAS,CAAC,IAAI,IAAI;EAC7G;EACA,OAAO;IACLI,WAAW;IACXC;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}