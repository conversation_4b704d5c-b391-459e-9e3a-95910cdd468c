{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { gridPinnedRowsSelector } from \"./gridRowsSelector.js\";\nimport { gridDimensionsSelector } from \"../dimensions/gridDimensionsSelectors.js\";\nexport const GRID_ROOT_GROUP_ID = `auto-generated-group-node-root`;\nexport const GRID_ID_AUTOGENERATED = Symbol('mui.id_autogenerated');\nexport const buildRootGroup = () => ({\n  type: 'group',\n  id: GRID_ROOT_GROUP_ID,\n  depth: -1,\n  groupingField: null,\n  groupingKey: null,\n  isAutoGenerated: true,\n  children: [],\n  childrenFromPath: {},\n  childrenExpanded: true,\n  parent: null\n});\n\n/**\n * A helper function to check if the id provided is valid.\n * @param {GridRowId} id Id as [[GridRowId]].\n * @param {GridRowModel | Partial<GridRowModel>} row Row as [[GridRowModel]].\n * @param {string} detailErrorMessage A custom error message to display for invalid IDs\n */\nexport function checkGridRowIdIsValid(id, row) {\n  let detailErrorMessage = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'A row was provided without id in the rows prop:';\n  if (id == null) {\n    throw new Error(['MUI X: The Data Grid component requires all rows to have a unique `id` property.', 'Alternatively, you can use the `getRowId` prop to specify a custom id for each row.', detailErrorMessage, JSON.stringify(row)].join('\\n'));\n  }\n}\nexport const getRowIdFromRowModel = (rowModel, getRowId, detailErrorMessage) => {\n  const id = getRowId ? getRowId(rowModel) : rowModel.id;\n  checkGridRowIdIsValid(id, rowModel, detailErrorMessage);\n  return id;\n};\nexport const createRowsInternalCache = _ref => {\n  let {\n    rows,\n    getRowId,\n    loading,\n    rowCount\n  } = _ref;\n  const updates = {\n    type: 'full',\n    rows: []\n  };\n  const dataRowIdToModelLookup = {};\n  const dataRowIdToIdLookup = {};\n  for (let i = 0; i < rows.length; i += 1) {\n    const model = rows[i];\n    const id = getRowIdFromRowModel(model, getRowId);\n    dataRowIdToModelLookup[id] = model;\n    dataRowIdToIdLookup[id] = id;\n    updates.rows.push(id);\n  }\n  return {\n    rowsBeforePartialUpdates: rows,\n    loadingPropBeforePartialUpdates: loading,\n    rowCountPropBeforePartialUpdates: rowCount,\n    updates,\n    dataRowIdToIdLookup,\n    dataRowIdToModelLookup\n  };\n};\nexport const getTopLevelRowCount = _ref2 => {\n  let {\n    tree,\n    rowCountProp = 0\n  } = _ref2;\n  const rootGroupNode = tree[GRID_ROOT_GROUP_ID];\n  return Math.max(rowCountProp, rootGroupNode.children.length + (rootGroupNode.footerId == null ? 0 : 1));\n};\nexport const getRowsStateFromCache = _ref3 => {\n  let {\n    apiRef,\n    rowCountProp = 0,\n    loadingProp,\n    previousTree,\n    previousTreeDepths,\n    previousGroupsToFetch\n  } = _ref3;\n  const cache = apiRef.current.caches.rows;\n\n  // 1. Apply the \"rowTreeCreation\" family processing.\n  const {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    groupingName,\n    groupsToFetch = []\n  } = apiRef.current.applyStrategyProcessor('rowTreeCreation', {\n    previousTree,\n    previousTreeDepths,\n    updates: cache.updates,\n    dataRowIdToIdLookup: cache.dataRowIdToIdLookup,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup,\n    previousGroupsToFetch\n  });\n\n  // 2. Apply the \"hydrateRows\" pipe-processing.\n  const groupingParamsWithHydrateRows = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIdToIdLookup: cache.dataRowIdToIdLookup,\n    dataRowIds: unProcessedDataRowIds,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup\n  });\n\n  // 3. Reset the cache updates\n  apiRef.current.caches.rows.updates = {\n    type: 'partial',\n    actions: {\n      insert: [],\n      modify: [],\n      remove: []\n    },\n    idToActionLookup: {}\n  };\n  return _extends({}, groupingParamsWithHydrateRows, {\n    totalRowCount: Math.max(rowCountProp, groupingParamsWithHydrateRows.dataRowIds.length),\n    totalTopLevelRowCount: getTopLevelRowCount({\n      tree: groupingParamsWithHydrateRows.tree,\n      rowCountProp\n    }),\n    groupingName,\n    loading: loadingProp,\n    groupsToFetch\n  });\n};\nexport const isAutogeneratedRow = row => GRID_ID_AUTOGENERATED in row;\nexport const isAutogeneratedRowNode = rowNode => rowNode.type === 'skeletonRow' || rowNode.type === 'footer' || rowNode.type === 'group' && rowNode.isAutoGenerated || rowNode.type === 'pinnedRow' && rowNode.isAutoGenerated;\nexport const getTreeNodeDescendants = (tree, parentId, skipAutoGeneratedRows) => {\n  const node = tree[parentId];\n  if (node.type !== 'group') {\n    return [];\n  }\n  const validDescendants = [];\n  for (let i = 0; i < node.children.length; i += 1) {\n    const child = node.children[i];\n    if (!skipAutoGeneratedRows || !isAutogeneratedRowNode(tree[child])) {\n      validDescendants.push(child);\n    }\n    const childDescendants = getTreeNodeDescendants(tree, child, skipAutoGeneratedRows);\n    for (let j = 0; j < childDescendants.length; j += 1) {\n      validDescendants.push(childDescendants[j]);\n    }\n  }\n  if (!skipAutoGeneratedRows && node.footerId != null) {\n    validDescendants.push(node.footerId);\n  }\n  return validDescendants;\n};\nexport const updateCacheWithNewRows = _ref4 => {\n  let {\n    previousCache,\n    getRowId,\n    updates,\n    groupKeys\n  } = _ref4;\n  if (previousCache.updates.type === 'full') {\n    throw new Error('MUI X: Unable to prepare a partial update if a full update is not applied yet.');\n  }\n\n  // Remove duplicate updates.\n  // A server can batch updates, and send several updates for the same row in one fn call.\n  const uniqueUpdates = new Map();\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    if (uniqueUpdates.has(id)) {\n      uniqueUpdates.set(id, _extends({}, uniqueUpdates.get(id), update));\n    } else {\n      uniqueUpdates.set(id, update);\n    }\n  });\n  const partialUpdates = {\n    type: 'partial',\n    actions: {\n      insert: [...(previousCache.updates.actions.insert ?? [])],\n      modify: [...(previousCache.updates.actions.modify ?? [])],\n      remove: [...(previousCache.updates.actions.remove ?? [])]\n    },\n    idToActionLookup: _extends({}, previousCache.updates.idToActionLookup),\n    groupKeys\n  };\n  const dataRowIdToModelLookup = _extends({}, previousCache.dataRowIdToModelLookup);\n  const dataRowIdToIdLookup = _extends({}, previousCache.dataRowIdToIdLookup);\n  const alreadyAppliedActionsToRemove = {\n    insert: {},\n    modify: {},\n    remove: {}\n  };\n\n  // Depending on the action already applied to the data row,\n  // We might want drop the already-applied-update.\n  // For instance:\n  // - if you delete then insert, then you don't want to apply the deletion in the tree.\n  // - if you insert, then modify, then you just want to apply the insertion in the tree.\n  uniqueUpdates.forEach((partialRow, id) => {\n    const actionAlreadyAppliedToRow = partialUpdates.idToActionLookup[id];\n\n    // Action === \"delete\"\n    // eslint-disable-next-line no-underscore-dangle\n    if (partialRow._action === 'delete') {\n      // If the data row has been removed since the last state update,\n      // Then do nothing.\n      if (actionAlreadyAppliedToRow === 'remove' || !dataRowIdToModelLookup[id]) {\n        return;\n      }\n\n      // If the data row has been inserted / modified since the last state update,\n      // Then drop this \"insert\" / \"modify\" update.\n      if (actionAlreadyAppliedToRow != null) {\n        alreadyAppliedActionsToRemove[actionAlreadyAppliedToRow][id] = true;\n      }\n\n      // Remove the data row from the lookups and add it to the \"delete\" update.\n      partialUpdates.actions.remove.push(id);\n      delete dataRowIdToModelLookup[id];\n      delete dataRowIdToIdLookup[id];\n      return;\n    }\n    const oldRow = dataRowIdToModelLookup[id];\n\n    // Action === \"modify\"\n    if (oldRow) {\n      // If the data row has been removed since the last state update,\n      // Then drop this \"remove\" update and add it to the \"modify\" update instead.\n      if (actionAlreadyAppliedToRow === 'remove') {\n        alreadyAppliedActionsToRemove.remove[id] = true;\n        partialUpdates.actions.modify.push(id);\n      }\n      // If the date has not been inserted / modified since the last state update,\n      // Then add it to the \"modify\" update (if it has been inserted it should just remain \"inserted\").\n      else if (actionAlreadyAppliedToRow == null) {\n        partialUpdates.actions.modify.push(id);\n      }\n\n      // Update the data row lookups.\n      dataRowIdToModelLookup[id] = _extends({}, oldRow, partialRow);\n      return;\n    }\n\n    // Action === \"insert\"\n    // If the data row has been removed since the last state update,\n    // Then drop the \"remove\" update and add it to the \"insert\" update instead.\n    if (actionAlreadyAppliedToRow === 'remove') {\n      alreadyAppliedActionsToRemove.remove[id] = true;\n      partialUpdates.actions.insert.push(id);\n    }\n    // If the data row has not been inserted since the last state update,\n    // Then add it to the \"insert\" update.\n    // `actionAlreadyAppliedToRow` can't be equal to \"modify\", otherwise we would have an `oldRow` above.\n    else if (actionAlreadyAppliedToRow == null) {\n      partialUpdates.actions.insert.push(id);\n    }\n\n    // Update the data row lookups.\n    dataRowIdToModelLookup[id] = partialRow;\n    dataRowIdToIdLookup[id] = id;\n  });\n  const actionTypeWithActionsToRemove = Object.keys(alreadyAppliedActionsToRemove);\n  for (let i = 0; i < actionTypeWithActionsToRemove.length; i += 1) {\n    const actionType = actionTypeWithActionsToRemove[i];\n    const idsToRemove = alreadyAppliedActionsToRemove[actionType];\n    if (Object.keys(idsToRemove).length > 0) {\n      partialUpdates.actions[actionType] = partialUpdates.actions[actionType].filter(id => !idsToRemove[id]);\n    }\n  }\n  return {\n    dataRowIdToModelLookup,\n    dataRowIdToIdLookup,\n    updates: partialUpdates,\n    rowsBeforePartialUpdates: previousCache.rowsBeforePartialUpdates,\n    loadingPropBeforePartialUpdates: previousCache.loadingPropBeforePartialUpdates,\n    rowCountPropBeforePartialUpdates: previousCache.rowCountPropBeforePartialUpdates\n  };\n};\nexport function calculatePinnedRowsHeight(apiRef) {\n  const pinnedRows = gridPinnedRowsSelector(apiRef);\n  const topPinnedRowsHeight = pinnedRows?.top?.reduce((acc, value) => {\n    acc += apiRef.current.unstable_getRowHeight(value.id);\n    return acc;\n  }, 0) || 0;\n  const bottomPinnedRowsHeight = pinnedRows?.bottom?.reduce((acc, value) => {\n    acc += apiRef.current.unstable_getRowHeight(value.id);\n    return acc;\n  }, 0) || 0;\n  return {\n    top: topPinnedRowsHeight,\n    bottom: bottomPinnedRowsHeight\n  };\n}\nexport function getMinimalContentHeight(apiRef) {\n  const dimensions = gridDimensionsSelector(apiRef.current.state);\n  return `var(--DataGrid-overlayHeight, ${2 * dimensions.rowHeight}px)`;\n}\nexport function computeRowsUpdates(apiRef, updates, getRowId) {\n  const nonPinnedRowsUpdates = [];\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    const rowNode = apiRef.current.getRowNode(id);\n    if (rowNode?.type === 'pinnedRow') {\n      // @ts-ignore because otherwise `release:build` doesn't work\n      const pinnedRowsCache = apiRef.current.caches.pinnedRows;\n      const prevModel = pinnedRowsCache.idLookup[id];\n      if (prevModel) {\n        pinnedRowsCache.idLookup[id] = _extends({}, prevModel, update);\n      }\n    } else {\n      nonPinnedRowsUpdates.push(update);\n    }\n  });\n  return nonPinnedRowsUpdates;\n}", "map": {"version": 3, "names": ["_extends", "gridPinnedRowsSelector", "gridDimensionsSelector", "GRID_ROOT_GROUP_ID", "GRID_ID_AUTOGENERATED", "Symbol", "buildRootGroup", "type", "id", "depth", "groupingField", "grouping<PERSON>ey", "isAutoGenerated", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenExpanded", "parent", "checkGridRowIdIsValid", "row", "detailErrorMessage", "arguments", "length", "undefined", "Error", "JSON", "stringify", "join", "getRowIdFromRowModel", "rowModel", "getRowId", "createRowsInternalCache", "_ref", "rows", "loading", "rowCount", "updates", "dataRowIdToModelLookup", "dataRowIdToIdLookup", "i", "model", "push", "rowsBeforePartialUpdates", "loadingPropBeforePartialUpdates", "rowCountPropBeforePartialUpdates", "getTopLevelRowCount", "_ref2", "tree", "rowCountProp", "rootGroupNode", "Math", "max", "footerId", "getRowsStateFromCache", "_ref3", "apiRef", "loadingProp", "previousTree", "previousTreeDepths", "previousGroupsToFetch", "cache", "current", "caches", "unProcessedTree", "treeDepths", "unProcessedTreeDepths", "dataRowIds", "unProcessedDataRowIds", "groupingName", "groupsToFetch", "applyStrategyProcessor", "groupingParamsWithHydrateRows", "unstable_applyPipeProcessors", "actions", "insert", "modify", "remove", "idToActionLookup", "totalRowCount", "totalTopLevelRowCount", "isAutogeneratedRow", "isAutogeneratedRowNode", "rowNode", "getTreeNodeDescendants", "parentId", "skipAutoGeneratedRows", "node", "validDescendants", "child", "childDescendants", "j", "updateCacheWithNewRows", "_ref4", "previousCache", "groupKeys", "uniqueUpdates", "Map", "for<PERSON>ach", "update", "has", "set", "get", "partialUpdates", "alreadyAppliedActionsToRemove", "partialRow", "actionAlreadyAppliedToRow", "_action", "oldRow", "actionTypeWithActionsToRemove", "Object", "keys", "actionType", "idsToRemove", "filter", "calculatePinnedRowsHeight", "pinnedRows", "topPinnedRowsHeight", "top", "reduce", "acc", "value", "unstable_getRowHeight", "bottomPinnedRowsHeight", "bottom", "getMinimalContentHeight", "dimensions", "state", "rowHeight", "computeRowsUpdates", "nonPinnedRowsUpdates", "getRowNode", "pinnedRowsCache", "prevModel", "idLookup"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/rows/gridRowsUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { gridPinnedRowsSelector } from \"./gridRowsSelector.js\";\nimport { gridDimensionsSelector } from \"../dimensions/gridDimensionsSelectors.js\";\nexport const GRID_ROOT_GROUP_ID = `auto-generated-group-node-root`;\nexport const GRID_ID_AUTOGENERATED = Symbol('mui.id_autogenerated');\nexport const buildRootGroup = () => ({\n  type: 'group',\n  id: GRID_ROOT_GROUP_ID,\n  depth: -1,\n  groupingField: null,\n  groupingKey: null,\n  isAutoGenerated: true,\n  children: [],\n  childrenFromPath: {},\n  childrenExpanded: true,\n  parent: null\n});\n\n/**\n * A helper function to check if the id provided is valid.\n * @param {GridRowId} id Id as [[GridRowId]].\n * @param {GridRowModel | Partial<GridRowModel>} row Row as [[GridRowModel]].\n * @param {string} detailErrorMessage A custom error message to display for invalid IDs\n */\nexport function checkGridRowIdIsValid(id, row, detailErrorMessage = 'A row was provided without id in the rows prop:') {\n  if (id == null) {\n    throw new Error(['MUI X: The Data Grid component requires all rows to have a unique `id` property.', 'Alternatively, you can use the `getRowId` prop to specify a custom id for each row.', detailErrorMessage, JSON.stringify(row)].join('\\n'));\n  }\n}\nexport const getRowIdFromRowModel = (rowModel, getRowId, detailErrorMessage) => {\n  const id = getRowId ? getRowId(rowModel) : rowModel.id;\n  checkGridRowIdIsValid(id, rowModel, detailErrorMessage);\n  return id;\n};\nexport const createRowsInternalCache = ({\n  rows,\n  getRowId,\n  loading,\n  rowCount\n}) => {\n  const updates = {\n    type: 'full',\n    rows: []\n  };\n  const dataRowIdToModelLookup = {};\n  const dataRowIdToIdLookup = {};\n  for (let i = 0; i < rows.length; i += 1) {\n    const model = rows[i];\n    const id = getRowIdFromRowModel(model, getRowId);\n    dataRowIdToModelLookup[id] = model;\n    dataRowIdToIdLookup[id] = id;\n    updates.rows.push(id);\n  }\n  return {\n    rowsBeforePartialUpdates: rows,\n    loadingPropBeforePartialUpdates: loading,\n    rowCountPropBeforePartialUpdates: rowCount,\n    updates,\n    dataRowIdToIdLookup,\n    dataRowIdToModelLookup\n  };\n};\nexport const getTopLevelRowCount = ({\n  tree,\n  rowCountProp = 0\n}) => {\n  const rootGroupNode = tree[GRID_ROOT_GROUP_ID];\n  return Math.max(rowCountProp, rootGroupNode.children.length + (rootGroupNode.footerId == null ? 0 : 1));\n};\nexport const getRowsStateFromCache = ({\n  apiRef,\n  rowCountProp = 0,\n  loadingProp,\n  previousTree,\n  previousTreeDepths,\n  previousGroupsToFetch\n}) => {\n  const cache = apiRef.current.caches.rows;\n\n  // 1. Apply the \"rowTreeCreation\" family processing.\n  const {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    groupingName,\n    groupsToFetch = []\n  } = apiRef.current.applyStrategyProcessor('rowTreeCreation', {\n    previousTree,\n    previousTreeDepths,\n    updates: cache.updates,\n    dataRowIdToIdLookup: cache.dataRowIdToIdLookup,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup,\n    previousGroupsToFetch\n  });\n\n  // 2. Apply the \"hydrateRows\" pipe-processing.\n  const groupingParamsWithHydrateRows = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIdToIdLookup: cache.dataRowIdToIdLookup,\n    dataRowIds: unProcessedDataRowIds,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup\n  });\n\n  // 3. Reset the cache updates\n  apiRef.current.caches.rows.updates = {\n    type: 'partial',\n    actions: {\n      insert: [],\n      modify: [],\n      remove: []\n    },\n    idToActionLookup: {}\n  };\n  return _extends({}, groupingParamsWithHydrateRows, {\n    totalRowCount: Math.max(rowCountProp, groupingParamsWithHydrateRows.dataRowIds.length),\n    totalTopLevelRowCount: getTopLevelRowCount({\n      tree: groupingParamsWithHydrateRows.tree,\n      rowCountProp\n    }),\n    groupingName,\n    loading: loadingProp,\n    groupsToFetch\n  });\n};\nexport const isAutogeneratedRow = row => GRID_ID_AUTOGENERATED in row;\nexport const isAutogeneratedRowNode = rowNode => rowNode.type === 'skeletonRow' || rowNode.type === 'footer' || rowNode.type === 'group' && rowNode.isAutoGenerated || rowNode.type === 'pinnedRow' && rowNode.isAutoGenerated;\nexport const getTreeNodeDescendants = (tree, parentId, skipAutoGeneratedRows) => {\n  const node = tree[parentId];\n  if (node.type !== 'group') {\n    return [];\n  }\n  const validDescendants = [];\n  for (let i = 0; i < node.children.length; i += 1) {\n    const child = node.children[i];\n    if (!skipAutoGeneratedRows || !isAutogeneratedRowNode(tree[child])) {\n      validDescendants.push(child);\n    }\n    const childDescendants = getTreeNodeDescendants(tree, child, skipAutoGeneratedRows);\n    for (let j = 0; j < childDescendants.length; j += 1) {\n      validDescendants.push(childDescendants[j]);\n    }\n  }\n  if (!skipAutoGeneratedRows && node.footerId != null) {\n    validDescendants.push(node.footerId);\n  }\n  return validDescendants;\n};\nexport const updateCacheWithNewRows = ({\n  previousCache,\n  getRowId,\n  updates,\n  groupKeys\n}) => {\n  if (previousCache.updates.type === 'full') {\n    throw new Error('MUI X: Unable to prepare a partial update if a full update is not applied yet.');\n  }\n\n  // Remove duplicate updates.\n  // A server can batch updates, and send several updates for the same row in one fn call.\n  const uniqueUpdates = new Map();\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    if (uniqueUpdates.has(id)) {\n      uniqueUpdates.set(id, _extends({}, uniqueUpdates.get(id), update));\n    } else {\n      uniqueUpdates.set(id, update);\n    }\n  });\n  const partialUpdates = {\n    type: 'partial',\n    actions: {\n      insert: [...(previousCache.updates.actions.insert ?? [])],\n      modify: [...(previousCache.updates.actions.modify ?? [])],\n      remove: [...(previousCache.updates.actions.remove ?? [])]\n    },\n    idToActionLookup: _extends({}, previousCache.updates.idToActionLookup),\n    groupKeys\n  };\n  const dataRowIdToModelLookup = _extends({}, previousCache.dataRowIdToModelLookup);\n  const dataRowIdToIdLookup = _extends({}, previousCache.dataRowIdToIdLookup);\n  const alreadyAppliedActionsToRemove = {\n    insert: {},\n    modify: {},\n    remove: {}\n  };\n\n  // Depending on the action already applied to the data row,\n  // We might want drop the already-applied-update.\n  // For instance:\n  // - if you delete then insert, then you don't want to apply the deletion in the tree.\n  // - if you insert, then modify, then you just want to apply the insertion in the tree.\n  uniqueUpdates.forEach((partialRow, id) => {\n    const actionAlreadyAppliedToRow = partialUpdates.idToActionLookup[id];\n\n    // Action === \"delete\"\n    // eslint-disable-next-line no-underscore-dangle\n    if (partialRow._action === 'delete') {\n      // If the data row has been removed since the last state update,\n      // Then do nothing.\n      if (actionAlreadyAppliedToRow === 'remove' || !dataRowIdToModelLookup[id]) {\n        return;\n      }\n\n      // If the data row has been inserted / modified since the last state update,\n      // Then drop this \"insert\" / \"modify\" update.\n      if (actionAlreadyAppliedToRow != null) {\n        alreadyAppliedActionsToRemove[actionAlreadyAppliedToRow][id] = true;\n      }\n\n      // Remove the data row from the lookups and add it to the \"delete\" update.\n      partialUpdates.actions.remove.push(id);\n      delete dataRowIdToModelLookup[id];\n      delete dataRowIdToIdLookup[id];\n      return;\n    }\n    const oldRow = dataRowIdToModelLookup[id];\n\n    // Action === \"modify\"\n    if (oldRow) {\n      // If the data row has been removed since the last state update,\n      // Then drop this \"remove\" update and add it to the \"modify\" update instead.\n      if (actionAlreadyAppliedToRow === 'remove') {\n        alreadyAppliedActionsToRemove.remove[id] = true;\n        partialUpdates.actions.modify.push(id);\n      }\n      // If the date has not been inserted / modified since the last state update,\n      // Then add it to the \"modify\" update (if it has been inserted it should just remain \"inserted\").\n      else if (actionAlreadyAppliedToRow == null) {\n        partialUpdates.actions.modify.push(id);\n      }\n\n      // Update the data row lookups.\n      dataRowIdToModelLookup[id] = _extends({}, oldRow, partialRow);\n      return;\n    }\n\n    // Action === \"insert\"\n    // If the data row has been removed since the last state update,\n    // Then drop the \"remove\" update and add it to the \"insert\" update instead.\n    if (actionAlreadyAppliedToRow === 'remove') {\n      alreadyAppliedActionsToRemove.remove[id] = true;\n      partialUpdates.actions.insert.push(id);\n    }\n    // If the data row has not been inserted since the last state update,\n    // Then add it to the \"insert\" update.\n    // `actionAlreadyAppliedToRow` can't be equal to \"modify\", otherwise we would have an `oldRow` above.\n    else if (actionAlreadyAppliedToRow == null) {\n      partialUpdates.actions.insert.push(id);\n    }\n\n    // Update the data row lookups.\n    dataRowIdToModelLookup[id] = partialRow;\n    dataRowIdToIdLookup[id] = id;\n  });\n  const actionTypeWithActionsToRemove = Object.keys(alreadyAppliedActionsToRemove);\n  for (let i = 0; i < actionTypeWithActionsToRemove.length; i += 1) {\n    const actionType = actionTypeWithActionsToRemove[i];\n    const idsToRemove = alreadyAppliedActionsToRemove[actionType];\n    if (Object.keys(idsToRemove).length > 0) {\n      partialUpdates.actions[actionType] = partialUpdates.actions[actionType].filter(id => !idsToRemove[id]);\n    }\n  }\n  return {\n    dataRowIdToModelLookup,\n    dataRowIdToIdLookup,\n    updates: partialUpdates,\n    rowsBeforePartialUpdates: previousCache.rowsBeforePartialUpdates,\n    loadingPropBeforePartialUpdates: previousCache.loadingPropBeforePartialUpdates,\n    rowCountPropBeforePartialUpdates: previousCache.rowCountPropBeforePartialUpdates\n  };\n};\nexport function calculatePinnedRowsHeight(apiRef) {\n  const pinnedRows = gridPinnedRowsSelector(apiRef);\n  const topPinnedRowsHeight = pinnedRows?.top?.reduce((acc, value) => {\n    acc += apiRef.current.unstable_getRowHeight(value.id);\n    return acc;\n  }, 0) || 0;\n  const bottomPinnedRowsHeight = pinnedRows?.bottom?.reduce((acc, value) => {\n    acc += apiRef.current.unstable_getRowHeight(value.id);\n    return acc;\n  }, 0) || 0;\n  return {\n    top: topPinnedRowsHeight,\n    bottom: bottomPinnedRowsHeight\n  };\n}\nexport function getMinimalContentHeight(apiRef) {\n  const dimensions = gridDimensionsSelector(apiRef.current.state);\n  return `var(--DataGrid-overlayHeight, ${2 * dimensions.rowHeight}px)`;\n}\nexport function computeRowsUpdates(apiRef, updates, getRowId) {\n  const nonPinnedRowsUpdates = [];\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    const rowNode = apiRef.current.getRowNode(id);\n    if (rowNode?.type === 'pinnedRow') {\n      // @ts-ignore because otherwise `release:build` doesn't work\n      const pinnedRowsCache = apiRef.current.caches.pinnedRows;\n      const prevModel = pinnedRowsCache.idLookup[id];\n      if (prevModel) {\n        pinnedRowsCache.idLookup[id] = _extends({}, prevModel, update);\n      }\n    } else {\n      nonPinnedRowsUpdates.push(update);\n    }\n  });\n  return nonPinnedRowsUpdates;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,sBAAsB,QAAQ,uBAAuB;AAC9D,SAASC,sBAAsB,QAAQ,0CAA0C;AACjF,OAAO,MAAMC,kBAAkB,GAAG,gCAAgC;AAClE,OAAO,MAAMC,qBAAqB,GAAGC,MAAM,CAAC,sBAAsB,CAAC;AACnE,OAAO,MAAMC,cAAc,GAAGA,CAAA,MAAO;EACnCC,IAAI,EAAE,OAAO;EACbC,EAAE,EAAEL,kBAAkB;EACtBM,KAAK,EAAE,CAAC,CAAC;EACTC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE,IAAI;EACjBC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE,EAAE;EACZC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,MAAM,EAAE;AACV,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACT,EAAE,EAAEU,GAAG,EAA0E;EAAA,IAAxEC,kBAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,iDAAiD;EACnH,IAAIZ,EAAE,IAAI,IAAI,EAAE;IACd,MAAM,IAAIe,KAAK,CAAC,CAAC,kFAAkF,EAAE,qFAAqF,EAAEJ,kBAAkB,EAAEK,IAAI,CAACC,SAAS,CAACP,GAAG,CAAC,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAC,CAAC;EAClP;AACF;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,EAAEV,kBAAkB,KAAK;EAC9E,MAAMX,EAAE,GAAGqB,QAAQ,GAAGA,QAAQ,CAACD,QAAQ,CAAC,GAAGA,QAAQ,CAACpB,EAAE;EACtDS,qBAAqB,CAACT,EAAE,EAAEoB,QAAQ,EAAET,kBAAkB,CAAC;EACvD,OAAOX,EAAE;AACX,CAAC;AACD,OAAO,MAAMsB,uBAAuB,GAAGC,IAAA,IAKjC;EAAA,IALkC;IACtCC,IAAI;IACJH,QAAQ;IACRI,OAAO;IACPC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMI,OAAO,GAAG;IACd5B,IAAI,EAAE,MAAM;IACZyB,IAAI,EAAE;EACR,CAAC;EACD,MAAMI,sBAAsB,GAAG,CAAC,CAAC;EACjC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAACX,MAAM,EAAEiB,CAAC,IAAI,CAAC,EAAE;IACvC,MAAMC,KAAK,GAAGP,IAAI,CAACM,CAAC,CAAC;IACrB,MAAM9B,EAAE,GAAGmB,oBAAoB,CAACY,KAAK,EAAEV,QAAQ,CAAC;IAChDO,sBAAsB,CAAC5B,EAAE,CAAC,GAAG+B,KAAK;IAClCF,mBAAmB,CAAC7B,EAAE,CAAC,GAAGA,EAAE;IAC5B2B,OAAO,CAACH,IAAI,CAACQ,IAAI,CAAChC,EAAE,CAAC;EACvB;EACA,OAAO;IACLiC,wBAAwB,EAAET,IAAI;IAC9BU,+BAA+B,EAAET,OAAO;IACxCU,gCAAgC,EAAET,QAAQ;IAC1CC,OAAO;IACPE,mBAAmB;IACnBD;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMQ,mBAAmB,GAAGC,KAAA,IAG7B;EAAA,IAH8B;IAClCC,IAAI;IACJC,YAAY,GAAG;EACjB,CAAC,GAAAF,KAAA;EACC,MAAMG,aAAa,GAAGF,IAAI,CAAC3C,kBAAkB,CAAC;EAC9C,OAAO8C,IAAI,CAACC,GAAG,CAACH,YAAY,EAAEC,aAAa,CAACnC,QAAQ,CAACQ,MAAM,IAAI2B,aAAa,CAACG,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACzG,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGC,KAAA,IAO/B;EAAA,IAPgC;IACpCC,MAAM;IACNP,YAAY,GAAG,CAAC;IAChBQ,WAAW;IACXC,YAAY;IACZC,kBAAkB;IAClBC;EACF,CAAC,GAAAL,KAAA;EACC,MAAMM,KAAK,GAAGL,MAAM,CAACM,OAAO,CAACC,MAAM,CAAC7B,IAAI;;EAExC;EACA,MAAM;IACJc,IAAI,EAAEgB,eAAe;IACrBC,UAAU,EAAEC,qBAAqB;IACjCC,UAAU,EAAEC,qBAAqB;IACjCC,YAAY;IACZC,aAAa,GAAG;EAClB,CAAC,GAAGd,MAAM,CAACM,OAAO,CAACS,sBAAsB,CAAC,iBAAiB,EAAE;IAC3Db,YAAY;IACZC,kBAAkB;IAClBtB,OAAO,EAAEwB,KAAK,CAACxB,OAAO;IACtBE,mBAAmB,EAAEsB,KAAK,CAACtB,mBAAmB;IAC9CD,sBAAsB,EAAEuB,KAAK,CAACvB,sBAAsB;IACpDsB;EACF,CAAC,CAAC;;EAEF;EACA,MAAMY,6BAA6B,GAAGhB,MAAM,CAACM,OAAO,CAACW,4BAA4B,CAAC,aAAa,EAAE;IAC/FzB,IAAI,EAAEgB,eAAe;IACrBC,UAAU,EAAEC,qBAAqB;IACjC3B,mBAAmB,EAAEsB,KAAK,CAACtB,mBAAmB;IAC9C4B,UAAU,EAAEC,qBAAqB;IACjC9B,sBAAsB,EAAEuB,KAAK,CAACvB;EAChC,CAAC,CAAC;;EAEF;EACAkB,MAAM,CAACM,OAAO,CAACC,MAAM,CAAC7B,IAAI,CAACG,OAAO,GAAG;IACnC5B,IAAI,EAAE,SAAS;IACfiE,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;IACV,CAAC;IACDC,gBAAgB,EAAE,CAAC;EACrB,CAAC;EACD,OAAO5E,QAAQ,CAAC,CAAC,CAAC,EAAEsE,6BAA6B,EAAE;IACjDO,aAAa,EAAE5B,IAAI,CAACC,GAAG,CAACH,YAAY,EAAEuB,6BAA6B,CAACL,UAAU,CAAC5C,MAAM,CAAC;IACtFyD,qBAAqB,EAAElC,mBAAmB,CAAC;MACzCE,IAAI,EAAEwB,6BAA6B,CAACxB,IAAI;MACxCC;IACF,CAAC,CAAC;IACFoB,YAAY;IACZlC,OAAO,EAAEsB,WAAW;IACpBa;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMW,kBAAkB,GAAG7D,GAAG,IAAId,qBAAqB,IAAIc,GAAG;AACrE,OAAO,MAAM8D,sBAAsB,GAAGC,OAAO,IAAIA,OAAO,CAAC1E,IAAI,KAAK,aAAa,IAAI0E,OAAO,CAAC1E,IAAI,KAAK,QAAQ,IAAI0E,OAAO,CAAC1E,IAAI,KAAK,OAAO,IAAI0E,OAAO,CAACrE,eAAe,IAAIqE,OAAO,CAAC1E,IAAI,KAAK,WAAW,IAAI0E,OAAO,CAACrE,eAAe;AAC9N,OAAO,MAAMsE,sBAAsB,GAAGA,CAACpC,IAAI,EAAEqC,QAAQ,EAAEC,qBAAqB,KAAK;EAC/E,MAAMC,IAAI,GAAGvC,IAAI,CAACqC,QAAQ,CAAC;EAC3B,IAAIE,IAAI,CAAC9E,IAAI,KAAK,OAAO,EAAE;IACzB,OAAO,EAAE;EACX;EACA,MAAM+E,gBAAgB,GAAG,EAAE;EAC3B,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,IAAI,CAACxE,QAAQ,CAACQ,MAAM,EAAEiB,CAAC,IAAI,CAAC,EAAE;IAChD,MAAMiD,KAAK,GAAGF,IAAI,CAACxE,QAAQ,CAACyB,CAAC,CAAC;IAC9B,IAAI,CAAC8C,qBAAqB,IAAI,CAACJ,sBAAsB,CAAClC,IAAI,CAACyC,KAAK,CAAC,CAAC,EAAE;MAClED,gBAAgB,CAAC9C,IAAI,CAAC+C,KAAK,CAAC;IAC9B;IACA,MAAMC,gBAAgB,GAAGN,sBAAsB,CAACpC,IAAI,EAAEyC,KAAK,EAAEH,qBAAqB,CAAC;IACnF,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,gBAAgB,CAACnE,MAAM,EAAEoE,CAAC,IAAI,CAAC,EAAE;MACnDH,gBAAgB,CAAC9C,IAAI,CAACgD,gBAAgB,CAACC,CAAC,CAAC,CAAC;IAC5C;EACF;EACA,IAAI,CAACL,qBAAqB,IAAIC,IAAI,CAAClC,QAAQ,IAAI,IAAI,EAAE;IACnDmC,gBAAgB,CAAC9C,IAAI,CAAC6C,IAAI,CAAClC,QAAQ,CAAC;EACtC;EACA,OAAOmC,gBAAgB;AACzB,CAAC;AACD,OAAO,MAAMI,sBAAsB,GAAGC,KAAA,IAKhC;EAAA,IALiC;IACrCC,aAAa;IACb/D,QAAQ;IACRM,OAAO;IACP0D;EACF,CAAC,GAAAF,KAAA;EACC,IAAIC,aAAa,CAACzD,OAAO,CAAC5B,IAAI,KAAK,MAAM,EAAE;IACzC,MAAM,IAAIgB,KAAK,CAAC,gFAAgF,CAAC;EACnG;;EAEA;EACA;EACA,MAAMuE,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/B5D,OAAO,CAAC6D,OAAO,CAACC,MAAM,IAAI;IACxB,MAAMzF,EAAE,GAAGmB,oBAAoB,CAACsE,MAAM,EAAEpE,QAAQ,EAAE,0DAA0D,CAAC;IAC7G,IAAIiE,aAAa,CAACI,GAAG,CAAC1F,EAAE,CAAC,EAAE;MACzBsF,aAAa,CAACK,GAAG,CAAC3F,EAAE,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAE8F,aAAa,CAACM,GAAG,CAAC5F,EAAE,CAAC,EAAEyF,MAAM,CAAC,CAAC;IACpE,CAAC,MAAM;MACLH,aAAa,CAACK,GAAG,CAAC3F,EAAE,EAAEyF,MAAM,CAAC;IAC/B;EACF,CAAC,CAAC;EACF,MAAMI,cAAc,GAAG;IACrB9F,IAAI,EAAE,SAAS;IACfiE,OAAO,EAAE;MACPC,MAAM,EAAE,CAAC,IAAImB,aAAa,CAACzD,OAAO,CAACqC,OAAO,CAACC,MAAM,IAAI,EAAE,CAAC,CAAC;MACzDC,MAAM,EAAE,CAAC,IAAIkB,aAAa,CAACzD,OAAO,CAACqC,OAAO,CAACE,MAAM,IAAI,EAAE,CAAC,CAAC;MACzDC,MAAM,EAAE,CAAC,IAAIiB,aAAa,CAACzD,OAAO,CAACqC,OAAO,CAACG,MAAM,IAAI,EAAE,CAAC;IAC1D,CAAC;IACDC,gBAAgB,EAAE5E,QAAQ,CAAC,CAAC,CAAC,EAAE4F,aAAa,CAACzD,OAAO,CAACyC,gBAAgB,CAAC;IACtEiB;EACF,CAAC;EACD,MAAMzD,sBAAsB,GAAGpC,QAAQ,CAAC,CAAC,CAAC,EAAE4F,aAAa,CAACxD,sBAAsB,CAAC;EACjF,MAAMC,mBAAmB,GAAGrC,QAAQ,CAAC,CAAC,CAAC,EAAE4F,aAAa,CAACvD,mBAAmB,CAAC;EAC3E,MAAMiE,6BAA6B,GAAG;IACpC7B,MAAM,EAAE,CAAC,CAAC;IACVC,MAAM,EAAE,CAAC,CAAC;IACVC,MAAM,EAAE,CAAC;EACX,CAAC;;EAED;EACA;EACA;EACA;EACA;EACAmB,aAAa,CAACE,OAAO,CAAC,CAACO,UAAU,EAAE/F,EAAE,KAAK;IACxC,MAAMgG,yBAAyB,GAAGH,cAAc,CAACzB,gBAAgB,CAACpE,EAAE,CAAC;;IAErE;IACA;IACA,IAAI+F,UAAU,CAACE,OAAO,KAAK,QAAQ,EAAE;MACnC;MACA;MACA,IAAID,yBAAyB,KAAK,QAAQ,IAAI,CAACpE,sBAAsB,CAAC5B,EAAE,CAAC,EAAE;QACzE;MACF;;MAEA;MACA;MACA,IAAIgG,yBAAyB,IAAI,IAAI,EAAE;QACrCF,6BAA6B,CAACE,yBAAyB,CAAC,CAAChG,EAAE,CAAC,GAAG,IAAI;MACrE;;MAEA;MACA6F,cAAc,CAAC7B,OAAO,CAACG,MAAM,CAACnC,IAAI,CAAChC,EAAE,CAAC;MACtC,OAAO4B,sBAAsB,CAAC5B,EAAE,CAAC;MACjC,OAAO6B,mBAAmB,CAAC7B,EAAE,CAAC;MAC9B;IACF;IACA,MAAMkG,MAAM,GAAGtE,sBAAsB,CAAC5B,EAAE,CAAC;;IAEzC;IACA,IAAIkG,MAAM,EAAE;MACV;MACA;MACA,IAAIF,yBAAyB,KAAK,QAAQ,EAAE;QAC1CF,6BAA6B,CAAC3B,MAAM,CAACnE,EAAE,CAAC,GAAG,IAAI;QAC/C6F,cAAc,CAAC7B,OAAO,CAACE,MAAM,CAAClC,IAAI,CAAChC,EAAE,CAAC;MACxC;MACA;MACA;MAAA,KACK,IAAIgG,yBAAyB,IAAI,IAAI,EAAE;QAC1CH,cAAc,CAAC7B,OAAO,CAACE,MAAM,CAAClC,IAAI,CAAChC,EAAE,CAAC;MACxC;;MAEA;MACA4B,sBAAsB,CAAC5B,EAAE,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC,EAAE0G,MAAM,EAAEH,UAAU,CAAC;MAC7D;IACF;;IAEA;IACA;IACA;IACA,IAAIC,yBAAyB,KAAK,QAAQ,EAAE;MAC1CF,6BAA6B,CAAC3B,MAAM,CAACnE,EAAE,CAAC,GAAG,IAAI;MAC/C6F,cAAc,CAAC7B,OAAO,CAACC,MAAM,CAACjC,IAAI,CAAChC,EAAE,CAAC;IACxC;IACA;IACA;IACA;IAAA,KACK,IAAIgG,yBAAyB,IAAI,IAAI,EAAE;MAC1CH,cAAc,CAAC7B,OAAO,CAACC,MAAM,CAACjC,IAAI,CAAChC,EAAE,CAAC;IACxC;;IAEA;IACA4B,sBAAsB,CAAC5B,EAAE,CAAC,GAAG+F,UAAU;IACvClE,mBAAmB,CAAC7B,EAAE,CAAC,GAAGA,EAAE;EAC9B,CAAC,CAAC;EACF,MAAMmG,6BAA6B,GAAGC,MAAM,CAACC,IAAI,CAACP,6BAA6B,CAAC;EAChF,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,6BAA6B,CAACtF,MAAM,EAAEiB,CAAC,IAAI,CAAC,EAAE;IAChE,MAAMwE,UAAU,GAAGH,6BAA6B,CAACrE,CAAC,CAAC;IACnD,MAAMyE,WAAW,GAAGT,6BAA6B,CAACQ,UAAU,CAAC;IAC7D,IAAIF,MAAM,CAACC,IAAI,CAACE,WAAW,CAAC,CAAC1F,MAAM,GAAG,CAAC,EAAE;MACvCgF,cAAc,CAAC7B,OAAO,CAACsC,UAAU,CAAC,GAAGT,cAAc,CAAC7B,OAAO,CAACsC,UAAU,CAAC,CAACE,MAAM,CAACxG,EAAE,IAAI,CAACuG,WAAW,CAACvG,EAAE,CAAC,CAAC;IACxG;EACF;EACA,OAAO;IACL4B,sBAAsB;IACtBC,mBAAmB;IACnBF,OAAO,EAAEkE,cAAc;IACvB5D,wBAAwB,EAAEmD,aAAa,CAACnD,wBAAwB;IAChEC,+BAA+B,EAAEkD,aAAa,CAAClD,+BAA+B;IAC9EC,gCAAgC,EAAEiD,aAAa,CAACjD;EAClD,CAAC;AACH,CAAC;AACD,OAAO,SAASsE,yBAAyBA,CAAC3D,MAAM,EAAE;EAChD,MAAM4D,UAAU,GAAGjH,sBAAsB,CAACqD,MAAM,CAAC;EACjD,MAAM6D,mBAAmB,GAAGD,UAAU,EAAEE,GAAG,EAAEC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAClED,GAAG,IAAIhE,MAAM,CAACM,OAAO,CAAC4D,qBAAqB,CAACD,KAAK,CAAC/G,EAAE,CAAC;IACrD,OAAO8G,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;EACV,MAAMG,sBAAsB,GAAGP,UAAU,EAAEQ,MAAM,EAAEL,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IACxED,GAAG,IAAIhE,MAAM,CAACM,OAAO,CAAC4D,qBAAqB,CAACD,KAAK,CAAC/G,EAAE,CAAC;IACrD,OAAO8G,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;EACV,OAAO;IACLF,GAAG,EAAED,mBAAmB;IACxBO,MAAM,EAAED;EACV,CAAC;AACH;AACA,OAAO,SAASE,uBAAuBA,CAACrE,MAAM,EAAE;EAC9C,MAAMsE,UAAU,GAAG1H,sBAAsB,CAACoD,MAAM,CAACM,OAAO,CAACiE,KAAK,CAAC;EAC/D,OAAO,iCAAiC,CAAC,GAAGD,UAAU,CAACE,SAAS,KAAK;AACvE;AACA,OAAO,SAASC,kBAAkBA,CAACzE,MAAM,EAAEnB,OAAO,EAAEN,QAAQ,EAAE;EAC5D,MAAMmG,oBAAoB,GAAG,EAAE;EAC/B7F,OAAO,CAAC6D,OAAO,CAACC,MAAM,IAAI;IACxB,MAAMzF,EAAE,GAAGmB,oBAAoB,CAACsE,MAAM,EAAEpE,QAAQ,EAAE,0DAA0D,CAAC;IAC7G,MAAMoD,OAAO,GAAG3B,MAAM,CAACM,OAAO,CAACqE,UAAU,CAACzH,EAAE,CAAC;IAC7C,IAAIyE,OAAO,EAAE1E,IAAI,KAAK,WAAW,EAAE;MACjC;MACA,MAAM2H,eAAe,GAAG5E,MAAM,CAACM,OAAO,CAACC,MAAM,CAACqD,UAAU;MACxD,MAAMiB,SAAS,GAAGD,eAAe,CAACE,QAAQ,CAAC5H,EAAE,CAAC;MAC9C,IAAI2H,SAAS,EAAE;QACbD,eAAe,CAACE,QAAQ,CAAC5H,EAAE,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC,EAAEmI,SAAS,EAAElC,MAAM,CAAC;MAChE;IACF,CAAC,MAAM;MACL+B,oBAAoB,CAACxF,IAAI,CAACyD,MAAM,CAAC;IACnC;EACF,CAAC,CAAC;EACF,OAAO+B,oBAAoB;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}