{"ast": null, "code": "/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n  this.size = 0;\n}\nmodule.exports = listCacheClear;", "map": {"version": 3, "names": ["listCacheClear", "__data__", "size", "module", "exports"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash/_listCacheClear.js"], "sourcesContent": ["/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n  this.size = 0;\n}\n\nmodule.exports = listCacheClear;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAcA,CAAA,EAAG;EACxB,IAAI,CAACC,QAAQ,GAAG,EAAE;EAClB,IAAI,CAACC,IAAI,GAAG,CAAC;AACf;AAEAC,MAAM,CAACC,OAAO,GAAGJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}