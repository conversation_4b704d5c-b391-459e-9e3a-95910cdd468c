{"version": 3, "file": "static/css/main.432d2f80.css", "mappings": "sFAAA,KAEE,mIAKF,CAEA,KACE,uEAEF,CCXA,MAOC,2BACD,CCTA,KACE,kBAGF,UACE,cACA,oBAGF,6CACE,UACE,6CAIJ,YAKE,mBAJA,yBAOA,WALA,aACA,sBAGA,6BADA,uBAJA,gBAMA,CAGF,UACE,cAGF,yBACE,GACE,uBAEF,GACE,yBCjCJ,MACC,qDAAyD,CAMtD,gBAAkB,CAClB,wBAA0B,CAC1B,0BAA4B,CAC5B,qBACJ,CACA,4GACC,6DACD,CACA,uJAGI,cAA2B,CAA3B,0BACJ,CACA,KAEC,6DAGD,CA6BA,YAaI,sBAAuB,CANvB,qBAAoC,CAApC,mCAAoC,CACpC,8BAA2C,CAA3C,0CAA2C,CAE3C,YAAa,CACb,qBAAsB,CALtB,WAAY,CAMZ,0BAA2B,CAV3B,MAAO,CAGP,yBAA2B,CAU3B,yBAA2B,CAd3B,cAAe,CAEf,KAAM,CAMN,6DAAkE,CALlE,qBAAuB,CAUvB,uBAEJ,CACA,mBACI,YACF,CACA,iCACE,cACJ,CACA,sBAGI,kCAAgD,CAAhD,8CAAgD,CAGhD,+BAAgC,CADhC,4BAA6B,CAH7B,wBAA0B,CAE1B,cAAiB,CAHjB,oBAMJ,CACA,0CACI,uBAAyB,CAEzB,yBAA6B,CAD7B,mBAEJ,CAEA,2EASQ,kBAAmB,CANnB,qBAAuB,CAEvB,kBAAmB,CADnB,yBAA2B,CAI3B,YAAa,CAFb,WAAY,CAJZ,0BAA4B,CADlC,mBAAqB,CASf,oBAAqB,CAHrB,UAIR,CACA,kFACI,+BACF,CAKF,+FACI,+BAAkC,CAClC,iBAAkB,CAClB,WAEJ,CACA,kFAEI,uBAAyB,CADzB,wBAKJ,CAKA,sFAGI,SAAU,CACV,WAAY,CACZ,YAAa,CAJb,qBAAuB,CACvB,oBAIJ,CAEA,qGACI,SAAW,CACX,WACJ,CACA,uGACI,SAEI,sBAAoC,CAApC,kCAAoC,CACpC,wBAAsC,CAAtC,oCACJ,CACJ,CACA,2DACI,SAEI,SAAW,CACf,WACA,CACJ,CAGA,4DAGI,kBAAmB,CADnB,YAAa,CAEb,oBACJ,CACA,0EACI,mBACJ,CACA,sEASI,kBAAmB,CAGnB,eAAgB,CAThB,qBAAuB,CAEvB,kBAAmB,CADnB,yBAA2B,CAO3B,cAAe,CAHf,YAAa,CAFb,WAAY,CAJZ,0BAA4B,CAW5B,YAAa,CAZb,mBAAqB,CASrB,oBAAqB,CAHrB,UAOJ,CACA,oFACI,YACJ,CAEA,6EACI,2DACJ,CAGA,qBAEI,qBAAuB,CADvB,oBAEJ,CAEA,oBAGI,kBAAmB,CADnB,YAAa,CADb,6BAA8B,CAK9B,kBAA0B,CAD1B,iBAAkB,CADlB,4BAGJ,CACA,sCACI,cAAe,CAGX,YAAa,CADjB,iBAAkB,CADlB,KAGJ,CAIA,sBAEC,eAAgB,CADhB,oBAAsB,CAEtB,cAGC,YAAa,CADb,cAAe,CADf,UAAW,CAGX,uBAEC,YAAa,CACb,iBAAkB,CAClB,4BAKC,YAAa,CAFb,aAAc,CACd,aAAc,CAFd,SAID,CACA,OACC,gCACD,CACD,CACA,YACC,yCACC,kBAAmB,CACnB,uBAGC,cAAe,CADf,cAAe,CADf,uBAGD,CACD,CACD,CACA,sCAGC,iBACC,qBAAuB,CACvB,oBACD,CACA,UACC,iBAAkB,CAClB,aACC,aACD,CACD,CACA,MACC,wBACD,CACD,CACA,eAEC,WAAY,CADZ,UAED,CACD,CAEA,eACC,YAAa,CACb,UAAW,CACX,sBAAuB,CACvB,OAIC,kBAAmB,CADnB,WAAY,CAEZ,gBAAiB,CAHjB,WAAY,CADZ,gCAKD,CACD,CACA,IACC,WAAY,CACZ,UACD,CACD,CAKA,uCAII,aAA0B,CAA1B,yBAA0B,CAH1B,0CAA+C,CAC/C,cAAe,CACf,eAEJ,CACA,wCAEI,cAAe,CADf,iBAEJ,CACA,4CAEI,YAAc,CADd,qBAEJ,CAMA,qBAEI,eAAgB,CADhB,UAEJ,CACA,4CACI,cAAe,CACf,eAAgB,CAEhB,YAAa,CADb,eAEJ,CACA,uCACI,eACJ,CACA,yDACI,cAAe,CACf,eAAgB,CAChB,eACJ,CACA,0DAQI,qBAAoC,CAApC,mCAAoC,CAJpC,qBAAqC,CAArC,oCAAqC,CACrC,iBAAkB,CAClB,eAAgB,CALhB,cAAe,CACf,eAAgB,CAKhB,WAAY,CAEZ,eAAgB,CANhB,YAOJ,CACA,2DACI,cACJ,CACA,wDACI,uBAAqC,CAArC,mCAAqC,CACrC,cAAe,CACf,cAAe,CACf,eAAgB,CAChB,gBAAiB,CACjB,eAAgB,CAChB,eACJ,CACA,oCAEI,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CAEvB,gBAAiB,CADjB,iBAEJ,CACA,mDAGI,cAAe,CADf,SAAU,CADV,iBAAkB,CAGlB,OACJ,CACA,yDACI,wBAA0B,CAC1B,eACJ,CACA,oCACI,eACJ,CACA,kCAEI,aAAS,CADT,YAAa,CACb,QAAS,CAET,gBAAiB,CADjB,eAEJ,CACA,qDAII,kBAAmB,CAEnB,iBAAkB,CAElB,cAAe,CALf,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAEvB,kBAAmB,CANnB,WAQJ,CACA,8DACI,oDACJ,CACA,oEACI,kBACJ,CACA,oDACI,cAAe,CACf,eAAgB,CAChB,eACJ,CACA,uCACI,eAAgB,CAChB,cACJ,CACA,qDAGK,kBAAmB,CAFpB,YAAa,CACb,wBAEJ,CACA,0DACI,gBACJ,CAEA,yEACI,uBACJ,CAEA,0DAGI,UAAW,CAFX,cAAe,CACf,eAAgB,CAEhB,iBAAkB,CAClB,eACJ,CACA,0DAII,iBAAkB,CADlB,QAAS,CAFT,WAAY,CAKZ,kBAAmB,CADnB,SAAU,CAHV,YAMJ,CACA,gEAGI,kFAA8B,CAA9B,4BAA8B,CAE9B,wBAA0B,CAJ1B,qBAAuB,CACvB,4BAA+B,CAE/B,WAEJ,CACA,mDAGQ,wBAA0B,CAF1B,gBAAiB,CACjB,mBAER,CACA,yCAGI,cAAe,CADf,iBAAkB,CADlB,QAGJ,CACA,2CAMI,kBAAmB,CAJnB,wBAAyB,CAKzB,iBAAkB,CAJlB,YAAa,CACb,qBAAsB,CAKtB,QAAS,CART,YAAa,CAIb,sBAAuB,CAGvB,eAEJ,CACA,WACI,kCAAgD,CAAhD,8CAAgD,CAMhD,kBAAoB,CAJpB,4BAA8B,CAD9B,oBAAoC,CAApC,kCAAoC,CAIpC,wBAA0B,CAE1B,4BAAgD,CAAhD,8CAAgD,CAChD,2BAA6B,CAJ7B,6BAA+B,CAD/B,oBAMJ,CACA,oBACA,UACA,CACA,kBAGI,kBAAmB,CAEnB,wBAAuC,CAAvC,sCAAuC,CACvC,+BAA4C,CAA5C,2CAA4C,CAL5C,YAAa,CAMb,WAAY,CAIZ,MAAO,CAFP,cAAe,CACf,KAAM,CAFN,UAAW,CAIX,eACJ,CAEA,uCASI,gBAAiC,CARjC,wBAAqC,CAArC,oCAAqC,CAErC,iBAAkB,CAClB,aAA0B,CAA1B,yBAA0B,CAC1B,OAAQ,CAER,WAAY,CADZ,eAAgB,CAJhB,WAAY,CAMZ,mCAEJ,CACA,wBAGI,wBAA0B,CAF1B,wBAA0B,CAC1B,yBAEJ,CACA,kDAII,kBAAmB,CAFnB,eAAgB,CADhB,eAAgB,CAEhB,oBAEJ,CACA,6DAEI,eAAkB,CADlB,mBAEJ,CAKA,kHAII,kBAAmB,CADnB,YAAa,CAEb,OAAQ,CACR,gBAAiB,CACjB,YACJ,CACA,qCACI,iBACJ,CAEA,oDAEI,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAIb,YAAa,CADb,UAEJ,CAEA,kDAII,0BAA2B,CAF3B,eAAgB,CAChB,cAEJ,CAEA,uCACI,gCACJ,CACA,sCACI,+BAAiC,CACjC,kBACJ,CACA,uIAEI,kBAAmB,CADnB,YAEJ,CACA,sJAMI,kBAAmB,CADnB,YAAa,CAJb,yBAA2B,CAE3B,eAAgB,CADhB,sBAAuB,CAEvB,kBAGJ,CACA,kDACA,UACA,CACA,sBACI,wBACJ,CACA,wCAKI,iBAAkB,CADlB,eAAgB,CAFhB,gBAAiB,CADjB,eAAgB,CAEhB,YAGJ,CACA,kBAEI,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAGb,kBACJ,CACA,2BAGI,cAAe,CADf,UAAY,CADZ,mBAGJ,CACA,6BACI,eACJ,CACA,kBAEI,YAAa,CADb,iBAEJ,CAGA,kBACI,cAAe,CACf,eAAgB,CAChB,gBACJ,CAKA,mBACI,sBACJ,CACA,qDAEI,oBAAsB,CAEtB,6BAA8B,CAH9B,mBAAqB,CAErB,yBAEJ,CACA,wDACI,kBAAmB,CACnB,qBACJ,CAEA,sBAGI,YAAa,CAFb,WAQJ,CAEA,yCAPI,wBAAwC,CAFxC,iBAAkB,CAGlB,YAAa,CACb,qBAAsB,CACtB,0BAeJ,CAXA,mBAUI,SAAU,CANV,cAAe,CAKf,QAAS,CART,WAAY,CAIZ,cAMJ,CACA,iCAII,SAAW,CADX,cAAe,CAFf,WAAY,CACZ,QAGJ,CACA,oCAIE,SAAU,CAHR,cAAe,CAEjB,WAAY,CADZ,SAAU,CAGV,aACF,CACA,oCAIE,SAAU,CAHR,cAAe,CAEjB,WAAY,CADZ,QAGF,CAEA,gCACC,+BACD,CACA,uDAGI,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,YACJ,CACA,oEAEI,aAA0B,CAA1B,yBAA0B,CAC1B,wBAA0B,CAF1B,eAAgB,CAGhB,iBACJ,CACA,2DACI,cACJ,CACA,oDASI,kBAAmB,CANnB,wBAAyB,CAIzB,kBAAmB,CAHnB,UAAc,CAMd,eAAgB,CARhB,0BAA2B,CAI3B,iBAAkB,CAElB,gBAAiB,CAHjB,mBAAoB,CAJpB,UAUJ,CACA,oEACI,oBAAmC,CACnC,mBAAoB,CACpB,eACJ,CAEA,oEAGI,oBAAmC,CACnC,mBAAoB,CAFpB,WAAY,CAKZ,gBAAiB,CAFjB,gBAAiB,CAJjB,gBAAiB,CAKjB,eAEJ,CAEA,wEACI,WAAY,CACZ,UACJ,CACA,sDAEI,mBACJ,CAGA,0DACI,wBACJ,CACA,wDAEI,kBAAmB,CADnB,aAA0B,CAA1B,yBAA0B,CAE1B,WAAY,CACZ,UACJ,CACA,0DACI,YAAa,CACT,qBAAsB,CACtB,QAAS,CAET,mBACR,CAGA,uEACI,eACJ,CAIA,qFAII,wBAA0B,CAF1B,WAAY,CADZ,yBAA0B,CAE1B,UAEJ,CACA,iHACI,wBAAqC,CAArC,oCACJ,CACA,qDACI,wBAAyB,CACzB,iBAAkB,CAClB,0BACJ,CACA,8DACI,oCACJ,CACA,4EACI,WAAY,CACZ,UACJ,CACA,qFACI,WACJ,CACA,2FACI,oBACJ,CACA,wCAEI,wBAAyC,CAAzC,wCAAyC,CACzC,yCAA0C,CAC1C,iBAAkB,CAHlB,WAIJ,CACA,sDACI,kBAAiC,CAAjC,gCAAiC,CAGjC,kBAAmB,CADnB,QAAS,CADT,UAGJ,CACA,0DAEI,wBAA0B,CAC1B,eAAgB,CAFhB,eAGJ,CACA,mCAEI,mBACJ,CACA,sDACI,gBACJ,CACA,qDACI,eACJ,CACA,qEAEC,0BAA4B,CADzB,eAEJ,CAGA,sDAEI,kBAAmB,CACnB,wBAAyC,CAAzC,wCAAyC,CACzC,yCAA0C,CAH1C,YAAa,CAIb,WAAY,CAGZ,6BAA8B,CAD9B,iBAAkB,CADlB,iBAGJ,CAKA,2EACI,qBAAsB,CACtB,qBAAuB,CACvB,qBACJ,CACA,0EAGI,kBAAmB,CAFnB,YAAa,CACb,6BAEF,CAEF,2EAII,kBAAmB,CAEnB,UAAc,CAHd,YAAa,CAFb,cAAe,CAIf,eAAgB,CAIhB,gBAAiB,CAPjB,iBAAkB,CAMlB,WAAY,CADZ,eAGJ,CACA,8EAEI,UAAc,CADd,eAAgB,CAEhB,WACJ,CAGA,2EAEI,gBAAiB,CADjB,UAEJ,CACA,iFAGI,0BAA4B,CAD5B,2BAA6B,CAD7B,gBAGJ,CAEA,mMAKI,wBAAyB,CAHzB,kBAAmB,CAEnB,cAAe,CADf,WAGJ,CACA,6FAII,kBAAmB,CAFnB,cAAe,CADf,WAIJ,CAEA,sDAKI,qBAAqC,CAArC,oCAAqC,CADrC,iBAAkB,CAFlB,WAAY,CACZ,mBAAqB,CAFrB,UAKJ,CACA,oFAEI,iBAAkB,CADlB,SAEJ,CACA,4EAEI,WAAY,CADZ,iBAEJ,CACA,kDACI,8BAA+B,CAC/B,aACJ,CACA,+DACI,wCACJ,CAIA,yBACA,WACA,CAOA,qBACI,sBACJ,CACA,eAII,yBAA2B,CAH3B,kBAAoB,CACpB,SAAU,CACV,yBAEJ,CACA,iCACI,aAAc,CACd,yBAA2B,CAG3B,eAAgB,CADhB,UAAW,CADX,qBAGJ,CACA,qCACI,wBACJ,CACA,mCACI,oBACJ,CACA,4BAGI,sBAAuB,CADvB,YAAa,CADb,UAIJ,CACA,iBACI,GAEI,6BAAuC,CADvC,kBAEJ,CACA,IAEI,6BAAuC,CADvC,oBAEJ,CACA,GAEI,6BAAuC,CADvC,kBAEJ,CACJ,CAEA,iBACI,6BACJ,CACA,mBACI,uBACJ,CACA,mBACI,0BACJ,CAEA,sBAGI,aAA0B,CAA1B,yBAA0B,CAF1B,aAAc,CACd,eAEF,CAEA,yCAGE,aAA0B,CAA1B,yBAA0B,CAF1B,aAAc,CACd,wBAEF,CAEA,kBACE,yBACJ,CACA,qCACI,aAAc,CACd,YAAa,CAChB,uBACE,kBAAmB,CACnB,aAAc,CACd,YACF,CACD,CAEA,0CACI,8BACJ,CAEA,eAEI,oBAAqB,CACrB,WAAY,CAFZ,iBAAkB,CAGlB,UACF,CAGA,qBAGE,QAAS,CAFT,SAAU,CACV,OAEF,CAGA,uBAOE,qBAAsB,CAEtB,kBAAmB,CAHnB,QAAS,CAJT,cAAe,CAEf,MAAO,CAMP,kBAAoB,CATpB,iBAAkB,CAIlB,OAAQ,CAFR,KAAM,CAKN,cAGF,CAGA,8BAOE,qBAAuB,CAEvB,iBAAkB,CAHlB,UAAW,CAIX,8BAAqC,CARrC,UAAW,CACX,WAAY,CAEZ,MAAS,CAJT,iBAAkB,CAOlB,cAAgB,CAJhB,UAOF,CAGA,qCACE,wBAAqC,CAArC,oCACF,CAEA,4CACE,0BACF,CAGA,mCACE,0BAAuC,CAAvC,sCACF,CAGA,sCAEE,kBAAmB,CADnB,UAEF,CACA,kBAKE,eAA8B,CAH9B,kCAAoC,CAEpC,iBAAkB,CAIlB,iBAAkB,CAFlB,YAAa,CALb,0BAA2B,CAM3B,eAAgB,CAJhB,4BAMF,CACA,8BACE,kCACF,CAEA,4BAME,0BAAoC,CADpC,YAAa,CAFb,MAAS,CAFT,cAAe,CACf,KAAQ,CAER,WAAY,CAGZ,aACF,CAEA,cACE,iBACF,CACA,qBACE,0BAA2B,CAO5B,+BAAgC,CADhC,4BAA6B,CAL5B,UAAW,CAEX,aAAc,CAEd,WAAY,CAHZ,iBAAkB,CAElB,SAIF,CAGA,uBAEE,sBAAuB,CADvB,YAEF,CAEA,qBAIE,wBAAyB,CAFzB,kBAAmB,CADnB,YAAa,CAEb,eAAgB,CAEhB,iBACF,CAEA,mBAGE,kBAAmB,CAEnB,qBAAuB,CAHvB,iBAAkB,CAElB,cAAe,CAEf,UAAW,CALR,WAML,CAEA,4BACE,kCAAgD,CAAhD,8CACF,CAEA,kBAGE,wBAAyB,CACzB,iBAAkB,CAFlB,WAAY,CADZ,UAIF,CAEA,mBAGE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CAJf,WAAY,CAGZ,eAAgB,CAJhB,UAMF,CAEF,kBAOI,oBAAqB,CACrB,2BAA4B,CAC5B,mBAAoB,CARpB,cAAe,CACf,eAAgB,CAChB,QAAW,CAEX,eAAgB,CAChB,sBAAuB,CAFvB,qBAMJ,CAGA,oBAMI,iDAA6D,CAL7D,kBAAmB,CAGnB,UAAY,CADZ,aAAc,CADd,YAAa,CAGb,eAEJ,CAEA,MAgBI,sDAAwD,CAdxD,eAAgB,CAUhB,yBAA6B,CAF7B,+BAAyC,CAKzC,UAAW,CATX,wBAA0B,CAH1B,eAAiB,CAWjB,WAAY,CAGZ,mBAAqB,CAZrB,gBAAiB,CAQjB,eAAgB,CANhB,iBAAkB,CAElB,UAAW,CALX,iBAAkB,CAIlB,QAAS,CAPT,UAAW,CAUX,UAOJ,CACE,qBACE,GAEE,4BAAyC,CADzC,kBAGF,CACA,IAEE,4BAAyC,CADzC,qBAIF,CACA,GAEE,4BAAyC,CADzC,kBAIF,CACF,CAGF,gBAGI,eAAgB,CADhB,kBAAmB,CADnB,uBAGJ,CAEA,cAEI,kBAAmB,CAEnB,qBAAuB,CAKvB,WAAY,CAJZ,kBAAmB,CAGnB,cAAe,CAPf,YAAa,CAMb,eAAgB,CADhB,gBAAiB,CAHjB,oBAAqB,CAQrB,oCAAsC,CADtC,UAEJ,CAEA,oBACI,wBACJ,CAEA,YAEI,kBAAmB,CADnB,YAAa,CAEb,sBAEJ,CAEA,gCACI,YAAyB,CAAzB,wBACJ,CAKA,YAEI,aAAc,CADd,cAEJ,CAEA,YASI,oBAAqB,CACrB,2BAA4B,CAT5B,aAAc,CAKd,mBAAoB,CAHpB,cAAe,CACf,aAAa,CAFb,eAAgB,CAGhB,kBAAmB,CAEnB,eAAgB,CAChB,sBAAuB,CAGvB,qBAEF,CACA,wBACE,2BAA4B,CAC5B,4BACF,CAEA,cAYM,kBAAmB,CAR3B,iDAA0C,CAA1C,yCAA0C,CAD1C,oBAAiC,CADjC,yBAA2B,CADvB,kBAAmB,CAcf,cAAe,CAJnB,YAAa,CAGT,MAAO,CAPX,WAAY,CAFhB,iBAAkB,CAQV,oBAAqB,CAJzB,cAAe,CACf,KAAM,CAJV,uBAAwB,CAEpB,eAQJ,CACA,8BACI,uBAEJ,CACA,qBAGI,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,SAAU,CAJV,UAAW,CAKX,UAEJ,CAEA,yBACI,YACJ,CACA,wJAII,SAAU,CACV,WACJ,CAGA,gCACI,2BAKJ,CACA,mEAFI,kBAAmB,CAHnB,YAAc,CACd,kBAAmB,CACnB,QAQJ,CACA,8BAEI,UAAW,CADX,iBAAkB,CAElB,yBACJ,CACA,8CACI,wBACJ,CACA,6BACI,UAAW,CAIX,cAAe,CADf,eAAgB,CADhB,gBAAiB,CADjB,yBAIJ,CAEA,4BACI,kCACJ,CACA,uEACI,YACJ,CACA,4BACI,kCACJ,CACA,iGACI,YACJ,CAEA,0BAGE,yBACF,CACA,+BAII,qBAAuB,CAHvB,yBAA2B,CAC3B,uBAAyB,CACzB,sBAEF,CAEA,uBAEE,yCAA2C,CAD3C,4CAEF,CAiBF,yFACI,wBACJ,CAEA,wBAKI,eAAgB,CAJhB,wBAAyB,CACzB,iBAAkB,CAElB,WAAY,CADZ,UAGJ,CAEA,6BACI,WACJ,CAEA,gBACI,uBACJ,CAEA,0BACI,wBACJ,CACA,gCACI,2BACJ,CACA,YACI,sBACF,CAGA,aAOE,kBAAmB,CAGnB,0BAAoC,CALpC,QAAS,CACT,YAAa,CAEb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAON,WAEF,CAEA,oCAGE,qBAAuB,CAEvB,wBAAyB,CACzB,kBAAmB,CAFnB,+BAAyC,CAFzC,eAAgB,CADhB,UAMF,CAEA,gCAEE,iBAAkB,CADlB,eAEF,CAEA,mCAEE,kBAAmB,CADnB,YAEF,CAEA,0BACE,cAAe,CACf,eACF,CAEA,+BAOE,kBAAmB,CANnB,wBAAyB,CACzB,iBAAkB,CAIlB,YAAa,CAHb,cAAe,CAEf,WAAY,CAGZ,sBAAuB,CACvB,eAAgB,CALhB,UAMF,CAEA,6BAEE,aAAc,CADd,cAAe,CAGf,mBAAqB,CADrB,kBAEF,CAEA,kCACE,YAAa,CACb,kBAAmB,CAEnB,QAAS,CADT,sBAAuB,CAEvB,iBACF,CAEA,iCAQE,eAAgB,CADhB,yBAA2B,CAF3B,iBAAkB,CADlB,cAAe,CAFf,YAAa,CAIb,iBAAkB,CAHlB,iBAAkB,CAMlB,8BAAgC,CARhC,WASF,CAEA,mCAEE,wBAAyB,CADzB,oBAEF,CAEA,iCACE,cAAe,CACf,eAAgB,CAEhB,WAAY,CADZ,gBAEF,CAEA,kCACE,cAAe,CACf,eAAgB,CAChB,kBACF,CAEA,wCACE,cAAe,CAEf,mBAAqB,CADrB,gBAAiB,CAEjB,iBACF,CAEA,kCACE,iBACF,CAEA,gCAEE,wBAAyB,CAKzB,WAAY,CAHZ,iBAAkB,CADlB,UAAW,CAKX,cAAe,CAHf,cAAe,CACf,eAAgB,CALhB,gBAAiB,CAQjB,oCACF,CAEA,6BAEE,kBAAmB,CADnB,UAEF,CAIA,8BACE,aAAc,CAId,cAAe,CAHf,eAAgB,CAIhB,eAAgB,CAHhB,sBAAuB,CACvB,kBAAmB,CAGnB,WACF,CACA,iBAEE,WAAY,CAGZ,SAAU,CAJV,iBAAkB,CAGlB,UAAW,CADX,cAGF,CAEA,gBACA,gEACkD,CAKlD,0BAAkB,CAClB,iBAAkB,CALlB,SAMF,CACE,8BAED,8BAA+B,CAC/B,eAAgB,CAFb,SAGF,CACA,iCAKE,+BAAgC,CADhC,cAAe,CAHf,YAAa,CACb,qBAAsB,CACtB,2BAGF,CACA,eAGE,yBAA2B,CAC3B,qBAAsB,CAEtB,kBAAmB,CAJnB,8BAA2C,CAM3C,WAAY,CADZ,qBAAuB,CANvB,2BAA6B,CAS7B,WAAY,CADZ,SAAU,CAJV,WAMF,CAuBA,oBAEE,sBAAuB,CADvB,iBAAkB,CAElB,cACF,CAEA,yBACE,oBAAqB,CACrB,UACF,CAEA,0BAEE,SAAU,CADV,qBAEF,CACA,iCACE,2BAA4B,CAC5B,iBACF,CAEA,kCACE,gBAAiB,CACjB,4BACF,CACA,cACE,aAAc,CACb,cAEH,CAGF,uCAJK,gBAML,CAEA,6CACE,UAAW,CACX,WACF,CAEA,mDACE,0BACF,CACA,0BAEE,cAAe,CACf,eAAgB,CAChB,YAAa,CAHb,iBAAkB,CAIlB,cACC,aAAyB,CAAzB,wBACD,CACA,iBACC,UAAW,CAEX,wBACC,aAAyB,CAAzB,wBAAyB,CACzB,gBACD,CACA,yCACC,2BACD,CACD,CACD,CAED,kDAOC,yBAAyC,CAAzC,uCAAyC,CAEzC,iBAAkB,CADlB,yBAA6B,CAF7B,wBAA0B,CAD1B,SAAU,CADV,SAAU,CADV,qBAAuB,CADvB,UAAW,CAQX,iBAEC,4BAA4C,CAA5C,2CAA4C,CAD5C,YAAa,CAEb,aACC,cAAe,CACf,eACD,CACD,CAEA,0BAEC,cAAe,CACf,eAAgB,CAChB,YAAa,CAHb,iBAAkB,CAIlB,cACC,aAAyB,CAAzB,wBACD,CACA,iBACC,UAAW,CAEX,wBACC,aAAyB,CAAzB,wBAAyB,CACzB,gBACD,CACA,yCACC,2BACD,CACD,CACD,CACA,KACC,yBACD,CACA,uBAEC,yBAAyC,CAAzC,wCAAyC,CADzC,YAAa,CAEb,cAAe,CACf,gBACC,uCAAwC,CACxC,UAAyB,CAAzB,wBAAyB,CAGzB,4BAAgD,CAAhD,8CAAgD,CADhD,yBAAyC,CAAzC,uCAAyC,CADzC,yBAA0B,CAG1B,UACC,wBAAyB,CACzB,mBACD,CACD,CACD,CACD,CACA,2CAEE,kBAAmB,CADnB,YAAa,CAGb,IACE,WAAY,CACZ,UACF,CACF,CC5rDE,gCAEC,oBADA,iBACA,CACA,0CAEC,yCADA,qCACA,CAED,sDACC,yBAKF,yCACC,YAED,8CACC,2BAID,wDACC,oBACA,WACA,yBAG8f,+DACC,0BAGD,+DAC9f,2BAMC,kEACE,iBACA,yBAGF,qQAGC,qBAMD,yEACC,iBAED,+DAGC,0BAFA,oBACA,UACA,CAED,qEAEC,2BADA,wBACA,CAKJ,6CAEC,iBADA,cACA,CAGD,0BAEC,2BADA,4BACA,CAEC,wNAGC,2BAIF,+CACC,gBACA,yBAID,0CACC,kBACA,mBACA,uDAMC,wBADA,4BAHA,wBADA,iBAGA,yBADA,kBAGA,CAED,yDACC,2BACA,iBACA,oEACC,yBAIH,sFAEC,iBADA,wBACA,CAED,2CACC,+BACA,kBACA,mBAGF,wBAEC,oBADA,UACA,CACA,sCACC,qBACA,qBAED,yCAEC,qBADA,oBACA,CAED,yCACC,qBACA,qBAKK,yJACJ,gBACA,kBAGJ,2DAEE,gBADA,wBACA,CAGF,0DACE,2BAEC,+EACC,iBAGI,sFACI,iBACA,0BAER,gFACC,iBACA,eAED,gFACC,cACA,kBACA,mGACC,kBACA,gBAIF,4EACC,iBACA,0BAKH,uEACC,yBAID,mDACC,UACA,qBAID,2DAEC,wBADA,gBACA,CAED,yDACC,iBAGF,mCACC,2BAED,sBAEC,UADA,UACA,CAcD,mBAEC,oBADA,OACA,CAEC,qCACC,2BACA,yBAKD,qFACC,yBAED,mCACC,iBACA,yBAGF,kCAEC,wBADA,iBACA,CAGF,oBACC,qBACA,qBAGA,iDACC,wBAED,mCACC,2BAGF,eACC,WACA,WAED,mBAEC,cADA,mBACA,CAGA,2DAEC,2BADA,0BACA,CAED,gIAEC,cAED,6DACC,cACA,kBAGF,kBACC,wBAGF,4DAEI,MAAK,CADL,UACA,CAqCJ,gDACK,gBACH,yBAEA,0DACD,sBAcA,8EACC,iCAED,qDACC,2BAIA,0CACC,4BACA,4BACA,2BAGD,wCAEC,SADA,UACA,CAGF,gEACG,wBACA,2BAEJ,wBAEC,oBADA,OACA,CAED,yEACC,qBAED,sCACC,2BAEA,uCACG,oBACH,qBCpXD,kBAMI,kBAAmB,CAGnB,wBAAyB,CACzB,WAAY,CACZ,iBAAkB,CAKlB,8BAAyC,CAHzC,UAAY,CADZ,cAAe,CAPf,YAAa,CAEb,OAAQ,CACR,iBAAkB,CAPlB,cAAe,CACf,UAAW,CACX,OAAQ,CACR,0BAA2B,CAW3B,+BAAiC,CADjC,YAGF,CAEA,wBACE,wBACF,CAEA,sBAEE,oBACF,CAMA,gBAEE,WAAY,CADZ,UAEF,CAEA,gBACE,cAAe,CACf,eACF,CCtCF,sCAGI,kBAAmB,CAFnB,YAAa,CAKb,cAAe,CAFf,WAAY,CACZ,cAEJ,CAEA,wCAEI,kBAAmB,CAGnB,cAAe,CAJf,YAAa,CAGb,cAAc,CADd,OAGJ,CACA,qBAMI,kBAAmB,CAHnB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAGZ,0BAA2B,CAG3B,QAAS,CACT,aAAa,CAFb,SAAU,CANV,UASJ,CACA,oBAKI,oCAAqC,CADrC,iBAAkB,CAFlB,WAAY,CACZ,mBAAqB,CAFrB,UAKJ,CAEA,kDAEI,iBAAkB,CADlB,SAEJ,CACA,0CAEI,WAAY,CADZ,iBAEJ,CCzCA,IAGE,uBAAwB,CAFxB,yBAA2B,CAC3B,oBAAqB,CAErB,iBAAkB,CAClB,qBCCF,CDKA,YAOE,QAAW,CAFX,WCKF,CDEA,wBAXE,YAAa,CACb,SAAU,CAOV,iBAAkB,CANlB,yDAA2D,CAC3D,iECkBF,CDVA,YAOE,OAAQ,CAFR,UCKF,CDEA,oDAGE,wBAA6B,CAD7B,aCEF,CDEA,oJAME,UCCF,CDEA,kJAME,qBAAsB,CACtB,UCCF,CDKA,aAOE,UAAW,CAFX,UAAW,CAFX,6DAA+D,CAC/D,qECMF,CDEA,0BAXE,qBAAsB,CACtB,iBAAkB,CAOlB,iBCaF,CDVA,aAOE,SAAU,CAJV,4DAA8D,CAC9D,oEAAsE,CACtE,SCKF,CDEA,oGAGE,qBAAsB,CACtB,WCCF,CDEA,oGAGE,qBAAsB,CACtB,UCCF,CDGA,oCACE,IACE,uBCCF,CACF,CDEA,sEACE,IACE,uBCCF,CACF,CCjHA,qBAEE,WAAY,CADZ,iBACY,CCJd,oBAEE,eAAgB,CADd,UAEJ,CACA,qBACI,eAAgB,CAEhB,YAAa,CADb,eAEJ,CACA,gBACI,eAAgB,CAChB,mBACJ,CACA,gBACI,cAAe,CACb,eAAgB,CAChB,eACN,CACA,cAOE,6CAA+C,CAH/C,8CAAgD,CAChD,2BAA6B,CAC7B,yBAA2B,CALzB,wBAA0B,CAC5B,yBAA2B,CAM3B,yBAA2B,CAL3B,sBAAwB,CAMxB,sCACF,CACA,kCAEI,qBAAuB,CADvB,qBAAuB,CAEvB,mBACJ,CACA,eACI,kCAAwC,CACxC,qBAAuB,CAEvB,kBAAoB,CADpB,mBAEJ,CACA,oBACI,YACJ,CAEA,mBACI,cACJ,CACA,gBACI,yBAA0B,CAG1B,eAAgB,CAChB,gBAAiB,CACjB,kBAAmB,CACnB,eACJ,CACA,0BAPI,cAAe,CACf,cAkBJ,CAZA,UACI,8CAAgD,CAEhD,WAAY,CAIZ,kBAAmB,CALnB,UAAW,CAQX,gBAAiB,CADjB,eAAgB,CALhB,iBAAkB,CAOlB,mBAAoB,CAHpB,UAIJ,CCpEA,aAMC,eAAgB,CAChB,kBAAmB,CAJnB,WAAY,CAEZ,WAAY,CADZ,gBAAiB,CAHjB,iBAAkB,CAClB,UAMD,CAEA,oBAGI,UAAW,CAFX,cAAe,CACf,eAGJ,CAQA,oBACC,eACD,CACA,8BACC,eACD,CACA,0BACC,eACD,CACA,wBACC,eACD,CACA,kCACC,eACD,CACA,+BACC,eACD,CACA,+BACC,eACD,CACA,sCACC,eACD,CACA,iCACC,eACD,CACA,iCACC,eACD,CACA,2BACC,eACD,CACA,iCACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,yBACC,eACD,CACA,+BACC,eACD,CACA,mBACC,eACD,CACA,eACC,eACD,CACA,kBACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,6BACC,eACD,CACA,uBACC,eACD,CACA,+BACC,eACD,CACA,4BACC,eACD,CACA,0BACC,eACD,CACA,wBACC,eACD,CACA,gCACC,eACD,CACA,sCACC,eACD,CACA,eACC,eACD,CACA,6BACC,eACD,CACA,4BACC,eACD,CACA,sCACC,eACD,CACA,0BACC,eACD,CACA,yBACC,eACD,CACA,6BACC,eACD,CACA,mCACC,eACD,CACA,eACC,eACD,CACA,uBACC,eACD,CACA,4BACC,eACD,CACA,gCACC,eACD,CACA,qCACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,mCACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,gCACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,eACC,eACD,CACA,sBACC,eACD,CACA,oCACC,eACD,CACA,sBACC,eACD,CACA,oCACC,eACD,CACA,2BACC,eACD,CACA,wBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,yBACC,eACD,CACA,oBACC,eACD,CACA,0BACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,0BACC,eACD,CACA,kBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,4BACC,eACD,CACA,iBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,uBACC,eACD,CACA,+BACC,eACD,CACA,wBACC,eACD,CACA,kCACC,eACD,CACA,mBACC,eACD,CACA,8BACC,eACD,CACA,uCACC,eACD,CACA,2BACC,eACD,CACA,wBACC,eACD,CACA,2BACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,kBACC,eACD,CACA,wBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,kCACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,0BACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,iBACC,eACD,CACA,eACC,eACD,CACA,4BACC,eACD,CACA,2BACC,eACD,CACA,wBACC,eACD,CACA,oBACC,eACD,CACA,yBACC,eACD,CACA,6BACC,eACD,CACA,wBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,6BACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,8BACC,eACD,CACA,8BACC,eACD,CACA,0BACC,eACD,CACA,oBACC,eACD,CACA,0BACC,eACD,CACA,2BACC,eACD,CACA,oBACC,eACD,CACA,8BACC,eACD,CACA,oCACC,eACD,CACA,+BACC,eACD,CACA,kBACC,eACD,CACA,+BACC,eACD,CACA,0BACC,eACD,CACA,2BACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,4BACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,yBACC,eACD,CACA,mBACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,yBACC,eACD,CACA,0BACC,eACD,CACA,kBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,gBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,wBACC,eACD,CACA,kBACC,eACD,CACA,4BACC,eACD,CACA,2BACC,eACD,CACA,gBACC,eACD,CACA,4BACC,eACD,CACA,kBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,iCACC,eACD,CACA,wBACC,eACD,CACA,oBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,wBACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,8BACC,eACD,CACA,0BACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,qBACC,eACD,CACA,2BACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,gBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,4BACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,gCACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,+BACC,eACD,CACA,2BACC,eACD,CACA,2BACC,eACD,CACA,iCACC,eACD,CACA,0BACC,eACD,CACA,2BACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,2BACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,4BACC,eACD,CACA,uBACC,eACD,CACA,2BACC,eACD,CACA,8BACC,eACD,CACA,gCACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,4BACC,eACD,CACA,mBACC,eACD,CACA,iCACC,eACD,CACA,+BACC,eACD,CACA,wBACC,eACD,CACA,+BACC,eACD,CACA,gCACC,eACD,CACA,0BACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,4BACC,eACD,CACA,8BACC,eACD,CACA,gCACC,eACD,CACA,qCACC,eACD,CACA,mBACC,eACD,CACA,6BACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,6BACC,eACD,CACA,wCACC,eACD,CACA,uBACC,eACD,CACA,gCACC,eACD,CACA,yBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,cACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,eACC,eACD,CACA,gBACC,eACD,CACA,iBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,mBACC,eACD,CACA,eACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,gBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,kBACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,kBACC,eACD,CACA,yBACC,eACD,CACA,oBACC,eACD,CACA,gBACC,eACD,CACA,mBACC,eACD,CACA,wBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,2BACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,eACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,gBACC,eACD,CACA,iBACC,eACD,CACA,oBACC,eACD,CACA,8BACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,eACC,eACD,CACA,qBACC,eACD,CACA,gCACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,kBACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,kBACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,eACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,eACC,eACD,CACA,eACC,eACD,CACA,oBACC,eACD,CACA,uBACC,eACD,CACA,4BACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,2BACC,eACD,CACA,6BACC,eACD,CACA,8BACC,eACD,CACA,2BACC,eACD,CACA,6BACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,gBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,eACC,eACD,CACA,gBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,kBACC,eACD,CACA,gBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,gBACC,eACD,CACA,iBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,0BACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,gBACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,uBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,uBACC,eACD,CACA,6BACC,eACD,CACA,8BACC,eACD,CACA,6BACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,eACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,0BACC,eACD,CACA,oBACC,eACD,CACA,gBACC,eACD,CACA,0BACC,eACD,CACA,gBACC,eACD,CACA,uBACC,eACD,CACA,gBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,4BACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,6BACC,eACD,CACA,kBACC,eACD,CACA,+BACC,eACD,CACA,gCACC,eACD,CACA,6BACC,eACD,CACA,+BACC,eACD,CACA,gBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,wBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,gCACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,6BACC,eACD,CACA,2BACC,eACD,CACA,8BACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,6BACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,eACC,eACD,CACA,qBACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,2BACC,eACD,CACA,4BACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,gBACC,eACD,CACA,eACC,eACD,CACA,gBACC,eACD,CACA,mBACC,eACD,CACA,eACC,eACD,CACA,qBACC,eACD,CACA,6BACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,2BACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,kBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,eACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,eACC,eACD,CACA,gBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,0BACC,eACD,CACA,mBACC,eACD,CACA,eACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,2BACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,iBACC,eACD,CACA,cACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,cACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,mBACC,eACD,CACA,eACC,eACD,CACA,qBACC,eACD,CACA,6BACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,2BACC,eACD,CACA,mBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,kBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,eACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,kCACC,eACD,CACA,wBACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,yBACC,eACD,CACA,oBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,0BACC,eACD,CACA,iBACC,eACD,CACA,yBACC,eACD,CACA,2BACC,eACD,CACA,0BACC,eACD,CACA,yBACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,4BACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,cACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,yBACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,4BACC,eACD,CACA,iBACC,eACD,CACA,6BACC,eACD,CACA,mBACC,eACD,CACA,uCACC,eACD,CACA,+CACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,4BACC,eACD,CACA,kBACC,eACD,CACA,mBACC,eACD,CACA,0BACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,+BACC,eACD,CACA,8BACC,eACD,CACA,eACC,eACD,CACA,kBACC,eACD,CACA,4BACC,eACD,CACA,mBACC,eACD,CACA,yBACC,eACD,CACA,yBACC,eACD,CACA,+BACC,eACD,CACA,+BACC,eACD,CACA,gCACC,eACD,CACA,6BACC,eACD,CACA,oBACC,eACD,CACA,0BACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,mBACC,eACD,CACA,cACC,eACD,CACA,cACC,eACD,CACA,cACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,4BACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,6BACC,eACD,CACA,uBACC,eACD,CACA,8BACC,eACD,CACA,+BACC,eACD,CACA,+BACC,eACD,CACA,gCACC,eACD,CACA,6BACC,eACD,CACA,yBACC,eACD,CACA,gBACC,eACD,CACA,qBACC,eACD,CACA,+BACC,eACD,CACA,+BACC,eACD,CACA,gCACC,eACD,CACA,6BACC,eACD,CACA,6BACC,eACD,CACA,6BACC,eACD,CACA,8BACC,eACD,CACA,2BACC,eACD,CACA,sBACC,eACD,CACA,gCACC,eACD,CACA,iBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,6BACC,eACD,CACA,6BACC,eACD,CACA,8BACC,eACD,CACA,2BACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,0BACC,eACD,CACA,wBACC,eACD,CACA,6BACC,eACD,CACA,2BACC,eACD,CACA,4BACC,eACD,CACA,0BACC,eACD,CACA,iCACC,eACD,CACA,+BACC,eACD,CACA,gCACC,eACD,CACA,8BACC,eACD,CACA,+BACC,eACD,CACA,6BACC,eACD,CACA,8BACC,eACD,CACA,4BACC,eACD,CACA,uBACC,eACD,CACA,iCACC,eACD,CACA,iCACC,eACD,CACA,kCACC,eACD,CACA,gCACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,2BACC,eACD,CACA,wBACC,eACD,CACA,iCACC,eACD,CACA,iCACC,eACD,CACA,kCACC,eACD,CACA,+BACC,eACD,CACA,6BACC,eACD,CACA,oCACC,eACD,CACA,wBACC,eACD,CACA,kBACC,eACD,CACA,2BACC,eACD,CACA,oBACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,eACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,mBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,mBACC,eACD,CACA,4BACC,eACD,CACA,sBACC,eACD,CACA,8BACC,eACD,CACA,4BACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,kBACC,eACD,CACA,6BACC,eACD,CACA,6BACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,0BACC,eACD,CACA,oBACC,eACD,CACA,eACC,eACD,CACA,wBACC,eACD,CACA,0BACC,eACD,CACA,wBACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,8BACC,eACD,CACA,0BACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,4BACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,oBACC,eACD,CACA,yBACC,eACD,CACA,2BACC,eACD,CACA,2BACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,4BACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,2BACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,wBACC,eACD,CACA,4BACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,uBACC,eACD,CACA,2BACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,2BACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,eACC,eACD,CACA,qBACC,eACD,CACA,iBACC,eACD,CACA,6BACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,2BACC,eACD,CACA,0BACC,eACD,CACA,yBACC,eACD,CACA,6BACC,eACD,CACA,qBACC,eACD,CACA,eACC,eACD,CACA,iBACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,6BACC,eACD,CACA,+BACC,eACD,CACA,wBACC,eACD,CACA,4BACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,4BACC,eACD,CACA,0BACC,eACD,CACA,wBACC,eACD,CACA,2BACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,kBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,6BACC,eACD,CACA,4BACC,eACD,CACA,4BACC,eACD,CACA,mCACC,eACD,CACA,6BACC,eACD,CACA,6BACC,eACD,CACA,4BACC,eACD,CACA,6BACC,eACD,CACA,6BACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,+BACC,eACD,CACA,yBACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,yBACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,6BACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,gBACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,8BACC,eACD,CACA,iCACC,eACD,CACA,4BACC,eACD,CACA,8BACC,eACD,CACA,iBACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,oBACC,eACD,CACA,+BACC,eACD,CACA,+BACC,eACD,CACA,kCACC,eACD,CACA,+BACC,eACD,CACA,+BACC,eACD,CACA,mCACC,eACD,CACA,kCACC,eACD,CACA,qCACC,eACD,CACA,0CACC,eACD,CACA,kCACC,eACD,CACA,oBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,8BACC,eACD,CACA,+BACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,2BACC,eACD,CACA,iBACC,eACD,CACA,sBACC,eACD,CACA,8BACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,gBACC,eACD,CACA,iBACC,eACD,CACA,gBACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,kBACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,mBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,wBACC,eACD,CACA,8BACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,2BACC,eACD,CACA,kBACC,eACD,CACA,gCACC,eACD,CACA,2BACC,eACD,CACA,+BACC,eACD,CACA,uBACC,eACD,CACA,2BACC,eACD,CACA,qBACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,2BACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,gBACC,eACD,CACA,iBACC,eACD,CACA,oBACC,eACD,CACA,wBACC,eACD,CACA,oBACC,eACD,CACA,0BACC,eACD,CACA,kBACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,8BACC,eACD,CACA,iBACC,eACD,CACA,4BACC,eACD,CACA,gBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,+BACC,eACD,CACA,6BACC,eACD,CACA,4BACC,eACD,CACA,uBACC,eACD,CACA,gBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,gBACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,6BACC,eACD,CACA,wBACC,eACD,CACA,0BACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,2BACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,6BACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,8BACC,eACD,CACA,4BACC,eACD,CACA,qBACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,0BACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,gBACC,eACD,CACA,qBACC,eACD,CACA,2BACC,eACD,CACA,iBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,4BACC,eACD,CACA,oBACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,oBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,iBACC,eACD,CACA,0BACC,eACD,CACA,yBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,uBACC,eACD,CACA,6BACC,eACD,CACA,gBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,uBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,4BACC,eACD,CACA,kCACC,eACD,CACA,yBACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,eACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,0BACC,eACD,CACA,iBACC,eACD,CACA,gCACC,eACD,CACA,4BACC,eACD,CACA,2BACC,eACD,CACA,kCACC,eACD,CACA,4BACC,eACD,CACA,+BACC,eACD,CACA,4BACC,eACD,CACA,4BACC,eACD,CACA,4BACC,eACD,CACA,sBACC,eACD,CACA,8BACC,eACD,CACA,mBACC,eACD,CACA,0BACC,eACD,CACA,yBACC,eACD,CACA,kBACC,eACD,CACA,0BACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,cACC,eACD,CACA,kBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,iBACC,eACD,CACA,0BACC,eACD,CACA,4BACC,eACD,CACA,yBACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,gBACC,eACD,CACA,0BACC,eACD,CACA,+BACC,eACD,CACA,2BACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,qBACC,eACD,CACA,gCACC,eACD,CACA,8BACC,eACD,CACA,2BACC,eACD,CACA,0BACC,eACD,CACA,4BACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,yBACC,eACD,CACA,qBACC,eACD,CACA,mBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,oBACC,eACD,CACA,wBACC,eACD,CACA,gBACC,eACD,CACA,0BACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,4BACC,eACD,CACA,iCACC,eACD,CACA,wBACC,eACD,CACA,2BACC,eACD,CACA,qBACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,kBACC,eACD,CACA,cACC,eACD,CACA,iBACC,eACD,CACA,0BACC,eACD,CACA,iBACC,eACD,CACA,cACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,iBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,qCACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,eACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,eACC,eACD,CACA,yBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,eACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,gBACC,eACD,CACA,eACC,eACD,CACA,yBACC,eACD,CACA,0BACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,eACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,qBACC,eACD,CACA,0BACC,eACD,CACA,gBACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,uBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,8BACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,mBACC,eACD,CACA,yBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,wBACC,eACD,CACA,gBACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,gBACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,iBACC,eACD,CACA,eACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,eACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,wBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,kBACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,gCACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,4BACC,eACD,CACA,0BACC,eACD,CACA,2BACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,yBACC,eACD,CACA,+BACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,kBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,oBACC,eACD,CACA,oBACC,eACD,CACA,eACC,eACD,CACA,uBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,yBACC,eACD,CACA,qBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,+BACC,eACD,CACA,sBACC,eACD,CACA,kBACC,eACD,CACA,yBACC,eACD,CACA,kBACC,eACD,CACA,yBACC,eACD,CACA,mBACC,eACD,CACA,+BACC,eACD,CACA,sBACC,eACD,CACA,gBACC,eACD,CACA,wBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,yBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,uBACC,eACD,CACA,iBACC,eACD,CACA,2BACC,eACD,CACA,2BACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,+BACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,+BACC,eACD,CACA,kBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,mBACC,eACD,CACA,iBACC,eACD,CACA,0BACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,mBACC,eACD,CACA,4BACC,eACD,CACA,oBACC,eACD,CACA,yBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,kBACC,eACD,CACA,eACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,iBACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,wBACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,oBACC,eACD,CACA,0BACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,yBACC,eACD,CACA,kBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,0BACC,eACD,CACA,0BACC,eACD,CACA,+BACC,eACD,CACA,2BACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,eACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,2BACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,8BACC,eACD,CACA,oBACC,eACD,CACA,2BACC,eACD,CACA,6BACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,qBACC,eACD,CACA,2BACC,eACD,CACA,0BACC,eACD,CACA,mBACC,eACD,CACA,gBACC,eACD,CACA,iBACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,iBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,gBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,yBACC,eACD,CACA,kBACC,eACD,CACA,kBACC,eACD,CACA,kBACC,eACD,CACA,sBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,6BACC,eACD,CACA,sBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,yBACC,eACD,CACA,4BACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,2BACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,wBACC,eACD,CACA,4BACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,uBACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,gBACC,eACD,CACA,gBACC,eACD,CACA,wBACC,eACD,CACA,mBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,eACC,eACD,CACA,gBACC,eACD,CACA,uBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,0BACC,eACD,CACA,wBACC,eACD,CACA,cACC,eACD,CACA,oBACC,eACD,CACA,2BACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,wBACC,eACD,CACA,uBACC,eACD,CACA,4BACC,eACD,CACA,gCACC,eACD,CACA,eACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,oBACC,eACD,CACA,gCACC,eACD,CACA,0BACC,eACD,CACA,oBACC,eACD,CACA,gBACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,wBACC,eACD,CACA,2BACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,iBACC,eACD,CACA,4BACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,iBACC,eACD,CACA,mBACC,eACD,CACA,mBACC,eACD,CACA,uBACC,eACD,CACA,kBACC,eACD,CACA,qBACC,eACD,CACA,wBACC,eACD,CACA,2BACC,eACD,CACA,yBACC,eACD,CACA,gBACC,eACD,CACA,oBACC,eACD,CACA,uBACC,eACD,CACA,6BACC,eACD,CACA,yBACC,eACD,CACA,4BACC,eACD,CACA,4BACC,eACD,CACA,mBACC,eACD,CACA,wBACC,eACD,CACA,sBACC,eACD,CACA,sBACC,eACD,CACA,yBACC,eACD,CACA,sBACC,eACD,CACA,iBACC,eACD,CACA,0BACC,eACD,CACA,iCACC,eACD,CACA,uBACC,eACD,CACA,sBACC,eACD,CACA,2BACC,eACD,CACA,mBACC,eACD,CACA,sBACC,eACD,CACA,2BACC,eACD,CACA,gCACC,eACD,CACA,wBACC,eACD,CACA,0BACC,eACD,CACA,oBACC,eACD,CACA,eACC,eACD,CACA,iBACC,eACD,CACA,iBACC,eACD,CACA,sBACC,eACD,CACA,4BACC,eACD,CACA,iBACC,eACD,CACA,qBACC,eACD,CACA,sBACC,eACD,CACA,uBACC,eACD,CACA,0BACC,eACD,CACA,qBACC,eACD,CACA,qBACC,eACD,CACA,kBACC,eACD,CACA,qBACC,eACD,CACA,oBACC,eACD,CACA,mBACC,eACD,CACA,qBACC,eACD,CACA,yBACC,eACD,CACA,oBACC,eACD,CACA,yBACC,eACD,CACA,2BACC,eACD,CACA,iBACC,eACD,CACA,yBACC,eACD,CACA,mBACC,eACD,CACA,oBACC,eACD,CACA,qBACC,eACD,CACA,mBACC,eACD,CACA,kBACC,eACD,CACA,yBACC,eACD,CACA,uBACC,eACD,CACA,mBACC,eACD,CCvlKA,MACC,8CAAkD,CAC/C,sBAAuB,CACvB,wBAAyB,CACzB,mBAAoB,CACpB,kBAAmB,CACnB,0BAA0B,CAC1B,qCACJ,CAIA,iHAFC,sDAA0C,CAA1C,wCAOD,CALA,KAGC,kCAAmC,CACnC,iCAAkC,CAHlC,QAID,CAEA,WACC,sBAAyB,CACzB,iBAGD,CAEA,WACC,wBAA2B,CAC3B,iBAAkB,CAClB,wNAGD,CAEA,WASC,kBAAmB,CARnB,wBAA2B,CAO3B,iBAAkB,CADlB,eAAmB,CALnB,4DAAyD,CACzD,uUAOD,CAEC,sBACC,eAAgB,CAEhB,QAAS,CADT,SAEF,CACC,iCAMC,wBAAyB,CAHzB,kBAAmB,CAEnB,cAAe,CAEf,8BAAgC,CALhC,aAAc,CADd,qBAA2B,CAG3B,oCAIF,CACA,uCAAwC,kCAAgD,CAAhD,8CAAiD,CAOxF,4CAGE,wBAA0B,CAC1B,4BAA+B,CAC/B,eACH,CACC,wDAIE,oBAAsB,CAHvB,wBAA0B,CAC1B,eAAgB,CAChB,iBAEF,CACC,8DAEC,aAAc,CADd,cAAe,CAEd,4BACH,CACC,wCAEC,kBAAmB,CADnB,YAAa,CAEZ,iBACH,CAEE,oBACE,UAAY,CACZ,mBACJ,CACA,kBACC,oBAAmC,CAClC,kCAAgD,CAAhD,8CACF,CAEA,qBAGE,kBAAmB,CADnB,yBAA0B,CAD1B,iBAAkB,CAGlB,uBACF,CACA,oCACE,kBACF,CACA,6BACE,kBAAiC,CAAjC,gCAAiC,CACjC,0BAA4B,CAC5B,YACF,CACA,YACE,cAAe,CACf,qBACF,CACA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,UAEF,CACA,iBACE,eAA8B,CAA9B,6BAA8B,CAC9B,kBAAmB,CACnB,SACF,CACA,wCAGE,wBAAyB,CADzB,kBAAmB,CADnB,WAGF,CACA,yCAEE,kBAAoB,CACpB,+BAAiC,CAEjC,0BACF,CACA,kDAEE,qBAAuB,CADvB,4BAEF,CACA,kBACE,YAIF,CACA,eAEA,kBAAmB,CADnB,YAEA,CACA,sBACA,wBAA0B,CACtB,yBAA2B,CAC3B,UAAY,CAChB,oBAUE,kBAAmB,CATnB,kCAAgD,CAAhD,8CAAgD,CAChD,qBAAuB,CACvB,4BAAqD,CAArD,mDAAqD,CACrD,oBAAoC,CAApC,kCAAoC,CACpC,wBAA0B,CAI1B,sBAAwB,CAHxB,wBAA0B,CAE1B,qBAAuB,CAGvB,eAAgB,CAJhB,sBAKF,CACA,sBACE,iBACF,CACA,yBACE,cAAe,CAEb,eAAgB,CAChB,sBAAuB,CAFrB,kBAGN,CACA,wBAEE,QAAS,CADT,gBAEF,CAEA,sBACE,4BAA6B,CAC7B,kBAGF,CACA,gCACE,kCACF,CACA,6BAEE,iBAAkB,CADnB,yBAGD,CACA,oCAGE,oBAAsB,CADtB,wBAA0B,CAD1B,yBAGF,CACA,yCACE,kCAAgD,CAAhD,8CACF,CACA,eACE,0BACF,CACA,iCACE,4CACF,CACA,gCACE,qBAAoC,CAApC,mCAAoC,CACpC,kBAA0C,CAA1C,yCACF,CACA,iCACE,WACF,CACA,0CACE,sCAAuC,CAEvC,8BAA+B,CAD/B,UAAY,CAEZ,qBACF,CACA,iCACE,kBACF,CAIA,yJACE,4BACF,CAGA,oGAGE,uBAAyB,CACzB,aAAc,CAFd,mBAGF,CASA,wMAGE,uBAAyB,CACzB,aAAc,CAFd,mBAMF,CAEA,wCAGE,kBAAmB,CAFnB,qBAAsB,CACtB,iBAAkB,CAElB,WAAY,CACZ,gBACA,CAKA,oDACE,iBAAkB,CACpB,WACD,CACC,kCAIE,4BAA4C,CAA5C,2CAA4C,CAH5C,wBAA0B,CAC1B,eAAgB,CAChB,sBAGH,CACA,2BAEC,yBAA0B,CAD1B,qBAED,CACC,kCAIE,UAAW,CAHX,2BAA6B,CAE7B,UAAW,CADX,QAGH,CACC,sCACE,wBACH,CACC,qCAEE,cAAe,CADf,eAEH,CACC,4CAGE,oBAAqB,CADrB,eAAgB,CADhB,sBAGH,CACA,4CAEC,yBAAyC,CAAzC,wCAAyC,CADzC,sBAED,CAEC,4DACE,wBAAqC,CAArC,oCAAqC,CACrC,iBAAkB,CAClB,oCAAqC,CACrC,6BAA8B,CAC9B,mCACH,CAKD,mBAEE,WAAY,CACZ,UAAW,CAFX,cAAe,CAGf,YACF,CAEA,UAGE,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAMZ,cAAe,CADf,cAAe,CADf,WAAY,CADZ,UAIF,CAEA,0BAVE,wBAAqC,CAArC,oCAYF,CAGA,YAcE,mBAAoB,CAVpB,qBAAuB,CACvB,qBAAsB,CAEtB,kBAAmB,CALnB,WAAY,CASZ,8BAAwC,CACxC,YAAa,CACb,qBAAsB,CAEtB,QAAS,CALT,YAAa,CAPb,SAAU,CAGV,YAAa,CALb,cAAe,CAQf,WAAY,CADZ,YAQF,CAEA,eAEE,qBAAsB,CACtB,kBAAmB,CAKnB,qBAAsB,CAHtB,cAAe,CAEf,YAAa,CAHb,YAAa,CAEb,WAAY,CAGZ,2BAA6B,CAR7B,UASF,CAEA,qBACE,oBAAiC,CAAjC,gCAAiC,CACjC,YACF,CAGA,WACE,8BACF,CAEA,iBAKE,iBAAkB,CADlB,YAAa,CAIb,oCACF,CAOA,iBAIE,mBAAoB,CADpB,UAAW,CADX,cAAe,CAGf,oBACF,CAEA,uBACE,aACF,CAGA,iBAIE,wBAA6B,CAC7B,aAAc,CAEd,cAAe,CADf,cAAe,CALf,iBAAkB,CAElB,SAAU,CADV,KAMF,CAEA,uBACE,UACF,CAEA,iBAGE,WAAY,CAIZ,iBAAkB,CALlB,UAAY,CAMZ,cAAe,CAHf,cAAe,CACf,eAAiB,CAIjB,YAAa,CANb,gBAAiB,CAKjB,uDAEF,CAEA,wCAZE,wBAAqC,CAArC,oCA0BF,CAdA,uBAEE,qBAAsB,CAExB,wBAEE,oBACF,CAEA,0BACE,wBAAyB,CACzB,kBAAmB,CACnB,UACF,CACA,CAGA,kBASE,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,aACF,CAEA,kBACE,qBAAuB,CAEvB,kBAAmB,CACnB,2BAAyC,CAEzC,eAAgB,CAJhB,YAAa,CAGb,iBAAkB,CAElB,UACF,CAEA,kBAME,iCAAkC,CAJlC,wBAAyC,CACzC,iBAAkB,CADlB,wBAAyC,CAAzC,wCAAyC,CAGzC,WAAY,CAEZ,kBAAmB,CAHnB,UAIF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,sBACE,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,eAAiB,CACjB,eAAgB,CAHhB,iBAAkB,CAKlB,+BACF,CAEA,4BACE,wBACF,CCjgBA,mBAMI,qBAAsB,CAKtB,eAAgB,CADhB,+BAAyC,CAHzC,YAAa,CACb,qBAAsB,CAHtB,YAAa,CAHb,MAAO,CADP,cAAe,CAEf,KAAM,CACN,WAAY,CAKZ,eAGF,CAEA,mBAGE,kBAAmB,CAGnB,qBAAsB,CADtB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,iBAGF,CAEA,kBAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,eAAgB,CADhB,OAGF,CAEA,iBAEE,kBAAmB,CACnB,UAAc,CAFd,YAGF,CAEA,qBAEE,WAAY,CADZ,UAEF,CAEA,iBAOE,kBAAmB,CANnB,eAAgB,CAChB,WAAY,CAOZ,iBAAkB,CANlB,UAAW,CACX,cAAe,CAEf,YAAa,CAEb,sBAAuB,CAHvB,WAAY,CAKZ,+BACF,CAEA,uBACE,0BACF,CACA,sBACE,0BAA2B,CAC3B,aAAc,CACd,YAEF,CACA,gBAIE,qBAAsB,CAFtB,YAAa,CACb,qBAAsB,CAGtB,eAAgB,CAChB,sBAAuB,CAFvB,iBAGF,CACA,eAIE,gCAAkC,CAHlC,YAAa,CACb,aAAc,CACd,iBAGA,CAEF,kBACE,GAAO,SAAU,CAAE,0BAA6B,CAChD,GAAK,SAAU,CAAE,uBAA0B,CAC7C,CAEA,cACE,mBAAoB,CACpB,kBAAmB,CAEnB,kBAAmB,CACnB,qBACF,CACA,+BACE,wBAAyB,CAEzB,8BAA+B,CAD/B,UAEF,CACA,YACE,qBACF,CACA,6BAEE,6BAA8B,CAD9B,UAEF,CAGA,WAKE,kBAAmB,CAGnB,wBAAyB,CALzB,iBAAkB,CAMlB,aAAc,CAPd,WAAY,CAIZ,sBAAuB,CACvB,gBAAiB,CANjB,UASF,CACA,0BAPE,YASF,CAEA,iBAEM,kBAAmB,CAEvB,cAAe,CACf,eAAgB,CAJhB,yBAA0B,CAE1B,iBAAkB,CAGlB,eACF,CAKA,mBACE,QAAS,CACT,oBACF,CAEA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,OAAQ,CACR,iBACF,CAEA,uBAME,+CAAgD,CAHhD,qBAAsB,CACtB,iBAAkB,CAClB,oBAAqB,CAHrB,UAAW,CADX,SAMF,CAEA,mCACE,kBACF,CAEA,oCACE,mBACF,CAEA,oCACE,mBACF,CAEA,kBACE,UAAuC,UAAY,CAAnC,mBAAqC,CACrD,IAA2B,SAAU,CAA/B,kBAAiC,CACzC,CAEA,aAGE,qBAAuB,CADvB,4BAA6B,CAD7B,YAAa,CAGb,iBACF,CAEA,eAKE,wBAAyB,CAEzB,6BAA8B,CAD9B,iBAAkB,CALlB,aAAc,CACd,cAAe,CACf,kBAAmB,CACnB,gBAIF,CAEA,iBACE,YAAa,CACb,qBAAsB,CACtB,iBACF,CAEA,kBAEE,kBAAmB,CAEnB,wBAAyB,CAGzB,kBAAmB,CAFnB,8BAAwC,CAJxC,YAAa,CAEb,qBAAsB,CAGtB,iBAEF,CACA,+BACE,8BACF,CACA,eACE,UACF,CACA,wBAOE,oBAAqB,CAMrB,qBAAuB,CAXvB,iBAAkB,CASlB,UAAW,CADX,mBAAoB,CAPpB,cAAe,CAGf,gBAAiB,CADjB,eAAgB,CAGhB,YAAa,CAIb,mBAAqB,CARrB,WAAY,CAKZ,2BAA6B,CAR7B,UAaF,CAGA,aAEE,kBAAmB,CADnB,YAAa,CAGb,2BAA4B,CAD5B,UAEF,CACA,iBACE,YAAc,CACV,OACN,CAEA,UAKE,kBAAmB,CAJnB,eAAgB,CAChB,WAAY,CAKZ,cAAe,CAHf,YAAa,CAEb,sBAAuB,CAHvB,SAAU,CAKV,uBACF,CAEA,mBAEE,kBAAmB,CADnB,UAEF,CAEA,eACE,YACF,CAEA,qBAEE,WAAY,CADZ,UAEF,CAEA,kBAWE,kBAAmB,CAJnB,qBAAyB,CAEzB,WAAY,CAHZ,iBAAkB,CAJlB,YAAa,CAYb,0BAAwC,CANxC,UAAY,CAKZ,cAAe,CAHf,YAAa,CALb,WAAY,CAOZ,sBAAuB,CAXvB,cAAe,CAEf,UAAW,CAaX,uBAAyB,CAZzB,UAAW,CAWX,aAEF,CAEA,wBACE,wBAAyB,CAEzB,8BAAwC,CADxC,0BAEF,CC9RF,iBAEE,MAAO,CACP,cAAe,CAEf,KAAM,CAJN,UAAW,CAGX,wBAEF,CACA,0BACE,uBACF,CAEA,4BAMI,QACJ,CACA,2DAJK,wBAAyB,CAF1B,YAAa,CACb,qBAAsB,CAFtB,iBAAkB,CAIlB,UASJ,CAEA,uCAII,kBAAmB,CAHnB,YAAa,CAEb,iBAAkB,CADlB,iBAGJ,CACA,gEAKE,kBAAmB,CADnB,YAAa,CAFb,WAAY,CADZ,iBAKF,CACA,iFAEK,iBAAkB,CAGnB,qBAAsB,CAFtB,cAAe,CAFf,WAAY,CAKZ,uBACJ,CACA,0FAII,kBAAoB,CADpB,iBAAkB,CADlB,WAAY,CAGZ,mBAAqB,CACrB,WAAY,CALZ,UAMJ,CACA,qGACI,iBAAkB,CAClB,UACJ,CAWA,sDACE,WAAY,CACZ,UAEF,CAEA,0DACE,WAAY,CACZ,UACF,CAMA,2dAEE,kBACF,CAQA,gDAII,qBAAsB,CAGtB,iBAAkB,CAFlB,2BAA2C,CAG3C,qBAAsB,CACtB,QAAS,CAHT,YAAa,CALb,iBAAkB,CAElB,UAAW,CADX,QAAS,CAST,WACJ,CACA,uEACI,YAAa,CACb,kBAAmB,CACnB,QACJ,CACA,qFAGI,kBAAmB,CACnB,cAAe,CAHf,YAAa,CACb,qBAGJ,CAKA,mBAKI,kBAAmB,CAFnB,YAAa,CADb,WAAY,CAEZ,sBAAuB,CAEvB,eAAgB,CALhB,UAMJ,CACA,uBAEI,WAAY,CACZ,gBAAiB,CAFjB,UAGJ,CACA,qCAEI,kBAAmB,CADnB,YAAa,CAEb,QACJ,CACA,8BACI,YAAa,CACb,kBAAmB,CAEnB,QAAS,CADT,UAEJ,CAEA,iCACI,iBACJ,CACA,6BAEI,kBAAmB,CADnB,YAAa,CAEb,QAEJ,CACA,kCACI,cACJ,CACA,iCACI,WAAY,CACZ,UACJ,CACA,oCACI,YACJ,CAGA,kBACI,WAAY,CAEZ,gBAAiB,CACjB,iBAAkB,CAClB,yBAA2B,CAH3B,WAIF,CAEA,4CAGE,kBAAmB,CAFnB,YAAa,CAGb,WAAY,CAFZ,6BAA8B,CAG9B,cAEF,CAEA,yCAEE,kBAAmB,CAGnB,cAAe,CAJf,YAAa,CAGb,cAAe,CADf,OAGF,CAEA,yCACE,cACF,CAEA,2CACE,YACF,CAIA,cACE,kBAAmB,CACnB,YAEF,CACA,kDAEE,SAAU,CADV,WAEF,CACA,qBAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,cACF,CACA,+CACE,UACF,CACA,oBACE,WAAY,CACZ,iBACF,CACA,qBAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,kBACF,CAQA,qBACE,YACF,CACA,wBACE,cAAe,CACf,iBACF,CACA,4BAEE,iBAAkB,CADlB,UAEF,CACA,yBACE,cAAe,CACf,cACF,CACA,wBAEE,UAAW,CADX,cAEF,CAEA,oBACE,eACF,CACA,mCAIE,gCAAiC,CAHjC,WAAY,CAEZ,WAAY,CADZ,UAGF,CACA,2BACE,gCAAiC,CAKjC,WAAY,CAHZ,iBAAkB,CAClB,UAAW,CACX,yBAA0B,CAH1B,SAKF,CCpRF,kBACE,qBACF,CAEA,UAME,0BAAoC,CADpC,YAAa,CAFb,MAAO,CAKP,mBAAoB,CAPpB,cAAe,CACf,KAAM,CAEN,WAAY,CAGZ,YAEF,CAEA,qBACE,iBAAkB,CAClB,YACF,CAEA,OACE,iBAAkB,CAClB,aACF,CAGA,mCACE,0BACF,CAEA,qCACE,wBACF,CAGA,4DAEE,wBACF", "sources": ["index.css", "styles/global.css", "App.scss", "components/drawer/Drawer.css", "styles/rtl_styles.scss", "components/AI/EnableAIButton.css", "components/guideSetting/PopupSections/PopupSections.css", "../../../../../node_modules/_perfect-scrollbar@1.5.0@perfect-scrollbar/css/perfect-scrollbar.css", "../node_modules/react-perfect-scrollbar/dist/css/styles.css", "../../../../../src/styles.scss", "components/login/ExtensionLogin.css", "assets/icon.css", "components/guideSetting/guideList/GuideMenuOptions.css", "components/AIAgent/ModernChatWindow.css", "components/guideBanners/guideBanner.css", "components/Tooltips/Tooltip.css"], "sourcesContent": ["body {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}\r\n\r\n", "@import url('https://fonts.googleapis.com/css2?family=Syncopate:wght@700&display=swap');\r\n:root {\r\n\t--font-family: Pop<PERSON>s, Proxima Nova, arial, serif;\r\n    --primarycolor: #5F9EA0;\r\n    --ext-background: #F6EEEE;\r\n    --border-color: #ccc;\r\n    --white-color: #fff;\r\n    --back-light-color:#EAE2E2;\r\n\t--button-border-radius: 12px;\r\n}\r\n*:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i):not(.qadpt-jodit *):not(.mat-icon) {\r\n\tfont-family: var(--font-family) !important;\r\n}\r\n\r\nbody {\r\n\tmargin: 0;\r\n\tfont-family: var(--font-family) !important;\r\n\t-webkit-font-smoothing: antialiased;\r\n\t-moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n@font-face {\r\n\tfont-family: \"Gotham Pro\";\r\n\tfont-style: normal;\r\n\t/* src: local(\"Gotham Pro\"), local(\"Gotham Pro\"), url(\"../assets/fonts/GothamPro.woff2\") format(\"woff2\"),\r\n\t\turl(\"../assets/fonts/GothamPro.woff\") format(\"woff\"); */\r\n}\r\n\r\n@font-face {\r\n\tfont-family: \"Proxima Nova\";\r\n\tfont-style: normal;\r\n\tsrc: local(\"Proxima Nova\"), local(\"ProximaNova-Regular\"),\r\n\t\turl(\"../assets/fonts/ProximaNova-Regular.woff2\") format(\"woff2\"),\r\n\t\turl(\"../assets/fonts/ProximaNova-Regular.woff\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n\tfont-family: \"qadapt-icons\";\r\n\tsrc: url(\"../assets/fonts/qadapt-icons.eot?qmcsfb\");\r\n\tsrc: url(\"../assets/fonts/qadapt-icons.eot?qmcsfb#iefix\") format(\"embedded-opentype\"),\r\n\t\turl(\"../assets/fonts/qadapt-icons.ttf?qmcsfb\") format(\"truetype\"),\r\n\t\turl(\"../assets/fonts/qadapt-icons.woff?qmcsfb\") format(\"woff\"),\r\n\t\turl(\"../assets/fonts/qadapt-icons.svg?qmcsfb#qadapt-icons\") format(\"svg\");\r\n\tfont-weight: normal;\r\n\tfont-style: normal;\r\n\tfont-display: block;\r\n}\r\n\r\n", ".App {\r\n  text-align: center;\r\n}\r\n\r\n.App-logo {\r\n  height: 40vmin;\r\n  pointer-events: none;\r\n}\r\n\r\n@media (prefers-reduced-motion: no-preference) {\r\n  .App-logo {\r\n    animation: App-logo-spin infinite 20s linear;\r\n  }\r\n}\r\n\r\n.App-header {\r\n  background-color: #282c34;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: calc(10px + 2vmin);\r\n  color: white;\r\n}\r\n\r\n.App-link {\r\n  color: #61dafb;\r\n}\r\n\r\n@keyframes App-logo-spin {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n", "@import url('https://fonts.googleapis.com/css2?family=Syncopate:wght@700&display=swap');\r\n/* @import '../../components/guideDesign/Canvas.module.css'; */\r\n:root {\r\n\t--font-family: Gotham Poppins, Proxima Nova, arial, serif;\r\n    --primarycolor: #5F9EA0;\r\n    --ext-background: #F6EEEE;\r\n    --border-color: #ccc;\r\n    --white-color: #fff;\r\n    --back-light-color:#EAE2E2;\r\n    --font-size : 14px;\r\n    --button-padding : 4px 8px;\r\n    --button-lineheight : normal;\r\n    --error-color:#d05353;\r\n}\r\n*:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i):not(.qadpt-jodit *):not(.mat-icon) {\r\n\tfont-family: var(--font-family) !important;\r\n}\r\n*:not(.qadpt-rte *):not(.qadptWelcomeMessage):not(.previewdata *):not(.qadpt-previewdata *):not(.qadpt-stpdrp *):not(.qadpt-nmuq *):not(.qadpt-jodit *)\r\n\r\n{\r\n    font-size: var(--font-size);\r\n}\r\nbody {\r\n\tmargin: 0;\r\n\tfont-family: var(--font-family) !important;\r\n\t-webkit-font-smoothing: antialiased;\r\n\t-moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n@font-face {\r\n\tfont-family: \"Gotham Pro\";\r\n\tfont-style: normal;\r\n\t/* src: local(\"Gotham Pro\"), local(\"Gotham Pro\"), url(\"../../assets/fonts/GothamPro.woff2\") format(\"woff2\"),\r\n\t\turl(\"../../assets/fonts/GothamPro.woff\") format(\"woff\"); */\r\n}\r\n\r\n@font-face {\r\n\tfont-family: \"Proxima Nova\";\r\n\tfont-style: normal;\r\n\tsrc: local(\"Proxima Nova\"), local(\"ProximaNova-Regular\"),\r\n\t\turl(\"../../assets/fonts/ProximaNova-Regular.woff2\") format(\"woff2\"),\r\n\t\turl(\"../../assets/fonts/ProximaNova-Regular.woff\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n\tfont-family: \"qadapt-icons\";\r\n\tsrc: url(\"../../assets/fonts/qadapt-icons.eot?qmcsfb\");\r\n\tsrc: url(\"../../assets/fonts/qadapt-icons.eot?qmcsfb#iefix\") format(\"embedded-opentype\"),\r\n\t\turl(\"../../assets/fonts/qadapt-icons.ttf?qmcsfb\") format(\"truetype\"),\r\n\t\turl(\"../../assets/fonts/qadapt-icons.woff?qmcsfb\") format(\"woff\"),\r\n\t\turl(\"../../assets/fonts/qadapt-icons.svg?qmcsfb#qadapt-icons\") format(\"svg\");\r\n\tfont-weight: normal;\r\n\tfont-style: normal;\r\n\tfont-display: block;\r\n}\r\n\r\n.leftDrawer {\r\n    position: fixed;\r\n    left: 0;\r\n    top: 0;\r\n    width: 300px !important;\r\n    min-width: 270px !important;\r\n    height: 100%;\r\n    background-color: var(--white-color);\r\n    border-right: 1px solid var(--primarycolor);\r\n    transition: width 0.3s ease, background-color 0.3s ease !important;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    align-items: flex-start;\r\n    z-index: 99999 !important;\r\n    overflow: hidden !important;\r\n}\r\n.leftDrawer.closed {\r\n    display: none;\r\n  }\r\n  .leftDrawer .scrollbar-container{\r\n    padding: 0px 20px 0px 20px;\r\n}\r\n.leftDrawer.collapsed {\r\n    width: 56px !important;\r\n    min-width: 35px !important;\r\n    background-color: var(--primarycolor) !important;\r\n    padding: 20px 0px;\r\n    border-top-right-radius: 12px;\r\n    border-bottom-right-radius: 12px;\r\n}\r\n.leftDrawer.collapsed .qadpt-drawerHeader{\r\n    display: block !important;\r\n    padding: 0 !important;\r\n    margin: 20px 0 0 0 !important;\r\n}\r\n\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item{\r\n\t\tpadding: 0 !important;\r\n        margin: 20px 10px !important;\r\n        border: none !important;\r\n        box-shadow: none !important;\r\n        border-radius: 50px;\r\n        height: 38px;\r\n        width: 38px;\r\n        display: flex;\r\n        align-items: center;\r\n        place-content: center;\r\n}\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item.active {\r\n    background-color: white !important;\r\n  }\r\n  /* .leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item:hover {\r\n    border: 2px solid white !important;\r\n}\r\n   */\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item .menu-content:hover {\r\n    border: 2px solid white !important;\r\n    border-radius: 50%;\r\n    padding: 5px;\r\n\r\n}\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item .icons{\r\n    margin-right: 0 !important;\r\n    display: block !important;\r\n    /* height: 30px !important;\r\n    width: 30px !important; */\r\n\r\n}\r\n/* .leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item:hover .icons svg {\r\n    transform: scale(1.4);\r\n    transition: transform 0.2s ease-in-out;\r\n} */\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item .icons svg{\r\n    height: 22px !important;\r\n    width: 22px !important;\r\n    fill: #fff;\r\n    stroke: #fff;\r\n    display: flex;\r\n}\r\n\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item .icons .qadpt-colsvg path{\r\n    fill: #fff ;\r\n    stroke:#fff ;\r\n}\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item.active .icons .qadpt-colsvg{\r\n    svg,\r\n    path{\r\n        fill: var(--primarycolor) !important;\r\n        stroke: var(--primarycolor) !important;\r\n    }\r\n}\r\n.leftDrawer.collapsed .qadpt-drawerHeader .qadpt-ai-button{\r\n    svg,\r\n    path{\r\n        fill: #fff ;\r\n    stroke:#fff ;\r\n    }\r\n}\r\n\r\n\r\n.leftDrawer.collapsed .qadpt-drawerHeader .qadpt-toggleIcon{\r\n    /* filter: brightness(0.5);*/\r\n    display: flex;\r\n    align-items: center;\r\n    place-content: center;\r\n}\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .scrollbar-container{\r\n    padding: 0 !important;\r\n}\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .qadpt-ai-button{\r\n    padding: 0 !important;\r\n    margin: 20px 10px !important;\r\n    border: none !important;\r\n    box-shadow: none !important;\r\n    border-radius: 50px;\r\n    height: 38px;\r\n    width: 38px;\r\n    display: flex;\r\n    align-items: center;\r\n    place-content: center;\r\n    cursor: pointer;\r\n    background: none;\r\n    outline: none;\r\n}\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .qadpt-ai-button .qadpt-aiicon{\r\n    display: flex;\r\n}\r\n\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .qadpt-ai-button.active{\r\n    background: linear-gradient(360deg, #04417F 0%, #0776E5 100%) !important;\r\n}\r\n\r\n\r\n.qadpt-selectaccount {\r\n    width: 100% !important;\r\n    height: 33px !important;\r\n}\r\n\r\n.qadpt-drawerHeader {\r\n    justify-content: space-between;\r\n    display: flex;\r\n    align-items: center;\r\n    width: -webkit-fill-available;\r\n    position: relative;\r\n    margin: 20px 15px 0px 15px;\r\n}\r\n.qadpt-drawerHeader .qadpt-toggleIcon {\r\n    cursor: pointer;\r\n    top: 0;\r\n    position: relative;\r\n        display: flex;\r\n}\r\n/* .qadpt-drawerHeader .qadpt-toggleIcon svg{\r\n    filter: brightness(0.5);\r\n} */\r\n.grid-toolbar-options {\r\n\twidth: 100% !important;\r\n\tmargin: 20px 0 0;\r\n\t.left-options {\r\n\t\twidth: 100%;\r\n\t\tmax-width: 100%;\r\n\t\tdisplay: flex;\r\n\t\t.drp-fields,\r\n\t\t.dt-fields {\r\n\t\t\tdisplay: flex;\r\n\t\t\tmargin-right: 15px;\r\n\t\t\t.auto-filed,\r\n\t\t\t.qadpt-DateTime {\r\n\t\t\t\twidth: 50%;\r\n\t\t\t\tmax-width: 50%;\r\n\t\t\t\tmin-width: 50%;\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t}\r\n\t\t\tbutton {\r\n\t\t\t\ttext-transform: unset !important;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.drp-fields {\r\n\t\t\t.MuiInputBase-root.MuiOutlinedInput-root {\r\n\t\t\t\tpadding-right: 35px;\r\n\t\t\t\t.MuiAutocomplete-input {\r\n\t\t\t\t\twidth: inherit !important;\r\n\t\t\t\t\tmin-width: 30px;\r\n\t\t\t\t\tmax-width: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.auto-filed,\r\n\t\t.name-fld,\r\n\t\t.qadpt-DateTime {\r\n\t\t\t.MuiSvgIcon-root {\r\n\t\t\t\theight: 16px !important;\r\n\t\t\t\twidth: 16px !important;\r\n\t\t\t}\r\n\t\t\t&.dt-fld2 {\r\n\t\t\t\tmargin-left: -26px;\r\n\t\t\t\t&.hide-close {\r\n\t\t\t\t\tmargin-left: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tinput {\r\n\t\t\t\tfont-size: 12px !important;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.dt-close-icon {\r\n\t\t\tright: 55px;\r\n\t\t\theight: 40px;\r\n\t\t}\r\n\t}\r\n\r\n\t.right-options {\r\n\t\tdisplay: flex;\r\n\t\tmargin: 5px;\r\n\t\tplace-content: flex-end;\r\n\t\tbutton {\r\n\t\t\ttext-transform: unset !important;\r\n\t\t\tpadding: 5px;\r\n\t\t\theight: 32px;\r\n\t\t\tborder-radius: 20px;\r\n\t\t\tmargin: 0 0 0 5px;\r\n\t\t}\r\n\t}\r\n\tsvg {\r\n\t\theight: 16px;\r\n\t\twidth: 16px;\r\n\t}\r\n}\r\n/* .qadpt-drawerHeader .qadpt-leftdrawer.collapsed .qadpt-toggleIcon {\r\n    left: 3px;\r\n    margin-top: 40px;\r\n} */\r\n.qadpt-drawerHeader .qadpt-drawerTitle {\r\n    font-family: \"Syncopate\", sans-serif !important;\r\n    font-size: 14px;\r\n    font-weight: 700;\r\n    color: var(--primarycolor);\r\n}\r\n.qadpt-drawerHeader .qadpt-threeDotMenu {\r\n    position: relative;\r\n    cursor: pointer;\r\n}\r\n.qadpt-drawerHeader .qadpt-threeDotMenu svg{\r\n    filter: brightness(0.5);\r\n    display: flex ;\r\n}\r\n\r\n/* #newInteractionBtn:hover {\r\n    background-color: #34A080;\r\n}\r\n*/\r\n.qadpt-drawerContent {\r\n    width: 100%;\r\n    margin-top: 20px;\r\n}\r\n.qadpt-drawerContent .qadpt-welcome-message {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    text-align: left;\r\n    padding: 13px;\r\n}\r\n.qadpt-drawerContent .qadpt-login-form {\r\n    margin-top: 20px;\r\n}\r\n.qadpt-drawerContent .qadpt-login-form .qadpt-form-label {\r\n    font-size: 14px;\r\n    margin-top: 10px;\r\n    text-align: left;\r\n}\r\n.qadpt-drawerContent .qadpt-login-form .qadpt-input-field {\r\n    font-size: 16px;\r\n    font-weight: 400;\r\n    padding: 12px;\r\n    border: 1px solid var(--border-color);\r\n    border-radius: 6px;\r\n    box-shadow: none;\r\n    height: 46px;\r\n    background-color: var(--white-color);\r\n    margin-top: 10px;\r\n}\r\n.qadpt-drawerContent .qadpt-login-form .qadpt-invalidcreds {\r\n    font-size: 14px;\r\n}\r\n.qadpt-drawerContent .qadpt-login-form .qadpt-forgotpwd {\r\n    color: var(--primarycolor) !important;\r\n    cursor: pointer;\r\n    font-size: 16px;\r\n    font-weight: 400;\r\n    line-height: 24px;\r\n    margin-top: 10px;\r\n    text-align: left;\r\n}\r\n.qadpt-drawerContent .qadpt-subhead {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: relative;\r\n    margin-left: 10px;\r\n}\r\n.qadpt-drawerContent .qadpt-subhead .qadpt-backbtn {\r\n    position: absolute;\r\n    left: 10px;\r\n    cursor: pointer;\r\n    top: 4px;\r\n}\r\n.qadpt-drawerContent .qadpt-subhead .qadpt-subhead-title {\r\n    font-size: 18px !important;\r\n    font-weight: 600;\r\n}\r\n.qadpt-drawerContent .qadpt-divider {\r\n    margin-top: 10px;\r\n}\r\n.qadpt-drawerContent .qadpt-items {\r\n    display: grid;\r\n    gap: 10px;\r\n    margin-top: 20px;\r\n    margin-left: -4px;\r\n}\r\n.qadpt-drawerContent .qadpt-items .qadpt-subitem-img {\r\n    width: 130px;\r\n    height: 80px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border-radius: 8px;\r\n    margin-bottom: 10px;\r\n    cursor: pointer;\r\n}\r\n.qadpt-drawerContent .qadpt-items .qadpt-subitem-img.selected {\r\n    background: linear-gradient(270deg, #ededed 0%, rgba(95, 158, 160, 0.5) 100%);\r\n}\r\n.qadpt-drawerContent .qadpt-items .qadpt-subitem-img:not(.selected) {\r\n    background: #eae2e2;\r\n}\r\n.qadpt-drawerContent .qadpt-items .qadpt-item-label {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    text-align: left;\r\n}\r\n.qadpt-drawerContent .qadpt-guide-form {\r\n    margin-top: 20px;\r\n    padding: 0 16px;\r\n}\r\n.qadpt-drawerContent .qadpt-guide-form .qadpt-errmsg{\r\n    display: flex;\r\n    font-size: 12px !important;\r\n     align-items: center;\r\n}\r\n.qadpt-drawerContent .qadpt-guide-form .qadpt-errmsg span{\r\n    margin-right: 4px;\r\n}\r\n\r\n.qadpt-drawerContent .qadpt-guide-form .MuiFormHelperText-root.Mui-error {\r\n    color: #e6a957 !important;\r\n}\r\n\r\n.qadpt-drawerContent .qadpt-guide-form .qadpt-guide-label {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #444;\r\n    margin-bottom: 5px;\r\n    text-align: left;\r\n}\r\n.qadpt-drawerContent .qadpt-guide-form .qadpt-guide-input {\r\n    height: 45px;\r\n    padding: 12px;\r\n    gap: 10px;\r\n    border-radius: 4px;\r\n    opacity: 1;\r\n    margin-bottom: 15px;\r\n\r\n}\r\n.qadpt-drawerContent .qadpt-guide-form .qadpt-guide-input input{\r\n    height: auto !important;\r\n    line-height: initial !important;\r\n    background: initial !important;\r\n    padding: 5px;\r\n    font-size: 14px !important;\r\n}\r\n.qadpt-drawerContent .qadpt-guide-form p.Mui-error {\r\n        line-height: 12px;\r\n        margin: -12px 0 10px 0;\r\n        font-size: 12px !important;\r\n}\r\n.qadpt-drawerContent .qadpt-drawerFooter {\r\n    top: 10px;\r\n    position: relative;\r\n    padding: 0 16px;\r\n}\r\n.qadpt-drawerContent .qadpt-crtfrm-scratch {\r\n    height: 100px;\r\n    background-color: #eae2e2;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 4px;\r\n    margin-top: 10px;\r\n    gap: 10px;\r\n}\r\n.qadpt-btn {\r\n    background-color: var(--primarycolor) !important;\r\n    color: var(--white-color) !important;\r\n    border-radius: 12px !important;\r\n    width: 100% !important;\r\n    text-transform: none !important;\r\n    font-size: 16px !important;\r\n    border: 0 !important;\r\n    line-height: var(--button-lineheight) !important;\r\n    padding: 10px 12px !important;\r\n}\r\n.qadpt-btn.disabled {\r\nopacity:0.5;\r\n}\r\n.qadpt-ext-banner {\r\n    display: flex;\r\n    /* justify-content: space-between; */\r\n    align-items: center;\r\n    /* padding: 0 20px; */\r\n    background-color: var(--ext-background);\r\n    border-bottom: 1px solid var(--primarycolor);\r\n    height: 55px;\r\n    width: 100%;\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 9999999;\r\n}\r\n\r\n.qadpt-ext-banner .qadpt-banner-button {\r\n    border: 1px solid var(--primarycolor);\r\n    padding: 8px;\r\n    border-radius: 8px;\r\n    color: var(--primarycolor);\r\n    gap: 5px;\r\n    max-height: 40px;\r\n    height: 40px;\r\n    text-transform: capitalize !important;\r\n    background: rgba(255,255,255,0.6);\r\n}\r\n.qadpt-ext-banner input{\r\n    height: inherit !important;\r\n    padding: inherit !important;\r\n    border: initial !important;\r\n}\r\n.qadpt-ext-banner .qadpt-banner-button.qadpt-icon {\r\n    min-height: 40px;\r\n    max-height: 40px;\r\n    place-content: center;\r\n    align-items: center;\r\n}\r\n.qadpt-ext-banner .qadpt-banner-button.qadpt-icon.qadpt-name{\r\n    padding : 0 !important;\r\n    gap : 0 !important;\r\n}\r\n\r\n/* .qadpt-ext-banner .qadpt-banner-button .qadp-btn-icon{\r\n    margin-top: 5px;\r\n} */\r\n.qadpt-ext-banner .qadpt-left-banner,\r\n.qadpt-ext-banner .qadpt-center-banner,\r\n.qadpt-ext-banner .qadpt-right-banner {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    max-width: 33.33%;\r\n    width: 33.33%;\r\n}\r\n.qadpt-ext-banner .qadpt-left-banner{\r\n    padding-left: 10px;\r\n}\r\n\r\n.qadpt-ext-banner .qadpt-left-banner .guidename-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    width: 10px;\r\n    padding: 10px;\r\n}\r\n\r\n.qadpt-ext-banner .qadpt-left-banner .qadpt-edtxt{\r\n  \r\n    max-width: 120px;\r\n    padding: 0 10px;\r\n    border-left: 2px solid #ccc;\r\n}\r\n\r\n.qadpt-ext-banner .qadpt-center-banner{\r\n    justify-content: center !important;\r\n}\r\n.qadpt-ext-banner .qadpt-right-banner {\r\n    justify-content: right !important;\r\n    padding-right: 10px;\r\n}\r\n.qadpt-ext-banner .qadpt-left-banner button, .qadpt-ext-banner .qadpt-center-banner button, .qadpt-ext-banner .qadpt-right-banner button {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n.qadpt-ext-banner .qadpt-left-banner button span, .qadpt-ext-banner .qadpt-center-banner button span, .qadpt-ext-banner .qadpt-right-banner button span{\r\n    max-width: 150px !important;\r\n    text-overflow: ellipsis;\r\n    overflow: hidden;\r\n    white-space: nowrap;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n.qadpt-ext-banner .qadpt-right-banner .qadpt-save{\r\nopacity: 0.5;\r\n}\r\n.qadpt-threedot-popup {\r\n    z-index: 999999 !important;\r\n}\r\n.qadpt-threedot-popup .MuiPopover-paper {\r\n    max-width: 200px;\r\n    max-height: 250px;\r\n    padding: 15px;\r\n    margin-top: 40px;\r\n    margin-left: 100px;\r\n}\r\n.qadpt-popup-item {\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    margin-bottom: 10px;\r\n}\r\n.qadpt-popup-item.disabled {\r\n    pointer-events: none;\r\n    opacity: 0.5;\r\n    cursor: default;\r\n}\r\n.qadpt-popup-item:last-child {\r\n    margin-bottom: 0;\r\n}\r\n.qadpt-popup-icon {\r\n    margin-right: 10px;\r\n    display: flex;\r\n}\r\n\r\n\r\n.qadpt-popup-text {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    line-height: 21px;\r\n}\r\n\r\n/* .MuiPopover-root .MuiSelect-root{\r\n    z-index: 1000000 !important;\r\n} */\r\n.qadpt-acnt-drpdwn{\r\n    z-index: 9999 !important;\r\n}\r\n.qadpt-acnt-drpdwn .MuiPaper-root.MuiPaper-elevation{\r\n    top: 110px !important;\r\n    left: 220px !important;\r\n    width: calc(100vh - 370px);\r\n    max-height: calc(100% - 130px);\r\n}\r\n.qadpt-acnt-drpdwn .MuiPaper-root.MuiPaper-elevation li{\r\n    white-space: normal;\r\n    word-break: break-word;\r\n}\r\n\r\n.qadpt-designpopup-v2 {\r\n    width: 220px;\r\n    border-radius: 8px;\r\n    padding: 16px;\r\n    background-color: rgba(246, 238, 238, 1);\r\n    display: flex;\r\n    flex-direction: column;\r\n    transition: height 0.3s ease;\r\n\r\n}\r\n\r\n.qadpt-designpopup {\r\n    width: 250px;\r\n    border-radius: 8px;\r\n    background-color: rgba(246, 238, 238, 1);\r\n    position: fixed;\r\n    z-index: 111111;\r\n        display: flex;\r\n    flex-direction: column;\r\n    transition: height 0.3s ease;\r\n    top: 60px;\r\n    left: 10px;\r\n}\r\n.qadpt-designpopup.qadpt-btnprop {\r\n    right: 190px;\r\n    top: 70px;\r\n    position: fixed;\r\n    left: unset;\r\n}\r\n.qadpt-designpopup.qadpt-banbtnprop{\r\n    position: fixed;\r\n  top: 116px;\r\n  right: 300px;\r\n  left: auto;\r\n  z-index: 99999;\r\n}\r\n.qadpt-designpopup.qadpt-tltbtnprop{\r\n    position: fixed;\r\n  top: 70px;\r\n  right: 170px;\r\n  left: auto;\r\n}\r\n\r\n.qadpt-designpopup.qadpt-imgset{\r\n\tbackground-color: #fff !important;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-design-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 15px;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-design-header .qadpt-title {\r\n    font-weight: 600;\r\n    color: var(--primarycolor);\r\n    font-size: 16px !important;\r\n    text-align: center;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-design-header svg {\r\n    font-size: 16px;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-design-btn {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n    background-color: #eae2e2;\r\n    color: #444444;\r\n    text-transform: none;\r\n    margin-bottom: 5px;\r\n    border-radius: 12px;\r\n    padding: 8px 12px;\r\n    align-items: center;\r\n    font-weight: 600;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-design-btn .MuiButton-icon{\r\n    background: rgba(95, 158, 160, 0.2);\r\n    border-radius: 100px;\r\n    padding: 4px 8px;\r\n}\r\n\r\n.qadpt-designpopup .qadpt-content .qadpt-design-btn .qadpt-hotsicon {\r\n    margin-right: 8px;\r\n    height: auto;\r\n    background: rgba(95, 158, 160, 0.2);\r\n    border-radius: 100px;\r\n    margin-left: -5px;\r\n    padding: 4px 8px;\r\n    line-height: 12px;\r\n}\r\n\r\n.qadpt-designpopup .qadpt-content .qadpt-design-btn .qadpt-hotsicon svg{\r\n    height: 23px;\r\n    width: 23px;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-drawerFooter{\r\n    padding: 15px;\r\n    padding-top: 0;\r\n}\r\n\r\n\r\n.qadpt-designpopup .qadpt-content .qadpt-design-btn:hover {\r\n    background-color: #d8d4d2;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-design-btn svg {\r\n    color: var(--primarycolor);\r\n    border-radius: 50px;\r\n    height: 24px;\r\n    width: 24px;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-status-container {\r\n    display: flex;\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        padding: 15px;\r\n        padding-top: 0;\r\n}\r\n\r\n\r\n.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-label {\r\n    text-align: left;\r\n}\r\n/* .qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-toggle-group {\r\n    margin-bottom: 16px;\r\n} */\r\n.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-toggle-group button {\r\n    text-transform: capitalize;\r\n    height: 35px;\r\n    width: 80px;\r\n    font-size: 12px !important;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-toggle-group .MuiToggleButton-root.Mui-selected {\r\n    border: 1px solid var(--primarycolor);\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-customfield {\r\n    background-color: #eae2e2;\r\n    border-radius: 8px;\r\n    height: calc(100vh - 210px);\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-customfield textarea {\r\n    height: calc(100vh - 210px) !important;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-customfield .MuiOutlinedInput-root {\r\n    height: 100%;\r\n    width: 110%;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-customfield .MuiOutlinedInput-root fieldset {\r\n    border: none;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-customfield .MuiOutlinedInput-root:hover fieldset {\r\n    border-color: #495e58;\r\n}\r\n.qadpt-designpopup .qadpt-position-grid {\r\n    padding: 8px;\r\n    background-color: var(--back-light-color);\r\n    border-radius: var(--button-border-radius);\r\n    margin-bottom: 5px;\r\n}\r\n.qadpt-designpopup .qadpt-position-grid .MuiGrid-root {\r\n    background: var(--ext-background);\r\n    width: 100%;\r\n    margin: 0;\r\n    border-radius: 10px;\r\n}\r\n.qadpt-designpopup .qadpt-position-grid .qadpt-ctrl-title {\r\n    text-align: left;\r\n    font-size: 14px !important;\r\n    font-weight: 600;\r\n}\r\n.qadpt-designpopup .qadpt-controls {\r\n    padding: 15px;\r\n    padding-top: 0;\r\n}\r\n.qadpt-designpopup .qadpt-controls .qadpt-gtnext span{\r\n    margin-right: 8px;\r\n}\r\n.qadpt-designpopup .qadpt-controls .qadpt-chos-btn p{\r\n    text-align: left;\r\n}\r\n.qadpt-designpopup .qadpt-controls .qadpt-chos-btn .MuiSelect-select{\r\n    text-align: left;\r\n\tpadding: 9px 14px !important;\r\n}\r\n\r\n\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box {\r\n    display: flex;\r\n    align-items: center;\r\n    background-color: var(--back-light-color);\r\n    border-radius: var(--button-border-radius);\r\n    height: auto; /* changed due to multilingual support*/\r\n    padding-right: 8px;\r\n    margin-bottom: 5px;\r\n    justify-content: space-between;\r\n}\r\n/* .qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-chkoffset{\r\n    padding-right: 8px;\r\n} */\r\n\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box.qadpt-chkcontrol-box {\r\n    flex-direction: column;\r\n    height: auto !important;\r\n    padding: 8px !important;\r\n}\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-label {\r\n    font-size: 14px;\r\n    margin-right: auto;\r\n    display: flex;\r\n    align-items: center;\r\n    font-weight: 600;\r\n    color: #444444;\r\n    text-align: left;\r\n    padding: 8px;\r\n    line-height: 18px;\r\n}\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-labeltxt {\r\n    font-weight: 600;\r\n    color: #444444;\r\n    padding: 7px;\r\n}\r\n\r\n\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input {\r\n    width: 77px;\r\n    margin-left: auto;\r\n}\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input input {\r\n    text-align: right;\r\n    padding-right: 5px !important;\r\n    padding-left: 5px !important;\r\n}\r\n\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input .MuiOutlinedInput-root,\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input.MuiOutlinedInput-root {\r\n    border-radius: 12px;\r\n    height: 30px;\r\n    font-size: 14px;\r\n    background-color: #f1ecec;\r\n}\r\n.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input .MuiSelect-select {\r\n    height: 20px;\r\n    font-size: 14px;\r\n    /* background-color: var(--white-color); */\r\n    border-radius: 12px;\r\n}\r\n\r\n.qadpt-designpopup .qadpt-controls .qadpt-color-input {\r\n    width: 20px;\r\n    height: 20px;\r\n    padding: 0 !important;\r\n    border-radius: 50%;\r\n    border: 1px solid var(--border-color);\r\n}\r\n.qadpt-designpopup .qadpt-controls .qadpt-color-input::-webkit-color-swatch-wrapper {\r\n    padding: 0;\r\n    border-radius: 50%;\r\n}\r\n.qadpt-designpopup .qadpt-controls .qadpt-color-input::-webkit-color-swatch {\r\n    border-radius: 50%;\r\n    border: none;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-canblock{\r\n    max-height: calc(100vh - 184px);\r\n    overflow: auto;\r\n}\r\n.qadpt-designpopup .qadpt-content .qadpt-canblock.qadpt-btnpro{\r\n    max-height: calc(100vh - 245px) !important;\r\n}\r\n\r\n/* p.styles */\r\n\r\n.htmlbanner .MuiBox-root{\r\nheight: 40px;\r\n}\r\n/* end */\r\n/* .MuiInputBase-input:-webkit-autofill{\r\n\tbackground-color: white !important;\r\n\tbox-shadow: 0 0 0px 1000px white inset !important;\r\n\tcolor: #000 !important;\r\n} */\r\n.custom-popover-root{\r\n    z-index: 9999 !important;\r\n}\r\n.qadpt-toaster {\r\n    top: 30px !important;\r\n    width: 40%;\r\n    z-index: 9999999 !important;\r\n    overflow: hidden !important;\r\n}\r\n.qadpt-toaster .MuiAlert-message {\r\n    display: block;\r\n    overflow: hidden !important;\r\n    word-break: break-word;\r\n    width: 100%;\r\n    text-align: left;\r\n}\r\n.qadpt-toaster.qadpt-toaster-success {\r\n    border: 1px solid #2e7d32;\r\n}\r\n.qadpt-toaster.qadpt-toaster-error {\r\n    border: 1px solid #f00;\r\n}\r\n.qadpt-toaster .qadpt-alert {\r\n    width: 150%;\r\n    display: flex;\r\n    align-items: flex-start;\r\n\r\n}\r\n@keyframes pulse {\r\n    0% {\r\n        transform: scale(1);\r\n        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);\r\n    }\r\n    50% {\r\n        transform: scale(1.1);\r\n        box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);\r\n    }\r\n    100% {\r\n        transform: scale(1);\r\n        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);\r\n    }\r\n}\r\n\r\n.pulse-animation {\r\n    animation: pulse 1.5s infinite;\r\n}\r\n.qadpt-guide-popup{\r\n    z-index: 99999 !important;\r\n}\r\n.qadpt-previewdata{\r\n    position: inherit !important;\r\n}\r\n\r\n.qadpt-tooltip-header {\r\n    display: block;\r\n    font-weight: 600;\r\n    color: var(--primarycolor);\r\n  }\r\n\r\n  .qadpt-ext-banner .qadpt-tooltip-subtext {\r\n    display: block;\r\n    font-size: 14px !important;\r\n    color: var(--primarycolor);\r\n  }\r\n\r\n  .qadpt-tour-popup{\r\n    z-index: 9999999 !important;\r\n}\r\n.MuiButtonBase-root.MuiMenuItem-root {\r\n    margin: 0 10px;\r\n    padding: 10px;\r\n\t&.Mui-selected,&:hover {\r\n\t  border-radius: 12px;\r\n\t  margin: 0 10px;\r\n\t  padding: 10px;\r\n\t}\r\n}\r\n\r\n.MuiOutlinedInput-root.Mui-error fieldset{\r\n    border-color: #e6a957 !important;\r\n}\r\n/* Container for the switch */\r\n.toggle-switch {\r\n    position: relative;\r\n    display: inline-block;\r\n    height: 20px;\r\n    width: 36px;\r\n  }\r\n\r\n  /* Hide the default checkbox input */\r\n  .toggle-switch input {\r\n    opacity: 0;\r\n    width: 0;\r\n    height: 0;\r\n  }\r\n\r\n  /* The slider background */\r\n  .toggle-switch .slider {\r\n    position: absolute;\r\n    cursor: pointer;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: #ccc;\r\n    transition: 0.4s;\r\n    border-radius: 24px;\r\n    margin: 0 !important;\r\n  }\r\n\r\n  /* The circle/knob */\r\n  .toggle-switch .slider:before {\r\n    position: absolute;\r\n    content: \"\";\r\n    height: 15px;\r\n    width: 15px;\r\n    left: 0px;\r\n    bottom: 3px;\r\n    background-color: white;\r\n    transition: 0.4s;\r\n    border-radius: 50%;\r\n    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  /* Checked state styles */\r\n  .toggle-switch input:checked + .slider {\r\n    background-color: var(--primarycolor);  /* Teal/turquoise color */\r\n  }\r\n\r\n  .toggle-switch input:checked + .slider:before {\r\n    transform: translateX(20px);\r\n  }\r\n\r\n  /* Focus styles for accessibility */\r\n  .toggle-switch input:focus + .slider {\r\n    box-shadow: 0 0 1px var(--primarycolor);\r\n  }\r\n\r\n  /* Optional: Disabled state styles */\r\n  .toggle-switch input:disabled + .slider {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n  .step-input input{\r\n    padding:6px 10px !important;\r\n    border: 1px solid #a8a8a8 !important;\r\n    width: -webkit-fill-available;\r\n    border-radius: 6px;\r\n    background: rgb(255, 255, 255);\r\n    outline: none;\r\n    text-align: left;\r\n    margin-bottom: 5px;\r\n  }\r\n  .step-input.qadpt-stbdr input{\r\n    border: 1px solid #e9a971 !important;\r\n  }\r\n\r\n  .qadpt-overlay.notcollapsed{\r\n    position: fixed;\r\n    top: 0px;\r\n    left: 0px;\r\n    width: 100vw;\r\n    height: 100vh;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n    z-index: 99999;\r\n  }\r\n\r\n  .qadpt-chkstp{\r\n    position: relative;\r\n  }\r\n  .qadpt-chkstp::before{\r\n    background: var(--chkcolor);\r\n    content: '';\r\n    position: absolute;\r\n    display: block;\r\n    width : 6px;\r\n    height: 100%;\r\n   border-top-right-radius: 10px;\r\n   border-bottom-right-radius: 10px;\r\n  }\r\n\r\n\r\n  .qadpt-launcher-config {\r\n    padding: 16px;\r\n    font-family: sans-serif;\r\n  }\r\n\r\n  .qadpt-type-selector {\r\n    display: flex;\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n    border: 1px solid #e0e0e0;\r\n    width: fit-content;\r\n  }\r\n\r\n  .qadpt-type-option {\r\n       padding: 8px;\r\n    border-radius: 8px;\r\n    background: #E5DADA;\r\n    cursor: pointer;\r\n    border: none !important;\r\n    margin: 2px;\r\n  }\r\n\r\n  .qadpt-type-option.selected {\r\n    border: 1px solid var(--primarycolor) !important;\r\n  }\r\n\r\n  .qadpt-text-input {\r\n    width: 100%;\r\n    padding: 8px;\r\n    border: 1px solid #e0e0e0;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .qadpt-color-input {\r\n    width: 36px;\r\n    height: 36px;\r\n    border: none;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n    cursor: pointer;\r\n  }\r\n\r\n.qadpt-chkpopdesc{\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n    margin: 0px;\r\n    word-break: break-word;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    display: -webkit-box;\r\n}\r\n\r\n/* Create With AI Button Styles start */\r\n.qadpt-ai-container {\r\n    border-radius: 10px;\r\n    padding: 17px;\r\n    margin: 10px 0;\r\n    color: white;\r\n    text-align: left;\r\n    background: linear-gradient(360deg, #04417F 0%, #0776E5 100%);\r\n}\r\n\r\n.beta {\r\n    width: 47px;\r\n    background: #000;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    line-height: 18px;\r\n    font-size: 10px !important;\r\n    position: absolute;\r\n    top: 19px;\r\n    right: 23px;\r\n    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);\r\n    z-index: 10;\r\n    border-radius:6px 0px 0px 6px;\r\n    overflow: hidden;\r\n    height: 18px;\r\n    color: #fff;\r\n    animation: betaPulse 0.8s ease-in-out infinite alternate;\r\n    letter-spacing: 0.5px;\r\n}\r\n  @keyframes betaPulse {\r\n    0% {\r\n      transform: scale(1);\r\n      box-shadow: 0 0 4px rgba(77, 74, 70, 0.5);\r\n      \r\n    }\r\n    50% {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 0 4px rgba(77, 74, 70, 0.8);\r\n\r\n     \r\n    }\r\n    100% {\r\n      transform: scale(1);\r\n      box-shadow: 0 0 4px rgba(77, 74, 70, 0.5);\r\n\r\n     \r\n    }\r\n  }\r\n  \r\n\r\n.qadpt-ai-title {\r\n    width: calc(100% - 40px);\r\n    margin-bottom: 10px;\r\n    line-height: 1.3;\r\n}\r\n\r\n.qadpt-button {\r\n    display: flex;\r\n    align-items: center;\r\n    place-content: center;\r\n    background-color: white;\r\n    border-radius: 12px;\r\n    padding: 8px 12px;\r\n    margin-top: 10px;\r\n    cursor: pointer;\r\n    border: none;\r\n    width: 100%;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n.qadpt-button:hover {\r\n    background-color: #f0f0f0;\r\n}\r\n\r\n.qadpt-icon {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    /* margin-right: 10px; commented due to effecting in rtl only  */\r\n}\r\n\r\n.qadpt-icon .back-icon svg path {\r\n    fill: var(--primarycolor);\r\n}\r\n.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .qadpt-ai-button.active{\r\n\r\n}\r\n\r\n.qadpt-text {\r\n    font-size: 16px;\r\n    color: #04417F;\r\n}\r\n/* Create With AI Button Styles ends*/\r\n.qadpt-desc {\r\n    color: #8D8D8D;\r\n    line-height: 1.5;\r\n    font-size: 14px;\r\n    height:4.3rem;\r\n    margin-bottom: 10px;\r\n    display: -webkit-box;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    -webkit-line-clamp: 3;\r\n    -webkit-box-orient: vertical;\r\n    word-break: break-word;\r\n\r\n  }\r\n  .MuiLinearProgress-root{\r\n    border-top-left-radius: 20px;\r\n    border-top-right-radius: 20px;\r\n  }\r\n\r\n  .qadpt-editor{\r\n    border-radius: 12px;\r\nborder: 0.6px solid #717171;\r\nbackground: rgba(9, 17, 17, 0.32);\r\nbackdrop-filter: blur(5.349999904632568px);\r\npadding: 10px 12px;\r\nwidth: calc(50% - 550px);\r\n    margin: 20px;\r\n    z-index: 9999999;\r\n    position: fixed;\r\n    top: 0;\r\n    display: flex;\r\n        align-items: center;\r\n        place-content: center;\r\n        left: 0;\r\n        cursor: pointer;\r\n}\r\n.qadpt-editor.qadpt-baneditor{\r\n    width: calc(50% - 600px);\r\n\r\n}\r\n.qadpt-editor button{\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n    padding: 0;\r\n    width: auto;\r\n\r\n}\r\n\r\n.qadpt-editor button svg{\r\n    display: flex;\r\n}\r\n.qadpt-editor button .qadpt-bansep svg,\r\n.qadpt-editor button .qadpt-bansep path,\r\n.qadpt-editor button .qadpt-sep svg,\r\n.qadpt-editor button .qadpt-sep path {\r\n    fill: #fff;\r\n    stroke: #fff;\r\n}\r\n\r\n\r\n.qadpt-editor button .qadpt-sep{\r\n    border-right: 1px solid #ccc;\r\n    display: flex ;\r\n    flex-direction: row;\r\n    gap: 12px;\r\n    align-items: center;\r\n}\r\n.qadpt-editor button .qadpt-bansep{\r\n    display: flex ;\r\n    flex-direction: row;\r\n    gap: 12px;\r\n    align-items: center;\r\n}\r\n.qadpt-editor button .edt-txt{\r\n    margin-right: 10px;\r\n    color: #fff;\r\n    text-transform: capitalize;\r\n}\r\n.qadpt-editor.qadpt-baneditor button .edt-txt{\r\n    margin-right: 0px !important;\r\n}\r\n.qadpt-editor .qadpt-curstep{\r\n    color: #fff;\r\n    text-transform: capitalize;\r\n    margin-left: 11px;\r\n    font-weight: 500;\r\n    cursor: default;\r\n}\r\n\r\n.MuiTextField-root fieldset{\r\n    background-color: transparent !important;\r\n}\r\n.MuiTextField-root:not(.qadpt-webclonepopup .MuiTextField-root) legend {\r\n    display: none;\r\n}\r\n.MuiInputBase-root fieldset{\r\n    background-color: transparent !important;\r\n}\r\n.MuiInputBase-root:not(.qadpt-selectaccount):not(.qadpt-webclonepopup .MuiInputBase-root) legend {\r\n    display: none;\r\n}\r\n\r\nbutton:focus,\r\nbutton:hover {\r\n  /* border-color: inherit !important; */\r\n  box-shadow: none !important;\r\n}\r\n.MuiTextField-root input:focus {\r\n    box-shadow: none !important;\r\n    color: inherit !important;\r\n    outline: none !important;\r\n    border :none !important;\r\n  }\r\n\r\n  input:-webkit-autofill {\r\n    box-shadow: 0 0 0 1000px white inset !important;\r\n    -webkit-text-fill-color: inherit !important;\r\n  }\r\n  /* .qadpt-imgsec-popover{ \r\n    z-index: 99999 !important;\r\n  }\r\n  .qadpt-bunprop{\r\n    z-index: 99999 !important;\r\n  }\r\n  .qadpt-imgset{\r\n    z-index: 99999 !important; \r\n  }\r\n  .qadpt-secprop{\r\n    z-index: 99999 !important;\r\n  } */\r\n\r\n.MuiPopover-root:not(.qadpt-turstp):not(.qadpt-index){\r\n    z-index: 999999 !important;\r\n}\r\n.MuiPopper-root:not(.qadpt-tlprte){\r\n    z-index:999999 !important;\r\n}\r\n\r\n.qadpt-image-upload svg {\r\n    border: 1px solid #5F9EA0;\r\n    border-radius: 8px;\r\n    width: 40px;\r\n    height: 40px;\r\n    background: #fff;\r\n}\r\n\r\n.qadpt-image-upload svg rect {\r\n    stroke: none;\r\n}\r\n\r\n.MuiDialog-root{\r\n    z-index:99999 !important; \r\n}\r\n\r\n.jodit-react-container br{\r\n    display: inline !important;\r\n}\r\n.body.dynamic-body-style header {\r\n    position: relative !important;\r\n}\r\n.ps__rail-x {\r\n    display: none !important;\r\n  }\r\n\r\n  /* tour template starts */\r\n  .qadpt-modal {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 999;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-tours-container {\r\n    width: 100%;\r\n    max-width: 663px;\r\n    background-color: white;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n    border: 1px solid #e0e0e0;\r\n    border-radius: 12px;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-tour-header {\r\n    text-align: left;\r\n    padding: 10px 15px;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-header-content {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-title {\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-step-label {\r\n    background-color: #c8e7e8;\r\n    border-radius: 4px;\r\n    font-size: 10px;\r\n    width: 45px;\r\n    height: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-left: 8px;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-subtitle {\r\n    font-size: 13px;\r\n    color: #b1b1b1;\r\n    line-height: 19.5px;\r\n    letter-spacing: 0.3px;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-tours-content {\r\n    display: flex;\r\n    flex-direction: row;\r\n    justify-content: center;\r\n    gap: 10px;\r\n    padding: 10px 15px;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-feature-card {\r\n    width: 145px;\r\n    height: 218px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    border-radius: 7px;\r\n    padding: 12px 10px;\r\n    border: 0.5px solid #e0e0e0;\r\n    background: #fff;\r\n    transition: all 0.2s ease-in-out;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-feature-active {\r\n    border-color: #5f9ea0;\r\n    background-color: #f6ffff;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-feature-icon {\r\n    font-size: 24px;\r\n    font-weight: 600;\r\n    margin-block: 5px;\r\n    height: 90px;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-feature-title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    margin-bottom: 10px;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-feature-description {\r\n    font-size: 14px;\r\n    line-height: 16px;\r\n    letter-spacing: 0.3px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-tours-actions {\r\n    padding: 10px 15px;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-next-button {\r\n    padding: 8px 32px;\r\n    background-color: #5f9ea0;\r\n    color: #fff;\r\n    border-radius: 7px;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    border: none;\r\n    cursor: pointer;\r\n    transition: background-color 0.3s ease;\r\n  }\r\n  \r\n  .qadpt-modal .qadpt-disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n  \r\n  /* tour template ends */\r\n\r\n  .step-dropdown .qadpt-stpname{\r\n    display: block;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    font-size: 14px;\r\n    text-align: left;\r\n    width: 140px;\r\n  }\r\n  .qadpt-chklayout {\r\n    position: absolute;\r\n    bottom: 20px;\r\n    z-index: 999999;\r\n    right: 20px;\r\n    left: auto;\r\n  }\r\n  \r\n  .qadpt-chkpopup {\r\n  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px,\r\n              rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;\r\n  z-index: 9;\r\n  margin-top: auto;\r\n  margin-bottom: 80px;\r\n  margin-left: auto;\r\n  margin-right: 40px;\r\n  position: relative;\r\n}\r\n  .qadpt-chkpopup .qadpt-chkrgt{\r\n    width: 40%;\r\n\tborder-right: 1px solid #e5e7eb;\r\n\ttext-align: left;\r\n  }\r\n  .qadpt-chkpopup .qadpt-chkstpctn{\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 10px 16px 10px 10px;\r\n    cursor: pointer;\r\n    border-bottom: 1px solid #E8E8E8;\r\n  }\r\n  .qadpt-dismiss{\r\n    position: absolute !important;\r\n    box-shadow: rgba(0, 0, 0, 0.06) 0px 4px 8px;\r\n    background: #fff !important;\r\n    border: 1px solid #ccc;\r\n    z-index: 999;\r\n    border-radius: 50px;\r\n    padding: 5px !important;\r\n    float: right;\r\n    top: -12px;\r\n    right: -12px;\r\n  }\r\n  /* .qadpr-chkprvlayout{\r\n    position: absolute;\r\n  }\r\n  .qadpr-chkprvlayout.left {\r\n    left: var(--offset);\r\n    right: auto;\r\n  }\r\n  \r\n  .qadpr-chkprvlayout.right {\r\n    right: var(--offset);\r\n    left: auto;\r\n  }\r\n  \r\n  html[dir=\"rtl\"] .qadpr-chkprvlayout.right {\r\n    right: var(--offset);\r\n    left: auto;\r\n  }\r\n  \r\n  html[dir=\"rtl\"] .qadpr-chkprvlayout.left {\r\n    left: var(--offset);\r\n    right: auto;\r\n  } */\r\n  .qadpr-chkprvlayout {\r\n    position: absolute;\r\n    bottom: var(--y-offset);\r\n    z-index: 999999;\r\n  }\r\n  \r\n  .qadpr-chkprvlayout.left {\r\n    left: var(--x-offset);\r\n    right: auto;\r\n  }\r\n  \r\n  .qadpr-chkprvlayout.right {\r\n    right: var(--x-offset);\r\n    left: auto;\r\n  }\r\n  .qadpt-prvchkpopup.left-position {\r\n    margin-left: var(--x-offset);\r\n    margin-right: auto;\r\n  }\r\n  \r\n  .qadpt-prvchkpopup.right-position {\r\n    margin-left: auto;\r\n    margin-right: var(--x-offset);\r\n  }\r\n  .qadpt-elmslc{\r\n    color: #1B798E;\r\n     font-size: 16px;\r\n     margin-right: 8px;\r\n  }\r\n\r\n/* Language Selector Styling */\r\n.qadpt-language-selector {\r\n  margin-right: 8px;\r\n}\r\n\r\n.qadpt-language-selector .MuiIconButton-root {\r\n  color: #fff;\r\n  padding: 6px;\r\n}\r\n\r\n.qadpt-language-selector .MuiIconButton-root:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n.qadpt-accountcreatefield {\r\n\t\tposition: relative;\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 600;\r\n\t\tpadding: 15px;\r\n\t\t&.qadpt-error {\r\n\t\t\tcolor: var(--error-color);\r\n\t\t}\r\n\t\t.qadpt-acctfield {\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\t.MuiFormHelperText-root {\r\n\t\t\t\tcolor: var(--error-color);\r\n\t\t\t\tline-height: 12px;\r\n\t\t\t}\r\n\t\t\t.MuiInputBase-root-MuiOutlinedInput-root {\r\n\t\t\t\tborder-radius: 4px !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n.qadpt-accountcreatepopup,\r\n.qadpt-accounteditpopup {\r\n\tz-index: 99;\r\n\twidth: 400px !important;\r\n\ttop: 200px;\r\n\tright: 30%;\r\n\tposition: fixed !important;\r\n\tbackground: var(--white-color) !important;\r\n\tbox-shadow: 0 3px 8px #000000;\r\n\tborder-radius: 4px;\r\n\t.qadpt-title-sec {\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid var(--border-color);\r\n\t\t.qadpt-title {\r\n\t\t\tfont-size: 18px;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\t}\r\n\r\n\t.qadpt-accountcreatefield {\r\n\t\tposition: relative;\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 600;\r\n\t\tpadding: 15px;\r\n\t\t&.qadpt-error {\r\n\t\t\tcolor: var(--error-color);\r\n\t\t}\r\n\t\t.qadpt-acctfield {\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\t.MuiFormHelperText-root {\r\n\t\t\t\tcolor: var(--error-color);\r\n\t\t\t\tline-height: 12px;\r\n\t\t\t}\r\n\t\t\t.MuiInputBase-root-MuiOutlinedInput-root {\r\n\t\t\t\tborder-radius: 4px !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tform {\r\n\t\theight: calc(100% - 100px);\r\n\t}\r\n\t.qadpt-account-buttons {\r\n\t\tpadding: 15px;\r\n\t\tborder-top: 1px solid var(--border-color);\r\n\t\ttext-align: end;\r\n\t\t.qadpt-save-btn {\r\n\t\t\tbackground-color: var(--button-bg-color);\r\n\t\t\tcolor: var(--white-color);\r\n\t\t\ttext-transform: capitalize;\r\n\t\t\tpadding: var(--button-padding) !important;\r\n\t\t\tline-height: var(--button-lineheight) !important;\r\n\t\t\t&.invalid {\r\n\t\t\t\tbackground-color: #a5c3c5;\r\n\t\t\t\tpointer-events: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n.qadpt-tool-items .MuiButtonBase-root span {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  svg {\r\n    height: 20px;\r\n    width: 20px;\r\n  }\r\n}\r\n", ".rtl {\r\n\t.qadpt-overlay {\r\n\t\t.leftDrawer {\r\n\t\t\tright: 0 !important;\r\n\t\t\tleft: auto !important;\r\n\t\t\t&.collapsed{\r\n\t\t\t\tborder-top-left-radius: 12px !important;\r\n    border-bottom-left-radius: 12px !important;\r\n\t\t\t}\r\n\t\t\t.qadpt-toggleIcon svg {\r\n\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.qadptDrawerContent {\r\n\t\t.qadptFormLabel {\r\n\t\t\tfloat: right;\r\n\t\t}\r\n\t\t.qadptWelcomeMessage {\r\n\t\t\ttext-align: right !important;\r\n\t\t}\r\n\t}\r\n\t.qadpt-drawerContent {\r\n\t\t.qadpt-subhead .qadpt-backbtn {\r\n\t\t\tleft: auto !important;\r\n\t\t\tright: 20px;\r\n\t\t\ttransform: rotate(180deg);\r\n\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.qadpt-guide-form {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.qadpt-errmsg span {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin-left: 4px !important;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.qadpt-guide-label {\r\n\t\t\ttext-align: right !important;\r\n\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t.side-menu {\r\n\t\t\t.menu-list {\r\n  .menu-item {\r\n    .icons {\r\n      margin-left: 10px;\r\n      margin-right: 0 !important;\r\n    }\r\n\r\n    &[data-id=\"banners\"] .icons svg,\r\n    &[data-id=\"tooltips\"] .icons svg,\r\n    &[data-id=\"survey\"] .icons svg {\r\n     transform: scaleX(-1);\r\n    }\r\n  }\r\n}\r\n\r\n\t\t\t.qadpt-ai-container {\r\n\t\t\t\t.qadpt-ai-title {\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t\t.beta {\r\n\t\t\t\t\tleft: 23px !important;\r\n\t\t\t\t\tright: auto;\r\n\t\t\t\t\tborder-radius: 0px 6px 6px 0px;\r\n\t\t\t\t}\r\n\t\t\t\t.qadpt-icon {\r\n\t\t\t\t\tmargin-right: 0px !important;\r\n\t\t\t\t\tmargin-left: 10px !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.qadpt-threedot-popup .qadpt-popup-icon {\r\n\t\tmargin-right: 0px;\r\n\t\tmargin-left: 10px;\r\n\t}\r\n\r\n\t.qadpt-gud-menupopup {\r\n\t\tmargin-right: 300px !important;\r\n\t\tmargin-left: auto !important;\r\n\t\t.qadpt-webgird {\r\n\t\t\t.MuiDataGrid-cell:nth-child(2),\r\n\t\t\t.MuiDataGrid-cell:nth-child(3),\r\n\t\t\t.MuiDataGrid-cell:nth-child(4) {\r\n\t\t\t\ttext-align: right !important;\r\n\t\t\t}\r\n\t\t}\r\n  }\r\n\t\t.qadpt-titsection .qadpt-memberButton svg {\r\n\t\t\tmargin-left: 8px;\r\n\t\t\tmargin-right: 0 !important;\r\n\t\t}\r\n    \r\n\t.qadpt-ext-banner {\r\n\t\t.qadpt-left-banner {\r\n\t\t\tpadding-left: auto;\r\n\t\t\tpadding-right: 10px;\r\n\t\t\t.qadpt-edtxt {\r\n\t\t\t\tmargin-right: 8px;\r\n\t\t\t\tmargin-left: 0 !important;\r\n\t\t\t\tpadding-right: 10px;\r\n\t\t\t\tpadding-left: 0 !important;\r\n\t\t\t\tborder-right: 2px solid rgb(204, 204, 204);\r\n\t\t\t\tborder-left: 0 !important;\r\n\t\t\t}\r\n\t\t\t.guidename-btn {\r\n\t\t\t\tmargin-left: auto !important;\r\n\t\t\t\tmargin-right: 6px;\r\n\t\t\t\t.back-icon {\r\n\t\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.qadpt-center-banner .qadpt-banner-button:not(.qadpt-icon) svg {\r\n\t\t\tmargin-right: 0px !important;\r\n\t\t\tmargin-left: -5px;\r\n\t\t}\r\n\t\t.qadpt-right-banner {\r\n\t\t\tjustify-content: left !important;\r\n\t\t\tpadding-left: 10px;\r\n\t\t\tpadding-right: auto;\r\n\t\t}\r\n\t}\r\n\t.qadpt-designpopup {\r\n\t\tright: 10px;\r\n\t\tleft: auto !important;\r\n\t\t&.qadpt-btnprop {\r\n\t\t\tleft: 190px !important;\r\n\t\t\tright: auto !important;\r\n\t\t}\r\n\t\t&.qadpt-banbtnprop {\r\n\t\t\tright: auto !important;\r\n\t\t\tleft: 300px !important;\r\n\t\t}\r\n\t\t&.qadpt-tltbtnprop {\r\n\t\t\tleft: 170px !important;\r\n\t\t\tright: auto !important;\r\n\t\t}\r\n\r\n\t\t.qadpt-controls {\r\n    .qadpt-design-btn {\r\n        .qadpt-hotsicon,.MuiButton-startIcon{\r\n\t\t\t\tmargin-left: 8px;\r\n\t\t\t\tmargin-right: -4px;\r\n\t\t\t}\r\n}\r\n.qadpt-gtnext span{\r\n  margin-right: 0px !important;\r\n  margin-left: 8px;\r\n\r\n}\r\n.qadpt-chos-btn p{\r\n  text-align: right !important;\r\n}\r\n\t\t\t.qadpt-position-grid .qadpt-ctrl-title {\r\n\t\t\t\ttext-align: right;\r\n\t\t\t}\r\n\t\t\t.qadpt-control-box {\r\n        &:not(.qadpt-chkcontrol-box) {\r\n            padding-left: 8px;\r\n            padding-right: 0 !important;\r\n          }\r\n\t\t\t\t.qadpt-control-label {\r\n\t\t\t\t\tmargin-left: auto;\r\n\t\t\t\t\tmargin-right: 0px;\r\n\t\t\t\t}\r\n\t\t\t\t.qadpt-control-input {\r\n\t\t\t\t\tmargin-left: 0;\r\n\t\t\t\t\tmargin-right: auto;\r\n\t\t\t\t\t.MuiInputBase-root {\r\n\t\t\t\t\t\tpadding-left: 14px;\r\n\t\t\t\t\t\tpadding-right: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t\t.qadpt-chkoffset{\r\n\t\t\t\t\tpadding-left: 8px;\r\n\t\t\t\t\tpadding-right: 0 !important;\r\n\t\t\t\t}\r\n   \r\n\t\t\t}\r\n\t\t}\r\n\t\t.qadpt-content .qadpt-design-header button svg {\r\n\t\t\ttransform: rotate(180deg);\r\n\t\t}\r\n\t}\r\n\t.qadpt-container.creation {\r\n\t\t.qadpt-options-menu {\r\n\t\t\tleft: 25px;\r\n\t\t\tright: auto !important;\r\n\t\t}\r\n\t}\r\n\t.qadpt-modal .qadpt-tours-container {\r\n\t\t.qadpt-step-label {\r\n\t\t\tmargin-right: 8px;\r\n\t\t\tmargin-left: 0 !important;\r\n\t\t}\r\n\t\t.qadpt-subtitle {\r\n\t\t\ttext-align: right;\r\n\t\t}\r\n\t}\r\n\t.step-dropdown .qadpt-stpname {\r\n\t\ttext-align: right !important;\r\n\t}\r\n\t.qadpt-chklayout {\r\n\t\tright: auto;\r\n\t\tleft: 20px;\r\n\t}\r\n\t// .qadpt-chkpopup {\r\n\t// \tmargin-left: 40px !important;\r\n\t// \tmargin-right: auto !important;\r\n\t// \t.qadpt-chkrgt {\r\n\t// \t\tborder-left: 1px solid rgb(229, 231, 235);\r\n\t// \t\tborder-right: 0 !important;\r\n\t// \t}\r\n\t// \t.qadpt-chkstp .qadpt-chkstpctn {\r\n\t// \t\tpadding-right: 20px !important;\r\n\t// \t\tpadding-left: 10px !important;\r\n\t// \t}\r\n\t// }\r\n\t.qadpt-editor {\r\n\t\tright: 0;\r\n\t\tleft: auto !important;\r\n\t\tbutton {\r\n\t\t\t.qadpt-sep {\r\n\t\t\t\tborder-left: 1px solid #ccc;\r\n\t\t\t\tborder-right: 0 !important;\r\n\t\t\t\tsvg {\r\n\t\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.qadpt-bansep svg{\t\t\t\t\t\r\n\t\t\t\ttransform: rotate(180deg);\r\n}\r\n\t\t\t.edt-txt {\r\n\t\t\t\tmargin-left: 10px;\r\n\t\t\t\tmargin-right: 0 !important;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.qadpt-curstep {\r\n\t\t\tmargin-right: 11px;\r\n\t\t\tmargin-left: 0 !important;\r\n\t\t}\r\n\t}\r\n\t.qadpt-dismiss {\r\n\t\tleft: -10px !important;\r\n\t\tright: auto !important;\r\n\t}\r\n\t.toggle-switch {\r\n\t\tinput:checked + .slider:before {\r\n\t\t\ttransform: translateX(0px);\r\n\t\t}\r\n\t\t.slider:before {\r\n\t\t\ttransform: translateX(20px);\r\n\t\t}\r\n\t}\r\n\t.del-icon {\r\n\t\tleft: -10px;\r\n\t\tright: auto;\r\n\t}\r\n\t.undo-redobtn {\r\n\t\tdisplay: inline-flex;\r\n\t\tdirection: ltr;\r\n\t}\r\n\t.MuiTablePagination-toolbar {\r\n\t\t.MuiTablePagination-input {\r\n\t\t\tmargin-right: 8px !important;\r\n\t\t\tmargin-left: 32px !important;\r\n\t\t}\r\n\t\t.MuiTablePagination-displayedRows,\r\n\t\t.MuiTablePagination-actions {\r\n\t\t\tdirection: ltr;\r\n\t\t}\r\n\t\t.MuiTablePagination-actions {\r\n\t\t\tmargin-left: 0px;\r\n\t\t\tmargin-right: 20px;\r\n\t\t}\r\n\t}\r\n\t.pwdicon-blk {\r\n\t\tmargin-left: 0px !important;\r\n\t}\r\n\r\n.MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--vertical {\r\n    right: auto;\r\n    left: 0;\r\n}\r\n// .qadpr-chkprvlayout { \r\n//   &.right {\r\n//     left: var(--x-offset) !important;\r\n//     right: auto;\r\n//   }\r\n// &.left {\r\n//     right: var(--x-offset) !important;\r\n//     left: auto;\r\n//   }\r\n// }\r\n// .qadpt-prvchkpopup{\r\n\r\n// &.right-position {\r\n//     margin-left: var(--x-offset);\r\n//     margin-right: auto;\r\n//     .qadpt-chkrgt{\r\n//         border-left: 1px solid rgb(229, 231, 235);\r\n//         border-right: 0 !important;\r\n//     }\r\n//   }\r\n  \r\n//   &.left-position {\r\n//     margin-left: auto;\r\n//     margin-right: var(--x-offset);\r\n//     .qadpt-chkrgt{\r\n//         border-left: 1px solid rgb(229, 231, 235);\r\n//         border-right: 0 !important;\r\n//     }\r\n//   }\r\n// }\r\n .qadpt-memberButton svg{\r\n    margin-left: 8px;\r\n    margin-right: 0 !important;\r\n\r\n}\r\n.qadpt-elmslc{\r\n     margin-left: 8px;\r\n\t margin-right: 0 !important;\r\n  }\r\n  .qadpt-acnt-drpdwn .MuiPaper-root.MuiPaper-elevation{\r\n\tright: 170px !important;\r\n\t}\r\n\t\r\n\t// .jodit_theme_default.jodit-popup_strategy_leftbottom {\r\n\t// \tright: 630px !important;\r\n\t// \tleft: auto !important;\r\n\t// }\r\n\t// \t\t\t.jodit_theme_default.jodit-popup_strategy_lefttop {\r\n\t// \t\t\t\tbottom: 6px;\r\n\t// \t\t\t\ttop: auto !important;\r\n\t// \t\t\t\tleft: auto !important;\r\n\t// \t\t\t\tright: 630px;\r\n\t// \t\t\t}\r\n\r\n\t.jodit-ui-checkbox .jodit-ui-checkbox__wrapper .jodit-ui-checkbox__input {\r\n\t\tmargin: 3px 4px 3px 3px !important;\r\n\t}\r\n\t.jodit-ui-input__wrapper .jodit-ui-input__input{\r\n\t\ttext-align: right !important;\r\n\t}\r\n\t\r\n\t.MuiInputBase-root {\r\n\t\t.MuiSelect-select {\r\n\t\t\tpadding-left: 32px !important;\r\n\t\t\tpadding-right: 8px !important;\r\n\t\t\ttext-align: right !important;\r\n\t\t}\r\n\t\r\n\t\t.MuiSelect-icon {\r\n\t\t\tright: auto;\r\n\t\t\tleft: 7px;\r\n\t\t}\r\n}\r\n .jodit-popup,.jodit-toolbar__box, .jodit-ui-group {\r\n    direction: rtl !important;\r\n    text-align: right !important;\r\n}\r\n.qadpt-chat-window{\r\n\tright: 0;\r\n\tleft : auto !important;\r\n}\r\n.qadpt-tours-container .qadpt-tours-content .qadpt-feature-icon svg{\r\n\ttransform: scaleX(-1);\r\n}\r\n.qadpt-toaster .MuiAlert-message{\r\n\ttext-align: right !important;\r\n}\r\n .qadpt-webclonepopup .qadpt-close {  \r\n    left: 20px !important;\r\n\tright : auto !important;    \r\n }\r\n}", ".enable-ai-button {\r\n    position: fixed;\r\n    right: 20px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 10px 16px;\r\n    background-color: #5F9EA0;\r\n    border: none;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    color: white;\r\n    z-index: 1000;\r\n    transition: background-color 0.3s;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  .enable-ai-button:hover {\r\n    background-color: #4F8E90;\r\n  }\r\n\r\n  .stop-scraping-button {\r\n    background-color: #e74c3c;\r\n    top: calc(50% + 60px); /* Position below the Enable AI button */\r\n  }\r\n\r\n  .stop-scraping-button:hover {\r\n    background-color: #c0392b;\r\n  }\r\n\r\n  .enable-ai-icon {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n\r\n  .enable-ai-text {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }", "/* Removed fixed positioning - now handled dynamically in component */\r\n\r\n.qadpt-imgsec-popover .qadpt-tool-btn{\r\n    display: flex;\r\n    /* justify-content: space-between; */\r\n    align-items: center;\r\n    height: 100%;\r\n    padding: 0 12px;\r\n    font-size: 12px;\r\n}\r\n\r\n.qadpt-imgsec-popover .qadpt-tool-items {\r\n    display: flex;\r\n    align-items: center;\r\n    gap:8px; \r\n    font-size:12px;\r\n    cursor: pointer;\r\n}\r\n.qadpt-container-box {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n    padding: 0;\r\n    margin: 0;\r\n    overflow: auto\r\n}\r\n.qadpt-color-picker{\r\n    width: 20px;\r\n    height: 20px;\r\n    padding: 0 !important;\r\n    border-radius: 50%;\r\n    border: 1px solid var(--border-color);\r\n}\r\n\r\n.qadpt-color-picker::-webkit-color-swatch-wrapper {\r\n    padding: 0;\r\n    border-radius: 50%;\r\n}\r\n.qadpt-color-picker::-webkit-color-swatch  {\r\n    border-radius: 50%;\r\n    border: none;\r\n}\r\n", "/*\n * Container style\n */\n.ps {\n  overflow: hidden !important;\n  overflow-anchor: none;\n  -ms-overflow-style: none;\n  touch-action: auto;\n  -ms-touch-action: auto;\n}\n\n/*\n * Scrollbar rail styles\n */\n.ps__rail-x {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  height: 15px;\n  /* there must be 'bottom' or 'top' for ps__rail-x */\n  bottom: 0px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-y {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  width: 15px;\n  /* there must be 'right' or 'left' for ps__rail-y */\n  right: 0;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps--active-x > .ps__rail-x,\n.ps--active-y > .ps__rail-y {\n  display: block;\n  background-color: transparent;\n}\n\n.ps:hover > .ps__rail-x,\n.ps:hover > .ps__rail-y,\n.ps--focus > .ps__rail-x,\n.ps--focus > .ps__rail-y,\n.ps--scrolling-x > .ps__rail-x,\n.ps--scrolling-y > .ps__rail-y {\n  opacity: 0.6;\n}\n\n.ps .ps__rail-x:hover,\n.ps .ps__rail-y:hover,\n.ps .ps__rail-x:focus,\n.ps .ps__rail-y:focus,\n.ps .ps__rail-x.ps--clicking,\n.ps .ps__rail-y.ps--clicking {\n  background-color: #eee;\n  opacity: 0.9;\n}\n\n/*\n * Scrollbar thumb styles\n */\n.ps__thumb-x {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, height .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, height .2s ease-in-out;\n  height: 6px;\n  /* there must be 'bottom' for ps__thumb-x */\n  bottom: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__thumb-y {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, width .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, width .2s ease-in-out;\n  width: 6px;\n  /* there must be 'right' for ps__thumb-y */\n  right: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-x:hover > .ps__thumb-x,\n.ps__rail-x:focus > .ps__thumb-x,\n.ps__rail-x.ps--clicking .ps__thumb-x {\n  background-color: #999;\n  height: 11px;\n}\n\n.ps__rail-y:hover > .ps__thumb-y,\n.ps__rail-y:focus > .ps__thumb-y,\n.ps__rail-y.ps--clicking .ps__thumb-y {\n  background-color: #999;\n  width: 11px;\n}\n\n/* MS supports */\n@supports (-ms-overflow-style: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./node_modules/_perfect-scrollbar@1.5.0@perfect-scrollbar/css/perfect-scrollbar.css", "/*\n * Container style\n */\n.ps {\n  overflow: hidden !important;\n  overflow-anchor: none;\n  -ms-overflow-style: none;\n  touch-action: auto;\n  -ms-touch-action: auto;\n}\n\n/*\n * Scrollbar rail styles\n */\n.ps__rail-x {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  height: 15px;\n  /* there must be 'bottom' or 'top' for ps__rail-x */\n  bottom: 0px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-y {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  width: 15px;\n  /* there must be 'right' or 'left' for ps__rail-y */\n  right: 0;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps--active-x > .ps__rail-x,\n.ps--active-y > .ps__rail-y {\n  display: block;\n  background-color: transparent;\n}\n\n.ps:hover > .ps__rail-x,\n.ps:hover > .ps__rail-y,\n.ps--focus > .ps__rail-x,\n.ps--focus > .ps__rail-y,\n.ps--scrolling-x > .ps__rail-x,\n.ps--scrolling-y > .ps__rail-y {\n  opacity: 0.6;\n}\n\n.ps .ps__rail-x:hover,\n.ps .ps__rail-y:hover,\n.ps .ps__rail-x:focus,\n.ps .ps__rail-y:focus,\n.ps .ps__rail-x.ps--clicking,\n.ps .ps__rail-y.ps--clicking {\n  background-color: #eee;\n  opacity: 0.9;\n}\n\n/*\n * Scrollbar thumb styles\n */\n.ps__thumb-x {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, height .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, height .2s ease-in-out;\n  height: 6px;\n  /* there must be 'bottom' for ps__thumb-x */\n  bottom: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__thumb-y {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, width .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, width .2s ease-in-out;\n  width: 6px;\n  /* there must be 'right' for ps__thumb-y */\n  right: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-x:hover > .ps__thumb-x,\n.ps__rail-x:focus > .ps__thumb-x,\n.ps__rail-x.ps--clicking .ps__thumb-x {\n  background-color: #999;\n  height: 11px;\n}\n\n.ps__rail-y:hover > .ps__thumb-y,\n.ps__rail-y:focus > .ps__thumb-y,\n.ps__rail-y.ps--clicking .ps__thumb-y {\n  background-color: #999;\n  width: 11px;\n}\n\n/* MS supports */\n@supports (-ms-overflow-style: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n.scrollbar-container {\n  position: relative;\n  height: 100%; }\n\n", "@import '../node_modules/perfect-scrollbar/css/perfect-scrollbar.css';\n\n.scrollbar-container {\n  position: relative;\n  height: 100%;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/styles.scss", ".qadptDrawerContent{\r\n    width: 100%;\r\n  margin-top: 20px;\r\n}\r\n.qadptWelcomeMessage{\r\n    font-weight: 600;\r\n    text-align: left;\r\n    padding: 14px;\r\n}\r\n.qadptLoginForm{\r\n    margin-top: 20px;\r\n    padding: 0px 10px 20px 10px;\r\n}\r\n.qadptFormLabel{\r\n    font-size: 14px;\r\n      margin-top: 10px;\r\n      text-align: left;\r\n}\r\n.qadpt-txtfld{\r\n    font-size: 16px !important;\r\n  font-weight: 400 !important;\r\n  padding: 12px !important;\r\n  border: 1px solid var(--border-color) !important;\r\n  border-radius: 6px !important;\r\n  box-shadow: none !important;\r\n  background-color: var(--white-color) !important;\r\n  margin-top: 10px !important;\r\n  width: -webkit-fill-available !important;\r\n}\r\n.qadpt-txtfld .MuiInputBase-input{\r\n    height: 18px !important;\r\n    border: none !important;\r\n    padding:0 !important;\r\n}\r\n.qadpt-pwdicon{\r\n    background-color: transparent !important;\r\n    border: none !important;\r\n    padding: 0 !important;\r\n    margin: 0 !important;\r\n}\r\n.qadpt-pwdicon span{\r\n    display: flex;\r\n}\r\n\r\n.qadptInvalidCreds{\r\n    font-size: 14px;\r\n}\r\n.qadptForgotPwd{\r\n    color: var(--primarycolor);\r\n    cursor: pointer;\r\n    font-size: 16px;\r\n    font-weight: 400;\r\n    line-height: 24px;\r\n    margin-bottom: 10px;\r\n    text-align: left;\r\n}\r\n.qadptBtn{\r\n    background-color: var(--primarycolor) !important;\r\n    color: #fff;\r\n    border: none;\r\n    padding: 10px 12px;\r\n    cursor: pointer;\r\n    font-size: 16px;\r\n    border-radius: 12px;\r\n    width: 100%;\r\n    margin-top: 10px;\r\n    line-height: 20px;\r\n    text-transform: none;\r\n}", ".common-icon {\r\n\ttext-align: center;\r\n\twidth: 60px;\r\n\theight: 60px;\r\n\tpadding-top: 10px;\r\n\tmargin: auto;\r\n\tbackground: #ccc;\r\n\tborder-radius: 100%;\r\n}\r\n\r\n.common-icon:before {\r\n    font-size: 36px;\r\n    font-weight: 100;\r\n    color: #444;\r\n   \r\n}\r\n\r\n/* i:before {\r\n    font-family: \"qadapt-icons\" !important; \r\n    font-style: normal;\r\n} */\r\n\r\n\r\n.fa-analysis:before {\r\n\tcontent: \"\\a003\";\r\n}\r\n.fa-attendance-machine:before {\r\n\tcontent: \"\\a004\";\r\n}\r\n.fa-bike-insurance:before {\r\n\tcontent: \"\\a005\";\r\n}\r\n.fa-bill-receipt:before {\r\n\tcontent: \"\\a006\";\r\n}\r\n.fa-business-communication:before {\r\n\tcontent: \"\\a007\";\r\n}\r\n.fa-business-investment:before {\r\n\tcontent: \"\\a008\";\r\n}\r\n.fa-business-management:before {\r\n\tcontent: \"\\a009\";\r\n}\r\n.fa-businessman-with-briefcase:before {\r\n\tcontent: \"\\a010\";\r\n}\r\n.fa-business-presentation:before {\r\n\tcontent: \"\\a011\";\r\n}\r\n.fa-business-professional:before {\r\n\tcontent: \"\\a012\";\r\n}\r\n.fa-business-profit:before {\r\n\tcontent: \"\\a013\";\r\n}\r\n.fa-business-relationship:before {\r\n\tcontent: \"\\a014\";\r\n}\r\n.fa-buyer:before {\r\n\tcontent: \"\\a015\";\r\n}\r\n.fa-career:before {\r\n\tcontent: \"\\a016\";\r\n}\r\n.fa-car-insurance:before {\r\n\tcontent: \"\\a017\";\r\n}\r\n.fa-car-repair-mechanic:before {\r\n\tcontent: \"\\a018\";\r\n}\r\n.fa-cashier:before {\r\n\tcontent: \"\\a019\";\r\n}\r\n.fa-ceo:before {\r\n\tcontent: \"\\a020\";\r\n}\r\n.fa-client:before {\r\n\tcontent: \"\\a021\";\r\n}\r\n.fa-clients:before {\r\n\tcontent: \"\\a022\";\r\n}\r\n.fa-closed:before {\r\n\tcontent: \"\\a023\";\r\n}\r\n.fa-contract:before {\r\n\tcontent: \"\\a024\";\r\n}\r\n.fa-core-values:before {\r\n\tcontent: \"\\a025\";\r\n}\r\n.fa-corporate:before {\r\n\tcontent: \"\\a026\";\r\n}\r\n.fa-credit-card-swipe:before {\r\n\tcontent: \"\\a027\";\r\n}\r\n.fa-crm-browser:before {\r\n\tcontent: \"\\a028\";\r\n}\r\n.fa-customer-experience:before {\r\n\tcontent: \"\\a029\";\r\n}\r\n.fa-customer-journey:before {\r\n\tcontent: \"\\a030\";\r\n}\r\n.fa-data-analytics:before {\r\n\tcontent: \"\\a031\";\r\n}\r\n.fa-data-science:before {\r\n\tcontent: \"\\a032\";\r\n}\r\n.fa-document-application:before {\r\n\tcontent: \"\\a033\";\r\n}\r\n.fa-document-application-woman:before {\r\n\tcontent: \"\\a034\";\r\n}\r\n.fa-erp:before {\r\n\tcontent: \"\\a035\";\r\n}\r\n.fa-factory-pollution:before {\r\n\tcontent: \"\\a036\";\r\n}\r\n.fa-family-insurance:before {\r\n\tcontent: \"\\a037\";\r\n}\r\n.fa-female-reporter-journalist:before {\r\n\tcontent: \"\\a038\";\r\n}\r\n.fa-fire-insurance:before {\r\n\tcontent: \"\\a039\";\r\n}\r\n.fa-food-industry:before {\r\n\tcontent: \"\\a040\";\r\n}\r\n.fa-general-insurance:before {\r\n\tcontent: \"\\a041\";\r\n}\r\n.fa-growing-market-analysis:before {\r\n\tcontent: \"\\a042\";\r\n}\r\n.fa-gst:before {\r\n\tcontent: \"\\a043\";\r\n}\r\n.fa-headquarter:before {\r\n\tcontent: \"\\a044\";\r\n}\r\n.fa-health-insurance:before {\r\n\tcontent: \"\\a045\";\r\n}\r\n.fa-hierarchy-management:before {\r\n\tcontent: \"\\a046\";\r\n}\r\n.fa-hierarchy-management-task:before {\r\n\tcontent: \"\\a047\";\r\n}\r\n.fa-home-insurance:before {\r\n\tcontent: \"\\a048\";\r\n}\r\n.fa-import-product:before {\r\n\tcontent: \"\\a049\";\r\n}\r\n.fa-improvement-performance:before {\r\n\tcontent: \"\\a050\";\r\n}\r\n.fa-income-taxes:before {\r\n\tcontent: \"\\a051\";\r\n}\r\n.fa-influencer:before {\r\n\tcontent: \"\\a052\";\r\n}\r\n.fa-insight:before {\r\n\tcontent: \"\\a053\";\r\n}\r\n.fa-inspection:before {\r\n\tcontent: \"\\a054\";\r\n}\r\n.fa-insurance-protection:before {\r\n\tcontent: \"\\a055\";\r\n}\r\n.fa-integration:before {\r\n\tcontent: \"\\a056\";\r\n}\r\n.fa-interview:before {\r\n\tcontent: \"\\a057\";\r\n}\r\n.fa-investor:before {\r\n\tcontent: \"\\a058\";\r\n}\r\n.fa-invoice:before {\r\n\tcontent: \"\\a059\";\r\n}\r\n.fa-job:before {\r\n\tcontent: \"\\a060\";\r\n}\r\n.fa-job-search:before {\r\n\tcontent: \"\\a061\";\r\n}\r\n.fa-male-reporter-journalist:before {\r\n\tcontent: \"\\a062\";\r\n}\r\n.fa-management:before {\r\n\tcontent: \"\\a063\";\r\n}\r\n.fa-manufacturing-production:before {\r\n\tcontent: \"\\a064\";\r\n}\r\n.fa-market-research:before {\r\n\tcontent: \"\\a065\";\r\n}\r\n.fa-market-share:before {\r\n\tcontent: \"\\a066\";\r\n}\r\n.fa-mechanic:before {\r\n\tcontent: \"\\a067\";\r\n}\r\n.fa-meeting:before {\r\n\tcontent: \"\\a068\";\r\n}\r\n.fa-meeting-table:before {\r\n\tcontent: \"\\a069\";\r\n}\r\n.fa-mind-map:before {\r\n\tcontent: \"\\a070\";\r\n}\r\n.fa-money-transfer:before {\r\n\tcontent: \"\\a071\";\r\n}\r\n.fa-new-product:before {\r\n\tcontent: \"\\a072\";\r\n}\r\n.fa-newspaper:before {\r\n\tcontent: \"\\a073\";\r\n}\r\n.fa-newspaper-jobs:before {\r\n\tcontent: \"\\a074\";\r\n}\r\n.fa-office:before {\r\n\tcontent: \"\\a075\";\r\n}\r\n.fa-online-survey:before {\r\n\tcontent: \"\\a076\";\r\n}\r\n.fa-online-work:before {\r\n\tcontent: \"\\a077\";\r\n}\r\n.fa-pending-work:before {\r\n\tcontent: \"\\a078\";\r\n}\r\n.fa-person-insurance:before {\r\n\tcontent: \"\\a079\";\r\n}\r\n.fa-pilot:before {\r\n\tcontent: \"\\a080\";\r\n}\r\n.fa-planning:before {\r\n\tcontent: \"\\a081\";\r\n}\r\n.fa-plumbing:before {\r\n\tcontent: \"\\a082\";\r\n}\r\n.fa-power-plant:before {\r\n\tcontent: \"\\a083\";\r\n}\r\n.fa-product-development:before {\r\n\tcontent: \"\\a084\";\r\n}\r\n.fa-productivity:before {\r\n\tcontent: \"\\a085\";\r\n}\r\n.fa-product-launch-release:before {\r\n\tcontent: \"\\a086\";\r\n}\r\n.fa-project:before {\r\n\tcontent: \"\\a087\";\r\n}\r\n.fa-project-management:before {\r\n\tcontent: \"\\a088\";\r\n}\r\n.fa-project-management-timeline:before {\r\n\tcontent: \"\\a089\";\r\n}\r\n.fa-project-manager:before {\r\n\tcontent: \"\\a090\";\r\n}\r\n.fa-project-work:before {\r\n\tcontent: \"\\a091\";\r\n}\r\n.fa-quality-control:before {\r\n\tcontent: \"\\a092\";\r\n}\r\n.fa-receipt:before {\r\n\tcontent: \"\\a093\";\r\n}\r\n.fa-remote-work:before {\r\n\tcontent: \"\\a094\";\r\n}\r\n.fa-repairing:before {\r\n\tcontent: \"\\a095\";\r\n}\r\n.fa-retail-shop:before {\r\n\tcontent: \"\\a096\";\r\n}\r\n.fa-satisfaction:before {\r\n\tcontent: \"\\a097\";\r\n}\r\n.fa-seller:before {\r\n\tcontent: \"\\a098\";\r\n}\r\n.fa-service-desk:before {\r\n\tcontent: \"\\a099\";\r\n}\r\n.fa-services:before {\r\n\tcontent: \"\\a100\";\r\n}\r\n.fa-solution:before {\r\n\tcontent: \"\\a101\";\r\n}\r\n.fa-strategist:before {\r\n\tcontent: \"\\a102\";\r\n}\r\n.fa-successful-businessman:before {\r\n\tcontent: \"\\a103\";\r\n}\r\n.fa-supervisor:before {\r\n\tcontent: \"\\a104\";\r\n}\r\n.fa-supply-chain:before {\r\n\tcontent: \"\\a105\";\r\n}\r\n.fa-tax-calculator:before {\r\n\tcontent: \"\\a106\";\r\n}\r\n.fa-tax-cut:before {\r\n\tcontent: \"\\a107\";\r\n}\r\n.fa-tax-return:before {\r\n\tcontent: \"\\a108\";\r\n}\r\n.fa-team:before {\r\n\tcontent: \"\\a109\";\r\n}\r\n.fa-team-meeting:before {\r\n\tcontent: \"\\a110\";\r\n}\r\n.fa-technician:before {\r\n\tcontent: \"\\a111\";\r\n}\r\n.fa-trade:before {\r\n\tcontent: \"\\a112\";\r\n}\r\n.fa-user-network:before {\r\n\tcontent: \"\\a113\";\r\n}\r\n.fa-value:before {\r\n\tcontent: \"\\a114\";\r\n}\r\n.fa-vat:before {\r\n\tcontent: \"\\a115\";\r\n}\r\n.fa-video-conference:before {\r\n\tcontent: \"\\a116\";\r\n}\r\n.fa-virtual-meeting:before {\r\n\tcontent: \"\\a117\";\r\n}\r\n.fa-meeting-room:before {\r\n\tcontent: \"\\a118\";\r\n}\r\n.fa-workflow:before {\r\n\tcontent: \"\\a119\";\r\n}\r\n.fa-working-hours:before {\r\n\tcontent: \"\\a120\";\r\n}\r\n.fa-working-on-office:before {\r\n\tcontent: \"\\a121\";\r\n}\r\n.fa-working-time:before {\r\n\tcontent: \"\\a122\";\r\n}\r\n.fa-workplace:before {\r\n\tcontent: \"\\a123\";\r\n}\r\n.fa-workshop:before {\r\n\tcontent: \"\\a124\";\r\n}\r\n.fa-waiting-room-area:before {\r\n\tcontent: \"\\a125\";\r\n}\r\n.fa-user-tie-solid:before {\r\n\tcontent: \"\\a127\";\r\n}\r\n.fa-caret-up-solid:before {\r\n\tcontent: \"\\a128\";\r\n}\r\n.fa-check-circle-solid:before {\r\n\tcontent: \"\\a129\";\r\n}\r\n.fa-times-circle-solid:before {\r\n\tcontent: \"\\a130\";\r\n}\r\n.fa-password-reset:before {\r\n\tcontent: \"\\a131\";\r\n}\r\n.fa-password:before {\r\n\tcontent: \"\\a132\";\r\n}\r\n.fa-reset-password:before {\r\n\tcontent: \"\\a133\";\r\n}\r\n.fa-men-gear-circle:before {\r\n\tcontent: \"\\a134\";\r\n}\r\n.fa-men-gear:before {\r\n\tcontent: \"\\a135\";\r\n}\r\n.fa-light-ceilinglight:before {\r\n\tcontent: \"\\a147\";\r\n}\r\n.fa-exclamation-circle-solid:before {\r\n\tcontent: \"\\a148\";\r\n}\r\n.fa-add-multi-dtrecords:before {\r\n\tcontent: \"\\a149\";\r\n}\r\n.fa-branch:before {\r\n\tcontent: \"\\a150\";\r\n}\r\n.fa-deploy-log-download:before {\r\n\tcontent: \"\\a151\";\r\n}\r\n.fa-deployment-log:before {\r\n\tcontent: \"\\a152\";\r\n}\r\n.fa-deploy-rollback:before {\r\n\tcontent: \"\\a153\";\r\n}\r\n.fa-dt-download:before {\r\n\tcontent: \"\\a154\";\r\n}\r\n.fa-ds-filter:before {\r\n\tcontent: \"\\a155\";\r\n}\r\n.fa-dt-functions:before {\r\n\tcontent: \"\\a156\";\r\n}\r\n.fa-import-dtrecords:before {\r\n\tcontent: \"\\a157\";\r\n}\r\n.fa-dt-index:before {\r\n\tcontent: \"\\a158\";\r\n}\r\n.fa-ds-join:before {\r\n\tcontent: \"\\a159\";\r\n}\r\n.fa-manage-stages:before {\r\n\tcontent: \"\\a160\";\r\n}\r\n.fa-records:before {\r\n\tcontent: \"\\a161\";\r\n}\r\n.fa-regular-view:before {\r\n\tcontent: \"\\a162\";\r\n}\r\n.fa-dt-sync:before {\r\n\tcontent: \"\\a163\";\r\n}\r\n.fa-timeline-view:before {\r\n\tcontent: \"\\a164\";\r\n}\r\n.fa-manage-apps:before {\r\n\tcontent: \"\\a165\";\r\n}\r\n.fa-box-key:before {\r\n\tcontent: \"\\a166\";\r\n}\r\n.fa-deploy-branch:before {\r\n\tcontent: \"\\a167\";\r\n}\r\n.fa-version-manage:before {\r\n\tcontent: \"\\a168\";\r\n}\r\n.fa-add-ds:before {\r\n\tcontent: \"\\a169\";\r\n}\r\n.fa-add-dttable:before {\r\n\tcontent: \"\\a170\";\r\n}\r\n.fa-admenu:before {\r\n\tcontent: \"\\a171\";\r\n}\r\n.fa-apps:before {\r\n\tcontent: \"\\a172\";\r\n}\r\n.fa-appstore:before {\r\n\tcontent: \"\\a173\";\r\n}\r\n.fa-crreport:before {\r\n\tcontent: \"\\a174\";\r\n}\r\n.fa-crview:before {\r\n\tcontent: \"\\a175\";\r\n}\r\n.fa-datasource:before {\r\n\tcontent: \"\\a176\";\r\n}\r\n.fa-dsref:before {\r\n\tcontent: \"\\a177\";\r\n}\r\n.fa-dttable:before {\r\n\tcontent: \"\\a178\";\r\n}\r\n.fa-imp-user:before {\r\n\tcontent: \"\\a179\";\r\n}\r\n.fa-ip-white:before {\r\n\tcontent: \"\\a180\";\r\n}\r\n.fa-more:before {\r\n\tcontent: \"\\a181\";\r\n}\r\n.fa-multi-user:before {\r\n\tcontent: \"\\a182\";\r\n}\r\n.fa-pref:before {\r\n\tcontent: \"\\a183\";\r\n}\r\n.fa-config-sms:before {\r\n\tcontent: \"\\a184\";\r\n}\r\n.fa-ham-menu:before {\r\n\tcontent: \"\\a185\";\r\n}\r\n.fa-myroles:before {\r\n\tcontent: \"\\a186\";\r\n}\r\n.fa-dots:before {\r\n\tcontent: \"\\a187\";\r\n}\r\n.fa-add-field:before {\r\n\tcontent: \"\\a188\";\r\n}\r\n.fa-add-plus:before {\r\n\tcontent: \"\\a189\";\r\n}\r\n.fa-avatar:before {\r\n\tcontent: \"\\a190\";\r\n}\r\n.fa-back-arrow:before {\r\n\tcontent: \"\\a191\";\r\n}\r\n.fa-close:before {\r\n\tcontent: \"\\a192\";\r\n}\r\n.fa-close-1:before {\r\n\tcontent: \"\\a193\";\r\n}\r\n.fa-copy-icon:before {\r\n\tcontent: \"\\a194\";\r\n}\r\n.fa-drag:before {\r\n\tcontent: \"\\a195\";\r\n}\r\n.fa-editprofile:before {\r\n\tcontent: \"\\a196\";\r\n}\r\n.fa-group-by:before {\r\n\tcontent: \"\\a197\";\r\n}\r\n.fa-integrations:before {\r\n\tcontent: \"\\a198\";\r\n}\r\n.fa-logout:before {\r\n\tcontent: \"\\a199\";\r\n}\r\n.fa-caret-down-solid:before {\r\n\tcontent: \"\\a200\";\r\n}\r\n.fa-sort-down-solid:before {\r\n\tcontent: \"\\a201\";\r\n}\r\n.fa-mpin:before {\r\n\tcontent: \"\\a202\";\r\n}\r\n.fa-no-notifications:before {\r\n\tcontent: \"\\a203\";\r\n}\r\n.fa-notask:before {\r\n\tcontent: \"\\a204\";\r\n}\r\n.fa-password-lock:before {\r\n\tcontent: \"\\a205\";\r\n}\r\n.fa-preferences:before {\r\n\tcontent: \"\\a206\";\r\n}\r\n.fa-process:before {\r\n\tcontent: \"\\a207\";\r\n}\r\n.fa-profile-notifications:before {\r\n\tcontent: \"\\a208\";\r\n}\r\n.fa-profile-user:before {\r\n\tcontent: \"\\a209\";\r\n}\r\n.fa-reassign:before {\r\n\tcontent: \"\\a210\";\r\n}\r\n.fa-reportproblem:before {\r\n\tcontent: \"\\a211\";\r\n}\r\n.fa-right-arrow:before {\r\n\tcontent: \"\\a212\";\r\n}\r\n.fa-sort:before {\r\n\tcontent: \"\\a213\";\r\n}\r\n.fa-validation:before {\r\n\tcontent: \"\\a214\";\r\n}\r\n.fa-add-record:before {\r\n\tcontent: \"\\a215\";\r\n}\r\n.fa-dafts:before {\r\n\tcontent: \"\\a216\";\r\n}\r\n.fa-dashboard:before {\r\n\tcontent: \"\\a217\";\r\n}\r\n.fa-initiated:before {\r\n\tcontent: \"\\a218\";\r\n}\r\n.fa-manage-app:before {\r\n\tcontent: \"\\a219\";\r\n}\r\n.fa-menu:before {\r\n\tcontent: \"\\a220\";\r\n}\r\n.fa-participated:before {\r\n\tcontent: \"\\a221\";\r\n}\r\n.fa-reports:before {\r\n\tcontent: \"\\a222\";\r\n}\r\n.fa-requests:before {\r\n\tcontent: \"\\a223\";\r\n}\r\n.fa-tasks-circle:before {\r\n\tcontent: \"\\a224\";\r\n}\r\n.fa-tasks_old:before {\r\n\tcontent: \"\\a225\";\r\n}\r\n.fa-solutionview:before {\r\n\tcontent: \"\\a226\";\r\n}\r\n.fa-meeting-room-light:before {\r\n\tcontent: \"\\a227\";\r\n}\r\n.fa-external-rdbms:before {\r\n\tcontent: \"\\a228\";\r\n}\r\n.fa-pin-inclined:before {\r\n\tcontent: \"\\a229\";\r\n}\r\n.fa-generate-data:before {\r\n\tcontent: \"\\a230\";\r\n}\r\n.fa-dt-filter:before {\r\n\tcontent: \"\\a231\";\r\n}\r\n.fa-export-settings:before {\r\n\tcontent: \"\\a400\";\r\n}\r\n.fa-caravan-alt:before {\r\n\tcontent: \"\\e000\";\r\n}\r\n.fa-cat-space:before {\r\n\tcontent: \"\\e001\";\r\n}\r\n.fa-coffee-pot:before {\r\n\tcontent: \"\\e002\";\r\n}\r\n.fa-comet:before {\r\n\tcontent: \"\\e003\";\r\n}\r\n.fa-fan-table:before {\r\n\tcontent: \"\\e004\";\r\n}\r\n.fa-faucet:before {\r\n\tcontent: \"\\e005\";\r\n}\r\n.fa-faucet-drip:before {\r\n\tcontent: \"\\e006\";\r\n}\r\n.fa-galaxy:before {\r\n\tcontent: \"\\e008\";\r\n}\r\n.fa-garage:before {\r\n\tcontent: \"\\e009\";\r\n}\r\n.fa-garage-car:before {\r\n\tcontent: \"\\e00a\";\r\n}\r\n.fa-garage-open:before {\r\n\tcontent: \"\\e00b\";\r\n}\r\n.fa-heat:before {\r\n\tcontent: \"\\e00c\";\r\n}\r\n.fa-house-day:before {\r\n\tcontent: \"\\e00e\";\r\n}\r\n.fa-house-leave:before {\r\n\tcontent: \"\\e00f\";\r\n}\r\n.fa-house-night:before {\r\n\tcontent: \"\\e010\";\r\n}\r\n.fa-house-return:before {\r\n\tcontent: \"\\e011\";\r\n}\r\n.fa-house-signal:before {\r\n\tcontent: \"\\e012\";\r\n}\r\n.fa-lamp-desk:before {\r\n\tcontent: \"\\e014\";\r\n}\r\n.fa-lamp-floor:before {\r\n\tcontent: \"\\e015\";\r\n}\r\n.fa-light-ceiling:before {\r\n\tcontent: \"\\e016\";\r\n}\r\n.fa-light-switch:before {\r\n\tcontent: \"\\e017\";\r\n}\r\n.fa-light-switch-off:before {\r\n\tcontent: \"\\e018\";\r\n}\r\n.fa-microwave:before {\r\n\tcontent: \"\\e01b\";\r\n}\r\n.fa-raygun:before {\r\n\tcontent: \"\\e025\";\r\n}\r\n.fa-rocket-launch:before {\r\n\tcontent: \"\\e027\";\r\n}\r\n.fa-coffin-cross:before {\r\n\tcontent: \"\\e051\";\r\n}\r\n.fa-folder-download:before {\r\n\tcontent: \"\\e053\";\r\n}\r\n.fa-folder-upload:before {\r\n\tcontent: \"\\e054\";\r\n}\r\n.fa-bacteria:before {\r\n\tcontent: \"\\e059\";\r\n}\r\n.fa-bacterium:before {\r\n\tcontent: \"\\e05a\";\r\n}\r\n.fa-box-tissue:before {\r\n\tcontent: \"\\e05b\";\r\n}\r\n.fa-hand-holding-medical:before {\r\n\tcontent: \"\\e05c\";\r\n}\r\n.fa-hand-sparkles:before {\r\n\tcontent: \"\\e05d\";\r\n}\r\n.fa-hands-wash:before {\r\n\tcontent: \"\\e05e\";\r\n}\r\n.fa-handshake-alt-slash:before {\r\n\tcontent: \"\\e05f\";\r\n}\r\n.fa-handshake-slash:before {\r\n\tcontent: \"\\e060\";\r\n}\r\n.fa-head-side-cough:before {\r\n\tcontent: \"\\e061\";\r\n}\r\n.fa-head-side-cough-slash:before {\r\n\tcontent: \"\\e062\";\r\n}\r\n.fa-head-side-mask:before {\r\n\tcontent: \"\\e063\";\r\n}\r\n.fa-head-side-virus:before {\r\n\tcontent: \"\\e064\";\r\n}\r\n.fa-house-user:before {\r\n\tcontent: \"\\e065\";\r\n}\r\n.fa-laptop-house:before {\r\n\tcontent: \"\\e066\";\r\n}\r\n.fa-lungs-virus:before {\r\n\tcontent: \"\\e067\";\r\n}\r\n.fa-angle-double-up:before {\r\n\tcontent: \"\\e92a\";\r\n}\r\n.fa-drum-light:before {\r\n\tcontent: \"\\e963\";\r\n}\r\n.fa-file-signature:before {\r\n\tcontent: \"\\e98a\";\r\n}\r\n.fa-horse-head-light:before {\r\n\tcontent: \"\\ea12\";\r\n}\r\n.fa-image-light:before {\r\n\tcontent: \"\\ea28\";\r\n}\r\n.fa-inventory-light:before {\r\n\tcontent: \"\\ea2f\";\r\n}\r\n.fa-line-columns-light:before {\r\n\tcontent: \"\\ea52\";\r\n}\r\n.fa-location-arrow-light:before {\r\n\tcontent: \"\\ea56\";\r\n}\r\n.fa-location-circle:before {\r\n\tcontent: \"\\ea57\";\r\n}\r\n.fa-mailbox-light:before {\r\n\tcontent: \"\\ea63\";\r\n}\r\n.fa-map-marker-light:before {\r\n\tcontent: \"\\ea6a\";\r\n}\r\n.fa-mug-tea:before {\r\n\tcontent: \"\\ea94\";\r\n}\r\n.fa-music-alt-slash-light:before {\r\n\tcontent: \"\\ea95\";\r\n}\r\n.fa-network-wired-light:before {\r\n\tcontent: \"\\ea96\";\r\n}\r\n.fa-neuter-light:before {\r\n\tcontent: \"\\ea97\";\r\n}\r\n.fa-notes-medical-light:before {\r\n\tcontent: \"\\ea98\";\r\n}\r\n.fa-object-ungroup-light:before {\r\n\tcontent: \"\\ea99\";\r\n}\r\n.fa-oil-temp-light:before {\r\n\tcontent: \"\\ea9a\";\r\n}\r\n.fa-otter-light:before {\r\n\tcontent: \"\\ea9b\";\r\n}\r\n.fa-outdent-light:before {\r\n\tcontent: \"\\ea9c\";\r\n}\r\n.fa-outlet-light:before {\r\n\tcontent: \"\\ea9d\";\r\n}\r\n.fa-oven-light:before {\r\n\tcontent: \"\\ea9e\";\r\n}\r\n.fa-overline-light:before {\r\n\tcontent: \"\\ea9f\";\r\n}\r\n.fa-page-break-light:before {\r\n\tcontent: \"\\eaa0\";\r\n}\r\n.fa-chevron-left-light:before {\r\n\tcontent: \"\\eaa1\";\r\n}\r\n.fa-mobile-android-light:before {\r\n\tcontent: \"\\eaa2\";\r\n}\r\n.fa-comments-alt-dollar-light:before {\r\n\tcontent: \"\\eaa3\";\r\n}\r\n.fa-bus-alt:before {\r\n\tcontent: \"\\eaa4\";\r\n}\r\n.fa-bars-light---f0c9:before {\r\n\tcontent: \"\\eaa5\";\r\n}\r\n.fa-bath:before {\r\n\tcontent: \"\\eaa6\";\r\n}\r\n.fa-user-tag:before {\r\n\tcontent: \"\\eaa7\";\r\n}\r\n.fa-trophy-alt:before {\r\n\tcontent: \"\\eaa8\";\r\n}\r\n.fa-file-light---f15b:before {\r\n\tcontent: \"\\eaa9\";\r\n}\r\n.fa-grip-horizontal-light---f58d:before {\r\n\tcontent: \"\\eaaa\";\r\n}\r\n.fa-blinds-open:before {\r\n\tcontent: \"\\eaab\";\r\n}\r\n.fa-mailbox-light---f813:before {\r\n\tcontent: \"\\eaac\";\r\n}\r\n.fa-glass-martini:before {\r\n\tcontent: \"\\f000\";\r\n}\r\n.fa-music:before {\r\n\tcontent: \"\\f001\";\r\n}\r\n.fa-search:before {\r\n\tcontent: \"\\f002\";\r\n}\r\n.fa-heart:before {\r\n\tcontent: \"\\f004\";\r\n}\r\n.fa-star:before {\r\n\tcontent: \"\\f005\";\r\n}\r\n.fa-user:before {\r\n\tcontent: \"\\f007\";\r\n}\r\n.fa-film:before {\r\n\tcontent: \"\\f008\";\r\n}\r\n.fa-th:before {\r\n\tcontent: \"\\f00a\";\r\n}\r\n.fa-check:before {\r\n\tcontent: \"\\f00c\";\r\n}\r\n.fa-times:before {\r\n\tcontent: \"\\f00d\";\r\n}\r\n.fa-search-plus:before {\r\n\tcontent: \"\\f00e\";\r\n}\r\n.fa-search-minus:before {\r\n\tcontent: \"\\f010\";\r\n}\r\n.fa-power-off:before {\r\n\tcontent: \"\\f011\";\r\n}\r\n.fa-signal:before {\r\n\tcontent: \"\\f012\";\r\n}\r\n.fa-cog:before {\r\n\tcontent: \"\\f013\";\r\n}\r\n.fa-home:before {\r\n\tcontent: \"\\f015\";\r\n}\r\n.fa-clock:before {\r\n\tcontent: \"\\f017\";\r\n}\r\n.fa-road:before {\r\n\tcontent: \"\\f018\";\r\n}\r\n.fa-download:before {\r\n\tcontent: \"\\f019\";\r\n}\r\n.fa-inbox:before {\r\n\tcontent: \"\\f01c\";\r\n}\r\n.fa-redo:before {\r\n\tcontent: \"\\f01e\";\r\n}\r\n.fa-sync:before {\r\n\tcontent: \"\\f021\";\r\n}\r\n.fa-list-alt:before {\r\n\tcontent: \"\\f022\";\r\n}\r\n.fa-lock:before {\r\n\tcontent: \"\\f023\";\r\n}\r\n.fa-flag:before {\r\n\tcontent: \"\\f024\";\r\n}\r\n.fa-headphones:before {\r\n\tcontent: \"\\f025\";\r\n}\r\n.fa-volume-up:before {\r\n\tcontent: \"\\f028\";\r\n}\r\n.fa-qrcode:before {\r\n\tcontent: \"\\f029\";\r\n}\r\n.fa-barcode:before {\r\n\tcontent: \"\\f02a\";\r\n}\r\n.fa-tag:before {\r\n\tcontent: \"\\f02b\";\r\n}\r\n.fa-book:before {\r\n\tcontent: \"\\f02d\";\r\n}\r\n.fa-bookmark:before {\r\n\tcontent: \"\\f02e\";\r\n}\r\n.fa-print:before {\r\n\tcontent: \"\\f02f\";\r\n}\r\n.fa-camera:before {\r\n\tcontent: \"\\f030\";\r\n}\r\n.fa-font:before {\r\n\tcontent: \"\\f031\";\r\n}\r\n.fa-bold:before {\r\n\tcontent: \"\\f032\";\r\n}\r\n.fa-italic:before {\r\n\tcontent: \"\\f033\";\r\n}\r\n.fa-text-width:before {\r\n\tcontent: \"\\f035\";\r\n}\r\n.fa-align-left:before {\r\n\tcontent: \"\\f036\";\r\n}\r\n.fa-align-center:before {\r\n\tcontent: \"\\f037\";\r\n}\r\n.fa-align-right:before {\r\n\tcontent: \"\\f038\";\r\n}\r\n.fa-align-justify:before {\r\n\tcontent: \"\\f039\";\r\n}\r\n.fa-list:before {\r\n\tcontent: \"\\f03a\";\r\n}\r\n.fa-indent:before {\r\n\tcontent: \"\\f03c\";\r\n}\r\n.fa-video:before {\r\n\tcontent: \"\\f03d\";\r\n}\r\n.fa-image:before {\r\n\tcontent: \"\\f03e\";\r\n}\r\n.fa-pencil:before {\r\n\tcontent: \"\\f040\";\r\n}\r\n.fa-map-marker:before {\r\n\tcontent: \"\\f041\";\r\n}\r\n.fa-adjust:before {\r\n\tcontent: \"\\f042\";\r\n}\r\n.fa-tint:before {\r\n\tcontent: \"\\f043\";\r\n}\r\n.fa-edit:before {\r\n\tcontent: \"\\f044\";\r\n}\r\n.fa-arrows:before {\r\n\tcontent: \"\\f047\";\r\n}\r\n.fa-fast-backward:before {\r\n\tcontent: \"\\f049\";\r\n}\r\n.fa-backward:before {\r\n\tcontent: \"\\f04a\";\r\n}\r\n.fa-stop:before {\r\n\tcontent: \"\\f04d\";\r\n}\r\n.fa-forward:before {\r\n\tcontent: \"\\f04e\";\r\n}\r\n.fa-fast-forward:before {\r\n\tcontent: \"\\f050\";\r\n}\r\n.fa-eject:before {\r\n\tcontent: \"\\f052\";\r\n}\r\n.fa-chevron-left:before {\r\n\tcontent: \"\\f053\";\r\n}\r\n.fa-chevron-right:before {\r\n\tcontent: \"\\f054\";\r\n}\r\n.fa-plus-circle:before {\r\n\tcontent: \"\\f055\";\r\n}\r\n.fa-minus-circle:before {\r\n\tcontent: \"\\f056\";\r\n}\r\n.fa-times-circle:before {\r\n\tcontent: \"\\f057\";\r\n}\r\n.fa-check-circle:before {\r\n\tcontent: \"\\f058\";\r\n}\r\n.fa-question-circle:before {\r\n\tcontent: \"\\f059\";\r\n}\r\n.fa-info-circle:before {\r\n\tcontent: \"\\f05a\";\r\n}\r\n.fa-crosshairs:before {\r\n\tcontent: \"\\f05b\";\r\n}\r\n.fa-ban:before {\r\n\tcontent: \"\\f05e\";\r\n}\r\n.fa-arrow-left:before {\r\n\tcontent: \"\\f060\";\r\n}\r\n.fa-arrow-right:before {\r\n\tcontent: \"\\f061\";\r\n}\r\n.fa-arrow-up:before {\r\n\tcontent: \"\\f062\";\r\n}\r\n.fa-arrow-down:before {\r\n\tcontent: \"\\f063\";\r\n}\r\n.fa-share:before {\r\n\tcontent: \"\\f064\";\r\n}\r\n.fa-expand:before {\r\n\tcontent: \"\\f065\";\r\n}\r\n.fa-compress:before {\r\n\tcontent: \"\\f066\";\r\n}\r\n.fa-plus:before {\r\n\tcontent: \"\\f067\";\r\n}\r\n.fa-minus:before {\r\n\tcontent: \"\\f068\";\r\n}\r\n.fa-asterisk:before {\r\n\tcontent: \"\\f069\";\r\n}\r\n.fa-exclamation-circle:before {\r\n\tcontent: \"\\f06a\";\r\n}\r\n.fa-gift:before {\r\n\tcontent: \"\\f06b\";\r\n}\r\n.fa-leaf:before {\r\n\tcontent: \"\\f06c\";\r\n}\r\n.fa-fire:before {\r\n\tcontent: \"\\f06d\";\r\n}\r\n.fa-eye:before {\r\n\tcontent: \"\\f06e\";\r\n}\r\n.fa-eye-slash:before {\r\n\tcontent: \"\\f070\";\r\n}\r\n.fa-exclamation-triangle:before {\r\n\tcontent: \"\\f071\";\r\n}\r\n.fa-plane:before {\r\n\tcontent: \"\\f072\";\r\n}\r\n.fa-calendar-alt:before {\r\n\tcontent: \"\\f073\";\r\n}\r\n.fa-random:before {\r\n\tcontent: \"\\f074\";\r\n}\r\n.fa-comment:before {\r\n\tcontent: \"\\f075\";\r\n}\r\n.fa-magnet:before {\r\n\tcontent: \"\\f076\";\r\n}\r\n.fa-chevron-up:before {\r\n\tcontent: \"\\f077\";\r\n}\r\n.fa-chevron-down:before {\r\n\tcontent: \"\\f078\";\r\n}\r\n.fa-shopping-cart:before {\r\n\tcontent: \"\\f07a\";\r\n}\r\n.fa-folder:before {\r\n\tcontent: \"\\f07b\";\r\n}\r\n.fa-folder-open:before {\r\n\tcontent: \"\\f07c\";\r\n}\r\n.fa-arrows-v:before {\r\n\tcontent: \"\\f07d\";\r\n}\r\n.fa-arrows-h:before {\r\n\tcontent: \"\\f07e\";\r\n}\r\n.fa-chart-bar:before {\r\n\tcontent: \"\\f080\";\r\n}\r\n.fa-camera-retro:before {\r\n\tcontent: \"\\f083\";\r\n}\r\n.fa-key:before {\r\n\tcontent: \"\\f084\";\r\n}\r\n.fa-cogs:before {\r\n\tcontent: \"\\f085\";\r\n}\r\n.fa-comments:before {\r\n\tcontent: \"\\f086\";\r\n}\r\n.fa-sign-out:before {\r\n\tcontent: \"\\f08b\";\r\n}\r\n.fa-thumbtack:before {\r\n\tcontent: \"\\f08d\";\r\n}\r\n.fa-external-link:before {\r\n\tcontent: \"\\f08e\";\r\n}\r\n.fa-upload:before {\r\n\tcontent: \"\\f093\";\r\n}\r\n.fa-lemon:before {\r\n\tcontent: \"\\f094\";\r\n}\r\n.fa-phone-square:before {\r\n\tcontent: \"\\f098\";\r\n}\r\n.fa-credit-card:before {\r\n\tcontent: \"\\f09d\";\r\n}\r\n.fa-rss:before {\r\n\tcontent: \"\\f09e\";\r\n}\r\n.fa-hdd:before {\r\n\tcontent: \"\\f0a0\";\r\n}\r\n.fa-bullhorn:before {\r\n\tcontent: \"\\f0a1\";\r\n}\r\n.fa-certificate:before {\r\n\tcontent: \"\\f0a3\";\r\n}\r\n.fa-hand-point-right:before {\r\n\tcontent: \"\\f0a4\";\r\n}\r\n.fa-hand-point-left:before {\r\n\tcontent: \"\\f0a5\";\r\n}\r\n.fa-hand-point-up:before {\r\n\tcontent: \"\\f0a6\";\r\n}\r\n.fa-hand-point-down:before {\r\n\tcontent: \"\\f0a7\";\r\n}\r\n.fa-arrow-circle-left:before {\r\n\tcontent: \"\\f0a8\";\r\n}\r\n.fa-arrow-circle-right:before {\r\n\tcontent: \"\\f0a9\";\r\n}\r\n.fa-arrow-circle-up:before {\r\n\tcontent: \"\\f0aa\";\r\n}\r\n.fa-arrow-circle-down:before {\r\n\tcontent: \"\\f0ab\";\r\n}\r\n.fa-globe:before {\r\n\tcontent: \"\\f0ac\";\r\n}\r\n.fa-wrench:before {\r\n\tcontent: \"\\f0ad\";\r\n}\r\n.fa-tasks:before {\r\n\tcontent: \"\\f0ae\";\r\n}\r\n.fa-filter:before {\r\n\tcontent: \"\\f0b0\";\r\n}\r\n.fa-briefcase:before {\r\n\tcontent: \"\\f0b1\";\r\n}\r\n.fa-arrows-alt:before {\r\n\tcontent: \"\\f0b2\";\r\n}\r\n.fa-users:before {\r\n\tcontent: \"\\f0c0\";\r\n}\r\n.fa-link:before {\r\n\tcontent: \"\\f0c1\";\r\n}\r\n.fa-cloud:before {\r\n\tcontent: \"\\f0c2\";\r\n}\r\n.fa-flask:before {\r\n\tcontent: \"\\f0c3\";\r\n}\r\n.fa-cut:before {\r\n\tcontent: \"\\f0c4\";\r\n}\r\n.fa-copy:before {\r\n\tcontent: \"\\f0c5\";\r\n}\r\n.fa-paperclip:before {\r\n\tcontent: \"\\f0c6\";\r\n}\r\n.fa-save:before {\r\n\tcontent: \"\\f0c7\";\r\n}\r\n.fa-square:before {\r\n\tcontent: \"\\f0c8\";\r\n}\r\n.fa-bars:before {\r\n\tcontent: \"\\f0c9\";\r\n}\r\n.fa-list-ul:before {\r\n\tcontent: \"\\f0ca\";\r\n}\r\n.fa-list-ol:before {\r\n\tcontent: \"\\f0cb\";\r\n}\r\n.fa-table:before {\r\n\tcontent: \"\\f0ce\";\r\n}\r\n.fa-magic:before {\r\n\tcontent: \"\\f0d0\";\r\n}\r\n.fa-truck:before {\r\n\tcontent: \"\\f0d1\";\r\n}\r\n.fa-money-bill:before {\r\n\tcontent: \"\\f0d6\";\r\n}\r\n.fa-caret-down:before {\r\n\tcontent: \"\\f0d7\";\r\n}\r\n.fa-caret-up:before {\r\n\tcontent: \"\\f0d8\";\r\n}\r\n.fa-caret-left:before {\r\n\tcontent: \"\\f0d9\";\r\n}\r\n.fa-caret-right:before {\r\n\tcontent: \"\\f0da\";\r\n}\r\n.fa-columns:before {\r\n\tcontent: \"\\f0db\";\r\n}\r\n.fa-sort-down:before {\r\n\tcontent: \"\\f0dd\";\r\n}\r\n.fa-envelope:before {\r\n\tcontent: \"\\f0e0\";\r\n}\r\n.fa-undo:before {\r\n\tcontent: \"\\f0e2\";\r\n}\r\n.fa-gavel:before {\r\n\tcontent: \"\\f0e3\";\r\n}\r\n.fa-tachometer:before {\r\n\tcontent: \"\\f0e4\";\r\n}\r\n.fa-bolt:before {\r\n\tcontent: \"\\f0e7\";\r\n}\r\n.fa-sitemap:before {\r\n\tcontent: \"\\f0e8\";\r\n}\r\n.fa-umbrella:before {\r\n\tcontent: \"\\f0e9\";\r\n}\r\n.fa-paste:before {\r\n\tcontent: \"\\f0ea\";\r\n}\r\n.fa-lightbulb:before {\r\n\tcontent: \"\\f0eb\";\r\n}\r\n.fa-exchange:before {\r\n\tcontent: \"\\f0ec\";\r\n}\r\n.fa-cloud-download:before {\r\n\tcontent: \"\\f0ed\";\r\n}\r\n.fa-cloud-upload:before {\r\n\tcontent: \"\\f0ee\";\r\n}\r\n.fa-user-md:before {\r\n\tcontent: \"\\f0f0\";\r\n}\r\n.fa-stethoscope:before {\r\n\tcontent: \"\\f0f1\";\r\n}\r\n.fa-bell:before {\r\n\tcontent: \"\\f0f3\";\r\n}\r\n.fa-coffee:before {\r\n\tcontent: \"\\f0f4\";\r\n}\r\n.fa-hospital:before {\r\n\tcontent: \"\\f0f8\";\r\n}\r\n.fa-ambulance:before {\r\n\tcontent: \"\\f0f9\";\r\n}\r\n.fa-medkit:before {\r\n\tcontent: \"\\f0fa\";\r\n}\r\n.fa-fighter-jet:before {\r\n\tcontent: \"\\f0fb\";\r\n}\r\n.fa-beer:before {\r\n\tcontent: \"\\f0fc\";\r\n}\r\n.fa-h-square:before {\r\n\tcontent: \"\\f0fd\";\r\n}\r\n.fa-plus-square:before {\r\n\tcontent: \"\\f0fe\";\r\n}\r\n.fa-angle-double-left:before {\r\n\tcontent: \"\\f100\";\r\n}\r\n.fa-angle-double-right:before {\r\n\tcontent: \"\\f101\";\r\n}\r\n.fa-angle-double-down:before {\r\n\tcontent: \"\\f103\";\r\n}\r\n.fa-angle-left:before {\r\n\tcontent: \"\\f104\";\r\n}\r\n.fa-angle-right:before {\r\n\tcontent: \"\\f105\";\r\n}\r\n.fa-angle-up:before {\r\n\tcontent: \"\\f106\";\r\n}\r\n.fa-angle-down:before {\r\n\tcontent: \"\\f107\";\r\n}\r\n.fa-desktop:before {\r\n\tcontent: \"\\f108\";\r\n}\r\n.fa-laptop:before {\r\n\tcontent: \"\\f109\";\r\n}\r\n.fa-mobile:before {\r\n\tcontent: \"\\f10b\";\r\n}\r\n.fa-quote-left:before {\r\n\tcontent: \"\\f10d\";\r\n}\r\n.fa-quote-right:before {\r\n\tcontent: \"\\f10e\";\r\n}\r\n.fa-spinner:before {\r\n\tcontent: \"\\f110\";\r\n}\r\n.fa-circle:before {\r\n\tcontent: \"\\f111\";\r\n}\r\n.fa-smile:before {\r\n\tcontent: \"\\f118\";\r\n}\r\n.fa-frown:before {\r\n\tcontent: \"\\f119\";\r\n}\r\n.fa-meh:before {\r\n\tcontent: \"\\f11a\";\r\n}\r\n.fa-gamepad:before {\r\n\tcontent: \"\\f11b\";\r\n}\r\n.fa-keyboard:before {\r\n\tcontent: \"\\f11c\";\r\n}\r\n.fa-flag-checkered:before {\r\n\tcontent: \"\\f11e\";\r\n}\r\n.fa-terminal:before {\r\n\tcontent: \"\\f120\";\r\n}\r\n.fa-code:before {\r\n\tcontent: \"\\f121\";\r\n}\r\n.fa-location-arrow:before {\r\n\tcontent: \"\\f124\";\r\n}\r\n.fa-crop:before {\r\n\tcontent: \"\\f125\";\r\n}\r\n.fa-code-branch:before {\r\n\tcontent: \"\\f126\";\r\n}\r\n.fa-info:before {\r\n\tcontent: \"\\f129\";\r\n}\r\n.fa-exclamation:before {\r\n\tcontent: \"\\f12a\";\r\n}\r\n.fa-eraser:before {\r\n\tcontent: \"\\f12d\";\r\n}\r\n.fa-puzzle-piece:before {\r\n\tcontent: \"\\f12e\";\r\n}\r\n.fa-microphone:before {\r\n\tcontent: \"\\f130\";\r\n}\r\n.fa-microphone-slash:before {\r\n\tcontent: \"\\f131\";\r\n}\r\n.fa-shield:before {\r\n\tcontent: \"\\f132\";\r\n}\r\n.fa-calendar:before {\r\n\tcontent: \"\\f133\";\r\n}\r\n.fa-fire-extinguisher:before {\r\n\tcontent: \"\\f134\";\r\n}\r\n.fa-rocket:before {\r\n\tcontent: \"\\f135\";\r\n}\r\n.fa-chevron-circle-left:before {\r\n\tcontent: \"\\f137\";\r\n}\r\n.fa-chevron-circle-right:before {\r\n\tcontent: \"\\f138\";\r\n}\r\n.fa-chevron-circle-up:before {\r\n\tcontent: \"\\f139\";\r\n}\r\n.fa-chevron-circle-down:before {\r\n\tcontent: \"\\f13a\";\r\n}\r\n.fa-css3:before {\r\n\tcontent: \"\\f13c\";\r\n}\r\n.fa-anchor:before {\r\n\tcontent: \"\\f13d\";\r\n}\r\n.fa-unlock-alt:before {\r\n\tcontent: \"\\f13e\";\r\n}\r\n.fa-bullseye:before {\r\n\tcontent: \"\\f140\";\r\n}\r\n.fa-ellipsis-h:before {\r\n\tcontent: \"\\f141\";\r\n}\r\n.fa-ellipsis-v:before {\r\n\tcontent: \"\\f142\";\r\n}\r\n.fa-play-circle:before {\r\n\tcontent: \"\\f144\";\r\n}\r\n.fa-ticket:before {\r\n\tcontent: \"\\f145\";\r\n}\r\n.fa-minus-square:before {\r\n\tcontent: \"\\f146\";\r\n}\r\n.fa-level-up:before {\r\n\tcontent: \"\\f148\";\r\n}\r\n.fa-level-down:before {\r\n\tcontent: \"\\f149\";\r\n}\r\n.fa-check-square:before {\r\n\tcontent: \"\\f14a\";\r\n}\r\n.fa-external-link-square:before {\r\n\tcontent: \"\\f14c\";\r\n}\r\n.fa-share-square:before {\r\n\tcontent: \"\\f14d\";\r\n}\r\n.fa-compass:before {\r\n\tcontent: \"\\f14e\";\r\n}\r\n.fa-caret-square-down:before {\r\n\tcontent: \"\\f150\";\r\n}\r\n.fa-caret-square-up:before {\r\n\tcontent: \"\\f151\";\r\n}\r\n.fa-caret-square-right:before {\r\n\tcontent: \"\\f152\";\r\n}\r\n.fa-euro-sign:before {\r\n\tcontent: \"\\f153\";\r\n}\r\n.fa-pound-sign:before {\r\n\tcontent: \"\\f154\";\r\n}\r\n.fa-dollar-sign:before {\r\n\tcontent: \"\\f155\";\r\n}\r\n.fa-rupee-sign:before {\r\n\tcontent: \"\\f156\";\r\n}\r\n.fa-yen-sign:before {\r\n\tcontent: \"\\f157\";\r\n}\r\n.fa-ruble-sign:before {\r\n\tcontent: \"\\f158\";\r\n}\r\n.fa-file:before {\r\n\tcontent: \"\\f15b\";\r\n}\r\n.fa-file-alt:before {\r\n\tcontent: \"\\f15c\";\r\n}\r\n.fa-sort-numeric-down:before {\r\n\tcontent: \"\\f162\";\r\n}\r\n.fa-thumbs-up:before {\r\n\tcontent: \"\\f164\";\r\n}\r\n.fa-thumbs-down:before {\r\n\tcontent: \"\\f165\";\r\n}\r\n.fa-adn:before {\r\n\tcontent: \"\\f170\";\r\n}\r\n.fa-bitbucket:before {\r\n\tcontent: \"\\f171\";\r\n}\r\n.fa-long-arrow-down:before {\r\n\tcontent: \"\\f175\";\r\n}\r\n.fa-long-arrow-up:before {\r\n\tcontent: \"\\f176\";\r\n}\r\n.fa-long-arrow-left:before {\r\n\tcontent: \"\\f177\";\r\n}\r\n.fa-long-arrow-right:before {\r\n\tcontent: \"\\f178\";\r\n}\r\n.fa-android:before {\r\n\tcontent: \"\\f17b\";\r\n}\r\n.fa-female:before {\r\n\tcontent: \"\\f182\";\r\n}\r\n.fa-male:before {\r\n\tcontent: \"\\f183\";\r\n}\r\n.fa-sun:before {\r\n\tcontent: \"\\f185\";\r\n}\r\n.fa-moon:before {\r\n\tcontent: \"\\f186\";\r\n}\r\n.fa-archive:before {\r\n\tcontent: \"\\f187\";\r\n}\r\n.fa-bug:before {\r\n\tcontent: \"\\f188\";\r\n}\r\n.fa-pagelines:before {\r\n\tcontent: \"\\f18c\";\r\n}\r\n.fa-caret-square-left:before {\r\n\tcontent: \"\\f191\";\r\n}\r\n.fa-dot-circle:before {\r\n\tcontent: \"\\f192\";\r\n}\r\n.fa-wheelchair:before {\r\n\tcontent: \"\\f193\";\r\n}\r\n.fa-lira-sign:before {\r\n\tcontent: \"\\f195\";\r\n}\r\n.fa-space-shuttle:before {\r\n\tcontent: \"\\f197\";\r\n}\r\n.fa-envelope-square:before {\r\n\tcontent: \"\\f199\";\r\n}\r\n.fa-openid:before {\r\n\tcontent: \"\\f19b\";\r\n}\r\n.fa-university:before {\r\n\tcontent: \"\\f19c\";\r\n}\r\n.fa-graduation-cap:before {\r\n\tcontent: \"\\f19d\";\r\n}\r\n.fa-google:before {\r\n\tcontent: \"\\f1a0\";\r\n}\r\n.fa-stumbleupon:before {\r\n\tcontent: \"\\f1a4\";\r\n}\r\n.fa-drupal:before {\r\n\tcontent: \"\\f1a9\";\r\n}\r\n.fa-language:before {\r\n\tcontent: \"\\f1ab\";\r\n}\r\n.fa-fax:before {\r\n\tcontent: \"\\f1ac\";\r\n}\r\n.fa-building:before {\r\n\tcontent: \"\\f1ad\";\r\n}\r\n.fa-child:before {\r\n\tcontent: \"\\f1ae\";\r\n}\r\n.fa-paw:before {\r\n\tcontent: \"\\f1b0\";\r\n}\r\n.fa-cube:before {\r\n\tcontent: \"\\f1b2\";\r\n}\r\n.fa-cubes:before {\r\n\tcontent: \"\\f1b3\";\r\n}\r\n.fa-behance:before {\r\n\tcontent: \"\\f1b4\";\r\n}\r\n.fa-behance-square:before {\r\n\tcontent: \"\\f1b5\";\r\n}\r\n.fa-recycle:before {\r\n\tcontent: \"\\f1b8\";\r\n}\r\n.fa-car:before {\r\n\tcontent: \"\\f1b9\";\r\n}\r\n.fa-taxi:before {\r\n\tcontent: \"\\f1ba\";\r\n}\r\n.fa-tree:before {\r\n\tcontent: \"\\f1bb\";\r\n}\r\n.fa-deviantart:before {\r\n\tcontent: \"\\f1bd\";\r\n}\r\n.fa-database:before {\r\n\tcontent: \"\\f1c0\";\r\n}\r\n.fa-file-pdf:before {\r\n\tcontent: \"\\f1c1\";\r\n}\r\n.fa-file-word:before {\r\n\tcontent: \"\\f1c2\";\r\n}\r\n.fa-file-excel:before {\r\n\tcontent: \"\\f1c3\";\r\n}\r\n.fa-file-powerpoint:before {\r\n\tcontent: \"\\f1c4\";\r\n}\r\n.fa-file-image:before {\r\n\tcontent: \"\\f1c5\";\r\n}\r\n.fa-file-archive:before {\r\n\tcontent: \"\\f1c6\";\r\n}\r\n.fa-file-audio:before {\r\n\tcontent: \"\\f1c7\";\r\n}\r\n.fa-file-video:before {\r\n\tcontent: \"\\f1c8\";\r\n}\r\n.fa-file-code:before {\r\n\tcontent: \"\\f1c9\";\r\n}\r\n.fa-vine:before {\r\n\tcontent: \"\\f1ca\";\r\n}\r\n.fa-codepen:before {\r\n\tcontent: \"\\f1cb\";\r\n}\r\n.fa-life-ring:before {\r\n\tcontent: \"\\f1cd\";\r\n}\r\n.fa-circle-notch:before {\r\n\tcontent: \"\\f1ce\";\r\n}\r\n.fa-rebel:before {\r\n\tcontent: \"\\f1d0\";\r\n}\r\n.fa-qq:before {\r\n\tcontent: \"\\f1d6\";\r\n}\r\n.fa-paper-plane:before {\r\n\tcontent: \"\\f1d8\";\r\n}\r\n.fa-history:before {\r\n\tcontent: \"\\f1da\";\r\n}\r\n.fa-heading:before {\r\n\tcontent: \"\\f1dc\";\r\n}\r\n.fa-paragraph:before {\r\n\tcontent: \"\\f1dd\";\r\n}\r\n.fa-share-alt:before {\r\n\tcontent: \"\\f1e0\";\r\n}\r\n.fa-bomb:before {\r\n\tcontent: \"\\f1e2\";\r\n}\r\n.fa-futbol:before {\r\n\tcontent: \"\\f1e3\";\r\n}\r\n.fa-binoculars:before {\r\n\tcontent: \"\\f1e5\";\r\n}\r\n.fa-plug:before {\r\n\tcontent: \"\\f1e6\";\r\n}\r\n.fa-newspapers:before {\r\n\tcontent: \"\\f1ea\";\r\n}\r\n.fa-wifi:before {\r\n\tcontent: \"\\f1eb\";\r\n}\r\n.fa-calculator:before {\r\n\tcontent: \"\\f1ec\";\r\n}\r\n.fa-cc-visa:before {\r\n\tcontent: \"\\f1f0\";\r\n}\r\n.fa-cc-mastercard:before {\r\n\tcontent: \"\\f1f1\";\r\n}\r\n.fa-cc-discover:before {\r\n\tcontent: \"\\f1f2\";\r\n}\r\n.fa-cc-amex:before {\r\n\tcontent: \"\\f1f3\";\r\n}\r\n.fa-cc-paypal:before {\r\n\tcontent: \"\\f1f4\";\r\n}\r\n.fa-cc-stripe:before {\r\n\tcontent: \"\\f1f5\";\r\n}\r\n.fa-bell-slash:before {\r\n\tcontent: \"\\f1f6\";\r\n}\r\n.fa-trash:before {\r\n\tcontent: \"\\f1f8\";\r\n}\r\n.fa-copyright:before {\r\n\tcontent: \"\\f1f9\";\r\n}\r\n.fa-at:before {\r\n\tcontent: \"\\f1fa\";\r\n}\r\n.fa-eye-dropper:before {\r\n\tcontent: \"\\f1fb\";\r\n}\r\n.fa-paint-brush:before {\r\n\tcontent: \"\\f1fc\";\r\n}\r\n.fa-birthday-cake:before {\r\n\tcontent: \"\\f1fd\";\r\n}\r\n.fa-chart-area:before {\r\n\tcontent: \"\\f1fe\";\r\n}\r\n.fa-chart-pie:before {\r\n\tcontent: \"\\f200\";\r\n}\r\n.fa-chart-line:before {\r\n\tcontent: \"\\f201\";\r\n}\r\n.fa-toggle-off:before {\r\n\tcontent: \"\\f204\";\r\n}\r\n.fa-toggle-on:before {\r\n\tcontent: \"\\f205\";\r\n}\r\n.fa-bicycle:before {\r\n\tcontent: \"\\f206\";\r\n}\r\n.fa-bus:before {\r\n\tcontent: \"\\f207\";\r\n}\r\n.fa-angellist:before {\r\n\tcontent: \"\\f209\";\r\n}\r\n.fa-closed-captioning:before {\r\n\tcontent: \"\\f20a\";\r\n}\r\n.fa-buysellads:before {\r\n\tcontent: \"\\f20d\";\r\n}\r\n.fa-connectdevelop:before {\r\n\tcontent: \"\\f20e\";\r\n}\r\n.fa-dashcube:before {\r\n\tcontent: \"\\f210\";\r\n}\r\n.fa-cart-plus:before {\r\n\tcontent: \"\\f217\";\r\n}\r\n.fa-cart-arrow-down:before {\r\n\tcontent: \"\\f218\";\r\n}\r\n.fa-diamond:before {\r\n\tcontent: \"\\f219\";\r\n}\r\n.fa-ship:before {\r\n\tcontent: \"\\f21a\";\r\n}\r\n.fa-motorcycle:before {\r\n\tcontent: \"\\f21c\";\r\n}\r\n.fa-heartbeat:before {\r\n\tcontent: \"\\f21e\";\r\n}\r\n.fa-mars:before {\r\n\tcontent: \"\\f222\";\r\n}\r\n.fa-mercury:before {\r\n\tcontent: \"\\f223\";\r\n}\r\n.fa-mars-double:before {\r\n\tcontent: \"\\f227\";\r\n}\r\n.fa-mars-stroke:before {\r\n\tcontent: \"\\f229\";\r\n}\r\n.fa-mars-stroke-v:before {\r\n\tcontent: \"\\f22a\";\r\n}\r\n.fa-mars-stroke-h:before {\r\n\tcontent: \"\\f22b\";\r\n}\r\n.fa-genderless:before {\r\n\tcontent: \"\\f22d\";\r\n}\r\n.fa-whatsapp:before {\r\n\tcontent: \"\\f232\";\r\n}\r\n.fa-server:before {\r\n\tcontent: \"\\f233\";\r\n}\r\n.fa-user-plus:before {\r\n\tcontent: \"\\f234\";\r\n}\r\n.fa-user-times:before {\r\n\tcontent: \"\\f235\";\r\n}\r\n.fa-bed:before {\r\n\tcontent: \"\\f236\";\r\n}\r\n.fa-train:before {\r\n\tcontent: \"\\f238\";\r\n}\r\n.fa-battery-full:before {\r\n\tcontent: \"\\f240\";\r\n}\r\n.fa-battery-three-quarters:before {\r\n\tcontent: \"\\f241\";\r\n}\r\n.fa-battery-half:before {\r\n\tcontent: \"\\f242\";\r\n}\r\n.fa-battery-quarter:before {\r\n\tcontent: \"\\f243\";\r\n}\r\n.fa-battery-empty:before {\r\n\tcontent: \"\\f244\";\r\n}\r\n.fa-mouse-pointer:before {\r\n\tcontent: \"\\f245\";\r\n}\r\n.fa-i-cursor:before {\r\n\tcontent: \"\\f246\";\r\n}\r\n.fa-object-group:before {\r\n\tcontent: \"\\f247\";\r\n}\r\n.fa-sticky-note:before {\r\n\tcontent: \"\\f249\";\r\n}\r\n.fa-cc-jcb:before {\r\n\tcontent: \"\\f24b\";\r\n}\r\n.fa-cc-diners-club:before {\r\n\tcontent: \"\\f24c\";\r\n}\r\n.fa-clone:before {\r\n\tcontent: \"\\f24d\";\r\n}\r\n.fa-balance-scale:before {\r\n\tcontent: \"\\f24e\";\r\n}\r\n.fa-hourglass-start:before {\r\n\tcontent: \"\\f251\";\r\n}\r\n.fa-hourglass-half:before {\r\n\tcontent: \"\\f252\";\r\n}\r\n.fa-hourglass-end:before {\r\n\tcontent: \"\\f253\";\r\n}\r\n.fa-hourglass:before {\r\n\tcontent: \"\\f254\";\r\n}\r\n.fa-hand-rock:before {\r\n\tcontent: \"\\f255\";\r\n}\r\n.fa-hand-paper:before {\r\n\tcontent: \"\\f256\";\r\n}\r\n.fa-hand-scissors:before {\r\n\tcontent: \"\\f257\";\r\n}\r\n.fa-hand-lizard:before {\r\n\tcontent: \"\\f258\";\r\n}\r\n.fa-hand-spock:before {\r\n\tcontent: \"\\f259\";\r\n}\r\n.fa-hand-pointer:before {\r\n\tcontent: \"\\f25a\";\r\n}\r\n.fa-hand-peace:before {\r\n\tcontent: \"\\f25b\";\r\n}\r\n.fa-trademark:before {\r\n\tcontent: \"\\f25c\";\r\n}\r\n.fa-registered:before {\r\n\tcontent: \"\\f25d\";\r\n}\r\n.fa-creative-commons:before {\r\n\tcontent: \"\\f25e\";\r\n}\r\n.fa-gg-circle:before {\r\n\tcontent: \"\\f261\";\r\n}\r\n.fa-chrome:before {\r\n\tcontent: \"\\f268\";\r\n}\r\n.fa-tv:before {\r\n\tcontent: \"\\f26c\";\r\n}\r\n.fa-contao:before {\r\n\tcontent: \"\\f26d\";\r\n}\r\n.fa-500px:before {\r\n\tcontent: \"\\f26e\";\r\n}\r\n.fa-amazon:before {\r\n\tcontent: \"\\f270\";\r\n}\r\n.fa-calendar-plus:before {\r\n\tcontent: \"\\f271\";\r\n}\r\n.fa-calendar-minus:before {\r\n\tcontent: \"\\f272\";\r\n}\r\n.fa-calendar-times:before {\r\n\tcontent: \"\\f273\";\r\n}\r\n.fa-calendar-check:before {\r\n\tcontent: \"\\f274\";\r\n}\r\n.fa-industry:before {\r\n\tcontent: \"\\f275\";\r\n}\r\n.fa-map-pin:before {\r\n\tcontent: \"\\f276\";\r\n}\r\n.fa-map-signs:before {\r\n\tcontent: \"\\f277\";\r\n}\r\n.fa-comment-alt:before {\r\n\tcontent: \"\\f27a\";\r\n}\r\n.fa-black-tie:before {\r\n\tcontent: \"\\f27e\";\r\n}\r\n.fa-codiepie:before {\r\n\tcontent: \"\\f284\";\r\n}\r\n.fa-pause-circle:before {\r\n\tcontent: \"\\f28b\";\r\n}\r\n.fa-stop-circle:before {\r\n\tcontent: \"\\f28d\";\r\n}\r\n.fa-hashtag:before {\r\n\tcontent: \"\\f292\";\r\n}\r\n.fa-bluetooth:before {\r\n\tcontent: \"\\f293\";\r\n}\r\n.fa-bluetooth-b:before {\r\n\tcontent: \"\\f294\";\r\n}\r\n.fa-universal-access:before {\r\n\tcontent: \"\\f29a\";\r\n}\r\n.fa-blind:before {\r\n\tcontent: \"\\f29d\";\r\n}\r\n.fa-audio-description:before {\r\n\tcontent: \"\\f29e\";\r\n}\r\n.fa-braille:before {\r\n\tcontent: \"\\f2a1\";\r\n}\r\n.fa-assistive-listening-systems:before {\r\n\tcontent: \"\\f2a2\";\r\n}\r\n.fa-american-sign-language-interpreting:before {\r\n\tcontent: \"\\f2a3\";\r\n}\r\n.fa-deaf:before {\r\n\tcontent: \"\\f2a4\";\r\n}\r\n.fa-low-vision:before {\r\n\tcontent: \"\\f2a8\";\r\n}\r\n.fa-handshake:before {\r\n\tcontent: \"\\f2b5\";\r\n}\r\n.fa-envelope-open:before {\r\n\tcontent: \"\\f2b6\";\r\n}\r\n.fa-address-book:before {\r\n\tcontent: \"\\f2b9\";\r\n}\r\n.fa-address-card:before {\r\n\tcontent: \"\\f2bb\";\r\n}\r\n.fa-user-circle:before {\r\n\tcontent: \"\\f2bd\";\r\n}\r\n.fa-id-badge:before {\r\n\tcontent: \"\\f2c1\";\r\n}\r\n.fa-id-card:before {\r\n\tcontent: \"\\f2c2\";\r\n}\r\n.fa-thermometer-full:before {\r\n\tcontent: \"\\f2c7\";\r\n}\r\n.fa-shower:before {\r\n\tcontent: \"\\f2cc\";\r\n}\r\n.fa-podcast:before {\r\n\tcontent: \"\\f2ce\";\r\n}\r\n.fa-window-restore:before {\r\n\tcontent: \"\\f2d2\";\r\n}\r\n.fa-microchip:before {\r\n\tcontent: \"\\f2db\";\r\n}\r\n.fa-snowflake:before {\r\n\tcontent: \"\\f2dc\";\r\n}\r\n.fa-watch:before {\r\n\tcontent: \"\\f2e1\";\r\n}\r\n.fa-utensils-alt:before {\r\n\tcontent: \"\\f2e6\";\r\n}\r\n.fa-trophy:before {\r\n\tcontent: \"\\f2eb\";\r\n}\r\n.fa-triangle:before {\r\n\tcontent: \"\\f2ec\";\r\n}\r\n.fa-trash-alt:before {\r\n\tcontent: \"\\f2ed\";\r\n}\r\n.fa-sync-alt:before {\r\n\tcontent: \"\\f2f1\";\r\n}\r\n.fa-stopwatch:before {\r\n\tcontent: \"\\f2f2\";\r\n}\r\n.fa-spade:before {\r\n\tcontent: \"\\f2f4\";\r\n}\r\n.fa-sign-out-alt:before {\r\n\tcontent: \"\\f2f5\";\r\n}\r\n.fa-sign-in-alt:before {\r\n\tcontent: \"\\f2f6\";\r\n}\r\n.fa-uniF2F7:before {\r\n\tcontent: \"\\f2f7\";\r\n}\r\n.fa-uniF2F8:before {\r\n\tcontent: \"\\f2f8\";\r\n}\r\n.fa-rectangle-landscape:before {\r\n\tcontent: \"\\f2fa\";\r\n}\r\n.fa-rectangle-portrait:before {\r\n\tcontent: \"\\f2fb\";\r\n}\r\n.fa-poo:before {\r\n\tcontent: \"\\f2fe\";\r\n}\r\n.fa-images:before {\r\n\tcontent: \"\\f302\";\r\n}\r\n.fa-pencil-alt-light:before {\r\n\tcontent: \"\\f303\";\r\n}\r\n.fa-octagon:before {\r\n\tcontent: \"\\f306\";\r\n}\r\n.fa-minus-hexagon:before {\r\n\tcontent: \"\\f307\";\r\n}\r\n.fa-minus-octagon:before {\r\n\tcontent: \"\\f308\";\r\n}\r\n.fa-long-arrow-alt-down:before {\r\n\tcontent: \"\\f309\";\r\n}\r\n.fa-long-arrow-alt-left:before {\r\n\tcontent: \"\\f30a\";\r\n}\r\n.fa-long-arrow-alt-right:before {\r\n\tcontent: \"\\f30b\";\r\n}\r\n.fa-long-arrow-alt-up:before {\r\n\tcontent: \"\\f30c\";\r\n}\r\n.fa-lock-alt:before {\r\n\tcontent: \"\\f30d\";\r\n}\r\n.fa-jack-o-lantern:before {\r\n\tcontent: \"\\f30e\";\r\n}\r\n.fa-info-square:before {\r\n\tcontent: \"\\f30f\";\r\n}\r\n.fa-inbox-in:before {\r\n\tcontent: \"\\f310\";\r\n}\r\n.fa-inbox-out:before {\r\n\tcontent: \"\\f311\";\r\n}\r\n.fa-hexagon:before {\r\n\tcontent: \"\\f312\";\r\n}\r\n.fa-h1:before {\r\n\tcontent: \"\\f313\";\r\n}\r\n.fa-h2:before {\r\n\tcontent: \"\\f314\";\r\n}\r\n.fa-h3:before {\r\n\tcontent: \"\\f315\";\r\n}\r\n.fa-file-check:before {\r\n\tcontent: \"\\f316\";\r\n}\r\n.fa-file-times:before {\r\n\tcontent: \"\\f317\";\r\n}\r\n.fa-file-minus:before {\r\n\tcontent: \"\\f318\";\r\n}\r\n.fa-file-plus:before {\r\n\tcontent: \"\\f319\";\r\n}\r\n.fa-file-exclamation:before {\r\n\tcontent: \"\\f31a\";\r\n}\r\n.fa-file-edit:before {\r\n\tcontent: \"\\f31c\";\r\n}\r\n.fa-expand-arrows:before {\r\n\tcontent: \"\\f31d\";\r\n}\r\n.fa-expand-arrows-alt:before {\r\n\tcontent: \"\\f31e\";\r\n}\r\n.fa-expand-wide:before {\r\n\tcontent: \"\\f320\";\r\n}\r\n.fa-exclamation-square:before {\r\n\tcontent: \"\\f321\";\r\n}\r\n.fa-chevron-double-down:before {\r\n\tcontent: \"\\f322\";\r\n}\r\n.fa-chevron-double-left:before {\r\n\tcontent: \"\\f323\";\r\n}\r\n.fa-chevron-double-right:before {\r\n\tcontent: \"\\f324\";\r\n}\r\n.fa-chevron-double-up:before {\r\n\tcontent: \"\\f325\";\r\n}\r\n.fa-compress-wide:before {\r\n\tcontent: \"\\f326\";\r\n}\r\n.fa-club:before {\r\n\tcontent: \"\\f327\";\r\n}\r\n.fa-clipboard:before {\r\n\tcontent: \"\\f328\";\r\n}\r\n.fa-chevron-square-down:before {\r\n\tcontent: \"\\f329\";\r\n}\r\n.fa-chevron-square-left:before {\r\n\tcontent: \"\\f32a\";\r\n}\r\n.fa-chevron-square-right:before {\r\n\tcontent: \"\\f32b\";\r\n}\r\n.fa-chevron-square-up:before {\r\n\tcontent: \"\\f32c\";\r\n}\r\n.fa-caret-circle-down:before {\r\n\tcontent: \"\\f32d\";\r\n}\r\n.fa-caret-circle-left:before {\r\n\tcontent: \"\\f32e\";\r\n}\r\n.fa-caret-circle-right:before {\r\n\tcontent: \"\\f330\";\r\n}\r\n.fa-caret-circle-up:before {\r\n\tcontent: \"\\f331\";\r\n}\r\n.fa-camera-alt:before {\r\n\tcontent: \"\\f332\";\r\n}\r\n.fa-calendar-exclamation:before {\r\n\tcontent: \"\\f334\";\r\n}\r\n.fa-badge:before {\r\n\tcontent: \"\\f335\";\r\n}\r\n.fa-badge-check:before {\r\n\tcontent: \"\\f336\";\r\n}\r\n.fa-arrows-alt-h:before {\r\n\tcontent: \"\\f337\";\r\n}\r\n.fa-arrows-alt-v:before {\r\n\tcontent: \"\\f338\";\r\n}\r\n.fa-arrow-square-down:before {\r\n\tcontent: \"\\f339\";\r\n}\r\n.fa-arrow-square-left:before {\r\n\tcontent: \"\\f33a\";\r\n}\r\n.fa-arrow-square-right:before {\r\n\tcontent: \"\\f33b\";\r\n}\r\n.fa-arrow-square-up:before {\r\n\tcontent: \"\\f33c\";\r\n}\r\n.fa-arrow-to-bottom:before {\r\n\tcontent: \"\\f33d\";\r\n}\r\n.fa-arrow-to-left:before {\r\n\tcontent: \"\\f33e\";\r\n}\r\n.fa-arrow-to-right:before {\r\n\tcontent: \"\\f340\";\r\n}\r\n.fa-arrow-to-top:before {\r\n\tcontent: \"\\f341\";\r\n}\r\n.fa-arrow-from-bottom:before {\r\n\tcontent: \"\\f342\";\r\n}\r\n.fa-arrow-from-left:before {\r\n\tcontent: \"\\f343\";\r\n}\r\n.fa-arrow-from-right:before {\r\n\tcontent: \"\\f344\";\r\n}\r\n.fa-arrow-from-top:before {\r\n\tcontent: \"\\f345\";\r\n}\r\n.fa-arrow-alt-from-bottom:before {\r\n\tcontent: \"\\f346\";\r\n}\r\n.fa-arrow-alt-from-left:before {\r\n\tcontent: \"\\f347\";\r\n}\r\n.fa-arrow-alt-from-right:before {\r\n\tcontent: \"\\f348\";\r\n}\r\n.fa-arrow-alt-from-top:before {\r\n\tcontent: \"\\f349\";\r\n}\r\n.fa-arrow-alt-to-bottom:before {\r\n\tcontent: \"\\f34a\";\r\n}\r\n.fa-arrow-alt-to-left:before {\r\n\tcontent: \"\\f34b\";\r\n}\r\n.fa-arrow-alt-to-right:before {\r\n\tcontent: \"\\f34c\";\r\n}\r\n.fa-arrow-alt-to-top:before {\r\n\tcontent: \"\\f34d\";\r\n}\r\n.fa-alarm-clock:before {\r\n\tcontent: \"\\f34e\";\r\n}\r\n.fa-arrow-alt-square-down:before {\r\n\tcontent: \"\\f350\";\r\n}\r\n.fa-arrow-alt-square-left:before {\r\n\tcontent: \"\\f351\";\r\n}\r\n.fa-arrow-alt-square-right:before {\r\n\tcontent: \"\\f352\";\r\n}\r\n.fa-arrow-alt-square-up-:before {\r\n\tcontent: \"\\f353\";\r\n}\r\n.fa-arrow-alt-down:before {\r\n\tcontent: \"\\f354\";\r\n}\r\n.fa-arrow-alt-left:before {\r\n\tcontent: \"\\f355\";\r\n}\r\n.fa-arrow-alt-right:before {\r\n\tcontent: \"\\f356\";\r\n}\r\n.fa-arrow-alt-up:before {\r\n\tcontent: \"\\f357\";\r\n}\r\n.fa-arrow-alt-circle-down:before {\r\n\tcontent: \"\\f358\";\r\n}\r\n.fa-arrow-alt-circle-left:before {\r\n\tcontent: \"\\f359\";\r\n}\r\n.fa-arrow-alt-circle-right:before {\r\n\tcontent: \"\\f35a\";\r\n}\r\n.fa-arrow-alt-circle-up:before {\r\n\tcontent: \"\\f35b\";\r\n}\r\n.fa-external-link-alt:before {\r\n\tcontent: \"\\f35d\";\r\n}\r\n.fa-external-link-square-alt:before {\r\n\tcontent: \"\\f360\";\r\n}\r\n.fa-exchange-alt:before {\r\n\tcontent: \"\\f362\";\r\n}\r\n.fa-repeat:before {\r\n\tcontent: \"\\f363\";\r\n}\r\n.fa-accessible-icon:before {\r\n\tcontent: \"\\f368\";\r\n}\r\n.fa-accusoft:before {\r\n\tcontent: \"\\f369\";\r\n}\r\n.fa-adversalbrands:before {\r\n\tcontent: \"\\f36a\";\r\n}\r\n.fa-affiliatetheme:before {\r\n\tcontent: \"\\f36b\";\r\n}\r\n.fa-algolia:before {\r\n\tcontent: \"\\f36c\";\r\n}\r\n.fa-amilia:before {\r\n\tcontent: \"\\f36d\";\r\n}\r\n.fa-app-store:before {\r\n\tcontent: \"\\f36f\";\r\n}\r\n.fa-app-store-ios:before {\r\n\tcontent: \"\\f370\";\r\n}\r\n.fa-asymmetrik:before {\r\n\tcontent: \"\\f372\";\r\n}\r\n.fa-avianex:before {\r\n\tcontent: \"\\f374\";\r\n}\r\n.fa-aws:before {\r\n\tcontent: \"\\f375\";\r\n}\r\n.fa-battery-bolt:before {\r\n\tcontent: \"\\f376\";\r\n}\r\n.fa-battery-slash:before {\r\n\tcontent: \"\\f377\";\r\n}\r\n.fa-bitcoin:before {\r\n\tcontent: \"\\f379\";\r\n}\r\n.fa-bity:before {\r\n\tcontent: \"\\f37a\";\r\n}\r\n.fa-blackberry:before {\r\n\tcontent: \"\\f37b\";\r\n}\r\n.fa-blogger:before {\r\n\tcontent: \"\\f37c\";\r\n}\r\n.fa-blogger-b:before {\r\n\tcontent: \"\\f37d\";\r\n}\r\n.fa-browser:before {\r\n\tcontent: \"\\f37e\";\r\n}\r\n.fa-buromobelexperte:before {\r\n\tcontent: \"\\f37f\";\r\n}\r\n.fa-centercode:before {\r\n\tcontent: \"\\f380\";\r\n}\r\n.fa-cloud-download-alt:before {\r\n\tcontent: \"\\f381\";\r\n}\r\n.fa-cloud-upload-alt:before {\r\n\tcontent: \"\\f382\";\r\n}\r\n.fa-cloudscale:before {\r\n\tcontent: \"\\f383\";\r\n}\r\n.fa-cloudsmith:before {\r\n\tcontent: \"\\f384\";\r\n}\r\n.fa-cloudversify:before {\r\n\tcontent: \"\\f385\";\r\n}\r\n.fa-code-commit:before {\r\n\tcontent: \"\\f386\";\r\n}\r\n.fa-code-merge:before {\r\n\tcontent: \"\\f387\";\r\n}\r\n.fa-cpanel:before {\r\n\tcontent: \"\\f388\";\r\n}\r\n.fa-credit-card-blank:before {\r\n\tcontent: \"\\f389\";\r\n}\r\n.fa-credit-card-front:before {\r\n\tcontent: \"\\f38a\";\r\n}\r\n.fa-css3-alt:before {\r\n\tcontent: \"\\f38b\";\r\n}\r\n.fa-cuttlefish:before {\r\n\tcontent: \"\\f38c\";\r\n}\r\n.fa-d-and-d:before {\r\n\tcontent: \"\\f38d\";\r\n}\r\n.fa-deskpro:before {\r\n\tcontent: \"\\f38f\";\r\n}\r\n.fa-desktop-alt:before {\r\n\tcontent: \"\\f390\";\r\n}\r\n.fa-ellipsis-v-alt:before {\r\n\tcontent: \"\\f39c\";\r\n}\r\n.fa-film-alt:before {\r\n\tcontent: \"\\f3a0\";\r\n}\r\n.fa-gem:before {\r\n\tcontent: \"\\f3a5\";\r\n}\r\n.fa-industry-alt:before {\r\n\tcontent: \"\\f3b3\";\r\n}\r\n.fa-level-down-alt:before {\r\n\tcontent: \"\\f3be\";\r\n}\r\n.fa-level-up-alt:before {\r\n\tcontent: \"\\f3bf\";\r\n}\r\n.fa-lock-open:before {\r\n\tcontent: \"\\f3c1\";\r\n}\r\n.fa-lock-open-alt:before {\r\n\tcontent: \"\\f3c2\";\r\n}\r\n.fa-map-marker-alt:before {\r\n\tcontent: \"\\f3c5\";\r\n}\r\n.fa-microphone-alt:before {\r\n\tcontent: \"\\f3c9\";\r\n}\r\n.fa-mobile-alt:before {\r\n\tcontent: \"\\f3cd\";\r\n}\r\n.fa-mobile-android:before {\r\n\tcontent: \"\\f3ce\";\r\n}\r\n.fa-mobile-android-alt:before {\r\n\tcontent: \"\\f3cf\";\r\n}\r\n.fa-money-bill-alt:before {\r\n\tcontent: \"\\f3d1\";\r\n}\r\n.fa-portrait:before {\r\n\tcontent: \"\\f3e0\";\r\n}\r\n.fa-reply:before {\r\n\tcontent: \"\\f3e5\";\r\n}\r\n.fa-sliders-v:before {\r\n\tcontent: \"\\f3f1\";\r\n}\r\n.fa-sliders-v-square:before {\r\n\tcontent: \"\\f3f2\";\r\n}\r\n.fa-user-alt:before {\r\n\tcontent: \"\\f406\";\r\n}\r\n.fa-window-alt:before {\r\n\tcontent: \"\\f40f\";\r\n}\r\n.fa-apple-pay:before {\r\n\tcontent: \"\\f415\";\r\n}\r\n.fa-cc-apple-pay:before {\r\n\tcontent: \"\\f416\";\r\n}\r\n.fa-autoprefixer:before {\r\n\tcontent: \"\\f41c\";\r\n}\r\n.fa-angular:before {\r\n\tcontent: \"\\f420\";\r\n}\r\n.fa-compress-alt:before {\r\n\tcontent: \"\\f422\";\r\n}\r\n.fa-expand-alt:before {\r\n\tcontent: \"\\f424\";\r\n}\r\n.fa-amazon-pay:before {\r\n\tcontent: \"\\f42c\";\r\n}\r\n.fa-cc-amazon-pay:before {\r\n\tcontent: \"\\f42d\";\r\n}\r\n.fa-baseball:before {\r\n\tcontent: \"\\f432\";\r\n}\r\n.fa-baseball-ball:before {\r\n\tcontent: \"\\f433\";\r\n}\r\n.fa-basketball-ball:before {\r\n\tcontent: \"\\f434\";\r\n}\r\n.fa-basketball-hoop:before {\r\n\tcontent: \"\\f435\";\r\n}\r\n.fa-bowling-ball:before {\r\n\tcontent: \"\\f436\";\r\n}\r\n.fa-bowling-pins:before {\r\n\tcontent: \"\\f437\";\r\n}\r\n.fa-boxing-glove:before {\r\n\tcontent: \"\\f438\";\r\n}\r\n.fa-chess:before {\r\n\tcontent: \"\\f439\";\r\n}\r\n.fa-chess-bishop:before {\r\n\tcontent: \"\\f43a\";\r\n}\r\n.fa-chess-bishop-alt:before {\r\n\tcontent: \"\\f43b\";\r\n}\r\n.fa-chess-board:before {\r\n\tcontent: \"\\f43c\";\r\n}\r\n.fa-chess-clock:before {\r\n\tcontent: \"\\f43d\";\r\n}\r\n.fa-chess-clock-alt:before {\r\n\tcontent: \"\\f43e\";\r\n}\r\n.fa-chess-king:before {\r\n\tcontent: \"\\f43f\";\r\n}\r\n.fa-chess-king-alt:before {\r\n\tcontent: \"\\f440\";\r\n}\r\n.fa-chess-knight:before {\r\n\tcontent: \"\\f441\";\r\n}\r\n.fa-chess-knight-alt:before {\r\n\tcontent: \"\\f442\";\r\n}\r\n.fa-chess-pawn:before {\r\n\tcontent: \"\\f443\";\r\n}\r\n.fa-chess-pawn-alt:before {\r\n\tcontent: \"\\f444\";\r\n}\r\n.fa-chess-queen:before {\r\n\tcontent: \"\\f445\";\r\n}\r\n.fa-chess-queen-alt:before {\r\n\tcontent: \"\\f446\";\r\n}\r\n.fa-chess-rook:before {\r\n\tcontent: \"\\f447\";\r\n}\r\n.fa-chess-rook-alt:before {\r\n\tcontent: \"\\f448\";\r\n}\r\n.fa-cricket:before {\r\n\tcontent: \"\\f449\";\r\n}\r\n.fa-curling:before {\r\n\tcontent: \"\\f44a\";\r\n}\r\n.fa-dumbbell:before {\r\n\tcontent: \"\\f44b\";\r\n}\r\n.fa-field-hockey:before {\r\n\tcontent: \"\\f44c\";\r\n}\r\n.fa-football-ball:before {\r\n\tcontent: \"\\f44e\";\r\n}\r\n.fa-football-helmet:before {\r\n\tcontent: \"\\f44f\";\r\n}\r\n.fa-golf-ball:before {\r\n\tcontent: \"\\f450\";\r\n}\r\n.fa-golf-club:before {\r\n\tcontent: \"\\f451\";\r\n}\r\n.fa-hockey-puck:before {\r\n\tcontent: \"\\f453\";\r\n}\r\n.fa-hockey-sticks:before {\r\n\tcontent: \"\\f454\";\r\n}\r\n.fa-luchador:before {\r\n\tcontent: \"\\f455\";\r\n}\r\n.fa-racquet:before {\r\n\tcontent: \"\\f45a\";\r\n}\r\n.fa-shuttlecock:before {\r\n\tcontent: \"\\f45b\";\r\n}\r\n.fa-square-full:before {\r\n\tcontent: \"\\f45c\";\r\n}\r\n.fa-table-tennis:before {\r\n\tcontent: \"\\f45d\";\r\n}\r\n.fa-tennis-ball:before {\r\n\tcontent: \"\\f45e\";\r\n}\r\n.fa-whistle:before {\r\n\tcontent: \"\\f460\";\r\n}\r\n.fa-allergies:before {\r\n\tcontent: \"\\f461\";\r\n}\r\n.fa-band-aid:before {\r\n\tcontent: \"\\f462\";\r\n}\r\n.fa-barcode-alt:before {\r\n\tcontent: \"\\f463\";\r\n}\r\n.fa-barcode-read:before {\r\n\tcontent: \"\\f464\";\r\n}\r\n.fa-barcode-scan:before {\r\n\tcontent: \"\\f465\";\r\n}\r\n.fa-box:before {\r\n\tcontent: \"\\f466\";\r\n}\r\n.fa-box-check:before {\r\n\tcontent: \"\\f467\";\r\n}\r\n.fa-boxes:before {\r\n\tcontent: \"\\f468\";\r\n}\r\n.fa-briefcase-medical:before {\r\n\tcontent: \"\\f469\";\r\n}\r\n.fa-burn:before {\r\n\tcontent: \"\\f46a\";\r\n}\r\n.fa-capsules:before {\r\n\tcontent: \"\\f46b\";\r\n}\r\n.fa-clipboard-check:before {\r\n\tcontent: \"\\f46c\";\r\n}\r\n.fa-clipboard-list:before {\r\n\tcontent: \"\\f46d\";\r\n}\r\n.fa-conveyor-belt:before {\r\n\tcontent: \"\\f46e\";\r\n}\r\n.fa-conveyor-belt-alt:before {\r\n\tcontent: \"\\f46f\";\r\n}\r\n.fa-diagnoses:before {\r\n\tcontent: \"\\f470\";\r\n}\r\n.fa-dna:before {\r\n\tcontent: \"\\f471\";\r\n}\r\n.fa-dolly:before {\r\n\tcontent: \"\\f472\";\r\n}\r\n.fa-dolly-empty:before {\r\n\tcontent: \"\\f473\";\r\n}\r\n.fa-dolly-flatbed:before {\r\n\tcontent: \"\\f474\";\r\n}\r\n.fa-dolly-flatbed-alt:before {\r\n\tcontent: \"\\f475\";\r\n}\r\n.fa-dolly-flatbed-empty:before {\r\n\tcontent: \"\\f476\";\r\n}\r\n.fa-file-medical:before {\r\n\tcontent: \"\\f477\";\r\n}\r\n.fa-file-medical-alt:before {\r\n\tcontent: \"\\f478\";\r\n}\r\n.fa-first-aid:before {\r\n\tcontent: \"\\f479\";\r\n}\r\n.fa-forklift:before {\r\n\tcontent: \"\\f47a\";\r\n}\r\n.fa-hand-holding-box:before {\r\n\tcontent: \"\\f47b\";\r\n}\r\n.fa-hand-receiving:before {\r\n\tcontent: \"\\f47c\";\r\n}\r\n.fa-hospital-alt:before {\r\n\tcontent: \"\\f47d\";\r\n}\r\n.fa-hospital-symbol:before {\r\n\tcontent: \"\\f47e\";\r\n}\r\n.fa-id-card-alt:before {\r\n\tcontent: \"\\f47f\";\r\n}\r\n.fa-inventory:before {\r\n\tcontent: \"\\f480\";\r\n}\r\n.fa-pills:before {\r\n\tcontent: \"\\f484\";\r\n}\r\n.fa-smoking:before {\r\n\tcontent: \"\\f48d\";\r\n}\r\n.fa-syringe:before {\r\n\tcontent: \"\\f48e\";\r\n}\r\n.fa-tablets:before {\r\n\tcontent: \"\\f490\";\r\n}\r\n.fa-warehouse:before {\r\n\tcontent: \"\\f494\";\r\n}\r\n.fa-weight:before {\r\n\tcontent: \"\\f496\";\r\n}\r\n.fa-x-ray:before {\r\n\tcontent: \"\\f497\";\r\n}\r\n.fa-blanket:before {\r\n\tcontent: \"\\f498\";\r\n}\r\n.fa-book-heart:before {\r\n\tcontent: \"\\f499\";\r\n}\r\n.fa-box-alt:before {\r\n\tcontent: \"\\f49a\";\r\n}\r\n.fa-box-fragile:before {\r\n\tcontent: \"\\f49b\";\r\n}\r\n.fa-box-full:before {\r\n\tcontent: \"\\f49c\";\r\n}\r\n.fa-box-heart:before {\r\n\tcontent: \"\\f49d\";\r\n}\r\n.fa-box-open:before {\r\n\tcontent: \"\\f49e\";\r\n}\r\n.fa-box-up:before {\r\n\tcontent: \"\\f49f\";\r\n}\r\n.fa-box-usd:before {\r\n\tcontent: \"\\f4a0\";\r\n}\r\n.fa-boxes-alt:before {\r\n\tcontent: \"\\f4a1\";\r\n}\r\n.fa-comment-alt-check:before {\r\n\tcontent: \"\\f4a2\";\r\n}\r\n.fa-comment-alt-dots:before {\r\n\tcontent: \"\\f4a3\";\r\n}\r\n.fa-comment-alt-edit:before {\r\n\tcontent: \"\\f4a4\";\r\n}\r\n.fa-comment-alt-exclamation:before {\r\n\tcontent: \"\\f4a5\";\r\n}\r\n.fa-comment-alt-lines:before {\r\n\tcontent: \"\\f4a6\";\r\n}\r\n.fa-comment-alt-minus:before {\r\n\tcontent: \"\\f4a7\";\r\n}\r\n.fa-comment-alt-plus:before {\r\n\tcontent: \"\\f4a8\";\r\n}\r\n.fa-comment-alt-smile:before {\r\n\tcontent: \"\\f4aa\";\r\n}\r\n.fa-comment-alt-times:before {\r\n\tcontent: \"\\f4ab\";\r\n}\r\n.fa-comment-check:before {\r\n\tcontent: \"\\f4ac\";\r\n}\r\n.fa-comment-dots:before {\r\n\tcontent: \"\\f4ad\";\r\n}\r\n.fa-comment-edit:before {\r\n\tcontent: \"\\f4ae\";\r\n}\r\n.fa-comment-exclamation:before {\r\n\tcontent: \"\\f4af\";\r\n}\r\n.fa-comment-lines:before {\r\n\tcontent: \"\\f4b0\";\r\n}\r\n.fa-comment-minus:before {\r\n\tcontent: \"\\f4b1\";\r\n}\r\n.fa-comment-plus:before {\r\n\tcontent: \"\\f4b2\";\r\n}\r\n.fa-comment-slash:before {\r\n\tcontent: \"\\f4b3\";\r\n}\r\n.fa-comment-smile:before {\r\n\tcontent: \"\\f4b4\";\r\n}\r\n.fa-comment-times:before {\r\n\tcontent: \"\\f4b5\";\r\n}\r\n.fa-comments-alt:before {\r\n\tcontent: \"\\f4b6\";\r\n}\r\n.fa-container-storage:before {\r\n\tcontent: \"\\f4b7\";\r\n}\r\n.fa-couch:before {\r\n\tcontent: \"\\f4b8\";\r\n}\r\n.fa-donate:before {\r\n\tcontent: \"\\f4b9\";\r\n}\r\n.fa-dove:before {\r\n\tcontent: \"\\f4ba\";\r\n}\r\n.fa-fragile:before {\r\n\tcontent: \"\\f4bb\";\r\n}\r\n.fa-hand-heart:before {\r\n\tcontent: \"\\f4bc\";\r\n}\r\n.fa-hand-holding:before {\r\n\tcontent: \"\\f4bd\";\r\n}\r\n.fa-hand-holding-heart:before {\r\n\tcontent: \"\\f4be\";\r\n}\r\n.fa-hand-holding-seedling:before {\r\n\tcontent: \"\\f4bf\";\r\n}\r\n.fa-hand-holding-usd:before {\r\n\tcontent: \"\\f4c0\";\r\n}\r\n.fa-hand-holding-water:before {\r\n\tcontent: \"\\f4c1\";\r\n}\r\n.fa-hands:before {\r\n\tcontent: \"\\f4c2\";\r\n}\r\n.fa-hands-heart:before {\r\n\tcontent: \"\\f4c3\";\r\n}\r\n.fa-hands-helping:before {\r\n\tcontent: \"\\f4c4\";\r\n}\r\n.fa-hands-usd:before {\r\n\tcontent: \"\\f4c5\";\r\n}\r\n.fa-handshake-alt:before {\r\n\tcontent: \"\\f4c6\";\r\n}\r\n.fa-heart-circle:before {\r\n\tcontent: \"\\f4c7\";\r\n}\r\n.fa-heart-square:before {\r\n\tcontent: \"\\f4c8\";\r\n}\r\n.fa-home-heart:before {\r\n\tcontent: \"\\f4c9\";\r\n}\r\n.fa-lamp:before {\r\n\tcontent: \"\\f4ca\";\r\n}\r\n.fa-leaf-heart:before {\r\n\tcontent: \"\\f4cb\";\r\n}\r\n.fa-parachute-box:before {\r\n\tcontent: \"\\f4cd\";\r\n}\r\n.fa-piggy-bank:before {\r\n\tcontent: \"\\f4d3\";\r\n}\r\n.fa-ribbon:before {\r\n\tcontent: \"\\f4d6\";\r\n}\r\n.fa-route:before {\r\n\tcontent: \"\\f4d7\";\r\n}\r\n.fa-seedling:before {\r\n\tcontent: \"\\f4d8\";\r\n}\r\n.fa-creative-commons-by:before {\r\n\tcontent: \"\\f4e7\";\r\n}\r\n.fa-creative-commons-nc:before {\r\n\tcontent: \"\\f4e8\";\r\n}\r\n.fa-creative-commons-nc-jp:before {\r\n\tcontent: \"\\f4ea\";\r\n}\r\n.fa-creative-commons-nd:before {\r\n\tcontent: \"\\f4eb\";\r\n}\r\n.fa-creative-commons-pd:before {\r\n\tcontent: \"\\f4ec\";\r\n}\r\n.fa-creative-commons-pd-alt:before {\r\n\tcontent: \"\\f4ed\";\r\n}\r\n.fa-creative-commons-remix:before {\r\n\tcontent: \"\\f4ee\";\r\n}\r\n.fa-creative-commons-sampling:before {\r\n\tcontent: \"\\f4f0\";\r\n}\r\n.fa-creative-commons-sampling-plus:before {\r\n\tcontent: \"\\f4f1\";\r\n}\r\n.fa-creative-commons-share:before {\r\n\tcontent: \"\\f4f2\";\r\n}\r\n.fa-user-cog:before {\r\n\tcontent: \"\\f4fe\";\r\n}\r\n.fa-user-friends:before {\r\n\tcontent: \"\\f500\";\r\n}\r\n.fa-user-slash:before {\r\n\tcontent: \"\\f506\";\r\n}\r\n.fa-user-tie:before {\r\n\tcontent: \"\\f508\";\r\n}\r\n.fa-balance-scale-left:before {\r\n\tcontent: \"\\f515\";\r\n}\r\n.fa-balance-scale-right:before {\r\n\tcontent: \"\\f516\";\r\n}\r\n.fa-blender:before {\r\n\tcontent: \"\\f517\";\r\n}\r\n.fa-book-open:before {\r\n\tcontent: \"\\f518\";\r\n}\r\n.fa-broadcast-tower:before {\r\n\tcontent: \"\\f519\";\r\n}\r\n.fa-broom:before {\r\n\tcontent: \"\\f51a\";\r\n}\r\n.fa-chalkboard:before {\r\n\tcontent: \"\\f51b\";\r\n}\r\n.fa-chalkboard-teacher:before {\r\n\tcontent: \"\\f51c\";\r\n}\r\n.fa-church:before {\r\n\tcontent: \"\\f51d\";\r\n}\r\n.fa-coins:before {\r\n\tcontent: \"\\f51e\";\r\n}\r\n.fa-compact-disc:before {\r\n\tcontent: \"\\f51f\";\r\n}\r\n.fa-crow:before {\r\n\tcontent: \"\\f520\";\r\n}\r\n.fa-crown:before {\r\n\tcontent: \"\\f521\";\r\n}\r\n.fa-dice:before {\r\n\tcontent: \"\\f522\";\r\n}\r\n.fa-dice-five:before {\r\n\tcontent: \"\\f523\";\r\n}\r\n.fa-dice-four:before {\r\n\tcontent: \"\\f524\";\r\n}\r\n.fa-dice-one:before {\r\n\tcontent: \"\\f525\";\r\n}\r\n.fa-dice-six:before {\r\n\tcontent: \"\\f526\";\r\n}\r\n.fa-dice-three:before {\r\n\tcontent: \"\\f527\";\r\n}\r\n.fa-dice-two:before {\r\n\tcontent: \"\\f528\";\r\n}\r\n.fa-divide:before {\r\n\tcontent: \"\\f529\";\r\n}\r\n.fa-door-closed:before {\r\n\tcontent: \"\\f52a\";\r\n}\r\n.fa-door-open:before {\r\n\tcontent: \"\\f52b\";\r\n}\r\n.fa-equals:before {\r\n\tcontent: \"\\f52c\";\r\n}\r\n.fa-feather:before {\r\n\tcontent: \"\\f52d\";\r\n}\r\n.fa-frog:before {\r\n\tcontent: \"\\f52e\";\r\n}\r\n.fa-gas-pump:before {\r\n\tcontent: \"\\f52f\";\r\n}\r\n.fa-glasses:before {\r\n\tcontent: \"\\f530\";\r\n}\r\n.fa-greater-than:before {\r\n\tcontent: \"\\f531\";\r\n}\r\n.fa-greater-than-equal:before {\r\n\tcontent: \"\\f532\";\r\n}\r\n.fa-helicopter:before {\r\n\tcontent: \"\\f533\";\r\n}\r\n.fa-infinity:before {\r\n\tcontent: \"\\f534\";\r\n}\r\n.fa-kiwi-bird:before {\r\n\tcontent: \"\\f535\";\r\n}\r\n.fa-less-than:before {\r\n\tcontent: \"\\f536\";\r\n}\r\n.fa-less-than-equal:before {\r\n\tcontent: \"\\f537\";\r\n}\r\n.fa-memory:before {\r\n\tcontent: \"\\f538\";\r\n}\r\n.fa-microphone-alt-slash:before {\r\n\tcontent: \"\\f539\";\r\n}\r\n.fa-money-bill-wave:before {\r\n\tcontent: \"\\f53a\";\r\n}\r\n.fa-money-bill-wave-alt:before {\r\n\tcontent: \"\\f53b\";\r\n}\r\n.fa-money-check:before {\r\n\tcontent: \"\\f53c\";\r\n}\r\n.fa-money-check-alt:before {\r\n\tcontent: \"\\f53d\";\r\n}\r\n.fa-not-equal:before {\r\n\tcontent: \"\\f53e\";\r\n}\r\n.fa-palette:before {\r\n\tcontent: \"\\f53f\";\r\n}\r\n.fa-percentage:before {\r\n\tcontent: \"\\f541\";\r\n}\r\n.fa-project-diagram:before {\r\n\tcontent: \"\\f542\";\r\n}\r\n.fa-receipts:before {\r\n\tcontent: \"\\f543\";\r\n}\r\n.fa-robot:before {\r\n\tcontent: \"\\f544\";\r\n}\r\n.fa-ruler:before {\r\n\tcontent: \"\\f545\";\r\n}\r\n.fa-school:before {\r\n\tcontent: \"\\f549\";\r\n}\r\n.fa-screwdriver:before {\r\n\tcontent: \"\\f54a\";\r\n}\r\n.fa-shoe-prints:before {\r\n\tcontent: \"\\f54b\";\r\n}\r\n.fa-skull:before {\r\n\tcontent: \"\\f54c\";\r\n}\r\n.fa-store:before {\r\n\tcontent: \"\\f54e\";\r\n}\r\n.fa-toolbox:before {\r\n\tcontent: \"\\f552\";\r\n}\r\n.fa-tshirt:before {\r\n\tcontent: \"\\f553\";\r\n}\r\n.fa-wallet:before {\r\n\tcontent: \"\\f555\";\r\n}\r\n.fa-angry:before {\r\n\tcontent: \"\\f556\";\r\n}\r\n.fa-archway:before {\r\n\tcontent: \"\\f557\";\r\n}\r\n.fa-atlas:before {\r\n\tcontent: \"\\f558\";\r\n}\r\n.fa-award:before {\r\n\tcontent: \"\\f559\";\r\n}\r\n.fa-backspace:before {\r\n\tcontent: \"\\f55a\";\r\n}\r\n.fa-bezier-curve:before {\r\n\tcontent: \"\\f55b\";\r\n}\r\n.fa-bong:before {\r\n\tcontent: \"\\f55c\";\r\n}\r\n.fa-brush:before {\r\n\tcontent: \"\\f55d\";\r\n}\r\n.fa-cannabis:before {\r\n\tcontent: \"\\f55e\";\r\n}\r\n.fa-check-double:before {\r\n\tcontent: \"\\f560\";\r\n}\r\n.fa-cocktail:before {\r\n\tcontent: \"\\f561\";\r\n}\r\n.fa-concierge-bell:before {\r\n\tcontent: \"\\f562\";\r\n}\r\n.fa-cookie:before {\r\n\tcontent: \"\\f563\";\r\n}\r\n.fa-cookie-bite:before {\r\n\tcontent: \"\\f564\";\r\n}\r\n.fa-crop-alt:before {\r\n\tcontent: \"\\f565\";\r\n}\r\n.fa-digital-tachograph:before {\r\n\tcontent: \"\\f566\";\r\n}\r\n.fa-dizzy:before {\r\n\tcontent: \"\\f567\";\r\n}\r\n.fa-drafting-compass:before {\r\n\tcontent: \"\\f568\";\r\n}\r\n.fa-drum:before {\r\n\tcontent: \"\\f569\";\r\n}\r\n.fa-drum-steelpan:before {\r\n\tcontent: \"\\f56a\";\r\n}\r\n.fa-feather-alt:before {\r\n\tcontent: \"\\f56b\";\r\n}\r\n.fa-file-contract:before {\r\n\tcontent: \"\\f56c\";\r\n}\r\n.fa-file-download:before {\r\n\tcontent: \"\\f56d\";\r\n}\r\n.fa-file-export:before {\r\n\tcontent: \"\\f56e\";\r\n}\r\n.fa-file-import:before {\r\n\tcontent: \"\\f56f\";\r\n}\r\n.fa-file-invoice:before {\r\n\tcontent: \"\\f570\";\r\n}\r\n.fa-file-invoice-dollar:before {\r\n\tcontent: \"\\f571\";\r\n}\r\n.fa-file-prescription:before {\r\n\tcontent: \"\\f572\";\r\n}\r\n.fa-file-certificate:before {\r\n\tcontent: \"\\f573\";\r\n}\r\n.fa-file-upload:before {\r\n\tcontent: \"\\f574\";\r\n}\r\n.fa-fill:before {\r\n\tcontent: \"\\f575\";\r\n}\r\n.fa-fill-drip:before {\r\n\tcontent: \"\\f576\";\r\n}\r\n.fa-fingerprint:before {\r\n\tcontent: \"\\f577\";\r\n}\r\n.fa-fish:before {\r\n\tcontent: \"\\f578\";\r\n}\r\n.fa-flushed:before {\r\n\tcontent: \"\\f579\";\r\n}\r\n.fa-frown-open:before {\r\n\tcontent: \"\\f57a\";\r\n}\r\n.fa-glass-martini-alt:before {\r\n\tcontent: \"\\f57b\";\r\n}\r\n.fa-globe-africa:before {\r\n\tcontent: \"\\f57c\";\r\n}\r\n.fa-globe-americas:before {\r\n\tcontent: \"\\f57d\";\r\n}\r\n.fa-globe-asia:before {\r\n\tcontent: \"\\f57e\";\r\n}\r\n.fa-grimace:before {\r\n\tcontent: \"\\f57f\";\r\n}\r\n.fa-grin:before {\r\n\tcontent: \"\\f580\";\r\n}\r\n.fa-grin-alt:before {\r\n\tcontent: \"\\f581\";\r\n}\r\n.fa-grin-beam:before {\r\n\tcontent: \"\\f582\";\r\n}\r\n.fa-grin-beam-sweat:before {\r\n\tcontent: \"\\f583\";\r\n}\r\n.fa-grin-hearts:before {\r\n\tcontent: \"\\f584\";\r\n}\r\n.fa-grin-squint:before {\r\n\tcontent: \"\\f585\";\r\n}\r\n.fa-grin-squint-tears:before {\r\n\tcontent: \"\\f586\";\r\n}\r\n.fa-grin-stars:before {\r\n\tcontent: \"\\f587\";\r\n}\r\n.fa-grin-tears:before {\r\n\tcontent: \"\\f588\";\r\n}\r\n.fa-grin-tongue:before {\r\n\tcontent: \"\\f589\";\r\n}\r\n.fa-grin-tongue-squint:before {\r\n\tcontent: \"\\f58a\";\r\n}\r\n.fa-grin-tongue-wink:before {\r\n\tcontent: \"\\f58b\";\r\n}\r\n.fa-grin-wink:before {\r\n\tcontent: \"\\f58c\";\r\n}\r\n.fa-grip-horizontal:before {\r\n\tcontent: \"\\f58d\";\r\n}\r\n.fa-grip-vertical:before {\r\n\tcontent: \"\\f58e\";\r\n}\r\n.fa-headphones-alt:before {\r\n\tcontent: \"\\f58f\";\r\n}\r\n.fa-highlighter:before {\r\n\tcontent: \"\\f591\";\r\n}\r\n.fa-hot-tub:before {\r\n\tcontent: \"\\f593\";\r\n}\r\n.fa-hotel:before {\r\n\tcontent: \"\\f594\";\r\n}\r\n.fa-joint:before {\r\n\tcontent: \"\\f595\";\r\n}\r\n.fa-kiss:before {\r\n\tcontent: \"\\f596\";\r\n}\r\n.fa-kiss-beam:before {\r\n\tcontent: \"\\f597\";\r\n}\r\n.fa-kiss-wink-heart:before {\r\n\tcontent: \"\\f598\";\r\n}\r\n.fa-laugh:before {\r\n\tcontent: \"\\f599\";\r\n}\r\n.fa-laugh-beam:before {\r\n\tcontent: \"\\f59a\";\r\n}\r\n.fa-laugh-squint:before {\r\n\tcontent: \"\\f59b\";\r\n}\r\n.fa-laugh-wink:before {\r\n\tcontent: \"\\f59c\";\r\n}\r\n.fa-luggage-cart:before {\r\n\tcontent: \"\\f59d\";\r\n}\r\n.fa-map-marked:before {\r\n\tcontent: \"\\f59f\";\r\n}\r\n.fa-map-marked-alt:before {\r\n\tcontent: \"\\f5a0\";\r\n}\r\n.fa-marker:before {\r\n\tcontent: \"\\f5a1\";\r\n}\r\n.fa-medal:before {\r\n\tcontent: \"\\f5a2\";\r\n}\r\n.fa-meh-blank:before {\r\n\tcontent: \"\\f5a4\";\r\n}\r\n.fa-meh-rolling-eyes:before {\r\n\tcontent: \"\\f5a5\";\r\n}\r\n.fa-monument:before {\r\n\tcontent: \"\\f5a6\";\r\n}\r\n.fa-mortar-pestle:before {\r\n\tcontent: \"\\f5a7\";\r\n}\r\n.fa-paint-roller:before {\r\n\tcontent: \"\\f5aa\";\r\n}\r\n.fa-passport:before {\r\n\tcontent: \"\\f5ab\";\r\n}\r\n.fa-prescription:before {\r\n\tcontent: \"\\f5b1\";\r\n}\r\n.fa-shuttle-van:before {\r\n\tcontent: \"\\f5b6\";\r\n}\r\n.fa-signature:before {\r\n\tcontent: \"\\f5b7\";\r\n}\r\n.fa-solar-panel:before {\r\n\tcontent: \"\\f5ba\";\r\n}\r\n.fa-spray-can:before {\r\n\tcontent: \"\\f5bd\";\r\n}\r\n.fa-stamp:before {\r\n\tcontent: \"\\f5bf\";\r\n}\r\n.fa-swimmer:before {\r\n\tcontent: \"\\f5c4\";\r\n}\r\n.fa-tooth:before {\r\n\tcontent: \"\\f5c9\";\r\n}\r\n.fa-weight-hanging:before {\r\n\tcontent: \"\\f5cd\";\r\n}\r\n.fa-air-freshener:before {\r\n\tcontent: \"\\f5d0\";\r\n}\r\n.fa-apple-alt:before {\r\n\tcontent: \"\\f5d1\";\r\n}\r\n.fa-atom:before {\r\n\tcontent: \"\\f5d2\";\r\n}\r\n.fa-atom-alt:before {\r\n\tcontent: \"\\f5d3\";\r\n}\r\n.fa-backpack:before {\r\n\tcontent: \"\\f5d4\";\r\n}\r\n.fa-bell-school:before {\r\n\tcontent: \"\\f5d5\";\r\n}\r\n.fa-bell-school-slash:before {\r\n\tcontent: \"\\f5d6\";\r\n}\r\n.fa-bone:before {\r\n\tcontent: \"\\f5d7\";\r\n}\r\n.fa-bone-break:before {\r\n\tcontent: \"\\f5d8\";\r\n}\r\n.fa-book-alt:before {\r\n\tcontent: \"\\f5d9\";\r\n}\r\n.fa-book-reader:before {\r\n\tcontent: \"\\f5da\";\r\n}\r\n.fa-books:before {\r\n\tcontent: \"\\f5db\";\r\n}\r\n.fa-brain:before {\r\n\tcontent: \"\\f5dc\";\r\n}\r\n.fa-bus-school:before {\r\n\tcontent: \"\\f5dd\";\r\n}\r\n.fa-car-alt:before {\r\n\tcontent: \"\\f5de\";\r\n}\r\n.fa-car-battery:before {\r\n\tcontent: \"\\f5df\";\r\n}\r\n.fa-car-bump:before {\r\n\tcontent: \"\\f5e0\";\r\n}\r\n.fa-car-crash:before {\r\n\tcontent: \"\\f5e1\";\r\n}\r\n.fa-car-garage:before {\r\n\tcontent: \"\\f5e2\";\r\n}\r\n.fa-car-mechanic:before {\r\n\tcontent: \"\\f5e3\";\r\n}\r\n.fa-car-side:before {\r\n\tcontent: \"\\f5e4\";\r\n}\r\n.fa-car-tilt:before {\r\n\tcontent: \"\\f5e5\";\r\n}\r\n.fa-car-wash:before {\r\n\tcontent: \"\\f5e6\";\r\n}\r\n.fa-charging-station:before {\r\n\tcontent: \"\\f5e7\";\r\n}\r\n.fa-clipboard-prescription:before {\r\n\tcontent: \"\\f5e8\";\r\n}\r\n.fa-compass-slash:before {\r\n\tcontent: \"\\f5e9\";\r\n}\r\n.fa-diploma:before {\r\n\tcontent: \"\\f5ea\";\r\n}\r\n.fa-directions:before {\r\n\tcontent: \"\\f5eb\";\r\n}\r\n.fa-do-not-enter:before {\r\n\tcontent: \"\\f5ec\";\r\n}\r\n.fa-draw-circle:before {\r\n\tcontent: \"\\f5ed\";\r\n}\r\n.fa-draw-polygon:before {\r\n\tcontent: \"\\f5ee\";\r\n}\r\n.fa-draw-square:before {\r\n\tcontent: \"\\f5ef\";\r\n}\r\n.fa-ear:before {\r\n\tcontent: \"\\f5f0\";\r\n}\r\n.fa-engine-warning:before {\r\n\tcontent: \"\\f5f2\";\r\n}\r\n.fa-gas-pump-slash:before {\r\n\tcontent: \"\\f5f4\";\r\n}\r\n.fa-glasses-alt:before {\r\n\tcontent: \"\\f5f5\";\r\n}\r\n.fa-globe-stand:before {\r\n\tcontent: \"\\f5f6\";\r\n}\r\n.fa-heart-rate:before {\r\n\tcontent: \"\\f5f8\";\r\n}\r\n.fa-inhaler:before {\r\n\tcontent: \"\\f5f9\";\r\n}\r\n.fa-kidneys:before {\r\n\tcontent: \"\\f5fb\";\r\n}\r\n.fa-laptop-code:before {\r\n\tcontent: \"\\f5fc\";\r\n}\r\n.fa-layer-group:before {\r\n\tcontent: \"\\f5fd\";\r\n}\r\n.fa-layer-minus:before {\r\n\tcontent: \"\\f5fe\";\r\n}\r\n.fa-layer-plus:before {\r\n\tcontent: \"\\f5ff\";\r\n}\r\n.fa-lips:before {\r\n\tcontent: \"\\f600\";\r\n}\r\n.fa-location:before {\r\n\tcontent: \"\\f601\";\r\n}\r\n.fa-location-slash:before {\r\n\tcontent: \"\\f603\";\r\n}\r\n.fa-lungs:before {\r\n\tcontent: \"\\f604\";\r\n}\r\n.fa-map-marker-alt-slash:before {\r\n\tcontent: \"\\f605\";\r\n}\r\n.fa-map-marker-check:before {\r\n\tcontent: \"\\f606\";\r\n}\r\n.fa-map-marker-edit:before {\r\n\tcontent: \"\\f607\";\r\n}\r\n.fa-map-marker-exclamation:before {\r\n\tcontent: \"\\f608\";\r\n}\r\n.fa-map-marker-minus:before {\r\n\tcontent: \"\\f609\";\r\n}\r\n.fa-map-marker-question:before {\r\n\tcontent: \"\\f60b\";\r\n}\r\n.fa-map-marker-slash:before {\r\n\tcontent: \"\\f60c\";\r\n}\r\n.fa-map-marker-smile:before {\r\n\tcontent: \"\\f60d\";\r\n}\r\n.fa-map-marker-times:before {\r\n\tcontent: \"\\f60e\";\r\n}\r\n.fa-microscope:before {\r\n\tcontent: \"\\f610\";\r\n}\r\n.fa-monitor-heart-rate:before {\r\n\tcontent: \"\\f611\";\r\n}\r\n.fa-oil-can:before {\r\n\tcontent: \"\\f613\";\r\n}\r\n.fa-parking-circle:before {\r\n\tcontent: \"\\f615\";\r\n}\r\n.fa-route-highway:before {\r\n\tcontent: \"\\f61a\";\r\n}\r\n.fa-shapes:before {\r\n\tcontent: \"\\f61f\";\r\n}\r\n.fa-steering-wheel:before {\r\n\tcontent: \"\\f622\";\r\n}\r\n.fa-stomach:before {\r\n\tcontent: \"\\f623\";\r\n}\r\n.fa-teeth-open:before {\r\n\tcontent: \"\\f62f\";\r\n}\r\n.fa-tire:before {\r\n\tcontent: \"\\f631\";\r\n}\r\n.fa-traffic-cone:before {\r\n\tcontent: \"\\f636\";\r\n}\r\n.fa-traffic-light:before {\r\n\tcontent: \"\\f637\";\r\n}\r\n.fa-users-class:before {\r\n\tcontent: \"\\f63d\";\r\n}\r\n.fa-abacus:before {\r\n\tcontent: \"\\f640\";\r\n}\r\n.fa-ad:before {\r\n\tcontent: \"\\f641\";\r\n}\r\n.fa-alipay:before {\r\n\tcontent: \"\\f642\";\r\n}\r\n.fa-analytics:before {\r\n\tcontent: \"\\f643\";\r\n}\r\n.fa-ankh:before {\r\n\tcontent: \"\\f644\";\r\n}\r\n.fa-badge-dollar:before {\r\n\tcontent: \"\\f645\";\r\n}\r\n.fa-badge-percent:before {\r\n\tcontent: \"\\f646\";\r\n}\r\n.fa-bible:before {\r\n\tcontent: \"\\f647\";\r\n}\r\n.fa-bullseye-arrow:before {\r\n\tcontent: \"\\f648\";\r\n}\r\n.fa-bullseye-pointer:before {\r\n\tcontent: \"\\f649\";\r\n}\r\n.fa-business-time:before {\r\n\tcontent: \"\\f64a\";\r\n}\r\n.fa-cabinet-filing:before {\r\n\tcontent: \"\\f64b\";\r\n}\r\n.fa-calculator-alt:before {\r\n\tcontent: \"\\f64c\";\r\n}\r\n.fa-chart-line-down:before {\r\n\tcontent: \"\\f64d\";\r\n}\r\n.fa-chart-pie-alt:before {\r\n\tcontent: \"\\f64e\";\r\n}\r\n.fa-city:before {\r\n\tcontent: \"\\f64f\";\r\n}\r\n.fa-comment-dollar:before {\r\n\tcontent: \"\\f651\";\r\n}\r\n.fa-comments-alt-dollar:before {\r\n\tcontent: \"\\f652\";\r\n}\r\n.fa-comments-dollar:before {\r\n\tcontent: \"\\f653\";\r\n}\r\n.fa-cross:before {\r\n\tcontent: \"\\f654\";\r\n}\r\n.fa-dharmachakra:before {\r\n\tcontent: \"\\f655\";\r\n}\r\n.fa-empty-set:before {\r\n\tcontent: \"\\f656\";\r\n}\r\n.fa-envelope-open-dollar:before {\r\n\tcontent: \"\\f657\";\r\n}\r\n.fa-envelope-open-text:before {\r\n\tcontent: \"\\f658\";\r\n}\r\n.fa-file-chart-line:before {\r\n\tcontent: \"\\f659\";\r\n}\r\n.fa-file-chart-pie:before {\r\n\tcontent: \"\\f65a\";\r\n}\r\n.fa-file-spreadsheet:before {\r\n\tcontent: \"\\f65b\";\r\n}\r\n.fa-file-user:before {\r\n\tcontent: \"\\f65c\";\r\n}\r\n.fa-folder-minus:before {\r\n\tcontent: \"\\f65d\";\r\n}\r\n.fa-folder-plus:before {\r\n\tcontent: \"\\f65e\";\r\n}\r\n.fa-folder-times:before {\r\n\tcontent: \"\\f65f\";\r\n}\r\n.fa-folders:before {\r\n\tcontent: \"\\f660\";\r\n}\r\n.fa-function:before {\r\n\tcontent: \"\\f661\";\r\n}\r\n.fa-funnel-dollar:before {\r\n\tcontent: \"\\f662\";\r\n}\r\n.fa-gift-card:before {\r\n\tcontent: \"\\f663\";\r\n}\r\n.fa-gopuram:before {\r\n\tcontent: \"\\f664\";\r\n}\r\n.fa-hamsa:before {\r\n\tcontent: \"\\f665\";\r\n}\r\n.fa-bahai:before {\r\n\tcontent: \"\\f666\";\r\n}\r\n.fa-integral:before {\r\n\tcontent: \"\\f667\";\r\n}\r\n.fa-intersection:before {\r\n\tcontent: \"\\f668\";\r\n}\r\n.fa-jedi:before {\r\n\tcontent: \"\\f669\";\r\n}\r\n.fa-journal-whills:before {\r\n\tcontent: \"\\f66a\";\r\n}\r\n.fa-kaaba:before {\r\n\tcontent: \"\\f66b\";\r\n}\r\n.fa-keynote:before {\r\n\tcontent: \"\\f66c\";\r\n}\r\n.fa-khanda:before {\r\n\tcontent: \"\\f66d\";\r\n}\r\n.fa-lambda:before {\r\n\tcontent: \"\\f66e\";\r\n}\r\n.fa-landmark:before {\r\n\tcontent: \"\\f66f\";\r\n}\r\n.fa-lightbulb-dollar:before {\r\n\tcontent: \"\\f670\";\r\n}\r\n.fa-lightbulb-exclamation:before {\r\n\tcontent: \"\\f671\";\r\n}\r\n.fa-lightbulb-on:before {\r\n\tcontent: \"\\f672\";\r\n}\r\n.fa-lightbulb-slash:before {\r\n\tcontent: \"\\f673\";\r\n}\r\n.fa-megaphone:before {\r\n\tcontent: \"\\f675\";\r\n}\r\n.fa-menorah:before {\r\n\tcontent: \"\\f676\";\r\n}\r\n.fa-mind-share:before {\r\n\tcontent: \"\\f677\";\r\n}\r\n.fa-mosque:before {\r\n\tcontent: \"\\f678\";\r\n}\r\n.fa-om:before {\r\n\tcontent: \"\\f679\";\r\n}\r\n.fa-omega:before {\r\n\tcontent: \"\\f67a\";\r\n}\r\n.fa-pastafarianism:before {\r\n\tcontent: \"\\f67b\";\r\n}\r\n.fa-peace:before {\r\n\tcontent: \"\\f67c\";\r\n}\r\n.fa-pi:before {\r\n\tcontent: \"\\f67e\";\r\n}\r\n.fa-praying-hands:before {\r\n\tcontent: \"\\f684\";\r\n}\r\n.fa-presentation:before {\r\n\tcontent: \"\\f685\";\r\n}\r\n.fa-quran:before {\r\n\tcontent: \"\\f687\";\r\n}\r\n.fa-sigma:before {\r\n\tcontent: \"\\f68b\";\r\n}\r\n.fa-signal-alt-2:before {\r\n\tcontent: \"\\f692\";\r\n}\r\n.fa-socks:before {\r\n\tcontent: \"\\f696\";\r\n}\r\n.fa-square-root:before {\r\n\tcontent: \"\\f697\";\r\n}\r\n.fa-user-chart:before {\r\n\tcontent: \"\\f6a3\";\r\n}\r\n.fa-volume:before {\r\n\tcontent: \"\\f6a8\";\r\n}\r\n.fa-wifi-slash:before {\r\n\tcontent: \"\\f6ac\";\r\n}\r\n.fa-yin-yang:before {\r\n\tcontent: \"\\f6ad\";\r\n}\r\n.fa-acorn:before {\r\n\tcontent: \"\\f6ae\";\r\n}\r\n.fa-acquisitions-incorporated:before {\r\n\tcontent: \"\\f6af\";\r\n}\r\n.fa-alicorn:before {\r\n\tcontent: \"\\f6b0\";\r\n}\r\n.fa-apple-crate:before {\r\n\tcontent: \"\\f6b1\";\r\n}\r\n.fa-axe:before {\r\n\tcontent: \"\\f6b2\";\r\n}\r\n.fa-axe-battle:before {\r\n\tcontent: \"\\f6b3\";\r\n}\r\n.fa-badger-honey:before {\r\n\tcontent: \"\\f6b4\";\r\n}\r\n.fa-bat:before {\r\n\tcontent: \"\\f6b5\";\r\n}\r\n.fa-blender-phone:before {\r\n\tcontent: \"\\f6b6\";\r\n}\r\n.fa-book-dead:before {\r\n\tcontent: \"\\f6b7\";\r\n}\r\n.fa-book-spells:before {\r\n\tcontent: \"\\f6b8\";\r\n}\r\n.fa-bow-arrow:before {\r\n\tcontent: \"\\f6b9\";\r\n}\r\n.fa-campfire:before {\r\n\tcontent: \"\\f6ba\";\r\n}\r\n.fa-campground:before {\r\n\tcontent: \"\\f6bb\";\r\n}\r\n.fa-candle-holder:before {\r\n\tcontent: \"\\f6bc\";\r\n}\r\n.fa-candy-corn:before {\r\n\tcontent: \"\\f6bd\";\r\n}\r\n.fa-cat:before {\r\n\tcontent: \"\\f6be\";\r\n}\r\n.fa-cauldron:before {\r\n\tcontent: \"\\f6bf\";\r\n}\r\n.fa-chair:before {\r\n\tcontent: \"\\f6c0\";\r\n}\r\n.fa-chair-office:before {\r\n\tcontent: \"\\f6c1\";\r\n}\r\n.fa-claw-marks:before {\r\n\tcontent: \"\\f6c2\";\r\n}\r\n.fa-cloud-moon:before {\r\n\tcontent: \"\\f6c3\";\r\n}\r\n.fa-cloud-sun:before {\r\n\tcontent: \"\\f6c4\";\r\n}\r\n.fa-coffee-togo:before {\r\n\tcontent: \"\\f6c5\";\r\n}\r\n.fa-coffin:before {\r\n\tcontent: \"\\f6c6\";\r\n}\r\n.fa-corn:before {\r\n\tcontent: \"\\f6c7\";\r\n}\r\n.fa-cow:before {\r\n\tcontent: \"\\f6c8\";\r\n}\r\n.fa-critical-role:before {\r\n\tcontent: \"\\f6c9\";\r\n}\r\n.fa-d-and-d-beyond:before {\r\n\tcontent: \"\\f6ca\";\r\n}\r\n.fa-dagger:before {\r\n\tcontent: \"\\f6cb\";\r\n}\r\n.fa-dice-d10:before {\r\n\tcontent: \"\\f6cd\";\r\n}\r\n.fa-dice-d12:before {\r\n\tcontent: \"\\f6ce\";\r\n}\r\n.fa-dice-d20:before {\r\n\tcontent: \"\\f6cf\";\r\n}\r\n.fa-dice-d4:before {\r\n\tcontent: \"\\f6d0\";\r\n}\r\n.fa-dice-d6:before {\r\n\tcontent: \"\\f6d1\";\r\n}\r\n.fa-dice-d8:before {\r\n\tcontent: \"\\f6d2\";\r\n}\r\n.fa-dog:before {\r\n\tcontent: \"\\f6d3\";\r\n}\r\n.fa-dog-leashed:before {\r\n\tcontent: \"\\f6d4\";\r\n}\r\n.fa-dragon:before {\r\n\tcontent: \"\\f6d5\";\r\n}\r\n.fa-drumstick:before {\r\n\tcontent: \"\\f6d6\";\r\n}\r\n.fa-drumstick-bite:before {\r\n\tcontent: \"\\f6d7\";\r\n}\r\n.fa-duck:before {\r\n\tcontent: \"\\f6d8\";\r\n}\r\n.fa-dungeon:before {\r\n\tcontent: \"\\f6d9\";\r\n}\r\n.fa-elephant:before {\r\n\tcontent: \"\\f6da\";\r\n}\r\n.fa-eye-evil:before {\r\n\tcontent: \"\\f6db\";\r\n}\r\n.fa-file-csv:before {\r\n\tcontent: \"\\f6dd\";\r\n}\r\n.fa-fist-raised:before {\r\n\tcontent: \"\\f6de\";\r\n}\r\n.fa-flame:before {\r\n\tcontent: \"\\f6df\";\r\n}\r\n.fa-flask-poison:before {\r\n\tcontent: \"\\f6e0\";\r\n}\r\n.fa-flask-potion:before {\r\n\tcontent: \"\\f6e1\";\r\n}\r\n.fa-ghost:before {\r\n\tcontent: \"\\f6e2\";\r\n}\r\n.fa-hammer:before {\r\n\tcontent: \"\\f6e3\";\r\n}\r\n.fa-hammer-war:before {\r\n\tcontent: \"\\f6e4\";\r\n}\r\n.fa-hand-holding-magic:before {\r\n\tcontent: \"\\f6e5\";\r\n}\r\n.fa-hanukiah:before {\r\n\tcontent: \"\\f6e6\";\r\n}\r\n.fa-hat-witch:before {\r\n\tcontent: \"\\f6e7\";\r\n}\r\n.fa-hat-wizard:before {\r\n\tcontent: \"\\f6e8\";\r\n}\r\n.fa-head-side:before {\r\n\tcontent: \"\\f6e9\";\r\n}\r\n.fa-head-vr:before {\r\n\tcontent: \"\\f6ea\";\r\n}\r\n.fa-helmet-battle:before {\r\n\tcontent: \"\\f6eb\";\r\n}\r\n.fa-hiking:before {\r\n\tcontent: \"\\f6ec\";\r\n}\r\n.fa-hippo:before {\r\n\tcontent: \"\\f6ed\";\r\n}\r\n.fa-hockey-mask:before {\r\n\tcontent: \"\\f6ee\";\r\n}\r\n.fa-hood-cloak:before {\r\n\tcontent: \"\\f6ef\";\r\n}\r\n.fa-horse:before {\r\n\tcontent: \"\\f6f0\";\r\n}\r\n.fa-house-damage:before {\r\n\tcontent: \"\\f6f1\";\r\n}\r\n.fa-hryvnia:before {\r\n\tcontent: \"\\f6f2\";\r\n}\r\n.fa-key-skeleton:before {\r\n\tcontent: \"\\f6f3\";\r\n}\r\n.fa-kite:before {\r\n\tcontent: \"\\f6f4\";\r\n}\r\n.fa-knife-kitchen:before {\r\n\tcontent: \"\\f6f5\";\r\n}\r\n.fa-leaf-maple:before {\r\n\tcontent: \"\\f6f6\";\r\n}\r\n.fa-leaf-oak:before {\r\n\tcontent: \"\\f6f7\";\r\n}\r\n.fa-mace:before {\r\n\tcontent: \"\\f6f8\";\r\n}\r\n.fa-mandolin:before {\r\n\tcontent: \"\\f6f9\";\r\n}\r\n.fa-mask:before {\r\n\tcontent: \"\\f6fa\";\r\n}\r\n.fa-monkey:before {\r\n\tcontent: \"\\f6fb\";\r\n}\r\n.fa-mountain:before {\r\n\tcontent: \"\\f6fc\";\r\n}\r\n.fa-mountains:before {\r\n\tcontent: \"\\f6fd\";\r\n}\r\n.fa-network-wired:before {\r\n\tcontent: \"\\f6ff\";\r\n}\r\n.fa-otter:before {\r\n\tcontent: \"\\f700\";\r\n}\r\n.fa-pie:before {\r\n\tcontent: \"\\f705\";\r\n}\r\n.fa-pumpkin:before {\r\n\tcontent: \"\\f707\";\r\n}\r\n.fa-rabbit:before {\r\n\tcontent: \"\\f708\";\r\n}\r\n.fa-ram:before {\r\n\tcontent: \"\\f70a\";\r\n}\r\n.fa-running:before {\r\n\tcontent: \"\\f70c\";\r\n}\r\n.fa-scarecrow:before {\r\n\tcontent: \"\\f70d\";\r\n}\r\n.fa-scroll:before {\r\n\tcontent: \"\\f70e\";\r\n}\r\n.fa-shovel:before {\r\n\tcontent: \"\\f713\";\r\n}\r\n.fa-slash:before {\r\n\tcontent: \"\\f715\";\r\n}\r\n.fa-snake:before {\r\n\tcontent: \"\\f716\";\r\n}\r\n.fa-spider:before {\r\n\tcontent: \"\\f717\";\r\n}\r\n.fa-spider-web:before {\r\n\tcontent: \"\\f719\";\r\n}\r\n.fa-squirrel:before {\r\n\tcontent: \"\\f71a\";\r\n}\r\n.fa-staff:before {\r\n\tcontent: \"\\f71b\";\r\n}\r\n.fa-sword:before {\r\n\tcontent: \"\\f71c\";\r\n}\r\n.fa-toilet-paper:before {\r\n\tcontent: \"\\f71e\";\r\n}\r\n.fa-tombstone:before {\r\n\tcontent: \"\\f720\";\r\n}\r\n.fa-turtle:before {\r\n\tcontent: \"\\f726\";\r\n}\r\n.fa-vr-cardboard:before {\r\n\tcontent: \"\\f729\";\r\n}\r\n.fa-whale:before {\r\n\tcontent: \"\\f72c\";\r\n}\r\n.fa-wheat:before {\r\n\tcontent: \"\\f72d\";\r\n}\r\n.fa-ballot:before {\r\n\tcontent: \"\\f732\";\r\n}\r\n.fa-ballot-check:before {\r\n\tcontent: \"\\f733\";\r\n}\r\n.fa-booth-curtain:before {\r\n\tcontent: \"\\f734\";\r\n}\r\n.fa-box-ballot:before {\r\n\tcontent: \"\\f735\";\r\n}\r\n.fa-calendar-star:before {\r\n\tcontent: \"\\f736\";\r\n}\r\n.fa-clipboard-list-check:before {\r\n\tcontent: \"\\f737\";\r\n}\r\n.fa-cloud-drizzle:before {\r\n\tcontent: \"\\f738\";\r\n}\r\n.fa-cloud-hail:before {\r\n\tcontent: \"\\f739\";\r\n}\r\n.fa-cloud-hail-mixed:before {\r\n\tcontent: \"\\f73a\";\r\n}\r\n.fa-cloud-meatball:before {\r\n\tcontent: \"\\f73b\";\r\n}\r\n.fa-cloud-moon-rain:before {\r\n\tcontent: \"\\f73c\";\r\n}\r\n.fa-cloud-rain:before {\r\n\tcontent: \"\\f73d\";\r\n}\r\n.fa-cloud-rainbow:before {\r\n\tcontent: \"\\f73e\";\r\n}\r\n.fa-cloud-showers:before {\r\n\tcontent: \"\\f73f\";\r\n}\r\n.fa-cloud-showers-heavy:before {\r\n\tcontent: \"\\f740\";\r\n}\r\n.fa-cloud-sleet:before {\r\n\tcontent: \"\\f741\";\r\n}\r\n.fa-cloud-snow:before {\r\n\tcontent: \"\\f742\";\r\n}\r\n.fa-cloud-sun-rain:before {\r\n\tcontent: \"\\f743\";\r\n}\r\n.fa-clouds:before {\r\n\tcontent: \"\\f744\";\r\n}\r\n.fa-clouds-moon:before {\r\n\tcontent: \"\\f745\";\r\n}\r\n.fa-clouds-sun:before {\r\n\tcontent: \"\\f746\";\r\n}\r\n.fa-democrat:before {\r\n\tcontent: \"\\f747\";\r\n}\r\n.fa-dewpoint:before {\r\n\tcontent: \"\\f748\";\r\n}\r\n.fa-eclipse:before {\r\n\tcontent: \"\\f749\";\r\n}\r\n.fa-eclipse-alt:before {\r\n\tcontent: \"\\f74a\";\r\n}\r\n.fa-fire-smoke:before {\r\n\tcontent: \"\\f74b\";\r\n}\r\n.fa-flag-alt:before {\r\n\tcontent: \"\\f74c\";\r\n}\r\n.fa-flag-usa:before {\r\n\tcontent: \"\\f74d\";\r\n}\r\n.fa-fog:before {\r\n\tcontent: \"\\f74e\";\r\n}\r\n.fa-house-flood:before {\r\n\tcontent: \"\\f74f\";\r\n}\r\n.fa-humidity:before {\r\n\tcontent: \"\\f750\";\r\n}\r\n.fa-hurricane:before {\r\n\tcontent: \"\\f751\";\r\n}\r\n.fa-landmark-alt:before {\r\n\tcontent: \"\\f752\";\r\n}\r\n.fa-meteor:before {\r\n\tcontent: \"\\f753\";\r\n}\r\n.fa-moon-cloud:before {\r\n\tcontent: \"\\f754\";\r\n}\r\n.fa-moon-stars:before {\r\n\tcontent: \"\\f755\";\r\n}\r\n.fa-podium-star:before {\r\n\tcontent: \"\\f758\";\r\n}\r\n.fa-raindrops:before {\r\n\tcontent: \"\\f75c\";\r\n}\r\n.fa-smog:before {\r\n\tcontent: \"\\f75f\";\r\n}\r\n.fa-thunderstorm:before {\r\n\tcontent: \"\\f76c\";\r\n}\r\n.fa-volcano:before {\r\n\tcontent: \"\\f770\";\r\n}\r\n.fa-water:before {\r\n\tcontent: \"\\f773\";\r\n}\r\n.fa-angel:before {\r\n\tcontent: \"\\f779\";\r\n}\r\n.fa-artstation:before {\r\n\tcontent: \"\\f77a\";\r\n}\r\n.fa-atlassian:before {\r\n\tcontent: \"\\f77b\";\r\n}\r\n.fa-baby:before {\r\n\tcontent: \"\\f77c\";\r\n}\r\n.fa-baby-carriage:before {\r\n\tcontent: \"\\f77d\";\r\n}\r\n.fa-ball-pile:before {\r\n\tcontent: \"\\f77e\";\r\n}\r\n.fa-bells:before {\r\n\tcontent: \"\\f77f\";\r\n}\r\n.fa-biohazard:before {\r\n\tcontent: \"\\f780\";\r\n}\r\n.fa-blog:before {\r\n\tcontent: \"\\f781\";\r\n}\r\n.fa-boot:before {\r\n\tcontent: \"\\f782\";\r\n}\r\n.fa-calendar-day:before {\r\n\tcontent: \"\\f783\";\r\n}\r\n.fa-calendar-week:before {\r\n\tcontent: \"\\f784\";\r\n}\r\n.fa-canadian-maple-leaf:before {\r\n\tcontent: \"\\f785\";\r\n}\r\n.fa-candy-cane:before {\r\n\tcontent: \"\\f786\";\r\n}\r\n.fa-carrot:before {\r\n\tcontent: \"\\f787\";\r\n}\r\n.fa-cash-register:before {\r\n\tcontent: \"\\f788\";\r\n}\r\n.fa-centos:before {\r\n\tcontent: \"\\f789\";\r\n}\r\n.fa-chart-network:before {\r\n\tcontent: \"\\f78a\";\r\n}\r\n.fa-chimney:before {\r\n\tcontent: \"\\f78b\";\r\n}\r\n.fa-compress-arrows-alt:before {\r\n\tcontent: \"\\f78c\";\r\n}\r\n.fa-confluence:before {\r\n\tcontent: \"\\f78d\";\r\n}\r\n.fa-deer:before {\r\n\tcontent: \"\\f78e\";\r\n}\r\n.fa-deer-rudolph:before {\r\n\tcontent: \"\\f78f\";\r\n}\r\n.fa-diaspora:before {\r\n\tcontent: \"\\f791\";\r\n}\r\n.fa-dreidel:before {\r\n\tcontent: \"\\f792\";\r\n}\r\n.fa-dumpster:before {\r\n\tcontent: \"\\f793\";\r\n}\r\n.fa-dumpster-fire:before {\r\n\tcontent: \"\\f794\";\r\n}\r\n.fa-ear-muffs:before {\r\n\tcontent: \"\\f795\";\r\n}\r\n.fa-ethernet:before {\r\n\tcontent: \"\\f796\";\r\n}\r\n.fa-fireplace:before {\r\n\tcontent: \"\\f79a\";\r\n}\r\n.fa-frosty-head:before {\r\n\tcontent: \"\\f79b\";\r\n}\r\n.fa-gifts:before {\r\n\tcontent: \"\\f79c\";\r\n}\r\n.fa-gingerbread-man:before {\r\n\tcontent: \"\\f79d\";\r\n}\r\n.fa-glass-champagne:before {\r\n\tcontent: \"\\f79e\";\r\n}\r\n.fa-glass-cheers:before {\r\n\tcontent: \"\\f79f\";\r\n}\r\n.fa-glass-whiskey:before {\r\n\tcontent: \"\\f7a0\";\r\n}\r\n.fa-glass-whiskey-rocks:before {\r\n\tcontent: \"\\f7a1\";\r\n}\r\n.fa-globe-europe:before {\r\n\tcontent: \"\\f7a2\";\r\n}\r\n.fa-globe-snow:before {\r\n\tcontent: \"\\f7a3\";\r\n}\r\n.fa-grip-lines:before {\r\n\tcontent: \"\\f7a4\";\r\n}\r\n.fa-grip-lines-vertical:before {\r\n\tcontent: \"\\f7a5\";\r\n}\r\n.fa-guitar:before {\r\n\tcontent: \"\\f7a6\";\r\n}\r\n.fa-hat-santa:before {\r\n\tcontent: \"\\f7a7\";\r\n}\r\n.fa-hat-winter:before {\r\n\tcontent: \"\\f7a8\";\r\n}\r\n.fa-heart-broken:before {\r\n\tcontent: \"\\f7a9\";\r\n}\r\n.fa-holly-berry:before {\r\n\tcontent: \"\\f7aa\";\r\n}\r\n.fa-horse-head:before {\r\n\tcontent: \"\\f7ab\";\r\n}\r\n.fa-ice-skate:before {\r\n\tcontent: \"\\f7ac\";\r\n}\r\n.fa-icicles:before {\r\n\tcontent: \"\\f7ad\";\r\n}\r\n.fa-igloo:before {\r\n\tcontent: \"\\f7ae\";\r\n}\r\n.fa-lights-holiday:before {\r\n\tcontent: \"\\f7b2\";\r\n}\r\n.fa-mistletoe:before {\r\n\tcontent: \"\\f7b4\";\r\n}\r\n.fa-mitten:before {\r\n\tcontent: \"\\f7b5\";\r\n}\r\n.fa-mug-hot:before {\r\n\tcontent: \"\\f7b6\";\r\n}\r\n.fa-mug-marshmallows:before {\r\n\tcontent: \"\\f7b7\";\r\n}\r\n.fa-ornament:before {\r\n\tcontent: \"\\f7b8\";\r\n}\r\n.fa-radiation-alt:before {\r\n\tcontent: \"\\f7ba\";\r\n}\r\n.fa-restroom:before {\r\n\tcontent: \"\\f7bd\";\r\n}\r\n.fa-satellite:before {\r\n\tcontent: \"\\f7bf\";\r\n}\r\n.fa-scarf:before {\r\n\tcontent: \"\\f7c1\";\r\n}\r\n.fa-sd-card:before {\r\n\tcontent: \"\\f7c2\";\r\n}\r\n.fa-sim-card:before {\r\n\tcontent: \"\\f7c4\";\r\n}\r\n.fa-sleigh:before {\r\n\tcontent: \"\\f7cc\";\r\n}\r\n.fa-sms:before {\r\n\tcontent: \"\\f7cd\";\r\n}\r\n.fa-snowman:before {\r\n\tcontent: \"\\f7d0\";\r\n}\r\n.fa-toilet:before {\r\n\tcontent: \"\\f7d8\";\r\n}\r\n.fa-tools:before {\r\n\tcontent: \"\\f7d9\";\r\n}\r\n.fa-fire-alt:before {\r\n\tcontent: \"\\f7e4\";\r\n}\r\n.fa-bacon:before {\r\n\tcontent: \"\\f7e5\";\r\n}\r\n.fa-book-medical:before {\r\n\tcontent: \"\\f7e6\";\r\n}\r\n.fa-book-user:before {\r\n\tcontent: \"\\f7e7\";\r\n}\r\n.fa-books-medical:before {\r\n\tcontent: \"\\f7e8\";\r\n}\r\n.fa-brackets:before {\r\n\tcontent: \"\\f7e9\";\r\n}\r\n.fa-brackets-curly:before {\r\n\tcontent: \"\\f7ea\";\r\n}\r\n.fa-bread-loaf:before {\r\n\tcontent: \"\\f7eb\";\r\n}\r\n.fa-bread-slice:before {\r\n\tcontent: \"\\f7ec\";\r\n}\r\n.fa-burrito:before {\r\n\tcontent: \"\\f7ed\";\r\n}\r\n.fa-chart-scatter:before {\r\n\tcontent: \"\\f7ee\";\r\n}\r\n.fa-cheese:before {\r\n\tcontent: \"\\f7ef\";\r\n}\r\n.fa-cheese-swiss:before {\r\n\tcontent: \"\\f7f0\";\r\n}\r\n.fa-cheeseburger:before {\r\n\tcontent: \"\\f7f1\";\r\n}\r\n.fa-clinic-medical:before {\r\n\tcontent: \"\\f7f2\";\r\n}\r\n.fa-clipboard-user:before {\r\n\tcontent: \"\\f7f3\";\r\n}\r\n.fa-comment-alt-medical:before {\r\n\tcontent: \"\\f7f4\";\r\n}\r\n.fa-comment-medical:before {\r\n\tcontent: \"\\f7f5\";\r\n}\r\n.fa-croissant:before {\r\n\tcontent: \"\\f7f6\";\r\n}\r\n.fa-crutch:before {\r\n\tcontent: \"\\f7f7\";\r\n}\r\n.fa-crutches:before {\r\n\tcontent: \"\\f7f8\";\r\n}\r\n.fa-debug:before {\r\n\tcontent: \"\\f7f9\";\r\n}\r\n.fa-disease:before {\r\n\tcontent: \"\\f7fa\";\r\n}\r\n.fa-egg:before {\r\n\tcontent: \"\\f7fb\";\r\n}\r\n.fa-egg-fried:before {\r\n\tcontent: \"\\f7fc\";\r\n}\r\n.fa-files-medical:before {\r\n\tcontent: \"\\f7fd\";\r\n}\r\n.fa-fish-cooked:before {\r\n\tcontent: \"\\f7fe\";\r\n}\r\n.fa-flower:before {\r\n\tcontent: \"\\f7ff\";\r\n}\r\n.fa-flower-daffodil:before {\r\n\tcontent: \"\\f800\";\r\n}\r\n.fa-flower-tulip:before {\r\n\tcontent: \"\\f801\";\r\n}\r\n.fa-folder-tree:before {\r\n\tcontent: \"\\f802\";\r\n}\r\n.fa-french-fries:before {\r\n\tcontent: \"\\f803\";\r\n}\r\n.fa-glass:before {\r\n\tcontent: \"\\f804\";\r\n}\r\n.fa-hamburger:before {\r\n\tcontent: \"\\f805\";\r\n}\r\n.fa-hand-middle-finger:before {\r\n\tcontent: \"\\f806\";\r\n}\r\n.fa-hard-hat:before {\r\n\tcontent: \"\\f807\";\r\n}\r\n.fa-head-side-brain:before {\r\n\tcontent: \"\\f808\";\r\n}\r\n.fa-head-side-medical:before {\r\n\tcontent: \"\\f809\";\r\n}\r\n.fa-home-alt:before {\r\n\tcontent: \"\\f80a\";\r\n}\r\n.fa-home-lg:before {\r\n\tcontent: \"\\f80b\";\r\n}\r\n.fa-home-lg-alt:before {\r\n\tcontent: \"\\f80c\";\r\n}\r\n.fa-hospital-user:before {\r\n\tcontent: \"\\f80d\";\r\n}\r\n.fa-hospitals:before {\r\n\tcontent: \"\\f80e\";\r\n}\r\n.fa-hotdog:before {\r\n\tcontent: \"\\f80f\";\r\n}\r\n.fa-ice-cream:before {\r\n\tcontent: \"\\f810\";\r\n}\r\n.fa-island-tropical:before {\r\n\tcontent: \"\\f811\";\r\n}\r\n.fa-laptop-medical:before {\r\n\tcontent: \"\\f812\";\r\n}\r\n.fa-mailbox:before {\r\n\tcontent: \"\\f813\";\r\n}\r\n.fa-meat:before {\r\n\tcontent: \"\\f814\";\r\n}\r\n.fa-pager:before {\r\n\tcontent: \"\\f815\";\r\n}\r\n.fa-pepper-hot:before {\r\n\tcontent: \"\\f816\";\r\n}\r\n.fa-pizza-slice:before {\r\n\tcontent: \"\\f818\";\r\n}\r\n.fa-popcorn:before {\r\n\tcontent: \"\\f819\";\r\n}\r\n.fa-rings-wedding:before {\r\n\tcontent: \"\\f81b\";\r\n}\r\n.fa-sack-dollar:before {\r\n\tcontent: \"\\f81d\";\r\n}\r\n.fa-salad:before {\r\n\tcontent: \"\\f81e\";\r\n}\r\n.fa-sandwich:before {\r\n\tcontent: \"\\f81f\";\r\n}\r\n.fa-sausage:before {\r\n\tcontent: \"\\f820\";\r\n}\r\n.fa-soup:before {\r\n\tcontent: \"\\f823\";\r\n}\r\n.fa-steak:before {\r\n\tcontent: \"\\f824\";\r\n}\r\n.fa-stretcher:before {\r\n\tcontent: \"\\f825\";\r\n}\r\n.fa-user-headset:before {\r\n\tcontent: \"\\f82d\";\r\n}\r\n.fa-users-medical:before {\r\n\tcontent: \"\\f830\";\r\n}\r\n.fa-walker:before {\r\n\tcontent: \"\\f831\";\r\n}\r\n.fa-webcam:before {\r\n\tcontent: \"\\f832\";\r\n}\r\n.fa-airbnb:before {\r\n\tcontent: \"\\f834\";\r\n}\r\n.fa-battle-net:before {\r\n\tcontent: \"\\f835\";\r\n}\r\n.fa-bootstrap:before {\r\n\tcontent: \"\\f836\";\r\n}\r\n.fa-chromecast:before {\r\n\tcontent: \"\\f838\";\r\n}\r\n.fa-alarm-exclamation:before {\r\n\tcontent: \"\\f843\";\r\n}\r\n.fa-alarm-plus:before {\r\n\tcontent: \"\\f844\";\r\n}\r\n.fa-alarm-snooze:before {\r\n\tcontent: \"\\f845\";\r\n}\r\n.fa-align-slash:before {\r\n\tcontent: \"\\f846\";\r\n}\r\n.fa-bags-shopping:before {\r\n\tcontent: \"\\f847\";\r\n}\r\n.fa-bell-exclamation:before {\r\n\tcontent: \"\\f848\";\r\n}\r\n.fa-bell-plus:before {\r\n\tcontent: \"\\f849\";\r\n}\r\n.fa-biking:before {\r\n\tcontent: \"\\f84a\";\r\n}\r\n.fa-biking-mountain:before {\r\n\tcontent: \"\\f84b\";\r\n}\r\n.fa-border-all:before {\r\n\tcontent: \"\\f84c\";\r\n}\r\n.fa-border-bottom:before {\r\n\tcontent: \"\\f84d\";\r\n}\r\n.fa-border-inner:before {\r\n\tcontent: \"\\f84e\";\r\n}\r\n.fa-border-left:before {\r\n\tcontent: \"\\f84f\";\r\n}\r\n.fa-border-none:before {\r\n\tcontent: \"\\f850\";\r\n}\r\n.fa-border-outer:before {\r\n\tcontent: \"\\f851\";\r\n}\r\n.fa-border-right:before {\r\n\tcontent: \"\\f852\";\r\n}\r\n.fa-border-style:before {\r\n\tcontent: \"\\f853\";\r\n}\r\n.fa-border-style-alt:before {\r\n\tcontent: \"\\f854\";\r\n}\r\n.fa-border-top:before {\r\n\tcontent: \"\\f855\";\r\n}\r\n.fa-bring-forward:before {\r\n\tcontent: \"\\f856\";\r\n}\r\n.fa-bring-front:before {\r\n\tcontent: \"\\f857\";\r\n}\r\n.fa-burger-soda:before {\r\n\tcontent: \"\\f858\";\r\n}\r\n.fa-car-building:before {\r\n\tcontent: \"\\f859\";\r\n}\r\n.fa-car-bus:before {\r\n\tcontent: \"\\f85a\";\r\n}\r\n.fa-cars:before {\r\n\tcontent: \"\\f85b\";\r\n}\r\n.fa-coin:before {\r\n\tcontent: \"\\f85c\";\r\n}\r\n.fa-construction:before {\r\n\tcontent: \"\\f85d\";\r\n}\r\n.fa-digging:before {\r\n\tcontent: \"\\f85e\";\r\n}\r\n.fa-drone:before {\r\n\tcontent: \"\\f85f\";\r\n}\r\n.fa-drone-alt:before {\r\n\tcontent: \"\\f860\";\r\n}\r\n.fa-dryer:before {\r\n\tcontent: \"\\f861\";\r\n}\r\n.fa-dryer-alt:before {\r\n\tcontent: \"\\f862\";\r\n}\r\n.fa-fan:before {\r\n\tcontent: \"\\f863\";\r\n}\r\n.fa-farm:before {\r\n\tcontent: \"\\f864\";\r\n}\r\n.fa-file-search:before {\r\n\tcontent: \"\\f865\";\r\n}\r\n.fa-font-case:before {\r\n\tcontent: \"\\f866\";\r\n}\r\n.fa-game-board:before {\r\n\tcontent: \"\\f867\";\r\n}\r\n.fa-game-board-alt:before {\r\n\tcontent: \"\\f868\";\r\n}\r\n.fa-glass-citrus:before {\r\n\tcontent: \"\\f869\";\r\n}\r\n.fa-h4:before {\r\n\tcontent: \"\\f86a\";\r\n}\r\n.fa-hat-chef:before {\r\n\tcontent: \"\\f86b\";\r\n}\r\n.fa-horizontal-rule:before {\r\n\tcontent: \"\\f86c\";\r\n}\r\n.fa-icons:before {\r\n\tcontent: \"\\f86d\";\r\n}\r\n.fa-kerning:before {\r\n\tcontent: \"\\f86f\";\r\n}\r\n.fa-line-columns:before {\r\n\tcontent: \"\\f870\";\r\n}\r\n.fa-line-height:before {\r\n\tcontent: \"\\f871\";\r\n}\r\n.fa-money-check-edit:before {\r\n\tcontent: \"\\f872\";\r\n}\r\n.fa-money-check-edit-alt:before {\r\n\tcontent: \"\\f873\";\r\n}\r\n.fa-mug:before {\r\n\tcontent: \"\\f874\";\r\n}\r\n.fa-phone-alt:before {\r\n\tcontent: \"\\f879\";\r\n}\r\n.fa-snooze:before {\r\n\tcontent: \"\\f880\";\r\n}\r\n.fa-sort-alt:before {\r\n\tcontent: \"\\f883\";\r\n}\r\n.fa-sort-amount-down-alt:before {\r\n\tcontent: \"\\f884\";\r\n}\r\n.fa-sort-size-down:before {\r\n\tcontent: \"\\f88c\";\r\n}\r\n.fa-sparkles:before {\r\n\tcontent: \"\\f890\";\r\n}\r\n.fa-text:before {\r\n\tcontent: \"\\f893\";\r\n}\r\n.fa-text-size:before {\r\n\tcontent: \"\\f894\";\r\n}\r\n.fa-voicemail:before {\r\n\tcontent: \"\\f897\";\r\n}\r\n.fa-washer:before {\r\n\tcontent: \"\\f898\";\r\n}\r\n.fa-wind-turbine:before {\r\n\tcontent: \"\\f89b\";\r\n}\r\n.fa-border-center-h:before {\r\n\tcontent: \"\\f89c\";\r\n}\r\n.fa-border-center-v:before {\r\n\tcontent: \"\\f89d\";\r\n}\r\n.fa-cotton-bureau:before {\r\n\tcontent: \"\\f89e\";\r\n}\r\n.fa-album:before {\r\n\tcontent: \"\\f89f\";\r\n}\r\n.fa-album-collection:before {\r\n\tcontent: \"\\f8a0\";\r\n}\r\n.fa-amp-guitar:before {\r\n\tcontent: \"\\f8a1\";\r\n}\r\n.fa-badge-sheriff:before {\r\n\tcontent: \"\\f8a2\";\r\n}\r\n.fa-banjo:before {\r\n\tcontent: \"\\f8a3\";\r\n}\r\n.fa-betamax:before {\r\n\tcontent: \"\\f8a4\";\r\n}\r\n.fa-boombox:before {\r\n\tcontent: \"\\f8a5\";\r\n}\r\n.fa-buy-n-large:before {\r\n\tcontent: \"\\f8a6\";\r\n}\r\n.fa-cactus:before {\r\n\tcontent: \"\\f8a7\";\r\n}\r\n.fa-camcorder:before {\r\n\tcontent: \"\\f8a8\";\r\n}\r\n.fa-camera-movie:before {\r\n\tcontent: \"\\f8a9\";\r\n}\r\n.fa-camera-polaroid:before {\r\n\tcontent: \"\\f8aa\";\r\n}\r\n.fa-cassette-tape:before {\r\n\tcontent: \"\\f8ab\";\r\n}\r\n.fa-cctv:before {\r\n\tcontent: \"\\f8ac\";\r\n}\r\n.fa-clarinet:before {\r\n\tcontent: \"\\f8ad\";\r\n}\r\n.fa-cloud-music:before {\r\n\tcontent: \"\\f8ae\";\r\n}\r\n.fa-comment-alt-music:before {\r\n\tcontent: \"\\f8af\";\r\n}\r\n.fa-comment-music:before {\r\n\tcontent: \"\\f8b0\";\r\n}\r\n.fa-computer-classic:before {\r\n\tcontent: \"\\f8b1\";\r\n}\r\n.fa-computer-speaker:before {\r\n\tcontent: \"\\f8b2\";\r\n}\r\n.fa-cowbell:before {\r\n\tcontent: \"\\f8b3\";\r\n}\r\n.fa-cowbell-more:before {\r\n\tcontent: \"\\f8b4\";\r\n}\r\n.fa-disc-drive:before {\r\n\tcontent: \"\\f8b5\";\r\n}\r\n.fa-file-music:before {\r\n\tcontent: \"\\f8b6\";\r\n}\r\n.fa-film-canister:before {\r\n\tcontent: \"\\f8b7\";\r\n}\r\n.fa-flashlight:before {\r\n\tcontent: \"\\f8b8\";\r\n}\r\n.fa-flute:before {\r\n\tcontent: \"\\f8b9\";\r\n}\r\n.fa-flux-capacitor:before {\r\n\tcontent: \"\\f8ba\";\r\n}\r\n.fa-game-console-handheld:before {\r\n\tcontent: \"\\f8bb\";\r\n}\r\n.fa-gamepad-alt:before {\r\n\tcontent: \"\\f8bc\";\r\n}\r\n.fa-gramophone:before {\r\n\tcontent: \"\\f8bd\";\r\n}\r\n.fa-guitar-electric:before {\r\n\tcontent: \"\\f8be\";\r\n}\r\n.fa-guitars:before {\r\n\tcontent: \"\\f8bf\";\r\n}\r\n.fa-hat-cowboy:before {\r\n\tcontent: \"\\f8c0\";\r\n}\r\n.fa-hat-cowboy-side:before {\r\n\tcontent: \"\\f8c1\";\r\n}\r\n.fa-head-side-headphones:before {\r\n\tcontent: \"\\f8c2\";\r\n}\r\n.fa-horse-saddle:before {\r\n\tcontent: \"\\f8c3\";\r\n}\r\n.fa-image-polaroid:before {\r\n\tcontent: \"\\f8c4\";\r\n}\r\n.fa-joystick:before {\r\n\tcontent: \"\\f8c5\";\r\n}\r\n.fa-jug:before {\r\n\tcontent: \"\\f8c6\";\r\n}\r\n.fa-kazoo:before {\r\n\tcontent: \"\\f8c7\";\r\n}\r\n.fa-lasso:before {\r\n\tcontent: \"\\f8c8\";\r\n}\r\n.fa-list-music:before {\r\n\tcontent: \"\\f8c9\";\r\n}\r\n.fa-microphone-stand:before {\r\n\tcontent: \"\\f8cb\";\r\n}\r\n.fa-mouse:before {\r\n\tcontent: \"\\f8cc\";\r\n}\r\n.fa-mouse-alt:before {\r\n\tcontent: \"\\f8cd\";\r\n}\r\n.fa-mp3-player:before {\r\n\tcontent: \"\\f8ce\";\r\n}\r\n.fa-music-slash:before {\r\n\tcontent: \"\\f8d1\";\r\n}\r\n.fa-piano-keyboard:before {\r\n\tcontent: \"\\f8d5\";\r\n}\r\n.fa-projector:before {\r\n\tcontent: \"\\f8d6\";\r\n}\r\n.fa-radio-alt:before {\r\n\tcontent: \"\\f8d8\";\r\n}\r\n.fa-router:before {\r\n\tcontent: \"\\f8da\";\r\n}\r\n.fa-saxophone:before {\r\n\tcontent: \"\\f8dc\";\r\n}\r\n.fa-speakers:before {\r\n\tcontent: \"\\f8e0\";\r\n}\r\n.fa-trumpet:before {\r\n\tcontent: \"\\f8e3\";\r\n}\r\n.fa-usb-drive:before {\r\n\tcontent: \"\\f8e9\";\r\n}\r\n.fa-walkie-talkie:before {\r\n\tcontent: \"\\f8ef\";\r\n}\r\n.fa-waveform:before {\r\n\tcontent: \"\\f8f1\";\r\n}\r\n.fa-scanner-image:before {\r\n\tcontent: \"\\f8f3\";\r\n}\r\n.fa-air-conditioner:before {\r\n\tcontent: \"\\f8f4\";\r\n}\r\n.fa-alien:before {\r\n\tcontent: \"\\f8f5\";\r\n}\r\n.fa-alien-monster:before {\r\n\tcontent: \"\\f8f6\";\r\n}\r\n.fa-bed-alt:before {\r\n\tcontent: \"\\f8f7\";\r\n}\r\n.fa-bed-bunk:before {\r\n\tcontent: \"\\f8f8\";\r\n}\r\n.fa-bed-empty:before {\r\n\tcontent: \"\\f8f9\";\r\n}\r\n.fa-bell-on:before {\r\n\tcontent: \"\\f8fa\";\r\n}\r\n.fa-blinds:before {\r\n\tcontent: \"\\f8fb\";\r\n}\r\n.fa-blinds-raised:before {\r\n\tcontent: \"\\f8fd\";\r\n}\r\n.fa-camera-home:before {\r\n\tcontent: \"\\f8fe\";\r\n}\r\n.fa-caravan:before {\r\n\tcontent: \"\\f8ff\";\r\n}\r\n", "@import \"/src/assets/icon.css\";\r\n\r\n:root {\r\n\t--font-family: Poppins, Proxima Nova, arial, serif;\r\n    --primarycolor: #5F9EA0;\r\n    --ext-background: #F6EEEE;\r\n    --border-color: #ccc;\r\n    --white-color: #fff;\r\n    --back-light-color:#EAE2E2;\r\n    --button-border-radius: 12px !important\r\n}\r\n*:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i):not(.qadpt-jodit *):not(.mat-icon) {\r\n\tfont-family: var(--font-family) !important;\r\n}\r\nbody {\r\n\tmargin: 0;\r\n\tfont-family: var(--font-family) !important;\r\n\t-webkit-font-smoothing: antialiased;\r\n\t-moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n@font-face {\r\n\tfont-family: \"Gotham Pro\";\r\n\tfont-style: normal;\r\n\t/* src: local(\"Gotham Pro\"), local(\"Gotham Pro\"), url(\"../../../assets/fonts/GothamPro.woff2\") format(\"woff2\"),\r\n\t\turl(\"../../../assets/fonts/GothamPro.woff\") format(\"woff\"); */\r\n}\r\n\r\n@font-face {\r\n\tfont-family: \"Proxima Nova\";\r\n\tfont-style: normal;\r\n\tsrc: local(\"Proxima Nova\"), local(\"ProximaNova-Regular\"),\r\n\t\turl(\"../../../assets/fonts/ProximaNova-Regular.woff2\") format(\"woff2\"),\r\n\t\turl(\"../../../assets/fonts/ProximaNova-Regular.woff\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n\tfont-family: \"qadapt-icons\";\r\n\tsrc: url(\"../../../assets/fonts/qadapt-icons.eot?qmcsfb\");\r\n\tsrc: url(\"../../../assets/fonts/qadapt-icons.eot?qmcsfb#iefix\") format(\"embedded-opentype\"),\r\n\t\turl(\"../../../assets/fonts/qadapt-icons.ttf?qmcsfb\") format(\"truetype\"),\r\n\t\turl(\"../../../assets/fonts/qadapt-icons.woff?qmcsfb\") format(\"woff\"),\r\n\t\turl(\"../../../assets/fonts/qadapt-icons.svg?qmcsfb#qadapt-icons\") format(\"svg\");\r\n\tfont-weight: normal;\r\n\tfont-style: normal;\r\n\tfont-display: block;\r\n}\r\n/* Side menu */\r\n .side-menu .menu-list {\r\n\t list-style: none;\r\n\t padding: 0;\r\n\t margin: 0;\r\n}\r\n .side-menu .menu-list .menu-item {\r\n  padding: 6px 12px 12px 12px;\r\n  margin: 10px 0;\r\n  border-radius: 12px;\r\n  transition: background-color 0.3s ease;\r\n  cursor: pointer;\r\n  border: 1px solid #e4e4e4;\r\n  list-style-type: none !important;\r\n}\r\n.side-menu .menu-list .menu-item:hover {border: 1px solid var(--primarycolor) !important;}\r\n\r\n /* .side-menu .menu-list .menu-item .menu-content {\r\n  display: flex;\r\n  width: 100%;\r\n  flex-direction: column;\r\n} */\r\n .side-menu .menu-list .menu-item .menu-text {\r\n\t /* display: flex;\r\n\t flex-direction: column; */\r\n   font-size: 12px !important;\r\n   line-height: initial !important;\r\n   text-align: left;\r\n}\r\n .side-menu .menu-list .menu-item .menu-text .menu-title {\r\n\t font-size: 16px !important;\r\n\t font-weight: 600;\r\n\t margin-bottom: 4px;\r\n   color: #000 !important;\r\n}\r\n .side-menu .menu-list .menu-item .menu-text .menu-description {\r\n\t font-size: 12px;\r\n\t color: #9c9c9c;\r\n   line-height: initial !important;\r\n}\r\n .side-menu .menu-list .menu-item .icons {\r\n\t display: flex;\r\n\t align-items: center;\r\n   margin-right: 10px;\r\n}\r\n\r\n  .menu-item.disabled {\r\n    opacity: 0.5;\r\n    pointer-events: none;\r\n}\r\n.menu-item.active {\r\n background: rgba(95, 158, 160, 0.1);\r\n  border: 1px solid var(--primarycolor) !important;\r\n}\r\n\r\n.qadpt-gud-menupopup {\r\n  margin-left: 300px;\r\n  height: calc(100vh + 13px);\r\n  border-radius: 30px;\r\n  z-index: 99999 !important;\r\n}\r\n.qadpt-gud-menupopup .MuiPaper-root{\r\n  border-radius: 25px;\r\n}\r\n.qadpt-gud-menupopup-content {\r\n  background: var(--ext-background);\r\n  padding-bottom: 0 !important;\r\n  z-index: 9999;\r\n}\r\n.qadpt-head {\r\n  margin: 0 -20px;\r\n  padding: 12px 20px 5px 20px;\r\n}\r\n.qadpt-titsection {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n /* Ensure titsection takes up the full width */\r\n}\r\n.qadpt-extsearch {\r\n  background: var(--white-color);\r\n  border-radius: 10px;\r\n  width: 70%;\r\n}\r\n.qadpt-extsearch .MuiOutlinedInput-root {\r\n  height: 40px;\r\n  border-radius: 10px;\r\n  border: 1px solid #a8a8a8;\r\n}\r\n.qadpt-extsearch .MuiOutlinedInput-input {\r\n  /* height: inherit !important; */\r\n  border: 0 !important;\r\n  border-radius: inherit !important;\r\n  /* line-height: inherit !important; */\r\n  font-size: initial !important;\r\n}\r\n.qadpt-extsearch .MuiOutlinedInput-notchedOutline {\r\n  border-radius: 10px !important;\r\n  border: none !important;\r\n}\r\n.qadpt-right-part {\r\n  display: flex;\r\n  /* justify-content: flex-end;\r\n\r\n  width: 25%; */\r\n}\r\n.qadpt-subhead{\r\ndisplay: flex;\r\nalign-items: center;\r\n}\r\n.qadpt-subhead .title{\r\nfont-size: 18px !important;\r\n    font-weight: 600 !important;\r\n    width: 100%;}\r\n.qadpt-memberButton {\r\n  background-color: var(--primarycolor) !important;\r\n  border: none !important;\r\n  border-radius: var(--button-border-radius) !important;\r\n  color: var(--white-color) !important;\r\n  cursor: pointer !important;\r\n  font-size: 14px !important;\r\n  padding: 10px !important;\r\n  height: 40px !important;\r\n  display: flex !important;\r\n  align-items: center;\r\n  max-width: 220px;\r\n}\r\n.qadpt-memberButton i {\r\n  margin-right: 10px;\r\n}\r\n.qadpt-memberButton span {\r\n  font-size: 14px;\r\n      white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n.qadpt-memberButton svg {\r\n  margin-right: 8px;\r\n  zoom: 1.4;\r\n}\r\n\r\n.qadpt-tabs-container {\r\n  border-bottom: 1px solid #ccc;\r\n  margin-bottom: 10px;\r\n\r\n\r\n}\r\n.qadpt-memberButton.qadpt-check{\r\n  background-color: #d3d9da !important;\r\n}\r\n.qadpt-tabs-container button {\r\n text-transform: capitalize;\r\n  padding: 10px 15px;\r\n\r\n}\r\n.qadpt-tabs-container .Mui-selected {\r\n  font-weight: 600 !important;\r\n  font-size: 14px !important;\r\n  color: #000 !important;\r\n}\r\n.qadpt-tabs-container .MuiTabs-indicator {\r\n  background-color: var(--primarycolor) !important;\r\n}\r\n.qadpt-webgird{\r\n  height: calc(100vh - 220px);\r\n}\r\n.qadpt-webgird .MuiDataGrid-main {\r\n  --DataGrid-topContainerHeight: 40px !important;\r\n}\r\n.qadpt-webgird .MuiDataGrid-row {\r\n  background-color: var(--white-color);\r\n  border-radius: var(--button-border-radius);\r\n}\r\n.qadpt-webgird .MuiDataGrid-root {\r\n  border: none;\r\n}\r\n.qadpt-webgird .MuiDataGrid-columnHeaders {\r\n  background: var(--grid-head-background);\r\n  color: black;\r\n  border-right: 1px solid #f6eeee;\r\n  height: 40px !important;\r\n}\r\n.qadpt-webgird .MuiDataGrid-cell {\r\n  border-bottom: none;\r\n}\r\n.qadpt-webgird .MuiTablePagination-root .MuiSelect-select.MuiTablePagination-select {\r\n  padding-right: 24px !important;\r\n}\r\n.MuiTablePagination-root .MuiSelect-select.MuiTablePagination-select {\r\n  padding-right: 24px !important;\r\n}\r\n\r\n\r\n.qadpt-webgird .MuiDataGrid-cell:nth-child(2),\r\n.qadpt-webgird .MuiDataGrid-columnHeader:nth-child(2) {\r\n  width: 35% !important;\r\n  max-width: 35% !important;\r\n  min-width: 35%;\r\n}\r\n\r\n.qadpt-webgird .MuiDataGrid-cell:nth-child(3),\r\n.qadpt-webgird .MuiDataGrid-columnHeader:nth-child(3) {\r\n  width: 30% !important;\r\n  max-width: 30% !important;\r\n  min-width: 30%;\r\n}\r\n\r\n.qadpt-webgird .MuiDataGrid-cell:nth-child(4),\r\n.qadpt-webgird .MuiDataGrid-columnHeader:nth-child(4) {\r\n  width: 30% !important;\r\n  max-width: 30% !important;\r\n  min-width: 30%;\r\n  /* text-align: right;\r\n  right: 6px;\r\n  position: absolute; */\r\n}\r\n\r\n.qadpt-webgird .MuiDataGrid-cell button{\r\n  border: 1px solid #ccc;\r\n  border-radius: 4px;\r\n  background: #F1F2F8;\r\n  height: 30px;\r\n  margin-right: 5px;\r\n  }\r\n  /* .qadpt-webgird .qadpt-grdcont{\r\n    --DataGrid-topContainerHeight: 0px !important;\r\n   } */\r\n\r\n  .qadpt-webclonepopup .MuiPaper-root.MuiDialog-paper {\r\n    border-radius: 4px;\r\n\t\twidth: 400px;\r\n }\r\n  .qadpt-webclonepopup .qadpt-title {\r\n    font-size: 18px !important;\r\n    font-weight: 600;\r\n    padding: 15px !important;\r\n    border-bottom: 1px solid var(--border-color);\r\n\r\n }\r\n .qadpt-webclonepopup input{\r\n  padding: 5px !important;\r\n  margin-top:10px !important;\r\n }\r\n  .qadpt-webclonepopup .qadpt-close {\r\n    position: absolute !important;\r\n    top: 15px;\r\n    right: 20px;\r\n    color: #ccc;\r\n }\r\n  .qadpt-webclonepopup .qadpt-close svg {\r\n    font-size: 18px !important;\r\n }\r\n  .qadpt-webclonepopup .qadpt-subtitle {\r\n    margin-top: 20px;\r\n    font-size: 14px;\r\n }\r\n  .qadpt-webclonepopup .MuiDialogContent-root {\r\n    padding: 15px !important;\r\n    min-height: 80px;\r\n    align-content: center;\r\n }\r\n .qadpt-webclonepopup .MuiDialogActions-root  {\r\n  padding: 15px !important;\r\n  border-top: 1px solid var(--border-color);\r\n }\r\n\r\n  .qadpt-webclonepopup .MuiDialogActions-root .MuiButton-root {\r\n    background-color: var(--primarycolor);\r\n    border-radius: 4px;\r\n    line-height: var(--button-lineheight);\r\n    padding: var(--button-padding);\r\n    text-transform: capitalize !important;\r\n }\r\n\r\n\r\n /*-------------------- AI UI changes-----------------------------*/\r\n\r\n.fixed-chat-button {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  left: 220px;\r\n  z-index: 9999;\r\n}\r\n\r\n.chat-btn {\r\n  background-color: var(--primarycolor);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 50%;\r\n  width: 60px;\r\n  height: 60px;\r\n  font-size: 20px;\r\n  cursor: pointer;\r\n}\r\n\r\n.chat-btn:hover {\r\n  background-color: var(--primarycolor);\r\n}\r\n\r\n\r\n.chat-popup {\r\n  position: fixed;\r\n  bottom: 80px;\r\n  left: 20px;\r\n  background-color: white;\r\n  border: 1px solid #ccc;\r\n  padding: 15px;\r\n  border-radius: 15px;\r\n  z-index: 9999;\r\n  width: 250px;\r\n  height: 110px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: stretch;\r\n  gap: 20px;\r\n}\r\n\r\n.chat-textarea {\r\n  width: 100%;\r\n  border: 1px solid #ccc;\r\n  border-radius: 10px;\r\n  padding: 10px;\r\n  font-size: 14px;\r\n  resize: none; /* Prevent resizing */\r\n  outline: none;\r\n  box-sizing: border-box;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.chat-textarea:focus {\r\n  border-color: var(--primarycolor);\r\n  outline: none;\r\n}\r\n\r\n\r\n.Mui-error {\r\n  border-color: #e74c3c !important;\r\n}\r\n\r\n.chat-submit-btn {\r\n  background-color: var(--primarycolor);\r\n  color: white;\r\n  border: none;\r\n  padding: 10px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.chat-submit-btn:hover {\r\n  background-color:var(--primarycolor);\r\n}\r\n\r\n\r\n.chat-close-icon {\r\n  cursor: pointer;\r\n  font-size: 18px;\r\n  color: #888;\r\n  align-self: flex-end;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.chat-close-icon:hover {\r\n  color: #e74c3c;\r\n}\r\n\r\n\r\n.chat-close-icon {\r\n  position: absolute;\r\n  top: 0px;\r\n  right: 6px;\r\n  background-color: transparent;\r\n  color: #ff4d4d;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n}\r\n\r\n.chat-close-icon:hover {\r\n  color: #ff3333;\r\n}\r\n\r\n.chat-submit-btn {\r\n  background-color: var(--primarycolor);\r\n  color: white;\r\n  border: none;\r\n  padding: 4px 20px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease, transform 0.2s ease;\r\n  outline: none;\r\n}\r\n\r\n.chat-submit-btn:hover {\r\n  background-color: var(--primarycolor);\r\n  transform: scale(1.05);\r\n\r\n.chat-submit-btn:active {\r\n  background-color: var(--primarycolor);\r\n  transform: scale(0.95);\r\n}\r\n\r\n.chat-submit-btn:disabled {\r\n  background-color: #d6d6d6;\r\n  cursor: not-allowed;\r\n  opacity: 0.6;\r\n}\r\n}\r\n\r\n/* Scraping overlay styles */\r\n.scraping-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 10000;\r\n}\r\n\r\n.scraping-message {\r\n  background-color: white;\r\n  padding: 20px;\r\n  border-radius: 10px;\r\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);\r\n  text-align: center;\r\n  max-width: 300px;\r\n  width: 100%;\r\n}\r\n\r\n.scraping-spinner {\r\n  border: 4px solid #f3f3f3;\r\n  border-top: 4px solid var(--primarycolor);\r\n  border-radius: 50%;\r\n  width: 40px;\r\n  height: 40px;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 15px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.stop-scraping-button {\r\n  background-color: #e74c3c;\r\n  color: white;\r\n  border: none;\r\n  padding: 10px 20px;\r\n  border-radius: 5px;\r\n  font-weight: bold;\r\n  margin-top: 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.stop-scraping-button:hover {\r\n  background-color: #c0392b;\r\n}", ".qadpt-chat-window {\r\n    position: fixed;\r\n    left: 0;\r\n    top: 0;\r\n    width: 400px;\r\n    height: 100vh;\r\n    background-color: #fff;\r\n    display: flex;\r\n    flex-direction: column;\r\n    z-index: 9999999;\r\n    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);\r\n    border-radius: 0;\r\n  }\r\n\r\n  .qadpt-chat-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n    background-color: #fff;\r\n  }\r\n\r\n  .qadpt-chat-title {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-weight: 600;\r\n    color: #04417F;\r\n  }\r\n\r\n  .qadpt-chat-icon {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #0066cc;\r\n  }\r\n\r\n  .qadpt-chat-icon svg {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n\r\n  .qadpt-close-btn {\r\n    background: none;\r\n    border: none;\r\n    color: #999;\r\n    cursor: pointer;\r\n    padding: 5px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border-radius: 50%;\r\n    transition: background-color 0.2s;\r\n  }\r\n\r\n  .qadpt-close-btn:hover {\r\n    background-color: rgba(0, 0, 0, 0.05);\r\n  }\r\n  .qadpt-chat-container {  \r\n    height: calc(100vh - 130px); \r\n    overflow: auto;\r\n    padding: 20px;\r\n    /* padding-bottom: 20px; */\r\n  }\r\n  .qadpt-messages {\r\n   \r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: #fff;\r\n    position: relative;\r\n    min-height: 100%;\r\n    place-content: flex-end;\r\n  }\r\n  .qadpt-message {\r\n    display: flex;\r\n    max-width: 85%;\r\n    position: relative;\r\n    animation: fadeIn 0.3s ease-in-out;\r\n   /* padding: 10px 0 0 20px; */\r\n    }\r\n\r\n  @keyframes fadeIn {\r\n    from { opacity: 0; transform: translateY(10px); }\r\n    to { opacity: 1; transform: translateY(0); }\r\n  }\r\n\r\n  .user-message {\r\n    align-self: flex-end;\r\n    flex-direction: row;\r\n    /* padding-right: 20px; */\r\n    white-space: nowrap;\r\n    word-break: break-word;\r\n  }\r\n  .user-message .message-content {\r\n    background-color: #f0f0f0;\r\n    color: #333;\r\n    border-bottom-right-radius: 4px; \r\n  }\r\n  .ai-message {\r\n    align-self: flex-start;\r\n  }\r\n  .ai-message .message-content {\r\n    color: #333;\r\n    border-bottom-left-radius: 4px;\r\n  }\r\n \r\n\r\n  .ai-avatar {\r\n    width: 28px;\r\n    height: 28px;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 8px;\r\n    background-color: #f0f0f0;\r\n    flex-shrink: 0;\r\n  }\r\n  .ai-avatar svg{\r\n    display: flex;\r\n  }\r\n\r\n  .message-content {\r\n    padding: 5px 16px 12px 5px;\r\n        border-radius: 18px;\r\n    position: relative;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n    text-align: left;\r\n  }\r\n\r\n \r\n\r\n\r\n  .message-content p {\r\n    margin: 0;\r\n    white-space: pre-wrap;\r\n  }\r\n\r\n  .typing-indicator {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n    padding-left: 20px;\r\n  }\r\n\r\n  .typing-indicator span {\r\n    width: 6px;\r\n    height: 6px;\r\n    background-color: #aaa;\r\n    border-radius: 50%;\r\n    display: inline-block;\r\n    animation: typing 1.4s infinite ease-in-out both;\r\n  }\r\n\r\n  .typing-indicator span:nth-child(1) {\r\n    animation-delay: 0s;\r\n  }\r\n\r\n  .typing-indicator span:nth-child(2) {\r\n    animation-delay: 0.2s;\r\n  }\r\n\r\n  .typing-indicator span:nth-child(3) {\r\n    animation-delay: 0.4s;\r\n  }\r\n\r\n  @keyframes typing {\r\n    0%, 80%, 100% { transform: scale(0.6); opacity: 0.6; }\r\n    40% { transform: scale(1); opacity: 1; }\r\n  }\r\n\r\n  .qadpt-input {\r\n    padding: 15px;\r\n    border-top: 1px solid #f0f0f0;\r\n    background-color: white;\r\n    position: relative;\r\n  }\r\n\r\n  .error-message {\r\n    color: #d32f2f;\r\n    font-size: 14px;\r\n    margin-bottom: 10px;\r\n    padding: 8px 12px;\r\n    background-color: #ffebee;\r\n    border-radius: 4px;\r\n    border-left: 3px solid #d32f2f;\r\n  }\r\n\r\n  .qadpt-input-box {\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n  }\r\n\r\n  .input-with-icons {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    border: 1px solid #e0e0e0; \r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n    padding: 10px 12px;\r\n    border-radius: 12px;\r\n  }\r\n  .input-with-icons:focus-within {\r\n    border-color: #a0a0a0 !important;\r\n  }\r\n  .qadpt-txtcont{\r\n    width: 100%;\r\n  }\r\n  .qadpt-txtcont textarea {\r\n    width: 100%;\r\n    border-radius: 4px;\r\n    font-size: 14px;\r\n    resize: none; \r\n    min-height: 45px;\r\n    max-height: 100px;\r\n    align-content: center;\r\n    outline: none;\r\n    transition: border-color 0.3s;\r\n    font-family: inherit;\r\n    color: #333;\r\n    padding: 0 !important;\r\n    border: none !important;\r\n  }\r\n  \r\n\r\n  .input-icons {\r\n    display: flex;\r\n    align-items: center;\r\n    width: 100%;\r\n    place-content: space-between;\r\n  }\r\n  .input-icons div {\r\n    display: flex ;\r\n        gap: 8px;\r\n  }\r\n\r\n  .icon-btn {\r\n    background: none;\r\n    border: none;\r\n    padding: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n  }\r\n\r\n  .icon-btn:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n\r\n  .icon-btn span {\r\n    display: flex;\r\n  }\r\n\r\n  .mic-btn, .upload-btn {\r\n    width: 30px;\r\n    height: 30px;\r\n  }\r\n\r\n  .scroll-to-bottom {\r\n    position: fixed;\r\n    bottom: 100px;\r\n    right: 20px;\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #0066cc;\r\n    color: white;\r\n    border: none;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\r\n    z-index: 10000;\r\n    transition: all 0.2s ease;\r\n  }\r\n\r\n  .scroll-to-bottom:hover {\r\n    background-color: #0055b3;\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n  }\r\n", ".qadpt-container {\r\n  width: 100%;\r\n  left: 0;\r\n  position: fixed;\r\n  z-index: 999999 !important;\r\n  top: 0;\r\n}\r\n.qadpt-container.creation{\r\n  z-index: 99999 !important;\r\n}\r\n\r\n.qadpt-container .qadpt-box {\r\n    position: relative;\r\n    display: flex;\r\n    flex-direction: column;\r\n     background-color: #f1f1f7; \r\n    z-index: 99;\r\n    top: 55px;\r\n}\r\n.qadpt-container .qadpt-boxpre {\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n   background-color: #f1f1f7; \r\n  z-index: 99;\r\n}\r\n\r\n.qadpt-container .qadpt-box .qadpt-row {\r\n    display: flex;\r\n    position: relative;\r\n    place-content: end;\r\n    align-items: center;\r\n}\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper {\r\n  position: relative;\r\n  flex-grow: 1;\r\n  /* padding: 5px; */\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area {\r\n    height: auto;\r\n     border-radius: 5px;\r\n    font-size: 16px;\r\n   /* border: 1px solid #ccc; */\r\n    box-sizing: border-box;\r\n    width: calc(100% - 10px);\r\n}\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area textarea {\r\n    width: 100%;\r\n    height: 40px;\r\n    border-radius: 5px;\r\n    border: 0 !important;\r\n    padding: 0 !important;\r\n    resize: none;\r\n}\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-emoji-button {\r\n    position: absolute;\r\n    right: 25px;\r\n}\r\n/* .qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-delete-button {\r\n    position: absolute;\r\n    top: 10px;\r\n    left: auto;\r\n    right: -13px;\r\n}\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-delete-button svg{\r\n  height: 21px;\r\n  width: 21px;\r\n} */\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-add-btn {\r\n  height: 25px;\r\n  width: 30px;\r\n  /*margin: 10px 0;*/\r\n}\r\n\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-add-btn svg{\r\n  height: 21px;\r\n  width: 21px;\r\n}\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte h1,\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte h2,\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte h3 {\r\n  margin: 0 !important;\r\n}\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte ol,\r\n.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte ul{\r\n  margin: 0 !important;\r\n}\r\n\r\n\r\n/* .qadpt-container .qadpt-box .qadpt-add-button {\r\n    position: relative;\r\n    left: 20px;\r\n    top: 20px;\r\n} */\r\n.qadpt-container .qadpt-box .qadpt-options-menu {\r\n    position: absolute;\r\n    top: 108%;\r\n    right: 25px;\r\n    background-color: #fff;\r\n    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.2);\r\n    padding: 10px;\r\n    border-radius: 5px;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    /* width: 100px; */\r\n    z-index: 999;\r\n}\r\n.qadpt-container .qadpt-box .qadpt-options-menu .qadpt-options-content {\r\n    display: flex;\r\n    flex-direction: row;\r\n    gap: 16px;\r\n}\r\n.qadpt-container .qadpt-box .qadpt-options-menu .qadpt-options-content .qadpt-option {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    cursor: pointer;\r\n}\r\n\r\n\r\n\r\n/* start */\r\n.qadpt-imageupload {\r\n    width: 100%;\r\n    height: auto;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    overflow: hidden;\r\n}\r\n.qadpt-imageupload img {\r\n    width: 100%;\r\n    height: 40px;\r\n    object-fit: cover;\r\n}\r\n.qadpt-imageupload .upload-container {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 15px;\r\n}\r\n.qadpt-imageupload .icon-text {\r\n    display: flex;\r\n    flex-direction: row;\r\n    width: 100%;\r\n    gap : 10px\r\n}\r\n\r\n.qadpt-imageupload .icon-text h6 {\r\n    text-align: center;\r\n}\r\n.qadpt-imageupload .icon-row {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n  \r\n}\r\n.qadpt-imageupload .icon-row span {\r\n    cursor: pointer;\r\n}\r\n.qadpt-imageupload .icon-row svg{\r\n    height: 30px;\r\n    width: 30px;\r\n}\r\n.qadpt-imageupload input[type=\"file\"] {\r\n    display: none;\r\n}\r\n\r\n/* image popup */\r\n.qadpt-imagepopup {\r\n    height: 44px;\r\n    width: 500px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n    margin-top: 65px !important; /* Use caution with !important */\r\n  }\r\n  \r\n  .qadpt-imagepopup .qadpt-imagepopup-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    height: 100%;\r\n    padding: 0 10px;\r\n  \r\n  }\r\n  \r\n  .qadpt-imagepopup .qadpt-imagepopup-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 12px;\r\n    cursor: pointer;\r\n  }\r\n  \r\n  .qadpt-imagepopup .qadpt-imagepopup-text {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .qadpt-imagepopup .qadpt-imagepopup-upload {\r\n    display: none;\r\n  }\r\n  /* imagepopup end */\r\n\r\n\r\n  .qadpt-imggal {\r\n    border-radius: 10px;\r\n    padding: 10px;\r\n  \r\n  }\r\n  .qadpt-imggal .MuiDialog-container .MuiPaper-root{\r\n    width: 205px;\r\n    top: 125px;\r\n  }\r\n  .qadpt-imggal-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 10px;\r\n  }\r\n  .qadpt-imggal-backicon, .qadpt-imggal-closeicon {\r\n    color: #333;\r\n  }\r\n  .qadpt-imggal-title {\r\n    flex-grow: 1;\r\n    text-align: center;\r\n  }\r\n  .qadpt-imggal-search {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 16px 8px;\r\n  }\r\n  /* .qadpt-imggal-searchfield {\r\n    flex-grow: 1;\r\n    margin-right: 8px;\r\n  } */\r\n  /* .qadpt-imggal-searchicon, .qadpt-imggal-filtericon {\r\n    color: #666;\r\n  } */\r\n  .qadpt-imggal-images {\r\n    padding: 16px;\r\n  }\r\n  .qadpt-imggal-imageitem {\r\n    cursor: pointer;\r\n    text-align: center;\r\n  }\r\n  .qadpt-imggal-imageitem img {\r\n    width: 100%;\r\n    border-radius: 5px;\r\n  }\r\n  .qadpt-imggal-imagetitle {\r\n    font-size: 14px;\r\n    margin-top: 4px;\r\n  }\r\n  .qadpt-imggal-imagesize {\r\n    font-size: 12px;\r\n    color: #888;\r\n  }\r\n\r\n  .qadpt-prop-section{\r\n    margin-top: 10px;\r\n  }\r\n  .qadpt-prop-section .qadpt-actions{\r\n    border: none;\r\n    width: 100%;\r\n    height: 40px;\r\n    background: var(--ext-background);\r\n  }\r\n  .qadpt-prop-section button{\r\n    background: var(--ext-background);\r\n    width: 50%;\r\n    border-radius: 4px;\r\n    color: #000;\r\n    text-transform: capitalize;\r\n    border: none;\r\n  }\r\n    /* image area end */\r\n  ", ".highlight-border {\r\n  outline: 2px solid red;\r\n}\r\n\r\n.backdrop {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */\r\n  z-index: 9998; /* Below hotspot and popup */\r\n  pointer-events: auto; /* Prevent interaction with the backdrop */\r\n}\r\n\r\n.highlighted-hotspot {\r\n  position: absolute;\r\n  z-index: 9999; /* Above the backdrop */\r\n}\r\n\r\n.popup {\r\n  position: absolute;\r\n  z-index: 10000; /* Above both the backdrop and hotspot */\r\n}\r\n\r\n/* Custom cursor styles for element selection mode */\r\n.quickadapt-element-selection-mode {\r\n  cursor: crosshair !important;\r\n}\r\n\r\n.quickadapt-element-selection-mode * {\r\n  cursor: inherit !important;\r\n}\r\n\r\n/* Ensure extension UI elements don't get custom cursor */\r\n.quickadapt-no-custom-cursor,\r\n.quickadapt-no-custom-cursor * {\r\n  cursor: default !important;\r\n}\r\n"], "names": [], "sourceRoot": ""}