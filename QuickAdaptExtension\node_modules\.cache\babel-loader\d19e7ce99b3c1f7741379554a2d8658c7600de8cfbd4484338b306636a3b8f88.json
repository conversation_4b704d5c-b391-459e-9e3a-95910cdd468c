{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nvar _react = require('react');\nvar _react2 = _interopRequireDefault(_react);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _objectWithoutProperties(obj, keys) {\n  var target = {};\n  for (var i in obj) {\n    if (keys.indexOf(i) >= 0) continue;\n    if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;\n    target[i] = obj[i];\n  }\n  return target;\n}\nvar DEFAULT_SIZE = 24;\nexports.default = function (_ref) {\n  var _ref$fill = _ref.fill,\n    fill = _ref$fill === undefined ? 'currentColor' : _ref$fill,\n    _ref$width = _ref.width,\n    width = _ref$width === undefined ? DEFAULT_SIZE : _ref$width,\n    _ref$height = _ref.height,\n    height = _ref$height === undefined ? DEFAULT_SIZE : _ref$height,\n    _ref$style = _ref.style,\n    style = _ref$style === undefined ? {} : _ref$style,\n    props = _objectWithoutProperties(_ref, ['fill', 'width', 'height', 'style']);\n  return _react2.default.createElement('svg', _extends({\n    viewBox: '0 0 ' + DEFAULT_SIZE + ' ' + DEFAULT_SIZE,\n    style: _extends({\n      fill: fill,\n      width: width,\n      height: height\n    }, style)\n  }, props), _react2.default.createElement('path', {\n    d: 'M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z'\n  }));\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_extends", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "_react", "require", "_react2", "_interopRequireDefault", "obj", "__esModule", "default", "_objectWithoutProperties", "keys", "indexOf", "DEFAULT_SIZE", "_ref", "_ref$fill", "fill", "undefined", "_ref$width", "width", "_ref$height", "height", "_ref$style", "style", "props", "createElement", "viewBox", "d"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@icons/material/CheckIcon.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _react = require('react');\n\nvar _react2 = _interopRequireDefault(_react);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _objectWithoutProperties(obj, keys) { var target = {}; for (var i in obj) { if (keys.indexOf(i) >= 0) continue; if (!Object.prototype.hasOwnProperty.call(obj, i)) continue; target[i] = obj[i]; } return target; }\n\nvar DEFAULT_SIZE = 24;\n\nexports.default = function (_ref) {\n  var _ref$fill = _ref.fill,\n      fill = _ref$fill === undefined ? 'currentColor' : _ref$fill,\n      _ref$width = _ref.width,\n      width = _ref$width === undefined ? DEFAULT_SIZE : _ref$width,\n      _ref$height = _ref.height,\n      height = _ref$height === undefined ? DEFAULT_SIZE : _ref$height,\n      _ref$style = _ref.style,\n      style = _ref$style === undefined ? {} : _ref$style,\n      props = _objectWithoutProperties(_ref, ['fill', 'width', 'height', 'style']);\n\n  return _react2.default.createElement(\n    'svg',\n    _extends({\n      viewBox: '0 0 ' + DEFAULT_SIZE + ' ' + DEFAULT_SIZE,\n      style: _extends({ fill: fill, width: width, height: height }, style)\n    }, props),\n    _react2.default.createElement('path', { d: 'M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z' })\n  );\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,QAAQ,GAAGJ,MAAM,CAACK,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIV,MAAM,CAACY,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,IAAIS,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,wBAAwBA,CAACH,GAAG,EAAEI,IAAI,EAAE;EAAE,IAAIjB,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIY,GAAG,EAAE;IAAE,IAAII,IAAI,CAACC,OAAO,CAACjB,CAAC,CAAC,IAAI,CAAC,EAAE;IAAU,IAAI,CAACP,MAAM,CAACY,SAAS,CAACC,cAAc,CAACC,IAAI,CAACK,GAAG,EAAEZ,CAAC,CAAC,EAAE;IAAUD,MAAM,CAACC,CAAC,CAAC,GAAGY,GAAG,CAACZ,CAAC,CAAC;EAAE;EAAE,OAAOD,MAAM;AAAE;AAE3N,IAAImB,YAAY,GAAG,EAAE;AAErBvB,OAAO,CAACmB,OAAO,GAAG,UAAUK,IAAI,EAAE;EAChC,IAAIC,SAAS,GAAGD,IAAI,CAACE,IAAI;IACrBA,IAAI,GAAGD,SAAS,KAAKE,SAAS,GAAG,cAAc,GAAGF,SAAS;IAC3DG,UAAU,GAAGJ,IAAI,CAACK,KAAK;IACvBA,KAAK,GAAGD,UAAU,KAAKD,SAAS,GAAGJ,YAAY,GAAGK,UAAU;IAC5DE,WAAW,GAAGN,IAAI,CAACO,MAAM;IACzBA,MAAM,GAAGD,WAAW,KAAKH,SAAS,GAAGJ,YAAY,GAAGO,WAAW;IAC/DE,UAAU,GAAGR,IAAI,CAACS,KAAK;IACvBA,KAAK,GAAGD,UAAU,KAAKL,SAAS,GAAG,CAAC,CAAC,GAAGK,UAAU;IAClDE,KAAK,GAAGd,wBAAwB,CAACI,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;EAEhF,OAAOT,OAAO,CAACI,OAAO,CAACgB,aAAa,CAClC,KAAK,EACLjC,QAAQ,CAAC;IACPkC,OAAO,EAAE,MAAM,GAAGb,YAAY,GAAG,GAAG,GAAGA,YAAY;IACnDU,KAAK,EAAE/B,QAAQ,CAAC;MAAEwB,IAAI,EAAEA,IAAI;MAAEG,KAAK,EAAEA,KAAK;MAAEE,MAAM,EAAEA;IAAO,CAAC,EAAEE,KAAK;EACrE,CAAC,EAAEC,KAAK,CAAC,EACTnB,OAAO,CAACI,OAAO,CAACgB,aAAa,CAAC,MAAM,EAAE;IAAEE,CAAC,EAAE;EAA0D,CAAC,CACxG,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}