{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { findGridCellElementsFromCol, findGridElement, findLeftPinnedCellsAfterCol, findRightPinnedCellsBeforeCol, getFieldFromHeaderElem, findHeaderElementFromField, getFieldsFromGroupHeaderElem, findGroupHeaderElementsFromField, findGridHeader, findGridCells, findParentElementFromClassName, findLeftPinnedHeadersAfterCol, findRightPinnedHeadersBeforeCol, escapeOperandAttributeSelector } from \"../../../utils/domUtils.js\";\nimport { DEFAULT_GRID_AUTOSIZE_OPTIONS } from \"./gridColumnResizeApi.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiEventHandler, useGridApiMethod, useGridApiOptionHandler, useGridLogger, useGridNativeEventListener, useGridSelector, useOnMount } from \"../../utils/index.js\";\nimport { gridVirtualizationColumnEnabledSelector } from \"../virtualization/index.js\";\nimport { createControllablePromise } from \"../../../utils/createControllablePromise.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { useTimeout } from \"../../utils/useTimeout.js\";\nimport { GridPinnedColumnPosition } from \"../columns/gridColumnsInterfaces.js\";\nimport { gridColumnsStateSelector } from \"../columns/index.js\";\nfunction trackFinger(event, currentTouchId) {\n  if (currentTouchId !== undefined && event.changedTouches) {\n    for (let i = 0; i < event.changedTouches.length; i += 1) {\n      const touch = event.changedTouches[i];\n      if (touch.identifier === currentTouchId) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nfunction computeNewWidth(initialOffsetToSeparator, clickX, columnBounds, resizeDirection) {\n  let newWidth = initialOffsetToSeparator;\n  if (resizeDirection === 'Right') {\n    newWidth += clickX - columnBounds.left;\n  } else {\n    newWidth += columnBounds.right - clickX;\n  }\n  return newWidth;\n}\nfunction computeOffsetToSeparator(clickX, columnBounds, resizeDirection) {\n  if (resizeDirection === 'Left') {\n    return clickX - columnBounds.left;\n  }\n  return columnBounds.right - clickX;\n}\nfunction flipResizeDirection(side) {\n  if (side === 'Right') {\n    return 'Left';\n  }\n  return 'Right';\n}\nfunction getResizeDirection(separator, isRtl) {\n  const side = separator.classList.contains(gridClasses['columnSeparator--sideRight']) ? 'Right' : 'Left';\n  if (isRtl) {\n    // Resizing logic should be mirrored in the RTL case\n    return flipResizeDirection(side);\n  }\n  return side;\n}\nfunction preventClick(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n\n/**\n * Checker that returns a promise that resolves when the column virtualization\n * is disabled.\n */\nfunction useColumnVirtualizationDisabled(apiRef) {\n  const promise = React.useRef();\n  const selector = () => gridVirtualizationColumnEnabledSelector(apiRef);\n  const value = useGridSelector(apiRef, selector);\n  React.useEffect(() => {\n    if (promise.current && value === false) {\n      promise.current.resolve();\n      promise.current = undefined;\n    }\n  });\n  const asyncCheck = () => {\n    if (!promise.current) {\n      if (selector() === false) {\n        return Promise.resolve();\n      }\n      promise.current = createControllablePromise();\n    }\n    return promise.current;\n  };\n  return asyncCheck;\n}\n\n/**\n * Basic statistical outlier detection, checks if the value is `F * IQR` away from\n * the Q1 and Q3 boundaries. IQR: interquartile range.\n */\nfunction excludeOutliers(inputValues, factor) {\n  if (inputValues.length < 4) {\n    return inputValues;\n  }\n  const values = inputValues.slice();\n  values.sort((a, b) => a - b);\n  const q1 = values[Math.floor(values.length * 0.25)];\n  const q3 = values[Math.floor(values.length * 0.75) - 1];\n  const iqr = q3 - q1;\n\n  // We make a small adjustment if `iqr < 5` for the cases where the IQR is\n  // very small (for example zero) due to very close by values in the input data.\n  // Otherwise, with an IQR of `0`, anything outside that would be considered\n  // an outlier, but it makes more sense visually to allow for this 5px variance\n  // rather than showing a cropped cell.\n  const deviation = iqr < 5 ? 5 : iqr * factor;\n  return values.filter(v => v > q1 - deviation && v < q3 + deviation);\n}\nfunction extractColumnWidths(apiRef, options, columns) {\n  const widthByField = {};\n  const root = apiRef.current.rootElementRef.current;\n  root.classList.add(gridClasses.autosizing);\n  columns.forEach(column => {\n    const cells = findGridCells(apiRef.current, column.field);\n    const widths = cells.map(cell => {\n      return cell.getBoundingClientRect().width ?? 0;\n    });\n    const filteredWidths = options.includeOutliers ? widths : excludeOutliers(widths, options.outliersFactor);\n    if (options.includeHeaders) {\n      const header = findGridHeader(apiRef.current, column.field);\n      if (header) {\n        const title = header.querySelector(`.${gridClasses.columnHeaderTitle}`);\n        const content = header.querySelector(`.${gridClasses.columnHeaderTitleContainerContent}`);\n        const iconContainer = header.querySelector(`.${gridClasses.iconButtonContainer}`);\n        const menuContainer = header.querySelector(`.${gridClasses.menuIcon}`);\n        const element = title ?? content;\n        const style = window.getComputedStyle(header, null);\n        const paddingWidth = parseInt(style.paddingLeft, 10) + parseInt(style.paddingRight, 10);\n        const contentWidth = element.scrollWidth + 1;\n        const width = contentWidth + paddingWidth + (iconContainer?.clientWidth ?? 0) + (menuContainer?.clientWidth ?? 0);\n        filteredWidths.push(width);\n      }\n    }\n    const hasColumnMin = column.minWidth !== -Infinity && column.minWidth !== undefined;\n    const hasColumnMax = column.maxWidth !== Infinity && column.maxWidth !== undefined;\n    const min = hasColumnMin ? column.minWidth : 0;\n    const max = hasColumnMax ? column.maxWidth : Infinity;\n    const maxContent = filteredWidths.length === 0 ? 0 : Math.max(...filteredWidths);\n    widthByField[column.field] = clamp(maxContent, min, max);\n  });\n  root.classList.remove(gridClasses.autosizing);\n  return widthByField;\n}\nexport const columnResizeStateInitializer = state => _extends({}, state, {\n  columnResize: {\n    resizingColumnField: ''\n  }\n});\nfunction createResizeRefs() {\n  return {\n    colDef: undefined,\n    initialColWidth: 0,\n    initialTotalWidth: 0,\n    previousMouseClickEvent: undefined,\n    columnHeaderElement: undefined,\n    headerFilterElement: undefined,\n    groupHeaderElements: [],\n    cellElements: [],\n    leftPinnedCellsAfter: [],\n    rightPinnedCellsBefore: [],\n    fillerLeft: undefined,\n    fillerRight: undefined,\n    leftPinnedHeadersAfter: [],\n    rightPinnedHeadersBefore: []\n  };\n}\n\n/**\n * @requires useGridColumns (method, event)\n * TODO: improve experience for last column\n */\nexport const useGridColumnResize = (apiRef, props) => {\n  const isRtl = useRtl();\n  const logger = useGridLogger(apiRef, 'useGridColumnResize');\n  const refs = useLazyRef(createResizeRefs).current;\n\n  // To improve accessibility, the separator has padding on both sides.\n  // Clicking inside the padding area should be treated as a click in the separator.\n  // This ref stores the offset between the click and the separator.\n  const initialOffsetToSeparator = React.useRef();\n  const resizeDirection = React.useRef();\n  const stopResizeEventTimeout = useTimeout();\n  const touchId = React.useRef();\n  const updateWidth = newWidth => {\n    logger.debug(`Updating width to ${newWidth} for col ${refs.colDef.field}`);\n    const prevWidth = refs.columnHeaderElement.offsetWidth;\n    const widthDiff = newWidth - prevWidth;\n    const columnWidthDiff = newWidth - refs.initialColWidth;\n    const newTotalWidth = refs.initialTotalWidth + columnWidthDiff;\n    apiRef.current.rootElementRef?.current?.style.setProperty('--DataGrid-rowWidth', `${newTotalWidth}px`);\n    refs.colDef.computedWidth = newWidth;\n    refs.colDef.width = newWidth;\n    refs.colDef.flex = 0;\n    refs.columnHeaderElement.style.width = `${newWidth}px`;\n    const headerFilterElement = refs.headerFilterElement;\n    if (headerFilterElement) {\n      headerFilterElement.style.width = `${newWidth}px`;\n    }\n    refs.groupHeaderElements.forEach(element => {\n      const div = element;\n      let finalWidth;\n      if (div.getAttribute('aria-colspan') === '1') {\n        finalWidth = `${newWidth}px`;\n      } else {\n        // Cell with colspan > 1 cannot be just updated width new width.\n        // Instead, we add width diff to the current width.\n        finalWidth = `${div.offsetWidth + widthDiff}px`;\n      }\n      div.style.width = finalWidth;\n    });\n    refs.cellElements.forEach(element => {\n      const div = element;\n      let finalWidth;\n      if (div.getAttribute('aria-colspan') === '1') {\n        finalWidth = `${newWidth}px`;\n      } else {\n        // Cell with colspan > 1 cannot be just updated width new width.\n        // Instead, we add width diff to the current width.\n        finalWidth = `${div.offsetWidth + widthDiff}px`;\n      }\n      div.style.setProperty('--width', finalWidth);\n    });\n    const pinnedPosition = apiRef.current.unstable_applyPipeProcessors('isColumnPinned', false, refs.colDef.field);\n    if (pinnedPosition === GridPinnedColumnPosition.LEFT) {\n      updateProperty(refs.fillerLeft, 'width', widthDiff);\n      refs.leftPinnedCellsAfter.forEach(cell => {\n        updateProperty(cell, 'left', widthDiff);\n      });\n      refs.leftPinnedHeadersAfter.forEach(header => {\n        updateProperty(header, 'left', widthDiff);\n      });\n    }\n    if (pinnedPosition === GridPinnedColumnPosition.RIGHT) {\n      updateProperty(refs.fillerRight, 'width', widthDiff);\n      refs.rightPinnedCellsBefore.forEach(cell => {\n        updateProperty(cell, 'right', widthDiff);\n      });\n      refs.rightPinnedHeadersBefore.forEach(header => {\n        updateProperty(header, 'right', widthDiff);\n      });\n    }\n  };\n  const finishResize = nativeEvent => {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n\n    // Prevent double-clicks from being interpreted as two separate clicks\n    if (refs.previousMouseClickEvent) {\n      const prevEvent = refs.previousMouseClickEvent;\n      const prevTimeStamp = prevEvent.timeStamp;\n      const prevClientX = prevEvent.clientX;\n      const prevClientY = prevEvent.clientY;\n\n      // Check if the current event is part of a double-click\n      if (nativeEvent.timeStamp - prevTimeStamp < 300 && nativeEvent.clientX === prevClientX && nativeEvent.clientY === prevClientY) {\n        refs.previousMouseClickEvent = undefined;\n        apiRef.current.publishEvent('columnResizeStop', null, nativeEvent);\n        return;\n      }\n    }\n    if (refs.colDef) {\n      apiRef.current.setColumnWidth(refs.colDef.field, refs.colDef.width);\n      logger.debug(`Updating col ${refs.colDef.field} with new width: ${refs.colDef.width}`);\n\n      // Since during resizing we update the columns width outside of React, React is unable to\n      // reapply the right style properties. We need to sync the state manually.\n      // So we reapply the same logic as in https://github.com/mui/mui-x/blob/0511bf65543ca05d2602a5a3e0a6156f2fc8e759/packages/x-data-grid/src/hooks/features/columnHeaders/useGridColumnHeaders.tsx#L405\n      const columnsState = gridColumnsStateSelector(apiRef.current.state);\n      refs.groupHeaderElements.forEach(element => {\n        const fields = getFieldsFromGroupHeaderElem(element);\n        const div = element;\n        const newWidth = fields.reduce((acc, field) => {\n          if (columnsState.columnVisibilityModel[field] !== false) {\n            return acc + columnsState.lookup[field].computedWidth;\n          }\n          return acc;\n        }, 0);\n        const finalWidth = `${newWidth}px`;\n        div.style.width = finalWidth;\n      });\n    }\n    stopResizeEventTimeout.start(0, () => {\n      apiRef.current.publishEvent('columnResizeStop', null, nativeEvent);\n    });\n  };\n  const storeReferences = (colDef, separator, xStart) => {\n    const root = apiRef.current.rootElementRef.current;\n    refs.initialColWidth = colDef.computedWidth;\n    refs.initialTotalWidth = apiRef.current.getRootDimensions().rowWidth;\n    refs.colDef = colDef;\n    refs.columnHeaderElement = findHeaderElementFromField(apiRef.current.columnHeadersContainerRef.current, colDef.field);\n    const headerFilterElement = root.querySelector(`.${gridClasses.headerFilterRow} [data-field=\"${escapeOperandAttributeSelector(colDef.field)}\"]`);\n    if (headerFilterElement) {\n      refs.headerFilterElement = headerFilterElement;\n    }\n    refs.groupHeaderElements = findGroupHeaderElementsFromField(apiRef.current.columnHeadersContainerRef?.current, colDef.field);\n    refs.cellElements = findGridCellElementsFromCol(refs.columnHeaderElement, apiRef.current);\n    refs.fillerLeft = findGridElement(apiRef.current, isRtl ? 'filler--pinnedRight' : 'filler--pinnedLeft');\n    refs.fillerRight = findGridElement(apiRef.current, isRtl ? 'filler--pinnedLeft' : 'filler--pinnedRight');\n    const pinnedPosition = apiRef.current.unstable_applyPipeProcessors('isColumnPinned', false, refs.colDef.field);\n    refs.leftPinnedCellsAfter = pinnedPosition !== GridPinnedColumnPosition.LEFT ? [] : findLeftPinnedCellsAfterCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    refs.rightPinnedCellsBefore = pinnedPosition !== GridPinnedColumnPosition.RIGHT ? [] : findRightPinnedCellsBeforeCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    refs.leftPinnedHeadersAfter = pinnedPosition !== GridPinnedColumnPosition.LEFT ? [] : findLeftPinnedHeadersAfterCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    refs.rightPinnedHeadersBefore = pinnedPosition !== GridPinnedColumnPosition.RIGHT ? [] : findRightPinnedHeadersBeforeCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    resizeDirection.current = getResizeDirection(separator, isRtl);\n    initialOffsetToSeparator.current = computeOffsetToSeparator(xStart, refs.columnHeaderElement.getBoundingClientRect(), resizeDirection.current);\n  };\n  const handleResizeMouseUp = useEventCallback(finishResize);\n  const handleResizeMouseMove = useEventCallback(nativeEvent => {\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    if (nativeEvent.buttons === 0) {\n      handleResizeMouseUp(nativeEvent);\n      return;\n    }\n    let newWidth = computeNewWidth(initialOffsetToSeparator.current, nativeEvent.clientX, refs.columnHeaderElement.getBoundingClientRect(), resizeDirection.current);\n    newWidth = clamp(newWidth, refs.colDef.minWidth, refs.colDef.maxWidth);\n    updateWidth(newWidth);\n    const params = {\n      element: refs.columnHeaderElement,\n      colDef: refs.colDef,\n      width: newWidth\n    };\n    apiRef.current.publishEvent('columnResize', params, nativeEvent);\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId.current);\n    if (!finger) {\n      return;\n    }\n    finishResize(nativeEvent);\n  });\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId.current);\n    if (!finger) {\n      return;\n    }\n\n    // Cancel move in case some other element consumed a touchmove event and it was not fired.\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    let newWidth = computeNewWidth(initialOffsetToSeparator.current, finger.x, refs.columnHeaderElement.getBoundingClientRect(), resizeDirection.current);\n    newWidth = clamp(newWidth, refs.colDef.minWidth, refs.colDef.maxWidth);\n    updateWidth(newWidth);\n    const params = {\n      element: refs.columnHeaderElement,\n      colDef: refs.colDef,\n      width: newWidth\n    };\n    apiRef.current.publishEvent('columnResize', params, nativeEvent);\n  });\n  const handleTouchStart = useEventCallback(event => {\n    const cellSeparator = findParentElementFromClassName(event.target, gridClasses['columnSeparator--resizable']);\n    // Let the event bubble if the target is not a col separator\n    if (!cellSeparator) {\n      return;\n    }\n    const touch = event.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const columnHeaderElement = findParentElementFromClassName(event.target, gridClasses.columnHeader);\n    const field = getFieldFromHeaderElem(columnHeaderElement);\n    const colDef = apiRef.current.getColumn(field);\n    logger.debug(`Start Resize on col ${colDef.field}`);\n    apiRef.current.publishEvent('columnResizeStart', {\n      field\n    }, event);\n    storeReferences(colDef, cellSeparator, touch.clientX);\n    const doc = ownerDocument(event.currentTarget);\n    doc.addEventListener('touchmove', handleTouchMove);\n    doc.addEventListener('touchend', handleTouchEnd);\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.body.style.removeProperty('cursor');\n    doc.removeEventListener('mousemove', handleResizeMouseMove);\n    doc.removeEventListener('mouseup', handleResizeMouseUp);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n    // The click event runs right after the mouseup event, we want to wait until it\n    // has been canceled before removing our handler.\n    setTimeout(() => {\n      doc.removeEventListener('click', preventClick, true);\n    }, 100);\n    if (refs.columnHeaderElement) {\n      refs.columnHeaderElement.style.pointerEvents = 'unset';\n    }\n  }, [apiRef, refs, handleResizeMouseMove, handleResizeMouseUp, handleTouchMove, handleTouchEnd]);\n  const handleResizeStart = React.useCallback(_ref => {\n    let {\n      field\n    } = _ref;\n    apiRef.current.setState(state => _extends({}, state, {\n      columnResize: _extends({}, state.columnResize, {\n        resizingColumnField: field\n      })\n    }));\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const handleResizeStop = React.useCallback(() => {\n    apiRef.current.setState(state => _extends({}, state, {\n      columnResize: _extends({}, state.columnResize, {\n        resizingColumnField: ''\n      })\n    }));\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const handleColumnResizeMouseDown = useEventCallback((_ref2, event) => {\n    let {\n      colDef\n    } = _ref2;\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Skip if the column isn't resizable\n    if (!event.currentTarget.classList.contains(gridClasses['columnSeparator--resizable'])) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    logger.debug(`Start Resize on col ${colDef.field}`);\n    apiRef.current.publishEvent('columnResizeStart', {\n      field: colDef.field\n    }, event);\n    storeReferences(colDef, event.currentTarget, event.clientX);\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.body.style.cursor = 'col-resize';\n    refs.previousMouseClickEvent = event.nativeEvent;\n    doc.addEventListener('mousemove', handleResizeMouseMove);\n    doc.addEventListener('mouseup', handleResizeMouseUp);\n\n    // Prevent the click event if we have resized the column.\n    // Fixes https://github.com/mui/mui-x/issues/4777\n    doc.addEventListener('click', preventClick, true);\n  });\n  const handleColumnSeparatorDoubleClick = useEventCallback((params, event) => {\n    if (props.disableAutosize) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n    const column = apiRef.current.state.columns.lookup[params.field];\n    if (column.resizable === false) {\n      return;\n    }\n    apiRef.current.autosizeColumns(_extends({}, props.autosizeOptions, {\n      columns: [column.field]\n    }));\n  });\n\n  /**\n   * API METHODS\n   */\n\n  const columnVirtualizationDisabled = useColumnVirtualizationDisabled(apiRef);\n  const isAutosizingRef = React.useRef(false);\n  const autosizeColumns = React.useCallback(async userOptions => {\n    const root = apiRef.current.rootElementRef?.current;\n    if (!root) {\n      return;\n    }\n    if (isAutosizingRef.current) {\n      return;\n    }\n    isAutosizingRef.current = true;\n    const state = gridColumnsStateSelector(apiRef.current.state);\n    const options = _extends({}, DEFAULT_GRID_AUTOSIZE_OPTIONS, userOptions, {\n      columns: userOptions?.columns ?? state.orderedFields\n    });\n    options.columns = options.columns.filter(c => state.columnVisibilityModel[c] !== false);\n    const columns = options.columns.map(c => apiRef.current.state.columns.lookup[c]);\n    try {\n      apiRef.current.unstable_setColumnVirtualization(false);\n      await columnVirtualizationDisabled();\n      const widthByField = extractColumnWidths(apiRef, options, columns);\n      const newColumns = columns.map(column => _extends({}, column, {\n        width: widthByField[column.field],\n        computedWidth: widthByField[column.field]\n      }));\n      if (options.expand) {\n        const visibleColumns = state.orderedFields.map(field => state.lookup[field]).filter(c => state.columnVisibilityModel[c.field] !== false);\n        const totalWidth = visibleColumns.reduce((total, column) => total + (widthByField[column.field] ?? column.computedWidth ?? column.width), 0);\n        const availableWidth = apiRef.current.getRootDimensions().viewportInnerSize.width;\n        const remainingWidth = availableWidth - totalWidth;\n        if (remainingWidth > 0) {\n          const widthPerColumn = remainingWidth / (newColumns.length || 1);\n          newColumns.forEach(column => {\n            column.width += widthPerColumn;\n            column.computedWidth += widthPerColumn;\n          });\n        }\n      }\n      apiRef.current.updateColumns(newColumns);\n      newColumns.forEach((newColumn, index) => {\n        if (newColumn.width !== columns[index].width) {\n          const width = newColumn.width;\n          apiRef.current.publishEvent('columnWidthChange', {\n            element: apiRef.current.getColumnHeaderElement(newColumn.field),\n            colDef: newColumn,\n            width\n          });\n        }\n      });\n    } finally {\n      apiRef.current.unstable_setColumnVirtualization(true);\n      isAutosizingRef.current = false;\n    }\n  }, [apiRef, columnVirtualizationDisabled]);\n\n  /**\n   * EFFECTS\n   */\n\n  React.useEffect(() => stopListening, [stopListening]);\n  useOnMount(() => {\n    if (props.autosizeOnMount) {\n      Promise.resolve().then(() => {\n        apiRef.current.autosizeColumns(props.autosizeOptions);\n      });\n    }\n  });\n  useGridNativeEventListener(apiRef, () => apiRef.current.columnHeadersContainerRef?.current, 'touchstart', handleTouchStart, {\n    passive: true\n  });\n  useGridApiMethod(apiRef, {\n    autosizeColumns\n  }, 'public');\n  useGridApiEventHandler(apiRef, 'columnResizeStop', handleResizeStop);\n  useGridApiEventHandler(apiRef, 'columnResizeStart', handleResizeStart);\n  useGridApiEventHandler(apiRef, 'columnSeparatorMouseDown', handleColumnResizeMouseDown);\n  useGridApiEventHandler(apiRef, 'columnSeparatorDoubleClick', handleColumnSeparatorDoubleClick);\n  useGridApiOptionHandler(apiRef, 'columnResize', props.onColumnResize);\n  useGridApiOptionHandler(apiRef, 'columnWidthChange', props.onColumnWidthChange);\n};\nfunction updateProperty(element, property, delta) {\n  if (!element) {\n    return;\n  }\n  element.style[property] = `${parseInt(element.style[property], 10) + delta}px`;\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_ownerDocument", "ownerDocument", "unstable_useEventCallback", "useEventCallback", "useLazyRef", "useRtl", "findGridCellElementsFromCol", "findGridElement", "findLeftPinnedCellsAfterCol", "findRightPinnedCellsBeforeCol", "getFieldFromHeaderElem", "findHeaderElementFromField", "getFieldsFromGroupHeaderElem", "findGroupHeaderElementsFromField", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "findParentElementFromClassName", "findLeftPinnedHeadersAfterCol", "findRightPinnedHeadersBeforeCol", "escapeOperandAttributeSelector", "DEFAULT_GRID_AUTOSIZE_OPTIONS", "gridClasses", "useGridApiEventHandler", "useGridApiMethod", "useGridApiOptionHandler", "useGridLogger", "useGridNativeEventListener", "useGridSelector", "useOnMount", "gridVirtualizationColumnEnabledSelector", "createControllablePromise", "clamp", "useTimeout", "GridPinnedColumnPosition", "gridColumnsStateSelector", "trackFinger", "event", "currentTouchId", "undefined", "changedTouches", "i", "length", "touch", "identifier", "x", "clientX", "y", "clientY", "computeNewWidth", "initialOffsetToSeparator", "clickX", "columnBounds", "resizeDirection", "newWidth", "left", "right", "computeOffsetToSeparator", "flipResizeDirection", "side", "getResizeDirection", "separator", "isRtl", "classList", "contains", "preventClick", "preventDefault", "stopImmediatePropagation", "useColumnVirtualizationDisabled", "apiRef", "promise", "useRef", "selector", "value", "useEffect", "current", "resolve", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Promise", "excludeOutliers", "inputValues", "factor", "values", "slice", "sort", "a", "b", "q1", "Math", "floor", "q3", "iqr", "deviation", "filter", "v", "extractColumnWidths", "options", "columns", "widthByField", "root", "rootElementRef", "add", "autosizing", "for<PERSON>ach", "column", "cells", "field", "widths", "map", "cell", "getBoundingClientRect", "width", "filteredWidths", "includeOutliers", "outliersFactor", "includeHeaders", "header", "title", "querySelector", "columnHeaderTitle", "content", "columnHeaderTitleContainerContent", "iconContainer", "iconButtonContainer", "menuContainer", "menuIcon", "element", "style", "window", "getComputedStyle", "paddingWidth", "parseInt", "paddingLeft", "paddingRight", "contentWidth", "scrollWidth", "clientWidth", "push", "hasColumnMin", "min<PERSON><PERSON><PERSON>", "Infinity", "hasColumnMax", "max<PERSON><PERSON><PERSON>", "min", "max", "max<PERSON><PERSON><PERSON>", "remove", "columnResizeStateInitializer", "state", "columnResize", "resizingColumnField", "createResizeRefs", "colDef", "initialColWidth", "initialTotalWidth", "previousMouseClickEvent", "columnHeaderElement", "headerFilterElement", "groupHeaderElements", "cellElements", "leftPinnedCellsAfter", "rightPinnedCellsBefore", "fillerLeft", "fillerRight", "leftPinnedHeadersAfter", "rightPinnedHeadersBefore", "useGridColumnResize", "props", "logger", "refs", "stopResizeEventTimeout", "touchId", "updateWidth", "debug", "prevWidth", "offsetWidth", "widthDiff", "columnWidthDiff", "newTotalWidth", "setProperty", "computedWidth", "flex", "div", "finalWidth", "getAttribute", "pinnedPosition", "unstable_applyPipeProcessors", "LEFT", "updateProperty", "RIGHT", "finishResize", "nativeEvent", "stopListening", "prevEvent", "prevTimeStamp", "timeStamp", "prevClientX", "prevClientY", "publishEvent", "setColumn<PERSON><PERSON><PERSON>", "columnsState", "fields", "reduce", "acc", "columnVisibilityModel", "lookup", "start", "storeReferences", "xStart", "getRootDimensions", "row<PERSON>id<PERSON>", "columnHeadersContainerRef", "headerFilterRow", "handleResizeMouseUp", "handleResizeMouseMove", "buttons", "params", "handleTouchEnd", "finger", "handleTouchMove", "type", "handleTouchStart", "cellSeparator", "target", "columnHeader", "getColumn", "doc", "currentTarget", "addEventListener", "useCallback", "body", "removeProperty", "removeEventListener", "setTimeout", "pointerEvents", "handleResizeStart", "_ref", "setState", "forceUpdate", "handleResizeStop", "handleColumnResizeMouseDown", "_ref2", "button", "cursor", "handleColumnSeparatorDoubleClick", "disableAutosize", "resizable", "autosizeColumns", "autosizeOptions", "columnVirtualizationDisabled", "isAutosizingRef", "userOptions", "orderedFields", "c", "unstable_setColumnVirtualization", "newColumns", "expand", "visibleColumns", "totalWidth", "total", "availableWidth", "viewportInnerSize", "remainingWidth", "widthPerColumn", "updateColumns", "newColumn", "index", "getColumnHeaderElement", "autosizeOnMount", "then", "passive", "onColumnResize", "onColumnWidthChange", "property", "delta"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/columnResize/useGridColumnResize.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { findGridCellElementsFromCol, findGridElement, findLeftPinnedCellsAfterCol, findRightPinnedCellsBeforeCol, getFieldFromHeaderElem, findHeaderElementFromField, getFieldsFromGroupHeaderElem, findGroupHeaderElementsFromField, findGridHeader, findGridCells, findParentElementFromClassName, findLeftPinnedHeadersAfterCol, findRightPinnedHeadersBeforeCol, escapeOperandAttributeSelector } from \"../../../utils/domUtils.js\";\nimport { DEFAULT_GRID_AUTOSIZE_OPTIONS } from \"./gridColumnResizeApi.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiEventHandler, useGridApiMethod, useGridApiOptionHandler, useGridLogger, useGridNativeEventListener, useGridSelector, useOnMount } from \"../../utils/index.js\";\nimport { gridVirtualizationColumnEnabledSelector } from \"../virtualization/index.js\";\nimport { createControllablePromise } from \"../../../utils/createControllablePromise.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { useTimeout } from \"../../utils/useTimeout.js\";\nimport { GridPinnedColumnPosition } from \"../columns/gridColumnsInterfaces.js\";\nimport { gridColumnsStateSelector } from \"../columns/index.js\";\nfunction trackFinger(event, currentTouchId) {\n  if (currentTouchId !== undefined && event.changedTouches) {\n    for (let i = 0; i < event.changedTouches.length; i += 1) {\n      const touch = event.changedTouches[i];\n      if (touch.identifier === currentTouchId) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nfunction computeNewWidth(initialOffsetToSeparator, clickX, columnBounds, resizeDirection) {\n  let newWidth = initialOffsetToSeparator;\n  if (resizeDirection === 'Right') {\n    newWidth += clickX - columnBounds.left;\n  } else {\n    newWidth += columnBounds.right - clickX;\n  }\n  return newWidth;\n}\nfunction computeOffsetToSeparator(clickX, columnBounds, resizeDirection) {\n  if (resizeDirection === 'Left') {\n    return clickX - columnBounds.left;\n  }\n  return columnBounds.right - clickX;\n}\nfunction flipResizeDirection(side) {\n  if (side === 'Right') {\n    return 'Left';\n  }\n  return 'Right';\n}\nfunction getResizeDirection(separator, isRtl) {\n  const side = separator.classList.contains(gridClasses['columnSeparator--sideRight']) ? 'Right' : 'Left';\n  if (isRtl) {\n    // Resizing logic should be mirrored in the RTL case\n    return flipResizeDirection(side);\n  }\n  return side;\n}\nfunction preventClick(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n\n/**\n * Checker that returns a promise that resolves when the column virtualization\n * is disabled.\n */\nfunction useColumnVirtualizationDisabled(apiRef) {\n  const promise = React.useRef();\n  const selector = () => gridVirtualizationColumnEnabledSelector(apiRef);\n  const value = useGridSelector(apiRef, selector);\n  React.useEffect(() => {\n    if (promise.current && value === false) {\n      promise.current.resolve();\n      promise.current = undefined;\n    }\n  });\n  const asyncCheck = () => {\n    if (!promise.current) {\n      if (selector() === false) {\n        return Promise.resolve();\n      }\n      promise.current = createControllablePromise();\n    }\n    return promise.current;\n  };\n  return asyncCheck;\n}\n\n/**\n * Basic statistical outlier detection, checks if the value is `F * IQR` away from\n * the Q1 and Q3 boundaries. IQR: interquartile range.\n */\nfunction excludeOutliers(inputValues, factor) {\n  if (inputValues.length < 4) {\n    return inputValues;\n  }\n  const values = inputValues.slice();\n  values.sort((a, b) => a - b);\n  const q1 = values[Math.floor(values.length * 0.25)];\n  const q3 = values[Math.floor(values.length * 0.75) - 1];\n  const iqr = q3 - q1;\n\n  // We make a small adjustment if `iqr < 5` for the cases where the IQR is\n  // very small (for example zero) due to very close by values in the input data.\n  // Otherwise, with an IQR of `0`, anything outside that would be considered\n  // an outlier, but it makes more sense visually to allow for this 5px variance\n  // rather than showing a cropped cell.\n  const deviation = iqr < 5 ? 5 : iqr * factor;\n  return values.filter(v => v > q1 - deviation && v < q3 + deviation);\n}\nfunction extractColumnWidths(apiRef, options, columns) {\n  const widthByField = {};\n  const root = apiRef.current.rootElementRef.current;\n  root.classList.add(gridClasses.autosizing);\n  columns.forEach(column => {\n    const cells = findGridCells(apiRef.current, column.field);\n    const widths = cells.map(cell => {\n      return cell.getBoundingClientRect().width ?? 0;\n    });\n    const filteredWidths = options.includeOutliers ? widths : excludeOutliers(widths, options.outliersFactor);\n    if (options.includeHeaders) {\n      const header = findGridHeader(apiRef.current, column.field);\n      if (header) {\n        const title = header.querySelector(`.${gridClasses.columnHeaderTitle}`);\n        const content = header.querySelector(`.${gridClasses.columnHeaderTitleContainerContent}`);\n        const iconContainer = header.querySelector(`.${gridClasses.iconButtonContainer}`);\n        const menuContainer = header.querySelector(`.${gridClasses.menuIcon}`);\n        const element = title ?? content;\n        const style = window.getComputedStyle(header, null);\n        const paddingWidth = parseInt(style.paddingLeft, 10) + parseInt(style.paddingRight, 10);\n        const contentWidth = element.scrollWidth + 1;\n        const width = contentWidth + paddingWidth + (iconContainer?.clientWidth ?? 0) + (menuContainer?.clientWidth ?? 0);\n        filteredWidths.push(width);\n      }\n    }\n    const hasColumnMin = column.minWidth !== -Infinity && column.minWidth !== undefined;\n    const hasColumnMax = column.maxWidth !== Infinity && column.maxWidth !== undefined;\n    const min = hasColumnMin ? column.minWidth : 0;\n    const max = hasColumnMax ? column.maxWidth : Infinity;\n    const maxContent = filteredWidths.length === 0 ? 0 : Math.max(...filteredWidths);\n    widthByField[column.field] = clamp(maxContent, min, max);\n  });\n  root.classList.remove(gridClasses.autosizing);\n  return widthByField;\n}\nexport const columnResizeStateInitializer = state => _extends({}, state, {\n  columnResize: {\n    resizingColumnField: ''\n  }\n});\nfunction createResizeRefs() {\n  return {\n    colDef: undefined,\n    initialColWidth: 0,\n    initialTotalWidth: 0,\n    previousMouseClickEvent: undefined,\n    columnHeaderElement: undefined,\n    headerFilterElement: undefined,\n    groupHeaderElements: [],\n    cellElements: [],\n    leftPinnedCellsAfter: [],\n    rightPinnedCellsBefore: [],\n    fillerLeft: undefined,\n    fillerRight: undefined,\n    leftPinnedHeadersAfter: [],\n    rightPinnedHeadersBefore: []\n  };\n}\n\n/**\n * @requires useGridColumns (method, event)\n * TODO: improve experience for last column\n */\nexport const useGridColumnResize = (apiRef, props) => {\n  const isRtl = useRtl();\n  const logger = useGridLogger(apiRef, 'useGridColumnResize');\n  const refs = useLazyRef(createResizeRefs).current;\n\n  // To improve accessibility, the separator has padding on both sides.\n  // Clicking inside the padding area should be treated as a click in the separator.\n  // This ref stores the offset between the click and the separator.\n  const initialOffsetToSeparator = React.useRef();\n  const resizeDirection = React.useRef();\n  const stopResizeEventTimeout = useTimeout();\n  const touchId = React.useRef();\n  const updateWidth = newWidth => {\n    logger.debug(`Updating width to ${newWidth} for col ${refs.colDef.field}`);\n    const prevWidth = refs.columnHeaderElement.offsetWidth;\n    const widthDiff = newWidth - prevWidth;\n    const columnWidthDiff = newWidth - refs.initialColWidth;\n    const newTotalWidth = refs.initialTotalWidth + columnWidthDiff;\n    apiRef.current.rootElementRef?.current?.style.setProperty('--DataGrid-rowWidth', `${newTotalWidth}px`);\n    refs.colDef.computedWidth = newWidth;\n    refs.colDef.width = newWidth;\n    refs.colDef.flex = 0;\n    refs.columnHeaderElement.style.width = `${newWidth}px`;\n    const headerFilterElement = refs.headerFilterElement;\n    if (headerFilterElement) {\n      headerFilterElement.style.width = `${newWidth}px`;\n    }\n    refs.groupHeaderElements.forEach(element => {\n      const div = element;\n      let finalWidth;\n      if (div.getAttribute('aria-colspan') === '1') {\n        finalWidth = `${newWidth}px`;\n      } else {\n        // Cell with colspan > 1 cannot be just updated width new width.\n        // Instead, we add width diff to the current width.\n        finalWidth = `${div.offsetWidth + widthDiff}px`;\n      }\n      div.style.width = finalWidth;\n    });\n    refs.cellElements.forEach(element => {\n      const div = element;\n      let finalWidth;\n      if (div.getAttribute('aria-colspan') === '1') {\n        finalWidth = `${newWidth}px`;\n      } else {\n        // Cell with colspan > 1 cannot be just updated width new width.\n        // Instead, we add width diff to the current width.\n        finalWidth = `${div.offsetWidth + widthDiff}px`;\n      }\n      div.style.setProperty('--width', finalWidth);\n    });\n    const pinnedPosition = apiRef.current.unstable_applyPipeProcessors('isColumnPinned', false, refs.colDef.field);\n    if (pinnedPosition === GridPinnedColumnPosition.LEFT) {\n      updateProperty(refs.fillerLeft, 'width', widthDiff);\n      refs.leftPinnedCellsAfter.forEach(cell => {\n        updateProperty(cell, 'left', widthDiff);\n      });\n      refs.leftPinnedHeadersAfter.forEach(header => {\n        updateProperty(header, 'left', widthDiff);\n      });\n    }\n    if (pinnedPosition === GridPinnedColumnPosition.RIGHT) {\n      updateProperty(refs.fillerRight, 'width', widthDiff);\n      refs.rightPinnedCellsBefore.forEach(cell => {\n        updateProperty(cell, 'right', widthDiff);\n      });\n      refs.rightPinnedHeadersBefore.forEach(header => {\n        updateProperty(header, 'right', widthDiff);\n      });\n    }\n  };\n  const finishResize = nativeEvent => {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n\n    // Prevent double-clicks from being interpreted as two separate clicks\n    if (refs.previousMouseClickEvent) {\n      const prevEvent = refs.previousMouseClickEvent;\n      const prevTimeStamp = prevEvent.timeStamp;\n      const prevClientX = prevEvent.clientX;\n      const prevClientY = prevEvent.clientY;\n\n      // Check if the current event is part of a double-click\n      if (nativeEvent.timeStamp - prevTimeStamp < 300 && nativeEvent.clientX === prevClientX && nativeEvent.clientY === prevClientY) {\n        refs.previousMouseClickEvent = undefined;\n        apiRef.current.publishEvent('columnResizeStop', null, nativeEvent);\n        return;\n      }\n    }\n    if (refs.colDef) {\n      apiRef.current.setColumnWidth(refs.colDef.field, refs.colDef.width);\n      logger.debug(`Updating col ${refs.colDef.field} with new width: ${refs.colDef.width}`);\n\n      // Since during resizing we update the columns width outside of React, React is unable to\n      // reapply the right style properties. We need to sync the state manually.\n      // So we reapply the same logic as in https://github.com/mui/mui-x/blob/0511bf65543ca05d2602a5a3e0a6156f2fc8e759/packages/x-data-grid/src/hooks/features/columnHeaders/useGridColumnHeaders.tsx#L405\n      const columnsState = gridColumnsStateSelector(apiRef.current.state);\n      refs.groupHeaderElements.forEach(element => {\n        const fields = getFieldsFromGroupHeaderElem(element);\n        const div = element;\n        const newWidth = fields.reduce((acc, field) => {\n          if (columnsState.columnVisibilityModel[field] !== false) {\n            return acc + columnsState.lookup[field].computedWidth;\n          }\n          return acc;\n        }, 0);\n        const finalWidth = `${newWidth}px`;\n        div.style.width = finalWidth;\n      });\n    }\n    stopResizeEventTimeout.start(0, () => {\n      apiRef.current.publishEvent('columnResizeStop', null, nativeEvent);\n    });\n  };\n  const storeReferences = (colDef, separator, xStart) => {\n    const root = apiRef.current.rootElementRef.current;\n    refs.initialColWidth = colDef.computedWidth;\n    refs.initialTotalWidth = apiRef.current.getRootDimensions().rowWidth;\n    refs.colDef = colDef;\n    refs.columnHeaderElement = findHeaderElementFromField(apiRef.current.columnHeadersContainerRef.current, colDef.field);\n    const headerFilterElement = root.querySelector(`.${gridClasses.headerFilterRow} [data-field=\"${escapeOperandAttributeSelector(colDef.field)}\"]`);\n    if (headerFilterElement) {\n      refs.headerFilterElement = headerFilterElement;\n    }\n    refs.groupHeaderElements = findGroupHeaderElementsFromField(apiRef.current.columnHeadersContainerRef?.current, colDef.field);\n    refs.cellElements = findGridCellElementsFromCol(refs.columnHeaderElement, apiRef.current);\n    refs.fillerLeft = findGridElement(apiRef.current, isRtl ? 'filler--pinnedRight' : 'filler--pinnedLeft');\n    refs.fillerRight = findGridElement(apiRef.current, isRtl ? 'filler--pinnedLeft' : 'filler--pinnedRight');\n    const pinnedPosition = apiRef.current.unstable_applyPipeProcessors('isColumnPinned', false, refs.colDef.field);\n    refs.leftPinnedCellsAfter = pinnedPosition !== GridPinnedColumnPosition.LEFT ? [] : findLeftPinnedCellsAfterCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    refs.rightPinnedCellsBefore = pinnedPosition !== GridPinnedColumnPosition.RIGHT ? [] : findRightPinnedCellsBeforeCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    refs.leftPinnedHeadersAfter = pinnedPosition !== GridPinnedColumnPosition.LEFT ? [] : findLeftPinnedHeadersAfterCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    refs.rightPinnedHeadersBefore = pinnedPosition !== GridPinnedColumnPosition.RIGHT ? [] : findRightPinnedHeadersBeforeCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    resizeDirection.current = getResizeDirection(separator, isRtl);\n    initialOffsetToSeparator.current = computeOffsetToSeparator(xStart, refs.columnHeaderElement.getBoundingClientRect(), resizeDirection.current);\n  };\n  const handleResizeMouseUp = useEventCallback(finishResize);\n  const handleResizeMouseMove = useEventCallback(nativeEvent => {\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    if (nativeEvent.buttons === 0) {\n      handleResizeMouseUp(nativeEvent);\n      return;\n    }\n    let newWidth = computeNewWidth(initialOffsetToSeparator.current, nativeEvent.clientX, refs.columnHeaderElement.getBoundingClientRect(), resizeDirection.current);\n    newWidth = clamp(newWidth, refs.colDef.minWidth, refs.colDef.maxWidth);\n    updateWidth(newWidth);\n    const params = {\n      element: refs.columnHeaderElement,\n      colDef: refs.colDef,\n      width: newWidth\n    };\n    apiRef.current.publishEvent('columnResize', params, nativeEvent);\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId.current);\n    if (!finger) {\n      return;\n    }\n    finishResize(nativeEvent);\n  });\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId.current);\n    if (!finger) {\n      return;\n    }\n\n    // Cancel move in case some other element consumed a touchmove event and it was not fired.\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    let newWidth = computeNewWidth(initialOffsetToSeparator.current, finger.x, refs.columnHeaderElement.getBoundingClientRect(), resizeDirection.current);\n    newWidth = clamp(newWidth, refs.colDef.minWidth, refs.colDef.maxWidth);\n    updateWidth(newWidth);\n    const params = {\n      element: refs.columnHeaderElement,\n      colDef: refs.colDef,\n      width: newWidth\n    };\n    apiRef.current.publishEvent('columnResize', params, nativeEvent);\n  });\n  const handleTouchStart = useEventCallback(event => {\n    const cellSeparator = findParentElementFromClassName(event.target, gridClasses['columnSeparator--resizable']);\n    // Let the event bubble if the target is not a col separator\n    if (!cellSeparator) {\n      return;\n    }\n    const touch = event.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const columnHeaderElement = findParentElementFromClassName(event.target, gridClasses.columnHeader);\n    const field = getFieldFromHeaderElem(columnHeaderElement);\n    const colDef = apiRef.current.getColumn(field);\n    logger.debug(`Start Resize on col ${colDef.field}`);\n    apiRef.current.publishEvent('columnResizeStart', {\n      field\n    }, event);\n    storeReferences(colDef, cellSeparator, touch.clientX);\n    const doc = ownerDocument(event.currentTarget);\n    doc.addEventListener('touchmove', handleTouchMove);\n    doc.addEventListener('touchend', handleTouchEnd);\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.body.style.removeProperty('cursor');\n    doc.removeEventListener('mousemove', handleResizeMouseMove);\n    doc.removeEventListener('mouseup', handleResizeMouseUp);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n    // The click event runs right after the mouseup event, we want to wait until it\n    // has been canceled before removing our handler.\n    setTimeout(() => {\n      doc.removeEventListener('click', preventClick, true);\n    }, 100);\n    if (refs.columnHeaderElement) {\n      refs.columnHeaderElement.style.pointerEvents = 'unset';\n    }\n  }, [apiRef, refs, handleResizeMouseMove, handleResizeMouseUp, handleTouchMove, handleTouchEnd]);\n  const handleResizeStart = React.useCallback(({\n    field\n  }) => {\n    apiRef.current.setState(state => _extends({}, state, {\n      columnResize: _extends({}, state.columnResize, {\n        resizingColumnField: field\n      })\n    }));\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const handleResizeStop = React.useCallback(() => {\n    apiRef.current.setState(state => _extends({}, state, {\n      columnResize: _extends({}, state.columnResize, {\n        resizingColumnField: ''\n      })\n    }));\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const handleColumnResizeMouseDown = useEventCallback(({\n    colDef\n  }, event) => {\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Skip if the column isn't resizable\n    if (!event.currentTarget.classList.contains(gridClasses['columnSeparator--resizable'])) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    logger.debug(`Start Resize on col ${colDef.field}`);\n    apiRef.current.publishEvent('columnResizeStart', {\n      field: colDef.field\n    }, event);\n    storeReferences(colDef, event.currentTarget, event.clientX);\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.body.style.cursor = 'col-resize';\n    refs.previousMouseClickEvent = event.nativeEvent;\n    doc.addEventListener('mousemove', handleResizeMouseMove);\n    doc.addEventListener('mouseup', handleResizeMouseUp);\n\n    // Prevent the click event if we have resized the column.\n    // Fixes https://github.com/mui/mui-x/issues/4777\n    doc.addEventListener('click', preventClick, true);\n  });\n  const handleColumnSeparatorDoubleClick = useEventCallback((params, event) => {\n    if (props.disableAutosize) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n    const column = apiRef.current.state.columns.lookup[params.field];\n    if (column.resizable === false) {\n      return;\n    }\n    apiRef.current.autosizeColumns(_extends({}, props.autosizeOptions, {\n      columns: [column.field]\n    }));\n  });\n\n  /**\n   * API METHODS\n   */\n\n  const columnVirtualizationDisabled = useColumnVirtualizationDisabled(apiRef);\n  const isAutosizingRef = React.useRef(false);\n  const autosizeColumns = React.useCallback(async userOptions => {\n    const root = apiRef.current.rootElementRef?.current;\n    if (!root) {\n      return;\n    }\n    if (isAutosizingRef.current) {\n      return;\n    }\n    isAutosizingRef.current = true;\n    const state = gridColumnsStateSelector(apiRef.current.state);\n    const options = _extends({}, DEFAULT_GRID_AUTOSIZE_OPTIONS, userOptions, {\n      columns: userOptions?.columns ?? state.orderedFields\n    });\n    options.columns = options.columns.filter(c => state.columnVisibilityModel[c] !== false);\n    const columns = options.columns.map(c => apiRef.current.state.columns.lookup[c]);\n    try {\n      apiRef.current.unstable_setColumnVirtualization(false);\n      await columnVirtualizationDisabled();\n      const widthByField = extractColumnWidths(apiRef, options, columns);\n      const newColumns = columns.map(column => _extends({}, column, {\n        width: widthByField[column.field],\n        computedWidth: widthByField[column.field]\n      }));\n      if (options.expand) {\n        const visibleColumns = state.orderedFields.map(field => state.lookup[field]).filter(c => state.columnVisibilityModel[c.field] !== false);\n        const totalWidth = visibleColumns.reduce((total, column) => total + (widthByField[column.field] ?? column.computedWidth ?? column.width), 0);\n        const availableWidth = apiRef.current.getRootDimensions().viewportInnerSize.width;\n        const remainingWidth = availableWidth - totalWidth;\n        if (remainingWidth > 0) {\n          const widthPerColumn = remainingWidth / (newColumns.length || 1);\n          newColumns.forEach(column => {\n            column.width += widthPerColumn;\n            column.computedWidth += widthPerColumn;\n          });\n        }\n      }\n      apiRef.current.updateColumns(newColumns);\n      newColumns.forEach((newColumn, index) => {\n        if (newColumn.width !== columns[index].width) {\n          const width = newColumn.width;\n          apiRef.current.publishEvent('columnWidthChange', {\n            element: apiRef.current.getColumnHeaderElement(newColumn.field),\n            colDef: newColumn,\n            width\n          });\n        }\n      });\n    } finally {\n      apiRef.current.unstable_setColumnVirtualization(true);\n      isAutosizingRef.current = false;\n    }\n  }, [apiRef, columnVirtualizationDisabled]);\n\n  /**\n   * EFFECTS\n   */\n\n  React.useEffect(() => stopListening, [stopListening]);\n  useOnMount(() => {\n    if (props.autosizeOnMount) {\n      Promise.resolve().then(() => {\n        apiRef.current.autosizeColumns(props.autosizeOptions);\n      });\n    }\n  });\n  useGridNativeEventListener(apiRef, () => apiRef.current.columnHeadersContainerRef?.current, 'touchstart', handleTouchStart, {\n    passive: true\n  });\n  useGridApiMethod(apiRef, {\n    autosizeColumns\n  }, 'public');\n  useGridApiEventHandler(apiRef, 'columnResizeStop', handleResizeStop);\n  useGridApiEventHandler(apiRef, 'columnResizeStart', handleResizeStart);\n  useGridApiEventHandler(apiRef, 'columnSeparatorMouseDown', handleColumnResizeMouseDown);\n  useGridApiEventHandler(apiRef, 'columnSeparatorDoubleClick', handleColumnSeparatorDoubleClick);\n  useGridApiOptionHandler(apiRef, 'columnResize', props.onColumnResize);\n  useGridApiOptionHandler(apiRef, 'columnWidthChange', props.onColumnWidthChange);\n};\nfunction updateProperty(element, property, delta) {\n  if (!element) {\n    return;\n  }\n  element.style[property] = `${parseInt(element.style[property], 10) + delta}px`;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AACnH,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,2BAA2B,EAAEC,eAAe,EAAEC,2BAA2B,EAAEC,6BAA6B,EAAEC,sBAAsB,EAAEC,0BAA0B,EAAEC,4BAA4B,EAAEC,gCAAgC,EAAEC,cAAc,EAAEC,aAAa,EAAEC,8BAA8B,EAAEC,6BAA6B,EAAEC,+BAA+B,EAAEC,8BAA8B,QAAQ,4BAA4B;AACxa,SAASC,6BAA6B,QAAQ,0BAA0B;AACxE,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,sBAAsB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,aAAa,EAAEC,0BAA0B,EAAEC,eAAe,EAAEC,UAAU,QAAQ,sBAAsB;AAChL,SAASC,uCAAuC,QAAQ,4BAA4B;AACpF,SAASC,yBAAyB,QAAQ,6CAA6C;AACvF,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,wBAAwB,QAAQ,qCAAqC;AAC9E,SAASC,wBAAwB,QAAQ,qBAAqB;AAC9D,SAASC,WAAWA,CAACC,KAAK,EAAEC,cAAc,EAAE;EAC1C,IAAIA,cAAc,KAAKC,SAAS,IAAIF,KAAK,CAACG,cAAc,EAAE;IACxD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACG,cAAc,CAACE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACvD,MAAME,KAAK,GAAGN,KAAK,CAACG,cAAc,CAACC,CAAC,CAAC;MACrC,IAAIE,KAAK,CAACC,UAAU,KAAKN,cAAc,EAAE;QACvC,OAAO;UACLO,CAAC,EAAEF,KAAK,CAACG,OAAO;UAChBC,CAAC,EAAEJ,KAAK,CAACK;QACX,CAAC;MACH;IACF;IACA,OAAO,KAAK;EACd;EACA,OAAO;IACLH,CAAC,EAAER,KAAK,CAACS,OAAO;IAChBC,CAAC,EAAEV,KAAK,CAACW;EACX,CAAC;AACH;AACA,SAASC,eAAeA,CAACC,wBAAwB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,eAAe,EAAE;EACxF,IAAIC,QAAQ,GAAGJ,wBAAwB;EACvC,IAAIG,eAAe,KAAK,OAAO,EAAE;IAC/BC,QAAQ,IAAIH,MAAM,GAAGC,YAAY,CAACG,IAAI;EACxC,CAAC,MAAM;IACLD,QAAQ,IAAIF,YAAY,CAACI,KAAK,GAAGL,MAAM;EACzC;EACA,OAAOG,QAAQ;AACjB;AACA,SAASG,wBAAwBA,CAACN,MAAM,EAAEC,YAAY,EAAEC,eAAe,EAAE;EACvE,IAAIA,eAAe,KAAK,MAAM,EAAE;IAC9B,OAAOF,MAAM,GAAGC,YAAY,CAACG,IAAI;EACnC;EACA,OAAOH,YAAY,CAACI,KAAK,GAAGL,MAAM;AACpC;AACA,SAASO,mBAAmBA,CAACC,IAAI,EAAE;EACjC,IAAIA,IAAI,KAAK,OAAO,EAAE;IACpB,OAAO,MAAM;EACf;EACA,OAAO,OAAO;AAChB;AACA,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,KAAK,EAAE;EAC5C,MAAMH,IAAI,GAAGE,SAAS,CAACE,SAAS,CAACC,QAAQ,CAAC1C,WAAW,CAAC,4BAA4B,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM;EACvG,IAAIwC,KAAK,EAAE;IACT;IACA,OAAOJ,mBAAmB,CAACC,IAAI,CAAC;EAClC;EACA,OAAOA,IAAI;AACb;AACA,SAASM,YAAYA,CAAC5B,KAAK,EAAE;EAC3BA,KAAK,CAAC6B,cAAc,CAAC,CAAC;EACtB7B,KAAK,CAAC8B,wBAAwB,CAAC,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAACC,MAAM,EAAE;EAC/C,MAAMC,OAAO,GAAGtE,KAAK,CAACuE,MAAM,CAAC,CAAC;EAC9B,MAAMC,QAAQ,GAAGA,CAAA,KAAM1C,uCAAuC,CAACuC,MAAM,CAAC;EACtE,MAAMI,KAAK,GAAG7C,eAAe,CAACyC,MAAM,EAAEG,QAAQ,CAAC;EAC/CxE,KAAK,CAAC0E,SAAS,CAAC,MAAM;IACpB,IAAIJ,OAAO,CAACK,OAAO,IAAIF,KAAK,KAAK,KAAK,EAAE;MACtCH,OAAO,CAACK,OAAO,CAACC,OAAO,CAAC,CAAC;MACzBN,OAAO,CAACK,OAAO,GAAGpC,SAAS;IAC7B;EACF,CAAC,CAAC;EACF,MAAMsC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACP,OAAO,CAACK,OAAO,EAAE;MACpB,IAAIH,QAAQ,CAAC,CAAC,KAAK,KAAK,EAAE;QACxB,OAAOM,OAAO,CAACF,OAAO,CAAC,CAAC;MAC1B;MACAN,OAAO,CAACK,OAAO,GAAG5C,yBAAyB,CAAC,CAAC;IAC/C;IACA,OAAOuC,OAAO,CAACK,OAAO;EACxB,CAAC;EACD,OAAOE,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA,SAASE,eAAeA,CAACC,WAAW,EAAEC,MAAM,EAAE;EAC5C,IAAID,WAAW,CAACtC,MAAM,GAAG,CAAC,EAAE;IAC1B,OAAOsC,WAAW;EACpB;EACA,MAAME,MAAM,GAAGF,WAAW,CAACG,KAAK,CAAC,CAAC;EAClCD,MAAM,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EAC5B,MAAMC,EAAE,GAAGL,MAAM,CAACM,IAAI,CAACC,KAAK,CAACP,MAAM,CAACxC,MAAM,GAAG,IAAI,CAAC,CAAC;EACnD,MAAMgD,EAAE,GAAGR,MAAM,CAACM,IAAI,CAACC,KAAK,CAACP,MAAM,CAACxC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;EACvD,MAAMiD,GAAG,GAAGD,EAAE,GAAGH,EAAE;;EAEnB;EACA;EACA;EACA;EACA;EACA,MAAMK,SAAS,GAAGD,GAAG,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG,GAAGV,MAAM;EAC5C,OAAOC,MAAM,CAACW,MAAM,CAACC,CAAC,IAAIA,CAAC,GAAGP,EAAE,GAAGK,SAAS,IAAIE,CAAC,GAAGJ,EAAE,GAAGE,SAAS,CAAC;AACrE;AACA,SAASG,mBAAmBA,CAAC1B,MAAM,EAAE2B,OAAO,EAAEC,OAAO,EAAE;EACrD,MAAMC,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,IAAI,GAAG9B,MAAM,CAACM,OAAO,CAACyB,cAAc,CAACzB,OAAO;EAClDwB,IAAI,CAACpC,SAAS,CAACsC,GAAG,CAAC/E,WAAW,CAACgF,UAAU,CAAC;EAC1CL,OAAO,CAACM,OAAO,CAACC,MAAM,IAAI;IACxB,MAAMC,KAAK,GAAGzF,aAAa,CAACqD,MAAM,CAACM,OAAO,EAAE6B,MAAM,CAACE,KAAK,CAAC;IACzD,MAAMC,MAAM,GAAGF,KAAK,CAACG,GAAG,CAACC,IAAI,IAAI;MAC/B,OAAOA,IAAI,CAACC,qBAAqB,CAAC,CAAC,CAACC,KAAK,IAAI,CAAC;IAChD,CAAC,CAAC;IACF,MAAMC,cAAc,GAAGhB,OAAO,CAACiB,eAAe,GAAGN,MAAM,GAAG5B,eAAe,CAAC4B,MAAM,EAAEX,OAAO,CAACkB,cAAc,CAAC;IACzG,IAAIlB,OAAO,CAACmB,cAAc,EAAE;MAC1B,MAAMC,MAAM,GAAGrG,cAAc,CAACsD,MAAM,CAACM,OAAO,EAAE6B,MAAM,CAACE,KAAK,CAAC;MAC3D,IAAIU,MAAM,EAAE;QACV,MAAMC,KAAK,GAAGD,MAAM,CAACE,aAAa,CAAC,IAAIhG,WAAW,CAACiG,iBAAiB,EAAE,CAAC;QACvE,MAAMC,OAAO,GAAGJ,MAAM,CAACE,aAAa,CAAC,IAAIhG,WAAW,CAACmG,iCAAiC,EAAE,CAAC;QACzF,MAAMC,aAAa,GAAGN,MAAM,CAACE,aAAa,CAAC,IAAIhG,WAAW,CAACqG,mBAAmB,EAAE,CAAC;QACjF,MAAMC,aAAa,GAAGR,MAAM,CAACE,aAAa,CAAC,IAAIhG,WAAW,CAACuG,QAAQ,EAAE,CAAC;QACtE,MAAMC,OAAO,GAAGT,KAAK,IAAIG,OAAO;QAChC,MAAMO,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACb,MAAM,EAAE,IAAI,CAAC;QACnD,MAAMc,YAAY,GAAGC,QAAQ,CAACJ,KAAK,CAACK,WAAW,EAAE,EAAE,CAAC,GAAGD,QAAQ,CAACJ,KAAK,CAACM,YAAY,EAAE,EAAE,CAAC;QACvF,MAAMC,YAAY,GAAGR,OAAO,CAACS,WAAW,GAAG,CAAC;QAC5C,MAAMxB,KAAK,GAAGuB,YAAY,GAAGJ,YAAY,IAAIR,aAAa,EAAEc,WAAW,IAAI,CAAC,CAAC,IAAIZ,aAAa,EAAEY,WAAW,IAAI,CAAC,CAAC;QACjHxB,cAAc,CAACyB,IAAI,CAAC1B,KAAK,CAAC;MAC5B;IACF;IACA,MAAM2B,YAAY,GAAGlC,MAAM,CAACmC,QAAQ,KAAK,CAACC,QAAQ,IAAIpC,MAAM,CAACmC,QAAQ,KAAKpG,SAAS;IACnF,MAAMsG,YAAY,GAAGrC,MAAM,CAACsC,QAAQ,KAAKF,QAAQ,IAAIpC,MAAM,CAACsC,QAAQ,KAAKvG,SAAS;IAClF,MAAMwG,GAAG,GAAGL,YAAY,GAAGlC,MAAM,CAACmC,QAAQ,GAAG,CAAC;IAC9C,MAAMK,GAAG,GAAGH,YAAY,GAAGrC,MAAM,CAACsC,QAAQ,GAAGF,QAAQ;IACrD,MAAMK,UAAU,GAAGjC,cAAc,CAACtE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG8C,IAAI,CAACwD,GAAG,CAAC,GAAGhC,cAAc,CAAC;IAChFd,YAAY,CAACM,MAAM,CAACE,KAAK,CAAC,GAAG1E,KAAK,CAACiH,UAAU,EAAEF,GAAG,EAAEC,GAAG,CAAC;EAC1D,CAAC,CAAC;EACF7C,IAAI,CAACpC,SAAS,CAACmF,MAAM,CAAC5H,WAAW,CAACgF,UAAU,CAAC;EAC7C,OAAOJ,YAAY;AACrB;AACA,OAAO,MAAMiD,4BAA4B,GAAGC,KAAK,IAAIrJ,QAAQ,CAAC,CAAC,CAAC,EAAEqJ,KAAK,EAAE;EACvEC,YAAY,EAAE;IACZC,mBAAmB,EAAE;EACvB;AACF,CAAC,CAAC;AACF,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,OAAO;IACLC,MAAM,EAAEjH,SAAS;IACjBkH,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE,CAAC;IACpBC,uBAAuB,EAAEpH,SAAS;IAClCqH,mBAAmB,EAAErH,SAAS;IAC9BsH,mBAAmB,EAAEtH,SAAS;IAC9BuH,mBAAmB,EAAE,EAAE;IACvBC,YAAY,EAAE,EAAE;IAChBC,oBAAoB,EAAE,EAAE;IACxBC,sBAAsB,EAAE,EAAE;IAC1BC,UAAU,EAAE3H,SAAS;IACrB4H,WAAW,EAAE5H,SAAS;IACtB6H,sBAAsB,EAAE,EAAE;IAC1BC,wBAAwB,EAAE;EAC5B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAACjG,MAAM,EAAEkG,KAAK,KAAK;EACpD,MAAMzG,KAAK,GAAGxD,MAAM,CAAC,CAAC;EACtB,MAAMkK,MAAM,GAAG9I,aAAa,CAAC2C,MAAM,EAAE,qBAAqB,CAAC;EAC3D,MAAMoG,IAAI,GAAGpK,UAAU,CAACkJ,gBAAgB,CAAC,CAAC5E,OAAO;;EAEjD;EACA;EACA;EACA,MAAMzB,wBAAwB,GAAGlD,KAAK,CAACuE,MAAM,CAAC,CAAC;EAC/C,MAAMlB,eAAe,GAAGrD,KAAK,CAACuE,MAAM,CAAC,CAAC;EACtC,MAAMmG,sBAAsB,GAAGzI,UAAU,CAAC,CAAC;EAC3C,MAAM0I,OAAO,GAAG3K,KAAK,CAACuE,MAAM,CAAC,CAAC;EAC9B,MAAMqG,WAAW,GAAGtH,QAAQ,IAAI;IAC9BkH,MAAM,CAACK,KAAK,CAAC,qBAAqBvH,QAAQ,YAAYmH,IAAI,CAACjB,MAAM,CAAC9C,KAAK,EAAE,CAAC;IAC1E,MAAMoE,SAAS,GAAGL,IAAI,CAACb,mBAAmB,CAACmB,WAAW;IACtD,MAAMC,SAAS,GAAG1H,QAAQ,GAAGwH,SAAS;IACtC,MAAMG,eAAe,GAAG3H,QAAQ,GAAGmH,IAAI,CAAChB,eAAe;IACvD,MAAMyB,aAAa,GAAGT,IAAI,CAACf,iBAAiB,GAAGuB,eAAe;IAC9D5G,MAAM,CAACM,OAAO,CAACyB,cAAc,EAAEzB,OAAO,EAAEoD,KAAK,CAACoD,WAAW,CAAC,qBAAqB,EAAE,GAAGD,aAAa,IAAI,CAAC;IACtGT,IAAI,CAACjB,MAAM,CAAC4B,aAAa,GAAG9H,QAAQ;IACpCmH,IAAI,CAACjB,MAAM,CAACzC,KAAK,GAAGzD,QAAQ;IAC5BmH,IAAI,CAACjB,MAAM,CAAC6B,IAAI,GAAG,CAAC;IACpBZ,IAAI,CAACb,mBAAmB,CAAC7B,KAAK,CAAChB,KAAK,GAAG,GAAGzD,QAAQ,IAAI;IACtD,MAAMuG,mBAAmB,GAAGY,IAAI,CAACZ,mBAAmB;IACpD,IAAIA,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC9B,KAAK,CAAChB,KAAK,GAAG,GAAGzD,QAAQ,IAAI;IACnD;IACAmH,IAAI,CAACX,mBAAmB,CAACvD,OAAO,CAACuB,OAAO,IAAI;MAC1C,MAAMwD,GAAG,GAAGxD,OAAO;MACnB,IAAIyD,UAAU;MACd,IAAID,GAAG,CAACE,YAAY,CAAC,cAAc,CAAC,KAAK,GAAG,EAAE;QAC5CD,UAAU,GAAG,GAAGjI,QAAQ,IAAI;MAC9B,CAAC,MAAM;QACL;QACA;QACAiI,UAAU,GAAG,GAAGD,GAAG,CAACP,WAAW,GAAGC,SAAS,IAAI;MACjD;MACAM,GAAG,CAACvD,KAAK,CAAChB,KAAK,GAAGwE,UAAU;IAC9B,CAAC,CAAC;IACFd,IAAI,CAACV,YAAY,CAACxD,OAAO,CAACuB,OAAO,IAAI;MACnC,MAAMwD,GAAG,GAAGxD,OAAO;MACnB,IAAIyD,UAAU;MACd,IAAID,GAAG,CAACE,YAAY,CAAC,cAAc,CAAC,KAAK,GAAG,EAAE;QAC5CD,UAAU,GAAG,GAAGjI,QAAQ,IAAI;MAC9B,CAAC,MAAM;QACL;QACA;QACAiI,UAAU,GAAG,GAAGD,GAAG,CAACP,WAAW,GAAGC,SAAS,IAAI;MACjD;MACAM,GAAG,CAACvD,KAAK,CAACoD,WAAW,CAAC,SAAS,EAAEI,UAAU,CAAC;IAC9C,CAAC,CAAC;IACF,MAAME,cAAc,GAAGpH,MAAM,CAACM,OAAO,CAAC+G,4BAA4B,CAAC,gBAAgB,EAAE,KAAK,EAAEjB,IAAI,CAACjB,MAAM,CAAC9C,KAAK,CAAC;IAC9G,IAAI+E,cAAc,KAAKvJ,wBAAwB,CAACyJ,IAAI,EAAE;MACpDC,cAAc,CAACnB,IAAI,CAACP,UAAU,EAAE,OAAO,EAAEc,SAAS,CAAC;MACnDP,IAAI,CAACT,oBAAoB,CAACzD,OAAO,CAACM,IAAI,IAAI;QACxC+E,cAAc,CAAC/E,IAAI,EAAE,MAAM,EAAEmE,SAAS,CAAC;MACzC,CAAC,CAAC;MACFP,IAAI,CAACL,sBAAsB,CAAC7D,OAAO,CAACa,MAAM,IAAI;QAC5CwE,cAAc,CAACxE,MAAM,EAAE,MAAM,EAAE4D,SAAS,CAAC;MAC3C,CAAC,CAAC;IACJ;IACA,IAAIS,cAAc,KAAKvJ,wBAAwB,CAAC2J,KAAK,EAAE;MACrDD,cAAc,CAACnB,IAAI,CAACN,WAAW,EAAE,OAAO,EAAEa,SAAS,CAAC;MACpDP,IAAI,CAACR,sBAAsB,CAAC1D,OAAO,CAACM,IAAI,IAAI;QAC1C+E,cAAc,CAAC/E,IAAI,EAAE,OAAO,EAAEmE,SAAS,CAAC;MAC1C,CAAC,CAAC;MACFP,IAAI,CAACJ,wBAAwB,CAAC9D,OAAO,CAACa,MAAM,IAAI;QAC9CwE,cAAc,CAACxE,MAAM,EAAE,OAAO,EAAE4D,SAAS,CAAC;MAC5C,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAMc,YAAY,GAAGC,WAAW,IAAI;IAClC;IACAC,aAAa,CAAC,CAAC;;IAEf;IACA,IAAIvB,IAAI,CAACd,uBAAuB,EAAE;MAChC,MAAMsC,SAAS,GAAGxB,IAAI,CAACd,uBAAuB;MAC9C,MAAMuC,aAAa,GAAGD,SAAS,CAACE,SAAS;MACzC,MAAMC,WAAW,GAAGH,SAAS,CAACnJ,OAAO;MACrC,MAAMuJ,WAAW,GAAGJ,SAAS,CAACjJ,OAAO;;MAErC;MACA,IAAI+I,WAAW,CAACI,SAAS,GAAGD,aAAa,GAAG,GAAG,IAAIH,WAAW,CAACjJ,OAAO,KAAKsJ,WAAW,IAAIL,WAAW,CAAC/I,OAAO,KAAKqJ,WAAW,EAAE;QAC7H5B,IAAI,CAACd,uBAAuB,GAAGpH,SAAS;QACxC8B,MAAM,CAACM,OAAO,CAAC2H,YAAY,CAAC,kBAAkB,EAAE,IAAI,EAAEP,WAAW,CAAC;QAClE;MACF;IACF;IACA,IAAItB,IAAI,CAACjB,MAAM,EAAE;MACfnF,MAAM,CAACM,OAAO,CAAC4H,cAAc,CAAC9B,IAAI,CAACjB,MAAM,CAAC9C,KAAK,EAAE+D,IAAI,CAACjB,MAAM,CAACzC,KAAK,CAAC;MACnEyD,MAAM,CAACK,KAAK,CAAC,gBAAgBJ,IAAI,CAACjB,MAAM,CAAC9C,KAAK,oBAAoB+D,IAAI,CAACjB,MAAM,CAACzC,KAAK,EAAE,CAAC;;MAEtF;MACA;MACA;MACA,MAAMyF,YAAY,GAAGrK,wBAAwB,CAACkC,MAAM,CAACM,OAAO,CAACyE,KAAK,CAAC;MACnEqB,IAAI,CAACX,mBAAmB,CAACvD,OAAO,CAACuB,OAAO,IAAI;QAC1C,MAAM2E,MAAM,GAAG5L,4BAA4B,CAACiH,OAAO,CAAC;QACpD,MAAMwD,GAAG,GAAGxD,OAAO;QACnB,MAAMxE,QAAQ,GAAGmJ,MAAM,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEjG,KAAK,KAAK;UAC7C,IAAI8F,YAAY,CAACI,qBAAqB,CAAClG,KAAK,CAAC,KAAK,KAAK,EAAE;YACvD,OAAOiG,GAAG,GAAGH,YAAY,CAACK,MAAM,CAACnG,KAAK,CAAC,CAAC0E,aAAa;UACvD;UACA,OAAOuB,GAAG;QACZ,CAAC,EAAE,CAAC,CAAC;QACL,MAAMpB,UAAU,GAAG,GAAGjI,QAAQ,IAAI;QAClCgI,GAAG,CAACvD,KAAK,CAAChB,KAAK,GAAGwE,UAAU;MAC9B,CAAC,CAAC;IACJ;IACAb,sBAAsB,CAACoC,KAAK,CAAC,CAAC,EAAE,MAAM;MACpCzI,MAAM,CAACM,OAAO,CAAC2H,YAAY,CAAC,kBAAkB,EAAE,IAAI,EAAEP,WAAW,CAAC;IACpE,CAAC,CAAC;EACJ,CAAC;EACD,MAAMgB,eAAe,GAAGA,CAACvD,MAAM,EAAE3F,SAAS,EAAEmJ,MAAM,KAAK;IACrD,MAAM7G,IAAI,GAAG9B,MAAM,CAACM,OAAO,CAACyB,cAAc,CAACzB,OAAO;IAClD8F,IAAI,CAAChB,eAAe,GAAGD,MAAM,CAAC4B,aAAa;IAC3CX,IAAI,CAACf,iBAAiB,GAAGrF,MAAM,CAACM,OAAO,CAACsI,iBAAiB,CAAC,CAAC,CAACC,QAAQ;IACpEzC,IAAI,CAACjB,MAAM,GAAGA,MAAM;IACpBiB,IAAI,CAACb,mBAAmB,GAAGhJ,0BAA0B,CAACyD,MAAM,CAACM,OAAO,CAACwI,yBAAyB,CAACxI,OAAO,EAAE6E,MAAM,CAAC9C,KAAK,CAAC;IACrH,MAAMmD,mBAAmB,GAAG1D,IAAI,CAACmB,aAAa,CAAC,IAAIhG,WAAW,CAAC8L,eAAe,iBAAiBhM,8BAA8B,CAACoI,MAAM,CAAC9C,KAAK,CAAC,IAAI,CAAC;IAChJ,IAAImD,mBAAmB,EAAE;MACvBY,IAAI,CAACZ,mBAAmB,GAAGA,mBAAmB;IAChD;IACAY,IAAI,CAACX,mBAAmB,GAAGhJ,gCAAgC,CAACuD,MAAM,CAACM,OAAO,CAACwI,yBAAyB,EAAExI,OAAO,EAAE6E,MAAM,CAAC9C,KAAK,CAAC;IAC5H+D,IAAI,CAACV,YAAY,GAAGxJ,2BAA2B,CAACkK,IAAI,CAACb,mBAAmB,EAAEvF,MAAM,CAACM,OAAO,CAAC;IACzF8F,IAAI,CAACP,UAAU,GAAG1J,eAAe,CAAC6D,MAAM,CAACM,OAAO,EAAEb,KAAK,GAAG,qBAAqB,GAAG,oBAAoB,CAAC;IACvG2G,IAAI,CAACN,WAAW,GAAG3J,eAAe,CAAC6D,MAAM,CAACM,OAAO,EAAEb,KAAK,GAAG,oBAAoB,GAAG,qBAAqB,CAAC;IACxG,MAAM2H,cAAc,GAAGpH,MAAM,CAACM,OAAO,CAAC+G,4BAA4B,CAAC,gBAAgB,EAAE,KAAK,EAAEjB,IAAI,CAACjB,MAAM,CAAC9C,KAAK,CAAC;IAC9G+D,IAAI,CAACT,oBAAoB,GAAGyB,cAAc,KAAKvJ,wBAAwB,CAACyJ,IAAI,GAAG,EAAE,GAAGlL,2BAA2B,CAAC4D,MAAM,CAACM,OAAO,EAAE8F,IAAI,CAACb,mBAAmB,EAAE9F,KAAK,CAAC;IAChK2G,IAAI,CAACR,sBAAsB,GAAGwB,cAAc,KAAKvJ,wBAAwB,CAAC2J,KAAK,GAAG,EAAE,GAAGnL,6BAA6B,CAAC2D,MAAM,CAACM,OAAO,EAAE8F,IAAI,CAACb,mBAAmB,EAAE9F,KAAK,CAAC;IACrK2G,IAAI,CAACL,sBAAsB,GAAGqB,cAAc,KAAKvJ,wBAAwB,CAACyJ,IAAI,GAAG,EAAE,GAAGzK,6BAA6B,CAACmD,MAAM,CAACM,OAAO,EAAE8F,IAAI,CAACb,mBAAmB,EAAE9F,KAAK,CAAC;IACpK2G,IAAI,CAACJ,wBAAwB,GAAGoB,cAAc,KAAKvJ,wBAAwB,CAAC2J,KAAK,GAAG,EAAE,GAAG1K,+BAA+B,CAACkD,MAAM,CAACM,OAAO,EAAE8F,IAAI,CAACb,mBAAmB,EAAE9F,KAAK,CAAC;IACzKT,eAAe,CAACsB,OAAO,GAAGf,kBAAkB,CAACC,SAAS,EAAEC,KAAK,CAAC;IAC9DZ,wBAAwB,CAACyB,OAAO,GAAGlB,wBAAwB,CAACuJ,MAAM,EAAEvC,IAAI,CAACb,mBAAmB,CAAC9C,qBAAqB,CAAC,CAAC,EAAEzD,eAAe,CAACsB,OAAO,CAAC;EAChJ,CAAC;EACD,MAAM0I,mBAAmB,GAAGjN,gBAAgB,CAAC0L,YAAY,CAAC;EAC1D,MAAMwB,qBAAqB,GAAGlN,gBAAgB,CAAC2L,WAAW,IAAI;IAC5D;IACA,IAAIA,WAAW,CAACwB,OAAO,KAAK,CAAC,EAAE;MAC7BF,mBAAmB,CAACtB,WAAW,CAAC;MAChC;IACF;IACA,IAAIzI,QAAQ,GAAGL,eAAe,CAACC,wBAAwB,CAACyB,OAAO,EAAEoH,WAAW,CAACjJ,OAAO,EAAE2H,IAAI,CAACb,mBAAmB,CAAC9C,qBAAqB,CAAC,CAAC,EAAEzD,eAAe,CAACsB,OAAO,CAAC;IAChKrB,QAAQ,GAAGtB,KAAK,CAACsB,QAAQ,EAAEmH,IAAI,CAACjB,MAAM,CAACb,QAAQ,EAAE8B,IAAI,CAACjB,MAAM,CAACV,QAAQ,CAAC;IACtE8B,WAAW,CAACtH,QAAQ,CAAC;IACrB,MAAMkK,MAAM,GAAG;MACb1F,OAAO,EAAE2C,IAAI,CAACb,mBAAmB;MACjCJ,MAAM,EAAEiB,IAAI,CAACjB,MAAM;MACnBzC,KAAK,EAAEzD;IACT,CAAC;IACDe,MAAM,CAACM,OAAO,CAAC2H,YAAY,CAAC,cAAc,EAAEkB,MAAM,EAAEzB,WAAW,CAAC;EAClE,CAAC,CAAC;EACF,MAAM0B,cAAc,GAAGrN,gBAAgB,CAAC2L,WAAW,IAAI;IACrD,MAAM2B,MAAM,GAAGtL,WAAW,CAAC2J,WAAW,EAAEpB,OAAO,CAAChG,OAAO,CAAC;IACxD,IAAI,CAAC+I,MAAM,EAAE;MACX;IACF;IACA5B,YAAY,CAACC,WAAW,CAAC;EAC3B,CAAC,CAAC;EACF,MAAM4B,eAAe,GAAGvN,gBAAgB,CAAC2L,WAAW,IAAI;IACtD,MAAM2B,MAAM,GAAGtL,WAAW,CAAC2J,WAAW,EAAEpB,OAAO,CAAChG,OAAO,CAAC;IACxD,IAAI,CAAC+I,MAAM,EAAE;MACX;IACF;;IAEA;IACA,IAAI3B,WAAW,CAAC6B,IAAI,KAAK,WAAW,IAAI7B,WAAW,CAACwB,OAAO,KAAK,CAAC,EAAE;MACjEE,cAAc,CAAC1B,WAAW,CAAC;MAC3B;IACF;IACA,IAAIzI,QAAQ,GAAGL,eAAe,CAACC,wBAAwB,CAACyB,OAAO,EAAE+I,MAAM,CAAC7K,CAAC,EAAE4H,IAAI,CAACb,mBAAmB,CAAC9C,qBAAqB,CAAC,CAAC,EAAEzD,eAAe,CAACsB,OAAO,CAAC;IACrJrB,QAAQ,GAAGtB,KAAK,CAACsB,QAAQ,EAAEmH,IAAI,CAACjB,MAAM,CAACb,QAAQ,EAAE8B,IAAI,CAACjB,MAAM,CAACV,QAAQ,CAAC;IACtE8B,WAAW,CAACtH,QAAQ,CAAC;IACrB,MAAMkK,MAAM,GAAG;MACb1F,OAAO,EAAE2C,IAAI,CAACb,mBAAmB;MACjCJ,MAAM,EAAEiB,IAAI,CAACjB,MAAM;MACnBzC,KAAK,EAAEzD;IACT,CAAC;IACDe,MAAM,CAACM,OAAO,CAAC2H,YAAY,CAAC,cAAc,EAAEkB,MAAM,EAAEzB,WAAW,CAAC;EAClE,CAAC,CAAC;EACF,MAAM8B,gBAAgB,GAAGzN,gBAAgB,CAACiC,KAAK,IAAI;IACjD,MAAMyL,aAAa,GAAG7M,8BAA8B,CAACoB,KAAK,CAAC0L,MAAM,EAAEzM,WAAW,CAAC,4BAA4B,CAAC,CAAC;IAC7G;IACA,IAAI,CAACwM,aAAa,EAAE;MAClB;IACF;IACA,MAAMnL,KAAK,GAAGN,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC;IACrC,IAAIG,KAAK,IAAI,IAAI,EAAE;MACjB;MACAgI,OAAO,CAAChG,OAAO,GAAGhC,KAAK,CAACC,UAAU;IACpC;IACA,MAAMgH,mBAAmB,GAAG3I,8BAA8B,CAACoB,KAAK,CAAC0L,MAAM,EAAEzM,WAAW,CAAC0M,YAAY,CAAC;IAClG,MAAMtH,KAAK,GAAG/F,sBAAsB,CAACiJ,mBAAmB,CAAC;IACzD,MAAMJ,MAAM,GAAGnF,MAAM,CAACM,OAAO,CAACsJ,SAAS,CAACvH,KAAK,CAAC;IAC9C8D,MAAM,CAACK,KAAK,CAAC,uBAAuBrB,MAAM,CAAC9C,KAAK,EAAE,CAAC;IACnDrC,MAAM,CAACM,OAAO,CAAC2H,YAAY,CAAC,mBAAmB,EAAE;MAC/C5F;IACF,CAAC,EAAErE,KAAK,CAAC;IACT0K,eAAe,CAACvD,MAAM,EAAEsE,aAAa,EAAEnL,KAAK,CAACG,OAAO,CAAC;IACrD,MAAMoL,GAAG,GAAGhO,aAAa,CAACmC,KAAK,CAAC8L,aAAa,CAAC;IAC9CD,GAAG,CAACE,gBAAgB,CAAC,WAAW,EAAET,eAAe,CAAC;IAClDO,GAAG,CAACE,gBAAgB,CAAC,UAAU,EAAEX,cAAc,CAAC;EAClD,CAAC,CAAC;EACF,MAAMzB,aAAa,GAAGhM,KAAK,CAACqO,WAAW,CAAC,MAAM;IAC5C,MAAMH,GAAG,GAAGhO,aAAa,CAACmE,MAAM,CAACM,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAAC;IAChEuJ,GAAG,CAACI,IAAI,CAACvG,KAAK,CAACwG,cAAc,CAAC,QAAQ,CAAC;IACvCL,GAAG,CAACM,mBAAmB,CAAC,WAAW,EAAElB,qBAAqB,CAAC;IAC3DY,GAAG,CAACM,mBAAmB,CAAC,SAAS,EAAEnB,mBAAmB,CAAC;IACvDa,GAAG,CAACM,mBAAmB,CAAC,WAAW,EAAEb,eAAe,CAAC;IACrDO,GAAG,CAACM,mBAAmB,CAAC,UAAU,EAAEf,cAAc,CAAC;IACnD;IACA;IACAgB,UAAU,CAAC,MAAM;MACfP,GAAG,CAACM,mBAAmB,CAAC,OAAO,EAAEvK,YAAY,EAAE,IAAI,CAAC;IACtD,CAAC,EAAE,GAAG,CAAC;IACP,IAAIwG,IAAI,CAACb,mBAAmB,EAAE;MAC5Ba,IAAI,CAACb,mBAAmB,CAAC7B,KAAK,CAAC2G,aAAa,GAAG,OAAO;IACxD;EACF,CAAC,EAAE,CAACrK,MAAM,EAAEoG,IAAI,EAAE6C,qBAAqB,EAAED,mBAAmB,EAAEM,eAAe,EAAEF,cAAc,CAAC,CAAC;EAC/F,MAAMkB,iBAAiB,GAAG3O,KAAK,CAACqO,WAAW,CAACO,IAAA,IAEtC;IAAA,IAFuC;MAC3ClI;IACF,CAAC,GAAAkI,IAAA;IACCvK,MAAM,CAACM,OAAO,CAACkK,QAAQ,CAACzF,KAAK,IAAIrJ,QAAQ,CAAC,CAAC,CAAC,EAAEqJ,KAAK,EAAE;MACnDC,YAAY,EAAEtJ,QAAQ,CAAC,CAAC,CAAC,EAAEqJ,KAAK,CAACC,YAAY,EAAE;QAC7CC,mBAAmB,EAAE5C;MACvB,CAAC;IACH,CAAC,CAAC,CAAC;IACHrC,MAAM,CAACM,OAAO,CAACmK,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACzK,MAAM,CAAC,CAAC;EACZ,MAAM0K,gBAAgB,GAAG/O,KAAK,CAACqO,WAAW,CAAC,MAAM;IAC/ChK,MAAM,CAACM,OAAO,CAACkK,QAAQ,CAACzF,KAAK,IAAIrJ,QAAQ,CAAC,CAAC,CAAC,EAAEqJ,KAAK,EAAE;MACnDC,YAAY,EAAEtJ,QAAQ,CAAC,CAAC,CAAC,EAAEqJ,KAAK,CAACC,YAAY,EAAE;QAC7CC,mBAAmB,EAAE;MACvB,CAAC;IACH,CAAC,CAAC,CAAC;IACHjF,MAAM,CAACM,OAAO,CAACmK,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACzK,MAAM,CAAC,CAAC;EACZ,MAAM2K,2BAA2B,GAAG5O,gBAAgB,CAAC,CAAA6O,KAAA,EAElD5M,KAAK,KAAK;IAAA,IAFyC;MACpDmH;IACF,CAAC,GAAAyF,KAAA;IACC;IACA,IAAI5M,KAAK,CAAC6M,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;;IAEA;IACA,IAAI,CAAC7M,KAAK,CAAC8L,aAAa,CAACpK,SAAS,CAACC,QAAQ,CAAC1C,WAAW,CAAC,4BAA4B,CAAC,CAAC,EAAE;MACtF;IACF;;IAEA;IACAe,KAAK,CAAC6B,cAAc,CAAC,CAAC;IACtBsG,MAAM,CAACK,KAAK,CAAC,uBAAuBrB,MAAM,CAAC9C,KAAK,EAAE,CAAC;IACnDrC,MAAM,CAACM,OAAO,CAAC2H,YAAY,CAAC,mBAAmB,EAAE;MAC/C5F,KAAK,EAAE8C,MAAM,CAAC9C;IAChB,CAAC,EAAErE,KAAK,CAAC;IACT0K,eAAe,CAACvD,MAAM,EAAEnH,KAAK,CAAC8L,aAAa,EAAE9L,KAAK,CAACS,OAAO,CAAC;IAC3D,MAAMoL,GAAG,GAAGhO,aAAa,CAACmE,MAAM,CAACM,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAAC;IAChEuJ,GAAG,CAACI,IAAI,CAACvG,KAAK,CAACoH,MAAM,GAAG,YAAY;IACpC1E,IAAI,CAACd,uBAAuB,GAAGtH,KAAK,CAAC0J,WAAW;IAChDmC,GAAG,CAACE,gBAAgB,CAAC,WAAW,EAAEd,qBAAqB,CAAC;IACxDY,GAAG,CAACE,gBAAgB,CAAC,SAAS,EAAEf,mBAAmB,CAAC;;IAEpD;IACA;IACAa,GAAG,CAACE,gBAAgB,CAAC,OAAO,EAAEnK,YAAY,EAAE,IAAI,CAAC;EACnD,CAAC,CAAC;EACF,MAAMmL,gCAAgC,GAAGhP,gBAAgB,CAAC,CAACoN,MAAM,EAAEnL,KAAK,KAAK;IAC3E,IAAIkI,KAAK,CAAC8E,eAAe,EAAE;MACzB;IACF;;IAEA;IACA,IAAIhN,KAAK,CAAC6M,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;IACA,MAAM1I,MAAM,GAAGnC,MAAM,CAACM,OAAO,CAACyE,KAAK,CAACnD,OAAO,CAAC4G,MAAM,CAACW,MAAM,CAAC9G,KAAK,CAAC;IAChE,IAAIF,MAAM,CAAC8I,SAAS,KAAK,KAAK,EAAE;MAC9B;IACF;IACAjL,MAAM,CAACM,OAAO,CAAC4K,eAAe,CAACxP,QAAQ,CAAC,CAAC,CAAC,EAAEwK,KAAK,CAACiF,eAAe,EAAE;MACjEvJ,OAAO,EAAE,CAACO,MAAM,CAACE,KAAK;IACxB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;;EAEF;AACF;AACA;;EAEE,MAAM+I,4BAA4B,GAAGrL,+BAA+B,CAACC,MAAM,CAAC;EAC5E,MAAMqL,eAAe,GAAG1P,KAAK,CAACuE,MAAM,CAAC,KAAK,CAAC;EAC3C,MAAMgL,eAAe,GAAGvP,KAAK,CAACqO,WAAW,CAAC,MAAMsB,WAAW,IAAI;IAC7D,MAAMxJ,IAAI,GAAG9B,MAAM,CAACM,OAAO,CAACyB,cAAc,EAAEzB,OAAO;IACnD,IAAI,CAACwB,IAAI,EAAE;MACT;IACF;IACA,IAAIuJ,eAAe,CAAC/K,OAAO,EAAE;MAC3B;IACF;IACA+K,eAAe,CAAC/K,OAAO,GAAG,IAAI;IAC9B,MAAMyE,KAAK,GAAGjH,wBAAwB,CAACkC,MAAM,CAACM,OAAO,CAACyE,KAAK,CAAC;IAC5D,MAAMpD,OAAO,GAAGjG,QAAQ,CAAC,CAAC,CAAC,EAAEsB,6BAA6B,EAAEsO,WAAW,EAAE;MACvE1J,OAAO,EAAE0J,WAAW,EAAE1J,OAAO,IAAImD,KAAK,CAACwG;IACzC,CAAC,CAAC;IACF5J,OAAO,CAACC,OAAO,GAAGD,OAAO,CAACC,OAAO,CAACJ,MAAM,CAACgK,CAAC,IAAIzG,KAAK,CAACwD,qBAAqB,CAACiD,CAAC,CAAC,KAAK,KAAK,CAAC;IACvF,MAAM5J,OAAO,GAAGD,OAAO,CAACC,OAAO,CAACW,GAAG,CAACiJ,CAAC,IAAIxL,MAAM,CAACM,OAAO,CAACyE,KAAK,CAACnD,OAAO,CAAC4G,MAAM,CAACgD,CAAC,CAAC,CAAC;IAChF,IAAI;MACFxL,MAAM,CAACM,OAAO,CAACmL,gCAAgC,CAAC,KAAK,CAAC;MACtD,MAAML,4BAA4B,CAAC,CAAC;MACpC,MAAMvJ,YAAY,GAAGH,mBAAmB,CAAC1B,MAAM,EAAE2B,OAAO,EAAEC,OAAO,CAAC;MAClE,MAAM8J,UAAU,GAAG9J,OAAO,CAACW,GAAG,CAACJ,MAAM,IAAIzG,QAAQ,CAAC,CAAC,CAAC,EAAEyG,MAAM,EAAE;QAC5DO,KAAK,EAAEb,YAAY,CAACM,MAAM,CAACE,KAAK,CAAC;QACjC0E,aAAa,EAAElF,YAAY,CAACM,MAAM,CAACE,KAAK;MAC1C,CAAC,CAAC,CAAC;MACH,IAAIV,OAAO,CAACgK,MAAM,EAAE;QAClB,MAAMC,cAAc,GAAG7G,KAAK,CAACwG,aAAa,CAAChJ,GAAG,CAACF,KAAK,IAAI0C,KAAK,CAACyD,MAAM,CAACnG,KAAK,CAAC,CAAC,CAACb,MAAM,CAACgK,CAAC,IAAIzG,KAAK,CAACwD,qBAAqB,CAACiD,CAAC,CAACnJ,KAAK,CAAC,KAAK,KAAK,CAAC;QACxI,MAAMwJ,UAAU,GAAGD,cAAc,CAACvD,MAAM,CAAC,CAACyD,KAAK,EAAE3J,MAAM,KAAK2J,KAAK,IAAIjK,YAAY,CAACM,MAAM,CAACE,KAAK,CAAC,IAAIF,MAAM,CAAC4E,aAAa,IAAI5E,MAAM,CAACO,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5I,MAAMqJ,cAAc,GAAG/L,MAAM,CAACM,OAAO,CAACsI,iBAAiB,CAAC,CAAC,CAACoD,iBAAiB,CAACtJ,KAAK;QACjF,MAAMuJ,cAAc,GAAGF,cAAc,GAAGF,UAAU;QAClD,IAAII,cAAc,GAAG,CAAC,EAAE;UACtB,MAAMC,cAAc,GAAGD,cAAc,IAAIP,UAAU,CAACrN,MAAM,IAAI,CAAC,CAAC;UAChEqN,UAAU,CAACxJ,OAAO,CAACC,MAAM,IAAI;YAC3BA,MAAM,CAACO,KAAK,IAAIwJ,cAAc;YAC9B/J,MAAM,CAAC4E,aAAa,IAAImF,cAAc;UACxC,CAAC,CAAC;QACJ;MACF;MACAlM,MAAM,CAACM,OAAO,CAAC6L,aAAa,CAACT,UAAU,CAAC;MACxCA,UAAU,CAACxJ,OAAO,CAAC,CAACkK,SAAS,EAAEC,KAAK,KAAK;QACvC,IAAID,SAAS,CAAC1J,KAAK,KAAKd,OAAO,CAACyK,KAAK,CAAC,CAAC3J,KAAK,EAAE;UAC5C,MAAMA,KAAK,GAAG0J,SAAS,CAAC1J,KAAK;UAC7B1C,MAAM,CAACM,OAAO,CAAC2H,YAAY,CAAC,mBAAmB,EAAE;YAC/CxE,OAAO,EAAEzD,MAAM,CAACM,OAAO,CAACgM,sBAAsB,CAACF,SAAS,CAAC/J,KAAK,CAAC;YAC/D8C,MAAM,EAAEiH,SAAS;YACjB1J;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,SAAS;MACR1C,MAAM,CAACM,OAAO,CAACmL,gCAAgC,CAAC,IAAI,CAAC;MACrDJ,eAAe,CAAC/K,OAAO,GAAG,KAAK;IACjC;EACF,CAAC,EAAE,CAACN,MAAM,EAAEoL,4BAA4B,CAAC,CAAC;;EAE1C;AACF;AACA;;EAEEzP,KAAK,CAAC0E,SAAS,CAAC,MAAMsH,aAAa,EAAE,CAACA,aAAa,CAAC,CAAC;EACrDnK,UAAU,CAAC,MAAM;IACf,IAAI0I,KAAK,CAACqG,eAAe,EAAE;MACzB9L,OAAO,CAACF,OAAO,CAAC,CAAC,CAACiM,IAAI,CAAC,MAAM;QAC3BxM,MAAM,CAACM,OAAO,CAAC4K,eAAe,CAAChF,KAAK,CAACiF,eAAe,CAAC;MACvD,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF7N,0BAA0B,CAAC0C,MAAM,EAAE,MAAMA,MAAM,CAACM,OAAO,CAACwI,yBAAyB,EAAExI,OAAO,EAAE,YAAY,EAAEkJ,gBAAgB,EAAE;IAC1HiD,OAAO,EAAE;EACX,CAAC,CAAC;EACFtP,gBAAgB,CAAC6C,MAAM,EAAE;IACvBkL;EACF,CAAC,EAAE,QAAQ,CAAC;EACZhO,sBAAsB,CAAC8C,MAAM,EAAE,kBAAkB,EAAE0K,gBAAgB,CAAC;EACpExN,sBAAsB,CAAC8C,MAAM,EAAE,mBAAmB,EAAEsK,iBAAiB,CAAC;EACtEpN,sBAAsB,CAAC8C,MAAM,EAAE,0BAA0B,EAAE2K,2BAA2B,CAAC;EACvFzN,sBAAsB,CAAC8C,MAAM,EAAE,4BAA4B,EAAE+K,gCAAgC,CAAC;EAC9F3N,uBAAuB,CAAC4C,MAAM,EAAE,cAAc,EAAEkG,KAAK,CAACwG,cAAc,CAAC;EACrEtP,uBAAuB,CAAC4C,MAAM,EAAE,mBAAmB,EAAEkG,KAAK,CAACyG,mBAAmB,CAAC;AACjF,CAAC;AACD,SAASpF,cAAcA,CAAC9D,OAAO,EAAEmJ,QAAQ,EAAEC,KAAK,EAAE;EAChD,IAAI,CAACpJ,OAAO,EAAE;IACZ;EACF;EACAA,OAAO,CAACC,KAAK,CAACkJ,QAAQ,CAAC,GAAG,GAAG9I,QAAQ,CAACL,OAAO,CAACC,KAAK,CAACkJ,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAGC,KAAK,IAAI;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}