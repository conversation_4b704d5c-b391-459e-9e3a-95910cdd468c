{"ast": null, "code": "export const getDefaultCellValue = colDef => {\n  switch (colDef.type) {\n    case 'boolean':\n      return false;\n    case 'date':\n    case 'dateTime':\n    case 'number':\n      return undefined;\n    case 'singleSelect':\n      return null;\n    case 'string':\n    default:\n      return '';\n  }\n};", "map": {"version": 3, "names": ["getDefaultCellValue", "colDef", "type", "undefined"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/editing/utils.js"], "sourcesContent": ["export const getDefaultCellValue = colDef => {\n  switch (colDef.type) {\n    case 'boolean':\n      return false;\n    case 'date':\n    case 'dateTime':\n    case 'number':\n      return undefined;\n    case 'singleSelect':\n      return null;\n    case 'string':\n    default:\n      return '';\n  }\n};"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,GAAGC,MAAM,IAAI;EAC3C,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,SAAS;MACZ,OAAO,KAAK;IACd,KAAK,MAAM;IACX,KAAK,UAAU;IACf,KAAK,QAAQ;MACX,OAAOC,SAAS;IAClB,KAAK,cAAc;MACjB,OAAO,IAAI;IACb,KAAK,QAAQ;IACb;MACE,OAAO,EAAE;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}