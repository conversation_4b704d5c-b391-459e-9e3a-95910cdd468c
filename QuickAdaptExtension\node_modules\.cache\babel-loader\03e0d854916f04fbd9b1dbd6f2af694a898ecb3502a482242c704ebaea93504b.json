{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = (props, overflowedContent) => {\n  const {\n    classes\n  } = props;\n  const slots = {\n    root: ['virtualScrollerContent', overflowedContent && 'virtualScrollerContent--overflowed']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerContentRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScrollerContent',\n  overridesResolver: (props, styles) => styles.virtualScrollerContent\n})({});\nconst GridVirtualScrollerContent = /*#__PURE__*/React.forwardRef(function GridVirtualScrollerContent(props, ref) {\n  const rootProps = useGridRootProps();\n  const overflowedContent = !rootProps.autoHeight && props.style?.minHeight === 'auto';\n  const classes = useUtilityClasses(rootProps, overflowedContent);\n  return /*#__PURE__*/_jsx(VirtualScrollerContentRoot, _extends({\n    ref: ref\n  }, props, {\n    ownerState: rootProps,\n    className: clsx(classes.root, props.className)\n  }));\n});\nexport { GridVirtualScrollerContent };", "map": {"version": 3, "names": ["_extends", "React", "clsx", "styled", "composeClasses", "useGridRootProps", "getDataGridUtilityClass", "jsx", "_jsx", "useUtilityClasses", "props", "overflowedContent", "classes", "slots", "root", "VirtualScrollerContentRoot", "name", "slot", "overridesResolver", "styles", "virtualScrollerContent", "GridVirtualScrollerContent", "forwardRef", "ref", "rootProps", "autoHeight", "style", "minHeight", "ownerState", "className"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/virtualization/GridVirtualScrollerContent.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = (props, overflowedContent) => {\n  const {\n    classes\n  } = props;\n  const slots = {\n    root: ['virtualScrollerContent', overflowedContent && 'virtualScrollerContent--overflowed']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerContentRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScrollerContent',\n  overridesResolver: (props, styles) => styles.virtualScrollerContent\n})({});\nconst GridVirtualScrollerContent = /*#__PURE__*/React.forwardRef(function GridVirtualScrollerContent(props, ref) {\n  const rootProps = useGridRootProps();\n  const overflowedContent = !rootProps.autoHeight && props.style?.minHeight === 'auto';\n  const classes = useUtilityClasses(rootProps, overflowedContent);\n  return /*#__PURE__*/_jsx(VirtualScrollerContentRoot, _extends({\n    ref: ref\n  }, props, {\n    ownerState: rootProps,\n    className: clsx(classes.root, props.className)\n  }));\n});\nexport { GridVirtualScrollerContent };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,iBAAiB,KAAK;EACtD,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,wBAAwB,EAAEH,iBAAiB,IAAI,oCAAoC;EAC5F,CAAC;EACD,OAAOP,cAAc,CAACS,KAAK,EAAEP,uBAAuB,EAAEM,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,0BAA0B,GAAGZ,MAAM,CAAC,KAAK,EAAE;EAC/Ca,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,wBAAwB;EAC9BC,iBAAiB,EAAEA,CAACR,KAAK,EAAES,MAAM,KAAKA,MAAM,CAACC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,MAAMC,0BAA0B,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAAC,SAASD,0BAA0BA,CAACX,KAAK,EAAEa,GAAG,EAAE;EAC/G,MAAMC,SAAS,GAAGnB,gBAAgB,CAAC,CAAC;EACpC,MAAMM,iBAAiB,GAAG,CAACa,SAAS,CAACC,UAAU,IAAIf,KAAK,CAACgB,KAAK,EAAEC,SAAS,KAAK,MAAM;EACpF,MAAMf,OAAO,GAAGH,iBAAiB,CAACe,SAAS,EAAEb,iBAAiB,CAAC;EAC/D,OAAO,aAAaH,IAAI,CAACO,0BAA0B,EAAEf,QAAQ,CAAC;IAC5DuB,GAAG,EAAEA;EACP,CAAC,EAAEb,KAAK,EAAE;IACRkB,UAAU,EAAEJ,SAAS;IACrBK,SAAS,EAAE3B,IAAI,CAACU,OAAO,CAACE,IAAI,EAAEJ,KAAK,CAACmB,SAAS;EAC/C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,SAASR,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}