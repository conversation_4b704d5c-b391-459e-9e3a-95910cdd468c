{"ast": null, "code": "import * as React from 'react';\nexport const GridApiContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  GridApiContext.displayName = 'GridApiContext';\n}", "map": {"version": 3, "names": ["React", "GridApiContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/GridApiContext.js"], "sourcesContent": ["import * as React from 'react';\nexport const GridApiContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  GridApiContext.displayName = 'GridApiContext';\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,cAAc,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AACzE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,cAAc,CAACM,WAAW,GAAG,gBAAgB;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}