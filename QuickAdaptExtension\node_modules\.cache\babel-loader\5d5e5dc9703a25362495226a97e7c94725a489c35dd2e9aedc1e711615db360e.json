{"ast": null, "code": "import { GridPinnedColumnPosition } from \"../hooks/features/columns/gridColumnsInterfaces.js\";\nexport const shouldCellShowRightBorder = (pinnedPosition, indexInSection, sectionLength, showCellVerticalBorderRootProp, gridHasFiller) => {\n  const isSectionLastCell = indexInSection === sectionLength - 1;\n  if (pinnedPosition === GridPinnedColumnPosition.LEFT && isSectionLastCell) {\n    return true;\n  }\n  if (showCellVerticalBorderRootProp) {\n    if (pinnedPosition === GridPinnedColumnPosition.LEFT) {\n      return true;\n    }\n    if (pinnedPosition === GridPinnedColumnPosition.RIGHT) {\n      return !isSectionLastCell;\n    }\n    // pinnedPosition === undefined, middle section\n    return !isSectionLastCell || gridHasFiller;\n  }\n  return false;\n};\nexport const shouldCellShowLeftBorder = (pinnedPosition, indexInSection) => {\n  return pinnedPosition === GridPinnedColumnPosition.RIGHT && indexInSection === 0;\n};", "map": {"version": 3, "names": ["GridPinnedColumnPosition", "shouldCellShowRightBorder", "pinnedPosition", "indexInSection", "sectionLength", "showCellVerticalBorderRootProp", "gridHasFiller", "isSectionLastCell", "LEFT", "RIGHT", "shouldCellShowLeftBorder"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/utils/cellBorderUtils.js"], "sourcesContent": ["import { GridPinnedColumnPosition } from \"../hooks/features/columns/gridColumnsInterfaces.js\";\nexport const shouldCellShowRightBorder = (pinnedPosition, indexInSection, sectionLength, showCellVerticalBorderRootProp, gridHasFiller) => {\n  const isSectionLastCell = indexInSection === sectionLength - 1;\n  if (pinnedPosition === GridPinnedColumnPosition.LEFT && isSectionLastCell) {\n    return true;\n  }\n  if (showCellVerticalBorderRootProp) {\n    if (pinnedPosition === GridPinnedColumnPosition.LEFT) {\n      return true;\n    }\n    if (pinnedPosition === GridPinnedColumnPosition.RIGHT) {\n      return !isSectionLastCell;\n    }\n    // pinnedPosition === undefined, middle section\n    return !isSectionLastCell || gridHasFiller;\n  }\n  return false;\n};\nexport const shouldCellShowLeftBorder = (pinnedPosition, indexInSection) => {\n  return pinnedPosition === GridPinnedColumnPosition.RIGHT && indexInSection === 0;\n};"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,oDAAoD;AAC7F,OAAO,MAAMC,yBAAyB,GAAGA,CAACC,cAAc,EAAEC,cAAc,EAAEC,aAAa,EAAEC,8BAA8B,EAAEC,aAAa,KAAK;EACzI,MAAMC,iBAAiB,GAAGJ,cAAc,KAAKC,aAAa,GAAG,CAAC;EAC9D,IAAIF,cAAc,KAAKF,wBAAwB,CAACQ,IAAI,IAAID,iBAAiB,EAAE;IACzE,OAAO,IAAI;EACb;EACA,IAAIF,8BAA8B,EAAE;IAClC,IAAIH,cAAc,KAAKF,wBAAwB,CAACQ,IAAI,EAAE;MACpD,OAAO,IAAI;IACb;IACA,IAAIN,cAAc,KAAKF,wBAAwB,CAACS,KAAK,EAAE;MACrD,OAAO,CAACF,iBAAiB;IAC3B;IACA;IACA,OAAO,CAACA,iBAAiB,IAAID,aAAa;EAC5C;EACA,OAAO,KAAK;AACd,CAAC;AACD,OAAO,MAAMI,wBAAwB,GAAGA,CAACR,cAAc,EAAEC,cAAc,KAAK;EAC1E,OAAOD,cAAc,KAAKF,wBAAwB,CAACS,KAAK,IAAIN,cAAc,KAAK,CAAC;AAClF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}