{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nexport const EMPTY_RENDER_CONTEXT = {\n  firstRowIndex: 0,\n  lastRowIndex: 0,\n  firstColumnIndex: 0,\n  lastColumnIndex: 0\n};\nexport const virtualizationStateInitializer = (state, props) => {\n  const {\n    disableVirtualization,\n    autoHeight\n  } = props;\n  const virtualization = {\n    enabled: !disableVirtualization,\n    enabledForColumns: !disableVirtualization,\n    enabledForRows: !disableVirtualization && !autoHeight,\n    renderContext: EMPTY_RENDER_CONTEXT\n  };\n  return _extends({}, state, {\n    virtualization\n  });\n};\nexport function useGridVirtualization(apiRef, props) {\n  /*\n   * API METHODS\n   */\n\n  const setVirtualization = enabled => {\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: _extends({}, state.virtualization, {\n        enabled,\n        enabledForColumns: enabled,\n        enabledForRows: enabled && !props.autoHeight\n      })\n    }));\n  };\n  const setColumnVirtualization = enabled => {\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: _extends({}, state.virtualization, {\n        enabledForColumns: enabled\n      })\n    }));\n  };\n  const api = {\n    unstable_setVirtualization: setVirtualization,\n    unstable_setColumnVirtualization: setColumnVirtualization\n  };\n  useGridApiMethod(apiRef, api, 'public');\n\n  /*\n   * EFFECTS\n   */\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(() => {\n    setVirtualization(!props.disableVirtualization);\n  }, [props.disableVirtualization, props.autoHeight]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "EMPTY_RENDER_CONTEXT", "firstRowIndex", "lastRowIndex", "firstColumnIndex", "lastColumnIndex", "virtualizationStateInitializer", "state", "props", "disableVirtualization", "autoHeight", "virtualization", "enabled", "enabledForColumns", "enabledForRows", "renderContext", "useGridVirtualization", "apiRef", "setVirtualization", "current", "setState", "setColumnVirtualization", "api", "unstable_setVirtualization", "unstable_setColumnVirtualization", "useEffect"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/virtualization/useGridVirtualization.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nexport const EMPTY_RENDER_CONTEXT = {\n  firstRowIndex: 0,\n  lastRowIndex: 0,\n  firstColumnIndex: 0,\n  lastColumnIndex: 0\n};\nexport const virtualizationStateInitializer = (state, props) => {\n  const {\n    disableVirtualization,\n    autoHeight\n  } = props;\n  const virtualization = {\n    enabled: !disableVirtualization,\n    enabledForColumns: !disableVirtualization,\n    enabledForRows: !disableVirtualization && !autoHeight,\n    renderContext: EMPTY_RENDER_CONTEXT\n  };\n  return _extends({}, state, {\n    virtualization\n  });\n};\nexport function useGridVirtualization(apiRef, props) {\n  /*\n   * API METHODS\n   */\n\n  const setVirtualization = enabled => {\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: _extends({}, state.virtualization, {\n        enabled,\n        enabledForColumns: enabled,\n        enabledForRows: enabled && !props.autoHeight\n      })\n    }));\n  };\n  const setColumnVirtualization = enabled => {\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: _extends({}, state.virtualization, {\n        enabledForColumns: enabled\n      })\n    }));\n  };\n  const api = {\n    unstable_setVirtualization: setVirtualization,\n    unstable_setColumnVirtualization: setColumnVirtualization\n  };\n  useGridApiMethod(apiRef, api, 'public');\n\n  /*\n   * EFFECTS\n   */\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(() => {\n    setVirtualization(!props.disableVirtualization);\n  }, [props.disableVirtualization, props.autoHeight]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,OAAO,MAAMC,oBAAoB,GAAG;EAClCC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,gBAAgB,EAAE,CAAC;EACnBC,eAAe,EAAE;AACnB,CAAC;AACD,OAAO,MAAMC,8BAA8B,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAC9D,MAAM;IACJC,qBAAqB;IACrBC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,cAAc,GAAG;IACrBC,OAAO,EAAE,CAACH,qBAAqB;IAC/BI,iBAAiB,EAAE,CAACJ,qBAAqB;IACzCK,cAAc,EAAE,CAACL,qBAAqB,IAAI,CAACC,UAAU;IACrDK,aAAa,EAAEd;EACjB,CAAC;EACD,OAAOH,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,EAAE;IACzBI;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,SAASK,qBAAqBA,CAACC,MAAM,EAAET,KAAK,EAAE;EACnD;AACF;AACA;;EAEE,MAAMU,iBAAiB,GAAGN,OAAO,IAAI;IACnCK,MAAM,CAACE,OAAO,CAACC,QAAQ,CAACb,KAAK,IAAIT,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,EAAE;MACnDI,cAAc,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,CAACI,cAAc,EAAE;QACjDC,OAAO;QACPC,iBAAiB,EAAED,OAAO;QAC1BE,cAAc,EAAEF,OAAO,IAAI,CAACJ,KAAK,CAACE;MACpC,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAMW,uBAAuB,GAAGT,OAAO,IAAI;IACzCK,MAAM,CAACE,OAAO,CAACC,QAAQ,CAACb,KAAK,IAAIT,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,EAAE;MACnDI,cAAc,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,CAACI,cAAc,EAAE;QACjDE,iBAAiB,EAAED;MACrB,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAMU,GAAG,GAAG;IACVC,0BAA0B,EAAEL,iBAAiB;IAC7CM,gCAAgC,EAAEH;EACpC,CAAC;EACDrB,gBAAgB,CAACiB,MAAM,EAAEK,GAAG,EAAE,QAAQ,CAAC;;EAEvC;AACF;AACA;;EAEE;EACAvB,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpBP,iBAAiB,CAAC,CAACV,KAAK,CAACC,qBAAqB,CAAC;EACjD,CAAC,EAAE,CAACD,KAAK,CAACC,qBAAqB,EAAED,KAAK,CAACE,UAAU,CAAC,CAAC;EACnD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}