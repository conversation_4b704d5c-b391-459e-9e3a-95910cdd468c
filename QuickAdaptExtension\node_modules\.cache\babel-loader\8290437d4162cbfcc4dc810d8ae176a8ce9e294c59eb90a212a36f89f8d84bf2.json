{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { GRID_DEFAULT_LOCALE_TEXT } from \"../constants/index.js\";\nimport { DATA_GRID_DEFAULT_SLOTS_COMPONENTS } from \"../constants/defaultGridSlotsComponents.js\";\nimport { GridEditModes } from \"../models/index.js\";\nimport { computeSlots, useProps } from \"../internals/utils/index.js\";\nconst DATA_GRID_FORCED_PROPS = {\n  disableMultipleColumnsFiltering: true,\n  disableMultipleColumnsSorting: true,\n  throttleRowsMs: undefined,\n  hideFooterRowCount: false,\n  pagination: true,\n  checkboxSelectionVisibleOnly: false,\n  disableColumnReorder: true,\n  keepColumnPositionIfDraggedOutside: false,\n  signature: 'DataGrid'\n};\n\n/**\n * The default values of `DataGridPropsWithDefaultValues` to inject in the props of DataGrid.\n */\nexport const DATA_GRID_PROPS_DEFAULT_VALUES = {\n  autoHeight: false,\n  autoPageSize: false,\n  autosizeOnMount: false,\n  checkboxSelection: false,\n  checkboxSelectionVisibleOnly: false,\n  clipboardCopyCellDelimiter: '\\t',\n  columnBufferPx: 150,\n  columnHeaderHeight: 56,\n  disableAutosize: false,\n  disableColumnFilter: false,\n  disableColumnMenu: false,\n  disableColumnReorder: false,\n  disableColumnResize: false,\n  disableColumnSelector: false,\n  disableColumnSorting: false,\n  disableDensitySelector: false,\n  disableEval: false,\n  disableMultipleColumnsFiltering: false,\n  disableMultipleColumnsSorting: false,\n  disableMultipleRowSelection: false,\n  disableRowSelectionOnClick: false,\n  disableVirtualization: false,\n  editMode: GridEditModes.Cell,\n  filterDebounceMs: 150,\n  filterMode: 'client',\n  hideFooter: false,\n  hideFooterPagination: false,\n  hideFooterRowCount: false,\n  hideFooterSelectedRowCount: false,\n  ignoreDiacritics: false,\n  ignoreValueFormatterDuringExport: false,\n  // TODO v8: Update to 'select'\n  indeterminateCheckboxAction: 'deselect',\n  keepColumnPositionIfDraggedOutside: false,\n  keepNonExistentRowsSelected: false,\n  loading: false,\n  logger: console,\n  logLevel: process.env.NODE_ENV === 'production' ? 'error' : 'warn',\n  pageSizeOptions: [25, 50, 100],\n  pagination: false,\n  paginationMode: 'client',\n  resizeThrottleMs: 60,\n  rowBufferPx: 150,\n  rowHeight: 52,\n  rowPositionsDebounceMs: 166,\n  rows: [],\n  rowSelection: true,\n  rowSpacingType: 'margin',\n  showCellVerticalBorder: false,\n  showColumnVerticalBorder: false,\n  sortingMode: 'client',\n  sortingOrder: ['asc', 'desc', null],\n  throttleRowsMs: 0,\n  unstable_rowSpanning: false\n};\nconst defaultSlots = DATA_GRID_DEFAULT_SLOTS_COMPONENTS;\nexport const useDataGridProps = inProps => {\n  const themedProps = useProps(\n  // eslint-disable-next-line material-ui/mui-name-matches-component-name\n  useThemeProps({\n    props: inProps,\n    name: 'MuiDataGrid'\n  }));\n  const localeText = React.useMemo(() => _extends({}, GRID_DEFAULT_LOCALE_TEXT, themedProps.localeText), [themedProps.localeText]);\n  const slots = React.useMemo(() => computeSlots({\n    defaultSlots,\n    slots: themedProps.slots\n  }), [themedProps.slots]);\n  const injectDefaultProps = React.useMemo(() => {\n    return Object.keys(DATA_GRID_PROPS_DEFAULT_VALUES).reduce((acc, key) => {\n      // @ts-ignore\n      acc[key] = themedProps[key] ?? DATA_GRID_PROPS_DEFAULT_VALUES[key];\n      return acc;\n    }, {});\n  }, [themedProps]);\n  return React.useMemo(() => _extends({}, themedProps, injectDefaultProps, {\n    localeText,\n    slots\n  }, DATA_GRID_FORCED_PROPS), [themedProps, localeText, slots, injectDefaultProps]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useThemeProps", "GRID_DEFAULT_LOCALE_TEXT", "DATA_GRID_DEFAULT_SLOTS_COMPONENTS", "GridEditModes", "computeSlots", "useProps", "DATA_GRID_FORCED_PROPS", "disableMultipleColumnsFiltering", "disableMultipleColumnsSorting", "throttleRowsMs", "undefined", "hideFooterRowCount", "pagination", "checkboxSelectionVisibleOnly", "disableColumnReorder", "keepColumnPositionIfDraggedOutside", "signature", "DATA_GRID_PROPS_DEFAULT_VALUES", "autoHeight", "autoPageSize", "autosizeOnMount", "checkboxSelection", "clipboardCopyCellDelimiter", "columnBufferPx", "columnHeaderHeight", "disableAutosize", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableColumnMenu", "disableColumnResize", "disableColumnSelector", "disableColumnSorting", "disableDensitySelector", "disableEval", "disableMultipleRowSelection", "disableRowSelectionOnClick", "disableVirtualization", "editMode", "Cell", "filterDebounceMs", "filterMode", "hideFooter", "hideFooterPagination", "hideFooterSelectedRowCount", "ignoreDiacritics", "ignoreValueFormatterDuringExport", "indeterminateCheckboxAction", "keepNonExistentRowsSelected", "loading", "logger", "console", "logLevel", "process", "env", "NODE_ENV", "pageSizeOptions", "paginationMode", "resizeThrottleMs", "rowBufferPx", "rowHeight", "rowPositionsDebounceMs", "rows", "rowSelection", "rowSpacingType", "showCellVerticalBorder", "showColumnVerticalBorder", "sortingMode", "sortingOrder", "unstable_rowSpanning", "defaultSlots", "useDataGridProps", "inProps", "themedProps", "props", "name", "localeText", "useMemo", "slots", "injectDefaultProps", "Object", "keys", "reduce", "acc", "key"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/DataGrid/useDataGridProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { GRID_DEFAULT_LOCALE_TEXT } from \"../constants/index.js\";\nimport { DATA_GRID_DEFAULT_SLOTS_COMPONENTS } from \"../constants/defaultGridSlotsComponents.js\";\nimport { GridEditModes } from \"../models/index.js\";\nimport { computeSlots, useProps } from \"../internals/utils/index.js\";\nconst DATA_GRID_FORCED_PROPS = {\n  disableMultipleColumnsFiltering: true,\n  disableMultipleColumnsSorting: true,\n  throttleRowsMs: undefined,\n  hideFooterRowCount: false,\n  pagination: true,\n  checkboxSelectionVisibleOnly: false,\n  disableColumnReorder: true,\n  keepColumnPositionIfDraggedOutside: false,\n  signature: 'DataGrid'\n};\n\n/**\n * The default values of `DataGridPropsWithDefaultValues` to inject in the props of DataGrid.\n */\nexport const DATA_GRID_PROPS_DEFAULT_VALUES = {\n  autoHeight: false,\n  autoPageSize: false,\n  autosizeOnMount: false,\n  checkboxSelection: false,\n  checkboxSelectionVisibleOnly: false,\n  clipboardCopyCellDelimiter: '\\t',\n  columnBufferPx: 150,\n  columnHeaderHeight: 56,\n  disableAutosize: false,\n  disableColumnFilter: false,\n  disableColumnMenu: false,\n  disableColumnReorder: false,\n  disableColumnResize: false,\n  disableColumnSelector: false,\n  disableColumnSorting: false,\n  disableDensitySelector: false,\n  disableEval: false,\n  disableMultipleColumnsFiltering: false,\n  disableMultipleColumnsSorting: false,\n  disableMultipleRowSelection: false,\n  disableRowSelectionOnClick: false,\n  disableVirtualization: false,\n  editMode: GridEditModes.Cell,\n  filterDebounceMs: 150,\n  filterMode: 'client',\n  hideFooter: false,\n  hideFooterPagination: false,\n  hideFooterRowCount: false,\n  hideFooterSelectedRowCount: false,\n  ignoreDiacritics: false,\n  ignoreValueFormatterDuringExport: false,\n  // TODO v8: Update to 'select'\n  indeterminateCheckboxAction: 'deselect',\n  keepColumnPositionIfDraggedOutside: false,\n  keepNonExistentRowsSelected: false,\n  loading: false,\n  logger: console,\n  logLevel: process.env.NODE_ENV === 'production' ? 'error' : 'warn',\n  pageSizeOptions: [25, 50, 100],\n  pagination: false,\n  paginationMode: 'client',\n  resizeThrottleMs: 60,\n  rowBufferPx: 150,\n  rowHeight: 52,\n  rowPositionsDebounceMs: 166,\n  rows: [],\n  rowSelection: true,\n  rowSpacingType: 'margin',\n  showCellVerticalBorder: false,\n  showColumnVerticalBorder: false,\n  sortingMode: 'client',\n  sortingOrder: ['asc', 'desc', null],\n  throttleRowsMs: 0,\n  unstable_rowSpanning: false\n};\nconst defaultSlots = DATA_GRID_DEFAULT_SLOTS_COMPONENTS;\nexport const useDataGridProps = inProps => {\n  const themedProps = useProps(\n  // eslint-disable-next-line material-ui/mui-name-matches-component-name\n  useThemeProps({\n    props: inProps,\n    name: 'MuiDataGrid'\n  }));\n  const localeText = React.useMemo(() => _extends({}, GRID_DEFAULT_LOCALE_TEXT, themedProps.localeText), [themedProps.localeText]);\n  const slots = React.useMemo(() => computeSlots({\n    defaultSlots,\n    slots: themedProps.slots\n  }), [themedProps.slots]);\n  const injectDefaultProps = React.useMemo(() => {\n    return Object.keys(DATA_GRID_PROPS_DEFAULT_VALUES).reduce((acc, key) => {\n      // @ts-ignore\n      acc[key] = themedProps[key] ?? DATA_GRID_PROPS_DEFAULT_VALUES[key];\n      return acc;\n    }, {});\n  }, [themedProps]);\n  return React.useMemo(() => _extends({}, themedProps, injectDefaultProps, {\n    localeText,\n    slots\n  }, DATA_GRID_FORCED_PROPS), [themedProps, localeText, slots, injectDefaultProps]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,kCAAkC,QAAQ,4CAA4C;AAC/F,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,EAAEC,QAAQ,QAAQ,6BAA6B;AACpE,MAAMC,sBAAsB,GAAG;EAC7BC,+BAA+B,EAAE,IAAI;EACrCC,6BAA6B,EAAE,IAAI;EACnCC,cAAc,EAAEC,SAAS;EACzBC,kBAAkB,EAAE,KAAK;EACzBC,UAAU,EAAE,IAAI;EAChBC,4BAA4B,EAAE,KAAK;EACnCC,oBAAoB,EAAE,IAAI;EAC1BC,kCAAkC,EAAE,KAAK;EACzCC,SAAS,EAAE;AACb,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,8BAA8B,GAAG;EAC5CC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBC,eAAe,EAAE,KAAK;EACtBC,iBAAiB,EAAE,KAAK;EACxBR,4BAA4B,EAAE,KAAK;EACnCS,0BAA0B,EAAE,IAAI;EAChCC,cAAc,EAAE,GAAG;EACnBC,kBAAkB,EAAE,EAAE;EACtBC,eAAe,EAAE,KAAK;EACtBC,mBAAmB,EAAE,KAAK;EAC1BC,iBAAiB,EAAE,KAAK;EACxBb,oBAAoB,EAAE,KAAK;EAC3Bc,mBAAmB,EAAE,KAAK;EAC1BC,qBAAqB,EAAE,KAAK;EAC5BC,oBAAoB,EAAE,KAAK;EAC3BC,sBAAsB,EAAE,KAAK;EAC7BC,WAAW,EAAE,KAAK;EAClBzB,+BAA+B,EAAE,KAAK;EACtCC,6BAA6B,EAAE,KAAK;EACpCyB,2BAA2B,EAAE,KAAK;EAClCC,0BAA0B,EAAE,KAAK;EACjCC,qBAAqB,EAAE,KAAK;EAC5BC,QAAQ,EAAEjC,aAAa,CAACkC,IAAI;EAC5BC,gBAAgB,EAAE,GAAG;EACrBC,UAAU,EAAE,QAAQ;EACpBC,UAAU,EAAE,KAAK;EACjBC,oBAAoB,EAAE,KAAK;EAC3B9B,kBAAkB,EAAE,KAAK;EACzB+B,0BAA0B,EAAE,KAAK;EACjCC,gBAAgB,EAAE,KAAK;EACvBC,gCAAgC,EAAE,KAAK;EACvC;EACAC,2BAA2B,EAAE,UAAU;EACvC9B,kCAAkC,EAAE,KAAK;EACzC+B,2BAA2B,EAAE,KAAK;EAClCC,OAAO,EAAE,KAAK;EACdC,MAAM,EAAEC,OAAO;EACfC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,OAAO,GAAG,MAAM;EAClEC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC9B1C,UAAU,EAAE,KAAK;EACjB2C,cAAc,EAAE,QAAQ;EACxBC,gBAAgB,EAAE,EAAE;EACpBC,WAAW,EAAE,GAAG;EAChBC,SAAS,EAAE,EAAE;EACbC,sBAAsB,EAAE,GAAG;EAC3BC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAE,IAAI;EAClBC,cAAc,EAAE,QAAQ;EACxBC,sBAAsB,EAAE,KAAK;EAC7BC,wBAAwB,EAAE,KAAK;EAC/BC,WAAW,EAAE,QAAQ;EACrBC,YAAY,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC;EACnCzD,cAAc,EAAE,CAAC;EACjB0D,oBAAoB,EAAE;AACxB,CAAC;AACD,MAAMC,YAAY,GAAGlE,kCAAkC;AACvD,OAAO,MAAMmE,gBAAgB,GAAGC,OAAO,IAAI;EACzC,MAAMC,WAAW,GAAGlE,QAAQ;EAC5B;EACAL,aAAa,CAAC;IACZwE,KAAK,EAAEF,OAAO;IACdG,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;EACH,MAAMC,UAAU,GAAG3E,KAAK,CAAC4E,OAAO,CAAC,MAAM7E,QAAQ,CAAC,CAAC,CAAC,EAAEG,wBAAwB,EAAEsE,WAAW,CAACG,UAAU,CAAC,EAAE,CAACH,WAAW,CAACG,UAAU,CAAC,CAAC;EAChI,MAAME,KAAK,GAAG7E,KAAK,CAAC4E,OAAO,CAAC,MAAMvE,YAAY,CAAC;IAC7CgE,YAAY;IACZQ,KAAK,EAAEL,WAAW,CAACK;EACrB,CAAC,CAAC,EAAE,CAACL,WAAW,CAACK,KAAK,CAAC,CAAC;EACxB,MAAMC,kBAAkB,GAAG9E,KAAK,CAAC4E,OAAO,CAAC,MAAM;IAC7C,OAAOG,MAAM,CAACC,IAAI,CAAC9D,8BAA8B,CAAC,CAAC+D,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACtE;MACAD,GAAG,CAACC,GAAG,CAAC,GAAGX,WAAW,CAACW,GAAG,CAAC,IAAIjE,8BAA8B,CAACiE,GAAG,CAAC;MAClE,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,EAAE,CAACV,WAAW,CAAC,CAAC;EACjB,OAAOxE,KAAK,CAAC4E,OAAO,CAAC,MAAM7E,QAAQ,CAAC,CAAC,CAAC,EAAEyE,WAAW,EAAEM,kBAAkB,EAAE;IACvEH,UAAU;IACVE;EACF,CAAC,EAAEtE,sBAAsB,CAAC,EAAE,CAACiE,WAAW,EAAEG,UAAU,EAAEE,KAAK,EAAEC,kBAAkB,CAAC,CAAC;AACnF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}