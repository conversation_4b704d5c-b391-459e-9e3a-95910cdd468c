{"ast": null, "code": "import * as React from 'react';\nexport const useGridInitializeState = (initializer, privateApiRef, props) => {\n  const isInitialized = React.useRef(false);\n  if (!isInitialized.current) {\n    privateApiRef.current.state = initializer(privateApiRef.current.state, props, privateApiRef);\n    isInitialized.current = true;\n  }\n};", "map": {"version": 3, "names": ["React", "useGridInitializeState", "initializer", "privateApiRef", "props", "isInitialized", "useRef", "current", "state"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/utils/useGridInitializeState.js"], "sourcesContent": ["import * as React from 'react';\nexport const useGridInitializeState = (initializer, privateApiRef, props) => {\n  const isInitialized = React.useRef(false);\n  if (!isInitialized.current) {\n    privateApiRef.current.state = initializer(privateApiRef.current.state, props, privateApiRef);\n    isInitialized.current = true;\n  }\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,WAAW,EAAEC,aAAa,EAAEC,KAAK,KAAK;EAC3E,MAAMC,aAAa,GAAGL,KAAK,CAACM,MAAM,CAAC,KAAK,CAAC;EACzC,IAAI,CAACD,aAAa,CAACE,OAAO,EAAE;IAC1BJ,aAAa,CAACI,OAAO,CAACC,KAAK,GAAGN,WAAW,CAACC,aAAa,CAACI,OAAO,CAACC,KAAK,EAAEJ,KAAK,EAAED,aAAa,CAAC;IAC5FE,aAAa,CAACE,OAAO,GAAG,IAAI;EAC9B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}