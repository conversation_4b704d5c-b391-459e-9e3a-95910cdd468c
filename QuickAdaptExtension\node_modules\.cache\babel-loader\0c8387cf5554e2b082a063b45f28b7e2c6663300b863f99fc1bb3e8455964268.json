{"ast": null, "code": "import React,{useEffect,useState,useRef}from\"react\";import{Box,Button,IconButton,LinearProgress,MobileStepper,Popover,Typography}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from'react-perfect-scrollbar';import'react-perfect-scrollbar/dist/css/styles.css';// Viewport boundary detection utilities\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";// Utility function to get viewport bounds\nconst getViewportBounds=()=>({top:0,left:0,right:window.innerWidth,bottom:window.innerHeight});// Utility function to calculate available space around an element\nconst getAvailableSpace=elementRect=>{const viewport=getViewportBounds();return{top:elementRect.top-viewport.top,bottom:viewport.bottom-elementRect.bottom,left:elementRect.left-viewport.left,right:viewport.right-elementRect.right};};// Utility function to determine optimal placement based on available space\nconst calculateOptimalPlacement=(elementRect,popupDimensions)=>{const space=getAvailableSpace(elementRect);const{width,height}=popupDimensions;// Add some padding for better UX\nconst padding=20;// Check if popup fits in each direction\nconst fitsTop=space.top>=height+padding;const fitsBottom=space.bottom>=height+padding;const fitsLeft=space.left>=width+padding;const fitsRight=space.right>=width+padding;// Prefer bottom first (most common), then top, then sides\nif(fitsBottom)return'bottom';if(fitsTop)return'top';if(fitsRight)return'right';if(fitsLeft)return'left';// If nothing fits perfectly, choose the side with most space\nconst maxSpace=Math.max(space.top,space.bottom,space.left,space.right);if(maxSpace===space.bottom)return'bottom';if(maxSpace===space.top)return'top';if(maxSpace===space.right)return'right';return'left';};// Utility function to calculate popup position with boundary detection\nconst calculatePopupPositionWithBounds=function(elementRect,popupDimensions,placement){let hotspotOffset=arguments.length>3&&arguments[3]!==undefined?arguments[3]:{x:0,y:0};let hotspotSize=arguments.length>4&&arguments[4]!==undefined?arguments[4]:30;const viewport=getViewportBounds();const padding=10;const beaconSpacing=10;// Exact spacing from beacon edge\nlet top;let left;let anchorOrigin;let transformOrigin;// Calculate hotspot center position\nconst hotspotCenterX=elementRect.left+hotspotOffset.x+hotspotSize/2;const hotspotCenterY=elementRect.top+hotspotOffset.y+hotspotSize/2;switch(placement){case'top':top=hotspotCenterY-hotspotSize/2-popupDimensions.height-beaconSpacing;left=hotspotCenterX-popupDimensions.width/2;anchorOrigin={vertical:'top',horizontal:'center'};transformOrigin={vertical:'bottom',horizontal:'center'};break;case'bottom':top=hotspotCenterY+hotspotSize/2+beaconSpacing;left=hotspotCenterX-popupDimensions.width/2;anchorOrigin={vertical:'bottom',horizontal:'center'};transformOrigin={vertical:'top',horizontal:'center'};break;case'left':top=hotspotCenterY-popupDimensions.height/2;left=hotspotCenterX-hotspotSize/2-popupDimensions.width-beaconSpacing;anchorOrigin={vertical:'center',horizontal:'left'};transformOrigin={vertical:'center',horizontal:'right'};break;case'right':top=hotspotCenterY-popupDimensions.height/2;left=hotspotCenterX+hotspotSize/2+beaconSpacing;anchorOrigin={vertical:'center',horizontal:'right'};transformOrigin={vertical:'center',horizontal:'left'};break;}// Ensure popup stays within viewport bounds\nleft=Math.max(padding,Math.min(left,viewport.right-popupDimensions.width-padding));top=Math.max(padding,Math.min(top,viewport.bottom-popupDimensions.height-padding));return{top:top+window.scrollY,left:left+window.scrollX,placement,anchorOrigin,transformOrigin};};const HotspotPreview=_ref=>{var _savedGuideData$Guide,_savedGuideData$Guide2,_textFieldProperties$,_textFieldProperties$2,_textFieldProperties$3,_imageProperties,_imageProperties$Cust,_imageProperties$Cust2,_savedGuideData$Guide31,_savedGuideData$Guide32,_savedGuideData$Guide33;let{anchorEl,guideStep,title,text,imageUrl,onClose,onPrevious,onContinue,videoUrl,currentStep,totalSteps,onDontShowAgain,progress,textFieldProperties,imageProperties,customButton,modalProperties,canvasProperties,htmlSnippet,previousButtonStyles,continueButtonStyles,OverlayValue,savedGuideData,hotspotProperties,handleHotspotHover,handleHotspotClick,isHotspotPopupOpen,showHotspotenduser}=_ref;const{setCurrentStep,selectedTemplate,toolTipGuideMetaData,elementSelected,axisData,tooltipXaxis,tooltipYaxis,setOpenTooltip,openTooltip,pulseAnimationsH,hotspotGuideMetaData,selectedTemplateTour,selectedOption,ProgressColor}=useDrawerStore(state=>state);const[targetElement,setTargetElement]=useState(null);// Enhanced state management for smart positioning\nconst[popupPosition,setPopupPosition]=useState(null);const[optimalPosition,setOptimalPosition]=useState(null);const[popupDimensions,setPopupDimensions]=useState({width:300,height:200});const[dynamicWidth,setDynamicWidth]=useState(null);const[hotspotSize,setHotspotSize]=useState(30);const contentRef=useRef(null);const buttonContainerRef=useRef(null);const popupRef=useRef(null);let hotspot;const getElementByXPath=xpath=>{const result=document.evaluate(xpath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const node=result.singleNodeValue;if(node instanceof HTMLElement){return node;}else if(node!==null&&node!==void 0&&node.parentElement){return node.parentElement;// Return parent if it's a text node\n}else{return null;}};let xpath;if(savedGuideData)xpath=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide=savedGuideData.GuideStep)===null||_savedGuideData$Guide===void 0?void 0:(_savedGuideData$Guide2=_savedGuideData$Guide[0])===null||_savedGuideData$Guide2===void 0?void 0:_savedGuideData$Guide2.ElementPath;// State to track if scrolling is needed\nconst[needsScrolling,setNeedsScrolling]=useState(false);const scrollbarRef=useRef(null);// Function to calculate popup position below the hotspot\nconst calculatePopupPosition=(elementRect,hotspotSize,xOffset,yOffset)=>{const hotspotLeft=elementRect.x+xOffset;const hotspotTop=elementRect.y+yOffset;// Position popup below the hotspot for better user experience\nconst dynamicOffsetX=hotspotSize+5;// Align horizontally with hotspot\nconst dynamicOffsetY=hotspotSize+10;// Position below hotspot with spacing\nreturn{top:hotspotTop+window.scrollY+dynamicOffsetY,left:hotspotLeft+window.scrollX+dynamicOffsetX};};// Initialize smart positioning when xpath changes\nuseEffect(()=>{if(xpath){const element=getElementByXPath(xpath);if(element){const dimensions=calculateOptimalDimensions();setPopupDimensions(dimensions);updateSmartPosition(element,dimensions);}}},[xpath]);useEffect(()=>{const element=getElementByXPath(xpath);// setTargetElement(element);\nif(element){}},[savedGuideData]);useEffect(()=>{var _guideStep;const element=getElementByXPath(guideStep===null||guideStep===void 0?void 0:(_guideStep=guideStep[currentStep-1])===null||_guideStep===void 0?void 0:_guideStep.ElementPath);setTargetElement(element);if(element){element.style.backgroundColor=\"red !important\";// Update popup position when target element changes\nconst rect=element.getBoundingClientRect();setPopupPosition({top:rect.top+window.scrollY,left:rect.left+window.scrollX});}},[guideStep,currentStep]);// Hotspot styles are applied directly in the applyHotspotStyles function\n// State for overlay value\nconst[,setOverlayValue]=useState(false);const handleContinue=()=>{if(selectedTemplate!==\"Tour\"){if(currentStep<totalSteps){setCurrentStep(currentStep+1);onContinue();renderNextPopup(currentStep<totalSteps);}}else{setCurrentStep(currentStep+1);const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.style.display=\"none\";existingHotspot.remove();}}};const renderNextPopup=shouldRenderNextPopup=>{var _savedGuideData$Guide3,_savedGuideData$Guide4,_savedGuideData$Guide5,_savedGuideData$Guide6,_savedGuideData$Guide7,_savedGuideData$Guide8,_savedGuideData$Guide9,_savedGuideData$Guide10,_savedGuideData$Guide11,_savedGuideData$Guide12;return shouldRenderNextPopup?/*#__PURE__*/_jsx(HotspotPreview,{isHotspotPopupOpen:isHotspotPopupOpen,showHotspotenduser:showHotspotenduser,handleHotspotHover:handleHotspotHover,handleHotspotClick:handleHotspotClick,anchorEl:anchorEl,savedGuideData:savedGuideData,guideStep:guideStep,onClose:onClose,onPrevious:handlePrevious,onContinue:handleContinue,title:title,text:text,imageUrl:imageUrl,currentStep:currentStep+1,totalSteps:totalSteps,onDontShowAgain:onDontShowAgain,progress:progress,textFieldProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide3=savedGuideData.GuideStep)===null||_savedGuideData$Guide3===void 0?void 0:(_savedGuideData$Guide4=_savedGuideData$Guide3[currentStep])===null||_savedGuideData$Guide4===void 0?void 0:_savedGuideData$Guide4.TextFieldProperties,imageProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide5=savedGuideData.GuideStep)===null||_savedGuideData$Guide5===void 0?void 0:(_savedGuideData$Guide6=_savedGuideData$Guide5[currentStep])===null||_savedGuideData$Guide6===void 0?void 0:_savedGuideData$Guide6.ImageProperties,customButton:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide7=savedGuideData.GuideStep)===null||_savedGuideData$Guide7===void 0?void 0:(_savedGuideData$Guide8=_savedGuideData$Guide7[currentStep])===null||_savedGuideData$Guide8===void 0?void 0:(_savedGuideData$Guide9=_savedGuideData$Guide8.ButtonSection)===null||_savedGuideData$Guide9===void 0?void 0:(_savedGuideData$Guide10=_savedGuideData$Guide9.map(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id// Attach the container ID for grouping\n}))))===null||_savedGuideData$Guide10===void 0?void 0:_savedGuideData$Guide10.reduce((acc,curr)=>acc.concat(curr),[]))||[],modalProperties:modalProperties,canvasProperties:canvasProperties,htmlSnippet:htmlSnippet,OverlayValue:OverlayValue,hotspotProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide11=savedGuideData.GuideStep)===null||_savedGuideData$Guide11===void 0?void 0:(_savedGuideData$Guide12=_savedGuideData$Guide11[currentStep-1])===null||_savedGuideData$Guide12===void 0?void 0:_savedGuideData$Guide12.Hotspot)||{}}):null;};const handlePrevious=()=>{if(currentStep>1){setCurrentStep(currentStep-1);onPrevious();}};useEffect(()=>{if(OverlayValue){setOverlayValue(true);}else{setOverlayValue(false);}},[OverlayValue]);// Image fit is used directly in the component\nconst getAnchorAndTransformOrigins=position=>{switch(position){case\"top-left\":return{anchorOrigin:{vertical:\"top\",horizontal:\"left\"},transformOrigin:{vertical:\"bottom\",horizontal:\"right\"}};case\"top-right\":return{anchorOrigin:{vertical:\"top\",horizontal:\"right\"},transformOrigin:{vertical:\"bottom\",horizontal:\"left\"}};case\"bottom-left\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"left\"},transformOrigin:{vertical:\"top\",horizontal:\"right\"}};case\"bottom-right\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};case\"center-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"top-center\":return{anchorOrigin:{vertical:\"top\",horizontal:\"center\"},transformOrigin:{vertical:\"bottom\",horizontal:\"center\"}};case\"left-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"left\"},transformOrigin:{vertical:\"center\",horizontal:\"right\"}};case\"bottom-center\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"right-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};default:return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};}};// Use smart positioning if available, otherwise fall back to canvas properties\nconst{anchorOrigin,transformOrigin}=optimalPosition?{anchorOrigin:optimalPosition.anchorOrigin,transformOrigin:optimalPosition.transformOrigin}:getAnchorAndTransformOrigins((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center center\");const textStyle={fontWeight:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$=textFieldProperties.TextProperties)!==null&&_textFieldProperties$!==void 0&&_textFieldProperties$.Bold?\"bold\":\"normal\",fontStyle:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$2=textFieldProperties.TextProperties)!==null&&_textFieldProperties$2!==void 0&&_textFieldProperties$2.Italic?\"italic\":\"normal\",color:(textFieldProperties===null||textFieldProperties===void 0?void 0:(_textFieldProperties$3=textFieldProperties.TextProperties)===null||_textFieldProperties$3===void 0?void 0:_textFieldProperties$3.TextColor)||\"#000000\",textAlign:(textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.Alignment)||\"left\"};// Image styles are applied directly in the component\nconst renderHtmlSnippet=snippet=>{// Return the raw HTML snippet for rendering\nreturn{__html:snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g,(_match,p1,p2,p3)=>{return`${p1}${p2}\" target=\"_blank\"${p3}`;})};};// Helper function to check if popup has only buttons (no text or images)\nconst hasOnlyButtons=()=>{const hasImage=imageProperties&&imageProperties.length>0&&imageProperties.some(prop=>prop.CustomImage&&prop.CustomImage.some(img=>img.Url));const hasText=textFieldProperties&&textFieldProperties.length>0&&textFieldProperties.some(field=>field.Text&&field.Text.trim()!==\"\");const hasButtons=customButton&&customButton.length>0;return hasButtons&&!hasImage&&!hasText;};// Helper function to check if popup has only text (no buttons or images)\nconst hasOnlyText=()=>{const hasImage=imageProperties&&imageProperties.length>0&&imageProperties.some(prop=>prop.CustomImage&&prop.CustomImage.some(img=>img.Url));const hasText=textFieldProperties&&textFieldProperties.length>0&&textFieldProperties.some(field=>field.Text&&field.Text.trim()!==\"\");const hasButtons=customButton&&customButton.length>0;return hasText&&!hasImage&&!hasButtons;};// Enhanced function to calculate optimal width and height based on content\nconst calculateOptimalDimensions=()=>{var _contentRef$current2,_buttonContainerRef$c2;// If we have a fixed width from canvas settings and not a compact popup, use that\nif(canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width&&!hasOnlyButtons()&&!hasOnlyText()){const width=parseInt(canvasProperties.Width);return{width:width,height:estimateContentHeight(width)};}// For popups with only buttons or only text, calculate minimal dimensions\nif(hasOnlyButtons()||hasOnlyText()){var _contentRef$current,_buttonContainerRef$c;const contentWidth=((_contentRef$current=contentRef.current)===null||_contentRef$current===void 0?void 0:_contentRef$current.scrollWidth)||0;const buttonWidth=((_buttonContainerRef$c=buttonContainerRef.current)===null||_buttonContainerRef$c===void 0?void 0:_buttonContainerRef$c.scrollWidth)||0;const width=Math.max(contentWidth,buttonWidth,150);// Minimum 150px\nreturn{width:width+40,// Add padding\nheight:estimateContentHeight(width+40)};}// Get the width of content and button container\nconst contentWidth=((_contentRef$current2=contentRef.current)===null||_contentRef$current2===void 0?void 0:_contentRef$current2.scrollWidth)||0;const buttonWidth=((_buttonContainerRef$c2=buttonContainerRef.current)===null||_buttonContainerRef$c2===void 0?void 0:_buttonContainerRef$c2.scrollWidth)||0;// Use the larger of the two, with some minimum and maximum constraints\nconst optimalWidth=Math.max(contentWidth,buttonWidth);// Add some padding to ensure text has room to wrap naturally\nconst paddedWidth=optimalWidth+40;// 20px padding on each side\n// Ensure width is between reasonable bounds\nconst minWidth=250;// Minimum width\nconst maxWidth=Math.min(800,window.innerWidth*0.9);// Max 90% of viewport width\nconst finalWidth=Math.max(minWidth,Math.min(paddedWidth,maxWidth));return{width:finalWidth,height:estimateContentHeight(finalWidth)};};// Helper function to estimate content height\nconst estimateContentHeight=width=>{let height=0;// Add height for images\nif(imageProperties&&imageProperties.length>0){imageProperties.forEach(imageProp=>{if(imageProp.CustomImage&&imageProp.CustomImage.length>0){imageProp.CustomImage.forEach(img=>{height+=parseInt(img.SectionHeight||'250')+20;// Add margin\n});}});}// Add height for text (rough estimation)\nif(textFieldProperties&&textFieldProperties.length>0){textFieldProperties.forEach(field=>{if(field.Text&&field.Text.trim()!==''){// Rough estimation: 20px per line, assuming ~50 characters per line\nconst textLength=field.Text.length;const estimatedLines=Math.ceil(textLength/(width/10));// Rough character width estimation\nheight+=Math.max(estimatedLines*20,40);// Minimum 40px per text field\n}});}// Add height for buttons\nif(customButton&&customButton.length>0){height+=60;// Standard button height + padding\n}// Add height for progress bar if enabled\nif(enableProgress&&totalSteps>1){height+=50;}// Add base padding\nheight+=40;// Ensure reasonable bounds\nconst minHeight=100;const maxHeight=Math.min(400,window.innerHeight*0.8);// Max 80% of viewport height\nreturn Math.max(minHeight,Math.min(height,maxHeight));};// Update dynamic dimensions and positioning when content changes\nuseEffect(()=>{// Use requestAnimationFrame to ensure DOM has been updated\nrequestAnimationFrame(()=>{const newDimensions=calculateOptimalDimensions();setPopupDimensions(newDimensions);setDynamicWidth(`${newDimensions.width}px`);// Recalculate position if we have a target element\nif(xpath){const element=getElementByXPath(xpath);if(element){updateSmartPosition(element,newDimensions);}}});},[textFieldProperties,imageProperties,customButton,currentStep,xpath]);// Smart positioning function\nconst updateSmartPosition=(element,dimensions)=>{var _toolTipGuideMetaData,_toolTipGuideMetaData2;const rect=element.getBoundingClientRect();const hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData!==void 0&&_toolTipGuideMetaData.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData2=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");// Calculate optimal placement\nconst placement=calculateOptimalPlacement(rect,dimensions);// Calculate position with boundary detection\nconst optimalPos=calculatePopupPositionWithBounds(rect,dimensions,placement,{x:xOffset,y:yOffset},hotspotSize);setOptimalPosition(optimalPos);setPopupPosition({top:optimalPos.top,left:optimalPos.left});};// Recalculate popup position when hotspot size changes\nuseEffect(()=>{if(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){updateSmartPosition(element,popupDimensions);}}},[hotspotSize,xpath,toolTipGuideMetaData,popupDimensions]);// Recalculate popup position on window resize and scroll\nuseEffect(()=>{const handleViewportChange=()=>{if(xpath){const element=getElementByXPath(xpath);if(element){updateSmartPosition(element,popupDimensions);}}};window.addEventListener('resize',handleViewportChange);window.addEventListener('scroll',handleViewportChange);return()=>{window.removeEventListener('resize',handleViewportChange);window.removeEventListener('scroll',handleViewportChange);};},[xpath,popupDimensions]);const groupedButtons=customButton.reduce((acc,button)=>{const containerId=button.ContainerId||\"default\";// Use a ContainerId or fallback\nif(!acc[containerId]){acc[containerId]=[];}acc[containerId].push(button);return acc;},{});const canvasStyle={position:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\",borderRadius:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Radius)||\"4px\",borderWidth:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderSize)||\"0px\",borderColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderColor)||\"black\",borderStyle:\"solid\",backgroundColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BackgroundColor)||\"white\",// Enhanced width calculation with viewport awareness\nmaxWidth:hasOnlyButtons()||hasOnlyText()?\"none !important\":`min(${popupDimensions.width}px, 90vw) !important`,width:hasOnlyButtons()||hasOnlyText()?\"auto !important\":`min(${popupDimensions.width}px, 90vw) !important`,// Enhanced height with max constraints\nmaxHeight:`min(${popupDimensions.height}px, 80vh) !important`};const sectionHeight=((_imageProperties=imageProperties[currentStep-1])===null||_imageProperties===void 0?void 0:(_imageProperties$Cust=_imageProperties.CustomImage)===null||_imageProperties$Cust===void 0?void 0:(_imageProperties$Cust2=_imageProperties$Cust[currentStep-1])===null||_imageProperties$Cust2===void 0?void 0:_imageProperties$Cust2.SectionHeight)||\"auto\";const handleButtonAction=action=>{if(action.Action===\"open-url\"||action.Action===\"open\"||action.Action===\"openurl\"){const targetUrl=action.TargetUrl;if(action.ActionValue===\"same-tab\"){// Open the URL in the same tab\nwindow.location.href=targetUrl;}else{// Open the URL in a new tab\nwindow.open(targetUrl,\"_blank\",\"noopener noreferrer\");}}else{if(action.Action==\"Previous\"||action.Action==\"previous\"||action.ActionValue==\"Previous\"||action.ActionValue==\"Previous\"){handlePrevious();}else if(action.Action==\"Next\"||action.Action==\"next\"||action.ActionValue==\"Next\"||action.ActionValue==\"next\"){handleContinue();}else if(action.Action==\"Restart\"||action.ActionValue==\"Restart\"){var _savedGuideData$Guide13,_savedGuideData$Guide14;// Reset to the first step\nsetCurrentStep(1);// If there's a specific URL for the first step, navigate to it\nif(savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide13=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide13!==void 0&&(_savedGuideData$Guide14=_savedGuideData$Guide13[0])!==null&&_savedGuideData$Guide14!==void 0&&_savedGuideData$Guide14.ElementPath){const firstStepElement=getElementByXPath(savedGuideData.GuideStep[0].ElementPath);if(firstStepElement){firstStepElement.scrollIntoView({behavior:'smooth'});}}}}setOverlayValue(false);};useEffect(()=>{var _guideStep2,_guideStep2$Hotspot;if(guideStep!==null&&guideStep!==void 0&&(_guideStep2=guideStep[currentStep-1])!==null&&_guideStep2!==void 0&&(_guideStep2$Hotspot=_guideStep2.Hotspot)!==null&&_guideStep2$Hotspot!==void 0&&_guideStep2$Hotspot.ShowByDefault){// Show tooltip by default\nsetOpenTooltip(true);}},[guideStep===null||guideStep===void 0?void 0:guideStep[currentStep-1],currentStep,setOpenTooltip]);// Add effect to handle isHotspotPopupOpen prop changes\nuseEffect(()=>{if(isHotspotPopupOpen){var _toolTipGuideMetaData3,_toolTipGuideMetaData4,_savedGuideData$Guide15,_savedGuideData$Guide16,_savedGuideData$Guide17,_savedGuideData$Guide18;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData3=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData3!==void 0&&_toolTipGuideMetaData3.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData4=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData4===void 0?void 0:_toolTipGuideMetaData4.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide15=savedGuideData.GuideStep)===null||_savedGuideData$Guide15===void 0?void 0:(_savedGuideData$Guide16=_savedGuideData$Guide15[currentStep-1])===null||_savedGuideData$Guide16===void 0?void 0:_savedGuideData$Guide16.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide17=savedGuideData.GuideStep)===null||_savedGuideData$Guide17===void 0?void 0:(_savedGuideData$Guide18=_savedGuideData$Guide17[0])===null||_savedGuideData$Guide18===void 0?void 0:_savedGuideData$Guide18.Hotspot;// Only show tooltip by default if ShowByDefault is true\n// For \"Hovering Hotspot\", we'll wait for the hover event\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[isHotspotPopupOpen,toolTipGuideMetaData]);// Add effect to handle showHotspotenduser prop changes\nuseEffect(()=>{if(showHotspotenduser){var _toolTipGuideMetaData5,_toolTipGuideMetaData6,_savedGuideData$Guide19,_savedGuideData$Guide20,_savedGuideData$Guide21,_savedGuideData$Guide22;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData5=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData5!==void 0&&_toolTipGuideMetaData5.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData6=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData6===void 0?void 0:_toolTipGuideMetaData6.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide19=savedGuideData.GuideStep)===null||_savedGuideData$Guide19===void 0?void 0:(_savedGuideData$Guide20=_savedGuideData$Guide19[currentStep-1])===null||_savedGuideData$Guide20===void 0?void 0:_savedGuideData$Guide20.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide21=savedGuideData.GuideStep)===null||_savedGuideData$Guide21===void 0?void 0:(_savedGuideData$Guide22=_savedGuideData$Guide21[0])===null||_savedGuideData$Guide22===void 0?void 0:_savedGuideData$Guide22.Hotspot;// Only show tooltip by default if ShowByDefault is true\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[showHotspotenduser,toolTipGuideMetaData]);// Add a global click handler to detect clicks outside the hotspot to close the tooltip\nuseEffect(()=>{const handleGlobalClick=e=>{const hotspotElement=document.getElementById(\"hotspotBlink\");// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\nif(hotspotElement&&hotspotElement.contains(e.target)){return;}// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on clicks outside anymore\n};document.addEventListener(\"click\",handleGlobalClick);return()=>{document.removeEventListener(\"click\",handleGlobalClick);};},[toolTipGuideMetaData]);// Check if content needs scrolling with improved detection\nuseEffect(()=>{const checkScrollNeeded=()=>{if(contentRef.current){// Force a reflow to get accurate measurements\ncontentRef.current.style.height='auto';const contentHeight=contentRef.current.scrollHeight;const containerHeight=320;// max-height value\nconst shouldScroll=contentHeight>containerHeight;setNeedsScrolling(shouldScroll);// Force update scrollbar\nif(scrollbarRef.current){// Try multiple methods to update the scrollbar\nif(scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}// Force re-initialization if needed\nsetTimeout(()=>{if(scrollbarRef.current&&scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}},10);}}};checkScrollNeeded();const timeouts=[setTimeout(checkScrollNeeded,50),setTimeout(checkScrollNeeded,100),setTimeout(checkScrollNeeded,200),setTimeout(checkScrollNeeded,500)];let resizeObserver=null;let mutationObserver=null;if(contentRef.current&&window.ResizeObserver){resizeObserver=new ResizeObserver(()=>{setTimeout(checkScrollNeeded,10);});resizeObserver.observe(contentRef.current);}if(contentRef.current&&window.MutationObserver){mutationObserver=new MutationObserver(()=>{setTimeout(checkScrollNeeded,10);});mutationObserver.observe(contentRef.current,{childList:true,subtree:true,attributes:true,attributeFilter:['style','class']});}return()=>{timeouts.forEach(clearTimeout);if(resizeObserver){resizeObserver.disconnect();}if(mutationObserver){mutationObserver.disconnect();}};},[currentStep]);// We no longer need the persistent monitoring effect since we want the tooltip\n// to close when the mouse leaves the hotspot\nfunction getAlignment(alignment){switch(alignment){case\"start\":return\"flex-start\";case\"end\":return\"flex-end\";case\"center\":default:return\"center\";}}const getCanvasPosition=function(){let position=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"center-center\";switch(position){case\"bottom-left\":return{top:\"auto !important\"};case\"bottom-right\":return{top:\"auto !important\"};case\"bottom-center\":return{top:\"auto !important\"};case\"center-center\":return{top:\"25% !important\"};case\"left-center\":return{top:imageUrl===\"\"?\"40% !important\":\"20% !important\"};case\"right-center\":return{top:\"10% !important\"};case\"top-left\":return{top:\"10% !important\"};case\"top-right\":return{top:\"10% !important\"};case\"top-center\":return{top:\"9% !important\"};default:return{top:\"25% !important\"};}};// function to get the correct property value based on tour vs normal hotspot\nconst getHotspotProperty=(propName,hotspotPropData,hotspotData)=>{if(selectedTemplateTour===\"Hotspot\"){// For tour hotspots, use saved data first, fallback to metadata\nswitch(propName){case'PulseAnimation':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.PulseAnimation)!==undefined?hotspotData.PulseAnimation:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.PulseAnimation;case'StopAnimation':// Always use stopAnimationUponInteraction for consistency\nreturn(hotspotData===null||hotspotData===void 0?void 0:hotspotData.stopAnimationUponInteraction)!==undefined?hotspotData.stopAnimationUponInteraction:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;case'ShowUpon':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowUpon)!==undefined?hotspotData.ShowUpon:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowUpon;case'ShowByDefault':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowByDefault)!==undefined?hotspotData.ShowByDefault:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowByDefault;default:return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}}else{// For normal hotspots, use metadata\nif(propName==='StopAnimation'){return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;}return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}};const applyHotspotStyles=(hotspot,hotspotPropData,hotspotData,left,top)=>{hotspot.style.position=\"absolute\";hotspot.style.left=`${left}px`;hotspot.style.top=`${top}px`;hotspot.style.width=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;// Default size if not provided\nhotspot.style.height=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;hotspot.style.backgroundColor=hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Color;hotspot.style.borderRadius=\"50%\";hotspot.style.zIndex=\"auto !important\";// Increased z-index\nhotspot.style.transition=\"none\";hotspot.style.pointerEvents=\"auto\";// Ensure clicks are registered\nhotspot.innerHTML=\"\";if((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Info\"||(hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Question\"){const textSpan=document.createElement(\"span\");textSpan.innerText=hotspotPropData.Type===\"Info\"?\"i\":\"?\";textSpan.style.color=\"white\";textSpan.style.fontSize=\"14px\";textSpan.style.fontWeight=\"bold\";textSpan.style.fontStyle=hotspotPropData.Type===\"Info\"?\"italic\":\"normal\";textSpan.style.display=\"flex\";textSpan.style.alignItems=\"center\";textSpan.style.justifyContent=\"center\";textSpan.style.width=\"100%\";textSpan.style.height=\"100%\";hotspot.appendChild(textSpan);}// Apply animation class if needed\n// Track if pulse has been stopped by hover\nconst pulseAnimationEnabled=getHotspotProperty('PulseAnimation',hotspotPropData,hotspotData);const shouldPulse=selectedTemplateTour===\"Hotspot\"?pulseAnimationEnabled!==false&&!hotspot._pulseStopped:hotspotPropData&&pulseAnimationsH&&!hotspot._pulseStopped;if(shouldPulse){hotspot.classList.add(\"pulse-animation\");hotspot.classList.remove(\"pulse-animation-removed\");}else{hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");}// Ensure the hotspot is visible and clickable\nhotspot.style.display=\"flex\";hotspot.style.pointerEvents=\"auto\";// No need for separate animation control functions here\n// Animation will be controlled directly in the event handlers\n// Set initial state of openTooltip based on ShowByDefault and ShowUpon\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{// If not showing by default, only show based on interaction type\n//setOpenTooltip(false);\n}// Only clone and replace if the hotspot doesn't have event listeners already\n// This prevents losing the _pulseStopped state unnecessarily\nif(!hotspot.hasAttribute('data-listeners-attached')){const newHotspot=hotspot.cloneNode(true);// Copy the _pulseStopped property if it exists\nif(hotspot._pulseStopped!==undefined){newHotspot._pulseStopped=hotspot._pulseStopped;}if(hotspot.parentNode){hotspot.parentNode.replaceChild(newHotspot,hotspot);hotspot=newHotspot;}}// Ensure pointer events are enabled\nhotspot.style.pointerEvents=\"auto\";// Define combined event handlers that handle both animation and tooltip\nconst showUpon=getHotspotProperty('ShowUpon',hotspotPropData,hotspotData);const handleHover=e=>{e.stopPropagation();console.log(\"Hover detected on hotspot\");// Show tooltip if ShowUpon is \"Hovering Hotspot\"\nif(showUpon===\"Hovering Hotspot\"){// Set openTooltip to true when hovering\nsetOpenTooltip(true);// Call the passed hover handler if it exists\nif(typeof handleHotspotHover===\"function\"){handleHotspotHover();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};const handleMouseOut=e=>{e.stopPropagation();// Hide tooltip when mouse leaves the hotspot\n// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showUpon===\"Hovering Hotspot\"&&!showByDefault){// setOpenTooltip(false);\n}};const handleClick=e=>{e.stopPropagation();console.log(\"Click detected on hotspot\");// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\nif(showUpon===\"Clicking Hotspot\"||!showUpon){// Toggle the tooltip state\nsetOpenTooltip(!openTooltip);// Call the passed click handler if it exists\nif(typeof handleHotspotClick===\"function\"){handleHotspotClick();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};// Add appropriate event listeners based on ShowUpon property\nif(!hotspot.hasAttribute('data-listeners-attached')){if(showUpon===\"Hovering Hotspot\"){// For hover interaction\nhotspot.addEventListener(\"mouseover\",handleHover);hotspot.addEventListener(\"mouseout\",handleMouseOut);// Also add click handler for better user experience\nhotspot.addEventListener(\"click\",handleClick);}else{// For click interaction (default)\nhotspot.addEventListener(\"click\",handleClick);}// Mark that listeners have been attached\nhotspot.setAttribute('data-listeners-attached','true');}};useEffect(()=>{let element;let steps;const fetchGuideDetails=async()=>{try{var _savedGuideData$Guide23,_savedGuideData$Guide24,_steps,_steps$;//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\nsteps=(savedGuideData===null||savedGuideData===void 0?void 0:savedGuideData.GuideStep)||[];// For tour hotspots, use the current step's element path\nconst elementPath=selectedTemplateTour===\"Hotspot\"&&savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide23=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide23!==void 0&&(_savedGuideData$Guide24=_savedGuideData$Guide23[currentStep-1])!==null&&_savedGuideData$Guide24!==void 0&&_savedGuideData$Guide24.ElementPath?savedGuideData.GuideStep[currentStep-1].ElementPath:((_steps=steps)===null||_steps===void 0?void 0:(_steps$=_steps[0])===null||_steps$===void 0?void 0:_steps$.ElementPath)||\"\";element=getElementByXPath(elementPath||\"\");setTargetElement(element);if(element){// element.style.outline = \"2px solid red\";\n}// Check if this is a hotspot scenario (normal or tour)\nconst isHotspotScenario=selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\"||title===\"Hotspot\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Hotspot\";if(isHotspotScenario){var _toolTipGuideMetaData7,_toolTipGuideMetaData8,_hotspotPropData,_hotspotPropData2,_hotspotPropData3,_hotspotPropData4;// Get hotspot properties - prioritize tour data for tour hotspots\nlet hotspotPropData;let hotspotData;if(selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData7=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData7!==void 0&&_toolTipGuideMetaData7.hotspots){var _savedGuideData$Guide25,_savedGuideData$Guide26;// Tour hotspot - use current step metadata\nhotspotPropData=toolTipGuideMetaData[currentStep-1].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide25=savedGuideData.GuideStep)===null||_savedGuideData$Guide25===void 0?void 0:(_savedGuideData$Guide26=_savedGuideData$Guide25[currentStep-1])===null||_savedGuideData$Guide26===void 0?void 0:_savedGuideData$Guide26.Hotspot;}else if(toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData8=toolTipGuideMetaData[0])!==null&&_toolTipGuideMetaData8!==void 0&&_toolTipGuideMetaData8.hotspots){var _savedGuideData$Guide27,_savedGuideData$Guide28;// Normal hotspot - use first metadata entry\nhotspotPropData=toolTipGuideMetaData[0].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide27=savedGuideData.GuideStep)===null||_savedGuideData$Guide27===void 0?void 0:(_savedGuideData$Guide28=_savedGuideData$Guide27[0])===null||_savedGuideData$Guide28===void 0?void 0:_savedGuideData$Guide28.Hotspot;}else{var _savedGuideData$Guide29,_savedGuideData$Guide30;// Fallback to default values for tour hotspots without metadata\nhotspotPropData={XPosition:\"4\",YPosition:\"4\",Type:\"Question\",Color:\"yellow\",Size:\"16\",PulseAnimation:true,stopAnimationUponInteraction:true,ShowUpon:\"Hovering Hotspot\",ShowByDefault:false};hotspotData=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide29=savedGuideData.GuideStep)===null||_savedGuideData$Guide29===void 0?void 0:(_savedGuideData$Guide30=_savedGuideData$Guide29[currentStep-1])===null||_savedGuideData$Guide30===void 0?void 0:_savedGuideData$Guide30.Hotspot)||{};}const xOffset=parseFloat(((_hotspotPropData=hotspotPropData)===null||_hotspotPropData===void 0?void 0:_hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat(((_hotspotPropData2=hotspotPropData)===null||_hotspotPropData2===void 0?void 0:_hotspotPropData2.YPosition)||\"4\");const currentHotspotSize=parseFloat(((_hotspotPropData3=hotspotPropData)===null||_hotspotPropData3===void 0?void 0:_hotspotPropData3.Size)||\"30\");// Update hotspot size state\nsetHotspotSize(currentHotspotSize);let left,top;if(element){const rect=element.getBoundingClientRect();left=rect.x+xOffset;top=rect.y+(yOffset>0?-yOffset:Math.abs(yOffset));// Calculate popup position below the hotspot\nconst popupPos=calculatePopupPosition(rect,currentHotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}// Check if hotspot already exists, preserve it to maintain _pulseStopped state\nconst existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){hotspot=existingHotspot;// Don't reset _pulseStopped if it already exists\n}else{// Create new hotspot only if it doesn't exist\nhotspot=document.createElement(\"div\");hotspot.id=\"hotspotBlink\";// Fixed ID for easier reference\nhotspot._pulseStopped=false;// Set only on creation\ndocument.body.appendChild(hotspot);}hotspot.style.cursor=\"pointer\";hotspot.style.pointerEvents=\"auto\";// Ensure it can receive mouse events\n// Make sure the hotspot is visible and clickable\nhotspot.style.zIndex=\"9999\";// If ShowByDefault is true, set openTooltip to true immediately\nif((_hotspotPropData4=hotspotPropData)!==null&&_hotspotPropData4!==void 0&&_hotspotPropData4.ShowByDefault){setOpenTooltip(true);}// Set styles first\napplyHotspotStyles(hotspot,hotspotPropData,hotspotData,left,top);// Set initial tooltip visibility based on ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{//setOpenTooltip(false);\n}// We don't need to add event listeners here as they're already added in applyHotspotStyles\n}}catch(error){console.error(\"Error in fetchGuideDetails:\",error);}};fetchGuideDetails();return()=>{const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.onclick=null;existingHotspot.onmouseover=null;existingHotspot.onmouseout=null;}};},[savedGuideData,toolTipGuideMetaData,isHotspotPopupOpen,showHotspotenduser,selectedTemplateTour,currentStep// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n]);const enableProgress=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide31=savedGuideData.GuideStep)===null||_savedGuideData$Guide31===void 0?void 0:(_savedGuideData$Guide32=_savedGuideData$Guide31[0])===null||_savedGuideData$Guide32===void 0?void 0:(_savedGuideData$Guide33=_savedGuideData$Guide32.Tooltip)===null||_savedGuideData$Guide33===void 0?void 0:_savedGuideData$Guide33.EnableProgress)||false;function getProgressTemplate(selectedOption){var _savedGuideData$Guide34,_savedGuideData$Guide35,_savedGuideData$Guide36;if(selectedOption===1){return\"dots\";}else if(selectedOption===2){return\"linear\";}else if(selectedOption===3){return\"BreadCrumbs\";}else if(selectedOption===4){return\"breadcrumbs\";}return(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide34=savedGuideData.GuideStep)===null||_savedGuideData$Guide34===void 0?void 0:(_savedGuideData$Guide35=_savedGuideData$Guide34[0])===null||_savedGuideData$Guide35===void 0?void 0:(_savedGuideData$Guide36=_savedGuideData$Guide35.Tooltip)===null||_savedGuideData$Guide36===void 0?void 0:_savedGuideData$Guide36.ProgressTemplate)||\"dots\";}const progressTemplate=getProgressTemplate(selectedOption);const renderProgress=()=>{if(!enableProgress)return null;if(progressTemplate===\"dots\"){return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:totalSteps,position:\"static\",activeStep:currentStep-1,sx:{backgroundColor:\"transparent\",position:\"inherit !important\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// Active dot\n}},backButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}}),nextButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}})});}if(progressTemplate===\"BreadCrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"center\",gap:\"5px\",padding:\"8px\"},children:Array.from({length:totalSteps}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:\"14px\",height:\"4px\",backgroundColor:index===currentStep-1?ProgressColor:\"#e0e0e0\",// Active color and inactive color\nborderRadius:\"100px\"}},index))});}if(progressTemplate===\"breadcrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"flex-start\"},children:/*#__PURE__*/_jsxs(Typography,{sx:{padding:\"8px\",color:ProgressColor},children:[\"Step \",currentStep,\" of \",totalSteps]})});}if(progressTemplate===\"linear\"){return/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{height:\"6px\",borderRadius:\"20px\",margin:\"6px 10px\",\"& .MuiLinearProgress-bar\":{backgroundColor:ProgressColor// progress bar color\n}}})})});}return null;};return/*#__PURE__*/_jsxs(_Fragment,{children:[targetElement&&/*#__PURE__*/_jsx(\"div\",{children:openTooltip&&/*#__PURE__*/_jsxs(Popover,{open:Boolean(popupPosition)||Boolean(anchorEl),anchorEl:anchorEl,onClose:()=>{// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on Popover close events\n},anchorOrigin:anchorOrigin,transformOrigin:transformOrigin,anchorReference:\"anchorPosition\",anchorPosition:popupPosition?{// Use smart positioning if available, otherwise use legacy positioning\ntop:optimalPosition?optimalPosition.top:popupPosition.top+10+(parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\"))),left:optimalPosition?optimalPosition.left:popupPosition.left+10+parseFloat(tooltipXaxis||\"0\")}:undefined,sx:{\"pointer-events\":anchorEl?\"auto\":\"auto\",'& .MuiPaper-root:not(.MuiMobileStepper-root)':{zIndex:1000,...canvasStyle,// Smart positioning overrides legacy positioning when available\n...(optimalPosition?{}:getCanvasPosition((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\")),// Only apply legacy positioning if smart positioning is not available\n...(optimalPosition?{}:{top:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.top)||0)+(tooltipYaxis&&tooltipYaxis!='undefined'?parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\")):0)}px !important`,left:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.left)||0)+(tooltipXaxis&&tooltipXaxis!='undefined'?parseFloat(tooltipXaxis)||0:0)}px !important`}),overflow:\"hidden\"}},disableScrollLock:true,children:[/*#__PURE__*/_jsx(\"div\",{style:{placeContent:\"end\",display:\"flex\"},children:(modalProperties===null||modalProperties===void 0?void 0:modalProperties.DismissOption)&&/*#__PURE__*/_jsx(IconButton,{onClick:()=>{// Only close if explicitly requested by user clicking the close button\n//setOpenTooltip(false);\n},sx:{position:\"fixed\",boxShadow:\"rgba(0, 0, 0, 0.06) 0px 4px 8px\",left:\"auto\",right:\"auto\",margin:\"-15px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999999\",borderRadius:\"50px\",padding:\"5px !important\"},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:1,color:\"#000\"}})})}),/*#__PURE__*/_jsx(PerfectScrollbar,{ref:scrollbarRef,style:{maxHeight:hasOnlyButtons()||hasOnlyText()?\"auto\":\"400px\"},options:{suppressScrollY:!needsScrolling,suppressScrollX:true,wheelPropagation:false,swipeEasing:true,minScrollbarLength:20,scrollingThreshold:1000,scrollYMarginOffset:0},children:/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:hasOnlyButtons()||hasOnlyText()?\"auto\":\"400px\",overflow:hasOnlyButtons()||hasOnlyText()?\"visible\":\"hidden auto\",width:hasOnlyButtons()||hasOnlyText()?\"auto\":undefined,margin:hasOnlyButtons()?\"0\":undefined},children:/*#__PURE__*/_jsxs(Box,{style:{padding:hasOnlyButtons()?\"0\":hasOnlyText()?\"0\":(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Padding)||\"10px\",height:hasOnlyButtons()?\"auto\":sectionHeight,width:hasOnlyButtons()||hasOnlyText()?\"auto\":undefined,margin:hasOnlyButtons()?\"0\":undefined},children:[/*#__PURE__*/_jsxs(Box,{ref:contentRef,display:\"flex\",flexDirection:\"column\",flexWrap:\"wrap\",justifyContent:\"center\",sx:{width:hasOnlyText()?\"auto\":\"100%\",padding:hasOnlyText()?\"0\":undefined},children:[imageProperties===null||imageProperties===void 0?void 0:imageProperties.map(imageProp=>imageProp.CustomImage.map((customImg,imgIndex)=>/*#__PURE__*/_jsx(Box,{component:\"img\",src:customImg.Url,alt:customImg.AltText||\"Image\",sx:{maxHeight:imageProp.MaxImageHeight||customImg.MaxImageHeight||\"500px\",textAlign:imageProp.Alignment||\"center\",objectFit:customImg.Fit||\"contain\",//  width: \"500px\",\nheight:`${customImg.SectionHeight||250}px`,background:customImg.BackgroundColor||\"#ffffff\",margin:\"10px 0\"},onClick:()=>{if(imageProp.Hyperlink){const targetUrl=imageProp.Hyperlink;window.open(targetUrl,\"_blank\",\"noopener noreferrer\");}},style:{cursor:imageProp.Hyperlink?\"pointer\":\"default\"}},`${imageProp.Id}-${imgIndex}`))),textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.map((textField,index)=>{var _textField$TextProper,_textField$TextProper2;return textField.Text&&/*#__PURE__*/_jsx(Typography,{className:\"qadpt-preview\",// Use a unique key, either Id or index\nsx:{textAlign:((_textField$TextProper=textField.TextProperties)===null||_textField$TextProper===void 0?void 0:_textField$TextProper.TextFormat)||textStyle.textAlign,color:((_textField$TextProper2=textField.TextProperties)===null||_textField$TextProper2===void 0?void 0:_textField$TextProper2.TextColor)||textStyle.color,whiteSpace:\"pre-wrap\",wordBreak:\"break-word\",padding:\"0 5px\"},dangerouslySetInnerHTML:renderHtmlSnippet(textField.Text)// Render the raw HTML\n},textField.Id||index);})]}),Object.keys(groupedButtons).map(containerId=>{var _groupedButtons$conta,_groupedButtons$conta2;return/*#__PURE__*/_jsx(Box,{ref:buttonContainerRef,sx:{display:\"flex\",justifyContent:getAlignment((_groupedButtons$conta=groupedButtons[containerId][0])===null||_groupedButtons$conta===void 0?void 0:_groupedButtons$conta.Alignment),flexWrap:\"wrap\",margin:hasOnlyButtons()?0:\"5px 0\",backgroundColor:(_groupedButtons$conta2=groupedButtons[containerId][0])===null||_groupedButtons$conta2===void 0?void 0:_groupedButtons$conta2.BackgroundColor,padding:hasOnlyButtons()?\"4px\":\"5px 0\",width:hasOnlyButtons()?\"auto\":\"100%\",borderRadius:hasOnlyButtons()?\"15px\":undefined},children:groupedButtons[containerId].map((button,index)=>{var _button$ButtonPropert,_button$ButtonPropert2,_button$ButtonPropert3,_button$ButtonPropert4,_button$ButtonPropert5,_button$ButtonPropert6,_button$ButtonPropert7;return/*#__PURE__*/_jsx(Button,{onClick:()=>handleButtonAction(button.ButtonAction),variant:\"contained\",sx:{marginRight:hasOnlyButtons()?\"5px\":\"13px\",margin:hasOnlyButtons()?\"4px\":\"0 5px 5px 5px\",backgroundColor:((_button$ButtonPropert=button.ButtonProperties)===null||_button$ButtonPropert===void 0?void 0:_button$ButtonPropert.ButtonBackgroundColor)||\"#007bff\",color:((_button$ButtonPropert2=button.ButtonProperties)===null||_button$ButtonPropert2===void 0?void 0:_button$ButtonPropert2.ButtonTextColor)||\"#fff\",border:((_button$ButtonPropert3=button.ButtonProperties)===null||_button$ButtonPropert3===void 0?void 0:_button$ButtonPropert3.ButtonBorderColor)||\"transparent\",fontSize:((_button$ButtonPropert4=button.ButtonProperties)===null||_button$ButtonPropert4===void 0?void 0:_button$ButtonPropert4.FontSize)||\"15px\",width:((_button$ButtonPropert5=button.ButtonProperties)===null||_button$ButtonPropert5===void 0?void 0:_button$ButtonPropert5.Width)||\"auto\",padding:hasOnlyButtons()?\"var(--button-padding) !important\":\"4px 8px\",lineHeight:hasOnlyButtons()?\"var(--button-lineheight)\":\"normal\",textTransform:\"none\",borderRadius:((_button$ButtonPropert6=button.ButtonProperties)===null||_button$ButtonPropert6===void 0?void 0:_button$ButtonPropert6.BorderRadius)||\"8px\",minWidth:hasOnlyButtons()?\"fit-content\":undefined,boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{backgroundColor:((_button$ButtonPropert7=button.ButtonProperties)===null||_button$ButtonPropert7===void 0?void 0:_button$ButtonPropert7.ButtonBackgroundColor)||\"#007bff\",// Keep the same background color on hover\nopacity:0.9,// Slightly reduce opacity on hover for visual feedback\nboxShadow:\"none !important\"// Remove box shadow in hover state\n}},children:button.ButtonName},index);})},containerId);})]})})},`scrollbar-${needsScrolling}`),enableProgress&&totalSteps>1&&selectedTemplate===\"Tour\"&&/*#__PURE__*/_jsx(Box,{children:renderProgress()}),\" \"]})}),/*#__PURE__*/_jsx(\"style\",{children:`\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `})]});};export default HotspotPreview;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "getViewportBounds", "top", "left", "right", "window", "innerWidth", "bottom", "innerHeight", "getAvailableSpace", "elementRect", "viewport", "calculateOptimalPlacement", "popupDimensions", "space", "width", "height", "padding", "fitsTop", "fitsBottom", "fitsLeft", "fitsRight", "maxSpace", "Math", "max", "calculatePopupPositionWithBounds", "placement", "hotspotOffset", "arguments", "length", "undefined", "x", "y", "hotspotSize", "beaconSpacing", "anchor<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "hotspotCenterX", "hotspotCenterY", "vertical", "horizontal", "min", "scrollY", "scrollX", "HotspotPreview", "_ref", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "optimalPosition", "setOptimalPosition", "setPopupDimensions", "dynamicWidth", "setDynamicWidth", "setHotspotSize", "contentRef", "buttonContainerRef", "popupRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "GuideStep", "<PERSON>ement<PERSON><PERSON>", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "calculatePopupPosition", "xOffset", "yOffset", "hotspotLeft", "hotspotTop", "dynamicOffsetX", "dynamicOffsetY", "element", "dimensions", "calculateOptimalDimensions", "updateSmartPosition", "_guideStep", "style", "backgroundColor", "rect", "getBoundingClientRect", "setOverlayValue", "handleContinue", "renderNextPopup", "existingHotspot", "getElementById", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "Hotspot", "getAnchorAndTransformOrigins", "position", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "replace", "_match", "p1", "p2", "p3", "hasOnlyButtons", "hasImage", "some", "prop", "CustomImage", "img", "Url", "hasText", "field", "Text", "trim", "hasButtons", "hasOnlyText", "_contentRef$current2", "_buttonContainerRef$c2", "<PERSON><PERSON><PERSON>", "parseInt", "estimateContentHeight", "_contentRef$current", "_buttonContainerRef$c", "contentWidth", "current", "scrollWidth", "buttonWidth", "optimalWidth", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "finalWidth", "for<PERSON>ach", "imageProp", "SectionHeight", "textLength", "estimatedLines", "ceil", "enableProgress", "minHeight", "maxHeight", "requestAnimationFrame", "newDimensions", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "hotspotPropData", "hotspots", "parseFloat", "XPosition", "YPosition", "optimalPos", "handleViewportChange", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "sectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "firstStepElement", "scrollIntoView", "behavior", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_savedGuideData$Guide15", "_savedGuideData$Guide16", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "hotspotData", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "clearTimeout", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "getHotspotProperty", "propName", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Size", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "console", "log", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_steps", "_steps$", "elementPath", "isHotspotScenario", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "currentHotspotSize", "abs", "popupPos", "id", "body", "cursor", "error", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "boxShadow", "background", "border", "zoom", "ref", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "Padding", "flexDirection", "flexWrap", "customImg", "imgIndex", "component", "src", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "className", "TextFormat", "whiteSpace", "wordBreak", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "marginRight", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n// Viewport boundary detection utilities\r\ninterface ViewportBounds {\r\n\ttop: number;\r\n\tleft: number;\r\n\tright: number;\r\n\tbottom: number;\r\n}\r\n\r\ninterface PopupDimensions {\r\n\twidth: number;\r\n\theight: number;\r\n}\r\n\r\ninterface OptimalPosition {\r\n\ttop: number;\r\n\tleft: number;\r\n\tplacement: 'top' | 'bottom' | 'left' | 'right';\r\n\tanchorOrigin: PopoverOrigin;\r\n\ttransformOrigin: PopoverOrigin;\r\n}\r\n\r\n// Utility function to get viewport bounds\r\nconst getViewportBounds = (): ViewportBounds => ({\r\n\ttop: 0,\r\n\tleft: 0,\r\n\tright: window.innerWidth,\r\n\tbottom: window.innerHeight\r\n});\r\n\r\n// Utility function to calculate available space around an element\r\nconst getAvailableSpace = (elementRect: DOMRect) => {\r\n\tconst viewport = getViewportBounds();\r\n\treturn {\r\n\t\ttop: elementRect.top - viewport.top,\r\n\t\tbottom: viewport.bottom - elementRect.bottom,\r\n\t\tleft: elementRect.left - viewport.left,\r\n\t\tright: viewport.right - elementRect.right\r\n\t};\r\n};\r\n\r\n// Utility function to determine optimal placement based on available space\r\nconst calculateOptimalPlacement = (\r\n\telementRect: DOMRect,\r\n\tpopupDimensions: PopupDimensions\r\n): 'top' | 'bottom' | 'left' | 'right' => {\r\n\tconst space = getAvailableSpace(elementRect);\r\n\tconst { width, height } = popupDimensions;\r\n\r\n\t// Add some padding for better UX\r\n\tconst padding = 20;\r\n\r\n\t// Check if popup fits in each direction\r\n\tconst fitsTop = space.top >= height + padding;\r\n\tconst fitsBottom = space.bottom >= height + padding;\r\n\tconst fitsLeft = space.left >= width + padding;\r\n\tconst fitsRight = space.right >= width + padding;\r\n\r\n\t// Prefer bottom first (most common), then top, then sides\r\n\tif (fitsBottom) return 'bottom';\r\n\tif (fitsTop) return 'top';\r\n\tif (fitsRight) return 'right';\r\n\tif (fitsLeft) return 'left';\r\n\r\n\t// If nothing fits perfectly, choose the side with most space\r\n\tconst maxSpace = Math.max(space.top, space.bottom, space.left, space.right);\r\n\tif (maxSpace === space.bottom) return 'bottom';\r\n\tif (maxSpace === space.top) return 'top';\r\n\tif (maxSpace === space.right) return 'right';\r\n\treturn 'left';\r\n};\r\n\r\n// Utility function to calculate popup position with boundary detection\r\nconst calculatePopupPositionWithBounds = (\r\n\telementRect: DOMRect,\r\n\tpopupDimensions: PopupDimensions,\r\n\tplacement: 'top' | 'bottom' | 'left' | 'right',\r\n\thotspotOffset: { x: number; y: number } = { x: 0, y: 0 },\r\n\thotspotSize: number = 30\r\n): OptimalPosition => {\r\n\tconst viewport = getViewportBounds();\r\n\tconst padding = 10;\r\n\tconst beaconSpacing = 10; // Exact spacing from beacon edge\r\n\r\n\tlet top: number;\r\n\tlet left: number;\r\n\tlet anchorOrigin: PopoverOrigin;\r\n\tlet transformOrigin: PopoverOrigin;\r\n\r\n\t// Calculate hotspot center position\r\n\tconst hotspotCenterX = elementRect.left + hotspotOffset.x + (hotspotSize / 2);\r\n\tconst hotspotCenterY = elementRect.top + hotspotOffset.y + (hotspotSize / 2);\r\n\r\n\tswitch (placement) {\r\n\t\tcase 'top':\r\n\t\t\ttop = hotspotCenterY - (hotspotSize / 2) - popupDimensions.height - beaconSpacing;\r\n\t\t\tleft = hotspotCenterX - (popupDimensions.width / 2);\r\n\t\t\tanchorOrigin = { vertical: 'top', horizontal: 'center' };\r\n\t\t\ttransformOrigin = { vertical: 'bottom', horizontal: 'center' };\r\n\t\t\tbreak;\r\n\r\n\t\tcase 'bottom':\r\n\t\t\ttop = hotspotCenterY + (hotspotSize / 2) + beaconSpacing;\r\n\t\t\tleft = hotspotCenterX - (popupDimensions.width / 2);\r\n\t\t\tanchorOrigin = { vertical: 'bottom', horizontal: 'center' };\r\n\t\t\ttransformOrigin = { vertical: 'top', horizontal: 'center' };\r\n\t\t\tbreak;\r\n\r\n\t\tcase 'left':\r\n\t\t\ttop = hotspotCenterY - (popupDimensions.height / 2);\r\n\t\t\tleft = hotspotCenterX - (hotspotSize / 2) - popupDimensions.width - beaconSpacing;\r\n\t\t\tanchorOrigin = { vertical: 'center', horizontal: 'left' };\r\n\t\t\ttransformOrigin = { vertical: 'center', horizontal: 'right' };\r\n\t\t\tbreak;\r\n\r\n\t\tcase 'right':\r\n\t\t\ttop = hotspotCenterY - (popupDimensions.height / 2);\r\n\t\t\tleft = hotspotCenterX + (hotspotSize / 2) + beaconSpacing;\r\n\t\t\tanchorOrigin = { vertical: 'center', horizontal: 'right' };\r\n\t\t\ttransformOrigin = { vertical: 'center', horizontal: 'left' };\r\n\t\t\tbreak;\r\n\t}\r\n\r\n\t// Ensure popup stays within viewport bounds\r\n\tleft = Math.max(padding, Math.min(left, viewport.right - popupDimensions.width - padding));\r\n\ttop = Math.max(padding, Math.min(top, viewport.bottom - popupDimensions.height - padding));\r\n\r\n\treturn {\r\n\t\ttop: top + window.scrollY,\r\n\t\tleft: left + window.scrollX,\r\n\t\tplacement,\r\n\t\tanchorOrigin,\r\n\t\ttransformOrigin\r\n\t};\r\n};\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// Enhanced state management for smart positioning\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [optimalPosition, setOptimalPosition] = useState<OptimalPosition | null>(null);\r\n\tconst [popupDimensions, setPopupDimensions] = useState<PopupDimensions>({ width: 300, height: 200 });\r\n\tconst [dynamicWidth, setDynamicWidth] = useState<string | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30);\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tconst popupRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\tconst node = result.singleNodeValue;\r\n\t\tif (node instanceof HTMLElement) {\r\n\t\t\treturn node;\r\n\t\t} else if (node?.parentElement) {\r\n\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t} else {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to calculate popup position below the hotspot\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Position popup below the hotspot for better user experience\r\n\t\tconst dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot\r\n\t\tconst dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing\r\n\r\n\t\treturn {\r\n\t\t\ttop: hotspotTop + window.scrollY + dynamicOffsetY,\r\n\t\t\tleft: hotspotLeft + window.scrollX + dynamicOffsetX\r\n\t\t};\r\n\t};\r\n\t// Initialize smart positioning when xpath changes\r\n\tuseEffect(() => {\r\n\t\tif (xpath) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst dimensions = calculateOptimalDimensions();\r\n\t\t\t\tsetPopupDimensions(dimensions);\r\n\t\t\t\tupdateSmartPosition(element, dimensions);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\t// setTargetElement(element);\r\n\t\tif (element) {\r\n\t\t}\r\n\t}, [savedGuideData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Update popup position when target element changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY,\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [guideStep, currentStep]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\t// Use smart positioning if available, otherwise fall back to canvas properties\r\n\tconst { anchorOrigin, transformOrigin } = optimalPosition\r\n\t\t? { anchorOrigin: optimalPosition.anchorOrigin, transformOrigin: optimalPosition.transformOrigin }\r\n\t\t: getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\t// Helper function to check if popup has only buttons (no text or images)\r\n\tconst hasOnlyButtons = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasButtons && !hasImage && !hasText;\r\n\t};\r\n\r\n\t// Helper function to check if popup has only text (no buttons or images)\r\n\tconst hasOnlyText = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasText && !hasImage && !hasButtons;\r\n\t};\r\n\r\n\t// Enhanced function to calculate optimal width and height based on content\r\n\tconst calculateOptimalDimensions = (): PopupDimensions => {\r\n\t\t// If we have a fixed width from canvas settings and not a compact popup, use that\r\n\t\tif (canvasProperties?.Width && !hasOnlyButtons() && !hasOnlyText()) {\r\n\t\t\tconst width = parseInt(canvasProperties.Width);\r\n\t\t\treturn {\r\n\t\t\t\twidth: width,\r\n\t\t\t\theight: estimateContentHeight(width)\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// For popups with only buttons or only text, calculate minimal dimensions\r\n\t\tif (hasOnlyButtons() || hasOnlyText()) {\r\n\t\t\tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t\t\tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\t\t\tconst width = Math.max(contentWidth, buttonWidth, 150); // Minimum 150px\r\n\r\n\t\t\treturn {\r\n\t\t\t\twidth: width + 40, // Add padding\r\n\t\t\t\theight: estimateContentHeight(width + 40)\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Get the width of content and button container\r\n\t\tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t\tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\r\n\t\t// Use the larger of the two, with some minimum and maximum constraints\r\n\t\tconst optimalWidth = Math.max(contentWidth, buttonWidth);\r\n\r\n\t\t// Add some padding to ensure text has room to wrap naturally\r\n\t\tconst paddedWidth = optimalWidth + 40; // 20px padding on each side\r\n\r\n\t\t// Ensure width is between reasonable bounds\r\n\t\tconst minWidth = 250; // Minimum width\r\n\t\tconst maxWidth = Math.min(800, window.innerWidth * 0.9); // Max 90% of viewport width\r\n\r\n\t\tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\r\n\r\n\t\treturn {\r\n\t\t\twidth: finalWidth,\r\n\t\t\theight: estimateContentHeight(finalWidth)\r\n\t\t};\r\n\t};\r\n\r\n\t// Helper function to estimate content height\r\n\tconst estimateContentHeight = (width: number): number => {\r\n\t\tlet height = 0;\r\n\r\n\t\t// Add height for images\r\n\t\tif (imageProperties && imageProperties.length > 0) {\r\n\t\t\timageProperties.forEach((imageProp: any) => {\r\n\t\t\t\tif (imageProp.CustomImage && imageProp.CustomImage.length > 0) {\r\n\t\t\t\t\timageProp.CustomImage.forEach((img: any) => {\r\n\t\t\t\t\t\theight += parseInt(img.SectionHeight || '250') + 20; // Add margin\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// Add height for text (rough estimation)\r\n\t\tif (textFieldProperties && textFieldProperties.length > 0) {\r\n\t\t\ttextFieldProperties.forEach((field: any) => {\r\n\t\t\t\tif (field.Text && field.Text.trim() !== '') {\r\n\t\t\t\t\t// Rough estimation: 20px per line, assuming ~50 characters per line\r\n\t\t\t\t\tconst textLength = field.Text.length;\r\n\t\t\t\t\tconst estimatedLines = Math.ceil(textLength / (width / 10)); // Rough character width estimation\r\n\t\t\t\t\theight += Math.max(estimatedLines * 20, 40); // Minimum 40px per text field\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// Add height for buttons\r\n\t\tif (customButton && customButton.length > 0) {\r\n\t\t\theight += 60; // Standard button height + padding\r\n\t\t}\r\n\r\n\t\t// Add height for progress bar if enabled\r\n\t\tif (enableProgress && totalSteps > 1) {\r\n\t\t\theight += 50;\r\n\t\t}\r\n\r\n\t\t// Add base padding\r\n\t\theight += 40;\r\n\r\n\t\t// Ensure reasonable bounds\r\n\t\tconst minHeight = 100;\r\n\t\tconst maxHeight = Math.min(400, window.innerHeight * 0.8); // Max 80% of viewport height\r\n\r\n\t\treturn Math.max(minHeight, Math.min(height, maxHeight));\r\n\t};\r\n\r\n\r\n\r\n\t// Update dynamic dimensions and positioning when content changes\r\n\tuseEffect(() => {\r\n\t\t// Use requestAnimationFrame to ensure DOM has been updated\r\n\t\trequestAnimationFrame(() => {\r\n\t\t\tconst newDimensions = calculateOptimalDimensions();\r\n\t\t\tsetPopupDimensions(newDimensions);\r\n\t\t\tsetDynamicWidth(`${newDimensions.width}px`);\r\n\r\n\t\t\t// Recalculate position if we have a target element\r\n\t\t\tif (xpath) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tupdateSmartPosition(element, newDimensions);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t});\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep, xpath]);\r\n\r\n\t// Smart positioning function\r\n\tconst updateSmartPosition = (element: HTMLElement, dimensions: PopupDimensions) => {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\r\n\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t// Calculate optimal placement\r\n\t\tconst placement = calculateOptimalPlacement(rect, dimensions);\r\n\r\n\t\t// Calculate position with boundary detection\r\n\t\tconst optimalPos = calculatePopupPositionWithBounds(\r\n\t\t\trect,\r\n\t\t\tdimensions,\r\n\t\t\tplacement,\r\n\t\t\t{ x: xOffset, y: yOffset },\r\n\t\t\thotspotSize\r\n\t\t);\r\n\r\n\t\tsetOptimalPosition(optimalPos);\r\n\t\tsetPopupPosition({\r\n\t\t\ttop: optimalPos.top,\r\n\t\t\tleft: optimalPos.left\r\n\t\t});\r\n\t};\r\n\r\n\t// Recalculate popup position when hotspot size changes\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tupdateSmartPosition(element, popupDimensions);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData, popupDimensions]);\r\n\r\n\t// Recalculate popup position on window resize and scroll\r\n\tuseEffect(() => {\r\n\t\tconst handleViewportChange = () => {\r\n\t\t\tif (xpath) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tupdateSmartPosition(element, popupDimensions);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleViewportChange);\r\n\t\twindow.addEventListener('scroll', handleViewportChange);\r\n\t\treturn () => {\r\n\t\t\twindow.removeEventListener('resize', handleViewportChange);\r\n\t\t\twindow.removeEventListener('scroll', handleViewportChange);\r\n\t\t};\r\n\t}, [xpath, popupDimensions]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\t// Enhanced width calculation with viewport awareness\r\n\t\tmaxWidth: (hasOnlyButtons() || hasOnlyText()) ? \"none !important\" :\r\n\t\t\t\t  `min(${popupDimensions.width}px, 90vw) !important`,\r\n\t\twidth: (hasOnlyButtons() || hasOnlyText()) ? \"auto !important\" :\r\n\t\t\t   `min(${popupDimensions.width}px, 90vw) !important`,\r\n\t\t// Enhanced height with max constraints\r\n\t\tmaxHeight: `min(${popupDimensions.height}px, 80vh) !important`,\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\tfirstStepElement.scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element;\r\n\t\tlet steps;\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\t// Use smart positioning if available, otherwise use legacy positioning\r\n\t\t\t\t\t\t\t\t\t\t\ttop: optimalPosition\r\n\t\t\t\t\t\t\t\t\t\t\t\t? optimalPosition.top\r\n\t\t\t\t\t\t\t\t\t\t\t\t: popupPosition.top + 10 + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: optimalPosition\r\n\t\t\t\t\t\t\t\t\t\t\t\t? optimalPosition.left\r\n\t\t\t\t\t\t\t\t\t\t\t\t: popupPosition.left + 10 + parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t// Smart positioning overrides legacy positioning when available\r\n\t\t\t\t\t\t\t\t\t...(optimalPosition ? {} : getCanvasPosition(canvasProperties?.Position || \"center-center\")),\r\n\t\t\t\t\t\t\t\t\t// Only apply legacy positioning if smart positioning is not available\r\n\t\t\t\t\t\t\t\t\t...(optimalPosition ? {} : {\r\n\t\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\t}),\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",}}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\r\n\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"0\" :\r\n\t\t\t\t\t\t\t\t\t\t\thasOnlyText() ? \"0\" : (canvasProperties?.Padding || \"10px\"),\r\n\t\t\t\t\t\t\t\t\theight: hasOnlyButtons() ? \"auto\" : sectionHeight,\r\n\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyText() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyText() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? 0 : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: hasOnlyButtons() ? \"15px\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tminWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,GAAG,CAAEC,MAAM,CAAEC,UAAU,CAAEC,cAAc,CAAEC,aAAa,CAAEC,OAAO,CAAiBC,UAAU,KAAQ,eAAe,CAE1H,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE;AACA,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CAEpD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAqBA;AACA,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,IAAuB,CAChDC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAEC,MAAM,CAACC,UAAU,CACxBC,MAAM,CAAEF,MAAM,CAACG,WAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,iBAAiB,CAAIC,WAAoB,EAAK,CACnD,KAAM,CAAAC,QAAQ,CAAGV,iBAAiB,CAAC,CAAC,CACpC,MAAO,CACNC,GAAG,CAAEQ,WAAW,CAACR,GAAG,CAAGS,QAAQ,CAACT,GAAG,CACnCK,MAAM,CAAEI,QAAQ,CAACJ,MAAM,CAAGG,WAAW,CAACH,MAAM,CAC5CJ,IAAI,CAAEO,WAAW,CAACP,IAAI,CAAGQ,QAAQ,CAACR,IAAI,CACtCC,KAAK,CAAEO,QAAQ,CAACP,KAAK,CAAGM,WAAW,CAACN,KACrC,CAAC,CACF,CAAC,CAED;AACA,KAAM,CAAAQ,yBAAyB,CAAGA,CACjCF,WAAoB,CACpBG,eAAgC,GACS,CACzC,KAAM,CAAAC,KAAK,CAAGL,iBAAiB,CAACC,WAAW,CAAC,CAC5C,KAAM,CAAEK,KAAK,CAAEC,MAAO,CAAC,CAAGH,eAAe,CAEzC;AACA,KAAM,CAAAI,OAAO,CAAG,EAAE,CAElB;AACA,KAAM,CAAAC,OAAO,CAAGJ,KAAK,CAACZ,GAAG,EAAIc,MAAM,CAAGC,OAAO,CAC7C,KAAM,CAAAE,UAAU,CAAGL,KAAK,CAACP,MAAM,EAAIS,MAAM,CAAGC,OAAO,CACnD,KAAM,CAAAG,QAAQ,CAAGN,KAAK,CAACX,IAAI,EAAIY,KAAK,CAAGE,OAAO,CAC9C,KAAM,CAAAI,SAAS,CAAGP,KAAK,CAACV,KAAK,EAAIW,KAAK,CAAGE,OAAO,CAEhD;AACA,GAAIE,UAAU,CAAE,MAAO,QAAQ,CAC/B,GAAID,OAAO,CAAE,MAAO,KAAK,CACzB,GAAIG,SAAS,CAAE,MAAO,OAAO,CAC7B,GAAID,QAAQ,CAAE,MAAO,MAAM,CAE3B;AACA,KAAM,CAAAE,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACV,KAAK,CAACZ,GAAG,CAAEY,KAAK,CAACP,MAAM,CAAEO,KAAK,CAACX,IAAI,CAAEW,KAAK,CAACV,KAAK,CAAC,CAC3E,GAAIkB,QAAQ,GAAKR,KAAK,CAACP,MAAM,CAAE,MAAO,QAAQ,CAC9C,GAAIe,QAAQ,GAAKR,KAAK,CAACZ,GAAG,CAAE,MAAO,KAAK,CACxC,GAAIoB,QAAQ,GAAKR,KAAK,CAACV,KAAK,CAAE,MAAO,OAAO,CAC5C,MAAO,MAAM,CACd,CAAC,CAED;AACA,KAAM,CAAAqB,gCAAgC,CAAG,QAAAA,CACxCf,WAAoB,CACpBG,eAAgC,CAChCa,SAA8C,CAGzB,IAFrB,CAAAC,aAAuC,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEG,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,IACxD,CAAAC,WAAmB,CAAAL,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAExB,KAAM,CAAAjB,QAAQ,CAAGV,iBAAiB,CAAC,CAAC,CACpC,KAAM,CAAAgB,OAAO,CAAG,EAAE,CAClB,KAAM,CAAAiB,aAAa,CAAG,EAAE,CAAE;AAE1B,GAAI,CAAAhC,GAAW,CACf,GAAI,CAAAC,IAAY,CAChB,GAAI,CAAAgC,YAA2B,CAC/B,GAAI,CAAAC,eAA8B,CAElC;AACA,KAAM,CAAAC,cAAc,CAAG3B,WAAW,CAACP,IAAI,CAAGwB,aAAa,CAACI,CAAC,CAAIE,WAAW,CAAG,CAAE,CAC7E,KAAM,CAAAK,cAAc,CAAG5B,WAAW,CAACR,GAAG,CAAGyB,aAAa,CAACK,CAAC,CAAIC,WAAW,CAAG,CAAE,CAE5E,OAAQP,SAAS,EAChB,IAAK,KAAK,CACTxB,GAAG,CAAGoC,cAAc,CAAIL,WAAW,CAAG,CAAE,CAAGpB,eAAe,CAACG,MAAM,CAAGkB,aAAa,CACjF/B,IAAI,CAAGkC,cAAc,CAAIxB,eAAe,CAACE,KAAK,CAAG,CAAE,CACnDoB,YAAY,CAAG,CAAEI,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAC,CACxDJ,eAAe,CAAG,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC9D,MAED,IAAK,QAAQ,CACZtC,GAAG,CAAGoC,cAAc,CAAIL,WAAW,CAAG,CAAE,CAAGC,aAAa,CACxD/B,IAAI,CAAGkC,cAAc,CAAIxB,eAAe,CAACE,KAAK,CAAG,CAAE,CACnDoB,YAAY,CAAG,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC3DJ,eAAe,CAAG,CAAEG,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC3D,MAED,IAAK,MAAM,CACVtC,GAAG,CAAGoC,cAAc,CAAIzB,eAAe,CAACG,MAAM,CAAG,CAAE,CACnDb,IAAI,CAAGkC,cAAc,CAAIJ,WAAW,CAAG,CAAE,CAAGpB,eAAe,CAACE,KAAK,CAAGmB,aAAa,CACjFC,YAAY,CAAG,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACzDJ,eAAe,CAAG,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CAC7D,MAED,IAAK,OAAO,CACXtC,GAAG,CAAGoC,cAAc,CAAIzB,eAAe,CAACG,MAAM,CAAG,CAAE,CACnDb,IAAI,CAAGkC,cAAc,CAAIJ,WAAW,CAAG,CAAE,CAAGC,aAAa,CACzDC,YAAY,CAAG,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CAC1DJ,eAAe,CAAG,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CAC5D,MACF,CAEA;AACArC,IAAI,CAAGoB,IAAI,CAACC,GAAG,CAACP,OAAO,CAAEM,IAAI,CAACkB,GAAG,CAACtC,IAAI,CAAEQ,QAAQ,CAACP,KAAK,CAAGS,eAAe,CAACE,KAAK,CAAGE,OAAO,CAAC,CAAC,CAC1Ff,GAAG,CAAGqB,IAAI,CAACC,GAAG,CAACP,OAAO,CAAEM,IAAI,CAACkB,GAAG,CAACvC,GAAG,CAAES,QAAQ,CAACJ,MAAM,CAAGM,eAAe,CAACG,MAAM,CAAGC,OAAO,CAAC,CAAC,CAE1F,MAAO,CACNf,GAAG,CAAEA,GAAG,CAAGG,MAAM,CAACqC,OAAO,CACzBvC,IAAI,CAAEA,IAAI,CAAGE,MAAM,CAACsC,OAAO,CAC3BjB,SAAS,CACTS,YAAY,CACZC,eACD,CAAC,CACF,CAAC,CA6GD,KAAM,CAAAQ,cAAoC,CAAGC,IAAA,EA8BvC,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IA9BwC,CAC1CC,QAAQ,CACRC,SAAS,CACTC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,QAAQ,CACRC,WAAW,CACXC,UAAU,CACVC,eAAe,CACfC,QAAQ,CACRC,mBAAmB,CACnBC,eAAe,CACfC,YAAY,CACZC,eAAe,CACfC,gBAAgB,CAChBC,WAAW,CACXC,oBAAoB,CACpBC,oBAAoB,CACpBC,YAAY,CACZC,cAAc,CACdC,iBAAiB,CACjBC,kBAAkB,CAClBC,kBAAkB,CAClBC,kBAAkB,CACnBC,kBAEH,CAAC,CAAAvC,IAAA,CACA,KAAM,CACLwC,cAAc,CACdC,gBAAgB,CAChBC,oBAAoB,CACpBC,eAAe,CACfC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,gBAAgB,CAChBC,oBAAoB,CACpBC,oBAAoB,CACpBC,cAAc,CACdC,aACD,CAAC,CAAGzG,cAAc,CAAE0G,KAAkB,EAAKA,KAAK,CAAC,CACjD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtH,QAAQ,CAAqB,IAAI,CAAC,CAC5E;AACA,KAAM,CAACuH,aAAa,CAAEC,gBAAgB,CAAC,CAAGxH,QAAQ,CAAuC,IAAI,CAAC,CAC9F,KAAM,CAACyH,eAAe,CAAEC,kBAAkB,CAAC,CAAG1H,QAAQ,CAAyB,IAAI,CAAC,CACpF,KAAM,CAAC8B,eAAe,CAAE6F,kBAAkB,CAAC,CAAG3H,QAAQ,CAAkB,CAAEgC,KAAK,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CAAC,CACpG,KAAM,CAAC2F,YAAY,CAAEC,eAAe,CAAC,CAAG7H,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAACkD,WAAW,CAAE4E,cAAc,CAAC,CAAG9H,QAAQ,CAAS,EAAE,CAAC,CAC1D,KAAM,CAAA+H,UAAU,CAAG9H,MAAM,CAAiB,IAAI,CAAC,CAC/C,KAAM,CAAA+H,kBAAkB,CAAG/H,MAAM,CAAiB,IAAI,CAAC,CACvD,KAAM,CAAAgI,QAAQ,CAAGhI,MAAM,CAAiB,IAAI,CAAC,CAC7C,GAAI,CAAAiI,OAAY,CAChB,KAAM,CAAAC,iBAAiB,CAAIC,KAAa,EAAyB,CAChE,KAAM,CAAAC,MAAM,CAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,CAAEE,QAAQ,CAAE,IAAI,CAAEE,WAAW,CAACC,uBAAuB,CAAE,IAAI,CAAC,CAClG,KAAM,CAAAC,IAAI,CAAGL,MAAM,CAACM,eAAe,CACnC,GAAID,IAAI,WAAY,CAAAE,WAAW,CAAE,CAChC,MAAO,CAAAF,IAAI,CACZ,CAAC,IAAM,IAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEG,aAAa,CAAE,CAC/B,MAAO,CAAAH,IAAI,CAACG,aAAa,CAAE;AAC5B,CAAC,IAAM,CACN,MAAO,KAAI,CACZ,CACD,CAAC,CACD,GAAI,CAAAT,KAAU,CACd,GAAIpC,cAAc,CAAEoC,KAAK,CAAGpC,cAAc,SAAdA,cAAc,kBAAAjC,qBAAA,CAAdiC,cAAc,CAAE8C,SAAS,UAAA/E,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,iBAA9BA,sBAAA,CAAgC+E,WAAW,CAErE;AACA,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGjJ,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAkJ,YAAY,CAAGjJ,MAAM,CAAM,IAAI,CAAC,CACxC;AACA,KAAM,CAAAkJ,sBAAsB,CAAGA,CAACxH,WAAoB,CAAEuB,WAAmB,CAAEkG,OAAe,CAAEC,OAAe,GAAK,CAC/G,KAAM,CAAAC,WAAW,CAAG3H,WAAW,CAACqB,CAAC,CAAGoG,OAAO,CAC3C,KAAM,CAAAG,UAAU,CAAG5H,WAAW,CAACsB,CAAC,CAAGoG,OAAO,CAE1C;AACA,KAAM,CAAAG,cAAc,CAAGtG,WAAW,CAAG,CAAC,CAAE;AACxC,KAAM,CAAAuG,cAAc,CAAGvG,WAAW,CAAG,EAAE,CAAE;AAEzC,MAAO,CACN/B,GAAG,CAAEoI,UAAU,CAAGjI,MAAM,CAACqC,OAAO,CAAG8F,cAAc,CACjDrI,IAAI,CAAEkI,WAAW,CAAGhI,MAAM,CAACsC,OAAO,CAAG4F,cACtC,CAAC,CACF,CAAC,CACD;AACAzJ,SAAS,CAAC,IAAM,CACf,GAAIqI,KAAK,CAAE,CACV,KAAM,CAAAsB,OAAO,CAAGvB,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIsB,OAAO,CAAE,CACZ,KAAM,CAAAC,UAAU,CAAGC,0BAA0B,CAAC,CAAC,CAC/CjC,kBAAkB,CAACgC,UAAU,CAAC,CAC9BE,mBAAmB,CAACH,OAAO,CAAEC,UAAU,CAAC,CACzC,CACD,CACD,CAAC,CAAE,CAACvB,KAAK,CAAC,CAAC,CACXrI,SAAS,CAAC,IAAM,CACf,KAAM,CAAA2J,OAAO,CAAGvB,iBAAiB,CAACC,KAAK,CAAC,CACxC;AACA,GAAIsB,OAAO,CAAE,CACb,CACD,CAAC,CAAE,CAAC1D,cAAc,CAAC,CAAC,CAEpBjG,SAAS,CAAC,IAAM,KAAA+J,UAAA,CACf,KAAM,CAAAJ,OAAO,CAAGvB,iBAAiB,CAACxD,SAAS,SAATA,SAAS,kBAAAmF,UAAA,CAATnF,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAA2E,UAAA,iBAA5BA,UAAA,CAA8Bf,WAAW,CAAC,CAC5EzB,gBAAgB,CAACoC,OAAO,CAAC,CACzB,GAAIA,OAAO,CAAE,CACZA,OAAO,CAACK,KAAK,CAACC,eAAe,CAAG,gBAAgB,CAEhD;AACA,KAAM,CAAAC,IAAI,CAAGP,OAAO,CAACQ,qBAAqB,CAAC,CAAC,CAC5C1C,gBAAgB,CAAC,CAChBrG,GAAG,CAAE8I,IAAI,CAAC9I,GAAG,CAAGG,MAAM,CAACqC,OAAO,CAC9BvC,IAAI,CAAE6I,IAAI,CAAC7I,IAAI,CAAGE,MAAM,CAACsC,OAC1B,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAACe,SAAS,CAAEQ,WAAW,CAAC,CAAC,CAE5B;AACA;AACA,KAAM,EAAGgF,eAAe,CAAC,CAAGnK,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAAoK,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAI7D,gBAAgB,GAAK,MAAM,CAAE,CAChC,GAAIpB,WAAW,CAAGC,UAAU,CAAE,CAC7BkB,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/BF,UAAU,CAAC,CAAC,CACZoF,eAAe,CAAClF,WAAW,CAAGC,UAAU,CAAC,CAC1C,CACD,CAAC,IAAM,CACNkB,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/B,KAAM,CAAAmF,eAAe,CAAGhC,QAAQ,CAACiC,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAACP,KAAK,CAACS,OAAO,CAAG,MAAM,CACtCF,eAAe,CAACG,MAAM,CAAC,CAAC,CACzB,CACD,CACD,CAAC,CAED,KAAM,CAAAJ,eAAe,CAAIK,qBAA8B,EAAK,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAC3D,MAAO,CAAAV,qBAAqB,cAC3B7J,IAAA,CAACgD,cAAc,EACduC,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCH,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCzB,QAAQ,CAAEA,QAAS,CACnBsB,cAAc,CAAEA,cAAe,CAC/BrB,SAAS,CAAEA,SAAU,CACrBI,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAEqG,cAAe,CAC3BpG,UAAU,CAAEmF,cAAe,CAC3BxF,KAAK,CAAEA,KAAM,CACbC,IAAI,CAAEA,IAAK,CACXC,QAAQ,CAAEA,QAAS,CACnBK,WAAW,CAAEA,WAAW,CAAG,CAAE,CAC7BC,UAAU,CAAEA,UAAW,CACvBC,eAAe,CAAEA,eAAgB,CACjCC,QAAQ,CAAEA,QAAS,CACnBC,mBAAmB,CAAES,cAAc,SAAdA,cAAc,kBAAA2E,sBAAA,CAAd3E,cAAc,CAAE8C,SAAS,UAAA6B,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4BxF,WAAW,CAAC,UAAAyF,sBAAA,iBAAxCA,sBAAA,CAA0CU,mBAAoB,CACnF9F,eAAe,CAAEQ,cAAc,SAAdA,cAAc,kBAAA6E,sBAAA,CAAd7E,cAAc,CAAE8C,SAAS,UAAA+B,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B1F,WAAW,CAAC,UAAA2F,sBAAA,iBAAxCA,sBAAA,CAA0CS,eAAgB,CAC3E9F,YAAY,CACX,CAAAO,cAAc,SAAdA,cAAc,kBAAA+E,sBAAA,CAAd/E,cAAc,CAAE8C,SAAS,UAAAiC,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B5F,WAAW,CAAC,UAAA6F,sBAAA,kBAAAC,sBAAA,CAAxCD,sBAAA,CAA0CQ,aAAa,UAAAP,sBAAA,kBAAAC,uBAAA,CAAvDD,sBAAA,CAAyDQ,GAAG,CAAEC,OAAY,EACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,GAAM,CAC3C,GAAGA,MAAM,CACTC,WAAW,CAAEH,OAAO,CAACI,EAAI;AAC1B,CAAC,CAAC,CACH,CAAC,UAAAZ,uBAAA,iBALDA,uBAAA,CAKGa,MAAM,CAAC,CAACC,GAAmB,CAAEC,IAAS,GAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,CAAE,EAAE,CAAC,GAAI,EACvE,CACDvG,eAAe,CAAEA,eAAgB,CACjCC,gBAAgB,CAAEA,gBAAiB,CACnCC,WAAW,CAAEA,WAAY,CACzBG,YAAY,CAAEA,YAAa,CAC3BE,iBAAiB,CAAE,CAAAD,cAAc,SAAdA,cAAc,kBAAAmF,uBAAA,CAAdnF,cAAc,CAAE8C,SAAS,UAAAqC,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BhG,WAAW,CAAG,CAAC,CAAC,UAAAiG,uBAAA,iBAA5CA,uBAAA,CAA8Ce,OAAO,GAAI,CAAC,CAAE,CAC/E,CAAC,CACC,IAAI,CACT,CAAC,CAED,KAAM,CAAAd,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAIlG,WAAW,CAAG,CAAC,CAAE,CACpBmB,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/BH,UAAU,CAAC,CAAC,CACb,CACD,CAAC,CACDjF,SAAS,CAAC,IAAM,CACf,GAAIgG,YAAY,CAAE,CACjBoE,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACNA,eAAe,CAAC,KAAK,CAAC,CACvB,CACD,CAAC,CAAE,CAACpE,YAAY,CAAC,CAAC,CAClB;AACA,KAAM,CAAAqG,4BAA4B,CACjCC,QAAgB,EACqD,CACrE,OAAQA,QAAQ,EACf,IAAK,UAAU,CACd,MAAO,CACNjJ,YAAY,CAAE,CAAEI,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,MAAO,CAAC,CACrDJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,WAAW,CACf,MAAO,CACNL,YAAY,CAAE,CAAEI,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACtDJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNL,YAAY,CAAE,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CACzD,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNL,YAAY,CAAE,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNL,YAAY,CAAE,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,YAAY,CAChB,MAAO,CACNL,YAAY,CAAE,CAAEI,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAC,CACvDJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNL,YAAY,CAAE,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNL,YAAY,CAAE,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNL,YAAY,CAAE,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,QACC,MAAO,CACNL,YAAY,CAAE,CAAEI,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DJ,eAAe,CAAE,CAAEG,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACH,CACD,CAAC,CAED;AACA,KAAM,CAAEL,YAAY,CAAEC,eAAgB,CAAC,CAAGoE,eAAe,CACtD,CAAErE,YAAY,CAAEqE,eAAe,CAACrE,YAAY,CAAEC,eAAe,CAAEoE,eAAe,CAACpE,eAAgB,CAAC,CAChG+I,4BAA4B,CAAC,CAAAzG,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE2G,QAAQ,GAAI,eAAe,CAAC,CAE9E,KAAM,CAAAC,SAAS,CAAG,CACjBC,UAAU,CAAEjH,mBAAmB,SAAnBA,mBAAmB,YAAAtB,qBAAA,CAAnBsB,mBAAmB,CAAEkH,cAAc,UAAAxI,qBAAA,WAAnCA,qBAAA,CAAqCyI,IAAI,CAAG,MAAM,CAAG,QAAQ,CACzEC,SAAS,CAAEpH,mBAAmB,SAAnBA,mBAAmB,YAAArB,sBAAA,CAAnBqB,mBAAmB,CAAEkH,cAAc,UAAAvI,sBAAA,WAAnCA,sBAAA,CAAqC0I,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAC5EC,KAAK,CAAE,CAAAtH,mBAAmB,SAAnBA,mBAAmB,kBAAApB,sBAAA,CAAnBoB,mBAAmB,CAAEkH,cAAc,UAAAtI,sBAAA,iBAAnCA,sBAAA,CAAqC2I,SAAS,GAAI,SAAS,CAClEC,SAAS,CAAE,CAAAxH,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEyH,SAAS,GAAI,MAC9C,CAAC,CAED;AAEA,KAAM,CAAAC,iBAAiB,CAAIC,OAAe,EAAK,CAC9C;AACA,MAAO,CACNC,MAAM,CAAED,OAAO,CAACE,OAAO,CAAC,qCAAqC,CAAE,CAACC,MAAM,CAAEC,EAAE,CAAEC,EAAE,CAAEC,EAAE,GAAK,CACtF,MAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE,CAC1C,CAAC,CACF,CAAC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,QAAQ,CAAGlI,eAAe,EAAIA,eAAe,CAAC1C,MAAM,CAAG,CAAC,EAC7D0C,eAAe,CAACmI,IAAI,CAAEC,IAAS,EAC9BA,IAAI,CAACC,WAAW,EAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,EAAKA,GAAG,CAACC,GAAG,CAChE,CAAC,CAEF,KAAM,CAAAC,OAAO,CAAGzI,mBAAmB,EAAIA,mBAAmB,CAACzC,MAAM,CAAG,CAAC,EACpEyC,mBAAmB,CAACoI,IAAI,CAAEM,KAAU,EAAKA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAEjF,KAAM,CAAAC,UAAU,CAAG3I,YAAY,EAAIA,YAAY,CAAC3C,MAAM,CAAG,CAAC,CAE1D,MAAO,CAAAsL,UAAU,EAAI,CAACV,QAAQ,EAAI,CAACM,OAAO,CAC3C,CAAC,CAED;AACA,KAAM,CAAAK,WAAW,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAX,QAAQ,CAAGlI,eAAe,EAAIA,eAAe,CAAC1C,MAAM,CAAG,CAAC,EAC7D0C,eAAe,CAACmI,IAAI,CAAEC,IAAS,EAC9BA,IAAI,CAACC,WAAW,EAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,EAAKA,GAAG,CAACC,GAAG,CAChE,CAAC,CAEF,KAAM,CAAAC,OAAO,CAAGzI,mBAAmB,EAAIA,mBAAmB,CAACzC,MAAM,CAAG,CAAC,EACpEyC,mBAAmB,CAACoI,IAAI,CAAEM,KAAU,EAAKA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAEjF,KAAM,CAAAC,UAAU,CAAG3I,YAAY,EAAIA,YAAY,CAAC3C,MAAM,CAAG,CAAC,CAE1D,MAAO,CAAAkL,OAAO,EAAI,CAACN,QAAQ,EAAI,CAACU,UAAU,CAC3C,CAAC,CAED;AACA,KAAM,CAAAxE,0BAA0B,CAAGA,CAAA,GAAuB,KAAA0E,oBAAA,CAAAC,sBAAA,CACzD;AACA,GAAI5I,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAE6I,KAAK,EAAI,CAACf,cAAc,CAAC,CAAC,EAAI,CAACY,WAAW,CAAC,CAAC,CAAE,CACnE,KAAM,CAAArM,KAAK,CAAGyM,QAAQ,CAAC9I,gBAAgB,CAAC6I,KAAK,CAAC,CAC9C,MAAO,CACNxM,KAAK,CAAEA,KAAK,CACZC,MAAM,CAAEyM,qBAAqB,CAAC1M,KAAK,CACpC,CAAC,CACF,CAEA;AACA,GAAIyL,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAE,KAAAM,mBAAA,CAAAC,qBAAA,CACtC,KAAM,CAAAC,YAAY,CAAG,EAAAF,mBAAA,CAAA5G,UAAU,CAAC+G,OAAO,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAoBI,WAAW,GAAI,CAAC,CACzD,KAAM,CAAAC,WAAW,CAAG,EAAAJ,qBAAA,CAAA5G,kBAAkB,CAAC8G,OAAO,UAAAF,qBAAA,iBAA1BA,qBAAA,CAA4BG,WAAW,GAAI,CAAC,CAChE,KAAM,CAAA/M,KAAK,CAAGQ,IAAI,CAACC,GAAG,CAACoM,YAAY,CAAEG,WAAW,CAAE,GAAG,CAAC,CAAE;AAExD,MAAO,CACNhN,KAAK,CAAEA,KAAK,CAAG,EAAE,CAAE;AACnBC,MAAM,CAAEyM,qBAAqB,CAAC1M,KAAK,CAAG,EAAE,CACzC,CAAC,CACF,CAEA;AACA,KAAM,CAAA6M,YAAY,CAAG,EAAAP,oBAAA,CAAAvG,UAAU,CAAC+G,OAAO,UAAAR,oBAAA,iBAAlBA,oBAAA,CAAoBS,WAAW,GAAI,CAAC,CACzD,KAAM,CAAAC,WAAW,CAAG,EAAAT,sBAAA,CAAAvG,kBAAkB,CAAC8G,OAAO,UAAAP,sBAAA,iBAA1BA,sBAAA,CAA4BQ,WAAW,GAAI,CAAC,CAEhE;AACA,KAAM,CAAAE,YAAY,CAAGzM,IAAI,CAACC,GAAG,CAACoM,YAAY,CAAEG,WAAW,CAAC,CAExD;AACA,KAAM,CAAAE,WAAW,CAAGD,YAAY,CAAG,EAAE,CAAE;AAEvC;AACA,KAAM,CAAAE,QAAQ,CAAG,GAAG,CAAE;AACtB,KAAM,CAAAC,QAAQ,CAAG5M,IAAI,CAACkB,GAAG,CAAC,GAAG,CAAEpC,MAAM,CAACC,UAAU,CAAG,GAAG,CAAC,CAAE;AAEzD,KAAM,CAAA8N,UAAU,CAAG7M,IAAI,CAACC,GAAG,CAAC0M,QAAQ,CAAE3M,IAAI,CAACkB,GAAG,CAACwL,WAAW,CAAEE,QAAQ,CAAC,CAAC,CAEtE,MAAO,CACNpN,KAAK,CAAEqN,UAAU,CACjBpN,MAAM,CAAEyM,qBAAqB,CAACW,UAAU,CACzC,CAAC,CACF,CAAC,CAED;AACA,KAAM,CAAAX,qBAAqB,CAAI1M,KAAa,EAAa,CACxD,GAAI,CAAAC,MAAM,CAAG,CAAC,CAEd;AACA,GAAIuD,eAAe,EAAIA,eAAe,CAAC1C,MAAM,CAAG,CAAC,CAAE,CAClD0C,eAAe,CAAC8J,OAAO,CAAEC,SAAc,EAAK,CAC3C,GAAIA,SAAS,CAAC1B,WAAW,EAAI0B,SAAS,CAAC1B,WAAW,CAAC/K,MAAM,CAAG,CAAC,CAAE,CAC9DyM,SAAS,CAAC1B,WAAW,CAACyB,OAAO,CAAExB,GAAQ,EAAK,CAC3C7L,MAAM,EAAIwM,QAAQ,CAACX,GAAG,CAAC0B,aAAa,EAAI,KAAK,CAAC,CAAG,EAAE,CAAE;AACtD,CAAC,CAAC,CACH,CACD,CAAC,CAAC,CACH,CAEA;AACA,GAAIjK,mBAAmB,EAAIA,mBAAmB,CAACzC,MAAM,CAAG,CAAC,CAAE,CAC1DyC,mBAAmB,CAAC+J,OAAO,CAAErB,KAAU,EAAK,CAC3C,GAAIA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC3C;AACA,KAAM,CAAAsB,UAAU,CAAGxB,KAAK,CAACC,IAAI,CAACpL,MAAM,CACpC,KAAM,CAAA4M,cAAc,CAAGlN,IAAI,CAACmN,IAAI,CAACF,UAAU,EAAIzN,KAAK,CAAG,EAAE,CAAC,CAAC,CAAE;AAC7DC,MAAM,EAAIO,IAAI,CAACC,GAAG,CAACiN,cAAc,CAAG,EAAE,CAAE,EAAE,CAAC,CAAE;AAC9C,CACD,CAAC,CAAC,CACH,CAEA;AACA,GAAIjK,YAAY,EAAIA,YAAY,CAAC3C,MAAM,CAAG,CAAC,CAAE,CAC5Cb,MAAM,EAAI,EAAE,CAAE;AACf,CAEA;AACA,GAAI2N,cAAc,EAAIxK,UAAU,CAAG,CAAC,CAAE,CACrCnD,MAAM,EAAI,EAAE,CACb,CAEA;AACAA,MAAM,EAAI,EAAE,CAEZ;AACA,KAAM,CAAA4N,SAAS,CAAG,GAAG,CACrB,KAAM,CAAAC,SAAS,CAAGtN,IAAI,CAACkB,GAAG,CAAC,GAAG,CAAEpC,MAAM,CAACG,WAAW,CAAG,GAAG,CAAC,CAAE;AAE3D,MAAO,CAAAe,IAAI,CAACC,GAAG,CAACoN,SAAS,CAAErN,IAAI,CAACkB,GAAG,CAACzB,MAAM,CAAE6N,SAAS,CAAC,CAAC,CACxD,CAAC,CAID;AACA/P,SAAS,CAAC,IAAM,CACf;AACAgQ,qBAAqB,CAAC,IAAM,CAC3B,KAAM,CAAAC,aAAa,CAAGpG,0BAA0B,CAAC,CAAC,CAClDjC,kBAAkB,CAACqI,aAAa,CAAC,CACjCnI,eAAe,CAAC,GAAGmI,aAAa,CAAChO,KAAK,IAAI,CAAC,CAE3C;AACA,GAAIoG,KAAK,CAAE,CACV,KAAM,CAAAsB,OAAO,CAAGvB,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIsB,OAAO,CAAE,CACZG,mBAAmB,CAACH,OAAO,CAAEsG,aAAa,CAAC,CAC5C,CACD,CACD,CAAC,CAAC,CACH,CAAC,CAAE,CAACzK,mBAAmB,CAAEC,eAAe,CAAEC,YAAY,CAAEN,WAAW,CAAEiD,KAAK,CAAC,CAAC,CAE5E;AACA,KAAM,CAAAyB,mBAAmB,CAAGA,CAACH,OAAoB,CAAEC,UAA2B,GAAK,KAAAsG,qBAAA,CAAAC,sBAAA,CAClF,KAAM,CAAAjG,IAAI,CAAGP,OAAO,CAACQ,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAiG,eAAe,CAAGlJ,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAyJ,qBAAA,CAApBzJ,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAA8K,qBAAA,WAAvCA,qBAAA,CAAyCG,QAAQ,CAC5G5J,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACiL,QAAQ,EAAAF,sBAAA,CAC9C1J,oBAAoB,CAAC,CAAC,CAAC,UAAA0J,sBAAA,iBAAvBA,sBAAA,CAAyBE,QAAQ,CAEpC,KAAM,CAAAhH,OAAO,CAAGiH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAjH,OAAO,CAAGgH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,SAAS,GAAI,GAAG,CAAC,CAE7D;AACA,KAAM,CAAA5N,SAAS,CAAGd,yBAAyB,CAACoI,IAAI,CAAEN,UAAU,CAAC,CAE7D;AACA,KAAM,CAAA6G,UAAU,CAAG9N,gCAAgC,CAClDuH,IAAI,CACJN,UAAU,CACVhH,SAAS,CACT,CAAEK,CAAC,CAAEoG,OAAO,CAAEnG,CAAC,CAAEoG,OAAQ,CAAC,CAC1BnG,WACD,CAAC,CAEDwE,kBAAkB,CAAC8I,UAAU,CAAC,CAC9BhJ,gBAAgB,CAAC,CAChBrG,GAAG,CAAEqP,UAAU,CAACrP,GAAG,CACnBC,IAAI,CAAEoP,UAAU,CAACpP,IAClB,CAAC,CAAC,CACH,CAAC,CAED;AACArB,SAAS,CAAC,IAAM,CACf,GAAIqI,KAAK,EAAIlF,WAAW,CAAE,CACzB,KAAM,CAAAwG,OAAO,CAAGvB,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIsB,OAAO,CAAE,CACZG,mBAAmB,CAACH,OAAO,CAAE5H,eAAe,CAAC,CAC9C,CACD,CACD,CAAC,CAAE,CAACoB,WAAW,CAAEkF,KAAK,CAAE5B,oBAAoB,CAAE1E,eAAe,CAAC,CAAC,CAE/D;AACA/B,SAAS,CAAC,IAAM,CACf,KAAM,CAAA0Q,oBAAoB,CAAGA,CAAA,GAAM,CAClC,GAAIrI,KAAK,CAAE,CACV,KAAM,CAAAsB,OAAO,CAAGvB,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIsB,OAAO,CAAE,CACZG,mBAAmB,CAACH,OAAO,CAAE5H,eAAe,CAAC,CAC9C,CACD,CACD,CAAC,CAEDR,MAAM,CAACoP,gBAAgB,CAAC,QAAQ,CAAED,oBAAoB,CAAC,CACvDnP,MAAM,CAACoP,gBAAgB,CAAC,QAAQ,CAAED,oBAAoB,CAAC,CACvD,MAAO,IAAM,CACZnP,MAAM,CAACqP,mBAAmB,CAAC,QAAQ,CAAEF,oBAAoB,CAAC,CAC1DnP,MAAM,CAACqP,mBAAmB,CAAC,QAAQ,CAAEF,oBAAoB,CAAC,CAC3D,CAAC,CACF,CAAC,CAAE,CAACrI,KAAK,CAAEtG,eAAe,CAAC,CAAC,CAE5B,KAAM,CAAA8O,cAAc,CAAGnL,YAAY,CAACsG,MAAM,CAAC,CAACC,GAAQ,CAAEJ,MAAW,GAAK,CACrE,KAAM,CAAAiF,WAAW,CAAGjF,MAAM,CAACC,WAAW,EAAI,SAAS,CAAE;AACrD,GAAI,CAACG,GAAG,CAAC6E,WAAW,CAAC,CAAE,CACtB7E,GAAG,CAAC6E,WAAW,CAAC,CAAG,EAAE,CACtB,CACA7E,GAAG,CAAC6E,WAAW,CAAC,CAACC,IAAI,CAAClF,MAAM,CAAC,CAC7B,MAAO,CAAAI,GAAG,CACX,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAA+E,WAAW,CAAG,CACnB1E,QAAQ,CAAE,CAAA1G,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE2G,QAAQ,GAAI,eAAe,CACvD0E,YAAY,CAAE,CAAArL,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEsL,MAAM,GAAI,KAAK,CAC/CC,WAAW,CAAE,CAAAvL,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEwL,UAAU,GAAI,KAAK,CAClDC,WAAW,CAAE,CAAAzL,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE0L,WAAW,GAAI,OAAO,CACrDC,WAAW,CAAE,OAAO,CACpBtH,eAAe,CAAE,CAAArE,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE4L,eAAe,GAAI,OAAO,CAC7D;AACAnC,QAAQ,CAAG3B,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAI,iBAAiB,CAC7D,OAAOvM,eAAe,CAACE,KAAK,sBAAsB,CACtDA,KAAK,CAAGyL,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAI,iBAAiB,CAC1D,OAAOvM,eAAe,CAACE,KAAK,sBAAsB,CACtD;AACA8N,SAAS,CAAE,OAAOhO,eAAe,CAACG,MAAM,sBACzC,CAAC,CACD,KAAM,CAAAuP,aAAa,CAAG,EAAApN,gBAAA,CAAAoB,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAAf,gBAAA,kBAAAC,qBAAA,CAAhCD,gBAAA,CAAkCyJ,WAAW,UAAAxJ,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgDc,WAAW,CAAG,CAAC,CAAC,UAAAb,sBAAA,iBAAhEA,sBAAA,CAAkEkL,aAAa,GAAI,MAAM,CAC/G,KAAM,CAAAiC,kBAAkB,CAAIC,MAAW,EAAK,CAC3C,GAAIA,MAAM,CAACC,MAAM,GAAK,UAAU,EAAID,MAAM,CAACC,MAAM,GAAK,MAAM,EAAID,MAAM,CAACC,MAAM,GAAK,SAAS,CAAE,CAC5F,KAAM,CAAAC,SAAS,CAAGF,MAAM,CAACG,SAAS,CAClC,GAAIH,MAAM,CAACI,WAAW,GAAK,UAAU,CAAE,CACtC;AACAxQ,MAAM,CAACyQ,QAAQ,CAACC,IAAI,CAAGJ,SAAS,CACjC,CAAC,IAAM,CACN;AACAtQ,MAAM,CAAC2Q,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAC,IAAM,CACN,GACCF,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACI,WAAW,EAAI,UAAU,EAChCJ,MAAM,CAACI,WAAW,EAAI,UAAU,CAC/B,CACDzG,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACNqG,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACI,WAAW,EAAI,MAAM,EAC5BJ,MAAM,CAACI,WAAW,EAAI,MAAM,CAC3B,CACD1H,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACNsH,MAAM,CAACC,MAAM,EAAI,SAAS,EAC1BD,MAAM,CAACI,WAAW,EAAI,SAAS,CAC9B,KAAAI,uBAAA,CAAAC,uBAAA,CACD;AACA7L,cAAc,CAAC,CAAC,CAAC,CACjB;AACA,GAAIN,cAAc,SAAdA,cAAc,YAAAkM,uBAAA,CAAdlM,cAAc,CAAE8C,SAAS,UAAAoJ,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,WAA9BA,uBAAA,CAAgCpJ,WAAW,CAAE,CAChD,KAAM,CAAAqJ,gBAAgB,CAAGjK,iBAAiB,CAACnC,cAAc,CAAC8C,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CACnF,GAAIqJ,gBAAgB,CAAE,CACrBA,gBAAgB,CAACC,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CACxD,CACD,CACD,CACD,CACAnI,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,CACDpK,SAAS,CAAC,IAAM,KAAAwS,WAAA,CAAAC,mBAAA,CACf,GAAI7N,SAAS,SAATA,SAAS,YAAA4N,WAAA,CAAT5N,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAAoN,WAAA,YAAAC,mBAAA,CAA5BD,WAAA,CAA8BpG,OAAO,UAAAqG,mBAAA,WAArCA,mBAAA,CAAuCC,aAAa,CAAE,CACzD;AACA5L,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAC,CAAE,CAAClC,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,CAAEA,WAAW,CAAE0B,cAAc,CAAC,CAAC,CAE/D;AACA9G,SAAS,CAAC,IAAM,CACf,GAAIqG,kBAAkB,CAAE,KAAAsM,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAA5C,eAAe,CAAGlJ,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAkM,sBAAA,CAApBlM,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAAuN,sBAAA,WAAvCA,sBAAA,CAAyCtC,QAAQ,CAC5G5J,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACiL,QAAQ,EAAAuC,sBAAA,CAC9CnM,oBAAoB,CAAC,CAAC,CAAC,UAAAmM,sBAAA,iBAAvBA,sBAAA,CAAyBvC,QAAQ,CACpC,KAAM,CAAA4C,WAAW,CAAG/L,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAA4M,uBAAA,CAAd5M,cAAc,CAAE8C,SAAS,UAAA8J,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BzN,WAAW,CAAG,CAAC,CAAC,UAAA0N,uBAAA,iBAA5CA,uBAAA,CAA8C1G,OAAO,CACrDnG,cAAc,SAAdA,cAAc,kBAAA8M,uBAAA,CAAd9M,cAAc,CAAE8C,SAAS,UAAAgK,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgC5G,OAAO,CAE1C;AACA;AACA,GAAIgE,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEsC,aAAa,CAAE,CACnC;AACA5L,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACT,kBAAkB,CAAEI,oBAAoB,CAAC,CAAC,CAE9C;AACAzG,SAAS,CAAC,IAAM,CACf,GAAIsG,kBAAkB,CAAE,KAAA4M,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAAnD,eAAe,CAAGlJ,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAyM,sBAAA,CAApBzM,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAA8N,sBAAA,WAAvCA,sBAAA,CAAyC7C,QAAQ,CAC5G5J,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACiL,QAAQ,EAAA8C,sBAAA,CAC9C1M,oBAAoB,CAAC,CAAC,CAAC,UAAA0M,sBAAA,iBAAvBA,sBAAA,CAAyB9C,QAAQ,CACpC,KAAM,CAAA4C,WAAW,CAAG/L,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAAmN,uBAAA,CAAdnN,cAAc,CAAE8C,SAAS,UAAAqK,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BhO,WAAW,CAAG,CAAC,CAAC,UAAAiO,uBAAA,iBAA5CA,uBAAA,CAA8CjH,OAAO,CACrDnG,cAAc,SAAdA,cAAc,kBAAAqN,uBAAA,CAAdrN,cAAc,CAAE8C,SAAS,UAAAuK,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgCnH,OAAO,CAE1C;AACA,GAAIgE,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEsC,aAAa,CAAE,CACnC;AACA5L,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACR,kBAAkB,CAAEG,oBAAoB,CAAC,CAAC,CAE9C;AACAzG,SAAS,CAAC,IAAM,CACf,KAAM,CAAAwT,iBAAiB,CAAIC,CAAa,EAAK,CAC5C,KAAM,CAAAC,cAAc,CAAGnL,QAAQ,CAACiC,cAAc,CAAC,cAAc,CAAC,CAE9D;AACA,GAAIkJ,cAAc,EAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,CAAE,CAChE,OACD,CAEA;AACA;AACD,CAAC,CAEDrL,QAAQ,CAACoI,gBAAgB,CAAC,OAAO,CAAE6C,iBAAiB,CAAC,CAErD,MAAO,IAAM,CACZjL,QAAQ,CAACqI,mBAAmB,CAAC,OAAO,CAAE4C,iBAAiB,CAAC,CACzD,CAAC,CACF,CAAC,CAAE,CAAC/M,oBAAoB,CAAC,CAAC,CAC1B;AACAzG,SAAS,CAAC,IAAM,CACf,KAAM,CAAA6T,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,GAAI7L,UAAU,CAAC+G,OAAO,CAAE,CACvB;AACA/G,UAAU,CAAC+G,OAAO,CAAC/E,KAAK,CAAC9H,MAAM,CAAG,MAAM,CACxC,KAAM,CAAA4R,aAAa,CAAG9L,UAAU,CAAC+G,OAAO,CAACgF,YAAY,CACrD,KAAM,CAAAC,eAAe,CAAG,GAAG,CAAE;AAC7B,KAAM,CAAAC,YAAY,CAAGH,aAAa,CAAGE,eAAe,CAGpD9K,iBAAiB,CAAC+K,YAAY,CAAC,CAE/B;AACA,GAAI9K,YAAY,CAAC4F,OAAO,CAAE,CACzB;AACA,GAAI5F,YAAY,CAAC4F,OAAO,CAACmF,YAAY,CAAE,CACtC/K,YAAY,CAAC4F,OAAO,CAACmF,YAAY,CAAC,CAAC,CACpC,CACA;AACAC,UAAU,CAAC,IAAM,CAChB,GAAIhL,YAAY,CAAC4F,OAAO,EAAI5F,YAAY,CAAC4F,OAAO,CAACmF,YAAY,CAAE,CAC9D/K,YAAY,CAAC4F,OAAO,CAACmF,YAAY,CAAC,CAAC,CACpC,CACD,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CACD,CAAC,CAGDL,iBAAiB,CAAC,CAAC,CAGnB,KAAM,CAAAO,QAAQ,CAAG,CAChBD,UAAU,CAACN,iBAAiB,CAAE,EAAE,CAAC,CACjCM,UAAU,CAACN,iBAAiB,CAAE,GAAG,CAAC,CAClCM,UAAU,CAACN,iBAAiB,CAAE,GAAG,CAAC,CAClCM,UAAU,CAACN,iBAAiB,CAAE,GAAG,CAAC,CAClC,CAGD,GAAI,CAAAQ,cAAqC,CAAG,IAAI,CAChD,GAAI,CAAAC,gBAAyC,CAAG,IAAI,CAEpD,GAAItM,UAAU,CAAC+G,OAAO,EAAIxN,MAAM,CAACgT,cAAc,CAAE,CAChDF,cAAc,CAAG,GAAI,CAAAE,cAAc,CAAC,IAAM,CACzCJ,UAAU,CAACN,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFQ,cAAc,CAACG,OAAO,CAACxM,UAAU,CAAC+G,OAAO,CAAC,CAC3C,CAGA,GAAI/G,UAAU,CAAC+G,OAAO,EAAIxN,MAAM,CAACkT,gBAAgB,CAAE,CAClDH,gBAAgB,CAAG,GAAI,CAAAG,gBAAgB,CAAC,IAAM,CAC7CN,UAAU,CAACN,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFS,gBAAgB,CAACE,OAAO,CAACxM,UAAU,CAAC+G,OAAO,CAAE,CAC5C2F,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,CAAC,OAAO,CAAE,OAAO,CACnC,CAAC,CAAC,CACH,CAEA,MAAO,IAAM,CACZT,QAAQ,CAAC7E,OAAO,CAACuF,YAAY,CAAC,CAC9B,GAAIT,cAAc,CAAE,CACnBA,cAAc,CAACU,UAAU,CAAC,CAAC,CAC5B,CACA,GAAIT,gBAAgB,CAAE,CACrBA,gBAAgB,CAACS,UAAU,CAAC,CAAC,CAC9B,CACD,CAAC,CACF,CAAC,CAAE,CAAC3P,WAAW,CAAC,CAAC,CACjB;AACA;AAEA,QAAS,CAAA4P,YAAYA,CAACC,SAAiB,CAAE,CACxC,OAAQA,SAAS,EAChB,IAAK,OAAO,CACX,MAAO,YAAY,CACpB,IAAK,KAAK,CACT,MAAO,UAAU,CAClB,IAAK,QAAQ,CACb,QACC,MAAO,QAAQ,CACjB,CACD,CACA,KAAM,CAAAC,iBAAiB,CAAG,QAAAA,CAAA,CAAwC,IAAvC,CAAA5I,QAAgB,CAAAxJ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,eAAe,CAC5D,OAAQwJ,QAAQ,EACf,IAAK,aAAa,CACjB,MAAO,CAAElL,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,cAAc,CAClB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,aAAa,CACjB,MAAO,CAAEA,GAAG,CAAE2D,QAAQ,GAAK,EAAE,CAAG,gBAAgB,CAAG,gBAAiB,CAAC,CACtE,IAAK,cAAc,CAClB,MAAO,CAAE3D,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,UAAU,CACd,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,WAAW,CACf,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,YAAY,CAChB,MAAO,CAAEA,GAAG,CAAE,eAAgB,CAAC,CAChC,QACC,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CAClC,CACD,CAAC,CAEA;AACD,KAAM,CAAA+T,kBAAkB,CAAGA,CAACC,QAAgB,CAAEhF,eAAoB,CAAE6C,WAAgB,GAAK,CACxF,GAAI/L,oBAAoB,GAAK,SAAS,CAAE,CACvC;AACA,OAAQkO,QAAQ,EACf,IAAK,gBAAgB,CACpB,MAAO,CAAAnC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEoC,cAAc,IAAKrS,SAAS,CAAGiQ,WAAW,CAACoC,cAAc,CAAGjF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEiF,cAAc,CAChH,IAAK,eAAe,CACnB;AACA,MAAO,CAAApC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEqC,4BAA4B,IAAKtS,SAAS,CAAGiQ,WAAW,CAACqC,4BAA4B,CAAGlF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkF,4BAA4B,CAC1J,IAAK,UAAU,CACd,MAAO,CAAArC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEsC,QAAQ,IAAKvS,SAAS,CAAGiQ,WAAW,CAACsC,QAAQ,CAAGnF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmF,QAAQ,CAC9F,IAAK,eAAe,CACnB,MAAO,CAAAtC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEP,aAAa,IAAK1P,SAAS,CAAGiQ,WAAW,CAACP,aAAa,CAAGtC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEsC,aAAa,CAC7G,QACC,MAAO,CAAAtC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAGgF,QAAQ,CAAC,CACpC,CACD,CAAC,IAAM,CACN;AACA,GAAIA,QAAQ,GAAK,eAAe,CAAE,CACjC,MAAO,CAAAhF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkF,4BAA4B,CACrD,CACA,MAAO,CAAAlF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAGgF,QAAQ,CAAC,CACnC,CACD,CAAC,CAED,KAAM,CAAAI,kBAAkB,CAAGA,CAACrN,OAAY,CAAEiI,eAAoB,CAAE6C,WAAgB,CAAE5R,IAAS,CAAED,GAAQ,GAAK,CACzG+G,OAAO,CAAC6B,KAAK,CAACsC,QAAQ,CAAG,UAAU,CACnCnE,OAAO,CAAC6B,KAAK,CAAC3I,IAAI,CAAG,GAAGA,IAAI,IAAI,CAChC8G,OAAO,CAAC6B,KAAK,CAAC5I,GAAG,CAAG,GAAGA,GAAG,IAAI,CAC9B+G,OAAO,CAAC6B,KAAK,CAAC/H,KAAK,CAAG,GAAGmO,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEqF,IAAI,IAAI,CAAE;AACpDtN,OAAO,CAAC6B,KAAK,CAAC9H,MAAM,CAAG,GAAGkO,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEqF,IAAI,IAAI,CACnDtN,OAAO,CAAC6B,KAAK,CAACC,eAAe,CAAGmG,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEsF,KAAK,CACtDvN,OAAO,CAAC6B,KAAK,CAACiH,YAAY,CAAG,KAAK,CAClC9I,OAAO,CAAC6B,KAAK,CAAC2L,MAAM,CAAG,iBAAiB,CAAE;AAC1CxN,OAAO,CAAC6B,KAAK,CAAC4L,UAAU,CAAG,MAAM,CACjCzN,OAAO,CAAC6B,KAAK,CAAC6L,aAAa,CAAG,MAAM,CAAE;AACtC1N,OAAO,CAAC2N,SAAS,CAAG,EAAE,CAEtB,GAAI,CAAA1F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2F,IAAI,IAAK,MAAM,EAAI,CAAA3F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2F,IAAI,IAAK,UAAU,CAAE,CAC7E,KAAM,CAAAC,QAAQ,CAAGzN,QAAQ,CAAC0N,aAAa,CAAC,MAAM,CAAC,CAC/CD,QAAQ,CAACE,SAAS,CAAG9F,eAAe,CAAC2F,IAAI,GAAK,MAAM,CAAG,GAAG,CAAG,GAAG,CAChEC,QAAQ,CAAChM,KAAK,CAAC8C,KAAK,CAAG,OAAO,CAC9BkJ,QAAQ,CAAChM,KAAK,CAACmM,QAAQ,CAAG,MAAM,CAChCH,QAAQ,CAAChM,KAAK,CAACyC,UAAU,CAAG,MAAM,CAClCuJ,QAAQ,CAAChM,KAAK,CAAC4C,SAAS,CAAGwD,eAAe,CAAC2F,IAAI,GAAK,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAChFC,QAAQ,CAAChM,KAAK,CAACS,OAAO,CAAG,MAAM,CAC/BuL,QAAQ,CAAChM,KAAK,CAACoM,UAAU,CAAG,QAAQ,CACpCJ,QAAQ,CAAChM,KAAK,CAACqM,cAAc,CAAG,QAAQ,CACxCL,QAAQ,CAAChM,KAAK,CAAC/H,KAAK,CAAG,MAAM,CAC7B+T,QAAQ,CAAChM,KAAK,CAAC9H,MAAM,CAAG,MAAM,CAC9BiG,OAAO,CAACmO,WAAW,CAACN,QAAQ,CAAC,CAC9B,CAEA;AACA;AACA,KAAM,CAAAO,qBAAqB,CAAGpB,kBAAkB,CAAC,gBAAgB,CAAE/E,eAAe,CAAE6C,WAAW,CAAC,CAChG,KAAM,CAAAuD,WAAW,CAAGtP,oBAAoB,GAAK,SAAS,CAClDqP,qBAAqB,GAAK,KAAK,EAAI,CAACpO,OAAO,CAACsO,aAAa,CACzDrG,eAAe,EAAIpJ,gBAAgB,EAAI,CAACmB,OAAO,CAACsO,aAAc,CAElE,GAAID,WAAW,CAAE,CACPrO,OAAO,CAACuO,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC,CACxCxO,OAAO,CAACuO,SAAS,CAAChM,MAAM,CAAC,yBAAyB,CAAC,CACvD,CAAC,IAAM,CACHvC,OAAO,CAACuO,SAAS,CAAChM,MAAM,CAAC,iBAAiB,CAAC,CAC3CvC,OAAO,CAACuO,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CACpD,CAEN;AACAxO,OAAO,CAAC6B,KAAK,CAACS,OAAO,CAAG,MAAM,CAC9BtC,OAAO,CAAC6B,KAAK,CAAC6L,aAAa,CAAG,MAAM,CAEpC;AACA;AACA;AACA,KAAM,CAAAe,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAE/E,eAAe,CAAE6C,WAAW,CAAC,CACvF,GAAI2D,aAAa,CAAE,CAClB9P,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACA;AAAA,CAGD;AACA;AACA,GAAI,CAACqB,OAAO,CAAC0O,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,KAAM,CAAAC,UAAU,CAAG3O,OAAO,CAAC4O,SAAS,CAAC,IAAI,CAAgB,CACzD;AACA,GAAI5O,OAAO,CAACsO,aAAa,GAAKzT,SAAS,CAAE,CAC9B8T,UAAU,CAASL,aAAa,CAAGtO,OAAO,CAACsO,aAAa,CAC7D,CACN,GAAItO,OAAO,CAAC6O,UAAU,CAAE,CACvB7O,OAAO,CAAC6O,UAAU,CAACC,YAAY,CAACH,UAAU,CAAE3O,OAAO,CAAC,CACpDA,OAAO,CAAG2O,UAAU,CACrB,CACD,CAEA;AACA3O,OAAO,CAAC6B,KAAK,CAAC6L,aAAa,CAAG,MAAM,CAEpC;AACA,KAAM,CAAAqB,QAAQ,CAAG/B,kBAAkB,CAAC,UAAU,CAAE/E,eAAe,CAAE6C,WAAW,CAAC,CAC7E,KAAM,CAAAkE,WAAW,CAAI1D,CAAQ,EAAK,CACjCA,CAAC,CAAC2D,eAAe,CAAC,CAAC,CACnBC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIJ,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACApQ,cAAc,CAAC,IAAI,CAAC,CAEpB;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACA,KAAM,CAAAoR,oBAAoB,CAAGpC,kBAAkB,CAAC,eAAe,CAAE/E,eAAe,CAAE6C,WAAW,CAAC,CAC9F,GAAIsE,oBAAoB,CAAE,CACzBpP,OAAO,CAACuO,SAAS,CAAChM,MAAM,CAAC,iBAAiB,CAAC,CAC3CvC,OAAO,CAACuO,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChDxO,OAAO,CAACsO,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED,KAAM,CAAAe,cAAc,CAAI/D,CAAQ,EAAK,CACpCA,CAAC,CAAC2D,eAAe,CAAC,CAAC,CAEnB;AACA;AACA,KAAM,CAAAR,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAE/E,eAAe,CAAE6C,WAAW,CAAC,CACvF,GAAIiE,QAAQ,GAAK,kBAAkB,EAAI,CAACN,aAAa,CAAE,CACtD;AAAA,CAEF,CAAC,CAED,KAAM,CAAAa,WAAW,CAAIhE,CAAQ,EAAK,CACjCA,CAAC,CAAC2D,eAAe,CAAC,CAAC,CACnBC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIJ,QAAQ,GAAK,kBAAkB,EAAI,CAACA,QAAQ,CAAE,CACjD;AACApQ,cAAc,CAAC,CAACC,WAAW,CAAC,CAE5B;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACJ,KAAM,CAAAmR,oBAAoB,CAAGpC,kBAAkB,CAAC,eAAe,CAAE/E,eAAe,CAAE6C,WAAW,CAAC,CAC1F,GAAIsE,oBAAoB,CAAE,CACzBpP,OAAO,CAACuO,SAAS,CAAChM,MAAM,CAAC,iBAAiB,CAAC,CAC3CvC,OAAO,CAACuO,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChDxO,OAAO,CAACsO,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED;AACA,GAAI,CAACtO,OAAO,CAAC0O,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,GAAIK,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACA/O,OAAO,CAACwI,gBAAgB,CAAC,WAAW,CAAEwG,WAAW,CAAC,CAClDhP,OAAO,CAACwI,gBAAgB,CAAC,UAAU,CAAE6G,cAAc,CAAC,CAEpD;AACArP,OAAO,CAACwI,gBAAgB,CAAC,OAAO,CAAE8G,WAAW,CAAC,CAC/C,CAAC,IAAM,CACN;AACAtP,OAAO,CAACwI,gBAAgB,CAAC,OAAO,CAAE8G,WAAW,CAAC,CAC/C,CAEA;AACAtP,OAAO,CAACuP,YAAY,CAAC,yBAAyB,CAAE,MAAM,CAAC,CACxD,CACD,CAAC,CACD1X,SAAS,CAAC,IAAM,CACf,GAAI,CAAA2J,OAAO,CACX,GAAI,CAAAgO,KAAK,CAET,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,KAAAC,uBAAA,CAAAC,uBAAA,CAAAC,MAAA,CAAAC,OAAA,CACH;AACAL,KAAK,CAAG,CAAA1R,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE8C,SAAS,GAAI,EAAE,CAEvC;AACA,KAAM,CAAAkP,WAAW,CAAG/Q,oBAAoB,GAAK,SAAS,EAAIjB,cAAc,SAAdA,cAAc,YAAA4R,uBAAA,CAAd5R,cAAc,CAAE8C,SAAS,UAAA8O,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4BzS,WAAW,CAAG,CAAC,CAAC,UAAA0S,uBAAA,WAA5CA,uBAAA,CAA8C9O,WAAW,CAC/G/C,cAAc,CAAC8C,SAAS,CAAC3D,WAAW,CAAG,CAAC,CAAC,CAAS4D,WAAW,CAC9D,EAAA+O,MAAA,CAAAJ,KAAK,UAAAI,MAAA,kBAAAC,OAAA,CAALD,MAAA,CAAQ,CAAC,CAAC,UAAAC,OAAA,iBAAVA,OAAA,CAAYhP,WAAW,GAAI,EAAE,CAEhCW,OAAO,CAAGvB,iBAAiB,CAAC6P,WAAW,EAAI,EAAE,CAAC,CAC9C1Q,gBAAgB,CAACoC,OAAO,CAAC,CAEzB,GAAIA,OAAO,CAAE,CACZ;AAAA,CAGD;AACA,KAAM,CAAAuO,iBAAiB,CAAG1R,gBAAgB,GAAK,SAAS,EACvDU,oBAAoB,GAAK,SAAS,EAClCrC,KAAK,GAAK,SAAS,EAClB2B,gBAAgB,GAAK,MAAM,EAAIU,oBAAoB,GAAK,SAAU,CAEpE,GAAIgR,iBAAiB,CAAE,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAItB;AACA,GAAI,CAAApI,eAAe,CACnB,GAAI,CAAA6C,WAAW,CAEf,GAAI/L,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAA0R,sBAAA,CAApB1R,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAA+S,sBAAA,WAAvCA,sBAAA,CAAyC9H,QAAQ,CAAE,KAAAoI,uBAAA,CAAAC,uBAAA,CAC5F;AACAtI,eAAe,CAAG3J,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACiL,QAAQ,CAChE4C,WAAW,CAAGhN,cAAc,SAAdA,cAAc,kBAAAwS,uBAAA,CAAdxS,cAAc,CAAE8C,SAAS,UAAA0P,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BrT,WAAW,CAAG,CAAC,CAAC,UAAAsT,uBAAA,iBAA5CA,uBAAA,CAA8CtM,OAAO,CACpE,CAAC,IAAM,IAAI3F,oBAAoB,SAApBA,oBAAoB,YAAA2R,sBAAA,CAApB3R,oBAAoB,CAAG,CAAC,CAAC,UAAA2R,sBAAA,WAAzBA,sBAAA,CAA2B/H,QAAQ,CAAE,KAAAsI,uBAAA,CAAAC,uBAAA,CAC/C;AACAxI,eAAe,CAAG3J,oBAAoB,CAAC,CAAC,CAAC,CAAC4J,QAAQ,CAClD4C,WAAW,CAAGhN,cAAc,SAAdA,cAAc,kBAAA0S,uBAAA,CAAd1S,cAAc,CAAE8C,SAAS,UAAA4P,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgCxM,OAAO,CACtD,CAAC,IAAM,KAAAyM,uBAAA,CAAAC,uBAAA,CACN;AACA1I,eAAe,CAAG,CACjBG,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACduF,IAAI,CAAE,UAAU,CAChBL,KAAK,CAAE,QAAQ,CACfD,IAAI,CAAE,IAAI,CACVJ,cAAc,CAAE,IAAI,CACpBC,4BAA4B,CAAE,IAAI,CAClCC,QAAQ,CAAE,kBAAkB,CAC5B7C,aAAa,CAAE,KAChB,CAAC,CACDO,WAAW,CAAG,CAAAhN,cAAc,SAAdA,cAAc,kBAAA4S,uBAAA,CAAd5S,cAAc,CAAE8C,SAAS,UAAA8P,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BzT,WAAW,CAAG,CAAC,CAAC,UAAA0T,uBAAA,iBAA5CA,uBAAA,CAA8C1M,OAAO,GAAI,CAAC,CAAC,CAC1E,CACA,KAAM,CAAA/C,OAAO,CAAGiH,UAAU,CAAC,EAAA+H,gBAAA,CAAAjI,eAAe,UAAAiI,gBAAA,iBAAfA,gBAAA,CAAiB9H,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAjH,OAAO,CAAGgH,UAAU,CAAC,EAAAgI,iBAAA,CAAAlI,eAAe,UAAAkI,iBAAA,iBAAfA,iBAAA,CAAiB9H,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAuI,kBAAkB,CAAGzI,UAAU,CAAC,EAAAiI,iBAAA,CAAAnI,eAAe,UAAAmI,iBAAA,iBAAfA,iBAAA,CAAiB9C,IAAI,GAAI,IAAI,CAAC,CAEpE;AACA1N,cAAc,CAACgR,kBAAkB,CAAC,CAElC,GAAI,CAAA1X,IAAI,CAAED,GAAG,CACb,GAAIuI,OAAO,CAAE,CACZ,KAAM,CAAAO,IAAI,CAAGP,OAAO,CAACQ,qBAAqB,CAAC,CAAC,CAC5C9I,IAAI,CAAG6I,IAAI,CAACjH,CAAC,CAAGoG,OAAO,CACvBjI,GAAG,CAAG8I,IAAI,CAAChH,CAAC,EAAIoG,OAAO,CAAG,CAAC,CAAG,CAACA,OAAO,CAAG7G,IAAI,CAACuW,GAAG,CAAC1P,OAAO,CAAC,CAAC,CAE3D;AACA,KAAM,CAAA2P,QAAQ,CAAG7P,sBAAsB,CAACc,IAAI,CAAE6O,kBAAkB,CAAE1P,OAAO,CAAEC,OAAO,CAAC,CACnF7B,gBAAgB,CAACwR,QAAQ,CAAC,CAC3B,CAEA;AACA,KAAM,CAAA1O,eAAe,CAAGhC,QAAQ,CAACiC,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBpC,OAAO,CAAGoC,eAAe,CACzB;AACD,CAAC,IAAM,CACN;AACApC,OAAO,CAAGI,QAAQ,CAAC0N,aAAa,CAAC,KAAK,CAAC,CACvC9N,OAAO,CAAC+Q,EAAE,CAAG,cAAc,CAAE;AAC7B/Q,OAAO,CAACsO,aAAa,CAAG,KAAK,CAAE;AAC/BlO,QAAQ,CAAC4Q,IAAI,CAAC7C,WAAW,CAACnO,OAAO,CAAC,CACnC,CAEAA,OAAO,CAAC6B,KAAK,CAACoP,MAAM,CAAG,SAAS,CAChCjR,OAAO,CAAC6B,KAAK,CAAC6L,aAAa,CAAG,MAAM,CAAE;AAEtC;AACA1N,OAAO,CAAC6B,KAAK,CAAC2L,MAAM,CAAG,MAAM,CAE7B;AACA,IAAA6C,iBAAA,CAAIpI,eAAe,UAAAoI,iBAAA,WAAfA,iBAAA,CAAiB9F,aAAa,CAAE,CACnC5L,cAAc,CAAC,IAAI,CAAC,CACrB,CAEA;AACA0O,kBAAkB,CAACrN,OAAO,CAAEiI,eAAe,CAAE6C,WAAW,CAAE5R,IAAI,CAAED,GAAG,CAAC,CAEpE;AACA,KAAM,CAAAwV,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAE/E,eAAe,CAAE6C,WAAW,CAAC,CACvF,GAAI2D,aAAa,CAAE,CAClB9P,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AAAA,CAGD;AACD,CACD,CAAE,MAAOuS,KAAK,CAAE,CACfhC,OAAO,CAACgC,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACpD,CACD,CAAC,CAEDzB,iBAAiB,CAAC,CAAC,CAEnB,MAAO,IAAM,CACZ,KAAM,CAAArN,eAAe,CAAGhC,QAAQ,CAACiC,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAAC+O,OAAO,CAAG,IAAI,CAC9B/O,eAAe,CAACgP,WAAW,CAAG,IAAI,CAClChP,eAAe,CAACiP,UAAU,CAAG,IAAI,CAClC,CACD,CAAC,CACF,CAAC,CAAE,CACFvT,cAAc,CACdQ,oBAAoB,CACpBJ,kBAAkB,CAClBC,kBAAkB,CAClBY,oBAAoB,CACpB9B,WACA;AAAA,CACA,CAAC,CACF,KAAM,CAAAyK,cAAc,CAAG,CAAA5J,cAAc,SAAdA,cAAc,kBAAAzB,uBAAA,CAAdyB,cAAc,CAAE8C,SAAS,UAAAvE,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgCgV,OAAO,UAAA/U,uBAAA,iBAAvCA,uBAAA,CAAyCgV,cAAc,GAAI,KAAK,CAEvF,QAAS,CAAAC,mBAAmBA,CAACxS,cAAmB,CAAE,KAAAyS,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACjD,GAAI3S,cAAc,GAAK,CAAC,CAAE,CACzB,MAAO,MAAM,CACd,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,QAAQ,CAChB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAEA,MAAO,CAAAlB,cAAc,SAAdA,cAAc,kBAAA2T,uBAAA,CAAd3T,cAAc,CAAE8C,SAAS,UAAA6Q,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgCJ,OAAO,UAAAK,uBAAA,iBAAvCA,uBAAA,CAAyCC,gBAAgB,GAAI,MAAM,CAC3E,CACA,KAAM,CAAAC,gBAAgB,CAAGL,mBAAmB,CAACxS,cAAc,CAAC,CAC5D,KAAM,CAAA8S,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACpK,cAAc,CAAE,MAAO,KAAI,CAEhC,GAAImK,gBAAgB,GAAK,MAAM,CAAE,CAChC,mBACClZ,IAAA,CAACP,aAAa,EACb2Z,OAAO,CAAC,MAAM,CACdvC,KAAK,CAAEtS,UAAW,CAClBiH,QAAQ,CAAC,QAAQ,CACjB6N,UAAU,CAAE/U,WAAW,CAAG,CAAE,CAC5BgV,EAAE,CAAE,CACHnQ,eAAe,CAAE,aAAa,CAC9BqC,QAAQ,CAAE,oBAAoB,CAC9B,+BAA+B,CAAE,CAChCrC,eAAe,CAAE7C,aAAe;AACjC,CACD,CAAE,CACFiT,UAAU,cAAEvZ,IAAA,CAACV,MAAM,EAAC4J,KAAK,CAAE,CAAEsQ,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxDC,UAAU,cAAEzZ,IAAA,CAACV,MAAM,EAAC4J,KAAK,CAAE,CAAEsQ,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxD,CAAC,CAEJ,CACA,GAAIN,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACClZ,IAAA,CAACX,GAAG,EAACia,EAAE,CAAE,CAAE3P,OAAO,CAAE,MAAM,CAAE2L,UAAU,CAAE,QAAQ,CAAEoE,YAAY,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAK,CAAEtY,OAAO,CAAE,KAAM,CAAE,CAAAuY,QAAA,CAGrGC,KAAK,CAACC,IAAI,CAAC,CAAE7X,MAAM,CAAEsC,UAAW,CAAC,CAAC,CAACqG,GAAG,CAAC,CAACmP,CAAC,CAAEC,KAAK,gBAChDha,IAAA,QAECkJ,KAAK,CAAE,CACN/H,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACb+H,eAAe,CAAE6Q,KAAK,GAAK1V,WAAW,CAAG,CAAC,CAAGgC,aAAa,CAAG,SAAS,CAAE;AACxE6J,YAAY,CAAE,OACf,CAAE,EANG6J,KAOL,CACD,CAAC,CACE,CAAC,CAER,CACA,GAAId,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACClZ,IAAA,CAACX,GAAG,EAACia,EAAE,CAAE,CAAE3P,OAAO,CAAE,MAAM,CAAE2L,UAAU,CAAE,QAAQ,CAAEoE,YAAY,CAAE,YAAa,CAAE,CAAAE,QAAA,cAC9E1Z,KAAA,CAACP,UAAU,EAAC2Z,EAAE,CAAE,CAAEjY,OAAO,CAAE,KAAK,CAAE2K,KAAK,CAAE1F,aAAc,CAAE,CAAAsT,QAAA,EAAC,OACpD,CAACtV,WAAW,CAAC,MAAI,CAACC,UAAU,EACtB,CAAC,CACT,CAAC,CAER,CAEA,GAAI2U,gBAAgB,GAAK,QAAQ,CAAE,CAClC,mBACClZ,IAAA,CAACX,GAAG,EAAAua,QAAA,cACH5Z,IAAA,CAACL,UAAU,EAACyZ,OAAO,CAAC,OAAO,CAAAQ,QAAA,cAC1B5Z,IAAA,CAACR,cAAc,EACd4Z,OAAO,CAAC,aAAa,CACrBa,KAAK,CAAExV,QAAS,CAChB6U,EAAE,CAAE,CACHlY,MAAM,CAAE,KAAK,CACX+O,YAAY,CAAE,MAAM,CACpB+J,MAAM,CAAE,UAAU,CACpB,0BAA0B,CAAE,CAC3B/Q,eAAe,CAAE7C,aAAe;AACjC,CACD,CAAE,CACF,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAEA,MAAO,KAAI,CACZ,CAAC,CACD,mBACCpG,KAAA,CAAAE,SAAA,EAAAwZ,QAAA,EACEpT,aAAa,eACbxG,IAAA,QAAA4Z,QAAA,CAcE3T,WAAW,eACX/F,KAAA,CAACR,OAAO,EACP0R,IAAI,CAAE+I,OAAO,CAACzT,aAAa,CAAC,EAAIyT,OAAO,CAACtW,QAAQ,CAAE,CAClDA,QAAQ,CAAEA,QAAS,CACnBK,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACF3B,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjC4X,eAAe,CAAC,gBAAgB,CAChCC,cAAc,CACb3T,aAAa,CACV,CACA;AACApG,GAAG,CAAEsG,eAAe,CACjBA,eAAe,CAACtG,GAAG,CACnBoG,aAAa,CAACpG,GAAG,CAAG,EAAE,EAAIkP,UAAU,CAACzJ,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAAG,CAACyJ,UAAU,CAACzJ,YAAY,EAAI,GAAG,CAAC,CAAGpE,IAAI,CAACuW,GAAG,CAAC1I,UAAU,CAACzJ,YAAY,EAAI,GAAG,CAAC,CAAC,CAAC,CAChJxF,IAAI,CAAEqG,eAAe,CAClBA,eAAe,CAACrG,IAAI,CACpBmG,aAAa,CAACnG,IAAI,CAAG,EAAE,CAAGiP,UAAU,CAAC1J,YAAY,EAAI,GAAG,CAC3D,CAAC,CACD5D,SACH,CACDoX,EAAE,CAAE,CACH,gBAAgB,CAAEzV,QAAQ,CAAG,MAAM,CAAG,MAAM,CAC5C,8CAA8C,CAAE,CAC/CgR,MAAM,CAAE,IAAI,CACZ,GAAG3E,WAAW,CACd;AACA,IAAItJ,eAAe,CAAG,CAAC,CAAC,CAAGwN,iBAAiB,CAAC,CAAAtP,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE2G,QAAQ,GAAI,eAAe,CAAC,CAAC,CAC5F;AACA,IAAI7E,eAAe,CAAG,CAAC,CAAC,CAAG,CAC1BtG,GAAG,CAAE,GAAG,CAAC,CAAAoG,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEpG,GAAG,GAAI,CAAC,GAC5ByF,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC3CyJ,UAAU,CAACzJ,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAClC,CAACyJ,UAAU,CAACzJ,YAAY,EAAI,GAAG,CAAC,CAChCpE,IAAI,CAACuW,GAAG,CAAC1I,UAAU,CAACzJ,YAAY,EAAI,GAAG,CAAC,CAAC,CAC1C,CAAC,CAAC,eAAe,CACrBxF,IAAI,CAAE,GAAG,CAAC,CAAAmG,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEnG,IAAI,GAAI,CAAC,GAAKuF,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC9E0J,UAAU,CAAC1J,YAAY,CAAC,EAAI,CAAC,CAC9B,CAAC,CAAE,eACP,CAAC,CAAC,CACFwU,QAAQ,CAAE,QACX,CACD,CAAE,CACFC,iBAAiB,CAAE,IAAK,CAAAX,QAAA,eAExB5Z,IAAA,QAAKkJ,KAAK,CAAE,CAAEwQ,YAAY,CAAE,KAAK,CAAE/P,OAAO,CAAE,MAAO,CAAE,CAAAiQ,QAAA,CACnD,CAAA/U,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2V,aAAa,gBAC9Bxa,IAAA,CAACT,UAAU,EACVkb,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACFnB,EAAE,CAAE,CACH9N,QAAQ,CAAE,OAAO,CACjBkP,SAAS,CAAE,iCAAiC,CAC5Cna,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,MAAM,CACb0Z,MAAM,CAAE,OAAO,CACfS,UAAU,CAAE,iBAAiB,CAC7BC,MAAM,CAAE,gBAAgB,CACxB/F,MAAM,CAAE,QAAQ,CAChB1E,YAAY,CAAE,MAAM,CACpB9O,OAAO,CAAE,gBACV,CAAE,CAAAuY,QAAA,cAEF5Z,IAAA,CAACJ,SAAS,EAAC0Z,EAAE,CAAE,CAAEuB,IAAI,CAAE,CAAC,CAAE7O,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAClC,CACZ,CACG,CAAC,cACNhM,IAAA,CAACF,gBAAgB,EAEpBgb,GAAG,CAAEzS,YAAa,CAClBa,KAAK,CAAE,CAAE+F,SAAS,CAAErC,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,OAAQ,CAAE,CAC3EuN,OAAO,CAAE,CACRC,eAAe,CAAE,CAAC7S,cAAc,CAChC8S,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,KAAK,CACvBC,WAAW,CAAE,IAAI,CACjBC,kBAAkB,CAAE,EAAE,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,mBAAmB,CAAE,CACtB,CAAE,CAAA1B,QAAA,cAEC5Z,IAAA,QAAKkJ,KAAK,CAAE,CACX+F,SAAS,CAAErC,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,OAAO,CAC/D8M,QAAQ,CAAE1N,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,SAAS,CAAG,aAAa,CACvErM,KAAK,CAAEyL,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,MAAM,CAAGtL,SAAS,CAC7DgY,MAAM,CAAEtN,cAAc,CAAC,CAAC,CAAG,GAAG,CAAG1K,SAClC,CAAE,CAAA0X,QAAA,cACD1Z,KAAA,CAACb,GAAG,EAAC6J,KAAK,CAAE,CACX7H,OAAO,CAAEuL,cAAc,CAAC,CAAC,CAAG,GAAG,CAC7BY,WAAW,CAAC,CAAC,CAAG,GAAG,CAAI,CAAA1I,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEyW,OAAO,GAAI,MAAO,CAC7Dna,MAAM,CAAEwL,cAAc,CAAC,CAAC,CAAG,MAAM,CAAG+D,aAAa,CACjDxP,KAAK,CAAEyL,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,MAAM,CAAGtL,SAAS,CAC7DgY,MAAM,CAAEtN,cAAc,CAAC,CAAC,CAAG,GAAG,CAAG1K,SAClC,CAAE,CAAA0X,QAAA,eACD1Z,KAAA,CAACb,GAAG,EACHyb,GAAG,CAAE5T,UAAW,CAChByC,OAAO,CAAC,MAAM,CACd6R,aAAa,CAAC,QAAQ,CACtBC,QAAQ,CAAC,MAAM,CACflG,cAAc,CAAC,QAAQ,CACvB+D,EAAE,CAAE,CACHnY,KAAK,CAAEqM,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,MAAM,CACtCnM,OAAO,CAAEmM,WAAW,CAAC,CAAC,CAAG,GAAG,CAAGtL,SAChC,CAAE,CAAA0X,QAAA,EAEDjV,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEiG,GAAG,CAAE8D,SAAc,EACpCA,SAAS,CAAC1B,WAAW,CAACpC,GAAG,CAAC,CAAC8Q,SAAc,CAAEC,QAAgB,gBAC1D3b,IAAA,CAACX,GAAG,EAEHuc,SAAS,CAAC,KAAK,CACfC,GAAG,CAAEH,SAAS,CAACxO,GAAI,CACnB4O,GAAG,CAAEJ,SAAS,CAACK,OAAO,EAAI,OAAQ,CAClCzC,EAAE,CAAE,CACHrK,SAAS,CAAEP,SAAS,CAACsN,cAAc,EAAIN,SAAS,CAACM,cAAc,EAAI,OAAO,CAC1E9P,SAAS,CAAEwC,SAAS,CAACvC,SAAS,EAAI,QAAQ,CAC1C8P,SAAS,CAAEP,SAAS,CAACQ,GAAG,EAAI,SAAS,CACrC;AACA9a,MAAM,CAAE,GAAGsa,SAAS,CAAC/M,aAAa,EAAI,GAAG,IAAI,CAC7CgM,UAAU,CAAEe,SAAS,CAAChL,eAAe,EAAI,SAAS,CAClDwJ,MAAM,CAAE,QACT,CAAE,CACFO,OAAO,CAAEA,CAAA,GAAM,CACd,GAAI/L,SAAS,CAACyN,SAAS,CAAE,CACxB,KAAM,CAAApL,SAAS,CAAGrC,SAAS,CAACyN,SAAS,CACrC1b,MAAM,CAAC2Q,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAE,CACF7H,KAAK,CAAE,CAAEoP,MAAM,CAAE5J,SAAS,CAACyN,SAAS,CAAG,SAAS,CAAG,SAAU,CAAE,EAnB1D,GAAGzN,SAAS,CAACzD,EAAE,IAAI0Q,QAAQ,EAoBhC,CACD,CACF,CAAC,CAEAjX,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEkG,GAAG,CACxB,CAACwR,SAAc,CAAEpC,KAAU,QAAAqC,qBAAA,CAAAC,sBAAA,OAC1B,CAAAF,SAAS,CAAC/O,IAAI,eACbrN,IAAA,CAACL,UAAU,EACV4c,SAAS,CAAC,eAAe,CACG;AAC5BjD,EAAE,CAAE,CACHpN,SAAS,CAAE,EAAAmQ,qBAAA,CAAAD,SAAS,CAACxQ,cAAc,UAAAyQ,qBAAA,iBAAxBA,qBAAA,CAA0BG,UAAU,GAAI9Q,SAAS,CAACQ,SAAS,CACtEF,KAAK,CAAE,EAAAsQ,sBAAA,CAAAF,SAAS,CAACxQ,cAAc,UAAA0Q,sBAAA,iBAAxBA,sBAAA,CAA0BrQ,SAAS,GAAIP,SAAS,CAACM,KAAK,CAC7DyQ,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAAY,CACvBrb,OAAO,CAAE,OACV,CAAE,CACFsb,uBAAuB,CAAEvQ,iBAAiB,CAACgQ,SAAS,CAAC/O,IAAI,CAAG;AAAA,EARvD+O,SAAS,CAACnR,EAAE,EAAI+O,KASrB,CACD,EACH,CAAC,EACG,CAAC,CAEL4C,MAAM,CAACC,IAAI,CAAC9M,cAAc,CAAC,CAACnF,GAAG,CAAEoF,WAAW,OAAA8M,qBAAA,CAAAC,sBAAA,oBAC5C/c,IAAA,CAACX,GAAG,EACHyb,GAAG,CAAE3T,kBAAmB,CAExBmS,EAAE,CAAE,CACH3P,OAAO,CAAE,MAAM,CACf4L,cAAc,CAAErB,YAAY,EAAA4I,qBAAA,CAAC/M,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAA8M,qBAAA,iBAA9BA,qBAAA,CAAgC3Q,SAAS,CAAC,CACvEsP,QAAQ,CAAE,MAAM,CAChBvB,MAAM,CAAEtN,cAAc,CAAC,CAAC,CAAG,CAAC,CAAG,OAAO,CACtCzD,eAAe,EAAA4T,sBAAA,CAAEhN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAA+M,sBAAA,iBAA9BA,sBAAA,CAAgCrM,eAAe,CAChErP,OAAO,CAAEuL,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,OAAO,CAC3CzL,KAAK,CAAEyL,cAAc,CAAC,CAAC,CAAG,MAAM,CAAG,MAAM,CACzCuD,YAAY,CAAEvD,cAAc,CAAC,CAAC,CAAG,MAAM,CAAG1K,SAC3C,CAAE,CAAA0X,QAAA,CAED7J,cAAc,CAACC,WAAW,CAAC,CAACpF,GAAG,CAAC,CAACG,MAAW,CAAEiP,KAAa,QAAAgD,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC3Dtd,IAAA,CAACV,MAAM,EAENmb,OAAO,CAAEA,CAAA,GAAM7J,kBAAkB,CAAC7F,MAAM,CAACwS,YAAY,CAAE,CACvDnE,OAAO,CAAC,WAAW,CACnBE,EAAE,CAAE,CACHkE,WAAW,CAAE5Q,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,MAAM,CAC9CsN,MAAM,CAAEtN,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,eAAe,CAClDzD,eAAe,CAAE,EAAA6T,qBAAA,CAAAjS,MAAM,CAAC0S,gBAAgB,UAAAT,qBAAA,iBAAvBA,qBAAA,CAAyBU,qBAAqB,GAAI,SAAS,CAC5E1R,KAAK,CAAE,EAAAiR,sBAAA,CAAAlS,MAAM,CAAC0S,gBAAgB,UAAAR,sBAAA,iBAAvBA,sBAAA,CAAyBU,eAAe,GAAI,MAAM,CACzD/C,MAAM,CAAE,EAAAsC,sBAAA,CAAAnS,MAAM,CAAC0S,gBAAgB,UAAAP,sBAAA,iBAAvBA,sBAAA,CAAyBU,iBAAiB,GAAI,aAAa,CACnEvI,QAAQ,CAAE,EAAA8H,sBAAA,CAAApS,MAAM,CAAC0S,gBAAgB,UAAAN,sBAAA,iBAAvBA,sBAAA,CAAyBU,QAAQ,GAAI,MAAM,CACrD1c,KAAK,CAAE,EAAAic,sBAAA,CAAArS,MAAM,CAAC0S,gBAAgB,UAAAL,sBAAA,iBAAvBA,sBAAA,CAAyBzP,KAAK,GAAI,MAAM,CAC/CtM,OAAO,CAAEuL,cAAc,CAAC,CAAC,CAAG,kCAAkC,CAAG,SAAS,CAC1EkR,UAAU,CAAElR,cAAc,CAAC,CAAC,CAAG,0BAA0B,CAAG,QAAQ,CACpEmR,aAAa,CAAE,MAAM,CACrB5N,YAAY,CAAE,EAAAkN,sBAAA,CAAAtS,MAAM,CAAC0S,gBAAgB,UAAAJ,sBAAA,iBAAvBA,sBAAA,CAAyBW,YAAY,GAAI,KAAK,CAC5D1P,QAAQ,CAAE1B,cAAc,CAAC,CAAC,CAAG,aAAa,CAAG1K,SAAS,CACtDwY,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACVvR,eAAe,CAAE,EAAAmU,sBAAA,CAAAvS,MAAM,CAAC0S,gBAAgB,UAAAH,sBAAA,iBAAvBA,sBAAA,CAAyBI,qBAAqB,GAAI,SAAS,CAAE;AAC9EO,OAAO,CAAE,GAAG,CAAE;AACdvD,SAAS,CAAE,iBAAmB;AAC/B,CACD,CAAE,CAAAd,QAAA,CAED7O,MAAM,CAACmT,UAAU,EAxBblE,KAyBE,CAAC,EACT,CAAC,EAxCGhK,WAyCD,CAAC,EACN,CAAC,EACE,CAAC,CAGD,CAAC,EApIL,aAAa7H,cAAc,EAqIV,CAAC,CAElB4G,cAAc,EAAIxK,UAAU,CAAC,CAAC,EAAImB,gBAAgB,GAAK,MAAM,eAAI1F,IAAA,CAACX,GAAG,EAAAua,QAAA,CAAET,cAAc,CAAC,CAAC,CAAM,CAAC,CAAE,GAAG,EAC7F,CACT,CACG,CACL,cAEDnZ,IAAA,UAAA4Z,QAAA,CACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CACC,CAAC,EACP,CAAC,CAEL,CAAC,CAED,cAAe,CAAA5W,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}