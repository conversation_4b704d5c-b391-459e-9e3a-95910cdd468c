{"ast": null, "code": "// Hex JavaScript decoder\n// Copyright (c) 2008-2013 <PERSON><PERSON> <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\nvar decoder;\nexport var Hex = {\n  decode: function (a) {\n    var i;\n    if (decoder === undefined) {\n      var hex = \"0123456789ABCDEF\";\n      var ignore = \" \\f\\n\\r\\t\\u00A0\\u2028\\u2029\";\n      decoder = {};\n      for (i = 0; i < 16; ++i) {\n        decoder[hex.charAt(i)] = i;\n      }\n      hex = hex.toLowerCase();\n      for (i = 10; i < 16; ++i) {\n        decoder[hex.charAt(i)] = i;\n      }\n      for (i = 0; i < ignore.length; ++i) {\n        decoder[ignore.charAt(i)] = -1;\n      }\n    }\n    var out = [];\n    var bits = 0;\n    var char_count = 0;\n    for (i = 0; i < a.length; ++i) {\n      var c = a.charAt(i);\n      if (c == \"=\") {\n        break;\n      }\n      c = decoder[c];\n      if (c == -1) {\n        continue;\n      }\n      if (c === undefined) {\n        throw new Error(\"Illegal character at offset \" + i);\n      }\n      bits |= c;\n      if (++char_count >= 2) {\n        out[out.length] = bits;\n        bits = 0;\n        char_count = 0;\n      } else {\n        bits <<= 4;\n      }\n    }\n    if (char_count) {\n      throw new Error(\"Hex encoding incomplete: 4 bits missing\");\n    }\n    return out;\n  }\n};", "map": {"version": 3, "names": ["decoder", "Hex", "decode", "a", "i", "undefined", "hex", "ignore", "char<PERSON>t", "toLowerCase", "length", "out", "bits", "char_count", "c", "Error"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/jsencrypt/lib/lib/asn1js/hex.js"], "sourcesContent": ["// Hex JavaScript decoder\n// Copyright (c) 2008-2013 <PERSON><PERSON> <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\nvar decoder;\nexport var Hex = {\n    decode: function (a) {\n        var i;\n        if (decoder === undefined) {\n            var hex = \"0123456789ABCDEF\";\n            var ignore = \" \\f\\n\\r\\t\\u00A0\\u2028\\u2029\";\n            decoder = {};\n            for (i = 0; i < 16; ++i) {\n                decoder[hex.charAt(i)] = i;\n            }\n            hex = hex.toLowerCase();\n            for (i = 10; i < 16; ++i) {\n                decoder[hex.charAt(i)] = i;\n            }\n            for (i = 0; i < ignore.length; ++i) {\n                decoder[ignore.charAt(i)] = -1;\n            }\n        }\n        var out = [];\n        var bits = 0;\n        var char_count = 0;\n        for (i = 0; i < a.length; ++i) {\n            var c = a.charAt(i);\n            if (c == \"=\") {\n                break;\n            }\n            c = decoder[c];\n            if (c == -1) {\n                continue;\n            }\n            if (c === undefined) {\n                throw new Error(\"Illegal character at offset \" + i);\n            }\n            bits |= c;\n            if (++char_count >= 2) {\n                out[out.length] = bits;\n                bits = 0;\n                char_count = 0;\n            }\n            else {\n                bits <<= 4;\n            }\n        }\n        if (char_count) {\n            throw new Error(\"Hex encoding incomplete: 4 bits missing\");\n        }\n        return out;\n    }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,OAAO;AACX,OAAO,IAAIC,GAAG,GAAG;EACbC,MAAM,EAAE,SAAAA,CAAUC,CAAC,EAAE;IACjB,IAAIC,CAAC;IACL,IAAIJ,OAAO,KAAKK,SAAS,EAAE;MACvB,IAAIC,GAAG,GAAG,kBAAkB;MAC5B,IAAIC,MAAM,GAAG,6BAA6B;MAC1CP,OAAO,GAAG,CAAC,CAAC;MACZ,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;QACrBJ,OAAO,CAACM,GAAG,CAACE,MAAM,CAACJ,CAAC,CAAC,CAAC,GAAGA,CAAC;MAC9B;MACAE,GAAG,GAAGA,GAAG,CAACG,WAAW,CAAC,CAAC;MACvB,KAAKL,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;QACtBJ,OAAO,CAACM,GAAG,CAACE,MAAM,CAACJ,CAAC,CAAC,CAAC,GAAGA,CAAC;MAC9B;MACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,MAAM,CAACG,MAAM,EAAE,EAAEN,CAAC,EAAE;QAChCJ,OAAO,CAACO,MAAM,CAACC,MAAM,CAACJ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAClC;IACJ;IACA,IAAIO,GAAG,GAAG,EAAE;IACZ,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,UAAU,GAAG,CAAC;IAClB,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACO,MAAM,EAAE,EAAEN,CAAC,EAAE;MAC3B,IAAIU,CAAC,GAAGX,CAAC,CAACK,MAAM,CAACJ,CAAC,CAAC;MACnB,IAAIU,CAAC,IAAI,GAAG,EAAE;QACV;MACJ;MACAA,CAAC,GAAGd,OAAO,CAACc,CAAC,CAAC;MACd,IAAIA,CAAC,IAAI,CAAC,CAAC,EAAE;QACT;MACJ;MACA,IAAIA,CAAC,KAAKT,SAAS,EAAE;QACjB,MAAM,IAAIU,KAAK,CAAC,8BAA8B,GAAGX,CAAC,CAAC;MACvD;MACAQ,IAAI,IAAIE,CAAC;MACT,IAAI,EAAED,UAAU,IAAI,CAAC,EAAE;QACnBF,GAAG,CAACA,GAAG,CAACD,MAAM,CAAC,GAAGE,IAAI;QACtBA,IAAI,GAAG,CAAC;QACRC,UAAU,GAAG,CAAC;MAClB,CAAC,MACI;QACDD,IAAI,KAAK,CAAC;MACd;IACJ;IACA,IAAIC,UAAU,EAAE;MACZ,MAAM,IAAIE,KAAK,CAAC,yCAAyC,CAAC;IAC9D;IACA,OAAOJ,GAAG;EACd;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}