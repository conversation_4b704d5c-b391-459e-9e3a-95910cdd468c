{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport MenuItem from '@mui/material/MenuItem';\nimport ListItemIcon from '@mui/material/ListItemIcon';\nimport ListItemText from '@mui/material/ListItemText';\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../../../../hooks/features/columns/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridColumnMenuHideItem(props) {\n  const {\n    colDef,\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n  const columnsWithMenu = visibleColumns.filter(col => col.disableColumnMenu !== true);\n  // do not allow to hide the last column with menu\n  const disabled = columnsWithMenu.length === 1;\n  const toggleColumn = React.useCallback(event => {\n    /**\n     * Disabled `MenuItem` would trigger `click` event\n     * after imperative `.click()` call on HTML element.\n     * Also, click is triggered in testing environment as well.\n     */\n    if (disabled) {\n      return;\n    }\n    apiRef.current.setColumnVisibility(colDef.field, false);\n    onClick(event);\n  }, [apiRef, colDef.field, onClick, disabled]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  if (colDef.hideable === false) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(MenuItem, {\n    onClick: toggleColumn,\n    disabled: disabled,\n    children: [/*#__PURE__*/_jsx(ListItemIcon, {\n      children: /*#__PURE__*/_jsx(rootProps.slots.columnMenuHideIcon, {\n        fontSize: \"small\"\n      })\n    }), /*#__PURE__*/_jsx(ListItemText, {\n      children: apiRef.current.getLocaleText('columnMenuHideColumn')\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuHideItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuHideItem };", "map": {"version": 3, "names": ["React", "PropTypes", "MenuItem", "ListItemIcon", "ListItemText", "useGridApiContext", "useGridRootProps", "gridVisibleColumnDefinitionsSelector", "jsx", "_jsx", "jsxs", "_jsxs", "GridColumnMenuHideItem", "props", "colDef", "onClick", "apiRef", "rootProps", "visibleColumns", "columnsWithMenu", "filter", "col", "disableColumnMenu", "disabled", "length", "toggleColumn", "useCallback", "event", "current", "setColumnVisibility", "field", "disableColumnSelector", "hideable", "children", "slots", "columnMenuHideIcon", "fontSize", "getLocaleText", "process", "env", "NODE_ENV", "propTypes", "object", "isRequired", "func"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/menu/columnMenu/menuItems/GridColumnMenuHideItem.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport MenuItem from '@mui/material/MenuItem';\nimport ListItemIcon from '@mui/material/ListItemIcon';\nimport ListItemText from '@mui/material/ListItemText';\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../../../../hooks/features/columns/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridColumnMenuHideItem(props) {\n  const {\n    colDef,\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n  const columnsWithMenu = visibleColumns.filter(col => col.disableColumnMenu !== true);\n  // do not allow to hide the last column with menu\n  const disabled = columnsWithMenu.length === 1;\n  const toggleColumn = React.useCallback(event => {\n    /**\n     * Disabled `MenuItem` would trigger `click` event\n     * after imperative `.click()` call on HTML element.\n     * Also, click is triggered in testing environment as well.\n     */\n    if (disabled) {\n      return;\n    }\n    apiRef.current.setColumnVisibility(colDef.field, false);\n    onClick(event);\n  }, [apiRef, colDef.field, onClick, disabled]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  if (colDef.hideable === false) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(MenuItem, {\n    onClick: toggleColumn,\n    disabled: disabled,\n    children: [/*#__PURE__*/_jsx(ListItemIcon, {\n      children: /*#__PURE__*/_jsx(rootProps.slots.columnMenuHideIcon, {\n        fontSize: \"small\"\n      })\n    }), /*#__PURE__*/_jsx(ListItemText, {\n      children: apiRef.current.getLocaleText('columnMenuHideColumn')\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuHideItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuHideItem };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,iBAAiB,QAAQ,8CAA8C;AAChF,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,oCAAoC,QAAQ,6CAA6C;AAClG,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EACrC,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,MAAM,GAAGX,iBAAiB,CAAC,CAAC;EAClC,MAAMY,SAAS,GAAGX,gBAAgB,CAAC,CAAC;EACpC,MAAMY,cAAc,GAAGX,oCAAoC,CAACS,MAAM,CAAC;EACnE,MAAMG,eAAe,GAAGD,cAAc,CAACE,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,iBAAiB,KAAK,IAAI,CAAC;EACpF;EACA,MAAMC,QAAQ,GAAGJ,eAAe,CAACK,MAAM,KAAK,CAAC;EAC7C,MAAMC,YAAY,GAAGzB,KAAK,CAAC0B,WAAW,CAACC,KAAK,IAAI;IAC9C;AACJ;AACA;AACA;AACA;IACI,IAAIJ,QAAQ,EAAE;MACZ;IACF;IACAP,MAAM,CAACY,OAAO,CAACC,mBAAmB,CAACf,MAAM,CAACgB,KAAK,EAAE,KAAK,CAAC;IACvDf,OAAO,CAACY,KAAK,CAAC;EAChB,CAAC,EAAE,CAACX,MAAM,EAAEF,MAAM,CAACgB,KAAK,EAAEf,OAAO,EAAEQ,QAAQ,CAAC,CAAC;EAC7C,IAAIN,SAAS,CAACc,qBAAqB,EAAE;IACnC,OAAO,IAAI;EACb;EACA,IAAIjB,MAAM,CAACkB,QAAQ,KAAK,KAAK,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,OAAO,aAAarB,KAAK,CAACT,QAAQ,EAAE;IAClCa,OAAO,EAAEU,YAAY;IACrBF,QAAQ,EAAEA,QAAQ;IAClBU,QAAQ,EAAE,CAAC,aAAaxB,IAAI,CAACN,YAAY,EAAE;MACzC8B,QAAQ,EAAE,aAAaxB,IAAI,CAACQ,SAAS,CAACiB,KAAK,CAACC,kBAAkB,EAAE;QAC9DC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,CAAC,EAAE,aAAa3B,IAAI,CAACL,YAAY,EAAE;MAClC6B,QAAQ,EAAEjB,MAAM,CAACY,OAAO,CAACS,aAAa,CAAC,sBAAsB;IAC/D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,sBAAsB,CAAC6B,SAAS,GAAG;EACzE;EACA;EACA;EACA;EACA3B,MAAM,EAAEb,SAAS,CAACyC,MAAM,CAACC,UAAU;EACnC5B,OAAO,EAAEd,SAAS,CAAC2C,IAAI,CAACD;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAAS/B,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}