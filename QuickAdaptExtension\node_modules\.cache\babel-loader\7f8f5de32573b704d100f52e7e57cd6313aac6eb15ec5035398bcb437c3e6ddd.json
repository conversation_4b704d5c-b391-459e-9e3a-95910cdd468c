{"ast": null, "code": "import { createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\n/**\n * Get the columns state\n * @category Virtualization\n */\nexport const gridVirtualizationSelector = state => state.virtualization;\n\n/**\n * Get the enabled state for virtualization\n * @category Virtualization\n * @deprecated Use `gridVirtualizationColumnEnabledSelector` and `gridVirtualizationRowEnabledSelector`\n */\nexport const gridVirtualizationEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabled);\n\n/**\n * Get the enabled state for column virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationColumnEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForColumns);\n\n/**\n * Get the enabled state for row virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationRowEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForRows);\n\n/**\n * Get the render context\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextSelector = createSelector(gridVirtualizationSelector, state => state.renderContext);\n\n/**\n * Get the render context, with only columns filled in.\n * This is cached, so it can be used to only re-render when the column interval changes.\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextColumnsSelector = createSelectorMemoized(state => state.virtualization.renderContext.firstColumnIndex, state => state.virtualization.renderContext.lastColumnIndex, (firstColumnIndex, lastColumnIndex) => ({\n  firstColumnIndex,\n  lastColumnIndex\n}));", "map": {"version": 3, "names": ["createSelector", "createSelectorMemoized", "gridVirtualizationSelector", "state", "virtualization", "gridVirtualizationEnabledSelector", "enabled", "gridVirtualizationColumnEnabledSelector", "enabledForColumns", "gridVirtualizationRowEnabledSelector", "enabledForRows", "gridRenderContextSelector", "renderContext", "gridRenderContextColumnsSelector", "firstColumnIndex", "lastColumnIndex"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/virtualization/gridVirtualizationSelectors.js"], "sourcesContent": ["import { createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\n/**\n * Get the columns state\n * @category Virtualization\n */\nexport const gridVirtualizationSelector = state => state.virtualization;\n\n/**\n * Get the enabled state for virtualization\n * @category Virtualization\n * @deprecated Use `gridVirtualizationColumnEnabledSelector` and `gridVirtualizationRowEnabledSelector`\n */\nexport const gridVirtualizationEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabled);\n\n/**\n * Get the enabled state for column virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationColumnEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForColumns);\n\n/**\n * Get the enabled state for row virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationRowEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForRows);\n\n/**\n * Get the render context\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextSelector = createSelector(gridVirtualizationSelector, state => state.renderContext);\n\n/**\n * Get the render context, with only columns filled in.\n * This is cached, so it can be used to only re-render when the column interval changes.\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextColumnsSelector = createSelectorMemoized(state => state.virtualization.renderContext.firstColumnIndex, state => state.virtualization.renderContext.lastColumnIndex, (firstColumnIndex, lastColumnIndex) => ({\n  firstColumnIndex,\n  lastColumnIndex\n}));"], "mappings": "AAAA,SAASA,cAAc,EAAEC,sBAAsB,QAAQ,kCAAkC;AACzF;AACA;AACA;AACA;AACA,OAAO,MAAMC,0BAA0B,GAAGC,KAAK,IAAIA,KAAK,CAACC,cAAc;;AAEvE;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iCAAiC,GAAGL,cAAc,CAACE,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACG,OAAO,CAAC;;AAEnH;AACA;AACA;AACA;AACA,OAAO,MAAMC,uCAAuC,GAAGP,cAAc,CAACE,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACK,iBAAiB,CAAC;;AAEnI;AACA;AACA;AACA;AACA,OAAO,MAAMC,oCAAoC,GAAGT,cAAc,CAACE,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACO,cAAc,CAAC;;AAE7H;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,yBAAyB,GAAGX,cAAc,CAACE,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACS,aAAa,CAAC;;AAEjH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gCAAgC,GAAGZ,sBAAsB,CAACE,KAAK,IAAIA,KAAK,CAACC,cAAc,CAACQ,aAAa,CAACE,gBAAgB,EAAEX,KAAK,IAAIA,KAAK,CAACC,cAAc,CAACQ,aAAa,CAACG,eAAe,EAAE,CAACD,gBAAgB,EAAEC,eAAe,MAAM;EACxOD,gBAAgB;EAChBC;AACF,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}