{"ast": null, "code": "import * as React from 'react';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nconst isDevEnvironment = process.env.NODE_ENV === 'development';\nconst noop = () => {};\nexport function useResizeObserver(ref, fn, enabled) {\n  const fnRef = React.useRef(null);\n  fnRef.current = fn;\n  useEnhancedEffect(() => {\n    if (enabled === false || typeof ResizeObserver === 'undefined') {\n      return noop;\n    }\n    let frameID = 0;\n    const target = ref.current;\n    const observer = new ResizeObserver(entries => {\n      // See https://github.com/mui/mui-x/issues/8733\n      // In dev, we avoid the React warning by moving the task to the next frame.\n      // In prod, we want the task to run in the same frame as to avoid tear.\n      if (isDevEnvironment) {\n        frameID = requestAnimationFrame(() => {\n          fnRef.current(entries);\n        });\n      } else {\n        fnRef.current(entries);\n      }\n    });\n    if (target) {\n      observer.observe(target);\n    }\n    return () => {\n      if (frameID) {\n        cancelAnimationFrame(frameID);\n      }\n      observer.disconnect();\n    };\n  }, [ref, enabled]);\n}", "map": {"version": 3, "names": ["React", "unstable_useEnhancedEffect", "useEnhancedEffect", "isDevEnvironment", "process", "env", "NODE_ENV", "noop", "useResizeObserver", "ref", "fn", "enabled", "fnRef", "useRef", "current", "ResizeObserver", "frameID", "target", "observer", "entries", "requestAnimationFrame", "observe", "cancelAnimationFrame", "disconnect"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-internals/useResizeObserver/useResizeObserver.js"], "sourcesContent": ["import * as React from 'react';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nconst isDevEnvironment = process.env.NODE_ENV === 'development';\nconst noop = () => {};\nexport function useResizeObserver(ref, fn, enabled) {\n  const fnRef = React.useRef(null);\n  fnRef.current = fn;\n  useEnhancedEffect(() => {\n    if (enabled === false || typeof ResizeObserver === 'undefined') {\n      return noop;\n    }\n    let frameID = 0;\n    const target = ref.current;\n    const observer = new ResizeObserver(entries => {\n      // See https://github.com/mui/mui-x/issues/8733\n      // In dev, we avoid the React warning by moving the task to the next frame.\n      // In prod, we want the task to run in the same frame as to avoid tear.\n      if (isDevEnvironment) {\n        frameID = requestAnimationFrame(() => {\n          fnRef.current(entries);\n        });\n      } else {\n        fnRef.current(entries);\n      }\n    });\n    if (target) {\n      observer.observe(target);\n    }\n    return () => {\n      if (frameID) {\n        cancelAnimationFrame(frameID);\n      }\n      observer.disconnect();\n    };\n  }, [ref, enabled]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC5E,MAAMC,gBAAgB,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;AAC/D,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB,OAAO,SAASC,iBAAiBA,CAACC,GAAG,EAAEC,EAAE,EAAEC,OAAO,EAAE;EAClD,MAAMC,KAAK,GAAGZ,KAAK,CAACa,MAAM,CAAC,IAAI,CAAC;EAChCD,KAAK,CAACE,OAAO,GAAGJ,EAAE;EAClBR,iBAAiB,CAAC,MAAM;IACtB,IAAIS,OAAO,KAAK,KAAK,IAAI,OAAOI,cAAc,KAAK,WAAW,EAAE;MAC9D,OAAOR,IAAI;IACb;IACA,IAAIS,OAAO,GAAG,CAAC;IACf,MAAMC,MAAM,GAAGR,GAAG,CAACK,OAAO;IAC1B,MAAMI,QAAQ,GAAG,IAAIH,cAAc,CAACI,OAAO,IAAI;MAC7C;MACA;MACA;MACA,IAAIhB,gBAAgB,EAAE;QACpBa,OAAO,GAAGI,qBAAqB,CAAC,MAAM;UACpCR,KAAK,CAACE,OAAO,CAACK,OAAO,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLP,KAAK,CAACE,OAAO,CAACK,OAAO,CAAC;MACxB;IACF,CAAC,CAAC;IACF,IAAIF,MAAM,EAAE;MACVC,QAAQ,CAACG,OAAO,CAACJ,MAAM,CAAC;IAC1B;IACA,OAAO,MAAM;MACX,IAAID,OAAO,EAAE;QACXM,oBAAoB,CAACN,OAAO,CAAC;MAC/B;MACAE,QAAQ,CAACK,UAAU,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACd,GAAG,EAAEE,OAAO,CAAC,CAAC;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}