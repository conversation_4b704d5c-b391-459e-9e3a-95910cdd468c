{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridSignature, useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridRowMaximumTreeDepthSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowSelectionStateSelector, selectedGridRowsSelector, selectedIdsLookupSelector } from \"./gridRowSelectionSelector.js\";\nimport { gridPaginatedVisibleSortedGridRowIdsSelector } from \"../pagination/index.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { gridExpandedSortedRowIdsSelector, gridFilteredRowsLookupSelector } from \"../filter/gridFilterSelector.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isKeyboardEvent, isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { useGridVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../constants/gridDetailPanelToggleField.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { isMultipleRowSelectionEnabled, findRowsToSelect, findRowsToDeselect } from \"./utils.js\";\nconst getSelectionModelPropValue = (selectionModelProp, prevSelectionModel) => {\n  if (selectionModelProp == null) {\n    return selectionModelProp;\n  }\n  if (Array.isArray(selectionModelProp)) {\n    return selectionModelProp;\n  }\n  if (prevSelectionModel && prevSelectionModel[0] === selectionModelProp) {\n    return prevSelectionModel;\n  }\n  return [selectionModelProp];\n};\nexport const rowSelectionStateInitializer = (state, props) => _extends({}, state, {\n  rowSelection: props.rowSelection ? getSelectionModelPropValue(props.rowSelectionModel) ?? [] : []\n});\n\n/**\n * @requires useGridRows (state, method) - can be after\n * @requires useGridParamsApi (method) - can be after\n * @requires useGridFocus (state) - can be after\n * @requires useGridKeyboardNavigation (`cellKeyDown` event must first be consumed by it)\n */\nexport const useGridRowSelection = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSelection');\n  const runIfRowSelectionIsEnabled = React.useCallback(callback => function () {\n    if (props.rowSelection) {\n      callback(...arguments);\n    }\n  }, [props.rowSelection]);\n  const applyAutoSelection = props.signature !== GridSignature.DataGrid && (props.rowSelectionPropagation?.parents || props.rowSelectionPropagation?.descendants);\n  const propRowSelectionModel = React.useMemo(() => {\n    return getSelectionModelPropValue(props.rowSelectionModel, gridRowSelectionStateSelector(apiRef.current.state));\n  }, [apiRef, props.rowSelectionModel]);\n  const lastRowToggled = React.useRef(null);\n  apiRef.current.registerControlState({\n    stateId: 'rowSelection',\n    propModel: propRowSelectionModel,\n    propOnChange: props.onRowSelectionModelChange,\n    stateSelector: gridRowSelectionStateSelector,\n    changeEvent: 'rowSelectionChange'\n  });\n  const {\n    checkboxSelection,\n    disableRowSelectionOnClick,\n    isRowSelectable: propIsRowSelectable\n  } = props;\n  const canHaveMultipleSelection = isMultipleRowSelectionEnabled(props);\n  const visibleRows = useGridVisibleRows(apiRef, props);\n  const tree = useGridSelector(apiRef, gridRowTreeSelector);\n  const isNestedData = useGridSelector(apiRef, gridRowMaximumTreeDepthSelector) > 1;\n  const expandMouseRowRangeSelection = React.useCallback(id => {\n    let endId = id;\n    const startId = lastRowToggled.current ?? id;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (isSelected) {\n      const visibleRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n      const startIndex = visibleRowIds.findIndex(rowId => rowId === startId);\n      const endIndex = visibleRowIds.findIndex(rowId => rowId === endId);\n      if (startIndex === endIndex) {\n        return;\n      }\n      if (startIndex > endIndex) {\n        endId = visibleRowIds[endIndex + 1];\n      } else {\n        endId = visibleRowIds[endIndex - 1];\n      }\n    }\n    lastRowToggled.current = id;\n    apiRef.current.selectRowRange({\n      startId,\n      endId\n    }, !isSelected);\n  }, [apiRef]);\n\n  /*\n   * API METHODS\n   */\n  const setRowSelectionModel = React.useCallback(model => {\n    if (props.signature === GridSignature.DataGrid && !canHaveMultipleSelection && Array.isArray(model) && model.length > 1) {\n      throw new Error(['MUI X: `rowSelectionModel` can only contain 1 item in DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection.'].join('\\n'));\n    }\n    const currentModel = gridRowSelectionStateSelector(apiRef.current.state);\n    if (currentModel !== model) {\n      logger.debug(`Setting selection model`);\n      apiRef.current.setState(state => _extends({}, state, {\n        rowSelection: props.rowSelection ? model : []\n      }));\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef, logger, props.rowSelection, props.signature, canHaveMultipleSelection]);\n  const isRowSelected = React.useCallback(id => gridRowSelectionStateSelector(apiRef.current.state).includes(id), [apiRef]);\n  const isRowSelectable = React.useCallback(id => {\n    if (props.rowSelection === false) {\n      return false;\n    }\n    if (propIsRowSelectable && !propIsRowSelectable(apiRef.current.getRowParams(id))) {\n      return false;\n    }\n    const rowNode = apiRef.current.getRowNode(id);\n    if (rowNode?.type === 'footer' || rowNode?.type === 'pinnedRow') {\n      return false;\n    }\n    return true;\n  }, [apiRef, props.rowSelection, propIsRowSelectable]);\n  const getSelectedRows = React.useCallback(() => selectedGridRowsSelector(apiRef), [apiRef]);\n  const selectRow = React.useCallback(function (id) {\n    let isSelected = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    let resetSelection = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    if (!apiRef.current.isRowSelectable(id)) {\n      return;\n    }\n    lastRowToggled.current = id;\n    if (resetSelection) {\n      logger.debug(`Setting selection for row ${id}`);\n      const newSelection = [];\n      const addRow = rowId => {\n        newSelection.push(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      }\n      apiRef.current.setRowSelectionModel(newSelection);\n    } else {\n      logger.debug(`Toggling selection for row ${id}`);\n      const selection = gridRowSelectionStateSelector(apiRef.current.state);\n      const newSelection = new Set(selection);\n      newSelection.delete(id);\n      const addRow = rowId => {\n        newSelection.add(rowId);\n      };\n      const removeRow = rowId => {\n        newSelection.delete(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      } else if (applyAutoSelection) {\n        findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n      }\n      const isSelectionValid = newSelection.size < 2 || canHaveMultipleSelection;\n      if (isSelectionValid) {\n        apiRef.current.setRowSelectionModel(Array.from(newSelection));\n      }\n    }\n  }, [apiRef, logger, applyAutoSelection, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, canHaveMultipleSelection]);\n  const selectRows = React.useCallback(function (ids) {\n    let isSelected = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    let resetSelection = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    logger.debug(`Setting selection for several rows`);\n    const selectableIds = ids.filter(id => apiRef.current.isRowSelectable(id));\n    let newSelection;\n    if (resetSelection) {\n      if (isSelected) {\n        newSelection = selectableIds;\n        if (applyAutoSelection) {\n          const addRow = rowId => {\n            newSelection.push(rowId);\n          };\n          selectableIds.forEach(id => {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          });\n        }\n      } else {\n        newSelection = [];\n      }\n    } else {\n      // We clone the existing object to avoid mutating the same object returned by the selector to others part of the project\n      const selectionLookup = _extends({}, selectedIdsLookupSelector(apiRef));\n      const addRow = rowId => {\n        selectionLookup[rowId] = rowId;\n      };\n      const removeRow = rowId => {\n        delete selectionLookup[rowId];\n      };\n      selectableIds.forEach(id => {\n        if (isSelected) {\n          selectionLookup[id] = id;\n          if (applyAutoSelection) {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          }\n        } else {\n          removeRow(id);\n          if (applyAutoSelection) {\n            findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n          }\n        }\n      });\n      newSelection = Object.values(selectionLookup);\n    }\n    const isSelectionValid = newSelection.length < 2 || canHaveMultipleSelection;\n    if (isSelectionValid) {\n      apiRef.current.setRowSelectionModel(newSelection);\n    }\n  }, [logger, applyAutoSelection, canHaveMultipleSelection, apiRef, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents]);\n  const selectRowRange = React.useCallback(function (_ref) {\n    let {\n      startId,\n      endId\n    } = _ref;\n    let isSelected = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    let resetSelection = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    if (!apiRef.current.getRow(startId) || !apiRef.current.getRow(endId)) {\n      return;\n    }\n    logger.debug(`Expanding selection from row ${startId} to row ${endId}`);\n\n    // Using rows from all pages allow to select a range across several pages\n    const allPagesRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n    const startIndex = allPagesRowIds.indexOf(startId);\n    const endIndex = allPagesRowIds.indexOf(endId);\n    const [start, end] = startIndex > endIndex ? [endIndex, startIndex] : [startIndex, endIndex];\n    const rowsBetweenStartAndEnd = allPagesRowIds.slice(start, end + 1);\n    apiRef.current.selectRows(rowsBetweenStartAndEnd, isSelected, resetSelection);\n  }, [apiRef, logger]);\n  const selectionPublicApi = {\n    selectRow,\n    setRowSelectionModel,\n    getSelectedRows,\n    isRowSelected,\n    isRowSelectable\n  };\n  const selectionPrivateApi = {\n    selectRows,\n    selectRowRange\n  };\n  useGridApiMethod(apiRef, selectionPublicApi, 'public');\n  useGridApiMethod(apiRef, selectionPrivateApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /*\n   * EVENTS\n   */\n  const removeOutdatedSelection = React.useCallback(function () {\n    let sortModelUpdated = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n\n    // We clone the existing object to avoid mutating the same object returned by the selector to others part of the project\n    const selectionLookup = _extends({}, selectedIdsLookupSelector(apiRef));\n    let hasChanged = false;\n    currentSelection.forEach(id => {\n      if (filteredRowsLookup[id] === false) {\n        if (props.keepNonExistentRowsSelected) {\n          return;\n        }\n        delete selectionLookup[id];\n        hasChanged = true;\n        return;\n      }\n      if (!props.rowSelectionPropagation?.parents) {\n        return;\n      }\n      const node = tree[id];\n      if (node.type === 'group') {\n        const isAutoGenerated = node.isAutoGenerated;\n        if (isAutoGenerated) {\n          delete selectionLookup[id];\n          hasChanged = true;\n          return;\n        }\n        // Keep previously selected tree data parents selected if all their children are filtered out\n        if (!node.children.every(childId => filteredRowsLookup[childId] === false)) {\n          delete selectionLookup[id];\n          hasChanged = true;\n        }\n      }\n    });\n    if (hasChanged || isNestedData && !sortModelUpdated) {\n      const newSelection = Object.values(selectionLookup);\n      if (isNestedData) {\n        apiRef.current.selectRows(newSelection, true, true);\n      } else {\n        apiRef.current.setRowSelectionModel(newSelection);\n      }\n    }\n  }, [apiRef, isNestedData, props.rowSelectionPropagation?.parents, props.keepNonExistentRowsSelected, tree]);\n  const handleSingleRowSelection = React.useCallback((id, event) => {\n    const hasCtrlKey = event.metaKey || event.ctrlKey;\n\n    // multiple selection is only allowed if:\n    // - it is a checkboxSelection\n    // - it is a keyboard selection\n    // - Ctrl is pressed\n\n    const isMultipleSelectionDisabled = !checkboxSelection && !hasCtrlKey && !isKeyboardEvent(event);\n    const resetSelection = !canHaveMultipleSelection || isMultipleSelectionDisabled;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (resetSelection) {\n      apiRef.current.selectRow(id, !isMultipleSelectionDisabled ? !isSelected : true, true);\n    } else {\n      apiRef.current.selectRow(id, !isSelected, false);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection]);\n  const handleRowClick = React.useCallback((params, event) => {\n    if (disableRowSelectionOnClick) {\n      return;\n    }\n    const field = event.target.closest(`.${gridClasses.cell}`)?.getAttribute('data-field');\n    if (field === GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // click on checkbox should not trigger row selection\n      return;\n    }\n    if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n      // click to open the detail panel should not select the row\n      return;\n    }\n    if (field) {\n      const column = apiRef.current.getColumn(field);\n      if (column?.type === GRID_ACTIONS_COLUMN_TYPE) {\n        return;\n      }\n    }\n    const rowNode = apiRef.current.getRowNode(params.id);\n    if (rowNode.type === 'pinnedRow') {\n      return;\n    }\n    if (event.shiftKey && canHaveMultipleSelection) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      handleSingleRowSelection(params.id, event);\n    }\n  }, [disableRowSelectionOnClick, canHaveMultipleSelection, apiRef, expandMouseRowRangeSelection, handleSingleRowSelection]);\n  const preventSelectionOnShift = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.shiftKey) {\n      window.getSelection()?.removeAllRanges();\n    }\n  }, [canHaveMultipleSelection]);\n  const handleRowSelectionCheckboxChange = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.nativeEvent.shiftKey) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      apiRef.current.selectRow(params.id, params.value, !canHaveMultipleSelection);\n    }\n  }, [apiRef, expandMouseRowRangeSelection, canHaveMultipleSelection]);\n  const handleHeaderSelectionCheckboxChange = React.useCallback(params => {\n    const rowsToBeSelected = props.pagination && props.checkboxSelectionVisibleOnly && props.paginationMode === 'client' ? gridPaginatedVisibleSortedGridRowIdsSelector(apiRef) : gridExpandedSortedRowIdsSelector(apiRef);\n    apiRef.current.selectRows(rowsToBeSelected, params.value);\n  }, [apiRef, props.checkboxSelectionVisibleOnly, props.pagination, props.paginationMode]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Get the most recent cell mode because it may have been changed by another listener\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.Edit) {\n      return;\n    }\n\n    // Ignore portal\n    // Do not apply shortcuts if the focus is not on the cell root component\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n    if (isNavigationKey(event.key) && event.shiftKey) {\n      // The cell that has focus after the keyboard navigation\n      const focusCell = gridFocusCellSelector(apiRef);\n      if (focusCell && focusCell.id !== params.id) {\n        event.preventDefault();\n        const isNextRowSelected = apiRef.current.isRowSelected(focusCell.id);\n        if (!canHaveMultipleSelection) {\n          apiRef.current.selectRow(focusCell.id, !isNextRowSelected, true);\n          return;\n        }\n        const newRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(focusCell.id);\n        const previousRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(params.id);\n        let start;\n        let end;\n        if (newRowIndex > previousRowIndex) {\n          if (isNextRowSelected) {\n            // We are navigating to the bottom of the page and adding selected rows\n            start = previousRowIndex;\n            end = newRowIndex - 1;\n          } else {\n            // We are navigating to the bottom of the page and removing selected rows\n            start = previousRowIndex;\n            end = newRowIndex;\n          }\n        } else {\n          // eslint-disable-next-line no-lonely-if\n          if (isNextRowSelected) {\n            // We are navigating to the top of the page and removing selected rows\n            start = newRowIndex + 1;\n            end = previousRowIndex;\n          } else {\n            // We are navigating to the top of the page and adding selected rows\n            start = newRowIndex;\n            end = previousRowIndex;\n          }\n        }\n        const rowsBetweenStartAndEnd = visibleRows.rows.slice(start, end + 1).map(row => row.id);\n        apiRef.current.selectRows(rowsBetweenStartAndEnd, !isNextRowSelected);\n        return;\n      }\n    }\n    if (event.key === ' ' && event.shiftKey) {\n      event.preventDefault();\n      handleSingleRowSelection(params.id, event);\n      return;\n    }\n    if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {\n      event.preventDefault();\n      selectRows(apiRef.current.getAllRowIds(), true);\n    }\n  }, [apiRef, handleSingleRowSelection, selectRows, visibleRows.rows, canHaveMultipleSelection]);\n  useGridApiEventHandler(apiRef, 'sortedRowsSet', runIfRowSelectionIsEnabled(() => removeOutdatedSelection(true)));\n  useGridApiEventHandler(apiRef, 'filteredRowsSet', runIfRowSelectionIsEnabled(removeOutdatedSelection));\n  useGridApiEventHandler(apiRef, 'rowClick', runIfRowSelectionIsEnabled(handleRowClick));\n  useGridApiEventHandler(apiRef, 'rowSelectionCheckboxChange', runIfRowSelectionIsEnabled(handleRowSelectionCheckboxChange));\n  useGridApiEventHandler(apiRef, 'headerSelectionCheckboxChange', handleHeaderSelectionCheckboxChange);\n  useGridApiEventHandler(apiRef, 'cellMouseDown', runIfRowSelectionIsEnabled(preventSelectionOnShift));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfRowSelectionIsEnabled(handleCellKeyDown));\n\n  /*\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (propRowSelectionModel !== undefined) {\n      apiRef.current.setRowSelectionModel(propRowSelectionModel);\n    }\n  }, [apiRef, propRowSelectionModel, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection) {\n      apiRef.current.setRowSelectionModel([]);\n    }\n  }, [apiRef, props.rowSelection]);\n  const isStateControlled = propRowSelectionModel != null;\n  React.useEffect(() => {\n    if (isStateControlled || !props.rowSelection) {\n      return;\n    }\n\n    // props.isRowSelectable changed\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    if (isRowSelectable) {\n      const newSelection = currentSelection.filter(id => isRowSelectable(id));\n      if (newSelection.length < currentSelection.length) {\n        apiRef.current.setRowSelectionModel(newSelection);\n      }\n    }\n  }, [apiRef, isRowSelectable, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection || isStateControlled) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    if (!canHaveMultipleSelection && currentSelection.length > 1) {\n      // See https://github.com/mui/mui-x/issues/8455\n      apiRef.current.setRowSelectionModel([]);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    runIfRowSelectionIsEnabled(removeOutdatedSelection);\n  }, [removeOutdatedSelection, runIfRowSelectionIsEnabled]);\n};", "map": {"version": 3, "names": ["_extends", "React", "GridSignature", "useGridApiEventHandler", "useGridApiMethod", "useGridLogger", "useGridSelector", "gridRowMaximumTreeDepthSelector", "gridRowTreeSelector", "gridRowSelectionStateSelector", "selectedGridRowsSelector", "selectedIdsLookupSelector", "gridPaginatedVisibleSortedGridRowIdsSelector", "gridFocusCellSelector", "gridExpandedSortedRowIdsSelector", "gridFilteredRowsLookupSelector", "GRID_CHECKBOX_SELECTION_COL_DEF", "GRID_ACTIONS_COLUMN_TYPE", "GridCellModes", "isKeyboardEvent", "isNavigationKey", "useGridVisibleRows", "GRID_DETAIL_PANEL_TOGGLE_FIELD", "gridClasses", "isEventTargetInPortal", "isMultipleRowSelectionEnabled", "findRowsToSelect", "findRowsToDeselect", "getSelectionModelPropValue", "selectionModelProp", "prevSelectionModel", "Array", "isArray", "rowSelectionStateInitializer", "state", "props", "rowSelection", "rowSelectionModel", "useGridRowSelection", "apiRef", "logger", "runIfRowSelectionIsEnabled", "useCallback", "callback", "arguments", "applyAutoSelection", "signature", "DataGrid", "rowSelectionPropagation", "parents", "descendants", "propRowSelectionModel", "useMemo", "current", "lastRowToggled", "useRef", "registerControlState", "stateId", "propModel", "propOnChange", "onRowSelectionModelChange", "stateSelector", "changeEvent", "checkboxSelection", "disableRowSelectionOnClick", "isRowSelectable", "propIsRowSelectable", "canHaveMultipleSelection", "visibleRows", "tree", "isNestedData", "expandMouseRowRangeSelection", "id", "endId", "startId", "isSelected", "isRowSelected", "visibleRowIds", "startIndex", "findIndex", "rowId", "endIndex", "selectRowRange", "setRowSelectionModel", "model", "length", "Error", "join", "currentModel", "debug", "setState", "forceUpdate", "includes", "getRowParams", "rowNode", "getRowNode", "type", "getSelectedRows", "selectRow", "undefined", "resetSelection", "newSelection", "addRow", "push", "selection", "Set", "delete", "add", "removeRow", "isSelectionValid", "size", "from", "selectRows", "ids", "selectableIds", "filter", "for<PERSON>ach", "selection<PERSON><PERSON><PERSON>", "Object", "values", "_ref", "getRow", "allPagesRowIds", "indexOf", "start", "end", "rowsBetweenStartAndEnd", "slice", "selectionPublicApi", "selectionPrivateApi", "removeOutdatedSelection", "sortModelUpdated", "currentSelection", "filteredRowsLookup", "has<PERSON><PERSON>ed", "keepNonExistentRowsSelected", "node", "isAutoGenerated", "children", "every", "childId", "handleSingleRowSelection", "event", "hasCtrlKey", "metaKey", "ctrl<PERSON>ey", "isMultipleSelectionDisabled", "handleRowClick", "params", "field", "target", "closest", "cell", "getAttribute", "column", "getColumn", "shift<PERSON>ey", "preventSelectionOnShift", "window", "getSelection", "removeAllRanges", "handleRowSelectionCheckboxChange", "nativeEvent", "value", "handleHeaderSelectionCheckboxChange", "rowsToBeSelected", "pagination", "checkboxSelectionVisibleOnly", "paginationMode", "handleCellKeyDown", "getCellMode", "Edit", "key", "focusCell", "preventDefault", "isNextRowSelected", "newRowIndex", "getRowIndexRelativeToVisibleRows", "previousRowIndex", "rows", "map", "row", "getAllRowIds", "useEffect", "isStateControlled"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/rowSelection/useGridRowSelection.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridSignature, useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridRowMaximumTreeDepthSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowSelectionStateSelector, selectedGridRowsSelector, selectedIdsLookupSelector } from \"./gridRowSelectionSelector.js\";\nimport { gridPaginatedVisibleSortedGridRowIdsSelector } from \"../pagination/index.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { gridExpandedSortedRowIdsSelector, gridFilteredRowsLookupSelector } from \"../filter/gridFilterSelector.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isKeyboardEvent, isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { useGridVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../constants/gridDetailPanelToggleField.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { isMultipleRowSelectionEnabled, findRowsToSelect, findRowsToDeselect } from \"./utils.js\";\nconst getSelectionModelPropValue = (selectionModelProp, prevSelectionModel) => {\n  if (selectionModelProp == null) {\n    return selectionModelProp;\n  }\n  if (Array.isArray(selectionModelProp)) {\n    return selectionModelProp;\n  }\n  if (prevSelectionModel && prevSelectionModel[0] === selectionModelProp) {\n    return prevSelectionModel;\n  }\n  return [selectionModelProp];\n};\nexport const rowSelectionStateInitializer = (state, props) => _extends({}, state, {\n  rowSelection: props.rowSelection ? getSelectionModelPropValue(props.rowSelectionModel) ?? [] : []\n});\n\n/**\n * @requires useGridRows (state, method) - can be after\n * @requires useGridParamsApi (method) - can be after\n * @requires useGridFocus (state) - can be after\n * @requires useGridKeyboardNavigation (`cellKeyDown` event must first be consumed by it)\n */\nexport const useGridRowSelection = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSelection');\n  const runIfRowSelectionIsEnabled = React.useCallback(callback => (...args) => {\n    if (props.rowSelection) {\n      callback(...args);\n    }\n  }, [props.rowSelection]);\n  const applyAutoSelection = props.signature !== GridSignature.DataGrid && (props.rowSelectionPropagation?.parents || props.rowSelectionPropagation?.descendants);\n  const propRowSelectionModel = React.useMemo(() => {\n    return getSelectionModelPropValue(props.rowSelectionModel, gridRowSelectionStateSelector(apiRef.current.state));\n  }, [apiRef, props.rowSelectionModel]);\n  const lastRowToggled = React.useRef(null);\n  apiRef.current.registerControlState({\n    stateId: 'rowSelection',\n    propModel: propRowSelectionModel,\n    propOnChange: props.onRowSelectionModelChange,\n    stateSelector: gridRowSelectionStateSelector,\n    changeEvent: 'rowSelectionChange'\n  });\n  const {\n    checkboxSelection,\n    disableRowSelectionOnClick,\n    isRowSelectable: propIsRowSelectable\n  } = props;\n  const canHaveMultipleSelection = isMultipleRowSelectionEnabled(props);\n  const visibleRows = useGridVisibleRows(apiRef, props);\n  const tree = useGridSelector(apiRef, gridRowTreeSelector);\n  const isNestedData = useGridSelector(apiRef, gridRowMaximumTreeDepthSelector) > 1;\n  const expandMouseRowRangeSelection = React.useCallback(id => {\n    let endId = id;\n    const startId = lastRowToggled.current ?? id;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (isSelected) {\n      const visibleRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n      const startIndex = visibleRowIds.findIndex(rowId => rowId === startId);\n      const endIndex = visibleRowIds.findIndex(rowId => rowId === endId);\n      if (startIndex === endIndex) {\n        return;\n      }\n      if (startIndex > endIndex) {\n        endId = visibleRowIds[endIndex + 1];\n      } else {\n        endId = visibleRowIds[endIndex - 1];\n      }\n    }\n    lastRowToggled.current = id;\n    apiRef.current.selectRowRange({\n      startId,\n      endId\n    }, !isSelected);\n  }, [apiRef]);\n\n  /*\n   * API METHODS\n   */\n  const setRowSelectionModel = React.useCallback(model => {\n    if (props.signature === GridSignature.DataGrid && !canHaveMultipleSelection && Array.isArray(model) && model.length > 1) {\n      throw new Error(['MUI X: `rowSelectionModel` can only contain 1 item in DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection.'].join('\\n'));\n    }\n    const currentModel = gridRowSelectionStateSelector(apiRef.current.state);\n    if (currentModel !== model) {\n      logger.debug(`Setting selection model`);\n      apiRef.current.setState(state => _extends({}, state, {\n        rowSelection: props.rowSelection ? model : []\n      }));\n      apiRef.current.forceUpdate();\n    }\n  }, [apiRef, logger, props.rowSelection, props.signature, canHaveMultipleSelection]);\n  const isRowSelected = React.useCallback(id => gridRowSelectionStateSelector(apiRef.current.state).includes(id), [apiRef]);\n  const isRowSelectable = React.useCallback(id => {\n    if (props.rowSelection === false) {\n      return false;\n    }\n    if (propIsRowSelectable && !propIsRowSelectable(apiRef.current.getRowParams(id))) {\n      return false;\n    }\n    const rowNode = apiRef.current.getRowNode(id);\n    if (rowNode?.type === 'footer' || rowNode?.type === 'pinnedRow') {\n      return false;\n    }\n    return true;\n  }, [apiRef, props.rowSelection, propIsRowSelectable]);\n  const getSelectedRows = React.useCallback(() => selectedGridRowsSelector(apiRef), [apiRef]);\n  const selectRow = React.useCallback((id, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.isRowSelectable(id)) {\n      return;\n    }\n    lastRowToggled.current = id;\n    if (resetSelection) {\n      logger.debug(`Setting selection for row ${id}`);\n      const newSelection = [];\n      const addRow = rowId => {\n        newSelection.push(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      }\n      apiRef.current.setRowSelectionModel(newSelection);\n    } else {\n      logger.debug(`Toggling selection for row ${id}`);\n      const selection = gridRowSelectionStateSelector(apiRef.current.state);\n      const newSelection = new Set(selection);\n      newSelection.delete(id);\n      const addRow = rowId => {\n        newSelection.add(rowId);\n      };\n      const removeRow = rowId => {\n        newSelection.delete(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      } else if (applyAutoSelection) {\n        findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n      }\n      const isSelectionValid = newSelection.size < 2 || canHaveMultipleSelection;\n      if (isSelectionValid) {\n        apiRef.current.setRowSelectionModel(Array.from(newSelection));\n      }\n    }\n  }, [apiRef, logger, applyAutoSelection, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, canHaveMultipleSelection]);\n  const selectRows = React.useCallback((ids, isSelected = true, resetSelection = false) => {\n    logger.debug(`Setting selection for several rows`);\n    const selectableIds = ids.filter(id => apiRef.current.isRowSelectable(id));\n    let newSelection;\n    if (resetSelection) {\n      if (isSelected) {\n        newSelection = selectableIds;\n        if (applyAutoSelection) {\n          const addRow = rowId => {\n            newSelection.push(rowId);\n          };\n          selectableIds.forEach(id => {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          });\n        }\n      } else {\n        newSelection = [];\n      }\n    } else {\n      // We clone the existing object to avoid mutating the same object returned by the selector to others part of the project\n      const selectionLookup = _extends({}, selectedIdsLookupSelector(apiRef));\n      const addRow = rowId => {\n        selectionLookup[rowId] = rowId;\n      };\n      const removeRow = rowId => {\n        delete selectionLookup[rowId];\n      };\n      selectableIds.forEach(id => {\n        if (isSelected) {\n          selectionLookup[id] = id;\n          if (applyAutoSelection) {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          }\n        } else {\n          removeRow(id);\n          if (applyAutoSelection) {\n            findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n          }\n        }\n      });\n      newSelection = Object.values(selectionLookup);\n    }\n    const isSelectionValid = newSelection.length < 2 || canHaveMultipleSelection;\n    if (isSelectionValid) {\n      apiRef.current.setRowSelectionModel(newSelection);\n    }\n  }, [logger, applyAutoSelection, canHaveMultipleSelection, apiRef, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents]);\n  const selectRowRange = React.useCallback(({\n    startId,\n    endId\n  }, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.getRow(startId) || !apiRef.current.getRow(endId)) {\n      return;\n    }\n    logger.debug(`Expanding selection from row ${startId} to row ${endId}`);\n\n    // Using rows from all pages allow to select a range across several pages\n    const allPagesRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n    const startIndex = allPagesRowIds.indexOf(startId);\n    const endIndex = allPagesRowIds.indexOf(endId);\n    const [start, end] = startIndex > endIndex ? [endIndex, startIndex] : [startIndex, endIndex];\n    const rowsBetweenStartAndEnd = allPagesRowIds.slice(start, end + 1);\n    apiRef.current.selectRows(rowsBetweenStartAndEnd, isSelected, resetSelection);\n  }, [apiRef, logger]);\n  const selectionPublicApi = {\n    selectRow,\n    setRowSelectionModel,\n    getSelectedRows,\n    isRowSelected,\n    isRowSelectable\n  };\n  const selectionPrivateApi = {\n    selectRows,\n    selectRowRange\n  };\n  useGridApiMethod(apiRef, selectionPublicApi, 'public');\n  useGridApiMethod(apiRef, selectionPrivateApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /*\n   * EVENTS\n   */\n  const removeOutdatedSelection = React.useCallback((sortModelUpdated = false) => {\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n\n    // We clone the existing object to avoid mutating the same object returned by the selector to others part of the project\n    const selectionLookup = _extends({}, selectedIdsLookupSelector(apiRef));\n    let hasChanged = false;\n    currentSelection.forEach(id => {\n      if (filteredRowsLookup[id] === false) {\n        if (props.keepNonExistentRowsSelected) {\n          return;\n        }\n        delete selectionLookup[id];\n        hasChanged = true;\n        return;\n      }\n      if (!props.rowSelectionPropagation?.parents) {\n        return;\n      }\n      const node = tree[id];\n      if (node.type === 'group') {\n        const isAutoGenerated = node.isAutoGenerated;\n        if (isAutoGenerated) {\n          delete selectionLookup[id];\n          hasChanged = true;\n          return;\n        }\n        // Keep previously selected tree data parents selected if all their children are filtered out\n        if (!node.children.every(childId => filteredRowsLookup[childId] === false)) {\n          delete selectionLookup[id];\n          hasChanged = true;\n        }\n      }\n    });\n    if (hasChanged || isNestedData && !sortModelUpdated) {\n      const newSelection = Object.values(selectionLookup);\n      if (isNestedData) {\n        apiRef.current.selectRows(newSelection, true, true);\n      } else {\n        apiRef.current.setRowSelectionModel(newSelection);\n      }\n    }\n  }, [apiRef, isNestedData, props.rowSelectionPropagation?.parents, props.keepNonExistentRowsSelected, tree]);\n  const handleSingleRowSelection = React.useCallback((id, event) => {\n    const hasCtrlKey = event.metaKey || event.ctrlKey;\n\n    // multiple selection is only allowed if:\n    // - it is a checkboxSelection\n    // - it is a keyboard selection\n    // - Ctrl is pressed\n\n    const isMultipleSelectionDisabled = !checkboxSelection && !hasCtrlKey && !isKeyboardEvent(event);\n    const resetSelection = !canHaveMultipleSelection || isMultipleSelectionDisabled;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (resetSelection) {\n      apiRef.current.selectRow(id, !isMultipleSelectionDisabled ? !isSelected : true, true);\n    } else {\n      apiRef.current.selectRow(id, !isSelected, false);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection]);\n  const handleRowClick = React.useCallback((params, event) => {\n    if (disableRowSelectionOnClick) {\n      return;\n    }\n    const field = event.target.closest(`.${gridClasses.cell}`)?.getAttribute('data-field');\n    if (field === GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // click on checkbox should not trigger row selection\n      return;\n    }\n    if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n      // click to open the detail panel should not select the row\n      return;\n    }\n    if (field) {\n      const column = apiRef.current.getColumn(field);\n      if (column?.type === GRID_ACTIONS_COLUMN_TYPE) {\n        return;\n      }\n    }\n    const rowNode = apiRef.current.getRowNode(params.id);\n    if (rowNode.type === 'pinnedRow') {\n      return;\n    }\n    if (event.shiftKey && canHaveMultipleSelection) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      handleSingleRowSelection(params.id, event);\n    }\n  }, [disableRowSelectionOnClick, canHaveMultipleSelection, apiRef, expandMouseRowRangeSelection, handleSingleRowSelection]);\n  const preventSelectionOnShift = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.shiftKey) {\n      window.getSelection()?.removeAllRanges();\n    }\n  }, [canHaveMultipleSelection]);\n  const handleRowSelectionCheckboxChange = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.nativeEvent.shiftKey) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      apiRef.current.selectRow(params.id, params.value, !canHaveMultipleSelection);\n    }\n  }, [apiRef, expandMouseRowRangeSelection, canHaveMultipleSelection]);\n  const handleHeaderSelectionCheckboxChange = React.useCallback(params => {\n    const rowsToBeSelected = props.pagination && props.checkboxSelectionVisibleOnly && props.paginationMode === 'client' ? gridPaginatedVisibleSortedGridRowIdsSelector(apiRef) : gridExpandedSortedRowIdsSelector(apiRef);\n    apiRef.current.selectRows(rowsToBeSelected, params.value);\n  }, [apiRef, props.checkboxSelectionVisibleOnly, props.pagination, props.paginationMode]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Get the most recent cell mode because it may have been changed by another listener\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.Edit) {\n      return;\n    }\n\n    // Ignore portal\n    // Do not apply shortcuts if the focus is not on the cell root component\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n    if (isNavigationKey(event.key) && event.shiftKey) {\n      // The cell that has focus after the keyboard navigation\n      const focusCell = gridFocusCellSelector(apiRef);\n      if (focusCell && focusCell.id !== params.id) {\n        event.preventDefault();\n        const isNextRowSelected = apiRef.current.isRowSelected(focusCell.id);\n        if (!canHaveMultipleSelection) {\n          apiRef.current.selectRow(focusCell.id, !isNextRowSelected, true);\n          return;\n        }\n        const newRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(focusCell.id);\n        const previousRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(params.id);\n        let start;\n        let end;\n        if (newRowIndex > previousRowIndex) {\n          if (isNextRowSelected) {\n            // We are navigating to the bottom of the page and adding selected rows\n            start = previousRowIndex;\n            end = newRowIndex - 1;\n          } else {\n            // We are navigating to the bottom of the page and removing selected rows\n            start = previousRowIndex;\n            end = newRowIndex;\n          }\n        } else {\n          // eslint-disable-next-line no-lonely-if\n          if (isNextRowSelected) {\n            // We are navigating to the top of the page and removing selected rows\n            start = newRowIndex + 1;\n            end = previousRowIndex;\n          } else {\n            // We are navigating to the top of the page and adding selected rows\n            start = newRowIndex;\n            end = previousRowIndex;\n          }\n        }\n        const rowsBetweenStartAndEnd = visibleRows.rows.slice(start, end + 1).map(row => row.id);\n        apiRef.current.selectRows(rowsBetweenStartAndEnd, !isNextRowSelected);\n        return;\n      }\n    }\n    if (event.key === ' ' && event.shiftKey) {\n      event.preventDefault();\n      handleSingleRowSelection(params.id, event);\n      return;\n    }\n    if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {\n      event.preventDefault();\n      selectRows(apiRef.current.getAllRowIds(), true);\n    }\n  }, [apiRef, handleSingleRowSelection, selectRows, visibleRows.rows, canHaveMultipleSelection]);\n  useGridApiEventHandler(apiRef, 'sortedRowsSet', runIfRowSelectionIsEnabled(() => removeOutdatedSelection(true)));\n  useGridApiEventHandler(apiRef, 'filteredRowsSet', runIfRowSelectionIsEnabled(removeOutdatedSelection));\n  useGridApiEventHandler(apiRef, 'rowClick', runIfRowSelectionIsEnabled(handleRowClick));\n  useGridApiEventHandler(apiRef, 'rowSelectionCheckboxChange', runIfRowSelectionIsEnabled(handleRowSelectionCheckboxChange));\n  useGridApiEventHandler(apiRef, 'headerSelectionCheckboxChange', handleHeaderSelectionCheckboxChange);\n  useGridApiEventHandler(apiRef, 'cellMouseDown', runIfRowSelectionIsEnabled(preventSelectionOnShift));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfRowSelectionIsEnabled(handleCellKeyDown));\n\n  /*\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (propRowSelectionModel !== undefined) {\n      apiRef.current.setRowSelectionModel(propRowSelectionModel);\n    }\n  }, [apiRef, propRowSelectionModel, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection) {\n      apiRef.current.setRowSelectionModel([]);\n    }\n  }, [apiRef, props.rowSelection]);\n  const isStateControlled = propRowSelectionModel != null;\n  React.useEffect(() => {\n    if (isStateControlled || !props.rowSelection) {\n      return;\n    }\n\n    // props.isRowSelectable changed\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    if (isRowSelectable) {\n      const newSelection = currentSelection.filter(id => isRowSelectable(id));\n      if (newSelection.length < currentSelection.length) {\n        apiRef.current.setRowSelectionModel(newSelection);\n      }\n    }\n  }, [apiRef, isRowSelectable, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection || isStateControlled) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef.current.state);\n    if (!canHaveMultipleSelection && currentSelection.length > 1) {\n      // See https://github.com/mui/mui-x/issues/8455\n      apiRef.current.setRowSelectionModel([]);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    runIfRowSelectionIsEnabled(removeOutdatedSelection);\n  }, [removeOutdatedSelection, runIfRowSelectionIsEnabled]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,sBAAsB,QAAQ,uCAAuC;AAC7F,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,+BAA+B,EAAEC,mBAAmB,QAAQ,6BAA6B;AAClG,SAASC,6BAA6B,EAAEC,wBAAwB,EAAEC,yBAAyB,QAAQ,+BAA+B;AAClI,SAASC,4CAA4C,QAAQ,wBAAwB;AACrF,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,gCAAgC,EAAEC,8BAA8B,QAAQ,iCAAiC;AAClH,SAASC,+BAA+B,EAAEC,wBAAwB,QAAQ,0BAA0B;AACpG,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,eAAe,EAAEC,eAAe,QAAQ,iCAAiC;AAClF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,8BAA8B,QAAQ,kDAAkD;AACjG,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,6BAA6B,EAAEC,gBAAgB,EAAEC,kBAAkB,QAAQ,YAAY;AAChG,MAAMC,0BAA0B,GAAGA,CAACC,kBAAkB,EAAEC,kBAAkB,KAAK;EAC7E,IAAID,kBAAkB,IAAI,IAAI,EAAE;IAC9B,OAAOA,kBAAkB;EAC3B;EACA,IAAIE,KAAK,CAACC,OAAO,CAACH,kBAAkB,CAAC,EAAE;IACrC,OAAOA,kBAAkB;EAC3B;EACA,IAAIC,kBAAkB,IAAIA,kBAAkB,CAAC,CAAC,CAAC,KAAKD,kBAAkB,EAAE;IACtE,OAAOC,kBAAkB;EAC3B;EACA,OAAO,CAACD,kBAAkB,CAAC;AAC7B,CAAC;AACD,OAAO,MAAMI,4BAA4B,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAKnC,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;EAChFE,YAAY,EAAED,KAAK,CAACC,YAAY,GAAGR,0BAA0B,CAACO,KAAK,CAACE,iBAAiB,CAAC,IAAI,EAAE,GAAG;AACjG,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAACC,MAAM,EAAEJ,KAAK,KAAK;EACpD,MAAMK,MAAM,GAAGnC,aAAa,CAACkC,MAAM,EAAE,kBAAkB,CAAC;EACxD,MAAME,0BAA0B,GAAGxC,KAAK,CAACyC,WAAW,CAACC,QAAQ,IAAI,YAAa;IAC5E,IAAIR,KAAK,CAACC,YAAY,EAAE;MACtBO,QAAQ,CAAC,GAAAC,SAAO,CAAC;IACnB;EACF,CAAC,EAAE,CAACT,KAAK,CAACC,YAAY,CAAC,CAAC;EACxB,MAAMS,kBAAkB,GAAGV,KAAK,CAACW,SAAS,KAAK5C,aAAa,CAAC6C,QAAQ,KAAKZ,KAAK,CAACa,uBAAuB,EAAEC,OAAO,IAAId,KAAK,CAACa,uBAAuB,EAAEE,WAAW,CAAC;EAC/J,MAAMC,qBAAqB,GAAGlD,KAAK,CAACmD,OAAO,CAAC,MAAM;IAChD,OAAOxB,0BAA0B,CAACO,KAAK,CAACE,iBAAiB,EAAE5B,6BAA6B,CAAC8B,MAAM,CAACc,OAAO,CAACnB,KAAK,CAAC,CAAC;EACjH,CAAC,EAAE,CAACK,MAAM,EAAEJ,KAAK,CAACE,iBAAiB,CAAC,CAAC;EACrC,MAAMiB,cAAc,GAAGrD,KAAK,CAACsD,MAAM,CAAC,IAAI,CAAC;EACzChB,MAAM,CAACc,OAAO,CAACG,oBAAoB,CAAC;IAClCC,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAEP,qBAAqB;IAChCQ,YAAY,EAAExB,KAAK,CAACyB,yBAAyB;IAC7CC,aAAa,EAAEpD,6BAA6B;IAC5CqD,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM;IACJC,iBAAiB;IACjBC,0BAA0B;IAC1BC,eAAe,EAAEC;EACnB,CAAC,GAAG/B,KAAK;EACT,MAAMgC,wBAAwB,GAAG1C,6BAA6B,CAACU,KAAK,CAAC;EACrE,MAAMiC,WAAW,GAAG/C,kBAAkB,CAACkB,MAAM,EAAEJ,KAAK,CAAC;EACrD,MAAMkC,IAAI,GAAG/D,eAAe,CAACiC,MAAM,EAAE/B,mBAAmB,CAAC;EACzD,MAAM8D,YAAY,GAAGhE,eAAe,CAACiC,MAAM,EAAEhC,+BAA+B,CAAC,GAAG,CAAC;EACjF,MAAMgE,4BAA4B,GAAGtE,KAAK,CAACyC,WAAW,CAAC8B,EAAE,IAAI;IAC3D,IAAIC,KAAK,GAAGD,EAAE;IACd,MAAME,OAAO,GAAGpB,cAAc,CAACD,OAAO,IAAImB,EAAE;IAC5C,MAAMG,UAAU,GAAGpC,MAAM,CAACc,OAAO,CAACuB,aAAa,CAACJ,EAAE,CAAC;IACnD,IAAIG,UAAU,EAAE;MACd,MAAME,aAAa,GAAG/D,gCAAgC,CAACyB,MAAM,CAAC;MAC9D,MAAMuC,UAAU,GAAGD,aAAa,CAACE,SAAS,CAACC,KAAK,IAAIA,KAAK,KAAKN,OAAO,CAAC;MACtE,MAAMO,QAAQ,GAAGJ,aAAa,CAACE,SAAS,CAACC,KAAK,IAAIA,KAAK,KAAKP,KAAK,CAAC;MAClE,IAAIK,UAAU,KAAKG,QAAQ,EAAE;QAC3B;MACF;MACA,IAAIH,UAAU,GAAGG,QAAQ,EAAE;QACzBR,KAAK,GAAGI,aAAa,CAACI,QAAQ,GAAG,CAAC,CAAC;MACrC,CAAC,MAAM;QACLR,KAAK,GAAGI,aAAa,CAACI,QAAQ,GAAG,CAAC,CAAC;MACrC;IACF;IACA3B,cAAc,CAACD,OAAO,GAAGmB,EAAE;IAC3BjC,MAAM,CAACc,OAAO,CAAC6B,cAAc,CAAC;MAC5BR,OAAO;MACPD;IACF,CAAC,EAAE,CAACE,UAAU,CAAC;EACjB,CAAC,EAAE,CAACpC,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAM4C,oBAAoB,GAAGlF,KAAK,CAACyC,WAAW,CAAC0C,KAAK,IAAI;IACtD,IAAIjD,KAAK,CAACW,SAAS,KAAK5C,aAAa,CAAC6C,QAAQ,IAAI,CAACoB,wBAAwB,IAAIpC,KAAK,CAACC,OAAO,CAACoD,KAAK,CAAC,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACvH,MAAM,IAAIC,KAAK,CAAC,CAAC,iEAAiE,EAAE,+FAA+F,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClM;IACA,MAAMC,YAAY,GAAG/E,6BAA6B,CAAC8B,MAAM,CAACc,OAAO,CAACnB,KAAK,CAAC;IACxE,IAAIsD,YAAY,KAAKJ,KAAK,EAAE;MAC1B5C,MAAM,CAACiD,KAAK,CAAC,yBAAyB,CAAC;MACvClD,MAAM,CAACc,OAAO,CAACqC,QAAQ,CAACxD,KAAK,IAAIlC,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACnDE,YAAY,EAAED,KAAK,CAACC,YAAY,GAAGgD,KAAK,GAAG;MAC7C,CAAC,CAAC,CAAC;MACH7C,MAAM,CAACc,OAAO,CAACsC,WAAW,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAACpD,MAAM,EAAEC,MAAM,EAAEL,KAAK,CAACC,YAAY,EAAED,KAAK,CAACW,SAAS,EAAEqB,wBAAwB,CAAC,CAAC;EACnF,MAAMS,aAAa,GAAG3E,KAAK,CAACyC,WAAW,CAAC8B,EAAE,IAAI/D,6BAA6B,CAAC8B,MAAM,CAACc,OAAO,CAACnB,KAAK,CAAC,CAAC0D,QAAQ,CAACpB,EAAE,CAAC,EAAE,CAACjC,MAAM,CAAC,CAAC;EACzH,MAAM0B,eAAe,GAAGhE,KAAK,CAACyC,WAAW,CAAC8B,EAAE,IAAI;IAC9C,IAAIrC,KAAK,CAACC,YAAY,KAAK,KAAK,EAAE;MAChC,OAAO,KAAK;IACd;IACA,IAAI8B,mBAAmB,IAAI,CAACA,mBAAmB,CAAC3B,MAAM,CAACc,OAAO,CAACwC,YAAY,CAACrB,EAAE,CAAC,CAAC,EAAE;MAChF,OAAO,KAAK;IACd;IACA,MAAMsB,OAAO,GAAGvD,MAAM,CAACc,OAAO,CAAC0C,UAAU,CAACvB,EAAE,CAAC;IAC7C,IAAIsB,OAAO,EAAEE,IAAI,KAAK,QAAQ,IAAIF,OAAO,EAAEE,IAAI,KAAK,WAAW,EAAE;MAC/D,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACzD,MAAM,EAAEJ,KAAK,CAACC,YAAY,EAAE8B,mBAAmB,CAAC,CAAC;EACrD,MAAM+B,eAAe,GAAGhG,KAAK,CAACyC,WAAW,CAAC,MAAMhC,wBAAwB,CAAC6B,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC3F,MAAM2D,SAAS,GAAGjG,KAAK,CAACyC,WAAW,CAAC,UAAC8B,EAAE,EAAgD;IAAA,IAA9CG,UAAU,GAAA/B,SAAA,CAAAyC,MAAA,QAAAzC,SAAA,QAAAuD,SAAA,GAAAvD,SAAA,MAAG,IAAI;IAAA,IAAEwD,cAAc,GAAAxD,SAAA,CAAAyC,MAAA,QAAAzC,SAAA,QAAAuD,SAAA,GAAAvD,SAAA,MAAG,KAAK;IAChF,IAAI,CAACL,MAAM,CAACc,OAAO,CAACY,eAAe,CAACO,EAAE,CAAC,EAAE;MACvC;IACF;IACAlB,cAAc,CAACD,OAAO,GAAGmB,EAAE;IAC3B,IAAI4B,cAAc,EAAE;MAClB5D,MAAM,CAACiD,KAAK,CAAC,6BAA6BjB,EAAE,EAAE,CAAC;MAC/C,MAAM6B,YAAY,GAAG,EAAE;MACvB,MAAMC,MAAM,GAAGtB,KAAK,IAAI;QACtBqB,YAAY,CAACE,IAAI,CAACvB,KAAK,CAAC;MAC1B,CAAC;MACD,IAAIL,UAAU,EAAE;QACd2B,MAAM,CAAC9B,EAAE,CAAC;QACV,IAAI3B,kBAAkB,EAAE;UACtBnB,gBAAgB,CAACa,MAAM,EAAE8B,IAAI,EAAEG,EAAE,EAAErC,KAAK,CAACa,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEf,KAAK,CAACa,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAEqD,MAAM,CAAC;QAClJ;MACF;MACA/D,MAAM,CAACc,OAAO,CAAC8B,oBAAoB,CAACkB,YAAY,CAAC;IACnD,CAAC,MAAM;MACL7D,MAAM,CAACiD,KAAK,CAAC,8BAA8BjB,EAAE,EAAE,CAAC;MAChD,MAAMgC,SAAS,GAAG/F,6BAA6B,CAAC8B,MAAM,CAACc,OAAO,CAACnB,KAAK,CAAC;MACrE,MAAMmE,YAAY,GAAG,IAAII,GAAG,CAACD,SAAS,CAAC;MACvCH,YAAY,CAACK,MAAM,CAAClC,EAAE,CAAC;MACvB,MAAM8B,MAAM,GAAGtB,KAAK,IAAI;QACtBqB,YAAY,CAACM,GAAG,CAAC3B,KAAK,CAAC;MACzB,CAAC;MACD,MAAM4B,SAAS,GAAG5B,KAAK,IAAI;QACzBqB,YAAY,CAACK,MAAM,CAAC1B,KAAK,CAAC;MAC5B,CAAC;MACD,IAAIL,UAAU,EAAE;QACd2B,MAAM,CAAC9B,EAAE,CAAC;QACV,IAAI3B,kBAAkB,EAAE;UACtBnB,gBAAgB,CAACa,MAAM,EAAE8B,IAAI,EAAEG,EAAE,EAAErC,KAAK,CAACa,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEf,KAAK,CAACa,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAEqD,MAAM,CAAC;QAClJ;MACF,CAAC,MAAM,IAAIzD,kBAAkB,EAAE;QAC7BlB,kBAAkB,CAACY,MAAM,EAAE8B,IAAI,EAAEG,EAAE,EAAErC,KAAK,CAACa,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEf,KAAK,CAACa,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAE2D,SAAS,CAAC;MACvJ;MACA,MAAMC,gBAAgB,GAAGR,YAAY,CAACS,IAAI,GAAG,CAAC,IAAI3C,wBAAwB;MAC1E,IAAI0C,gBAAgB,EAAE;QACpBtE,MAAM,CAACc,OAAO,CAAC8B,oBAAoB,CAACpD,KAAK,CAACgF,IAAI,CAACV,YAAY,CAAC,CAAC;MAC/D;IACF;EACF,CAAC,EAAE,CAAC9D,MAAM,EAAEC,MAAM,EAAEK,kBAAkB,EAAEwB,IAAI,EAAElC,KAAK,CAACa,uBAAuB,EAAEE,WAAW,EAAEf,KAAK,CAACa,uBAAuB,EAAEC,OAAO,EAAEkB,wBAAwB,CAAC,CAAC;EAC5J,MAAM6C,UAAU,GAAG/G,KAAK,CAACyC,WAAW,CAAC,UAACuE,GAAG,EAAgD;IAAA,IAA9CtC,UAAU,GAAA/B,SAAA,CAAAyC,MAAA,QAAAzC,SAAA,QAAAuD,SAAA,GAAAvD,SAAA,MAAG,IAAI;IAAA,IAAEwD,cAAc,GAAAxD,SAAA,CAAAyC,MAAA,QAAAzC,SAAA,QAAAuD,SAAA,GAAAvD,SAAA,MAAG,KAAK;IAClFJ,MAAM,CAACiD,KAAK,CAAC,oCAAoC,CAAC;IAClD,MAAMyB,aAAa,GAAGD,GAAG,CAACE,MAAM,CAAC3C,EAAE,IAAIjC,MAAM,CAACc,OAAO,CAACY,eAAe,CAACO,EAAE,CAAC,CAAC;IAC1E,IAAI6B,YAAY;IAChB,IAAID,cAAc,EAAE;MAClB,IAAIzB,UAAU,EAAE;QACd0B,YAAY,GAAGa,aAAa;QAC5B,IAAIrE,kBAAkB,EAAE;UACtB,MAAMyD,MAAM,GAAGtB,KAAK,IAAI;YACtBqB,YAAY,CAACE,IAAI,CAACvB,KAAK,CAAC;UAC1B,CAAC;UACDkC,aAAa,CAACE,OAAO,CAAC5C,EAAE,IAAI;YAC1B9C,gBAAgB,CAACa,MAAM,EAAE8B,IAAI,EAAEG,EAAE,EAAErC,KAAK,CAACa,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEf,KAAK,CAACa,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAEqD,MAAM,CAAC;UAClJ,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLD,YAAY,GAAG,EAAE;MACnB;IACF,CAAC,MAAM;MACL;MACA,MAAMgB,eAAe,GAAGrH,QAAQ,CAAC,CAAC,CAAC,EAAEW,yBAAyB,CAAC4B,MAAM,CAAC,CAAC;MACvE,MAAM+D,MAAM,GAAGtB,KAAK,IAAI;QACtBqC,eAAe,CAACrC,KAAK,CAAC,GAAGA,KAAK;MAChC,CAAC;MACD,MAAM4B,SAAS,GAAG5B,KAAK,IAAI;QACzB,OAAOqC,eAAe,CAACrC,KAAK,CAAC;MAC/B,CAAC;MACDkC,aAAa,CAACE,OAAO,CAAC5C,EAAE,IAAI;QAC1B,IAAIG,UAAU,EAAE;UACd0C,eAAe,CAAC7C,EAAE,CAAC,GAAGA,EAAE;UACxB,IAAI3B,kBAAkB,EAAE;YACtBnB,gBAAgB,CAACa,MAAM,EAAE8B,IAAI,EAAEG,EAAE,EAAErC,KAAK,CAACa,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEf,KAAK,CAACa,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAEqD,MAAM,CAAC;UAClJ;QACF,CAAC,MAAM;UACLM,SAAS,CAACpC,EAAE,CAAC;UACb,IAAI3B,kBAAkB,EAAE;YACtBlB,kBAAkB,CAACY,MAAM,EAAE8B,IAAI,EAAEG,EAAE,EAAErC,KAAK,CAACa,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEf,KAAK,CAACa,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAE2D,SAAS,CAAC;UACvJ;QACF;MACF,CAAC,CAAC;MACFP,YAAY,GAAGiB,MAAM,CAACC,MAAM,CAACF,eAAe,CAAC;IAC/C;IACA,MAAMR,gBAAgB,GAAGR,YAAY,CAAChB,MAAM,GAAG,CAAC,IAAIlB,wBAAwB;IAC5E,IAAI0C,gBAAgB,EAAE;MACpBtE,MAAM,CAACc,OAAO,CAAC8B,oBAAoB,CAACkB,YAAY,CAAC;IACnD;EACF,CAAC,EAAE,CAAC7D,MAAM,EAAEK,kBAAkB,EAAEsB,wBAAwB,EAAE5B,MAAM,EAAE8B,IAAI,EAAElC,KAAK,CAACa,uBAAuB,EAAEE,WAAW,EAAEf,KAAK,CAACa,uBAAuB,EAAEC,OAAO,CAAC,CAAC;EAC5J,MAAMiC,cAAc,GAAGjF,KAAK,CAACyC,WAAW,CAAC,UAAA8E,IAAA,EAGQ;IAAA,IAHP;MACxC9C,OAAO;MACPD;IACF,CAAC,GAAA+C,IAAA;IAAA,IAAE7C,UAAU,GAAA/B,SAAA,CAAAyC,MAAA,QAAAzC,SAAA,QAAAuD,SAAA,GAAAvD,SAAA,MAAG,IAAI;IAAA,IAAEwD,cAAc,GAAAxD,SAAA,CAAAyC,MAAA,QAAAzC,SAAA,QAAAuD,SAAA,GAAAvD,SAAA,MAAG,KAAK;IAC1C,IAAI,CAACL,MAAM,CAACc,OAAO,CAACoE,MAAM,CAAC/C,OAAO,CAAC,IAAI,CAACnC,MAAM,CAACc,OAAO,CAACoE,MAAM,CAAChD,KAAK,CAAC,EAAE;MACpE;IACF;IACAjC,MAAM,CAACiD,KAAK,CAAC,gCAAgCf,OAAO,WAAWD,KAAK,EAAE,CAAC;;IAEvE;IACA,MAAMiD,cAAc,GAAG5G,gCAAgC,CAACyB,MAAM,CAAC;IAC/D,MAAMuC,UAAU,GAAG4C,cAAc,CAACC,OAAO,CAACjD,OAAO,CAAC;IAClD,MAAMO,QAAQ,GAAGyC,cAAc,CAACC,OAAO,CAAClD,KAAK,CAAC;IAC9C,MAAM,CAACmD,KAAK,EAAEC,GAAG,CAAC,GAAG/C,UAAU,GAAGG,QAAQ,GAAG,CAACA,QAAQ,EAAEH,UAAU,CAAC,GAAG,CAACA,UAAU,EAAEG,QAAQ,CAAC;IAC5F,MAAM6C,sBAAsB,GAAGJ,cAAc,CAACK,KAAK,CAACH,KAAK,EAAEC,GAAG,GAAG,CAAC,CAAC;IACnEtF,MAAM,CAACc,OAAO,CAAC2D,UAAU,CAACc,sBAAsB,EAAEnD,UAAU,EAAEyB,cAAc,CAAC;EAC/E,CAAC,EAAE,CAAC7D,MAAM,EAAEC,MAAM,CAAC,CAAC;EACpB,MAAMwF,kBAAkB,GAAG;IACzB9B,SAAS;IACTf,oBAAoB;IACpBc,eAAe;IACfrB,aAAa;IACbX;EACF,CAAC;EACD,MAAMgE,mBAAmB,GAAG;IAC1BjB,UAAU;IACV9B;EACF,CAAC;EACD9E,gBAAgB,CAACmC,MAAM,EAAEyF,kBAAkB,EAAE,QAAQ,CAAC;EACtD5H,gBAAgB,CAACmC,MAAM,EAAE0F,mBAAmB,EAAE9F,KAAK,CAACW,SAAS,KAAK5C,aAAa,CAAC6C,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;;EAEhH;AACF;AACA;EACE,MAAMmF,uBAAuB,GAAGjI,KAAK,CAACyC,WAAW,CAAC,YAA8B;IAAA,IAA7ByF,gBAAgB,GAAAvF,SAAA,CAAAyC,MAAA,QAAAzC,SAAA,QAAAuD,SAAA,GAAAvD,SAAA,MAAG,KAAK;IACzE,MAAMwF,gBAAgB,GAAG3H,6BAA6B,CAAC8B,MAAM,CAACc,OAAO,CAACnB,KAAK,CAAC;IAC5E,MAAMmG,kBAAkB,GAAGtH,8BAA8B,CAACwB,MAAM,CAAC;;IAEjE;IACA,MAAM8E,eAAe,GAAGrH,QAAQ,CAAC,CAAC,CAAC,EAAEW,yBAAyB,CAAC4B,MAAM,CAAC,CAAC;IACvE,IAAI+F,UAAU,GAAG,KAAK;IACtBF,gBAAgB,CAAChB,OAAO,CAAC5C,EAAE,IAAI;MAC7B,IAAI6D,kBAAkB,CAAC7D,EAAE,CAAC,KAAK,KAAK,EAAE;QACpC,IAAIrC,KAAK,CAACoG,2BAA2B,EAAE;UACrC;QACF;QACA,OAAOlB,eAAe,CAAC7C,EAAE,CAAC;QAC1B8D,UAAU,GAAG,IAAI;QACjB;MACF;MACA,IAAI,CAACnG,KAAK,CAACa,uBAAuB,EAAEC,OAAO,EAAE;QAC3C;MACF;MACA,MAAMuF,IAAI,GAAGnE,IAAI,CAACG,EAAE,CAAC;MACrB,IAAIgE,IAAI,CAACxC,IAAI,KAAK,OAAO,EAAE;QACzB,MAAMyC,eAAe,GAAGD,IAAI,CAACC,eAAe;QAC5C,IAAIA,eAAe,EAAE;UACnB,OAAOpB,eAAe,CAAC7C,EAAE,CAAC;UAC1B8D,UAAU,GAAG,IAAI;UACjB;QACF;QACA;QACA,IAAI,CAACE,IAAI,CAACE,QAAQ,CAACC,KAAK,CAACC,OAAO,IAAIP,kBAAkB,CAACO,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE;UAC1E,OAAOvB,eAAe,CAAC7C,EAAE,CAAC;UAC1B8D,UAAU,GAAG,IAAI;QACnB;MACF;IACF,CAAC,CAAC;IACF,IAAIA,UAAU,IAAIhE,YAAY,IAAI,CAAC6D,gBAAgB,EAAE;MACnD,MAAM9B,YAAY,GAAGiB,MAAM,CAACC,MAAM,CAACF,eAAe,CAAC;MACnD,IAAI/C,YAAY,EAAE;QAChB/B,MAAM,CAACc,OAAO,CAAC2D,UAAU,CAACX,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC;MACrD,CAAC,MAAM;QACL9D,MAAM,CAACc,OAAO,CAAC8B,oBAAoB,CAACkB,YAAY,CAAC;MACnD;IACF;EACF,CAAC,EAAE,CAAC9D,MAAM,EAAE+B,YAAY,EAAEnC,KAAK,CAACa,uBAAuB,EAAEC,OAAO,EAAEd,KAAK,CAACoG,2BAA2B,EAAElE,IAAI,CAAC,CAAC;EAC3G,MAAMwE,wBAAwB,GAAG5I,KAAK,CAACyC,WAAW,CAAC,CAAC8B,EAAE,EAAEsE,KAAK,KAAK;IAChE,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO,IAAIF,KAAK,CAACG,OAAO;;IAEjD;IACA;IACA;IACA;;IAEA,MAAMC,2BAA2B,GAAG,CAACnF,iBAAiB,IAAI,CAACgF,UAAU,IAAI,CAAC5H,eAAe,CAAC2H,KAAK,CAAC;IAChG,MAAM1C,cAAc,GAAG,CAACjC,wBAAwB,IAAI+E,2BAA2B;IAC/E,MAAMvE,UAAU,GAAGpC,MAAM,CAACc,OAAO,CAACuB,aAAa,CAACJ,EAAE,CAAC;IACnD,IAAI4B,cAAc,EAAE;MAClB7D,MAAM,CAACc,OAAO,CAAC6C,SAAS,CAAC1B,EAAE,EAAE,CAAC0E,2BAA2B,GAAG,CAACvE,UAAU,GAAG,IAAI,EAAE,IAAI,CAAC;IACvF,CAAC,MAAM;MACLpC,MAAM,CAACc,OAAO,CAAC6C,SAAS,CAAC1B,EAAE,EAAE,CAACG,UAAU,EAAE,KAAK,CAAC;IAClD;EACF,CAAC,EAAE,CAACpC,MAAM,EAAE4B,wBAAwB,EAAEJ,iBAAiB,CAAC,CAAC;EACzD,MAAMoF,cAAc,GAAGlJ,KAAK,CAACyC,WAAW,CAAC,CAAC0G,MAAM,EAAEN,KAAK,KAAK;IAC1D,IAAI9E,0BAA0B,EAAE;MAC9B;IACF;IACA,MAAMqF,KAAK,GAAGP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAAC,IAAIhI,WAAW,CAACiI,IAAI,EAAE,CAAC,EAAEC,YAAY,CAAC,YAAY,CAAC;IACtF,IAAIJ,KAAK,KAAKrI,+BAA+B,CAACqI,KAAK,EAAE;MACnD;MACA;IACF;IACA,IAAIA,KAAK,KAAK/H,8BAA8B,EAAE;MAC5C;MACA;IACF;IACA,IAAI+H,KAAK,EAAE;MACT,MAAMK,MAAM,GAAGnH,MAAM,CAACc,OAAO,CAACsG,SAAS,CAACN,KAAK,CAAC;MAC9C,IAAIK,MAAM,EAAE1D,IAAI,KAAK/E,wBAAwB,EAAE;QAC7C;MACF;IACF;IACA,MAAM6E,OAAO,GAAGvD,MAAM,CAACc,OAAO,CAAC0C,UAAU,CAACqD,MAAM,CAAC5E,EAAE,CAAC;IACpD,IAAIsB,OAAO,CAACE,IAAI,KAAK,WAAW,EAAE;MAChC;IACF;IACA,IAAI8C,KAAK,CAACc,QAAQ,IAAIzF,wBAAwB,EAAE;MAC9CI,4BAA4B,CAAC6E,MAAM,CAAC5E,EAAE,CAAC;IACzC,CAAC,MAAM;MACLqE,wBAAwB,CAACO,MAAM,CAAC5E,EAAE,EAAEsE,KAAK,CAAC;IAC5C;EACF,CAAC,EAAE,CAAC9E,0BAA0B,EAAEG,wBAAwB,EAAE5B,MAAM,EAAEgC,4BAA4B,EAAEsE,wBAAwB,CAAC,CAAC;EAC1H,MAAMgB,uBAAuB,GAAG5J,KAAK,CAACyC,WAAW,CAAC,CAAC0G,MAAM,EAAEN,KAAK,KAAK;IACnE,IAAI3E,wBAAwB,IAAI2E,KAAK,CAACc,QAAQ,EAAE;MAC9CE,MAAM,CAACC,YAAY,CAAC,CAAC,EAAEC,eAAe,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE,CAAC7F,wBAAwB,CAAC,CAAC;EAC9B,MAAM8F,gCAAgC,GAAGhK,KAAK,CAACyC,WAAW,CAAC,CAAC0G,MAAM,EAAEN,KAAK,KAAK;IAC5E,IAAI3E,wBAAwB,IAAI2E,KAAK,CAACoB,WAAW,CAACN,QAAQ,EAAE;MAC1DrF,4BAA4B,CAAC6E,MAAM,CAAC5E,EAAE,CAAC;IACzC,CAAC,MAAM;MACLjC,MAAM,CAACc,OAAO,CAAC6C,SAAS,CAACkD,MAAM,CAAC5E,EAAE,EAAE4E,MAAM,CAACe,KAAK,EAAE,CAAChG,wBAAwB,CAAC;IAC9E;EACF,CAAC,EAAE,CAAC5B,MAAM,EAAEgC,4BAA4B,EAAEJ,wBAAwB,CAAC,CAAC;EACpE,MAAMiG,mCAAmC,GAAGnK,KAAK,CAACyC,WAAW,CAAC0G,MAAM,IAAI;IACtE,MAAMiB,gBAAgB,GAAGlI,KAAK,CAACmI,UAAU,IAAInI,KAAK,CAACoI,4BAA4B,IAAIpI,KAAK,CAACqI,cAAc,KAAK,QAAQ,GAAG5J,4CAA4C,CAAC2B,MAAM,CAAC,GAAGzB,gCAAgC,CAACyB,MAAM,CAAC;IACtNA,MAAM,CAACc,OAAO,CAAC2D,UAAU,CAACqD,gBAAgB,EAAEjB,MAAM,CAACe,KAAK,CAAC;EAC3D,CAAC,EAAE,CAAC5H,MAAM,EAAEJ,KAAK,CAACoI,4BAA4B,EAAEpI,KAAK,CAACmI,UAAU,EAAEnI,KAAK,CAACqI,cAAc,CAAC,CAAC;EACxF,MAAMC,iBAAiB,GAAGxK,KAAK,CAACyC,WAAW,CAAC,CAAC0G,MAAM,EAAEN,KAAK,KAAK;IAC7D;IACA,IAAIvG,MAAM,CAACc,OAAO,CAACqH,WAAW,CAACtB,MAAM,CAAC5E,EAAE,EAAE4E,MAAM,CAACC,KAAK,CAAC,KAAKnI,aAAa,CAACyJ,IAAI,EAAE;MAC9E;IACF;;IAEA;IACA;IACA,IAAInJ,qBAAqB,CAACsH,KAAK,CAAC,EAAE;MAChC;IACF;IACA,IAAI1H,eAAe,CAAC0H,KAAK,CAAC8B,GAAG,CAAC,IAAI9B,KAAK,CAACc,QAAQ,EAAE;MAChD;MACA,MAAMiB,SAAS,GAAGhK,qBAAqB,CAAC0B,MAAM,CAAC;MAC/C,IAAIsI,SAAS,IAAIA,SAAS,CAACrG,EAAE,KAAK4E,MAAM,CAAC5E,EAAE,EAAE;QAC3CsE,KAAK,CAACgC,cAAc,CAAC,CAAC;QACtB,MAAMC,iBAAiB,GAAGxI,MAAM,CAACc,OAAO,CAACuB,aAAa,CAACiG,SAAS,CAACrG,EAAE,CAAC;QACpE,IAAI,CAACL,wBAAwB,EAAE;UAC7B5B,MAAM,CAACc,OAAO,CAAC6C,SAAS,CAAC2E,SAAS,CAACrG,EAAE,EAAE,CAACuG,iBAAiB,EAAE,IAAI,CAAC;UAChE;QACF;QACA,MAAMC,WAAW,GAAGzI,MAAM,CAACc,OAAO,CAAC4H,gCAAgC,CAACJ,SAAS,CAACrG,EAAE,CAAC;QACjF,MAAM0G,gBAAgB,GAAG3I,MAAM,CAACc,OAAO,CAAC4H,gCAAgC,CAAC7B,MAAM,CAAC5E,EAAE,CAAC;QACnF,IAAIoD,KAAK;QACT,IAAIC,GAAG;QACP,IAAImD,WAAW,GAAGE,gBAAgB,EAAE;UAClC,IAAIH,iBAAiB,EAAE;YACrB;YACAnD,KAAK,GAAGsD,gBAAgB;YACxBrD,GAAG,GAAGmD,WAAW,GAAG,CAAC;UACvB,CAAC,MAAM;YACL;YACApD,KAAK,GAAGsD,gBAAgB;YACxBrD,GAAG,GAAGmD,WAAW;UACnB;QACF,CAAC,MAAM;UACL;UACA,IAAID,iBAAiB,EAAE;YACrB;YACAnD,KAAK,GAAGoD,WAAW,GAAG,CAAC;YACvBnD,GAAG,GAAGqD,gBAAgB;UACxB,CAAC,MAAM;YACL;YACAtD,KAAK,GAAGoD,WAAW;YACnBnD,GAAG,GAAGqD,gBAAgB;UACxB;QACF;QACA,MAAMpD,sBAAsB,GAAG1D,WAAW,CAAC+G,IAAI,CAACpD,KAAK,CAACH,KAAK,EAAEC,GAAG,GAAG,CAAC,CAAC,CAACuD,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC7G,EAAE,CAAC;QACxFjC,MAAM,CAACc,OAAO,CAAC2D,UAAU,CAACc,sBAAsB,EAAE,CAACiD,iBAAiB,CAAC;QACrE;MACF;IACF;IACA,IAAIjC,KAAK,CAAC8B,GAAG,KAAK,GAAG,IAAI9B,KAAK,CAACc,QAAQ,EAAE;MACvCd,KAAK,CAACgC,cAAc,CAAC,CAAC;MACtBjC,wBAAwB,CAACO,MAAM,CAAC5E,EAAE,EAAEsE,KAAK,CAAC;MAC1C;IACF;IACA,IAAIA,KAAK,CAAC8B,GAAG,KAAK,GAAG,KAAK9B,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACE,OAAO,CAAC,EAAE;MACzDF,KAAK,CAACgC,cAAc,CAAC,CAAC;MACtB9D,UAAU,CAACzE,MAAM,CAACc,OAAO,CAACiI,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC;IACjD;EACF,CAAC,EAAE,CAAC/I,MAAM,EAAEsG,wBAAwB,EAAE7B,UAAU,EAAE5C,WAAW,CAAC+G,IAAI,EAAEhH,wBAAwB,CAAC,CAAC;EAC9FhE,sBAAsB,CAACoC,MAAM,EAAE,eAAe,EAAEE,0BAA0B,CAAC,MAAMyF,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;EAChH/H,sBAAsB,CAACoC,MAAM,EAAE,iBAAiB,EAAEE,0BAA0B,CAACyF,uBAAuB,CAAC,CAAC;EACtG/H,sBAAsB,CAACoC,MAAM,EAAE,UAAU,EAAEE,0BAA0B,CAAC0G,cAAc,CAAC,CAAC;EACtFhJ,sBAAsB,CAACoC,MAAM,EAAE,4BAA4B,EAAEE,0BAA0B,CAACwH,gCAAgC,CAAC,CAAC;EAC1H9J,sBAAsB,CAACoC,MAAM,EAAE,+BAA+B,EAAE6H,mCAAmC,CAAC;EACpGjK,sBAAsB,CAACoC,MAAM,EAAE,eAAe,EAAEE,0BAA0B,CAACoH,uBAAuB,CAAC,CAAC;EACpG1J,sBAAsB,CAACoC,MAAM,EAAE,aAAa,EAAEE,0BAA0B,CAACgI,iBAAiB,CAAC,CAAC;;EAE5F;AACF;AACA;EACExK,KAAK,CAACsL,SAAS,CAAC,MAAM;IACpB,IAAIpI,qBAAqB,KAAKgD,SAAS,EAAE;MACvC5D,MAAM,CAACc,OAAO,CAAC8B,oBAAoB,CAAChC,qBAAqB,CAAC;IAC5D;EACF,CAAC,EAAE,CAACZ,MAAM,EAAEY,qBAAqB,EAAEhB,KAAK,CAACC,YAAY,CAAC,CAAC;EACvDnC,KAAK,CAACsL,SAAS,CAAC,MAAM;IACpB,IAAI,CAACpJ,KAAK,CAACC,YAAY,EAAE;MACvBG,MAAM,CAACc,OAAO,CAAC8B,oBAAoB,CAAC,EAAE,CAAC;IACzC;EACF,CAAC,EAAE,CAAC5C,MAAM,EAAEJ,KAAK,CAACC,YAAY,CAAC,CAAC;EAChC,MAAMoJ,iBAAiB,GAAGrI,qBAAqB,IAAI,IAAI;EACvDlD,KAAK,CAACsL,SAAS,CAAC,MAAM;IACpB,IAAIC,iBAAiB,IAAI,CAACrJ,KAAK,CAACC,YAAY,EAAE;MAC5C;IACF;;IAEA;IACA,MAAMgG,gBAAgB,GAAG3H,6BAA6B,CAAC8B,MAAM,CAACc,OAAO,CAACnB,KAAK,CAAC;IAC5E,IAAI+B,eAAe,EAAE;MACnB,MAAMoC,YAAY,GAAG+B,gBAAgB,CAACjB,MAAM,CAAC3C,EAAE,IAAIP,eAAe,CAACO,EAAE,CAAC,CAAC;MACvE,IAAI6B,YAAY,CAAChB,MAAM,GAAG+C,gBAAgB,CAAC/C,MAAM,EAAE;QACjD9C,MAAM,CAACc,OAAO,CAAC8B,oBAAoB,CAACkB,YAAY,CAAC;MACnD;IACF;EACF,CAAC,EAAE,CAAC9D,MAAM,EAAE0B,eAAe,EAAEuH,iBAAiB,EAAErJ,KAAK,CAACC,YAAY,CAAC,CAAC;EACpEnC,KAAK,CAACsL,SAAS,CAAC,MAAM;IACpB,IAAI,CAACpJ,KAAK,CAACC,YAAY,IAAIoJ,iBAAiB,EAAE;MAC5C;IACF;IACA,MAAMpD,gBAAgB,GAAG3H,6BAA6B,CAAC8B,MAAM,CAACc,OAAO,CAACnB,KAAK,CAAC;IAC5E,IAAI,CAACiC,wBAAwB,IAAIiE,gBAAgB,CAAC/C,MAAM,GAAG,CAAC,EAAE;MAC5D;MACA9C,MAAM,CAACc,OAAO,CAAC8B,oBAAoB,CAAC,EAAE,CAAC;IACzC;EACF,CAAC,EAAE,CAAC5C,MAAM,EAAE4B,wBAAwB,EAAEJ,iBAAiB,EAAEyH,iBAAiB,EAAErJ,KAAK,CAACC,YAAY,CAAC,CAAC;EAChGnC,KAAK,CAACsL,SAAS,CAAC,MAAM;IACpB9I,0BAA0B,CAACyF,uBAAuB,CAAC;EACrD,CAAC,EAAE,CAACA,uBAAuB,EAAEzF,0BAA0B,CAAC,CAAC;AAC3D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}