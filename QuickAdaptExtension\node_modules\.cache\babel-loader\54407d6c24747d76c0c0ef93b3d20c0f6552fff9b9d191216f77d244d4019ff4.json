{"ast": null, "code": "import { useGridRefs } from \"./useGridRefs.js\";\nimport { useGridIsRtl } from \"./useGridIsRtl.js\";\nimport { useGridLoggerFactory } from \"./useGridLoggerFactory.js\";\nimport { useGridApiInitialization } from \"./useGridApiInitialization.js\";\nimport { useGridLocaleText } from \"./useGridLocaleText.js\";\nimport { useGridPipeProcessing } from \"./pipeProcessing/index.js\";\nimport { useGridStrategyProcessing } from \"./strategyProcessing/index.js\";\nimport { useGridStateInitialization } from \"./useGridStateInitialization.js\";\n\n/**\n * Initialize the technical pieces of the DataGrid (logger, state, ...) that any DataGrid implementation needs\n */\nexport const useGridInitialization = (inputApiRef, props) => {\n  const privateApiRef = useGridApiInitialization(inputApiRef, props);\n  useGridRefs(privateApiRef);\n  useGridIsRtl(privateApiRef);\n  useGridLoggerFactory(privateApiRef, props);\n  useGridStateInitialization(privateApiRef);\n  useGridPipeProcessing(privateApiRef);\n  useGridStrategyProcessing(privateApiRef);\n  useGridLocaleText(privateApiRef, props);\n  privateApiRef.current.register('private', {\n    rootProps: props\n  });\n  return privateApiRef;\n};", "map": {"version": 3, "names": ["useGridRefs", "useGridIsRtl", "useGridLoggerFactory", "useGridApiInitialization", "useGridLocaleText", "useGridPipeProcessing", "useGridStrategyProcessing", "useGridStateInitialization", "useGridInitialization", "inputApiRef", "props", "privateApiRef", "current", "register", "rootProps"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/core/useGridInitialization.js"], "sourcesContent": ["import { useGridRefs } from \"./useGridRefs.js\";\nimport { useGridIsRtl } from \"./useGridIsRtl.js\";\nimport { useGridLoggerFactory } from \"./useGridLoggerFactory.js\";\nimport { useGridApiInitialization } from \"./useGridApiInitialization.js\";\nimport { useGridLocaleText } from \"./useGridLocaleText.js\";\nimport { useGridPipeProcessing } from \"./pipeProcessing/index.js\";\nimport { useGridStrategyProcessing } from \"./strategyProcessing/index.js\";\nimport { useGridStateInitialization } from \"./useGridStateInitialization.js\";\n\n/**\n * Initialize the technical pieces of the DataGrid (logger, state, ...) that any DataGrid implementation needs\n */\nexport const useGridInitialization = (inputApiRef, props) => {\n  const privateApiRef = useGridApiInitialization(inputApiRef, props);\n  useGridRefs(privateApiRef);\n  useGridIsRtl(privateApiRef);\n  useGridLoggerFactory(privateApiRef, props);\n  useGridStateInitialization(privateApiRef);\n  useGridPipeProcessing(privateApiRef);\n  useGridStrategyProcessing(privateApiRef);\n  useGridLocaleText(privateApiRef, props);\n  privateApiRef.current.register('private', {\n    rootProps: props\n  });\n  return privateApiRef;\n};"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,0BAA0B,QAAQ,iCAAiC;;AAE5E;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,WAAW,EAAEC,KAAK,KAAK;EAC3D,MAAMC,aAAa,GAAGR,wBAAwB,CAACM,WAAW,EAAEC,KAAK,CAAC;EAClEV,WAAW,CAACW,aAAa,CAAC;EAC1BV,YAAY,CAACU,aAAa,CAAC;EAC3BT,oBAAoB,CAACS,aAAa,EAAED,KAAK,CAAC;EAC1CH,0BAA0B,CAACI,aAAa,CAAC;EACzCN,qBAAqB,CAACM,aAAa,CAAC;EACpCL,yBAAyB,CAACK,aAAa,CAAC;EACxCP,iBAAiB,CAACO,aAAa,EAAED,KAAK,CAAC;EACvCC,aAAa,CAACC,OAAO,CAACC,QAAQ,CAAC,SAAS,EAAE;IACxCC,SAAS,EAAEJ;EACb,CAAC,CAAC;EACF,OAAOC,aAAa;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}