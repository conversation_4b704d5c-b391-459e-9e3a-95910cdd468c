{"ast": null, "code": "export var calculateChange = function calculateChange(e, hsl, container) {\n  var _container$getBoundin = container.getBoundingClientRect(),\n    containerWidth = _container$getBoundin.width,\n    containerHeight = _container$getBoundin.height;\n  var x = typeof e.pageX === 'number' ? e.pageX : e.touches[0].pageX;\n  var y = typeof e.pageY === 'number' ? e.pageY : e.touches[0].pageY;\n  var left = x - (container.getBoundingClientRect().left + window.pageXOffset);\n  var top = y - (container.getBoundingClientRect().top + window.pageYOffset);\n  if (left < 0) {\n    left = 0;\n  } else if (left > containerWidth) {\n    left = containerWidth;\n  }\n  if (top < 0) {\n    top = 0;\n  } else if (top > containerHeight) {\n    top = containerHeight;\n  }\n  var saturation = left / containerWidth;\n  var bright = 1 - top / containerHeight;\n  return {\n    h: hsl.h,\n    s: saturation,\n    v: bright,\n    a: hsl.a,\n    source: 'hsv'\n  };\n};", "map": {"version": 3, "names": ["calculateChange", "e", "hsl", "container", "_container$getBoundin", "getBoundingClientRect", "containerWidth", "width", "containerHeight", "height", "x", "pageX", "touches", "y", "pageY", "left", "window", "pageXOffset", "top", "pageYOffset", "saturation", "bright", "h", "s", "v", "a", "source"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/react-color/es/helpers/saturation.js"], "sourcesContent": ["export var calculateChange = function calculateChange(e, hsl, container) {\n  var _container$getBoundin = container.getBoundingClientRect(),\n      containerWidth = _container$getBoundin.width,\n      containerHeight = _container$getBoundin.height;\n\n  var x = typeof e.pageX === 'number' ? e.pageX : e.touches[0].pageX;\n  var y = typeof e.pageY === 'number' ? e.pageY : e.touches[0].pageY;\n  var left = x - (container.getBoundingClientRect().left + window.pageXOffset);\n  var top = y - (container.getBoundingClientRect().top + window.pageYOffset);\n\n  if (left < 0) {\n    left = 0;\n  } else if (left > containerWidth) {\n    left = containerWidth;\n  }\n\n  if (top < 0) {\n    top = 0;\n  } else if (top > containerHeight) {\n    top = containerHeight;\n  }\n\n  var saturation = left / containerWidth;\n  var bright = 1 - top / containerHeight;\n\n  return {\n    h: hsl.h,\n    s: saturation,\n    v: bright,\n    a: hsl.a,\n    source: 'hsv'\n  };\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAEC,GAAG,EAAEC,SAAS,EAAE;EACvE,IAAIC,qBAAqB,GAAGD,SAAS,CAACE,qBAAqB,CAAC,CAAC;IACzDC,cAAc,GAAGF,qBAAqB,CAACG,KAAK;IAC5CC,eAAe,GAAGJ,qBAAqB,CAACK,MAAM;EAElD,IAAIC,CAAC,GAAG,OAAOT,CAAC,CAACU,KAAK,KAAK,QAAQ,GAAGV,CAAC,CAACU,KAAK,GAAGV,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACD,KAAK;EAClE,IAAIE,CAAC,GAAG,OAAOZ,CAAC,CAACa,KAAK,KAAK,QAAQ,GAAGb,CAAC,CAACa,KAAK,GAAGb,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACE,KAAK;EAClE,IAAIC,IAAI,GAAGL,CAAC,IAAIP,SAAS,CAACE,qBAAqB,CAAC,CAAC,CAACU,IAAI,GAAGC,MAAM,CAACC,WAAW,CAAC;EAC5E,IAAIC,GAAG,GAAGL,CAAC,IAAIV,SAAS,CAACE,qBAAqB,CAAC,CAAC,CAACa,GAAG,GAAGF,MAAM,CAACG,WAAW,CAAC;EAE1E,IAAIJ,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAAC;EACV,CAAC,MAAM,IAAIA,IAAI,GAAGT,cAAc,EAAE;IAChCS,IAAI,GAAGT,cAAc;EACvB;EAEA,IAAIY,GAAG,GAAG,CAAC,EAAE;IACXA,GAAG,GAAG,CAAC;EACT,CAAC,MAAM,IAAIA,GAAG,GAAGV,eAAe,EAAE;IAChCU,GAAG,GAAGV,eAAe;EACvB;EAEA,IAAIY,UAAU,GAAGL,IAAI,GAAGT,cAAc;EACtC,IAAIe,MAAM,GAAG,CAAC,GAAGH,GAAG,GAAGV,eAAe;EAEtC,OAAO;IACLc,CAAC,EAAEpB,GAAG,CAACoB,CAAC;IACRC,CAAC,EAAEH,UAAU;IACbI,CAAC,EAAEH,MAAM;IACTI,CAAC,EAAEvB,GAAG,CAACuB,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}