{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridRenderContextSelector } from \"../virtualization/gridVirtualizationSelectors.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { getUnprocessedRange, isRowRangeUpdated, isRowContextInitialized, getCellValue } from \"./gridRowSpanningUtils.js\";\nconst EMPTY_STATE = {\n  spannedCells: {},\n  hiddenCells: {},\n  hiddenCellOriginMap: {}\n};\nconst EMPTY_RANGE = {\n  firstRowIndex: 0,\n  lastRowIndex: 0\n};\nconst skippedFields = new Set(['__check__', '__reorder__', '__detail_panel_toggle__']);\n/**\n * Default number of rows to process during state initialization to avoid flickering.\n * Number `20` is arbitrarily chosen to be large enough to cover most of the cases without\n * compromising performance.\n */\nconst DEFAULT_ROWS_TO_PROCESS = 20;\nconst computeRowSpanningState = (apiRef, colDefs, visibleRows, range, rangeToProcess, resetState, processedRange) => {\n  const spannedCells = resetState ? {} : _extends({}, apiRef.current.state.rowSpanning.spannedCells);\n  const hiddenCells = resetState ? {} : _extends({}, apiRef.current.state.rowSpanning.hiddenCells);\n  const hiddenCellOriginMap = resetState ? {} : _extends({}, apiRef.current.state.rowSpanning.hiddenCellOriginMap);\n  if (resetState) {\n    processedRange = EMPTY_RANGE;\n  }\n  colDefs.forEach(colDef => {\n    if (skippedFields.has(colDef.field)) {\n      return;\n    }\n    for (let index = rangeToProcess.firstRowIndex; index <= rangeToProcess.lastRowIndex; index += 1) {\n      const row = visibleRows[index];\n      if (hiddenCells[row.id]?.[colDef.field]) {\n        continue;\n      }\n      const cellValue = getCellValue(row.model, colDef, apiRef);\n      if (cellValue == null) {\n        continue;\n      }\n      let spannedRowId = row.id;\n      let spannedRowIndex = index;\n      let rowSpan = 0;\n\n      // For first index, also scan in the previous rows to handle the reset state case e.g by sorting\n      const backwardsHiddenCells = [];\n      if (index === rangeToProcess.firstRowIndex) {\n        let prevIndex = index - 1;\n        const prevRowEntry = visibleRows[prevIndex];\n        while (prevIndex >= range.firstRowIndex && getCellValue(prevRowEntry.model, colDef, apiRef) === cellValue) {\n          const currentRow = visibleRows[prevIndex + 1];\n          if (hiddenCells[currentRow.id]) {\n            hiddenCells[currentRow.id][colDef.field] = true;\n          } else {\n            hiddenCells[currentRow.id] = {\n              [colDef.field]: true\n            };\n          }\n          backwardsHiddenCells.push(index);\n          rowSpan += 1;\n          spannedRowId = prevRowEntry.id;\n          spannedRowIndex = prevIndex;\n          prevIndex -= 1;\n        }\n      }\n      backwardsHiddenCells.forEach(hiddenCellIndex => {\n        if (hiddenCellOriginMap[hiddenCellIndex]) {\n          hiddenCellOriginMap[hiddenCellIndex][colDef.field] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[hiddenCellIndex] = {\n            [colDef.field]: spannedRowIndex\n          };\n        }\n      });\n\n      // Scan the next rows\n      let relativeIndex = index + 1;\n      while (relativeIndex <= range.lastRowIndex && visibleRows[relativeIndex] && getCellValue(visibleRows[relativeIndex].model, colDef, apiRef) === cellValue) {\n        const currentRow = visibleRows[relativeIndex];\n        if (hiddenCells[currentRow.id]) {\n          hiddenCells[currentRow.id][colDef.field] = true;\n        } else {\n          hiddenCells[currentRow.id] = {\n            [colDef.field]: true\n          };\n        }\n        if (hiddenCellOriginMap[relativeIndex]) {\n          hiddenCellOriginMap[relativeIndex][colDef.field] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[relativeIndex] = {\n            [colDef.field]: spannedRowIndex\n          };\n        }\n        relativeIndex += 1;\n        rowSpan += 1;\n      }\n      if (rowSpan > 0) {\n        if (spannedCells[spannedRowId]) {\n          spannedCells[spannedRowId][colDef.field] = rowSpan + 1;\n        } else {\n          spannedCells[spannedRowId] = {\n            [colDef.field]: rowSpan + 1\n          };\n        }\n      }\n    }\n    processedRange = {\n      firstRowIndex: Math.min(processedRange.firstRowIndex, rangeToProcess.firstRowIndex),\n      lastRowIndex: Math.max(processedRange.lastRowIndex, rangeToProcess.lastRowIndex)\n    };\n  });\n  return {\n    spannedCells,\n    hiddenCells,\n    hiddenCellOriginMap,\n    processedRange\n  };\n};\n\n/**\n * @requires columnsStateInitializer (method) - should be initialized before\n * @requires rowsStateInitializer (method) - should be initialized before\n * @requires filterStateInitializer (method) - should be initialized before\n */\nexport const rowSpanningStateInitializer = (state, props, apiRef) => {\n  if (props.unstable_rowSpanning) {\n    const rowIds = state.rows.dataRowIds || [];\n    const orderedFields = state.columns.orderedFields || [];\n    const dataRowIdToModelLookup = state.rows.dataRowIdToModelLookup;\n    const columnsLookup = state.columns.lookup;\n    const isFilteringPending = Boolean(state.filter.filterModel.items.length) || Boolean(state.filter.filterModel.quickFilterValues?.length);\n    if (!rowIds.length || !orderedFields.length || !dataRowIdToModelLookup || !columnsLookup || isFilteringPending) {\n      return _extends({}, state, {\n        rowSpanning: EMPTY_STATE\n      });\n    }\n    const rangeToProcess = {\n      firstRowIndex: 0,\n      lastRowIndex: Math.min(DEFAULT_ROWS_TO_PROCESS - 1, Math.max(rowIds.length - 1, 0))\n    };\n    const rows = rowIds.map(id => ({\n      id,\n      model: dataRowIdToModelLookup[id]\n    }));\n    const colDefs = orderedFields.map(field => columnsLookup[field]);\n    const {\n      spannedCells,\n      hiddenCells,\n      hiddenCellOriginMap\n    } = computeRowSpanningState(apiRef, colDefs, rows, rangeToProcess, rangeToProcess, true, EMPTY_RANGE);\n    return _extends({}, state, {\n      rowSpanning: {\n        spannedCells,\n        hiddenCells,\n        hiddenCellOriginMap\n      }\n    });\n  }\n  return _extends({}, state, {\n    rowSpanning: EMPTY_STATE\n  });\n};\nexport const useGridRowSpanning = (apiRef, props) => {\n  const {\n    range,\n    rows: visibleRows\n  } = useGridVisibleRows(apiRef, props);\n  const renderContext = useGridSelector(apiRef, gridRenderContextSelector);\n  const colDefs = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const processedRange = useLazyRef(() => {\n    return Object.keys(apiRef.current.state.rowSpanning.spannedCells).length > 0 ? {\n      firstRowIndex: 0,\n      lastRowIndex: Math.min(DEFAULT_ROWS_TO_PROCESS - 1, Math.max(apiRef.current.state.rows.dataRowIds.length - 1, 0))\n    } : EMPTY_RANGE;\n  });\n  const lastRange = React.useRef(EMPTY_RANGE);\n  const updateRowSpanningState = React.useCallback(\n  // A reset needs to occur when:\n  // - The `unstable_rowSpanning` prop is updated (feature flag)\n  // - The filtering is applied\n  // - The sorting is applied\n  // - The `paginationModel` is updated\n  // - The rows are updated\n  function () {\n    let resetState = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    if (!props.unstable_rowSpanning) {\n      if (apiRef.current.state.rowSpanning !== EMPTY_STATE) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rowSpanning: EMPTY_STATE\n        }));\n      }\n      return;\n    }\n    if (range === null || !isRowContextInitialized(renderContext)) {\n      return;\n    }\n    if (resetState) {\n      processedRange.current = EMPTY_RANGE;\n    }\n    const rangeToProcess = getUnprocessedRange({\n      firstRowIndex: renderContext.firstRowIndex,\n      lastRowIndex: Math.min(renderContext.lastRowIndex - 1, range.lastRowIndex)\n    }, processedRange.current);\n    if (rangeToProcess === null) {\n      return;\n    }\n    const {\n      spannedCells,\n      hiddenCells,\n      hiddenCellOriginMap,\n      processedRange: newProcessedRange\n    } = computeRowSpanningState(apiRef, colDefs, visibleRows, range, rangeToProcess, resetState, processedRange.current);\n    processedRange.current = newProcessedRange;\n    const newSpannedCellsCount = Object.keys(spannedCells).length;\n    const newHiddenCellsCount = Object.keys(hiddenCells).length;\n    const currentSpannedCellsCount = Object.keys(apiRef.current.state.rowSpanning.spannedCells).length;\n    const currentHiddenCellsCount = Object.keys(apiRef.current.state.rowSpanning.hiddenCells).length;\n    const shouldUpdateState = resetState || newSpannedCellsCount !== currentSpannedCellsCount || newHiddenCellsCount !== currentHiddenCellsCount;\n    if (!shouldUpdateState) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rowSpanning: {\n          spannedCells,\n          hiddenCells,\n          hiddenCellOriginMap\n        }\n      });\n    });\n  }, [apiRef, props.unstable_rowSpanning, range, renderContext, visibleRows, colDefs, processedRange]);\n  const prevRenderContext = React.useRef(renderContext);\n  const isFirstRender = React.useRef(true);\n  const shouldResetState = React.useRef(false);\n  React.useEffect(() => {\n    const firstRender = isFirstRender.current;\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    }\n    if (range && lastRange.current && isRowRangeUpdated(range, lastRange.current)) {\n      lastRange.current = range;\n      shouldResetState.current = true;\n    }\n    if (!firstRender && prevRenderContext.current !== renderContext) {\n      if (isRowRangeUpdated(prevRenderContext.current, renderContext)) {\n        updateRowSpanningState(shouldResetState.current);\n        shouldResetState.current = false;\n      }\n      prevRenderContext.current = renderContext;\n      return;\n    }\n    updateRowSpanningState();\n  }, [updateRowSpanningState, renderContext, range, lastRange]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useLazyRef", "gridVisibleColumnDefinitionsSelector", "useGridVisibleRows", "gridRenderContextSelector", "useGridSelector", "getUnprocessedRange", "isRowRangeUpdated", "isRowContextInitialized", "getCellValue", "EMPTY_STATE", "<PERSON><PERSON><PERSON><PERSON>", "hidden<PERSON>ells", "hiddenCellOriginMap", "EMPTY_RANGE", "firstRowIndex", "lastRowIndex", "<PERSON><PERSON>ields", "Set", "DEFAULT_ROWS_TO_PROCESS", "computeRowSpanningState", "apiRef", "colDefs", "visibleRows", "range", "rangeToProcess", "resetState", "processedRange", "current", "state", "rowSpanning", "for<PERSON>ach", "colDef", "has", "field", "index", "row", "id", "cellValue", "model", "spannedRowId", "spannedRowIndex", "rowSpan", "backwards<PERSON><PERSON>denCells", "prevIndex", "prevRowEntry", "currentRow", "push", "hiddenCellIndex", "relativeIndex", "Math", "min", "max", "rowSpanningStateInitializer", "props", "unstable_rowSpanning", "rowIds", "rows", "dataRowIds", "orderedFields", "columns", "dataRowIdToModelLookup", "columnsLookup", "lookup", "isFilteringPending", "Boolean", "filter", "filterModel", "items", "length", "quickFilterV<PERSON>ues", "map", "useGridRowSpanning", "renderContext", "Object", "keys", "<PERSON><PERSON><PERSON><PERSON>", "useRef", "updateRowSpanningState", "useCallback", "arguments", "undefined", "setState", "newProcessedRange", "newSpannedCellsCount", "newHiddenCellsCount", "currentSpannedCellsCount", "currentHiddenCellsCount", "shouldUpdateState", "prevRenderContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shouldResetState", "useEffect", "firstRender"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/rows/useGridRowSpanning.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridRenderContextSelector } from \"../virtualization/gridVirtualizationSelectors.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { getUnprocessedRange, isRowRangeUpdated, isRowContextInitialized, getCellValue } from \"./gridRowSpanningUtils.js\";\nconst EMPTY_STATE = {\n  spannedCells: {},\n  hiddenCells: {},\n  hiddenCellOriginMap: {}\n};\nconst EMPTY_RANGE = {\n  firstRowIndex: 0,\n  lastRowIndex: 0\n};\nconst skippedFields = new Set(['__check__', '__reorder__', '__detail_panel_toggle__']);\n/**\n * Default number of rows to process during state initialization to avoid flickering.\n * Number `20` is arbitrarily chosen to be large enough to cover most of the cases without\n * compromising performance.\n */\nconst DEFAULT_ROWS_TO_PROCESS = 20;\nconst computeRowSpanningState = (apiRef, colDefs, visibleRows, range, rangeToProcess, resetState, processedRange) => {\n  const spannedCells = resetState ? {} : _extends({}, apiRef.current.state.rowSpanning.spannedCells);\n  const hiddenCells = resetState ? {} : _extends({}, apiRef.current.state.rowSpanning.hiddenCells);\n  const hiddenCellOriginMap = resetState ? {} : _extends({}, apiRef.current.state.rowSpanning.hiddenCellOriginMap);\n  if (resetState) {\n    processedRange = EMPTY_RANGE;\n  }\n  colDefs.forEach(colDef => {\n    if (skippedFields.has(colDef.field)) {\n      return;\n    }\n    for (let index = rangeToProcess.firstRowIndex; index <= rangeToProcess.lastRowIndex; index += 1) {\n      const row = visibleRows[index];\n      if (hiddenCells[row.id]?.[colDef.field]) {\n        continue;\n      }\n      const cellValue = getCellValue(row.model, colDef, apiRef);\n      if (cellValue == null) {\n        continue;\n      }\n      let spannedRowId = row.id;\n      let spannedRowIndex = index;\n      let rowSpan = 0;\n\n      // For first index, also scan in the previous rows to handle the reset state case e.g by sorting\n      const backwardsHiddenCells = [];\n      if (index === rangeToProcess.firstRowIndex) {\n        let prevIndex = index - 1;\n        const prevRowEntry = visibleRows[prevIndex];\n        while (prevIndex >= range.firstRowIndex && getCellValue(prevRowEntry.model, colDef, apiRef) === cellValue) {\n          const currentRow = visibleRows[prevIndex + 1];\n          if (hiddenCells[currentRow.id]) {\n            hiddenCells[currentRow.id][colDef.field] = true;\n          } else {\n            hiddenCells[currentRow.id] = {\n              [colDef.field]: true\n            };\n          }\n          backwardsHiddenCells.push(index);\n          rowSpan += 1;\n          spannedRowId = prevRowEntry.id;\n          spannedRowIndex = prevIndex;\n          prevIndex -= 1;\n        }\n      }\n      backwardsHiddenCells.forEach(hiddenCellIndex => {\n        if (hiddenCellOriginMap[hiddenCellIndex]) {\n          hiddenCellOriginMap[hiddenCellIndex][colDef.field] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[hiddenCellIndex] = {\n            [colDef.field]: spannedRowIndex\n          };\n        }\n      });\n\n      // Scan the next rows\n      let relativeIndex = index + 1;\n      while (relativeIndex <= range.lastRowIndex && visibleRows[relativeIndex] && getCellValue(visibleRows[relativeIndex].model, colDef, apiRef) === cellValue) {\n        const currentRow = visibleRows[relativeIndex];\n        if (hiddenCells[currentRow.id]) {\n          hiddenCells[currentRow.id][colDef.field] = true;\n        } else {\n          hiddenCells[currentRow.id] = {\n            [colDef.field]: true\n          };\n        }\n        if (hiddenCellOriginMap[relativeIndex]) {\n          hiddenCellOriginMap[relativeIndex][colDef.field] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[relativeIndex] = {\n            [colDef.field]: spannedRowIndex\n          };\n        }\n        relativeIndex += 1;\n        rowSpan += 1;\n      }\n      if (rowSpan > 0) {\n        if (spannedCells[spannedRowId]) {\n          spannedCells[spannedRowId][colDef.field] = rowSpan + 1;\n        } else {\n          spannedCells[spannedRowId] = {\n            [colDef.field]: rowSpan + 1\n          };\n        }\n      }\n    }\n    processedRange = {\n      firstRowIndex: Math.min(processedRange.firstRowIndex, rangeToProcess.firstRowIndex),\n      lastRowIndex: Math.max(processedRange.lastRowIndex, rangeToProcess.lastRowIndex)\n    };\n  });\n  return {\n    spannedCells,\n    hiddenCells,\n    hiddenCellOriginMap,\n    processedRange\n  };\n};\n\n/**\n * @requires columnsStateInitializer (method) - should be initialized before\n * @requires rowsStateInitializer (method) - should be initialized before\n * @requires filterStateInitializer (method) - should be initialized before\n */\nexport const rowSpanningStateInitializer = (state, props, apiRef) => {\n  if (props.unstable_rowSpanning) {\n    const rowIds = state.rows.dataRowIds || [];\n    const orderedFields = state.columns.orderedFields || [];\n    const dataRowIdToModelLookup = state.rows.dataRowIdToModelLookup;\n    const columnsLookup = state.columns.lookup;\n    const isFilteringPending = Boolean(state.filter.filterModel.items.length) || Boolean(state.filter.filterModel.quickFilterValues?.length);\n    if (!rowIds.length || !orderedFields.length || !dataRowIdToModelLookup || !columnsLookup || isFilteringPending) {\n      return _extends({}, state, {\n        rowSpanning: EMPTY_STATE\n      });\n    }\n    const rangeToProcess = {\n      firstRowIndex: 0,\n      lastRowIndex: Math.min(DEFAULT_ROWS_TO_PROCESS - 1, Math.max(rowIds.length - 1, 0))\n    };\n    const rows = rowIds.map(id => ({\n      id,\n      model: dataRowIdToModelLookup[id]\n    }));\n    const colDefs = orderedFields.map(field => columnsLookup[field]);\n    const {\n      spannedCells,\n      hiddenCells,\n      hiddenCellOriginMap\n    } = computeRowSpanningState(apiRef, colDefs, rows, rangeToProcess, rangeToProcess, true, EMPTY_RANGE);\n    return _extends({}, state, {\n      rowSpanning: {\n        spannedCells,\n        hiddenCells,\n        hiddenCellOriginMap\n      }\n    });\n  }\n  return _extends({}, state, {\n    rowSpanning: EMPTY_STATE\n  });\n};\nexport const useGridRowSpanning = (apiRef, props) => {\n  const {\n    range,\n    rows: visibleRows\n  } = useGridVisibleRows(apiRef, props);\n  const renderContext = useGridSelector(apiRef, gridRenderContextSelector);\n  const colDefs = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const processedRange = useLazyRef(() => {\n    return Object.keys(apiRef.current.state.rowSpanning.spannedCells).length > 0 ? {\n      firstRowIndex: 0,\n      lastRowIndex: Math.min(DEFAULT_ROWS_TO_PROCESS - 1, Math.max(apiRef.current.state.rows.dataRowIds.length - 1, 0))\n    } : EMPTY_RANGE;\n  });\n  const lastRange = React.useRef(EMPTY_RANGE);\n  const updateRowSpanningState = React.useCallback(\n  // A reset needs to occur when:\n  // - The `unstable_rowSpanning` prop is updated (feature flag)\n  // - The filtering is applied\n  // - The sorting is applied\n  // - The `paginationModel` is updated\n  // - The rows are updated\n  (resetState = true) => {\n    if (!props.unstable_rowSpanning) {\n      if (apiRef.current.state.rowSpanning !== EMPTY_STATE) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rowSpanning: EMPTY_STATE\n        }));\n      }\n      return;\n    }\n    if (range === null || !isRowContextInitialized(renderContext)) {\n      return;\n    }\n    if (resetState) {\n      processedRange.current = EMPTY_RANGE;\n    }\n    const rangeToProcess = getUnprocessedRange({\n      firstRowIndex: renderContext.firstRowIndex,\n      lastRowIndex: Math.min(renderContext.lastRowIndex - 1, range.lastRowIndex)\n    }, processedRange.current);\n    if (rangeToProcess === null) {\n      return;\n    }\n    const {\n      spannedCells,\n      hiddenCells,\n      hiddenCellOriginMap,\n      processedRange: newProcessedRange\n    } = computeRowSpanningState(apiRef, colDefs, visibleRows, range, rangeToProcess, resetState, processedRange.current);\n    processedRange.current = newProcessedRange;\n    const newSpannedCellsCount = Object.keys(spannedCells).length;\n    const newHiddenCellsCount = Object.keys(hiddenCells).length;\n    const currentSpannedCellsCount = Object.keys(apiRef.current.state.rowSpanning.spannedCells).length;\n    const currentHiddenCellsCount = Object.keys(apiRef.current.state.rowSpanning.hiddenCells).length;\n    const shouldUpdateState = resetState || newSpannedCellsCount !== currentSpannedCellsCount || newHiddenCellsCount !== currentHiddenCellsCount;\n    if (!shouldUpdateState) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rowSpanning: {\n          spannedCells,\n          hiddenCells,\n          hiddenCellOriginMap\n        }\n      });\n    });\n  }, [apiRef, props.unstable_rowSpanning, range, renderContext, visibleRows, colDefs, processedRange]);\n  const prevRenderContext = React.useRef(renderContext);\n  const isFirstRender = React.useRef(true);\n  const shouldResetState = React.useRef(false);\n  React.useEffect(() => {\n    const firstRender = isFirstRender.current;\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    }\n    if (range && lastRange.current && isRowRangeUpdated(range, lastRange.current)) {\n      lastRange.current = range;\n      shouldResetState.current = true;\n    }\n    if (!firstRender && prevRenderContext.current !== renderContext) {\n      if (isRowRangeUpdated(prevRenderContext.current, renderContext)) {\n        updateRowSpanningState(shouldResetState.current);\n        shouldResetState.current = false;\n      }\n      prevRenderContext.current = renderContext;\n      return;\n    }\n    updateRowSpanningState();\n  }, [updateRowSpanningState, renderContext, range, lastRange]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,oCAAoC,QAAQ,mCAAmC;AACxF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,mBAAmB,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,YAAY,QAAQ,2BAA2B;AACzH,MAAMC,WAAW,GAAG;EAClBC,YAAY,EAAE,CAAC,CAAC;EAChBC,WAAW,EAAE,CAAC,CAAC;EACfC,mBAAmB,EAAE,CAAC;AACxB,CAAC;AACD,MAAMC,WAAW,GAAG;EAClBC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE;AAChB,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,EAAE;AAClC,MAAMC,uBAAuB,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEC,cAAc,EAAEC,UAAU,EAAEC,cAAc,KAAK;EACnH,MAAMhB,YAAY,GAAGe,UAAU,GAAG,CAAC,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,EAAEsB,MAAM,CAACO,OAAO,CAACC,KAAK,CAACC,WAAW,CAACnB,YAAY,CAAC;EAClG,MAAMC,WAAW,GAAGc,UAAU,GAAG,CAAC,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,EAAEsB,MAAM,CAACO,OAAO,CAACC,KAAK,CAACC,WAAW,CAAClB,WAAW,CAAC;EAChG,MAAMC,mBAAmB,GAAGa,UAAU,GAAG,CAAC,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,EAAEsB,MAAM,CAACO,OAAO,CAACC,KAAK,CAACC,WAAW,CAACjB,mBAAmB,CAAC;EAChH,IAAIa,UAAU,EAAE;IACdC,cAAc,GAAGb,WAAW;EAC9B;EACAQ,OAAO,CAACS,OAAO,CAACC,MAAM,IAAI;IACxB,IAAIf,aAAa,CAACgB,GAAG,CAACD,MAAM,CAACE,KAAK,CAAC,EAAE;MACnC;IACF;IACA,KAAK,IAAIC,KAAK,GAAGV,cAAc,CAACV,aAAa,EAAEoB,KAAK,IAAIV,cAAc,CAACT,YAAY,EAAEmB,KAAK,IAAI,CAAC,EAAE;MAC/F,MAAMC,GAAG,GAAGb,WAAW,CAACY,KAAK,CAAC;MAC9B,IAAIvB,WAAW,CAACwB,GAAG,CAACC,EAAE,CAAC,GAAGL,MAAM,CAACE,KAAK,CAAC,EAAE;QACvC;MACF;MACA,MAAMI,SAAS,GAAG7B,YAAY,CAAC2B,GAAG,CAACG,KAAK,EAAEP,MAAM,EAAEX,MAAM,CAAC;MACzD,IAAIiB,SAAS,IAAI,IAAI,EAAE;QACrB;MACF;MACA,IAAIE,YAAY,GAAGJ,GAAG,CAACC,EAAE;MACzB,IAAII,eAAe,GAAGN,KAAK;MAC3B,IAAIO,OAAO,GAAG,CAAC;;MAEf;MACA,MAAMC,oBAAoB,GAAG,EAAE;MAC/B,IAAIR,KAAK,KAAKV,cAAc,CAACV,aAAa,EAAE;QAC1C,IAAI6B,SAAS,GAAGT,KAAK,GAAG,CAAC;QACzB,MAAMU,YAAY,GAAGtB,WAAW,CAACqB,SAAS,CAAC;QAC3C,OAAOA,SAAS,IAAIpB,KAAK,CAACT,aAAa,IAAIN,YAAY,CAACoC,YAAY,CAACN,KAAK,EAAEP,MAAM,EAAEX,MAAM,CAAC,KAAKiB,SAAS,EAAE;UACzG,MAAMQ,UAAU,GAAGvB,WAAW,CAACqB,SAAS,GAAG,CAAC,CAAC;UAC7C,IAAIhC,WAAW,CAACkC,UAAU,CAACT,EAAE,CAAC,EAAE;YAC9BzB,WAAW,CAACkC,UAAU,CAACT,EAAE,CAAC,CAACL,MAAM,CAACE,KAAK,CAAC,GAAG,IAAI;UACjD,CAAC,MAAM;YACLtB,WAAW,CAACkC,UAAU,CAACT,EAAE,CAAC,GAAG;cAC3B,CAACL,MAAM,CAACE,KAAK,GAAG;YAClB,CAAC;UACH;UACAS,oBAAoB,CAACI,IAAI,CAACZ,KAAK,CAAC;UAChCO,OAAO,IAAI,CAAC;UACZF,YAAY,GAAGK,YAAY,CAACR,EAAE;UAC9BI,eAAe,GAAGG,SAAS;UAC3BA,SAAS,IAAI,CAAC;QAChB;MACF;MACAD,oBAAoB,CAACZ,OAAO,CAACiB,eAAe,IAAI;QAC9C,IAAInC,mBAAmB,CAACmC,eAAe,CAAC,EAAE;UACxCnC,mBAAmB,CAACmC,eAAe,CAAC,CAAChB,MAAM,CAACE,KAAK,CAAC,GAAGO,eAAe;QACtE,CAAC,MAAM;UACL5B,mBAAmB,CAACmC,eAAe,CAAC,GAAG;YACrC,CAAChB,MAAM,CAACE,KAAK,GAAGO;UAClB,CAAC;QACH;MACF,CAAC,CAAC;;MAEF;MACA,IAAIQ,aAAa,GAAGd,KAAK,GAAG,CAAC;MAC7B,OAAOc,aAAa,IAAIzB,KAAK,CAACR,YAAY,IAAIO,WAAW,CAAC0B,aAAa,CAAC,IAAIxC,YAAY,CAACc,WAAW,CAAC0B,aAAa,CAAC,CAACV,KAAK,EAAEP,MAAM,EAAEX,MAAM,CAAC,KAAKiB,SAAS,EAAE;QACxJ,MAAMQ,UAAU,GAAGvB,WAAW,CAAC0B,aAAa,CAAC;QAC7C,IAAIrC,WAAW,CAACkC,UAAU,CAACT,EAAE,CAAC,EAAE;UAC9BzB,WAAW,CAACkC,UAAU,CAACT,EAAE,CAAC,CAACL,MAAM,CAACE,KAAK,CAAC,GAAG,IAAI;QACjD,CAAC,MAAM;UACLtB,WAAW,CAACkC,UAAU,CAACT,EAAE,CAAC,GAAG;YAC3B,CAACL,MAAM,CAACE,KAAK,GAAG;UAClB,CAAC;QACH;QACA,IAAIrB,mBAAmB,CAACoC,aAAa,CAAC,EAAE;UACtCpC,mBAAmB,CAACoC,aAAa,CAAC,CAACjB,MAAM,CAACE,KAAK,CAAC,GAAGO,eAAe;QACpE,CAAC,MAAM;UACL5B,mBAAmB,CAACoC,aAAa,CAAC,GAAG;YACnC,CAACjB,MAAM,CAACE,KAAK,GAAGO;UAClB,CAAC;QACH;QACAQ,aAAa,IAAI,CAAC;QAClBP,OAAO,IAAI,CAAC;MACd;MACA,IAAIA,OAAO,GAAG,CAAC,EAAE;QACf,IAAI/B,YAAY,CAAC6B,YAAY,CAAC,EAAE;UAC9B7B,YAAY,CAAC6B,YAAY,CAAC,CAACR,MAAM,CAACE,KAAK,CAAC,GAAGQ,OAAO,GAAG,CAAC;QACxD,CAAC,MAAM;UACL/B,YAAY,CAAC6B,YAAY,CAAC,GAAG;YAC3B,CAACR,MAAM,CAACE,KAAK,GAAGQ,OAAO,GAAG;UAC5B,CAAC;QACH;MACF;IACF;IACAf,cAAc,GAAG;MACfZ,aAAa,EAAEmC,IAAI,CAACC,GAAG,CAACxB,cAAc,CAACZ,aAAa,EAAEU,cAAc,CAACV,aAAa,CAAC;MACnFC,YAAY,EAAEkC,IAAI,CAACE,GAAG,CAACzB,cAAc,CAACX,YAAY,EAAES,cAAc,CAACT,YAAY;IACjF,CAAC;EACH,CAAC,CAAC;EACF,OAAO;IACLL,YAAY;IACZC,WAAW;IACXC,mBAAmB;IACnBc;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0B,2BAA2B,GAAGA,CAACxB,KAAK,EAAEyB,KAAK,EAAEjC,MAAM,KAAK;EACnE,IAAIiC,KAAK,CAACC,oBAAoB,EAAE;IAC9B,MAAMC,MAAM,GAAG3B,KAAK,CAAC4B,IAAI,CAACC,UAAU,IAAI,EAAE;IAC1C,MAAMC,aAAa,GAAG9B,KAAK,CAAC+B,OAAO,CAACD,aAAa,IAAI,EAAE;IACvD,MAAME,sBAAsB,GAAGhC,KAAK,CAAC4B,IAAI,CAACI,sBAAsB;IAChE,MAAMC,aAAa,GAAGjC,KAAK,CAAC+B,OAAO,CAACG,MAAM;IAC1C,MAAMC,kBAAkB,GAAGC,OAAO,CAACpC,KAAK,CAACqC,MAAM,CAACC,WAAW,CAACC,KAAK,CAACC,MAAM,CAAC,IAAIJ,OAAO,CAACpC,KAAK,CAACqC,MAAM,CAACC,WAAW,CAACG,iBAAiB,EAAED,MAAM,CAAC;IACxI,IAAI,CAACb,MAAM,CAACa,MAAM,IAAI,CAACV,aAAa,CAACU,MAAM,IAAI,CAACR,sBAAsB,IAAI,CAACC,aAAa,IAAIE,kBAAkB,EAAE;MAC9G,OAAOjE,QAAQ,CAAC,CAAC,CAAC,EAAE8B,KAAK,EAAE;QACzBC,WAAW,EAAEpB;MACf,CAAC,CAAC;IACJ;IACA,MAAMe,cAAc,GAAG;MACrBV,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAEkC,IAAI,CAACC,GAAG,CAAChC,uBAAuB,GAAG,CAAC,EAAE+B,IAAI,CAACE,GAAG,CAACI,MAAM,CAACa,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IACpF,CAAC;IACD,MAAMZ,IAAI,GAAGD,MAAM,CAACe,GAAG,CAAClC,EAAE,KAAK;MAC7BA,EAAE;MACFE,KAAK,EAAEsB,sBAAsB,CAACxB,EAAE;IAClC,CAAC,CAAC,CAAC;IACH,MAAMf,OAAO,GAAGqC,aAAa,CAACY,GAAG,CAACrC,KAAK,IAAI4B,aAAa,CAAC5B,KAAK,CAAC,CAAC;IAChE,MAAM;MACJvB,YAAY;MACZC,WAAW;MACXC;IACF,CAAC,GAAGO,uBAAuB,CAACC,MAAM,EAAEC,OAAO,EAAEmC,IAAI,EAAEhC,cAAc,EAAEA,cAAc,EAAE,IAAI,EAAEX,WAAW,CAAC;IACrG,OAAOf,QAAQ,CAAC,CAAC,CAAC,EAAE8B,KAAK,EAAE;MACzBC,WAAW,EAAE;QACXnB,YAAY;QACZC,WAAW;QACXC;MACF;IACF,CAAC,CAAC;EACJ;EACA,OAAOd,QAAQ,CAAC,CAAC,CAAC,EAAE8B,KAAK,EAAE;IACzBC,WAAW,EAAEpB;EACf,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAM8D,kBAAkB,GAAGA,CAACnD,MAAM,EAAEiC,KAAK,KAAK;EACnD,MAAM;IACJ9B,KAAK;IACLiC,IAAI,EAAElC;EACR,CAAC,GAAGpB,kBAAkB,CAACkB,MAAM,EAAEiC,KAAK,CAAC;EACrC,MAAMmB,aAAa,GAAGpE,eAAe,CAACgB,MAAM,EAAEjB,yBAAyB,CAAC;EACxE,MAAMkB,OAAO,GAAGjB,eAAe,CAACgB,MAAM,EAAEnB,oCAAoC,CAAC;EAC7E,MAAMyB,cAAc,GAAG1B,UAAU,CAAC,MAAM;IACtC,OAAOyE,MAAM,CAACC,IAAI,CAACtD,MAAM,CAACO,OAAO,CAACC,KAAK,CAACC,WAAW,CAACnB,YAAY,CAAC,CAAC0D,MAAM,GAAG,CAAC,GAAG;MAC7EtD,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAEkC,IAAI,CAACC,GAAG,CAAChC,uBAAuB,GAAG,CAAC,EAAE+B,IAAI,CAACE,GAAG,CAAC/B,MAAM,CAACO,OAAO,CAACC,KAAK,CAAC4B,IAAI,CAACC,UAAU,CAACW,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IAClH,CAAC,GAAGvD,WAAW;EACjB,CAAC,CAAC;EACF,MAAM8D,SAAS,GAAG5E,KAAK,CAAC6E,MAAM,CAAC/D,WAAW,CAAC;EAC3C,MAAMgE,sBAAsB,GAAG9E,KAAK,CAAC+E,WAAW;EAChD;EACA;EACA;EACA;EACA;EACA;EACA,YAAuB;IAAA,IAAtBrD,UAAU,GAAAsD,SAAA,CAAAX,MAAA,QAAAW,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;IAChB,IAAI,CAAC1B,KAAK,CAACC,oBAAoB,EAAE;MAC/B,IAAIlC,MAAM,CAACO,OAAO,CAACC,KAAK,CAACC,WAAW,KAAKpB,WAAW,EAAE;QACpDW,MAAM,CAACO,OAAO,CAACsD,QAAQ,CAACrD,KAAK,IAAI9B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,KAAK,EAAE;UACnDC,WAAW,EAAEpB;QACf,CAAC,CAAC,CAAC;MACL;MACA;IACF;IACA,IAAIc,KAAK,KAAK,IAAI,IAAI,CAAChB,uBAAuB,CAACiE,aAAa,CAAC,EAAE;MAC7D;IACF;IACA,IAAI/C,UAAU,EAAE;MACdC,cAAc,CAACC,OAAO,GAAGd,WAAW;IACtC;IACA,MAAMW,cAAc,GAAGnB,mBAAmB,CAAC;MACzCS,aAAa,EAAE0D,aAAa,CAAC1D,aAAa;MAC1CC,YAAY,EAAEkC,IAAI,CAACC,GAAG,CAACsB,aAAa,CAACzD,YAAY,GAAG,CAAC,EAAEQ,KAAK,CAACR,YAAY;IAC3E,CAAC,EAAEW,cAAc,CAACC,OAAO,CAAC;IAC1B,IAAIH,cAAc,KAAK,IAAI,EAAE;MAC3B;IACF;IACA,MAAM;MACJd,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBc,cAAc,EAAEwD;IAClB,CAAC,GAAG/D,uBAAuB,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEC,cAAc,EAAEC,UAAU,EAAEC,cAAc,CAACC,OAAO,CAAC;IACpHD,cAAc,CAACC,OAAO,GAAGuD,iBAAiB;IAC1C,MAAMC,oBAAoB,GAAGV,MAAM,CAACC,IAAI,CAAChE,YAAY,CAAC,CAAC0D,MAAM;IAC7D,MAAMgB,mBAAmB,GAAGX,MAAM,CAACC,IAAI,CAAC/D,WAAW,CAAC,CAACyD,MAAM;IAC3D,MAAMiB,wBAAwB,GAAGZ,MAAM,CAACC,IAAI,CAACtD,MAAM,CAACO,OAAO,CAACC,KAAK,CAACC,WAAW,CAACnB,YAAY,CAAC,CAAC0D,MAAM;IAClG,MAAMkB,uBAAuB,GAAGb,MAAM,CAACC,IAAI,CAACtD,MAAM,CAACO,OAAO,CAACC,KAAK,CAACC,WAAW,CAAClB,WAAW,CAAC,CAACyD,MAAM;IAChG,MAAMmB,iBAAiB,GAAG9D,UAAU,IAAI0D,oBAAoB,KAAKE,wBAAwB,IAAID,mBAAmB,KAAKE,uBAAuB;IAC5I,IAAI,CAACC,iBAAiB,EAAE;MACtB;IACF;IACAnE,MAAM,CAACO,OAAO,CAACsD,QAAQ,CAACrD,KAAK,IAAI;MAC/B,OAAO9B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,KAAK,EAAE;QACzBC,WAAW,EAAE;UACXnB,YAAY;UACZC,WAAW;UACXC;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACQ,MAAM,EAAEiC,KAAK,CAACC,oBAAoB,EAAE/B,KAAK,EAAEiD,aAAa,EAAElD,WAAW,EAAED,OAAO,EAAEK,cAAc,CAAC,CAAC;EACpG,MAAM8D,iBAAiB,GAAGzF,KAAK,CAAC6E,MAAM,CAACJ,aAAa,CAAC;EACrD,MAAMiB,aAAa,GAAG1F,KAAK,CAAC6E,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMc,gBAAgB,GAAG3F,KAAK,CAAC6E,MAAM,CAAC,KAAK,CAAC;EAC5C7E,KAAK,CAAC4F,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAGH,aAAa,CAAC9D,OAAO;IACzC,IAAI8D,aAAa,CAAC9D,OAAO,EAAE;MACzB8D,aAAa,CAAC9D,OAAO,GAAG,KAAK;IAC/B;IACA,IAAIJ,KAAK,IAAIoD,SAAS,CAAChD,OAAO,IAAIrB,iBAAiB,CAACiB,KAAK,EAAEoD,SAAS,CAAChD,OAAO,CAAC,EAAE;MAC7EgD,SAAS,CAAChD,OAAO,GAAGJ,KAAK;MACzBmE,gBAAgB,CAAC/D,OAAO,GAAG,IAAI;IACjC;IACA,IAAI,CAACiE,WAAW,IAAIJ,iBAAiB,CAAC7D,OAAO,KAAK6C,aAAa,EAAE;MAC/D,IAAIlE,iBAAiB,CAACkF,iBAAiB,CAAC7D,OAAO,EAAE6C,aAAa,CAAC,EAAE;QAC/DK,sBAAsB,CAACa,gBAAgB,CAAC/D,OAAO,CAAC;QAChD+D,gBAAgB,CAAC/D,OAAO,GAAG,KAAK;MAClC;MACA6D,iBAAiB,CAAC7D,OAAO,GAAG6C,aAAa;MACzC;IACF;IACAK,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,CAACA,sBAAsB,EAAEL,aAAa,EAAEjD,KAAK,EAAEoD,SAAS,CAAC,CAAC;AAC/D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}