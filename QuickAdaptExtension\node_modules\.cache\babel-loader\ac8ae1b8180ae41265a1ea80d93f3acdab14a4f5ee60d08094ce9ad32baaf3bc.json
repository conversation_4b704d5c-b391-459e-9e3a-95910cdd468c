{"ast": null, "code": "import { isLeaf } from \"../../../models/gridColumnGrouping.js\";\nimport { isDeepEqual } from \"../../../utils/utils.js\";\n// This is the recurrence function that help writing `unwrapGroupingColumnModel()`\nconst recurrentUnwrapGroupingColumnModel = (columnGroupNode, parents, unwrappedGroupingModelToComplete) => {\n  if (isLeaf(columnGroupNode)) {\n    if (unwrappedGroupingModelToComplete[columnGroupNode.field] !== undefined) {\n      throw new Error([`MUI X: columnGroupingModel contains duplicated field`, `column field ${columnGroupNode.field} occurs two times in the grouping model:`, `- ${unwrappedGroupingModelToComplete[columnGroupNode.field].join(' > ')}`, `- ${parents.join(' > ')}`].join('\\n'));\n    }\n    unwrappedGroupingModelToComplete[columnGroupNode.field] = parents;\n    return;\n  }\n  const {\n    groupId,\n    children\n  } = columnGroupNode;\n  children.forEach(child => {\n    recurrentUnwrapGroupingColumnModel(child, [...parents, groupId], unwrappedGroupingModelToComplete);\n  });\n};\n\n/**\n * This is a function that provide for each column the array of its parents.\n * Parents are ordered from the root to the leaf.\n * @param columnGroupingModel The model such as provided in DataGrid props\n * @returns An object `{[field]: groupIds}` where `groupIds` is the parents of the column `field`\n */\nexport const unwrapGroupingColumnModel = columnGroupingModel => {\n  if (!columnGroupingModel) {\n    return {};\n  }\n  const unwrappedSubTree = {};\n  columnGroupingModel.forEach(columnGroupNode => {\n    recurrentUnwrapGroupingColumnModel(columnGroupNode, [], unwrappedSubTree);\n  });\n  return unwrappedSubTree;\n};\nexport const getColumnGroupsHeaderStructure = (orderedColumns, unwrappedGroupingModel, pinnedFields) => {\n  const getParents = field => unwrappedGroupingModel[field] ?? [];\n  const groupingHeaderStructure = [];\n  const maxDepth = Math.max(...orderedColumns.map(field => getParents(field).length));\n  const haveSameParents = (field1, field2, depth) => isDeepEqual(getParents(field1).slice(0, depth + 1), getParents(field2).slice(0, depth + 1));\n  const haveDifferentContainers = (field1, field2) => {\n    if (pinnedFields?.left && pinnedFields.left.includes(field1) && !pinnedFields.left.includes(field2)) {\n      return true;\n    }\n    if (pinnedFields?.right && !pinnedFields.right.includes(field1) && pinnedFields.right.includes(field2)) {\n      return true;\n    }\n    return false;\n  };\n  for (let depth = 0; depth < maxDepth; depth += 1) {\n    const depthStructure = orderedColumns.reduce((structure, newField) => {\n      const groupId = getParents(newField)[depth] ?? null;\n      if (structure.length === 0) {\n        return [{\n          columnFields: [newField],\n          groupId\n        }];\n      }\n      const lastGroup = structure[structure.length - 1];\n      const prevField = lastGroup.columnFields[lastGroup.columnFields.length - 1];\n      const prevGroupId = lastGroup.groupId;\n      if (prevGroupId !== groupId || !haveSameParents(prevField, newField, depth) ||\n      // Fix for https://github.com/mui/mui-x/issues/7041\n      haveDifferentContainers(prevField, newField)) {\n        // It's a new group\n        return [...structure, {\n          columnFields: [newField],\n          groupId\n        }];\n      }\n\n      // It extends the previous group\n      return [...structure.slice(0, structure.length - 1), {\n        columnFields: [...lastGroup.columnFields, newField],\n        groupId\n      }];\n    }, []);\n    groupingHeaderStructure.push(depthStructure);\n  }\n  return groupingHeaderStructure;\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDeepEqual", "recurrentUnwrapGroupingColumnModel", "columnGroupNode", "parents", "unwrappedGroupingModelToComplete", "field", "undefined", "Error", "join", "groupId", "children", "for<PERSON>ach", "child", "unwrapGroupingColumnModel", "columnGroupingModel", "unwrappedSubTree", "getColumnGroupsHeaderStructure", "orderedColumns", "unwrappedGroupingModel", "pinnedFields", "getParents", "groupingHeaderStructure", "max<PERSON><PERSON><PERSON>", "Math", "max", "map", "length", "haveSameParents", "field1", "field2", "depth", "slice", "haveDifferentContainers", "left", "includes", "right", "depthStructure", "reduce", "structure", "newField", "columnFields", "lastGroup", "prevField", "prevGroupId", "push"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/columnGrouping/gridColumnGroupsUtils.js"], "sourcesContent": ["import { isLeaf } from \"../../../models/gridColumnGrouping.js\";\nimport { isDeepEqual } from \"../../../utils/utils.js\";\n// This is the recurrence function that help writing `unwrapGroupingColumnModel()`\nconst recurrentUnwrapGroupingColumnModel = (columnGroupNode, parents, unwrappedGroupingModelToComplete) => {\n  if (isLeaf(columnGroupNode)) {\n    if (unwrappedGroupingModelToComplete[columnGroupNode.field] !== undefined) {\n      throw new Error([`MUI X: columnGroupingModel contains duplicated field`, `column field ${columnGroupNode.field} occurs two times in the grouping model:`, `- ${unwrappedGroupingModelToComplete[columnGroupNode.field].join(' > ')}`, `- ${parents.join(' > ')}`].join('\\n'));\n    }\n    unwrappedGroupingModelToComplete[columnGroupNode.field] = parents;\n    return;\n  }\n  const {\n    groupId,\n    children\n  } = columnGroupNode;\n  children.forEach(child => {\n    recurrentUnwrapGroupingColumnModel(child, [...parents, groupId], unwrappedGroupingModelToComplete);\n  });\n};\n\n/**\n * This is a function that provide for each column the array of its parents.\n * Parents are ordered from the root to the leaf.\n * @param columnGroupingModel The model such as provided in DataGrid props\n * @returns An object `{[field]: groupIds}` where `groupIds` is the parents of the column `field`\n */\nexport const unwrapGroupingColumnModel = columnGroupingModel => {\n  if (!columnGroupingModel) {\n    return {};\n  }\n  const unwrappedSubTree = {};\n  columnGroupingModel.forEach(columnGroupNode => {\n    recurrentUnwrapGroupingColumnModel(columnGroupNode, [], unwrappedSubTree);\n  });\n  return unwrappedSubTree;\n};\nexport const getColumnGroupsHeaderStructure = (orderedColumns, unwrappedGroupingModel, pinnedFields) => {\n  const getParents = field => unwrappedGroupingModel[field] ?? [];\n  const groupingHeaderStructure = [];\n  const maxDepth = Math.max(...orderedColumns.map(field => getParents(field).length));\n  const haveSameParents = (field1, field2, depth) => isDeepEqual(getParents(field1).slice(0, depth + 1), getParents(field2).slice(0, depth + 1));\n  const haveDifferentContainers = (field1, field2) => {\n    if (pinnedFields?.left && pinnedFields.left.includes(field1) && !pinnedFields.left.includes(field2)) {\n      return true;\n    }\n    if (pinnedFields?.right && !pinnedFields.right.includes(field1) && pinnedFields.right.includes(field2)) {\n      return true;\n    }\n    return false;\n  };\n  for (let depth = 0; depth < maxDepth; depth += 1) {\n    const depthStructure = orderedColumns.reduce((structure, newField) => {\n      const groupId = getParents(newField)[depth] ?? null;\n      if (structure.length === 0) {\n        return [{\n          columnFields: [newField],\n          groupId\n        }];\n      }\n      const lastGroup = structure[structure.length - 1];\n      const prevField = lastGroup.columnFields[lastGroup.columnFields.length - 1];\n      const prevGroupId = lastGroup.groupId;\n      if (prevGroupId !== groupId || !haveSameParents(prevField, newField, depth) ||\n      // Fix for https://github.com/mui/mui-x/issues/7041\n      haveDifferentContainers(prevField, newField)) {\n        // It's a new group\n        return [...structure, {\n          columnFields: [newField],\n          groupId\n        }];\n      }\n\n      // It extends the previous group\n      return [...structure.slice(0, structure.length - 1), {\n        columnFields: [...lastGroup.columnFields, newField],\n        groupId\n      }];\n    }, []);\n    groupingHeaderStructure.push(depthStructure);\n  }\n  return groupingHeaderStructure;\n};"], "mappings": "AAAA,SAASA,MAAM,QAAQ,uCAAuC;AAC9D,SAASC,WAAW,QAAQ,yBAAyB;AACrD;AACA,MAAMC,kCAAkC,GAAGA,CAACC,eAAe,EAAEC,OAAO,EAAEC,gCAAgC,KAAK;EACzG,IAAIL,MAAM,CAACG,eAAe,CAAC,EAAE;IAC3B,IAAIE,gCAAgC,CAACF,eAAe,CAACG,KAAK,CAAC,KAAKC,SAAS,EAAE;MACzE,MAAM,IAAIC,KAAK,CAAC,CAAC,sDAAsD,EAAE,gBAAgBL,eAAe,CAACG,KAAK,0CAA0C,EAAE,KAAKD,gCAAgC,CAACF,eAAe,CAACG,KAAK,CAAC,CAACG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,KAAKL,OAAO,CAACK,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/Q;IACAJ,gCAAgC,CAACF,eAAe,CAACG,KAAK,CAAC,GAAGF,OAAO;IACjE;EACF;EACA,MAAM;IACJM,OAAO;IACPC;EACF,CAAC,GAAGR,eAAe;EACnBQ,QAAQ,CAACC,OAAO,CAACC,KAAK,IAAI;IACxBX,kCAAkC,CAACW,KAAK,EAAE,CAAC,GAAGT,OAAO,EAAEM,OAAO,CAAC,EAAEL,gCAAgC,CAAC;EACpG,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,yBAAyB,GAAGC,mBAAmB,IAAI;EAC9D,IAAI,CAACA,mBAAmB,EAAE;IACxB,OAAO,CAAC,CAAC;EACX;EACA,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3BD,mBAAmB,CAACH,OAAO,CAACT,eAAe,IAAI;IAC7CD,kCAAkC,CAACC,eAAe,EAAE,EAAE,EAAEa,gBAAgB,CAAC;EAC3E,CAAC,CAAC;EACF,OAAOA,gBAAgB;AACzB,CAAC;AACD,OAAO,MAAMC,8BAA8B,GAAGA,CAACC,cAAc,EAAEC,sBAAsB,EAAEC,YAAY,KAAK;EACtG,MAAMC,UAAU,GAAGf,KAAK,IAAIa,sBAAsB,CAACb,KAAK,CAAC,IAAI,EAAE;EAC/D,MAAMgB,uBAAuB,GAAG,EAAE;EAClC,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,cAAc,CAACQ,GAAG,CAACpB,KAAK,IAAIe,UAAU,CAACf,KAAK,CAAC,CAACqB,MAAM,CAAC,CAAC;EACnF,MAAMC,eAAe,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,KAAK9B,WAAW,CAACoB,UAAU,CAACQ,MAAM,CAAC,CAACG,KAAK,CAAC,CAAC,EAAED,KAAK,GAAG,CAAC,CAAC,EAAEV,UAAU,CAACS,MAAM,CAAC,CAACE,KAAK,CAAC,CAAC,EAAED,KAAK,GAAG,CAAC,CAAC,CAAC;EAC9I,MAAME,uBAAuB,GAAGA,CAACJ,MAAM,EAAEC,MAAM,KAAK;IAClD,IAAIV,YAAY,EAAEc,IAAI,IAAId,YAAY,CAACc,IAAI,CAACC,QAAQ,CAACN,MAAM,CAAC,IAAI,CAACT,YAAY,CAACc,IAAI,CAACC,QAAQ,CAACL,MAAM,CAAC,EAAE;MACnG,OAAO,IAAI;IACb;IACA,IAAIV,YAAY,EAAEgB,KAAK,IAAI,CAAChB,YAAY,CAACgB,KAAK,CAACD,QAAQ,CAACN,MAAM,CAAC,IAAIT,YAAY,CAACgB,KAAK,CAACD,QAAQ,CAACL,MAAM,CAAC,EAAE;MACtG,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC;EACD,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGR,QAAQ,EAAEQ,KAAK,IAAI,CAAC,EAAE;IAChD,MAAMM,cAAc,GAAGnB,cAAc,CAACoB,MAAM,CAAC,CAACC,SAAS,EAAEC,QAAQ,KAAK;MACpE,MAAM9B,OAAO,GAAGW,UAAU,CAACmB,QAAQ,CAAC,CAACT,KAAK,CAAC,IAAI,IAAI;MACnD,IAAIQ,SAAS,CAACZ,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO,CAAC;UACNc,YAAY,EAAE,CAACD,QAAQ,CAAC;UACxB9B;QACF,CAAC,CAAC;MACJ;MACA,MAAMgC,SAAS,GAAGH,SAAS,CAACA,SAAS,CAACZ,MAAM,GAAG,CAAC,CAAC;MACjD,MAAMgB,SAAS,GAAGD,SAAS,CAACD,YAAY,CAACC,SAAS,CAACD,YAAY,CAACd,MAAM,GAAG,CAAC,CAAC;MAC3E,MAAMiB,WAAW,GAAGF,SAAS,CAAChC,OAAO;MACrC,IAAIkC,WAAW,KAAKlC,OAAO,IAAI,CAACkB,eAAe,CAACe,SAAS,EAAEH,QAAQ,EAAET,KAAK,CAAC;MAC3E;MACAE,uBAAuB,CAACU,SAAS,EAAEH,QAAQ,CAAC,EAAE;QAC5C;QACA,OAAO,CAAC,GAAGD,SAAS,EAAE;UACpBE,YAAY,EAAE,CAACD,QAAQ,CAAC;UACxB9B;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,OAAO,CAAC,GAAG6B,SAAS,CAACP,KAAK,CAAC,CAAC,EAAEO,SAAS,CAACZ,MAAM,GAAG,CAAC,CAAC,EAAE;QACnDc,YAAY,EAAE,CAAC,GAAGC,SAAS,CAACD,YAAY,EAAED,QAAQ,CAAC;QACnD9B;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IACNY,uBAAuB,CAACuB,IAAI,CAACR,cAAc,CAAC;EAC9C;EACA,OAAOf,uBAAuB;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}