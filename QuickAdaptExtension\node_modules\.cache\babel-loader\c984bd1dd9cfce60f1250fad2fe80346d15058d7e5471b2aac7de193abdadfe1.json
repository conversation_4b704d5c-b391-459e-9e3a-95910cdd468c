{"ast": null, "code": "const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : 'empty';\nexport const isFirefox = userAgent.includes('firefox');", "map": {"version": 3, "names": ["userAgent", "navigator", "toLowerCase", "isFirefox", "includes"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/utils/platform.js"], "sourcesContent": ["const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : 'empty';\nexport const isFirefox = userAgent.includes('firefox');"], "mappings": "AAAA,MAAMA,SAAS,GAAG,OAAOC,SAAS,KAAK,WAAW,GAAGA,SAAS,CAACD,SAAS,CAACE,WAAW,CAAC,CAAC,GAAG,OAAO;AAChG,OAAO,MAAMC,SAAS,GAAGH,SAAS,CAACI,QAAQ,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}