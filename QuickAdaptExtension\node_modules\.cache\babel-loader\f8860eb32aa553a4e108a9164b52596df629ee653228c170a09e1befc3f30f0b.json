{"ast": null, "code": "export { fastObjectShallowCompare } from \"./fastObjectShallowCompare.js\";", "map": {"version": 3, "names": ["fastObjectShallowCompare"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-internals/fastObjectShallowCompare/index.js"], "sourcesContent": ["export { fastObjectShallowCompare } from \"./fastObjectShallowCompare.js\";"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}