{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_ownerWindow as ownerWindow } from '@mui/utils';\nimport { throttle } from '@mui/x-internals/throttle';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnsTotalWidthSelector, gridVisiblePinnedColumnDefinitionsSelector } from \"../columns/index.js\";\nimport { gridDimensionsSelector } from \"./gridDimensionsSelectors.js\";\nimport { gridDensityFactorSelector } from \"../density/index.js\";\nimport { gridRenderContextSelector } from \"../virtualization/index.js\";\nimport { useGridSelector } from \"../../utils/index.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { calculatePinnedRowsHeight } from \"../rows/gridRowsUtils.js\";\nimport { getTotalHeaderHeight } from \"../columns/gridColumnsUtils.js\";\nconst EMPTY_SIZE = {\n  width: 0,\n  height: 0\n};\nconst EMPTY_DIMENSIONS = {\n  isReady: false,\n  root: EMPTY_SIZE,\n  viewportOuterSize: EMPTY_SIZE,\n  viewportInnerSize: EMPTY_SIZE,\n  contentSize: EMPTY_SIZE,\n  minimumSize: EMPTY_SIZE,\n  hasScrollX: false,\n  hasScrollY: false,\n  scrollbarSize: 0,\n  headerHeight: 0,\n  groupHeaderHeight: 0,\n  headerFilterHeight: 0,\n  rowWidth: 0,\n  rowHeight: 0,\n  columnsTotalWidth: 0,\n  leftPinnedWidth: 0,\n  rightPinnedWidth: 0,\n  headersTotalHeight: 0,\n  topContainerHeight: 0,\n  bottomContainerHeight: 0\n};\nexport const dimensionsStateInitializer = state => {\n  const dimensions = EMPTY_DIMENSIONS;\n  return _extends({}, state, {\n    dimensions\n  });\n};\nexport function useGridDimensions(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useResizeContainer');\n  const errorShown = React.useRef(false);\n  const rootDimensionsRef = React.useRef(EMPTY_SIZE);\n  const dimensionsState = useGridSelector(apiRef, gridDimensionsSelector);\n  const rowsMeta = useGridSelector(apiRef, gridRowsMetaSelector);\n  const pinnedColumns = useGridSelector(apiRef, gridVisiblePinnedColumnDefinitionsSelector);\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const rowHeight = Math.floor(props.rowHeight * densityFactor);\n  const headerHeight = Math.floor(props.columnHeaderHeight * densityFactor);\n  const groupHeaderHeight = Math.floor((props.columnGroupHeaderHeight ?? props.columnHeaderHeight) * densityFactor);\n  const headerFilterHeight = Math.floor((props.headerFilterHeight ?? props.columnHeaderHeight) * densityFactor);\n  const columnsTotalWidth = roundToDecimalPlaces(gridColumnsTotalWidthSelector(apiRef), 6);\n  const headersTotalHeight = getTotalHeaderHeight(apiRef, props);\n  const leftPinnedWidth = pinnedColumns.left.reduce((w, col) => w + col.computedWidth, 0);\n  const rightPinnedWidth = pinnedColumns.right.reduce((w, col) => w + col.computedWidth, 0);\n  const [savedSize, setSavedSize] = React.useState();\n  const debouncedSetSavedSize = React.useMemo(() => throttle(setSavedSize, props.resizeThrottleMs), [props.resizeThrottleMs]);\n  const previousSize = React.useRef();\n  const getRootDimensions = () => apiRef.current.state.dimensions;\n  const setDimensions = useEventCallback(dimensions => {\n    apiRef.current.setState(state => _extends({}, state, {\n      dimensions\n    }));\n  });\n  const resize = React.useCallback(() => {\n    const element = apiRef.current.mainElementRef.current;\n    if (!element) {\n      return;\n    }\n    const computedStyle = ownerWindow(element).getComputedStyle(element);\n    const newSize = {\n      width: parseFloat(computedStyle.width) || 0,\n      height: parseFloat(computedStyle.height) || 0\n    };\n    if (!previousSize.current || !areElementSizesEqual(previousSize.current, newSize)) {\n      apiRef.current.publishEvent('resize', newSize);\n      previousSize.current = newSize;\n    }\n  }, [apiRef]);\n  const getViewportPageSize = React.useCallback(() => {\n    const dimensions = gridDimensionsSelector(apiRef.current.state);\n    if (!dimensions.isReady) {\n      return 0;\n    }\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n\n    // TODO: Use a combination of scrollTop, dimensions.viewportInnerSize.height and rowsMeta.possitions\n    // to find out the maximum number of rows that can fit in the visible part of the grid\n    if (props.getRowHeight) {\n      const renderContext = gridRenderContextSelector(apiRef);\n      const viewportPageSize = renderContext.lastRowIndex - renderContext.firstRowIndex;\n      return Math.min(viewportPageSize - 1, currentPage.rows.length);\n    }\n    const maximumPageSizeWithoutScrollBar = Math.floor(dimensions.viewportInnerSize.height / rowHeight);\n    return Math.min(maximumPageSizeWithoutScrollBar, currentPage.rows.length);\n  }, [apiRef, props.pagination, props.paginationMode, props.getRowHeight, rowHeight]);\n  const updateDimensions = React.useCallback(() => {\n    const rootElement = apiRef.current.rootElementRef.current;\n    const pinnedRowsHeight = calculatePinnedRowsHeight(apiRef);\n    const scrollbarSize = measureScrollbarSize(rootElement, columnsTotalWidth, props.scrollbarSize);\n    const topContainerHeight = headersTotalHeight + pinnedRowsHeight.top;\n    const bottomContainerHeight = pinnedRowsHeight.bottom;\n    const nonPinnedColumnsTotalWidth = columnsTotalWidth - leftPinnedWidth - rightPinnedWidth;\n    const contentSize = {\n      width: nonPinnedColumnsTotalWidth,\n      height: rowsMeta.currentPageTotalHeight\n    };\n    let viewportOuterSize;\n    let viewportInnerSize;\n    let hasScrollX = false;\n    let hasScrollY = false;\n    if (props.autoHeight) {\n      hasScrollY = false;\n      hasScrollX = Math.round(columnsTotalWidth) > Math.round(rootDimensionsRef.current.width);\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: topContainerHeight + bottomContainerHeight + contentSize.height\n      };\n      viewportInnerSize = {\n        width: Math.max(0, viewportOuterSize.width - (hasScrollY ? scrollbarSize : 0)),\n        height: Math.max(0, viewportOuterSize.height - (hasScrollX ? scrollbarSize : 0))\n      };\n    } else {\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: rootDimensionsRef.current.height\n      };\n      viewportInnerSize = {\n        width: Math.max(0, viewportOuterSize.width - leftPinnedWidth - rightPinnedWidth),\n        height: Math.max(0, viewportOuterSize.height - topContainerHeight - bottomContainerHeight)\n      };\n      const content = contentSize;\n      const container = viewportInnerSize;\n      const hasScrollXIfNoYScrollBar = content.width > container.width;\n      const hasScrollYIfNoXScrollBar = content.height > container.height;\n      if (hasScrollXIfNoYScrollBar || hasScrollYIfNoXScrollBar) {\n        hasScrollY = hasScrollYIfNoXScrollBar;\n        hasScrollX = content.width + (hasScrollY ? scrollbarSize : 0) > container.width;\n\n        // We recalculate the scroll y to consider the size of the x scrollbar.\n        if (hasScrollX) {\n          hasScrollY = content.height + scrollbarSize > container.height;\n        }\n      }\n      if (hasScrollY) {\n        viewportInnerSize.width -= scrollbarSize;\n      }\n      if (hasScrollX) {\n        viewportInnerSize.height -= scrollbarSize;\n      }\n    }\n    const rowWidth = Math.max(viewportOuterSize.width, columnsTotalWidth + (hasScrollY ? scrollbarSize : 0));\n    const minimumSize = {\n      width: columnsTotalWidth,\n      height: topContainerHeight + contentSize.height + bottomContainerHeight\n    };\n    const newDimensions = {\n      isReady: true,\n      root: rootDimensionsRef.current,\n      viewportOuterSize,\n      viewportInnerSize,\n      contentSize,\n      minimumSize,\n      hasScrollX,\n      hasScrollY,\n      scrollbarSize,\n      headerHeight,\n      groupHeaderHeight,\n      headerFilterHeight,\n      rowWidth,\n      rowHeight,\n      columnsTotalWidth,\n      leftPinnedWidth,\n      rightPinnedWidth,\n      headersTotalHeight,\n      topContainerHeight,\n      bottomContainerHeight\n    };\n    const prevDimensions = apiRef.current.state.dimensions;\n    setDimensions(newDimensions);\n    if (!areElementSizesEqual(newDimensions.viewportInnerSize, prevDimensions.viewportInnerSize)) {\n      apiRef.current.publishEvent('viewportInnerSizeChange', newDimensions.viewportInnerSize);\n    }\n    apiRef.current.updateRenderContext?.();\n  }, [apiRef, setDimensions, props.scrollbarSize, props.autoHeight, rowsMeta.currentPageTotalHeight, rowHeight, headerHeight, groupHeaderHeight, headerFilterHeight, columnsTotalWidth, headersTotalHeight, leftPinnedWidth, rightPinnedWidth]);\n  const apiPublic = {\n    resize,\n    getRootDimensions\n  };\n  const apiPrivate = {\n    updateDimensions,\n    getViewportPageSize\n  };\n  useGridApiMethod(apiRef, apiPublic, 'public');\n  useGridApiMethod(apiRef, apiPrivate, 'private');\n  useEnhancedEffect(() => {\n    if (savedSize) {\n      updateDimensions();\n      apiRef.current.publishEvent('debouncedResize', rootDimensionsRef.current);\n    }\n  }, [apiRef, savedSize, updateDimensions]);\n  const root = apiRef.current.rootElementRef.current;\n  useEnhancedEffect(() => {\n    if (!root) {\n      return;\n    }\n    const set = (k, v) => root.style.setProperty(k, v);\n    set('--DataGrid-width', `${dimensionsState.viewportOuterSize.width}px`);\n    set('--DataGrid-hasScrollX', `${Number(dimensionsState.hasScrollX)}`);\n    set('--DataGrid-hasScrollY', `${Number(dimensionsState.hasScrollY)}`);\n    set('--DataGrid-scrollbarSize', `${dimensionsState.scrollbarSize}px`);\n    set('--DataGrid-rowWidth', `${dimensionsState.rowWidth}px`);\n    set('--DataGrid-columnsTotalWidth', `${dimensionsState.columnsTotalWidth}px`);\n    set('--DataGrid-leftPinnedWidth', `${dimensionsState.leftPinnedWidth}px`);\n    set('--DataGrid-rightPinnedWidth', `${dimensionsState.rightPinnedWidth}px`);\n    set('--DataGrid-headerHeight', `${dimensionsState.headerHeight}px`);\n    set('--DataGrid-headersTotalHeight', `${dimensionsState.headersTotalHeight}px`);\n    set('--DataGrid-topContainerHeight', `${dimensionsState.topContainerHeight}px`);\n    set('--DataGrid-bottomContainerHeight', `${dimensionsState.bottomContainerHeight}px`);\n    set('--height', `${dimensionsState.rowHeight}px`);\n  }, [root, dimensionsState]);\n  const isFirstSizing = React.useRef(true);\n  const handleResize = React.useCallback(size => {\n    rootDimensionsRef.current = size;\n\n    // jsdom has no layout capabilities\n    const isJSDOM = /jsdom/.test(window.navigator.userAgent);\n    if (size.height === 0 && !errorShown.current && !props.autoHeight && !isJSDOM) {\n      logger.error(['The parent DOM element of the Data Grid has an empty height.', 'Please make sure that this element has an intrinsic height.', 'The grid displays with a height of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (size.width === 0 && !errorShown.current && !isJSDOM) {\n      logger.error(['The parent DOM element of the Data Grid has an empty width.', 'Please make sure that this element has an intrinsic width.', 'The grid displays with a width of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (isFirstSizing.current) {\n      // We want to initialize the grid dimensions as soon as possible to avoid flickering\n      setSavedSize(size);\n      isFirstSizing.current = false;\n      return;\n    }\n    debouncedSetSavedSize(size);\n  }, [props.autoHeight, debouncedSetSavedSize, logger]);\n  useEnhancedEffect(updateDimensions, [updateDimensions]);\n  useGridApiOptionHandler(apiRef, 'sortedRowsSet', updateDimensions);\n  useGridApiOptionHandler(apiRef, 'paginationModelChange', updateDimensions);\n  useGridApiOptionHandler(apiRef, 'columnsChange', updateDimensions);\n  useGridApiEventHandler(apiRef, 'resize', handleResize);\n  useGridApiOptionHandler(apiRef, 'debouncedResize', props.onResize);\n}\nfunction measureScrollbarSize(rootElement, columnsTotalWidth, scrollbarSize) {\n  if (scrollbarSize !== undefined) {\n    return scrollbarSize;\n  }\n  if (rootElement === null || columnsTotalWidth === 0) {\n    return 0;\n  }\n  const doc = ownerDocument(rootElement);\n  const scrollDiv = doc.createElement('div');\n  scrollDiv.style.width = '99px';\n  scrollDiv.style.height = '99px';\n  scrollDiv.style.position = 'absolute';\n  scrollDiv.style.overflow = 'scroll';\n  scrollDiv.className = 'scrollDiv';\n  rootElement.appendChild(scrollDiv);\n  const size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n  rootElement.removeChild(scrollDiv);\n  return size;\n}\n\n// Get rid of floating point imprecision errors\n// https://github.com/mui/mui-x/issues/9550#issuecomment-1619020477\nfunction roundToDecimalPlaces(value, decimals) {\n  return Math.round(value * 10 ** decimals) / 10 ** decimals;\n}\nfunction areElementSizesEqual(a, b) {\n  return a.width === b.width && a.height === b.height;\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_ownerDocument", "ownerDocument", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_useEventCallback", "useEventCallback", "unstable_ownerW<PERSON>ow", "ownerWindow", "throttle", "useGridApiEventHandler", "useGridApiOptionHandler", "useGridApiMethod", "useGridLogger", "gridColumnsTotalWidthSelector", "gridVisiblePinnedColumnDefinitionsSelector", "gridDimensionsSelector", "gridDensityFactorSelector", "gridRenderContextSelector", "useGridSelector", "getVisibleRows", "gridRowsMetaSelector", "calculatePinnedRowsHeight", "getTotalHeaderHeight", "EMPTY_SIZE", "width", "height", "EMPTY_DIMENSIONS", "isReady", "root", "viewportOuterSize", "viewportInnerSize", "contentSize", "minimumSize", "hasScrollX", "hasScrollY", "scrollbarSize", "headerHeight", "groupHeaderHeight", "headerFilterHeight", "row<PERSON>id<PERSON>", "rowHeight", "columnsTotalWidth", "leftPinnedWidth", "right<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headersTotalHeight", "topContainerHeight", "bottomContainerHeight", "dimensionsStateInitializer", "state", "dimensions", "useGridDimensions", "apiRef", "props", "logger", "errorShown", "useRef", "rootDimensionsRef", "dimensionsState", "rowsMeta", "pinnedColumns", "densityFactor", "Math", "floor", "columnHeaderHeight", "columnGroupHeaderHeight", "roundToDecimalPlaces", "left", "reduce", "w", "col", "computedWidth", "right", "savedSize", "setSavedSize", "useState", "debouncedSetSavedSize", "useMemo", "resizeThrottleMs", "previousSize", "getRootDimensions", "current", "setDimensions", "setState", "resize", "useCallback", "element", "mainElementRef", "computedStyle", "getComputedStyle", "newSize", "parseFloat", "areElementSizesEqual", "publishEvent", "getViewportPageSize", "currentPage", "pagination", "paginationMode", "getRowHeight", "renderContext", "viewportPageSize", "lastRowIndex", "firstRowIndex", "min", "rows", "length", "maximumPageSizeWithoutScrollBar", "updateDimensions", "rootElement", "rootElementRef", "pinnedRowsHeight", "measureScrollbarSize", "top", "bottom", "nonPinnedColumnsTotalWidth", "currentPageTotalHeight", "autoHeight", "round", "max", "content", "container", "hasScrollXIfNoYScrollBar", "hasScrollYIfNoXScrollBar", "newDimensions", "prevDimensions", "updateRenderContext", "apiPublic", "apiPrivate", "set", "k", "v", "style", "setProperty", "Number", "isFirstSizing", "handleResize", "size", "isJSDOM", "test", "window", "navigator", "userAgent", "error", "join", "onResize", "undefined", "doc", "scrollDiv", "createElement", "position", "overflow", "className", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "value", "decimals", "a", "b"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/dimensions/useGridDimensions.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_ownerWindow as ownerWindow } from '@mui/utils';\nimport { throttle } from '@mui/x-internals/throttle';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnsTotalWidthSelector, gridVisiblePinnedColumnDefinitionsSelector } from \"../columns/index.js\";\nimport { gridDimensionsSelector } from \"./gridDimensionsSelectors.js\";\nimport { gridDensityFactorSelector } from \"../density/index.js\";\nimport { gridRenderContextSelector } from \"../virtualization/index.js\";\nimport { useGridSelector } from \"../../utils/index.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { calculatePinnedRowsHeight } from \"../rows/gridRowsUtils.js\";\nimport { getTotalHeaderHeight } from \"../columns/gridColumnsUtils.js\";\nconst EMPTY_SIZE = {\n  width: 0,\n  height: 0\n};\nconst EMPTY_DIMENSIONS = {\n  isReady: false,\n  root: EMPTY_SIZE,\n  viewportOuterSize: EMPTY_SIZE,\n  viewportInnerSize: EMPTY_SIZE,\n  contentSize: EMPTY_SIZE,\n  minimumSize: EMPTY_SIZE,\n  hasScrollX: false,\n  hasScrollY: false,\n  scrollbarSize: 0,\n  headerHeight: 0,\n  groupHeaderHeight: 0,\n  headerFilterHeight: 0,\n  rowWidth: 0,\n  rowHeight: 0,\n  columnsTotalWidth: 0,\n  leftPinnedWidth: 0,\n  rightPinnedWidth: 0,\n  headersTotalHeight: 0,\n  topContainerHeight: 0,\n  bottomContainerHeight: 0\n};\nexport const dimensionsStateInitializer = state => {\n  const dimensions = EMPTY_DIMENSIONS;\n  return _extends({}, state, {\n    dimensions\n  });\n};\nexport function useGridDimensions(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useResizeContainer');\n  const errorShown = React.useRef(false);\n  const rootDimensionsRef = React.useRef(EMPTY_SIZE);\n  const dimensionsState = useGridSelector(apiRef, gridDimensionsSelector);\n  const rowsMeta = useGridSelector(apiRef, gridRowsMetaSelector);\n  const pinnedColumns = useGridSelector(apiRef, gridVisiblePinnedColumnDefinitionsSelector);\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const rowHeight = Math.floor(props.rowHeight * densityFactor);\n  const headerHeight = Math.floor(props.columnHeaderHeight * densityFactor);\n  const groupHeaderHeight = Math.floor((props.columnGroupHeaderHeight ?? props.columnHeaderHeight) * densityFactor);\n  const headerFilterHeight = Math.floor((props.headerFilterHeight ?? props.columnHeaderHeight) * densityFactor);\n  const columnsTotalWidth = roundToDecimalPlaces(gridColumnsTotalWidthSelector(apiRef), 6);\n  const headersTotalHeight = getTotalHeaderHeight(apiRef, props);\n  const leftPinnedWidth = pinnedColumns.left.reduce((w, col) => w + col.computedWidth, 0);\n  const rightPinnedWidth = pinnedColumns.right.reduce((w, col) => w + col.computedWidth, 0);\n  const [savedSize, setSavedSize] = React.useState();\n  const debouncedSetSavedSize = React.useMemo(() => throttle(setSavedSize, props.resizeThrottleMs), [props.resizeThrottleMs]);\n  const previousSize = React.useRef();\n  const getRootDimensions = () => apiRef.current.state.dimensions;\n  const setDimensions = useEventCallback(dimensions => {\n    apiRef.current.setState(state => _extends({}, state, {\n      dimensions\n    }));\n  });\n  const resize = React.useCallback(() => {\n    const element = apiRef.current.mainElementRef.current;\n    if (!element) {\n      return;\n    }\n    const computedStyle = ownerWindow(element).getComputedStyle(element);\n    const newSize = {\n      width: parseFloat(computedStyle.width) || 0,\n      height: parseFloat(computedStyle.height) || 0\n    };\n    if (!previousSize.current || !areElementSizesEqual(previousSize.current, newSize)) {\n      apiRef.current.publishEvent('resize', newSize);\n      previousSize.current = newSize;\n    }\n  }, [apiRef]);\n  const getViewportPageSize = React.useCallback(() => {\n    const dimensions = gridDimensionsSelector(apiRef.current.state);\n    if (!dimensions.isReady) {\n      return 0;\n    }\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n\n    // TODO: Use a combination of scrollTop, dimensions.viewportInnerSize.height and rowsMeta.possitions\n    // to find out the maximum number of rows that can fit in the visible part of the grid\n    if (props.getRowHeight) {\n      const renderContext = gridRenderContextSelector(apiRef);\n      const viewportPageSize = renderContext.lastRowIndex - renderContext.firstRowIndex;\n      return Math.min(viewportPageSize - 1, currentPage.rows.length);\n    }\n    const maximumPageSizeWithoutScrollBar = Math.floor(dimensions.viewportInnerSize.height / rowHeight);\n    return Math.min(maximumPageSizeWithoutScrollBar, currentPage.rows.length);\n  }, [apiRef, props.pagination, props.paginationMode, props.getRowHeight, rowHeight]);\n  const updateDimensions = React.useCallback(() => {\n    const rootElement = apiRef.current.rootElementRef.current;\n    const pinnedRowsHeight = calculatePinnedRowsHeight(apiRef);\n    const scrollbarSize = measureScrollbarSize(rootElement, columnsTotalWidth, props.scrollbarSize);\n    const topContainerHeight = headersTotalHeight + pinnedRowsHeight.top;\n    const bottomContainerHeight = pinnedRowsHeight.bottom;\n    const nonPinnedColumnsTotalWidth = columnsTotalWidth - leftPinnedWidth - rightPinnedWidth;\n    const contentSize = {\n      width: nonPinnedColumnsTotalWidth,\n      height: rowsMeta.currentPageTotalHeight\n    };\n    let viewportOuterSize;\n    let viewportInnerSize;\n    let hasScrollX = false;\n    let hasScrollY = false;\n    if (props.autoHeight) {\n      hasScrollY = false;\n      hasScrollX = Math.round(columnsTotalWidth) > Math.round(rootDimensionsRef.current.width);\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: topContainerHeight + bottomContainerHeight + contentSize.height\n      };\n      viewportInnerSize = {\n        width: Math.max(0, viewportOuterSize.width - (hasScrollY ? scrollbarSize : 0)),\n        height: Math.max(0, viewportOuterSize.height - (hasScrollX ? scrollbarSize : 0))\n      };\n    } else {\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: rootDimensionsRef.current.height\n      };\n      viewportInnerSize = {\n        width: Math.max(0, viewportOuterSize.width - leftPinnedWidth - rightPinnedWidth),\n        height: Math.max(0, viewportOuterSize.height - topContainerHeight - bottomContainerHeight)\n      };\n      const content = contentSize;\n      const container = viewportInnerSize;\n      const hasScrollXIfNoYScrollBar = content.width > container.width;\n      const hasScrollYIfNoXScrollBar = content.height > container.height;\n      if (hasScrollXIfNoYScrollBar || hasScrollYIfNoXScrollBar) {\n        hasScrollY = hasScrollYIfNoXScrollBar;\n        hasScrollX = content.width + (hasScrollY ? scrollbarSize : 0) > container.width;\n\n        // We recalculate the scroll y to consider the size of the x scrollbar.\n        if (hasScrollX) {\n          hasScrollY = content.height + scrollbarSize > container.height;\n        }\n      }\n      if (hasScrollY) {\n        viewportInnerSize.width -= scrollbarSize;\n      }\n      if (hasScrollX) {\n        viewportInnerSize.height -= scrollbarSize;\n      }\n    }\n    const rowWidth = Math.max(viewportOuterSize.width, columnsTotalWidth + (hasScrollY ? scrollbarSize : 0));\n    const minimumSize = {\n      width: columnsTotalWidth,\n      height: topContainerHeight + contentSize.height + bottomContainerHeight\n    };\n    const newDimensions = {\n      isReady: true,\n      root: rootDimensionsRef.current,\n      viewportOuterSize,\n      viewportInnerSize,\n      contentSize,\n      minimumSize,\n      hasScrollX,\n      hasScrollY,\n      scrollbarSize,\n      headerHeight,\n      groupHeaderHeight,\n      headerFilterHeight,\n      rowWidth,\n      rowHeight,\n      columnsTotalWidth,\n      leftPinnedWidth,\n      rightPinnedWidth,\n      headersTotalHeight,\n      topContainerHeight,\n      bottomContainerHeight\n    };\n    const prevDimensions = apiRef.current.state.dimensions;\n    setDimensions(newDimensions);\n    if (!areElementSizesEqual(newDimensions.viewportInnerSize, prevDimensions.viewportInnerSize)) {\n      apiRef.current.publishEvent('viewportInnerSizeChange', newDimensions.viewportInnerSize);\n    }\n    apiRef.current.updateRenderContext?.();\n  }, [apiRef, setDimensions, props.scrollbarSize, props.autoHeight, rowsMeta.currentPageTotalHeight, rowHeight, headerHeight, groupHeaderHeight, headerFilterHeight, columnsTotalWidth, headersTotalHeight, leftPinnedWidth, rightPinnedWidth]);\n  const apiPublic = {\n    resize,\n    getRootDimensions\n  };\n  const apiPrivate = {\n    updateDimensions,\n    getViewportPageSize\n  };\n  useGridApiMethod(apiRef, apiPublic, 'public');\n  useGridApiMethod(apiRef, apiPrivate, 'private');\n  useEnhancedEffect(() => {\n    if (savedSize) {\n      updateDimensions();\n      apiRef.current.publishEvent('debouncedResize', rootDimensionsRef.current);\n    }\n  }, [apiRef, savedSize, updateDimensions]);\n  const root = apiRef.current.rootElementRef.current;\n  useEnhancedEffect(() => {\n    if (!root) {\n      return;\n    }\n    const set = (k, v) => root.style.setProperty(k, v);\n    set('--DataGrid-width', `${dimensionsState.viewportOuterSize.width}px`);\n    set('--DataGrid-hasScrollX', `${Number(dimensionsState.hasScrollX)}`);\n    set('--DataGrid-hasScrollY', `${Number(dimensionsState.hasScrollY)}`);\n    set('--DataGrid-scrollbarSize', `${dimensionsState.scrollbarSize}px`);\n    set('--DataGrid-rowWidth', `${dimensionsState.rowWidth}px`);\n    set('--DataGrid-columnsTotalWidth', `${dimensionsState.columnsTotalWidth}px`);\n    set('--DataGrid-leftPinnedWidth', `${dimensionsState.leftPinnedWidth}px`);\n    set('--DataGrid-rightPinnedWidth', `${dimensionsState.rightPinnedWidth}px`);\n    set('--DataGrid-headerHeight', `${dimensionsState.headerHeight}px`);\n    set('--DataGrid-headersTotalHeight', `${dimensionsState.headersTotalHeight}px`);\n    set('--DataGrid-topContainerHeight', `${dimensionsState.topContainerHeight}px`);\n    set('--DataGrid-bottomContainerHeight', `${dimensionsState.bottomContainerHeight}px`);\n    set('--height', `${dimensionsState.rowHeight}px`);\n  }, [root, dimensionsState]);\n  const isFirstSizing = React.useRef(true);\n  const handleResize = React.useCallback(size => {\n    rootDimensionsRef.current = size;\n\n    // jsdom has no layout capabilities\n    const isJSDOM = /jsdom/.test(window.navigator.userAgent);\n    if (size.height === 0 && !errorShown.current && !props.autoHeight && !isJSDOM) {\n      logger.error(['The parent DOM element of the Data Grid has an empty height.', 'Please make sure that this element has an intrinsic height.', 'The grid displays with a height of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (size.width === 0 && !errorShown.current && !isJSDOM) {\n      logger.error(['The parent DOM element of the Data Grid has an empty width.', 'Please make sure that this element has an intrinsic width.', 'The grid displays with a width of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (isFirstSizing.current) {\n      // We want to initialize the grid dimensions as soon as possible to avoid flickering\n      setSavedSize(size);\n      isFirstSizing.current = false;\n      return;\n    }\n    debouncedSetSavedSize(size);\n  }, [props.autoHeight, debouncedSetSavedSize, logger]);\n  useEnhancedEffect(updateDimensions, [updateDimensions]);\n  useGridApiOptionHandler(apiRef, 'sortedRowsSet', updateDimensions);\n  useGridApiOptionHandler(apiRef, 'paginationModelChange', updateDimensions);\n  useGridApiOptionHandler(apiRef, 'columnsChange', updateDimensions);\n  useGridApiEventHandler(apiRef, 'resize', handleResize);\n  useGridApiOptionHandler(apiRef, 'debouncedResize', props.onResize);\n}\nfunction measureScrollbarSize(rootElement, columnsTotalWidth, scrollbarSize) {\n  if (scrollbarSize !== undefined) {\n    return scrollbarSize;\n  }\n  if (rootElement === null || columnsTotalWidth === 0) {\n    return 0;\n  }\n  const doc = ownerDocument(rootElement);\n  const scrollDiv = doc.createElement('div');\n  scrollDiv.style.width = '99px';\n  scrollDiv.style.height = '99px';\n  scrollDiv.style.position = 'absolute';\n  scrollDiv.style.overflow = 'scroll';\n  scrollDiv.className = 'scrollDiv';\n  rootElement.appendChild(scrollDiv);\n  const size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n  rootElement.removeChild(scrollDiv);\n  return size;\n}\n\n// Get rid of floating point imprecision errors\n// https://github.com/mui/mui-x/issues/9550#issuecomment-1619020477\nfunction roundToDecimalPlaces(value, decimals) {\n  return Math.round(value * 10 ** decimals) / 10 ** decimals;\n}\nfunction areElementSizesEqual(a, b) {\n  return a.width === b.width && a.height === b.height;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,oBAAoB,IAAIC,WAAW,QAAQ,YAAY;AACzM,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,uCAAuC;AACvG,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,6BAA6B,EAAEC,0CAA0C,QAAQ,qBAAqB;AAC/G,SAASC,sBAAsB,QAAQ,8BAA8B;AACrE,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,yBAAyB,QAAQ,0BAA0B;AACpE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,MAAMC,UAAU,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC;AACD,MAAMC,gBAAgB,GAAG;EACvBC,OAAO,EAAE,KAAK;EACdC,IAAI,EAAEL,UAAU;EAChBM,iBAAiB,EAAEN,UAAU;EAC7BO,iBAAiB,EAAEP,UAAU;EAC7BQ,WAAW,EAAER,UAAU;EACvBS,WAAW,EAAET,UAAU;EACvBU,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,iBAAiB,EAAE,CAAC;EACpBC,kBAAkB,EAAE,CAAC;EACrBC,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,CAAC;EACZC,iBAAiB,EAAE,CAAC;EACpBC,eAAe,EAAE,CAAC;EAClBC,gBAAgB,EAAE,CAAC;EACnBC,kBAAkB,EAAE,CAAC;EACrBC,kBAAkB,EAAE,CAAC;EACrBC,qBAAqB,EAAE;AACzB,CAAC;AACD,OAAO,MAAMC,0BAA0B,GAAGC,KAAK,IAAI;EACjD,MAAMC,UAAU,GAAGvB,gBAAgB;EACnC,OAAO5B,QAAQ,CAAC,CAAC,CAAC,EAAEkD,KAAK,EAAE;IACzBC;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC/C,MAAMC,MAAM,GAAGzC,aAAa,CAACuC,MAAM,EAAE,oBAAoB,CAAC;EAC1D,MAAMG,UAAU,GAAGvD,KAAK,CAACwD,MAAM,CAAC,KAAK,CAAC;EACtC,MAAMC,iBAAiB,GAAGzD,KAAK,CAACwD,MAAM,CAAChC,UAAU,CAAC;EAClD,MAAMkC,eAAe,GAAGvC,eAAe,CAACiC,MAAM,EAAEpC,sBAAsB,CAAC;EACvE,MAAM2C,QAAQ,GAAGxC,eAAe,CAACiC,MAAM,EAAE/B,oBAAoB,CAAC;EAC9D,MAAMuC,aAAa,GAAGzC,eAAe,CAACiC,MAAM,EAAErC,0CAA0C,CAAC;EACzF,MAAM8C,aAAa,GAAG1C,eAAe,CAACiC,MAAM,EAAEnC,yBAAyB,CAAC;EACxE,MAAMwB,SAAS,GAAGqB,IAAI,CAACC,KAAK,CAACV,KAAK,CAACZ,SAAS,GAAGoB,aAAa,CAAC;EAC7D,MAAMxB,YAAY,GAAGyB,IAAI,CAACC,KAAK,CAACV,KAAK,CAACW,kBAAkB,GAAGH,aAAa,CAAC;EACzE,MAAMvB,iBAAiB,GAAGwB,IAAI,CAACC,KAAK,CAAC,CAACV,KAAK,CAACY,uBAAuB,IAAIZ,KAAK,CAACW,kBAAkB,IAAIH,aAAa,CAAC;EACjH,MAAMtB,kBAAkB,GAAGuB,IAAI,CAACC,KAAK,CAAC,CAACV,KAAK,CAACd,kBAAkB,IAAIc,KAAK,CAACW,kBAAkB,IAAIH,aAAa,CAAC;EAC7G,MAAMnB,iBAAiB,GAAGwB,oBAAoB,CAACpD,6BAA6B,CAACsC,MAAM,CAAC,EAAE,CAAC,CAAC;EACxF,MAAMP,kBAAkB,GAAGtB,oBAAoB,CAAC6B,MAAM,EAAEC,KAAK,CAAC;EAC9D,MAAMV,eAAe,GAAGiB,aAAa,CAACO,IAAI,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKD,CAAC,GAAGC,GAAG,CAACC,aAAa,EAAE,CAAC,CAAC;EACvF,MAAM3B,gBAAgB,GAAGgB,aAAa,CAACY,KAAK,CAACJ,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKD,CAAC,GAAGC,GAAG,CAACC,aAAa,EAAE,CAAC,CAAC;EACzF,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,KAAK,CAAC2E,QAAQ,CAAC,CAAC;EAClD,MAAMC,qBAAqB,GAAG5E,KAAK,CAAC6E,OAAO,CAAC,MAAMpE,QAAQ,CAACiE,YAAY,EAAErB,KAAK,CAACyB,gBAAgB,CAAC,EAAE,CAACzB,KAAK,CAACyB,gBAAgB,CAAC,CAAC;EAC3H,MAAMC,YAAY,GAAG/E,KAAK,CAACwD,MAAM,CAAC,CAAC;EACnC,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM5B,MAAM,CAAC6B,OAAO,CAAChC,KAAK,CAACC,UAAU;EAC/D,MAAMgC,aAAa,GAAG5E,gBAAgB,CAAC4C,UAAU,IAAI;IACnDE,MAAM,CAAC6B,OAAO,CAACE,QAAQ,CAAClC,KAAK,IAAIlD,QAAQ,CAAC,CAAC,CAAC,EAAEkD,KAAK,EAAE;MACnDC;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,MAAMkC,MAAM,GAAGpF,KAAK,CAACqF,WAAW,CAAC,MAAM;IACrC,MAAMC,OAAO,GAAGlC,MAAM,CAAC6B,OAAO,CAACM,cAAc,CAACN,OAAO;IACrD,IAAI,CAACK,OAAO,EAAE;MACZ;IACF;IACA,MAAME,aAAa,GAAGhF,WAAW,CAAC8E,OAAO,CAAC,CAACG,gBAAgB,CAACH,OAAO,CAAC;IACpE,MAAMI,OAAO,GAAG;MACdjE,KAAK,EAAEkE,UAAU,CAACH,aAAa,CAAC/D,KAAK,CAAC,IAAI,CAAC;MAC3CC,MAAM,EAAEiE,UAAU,CAACH,aAAa,CAAC9D,MAAM,CAAC,IAAI;IAC9C,CAAC;IACD,IAAI,CAACqD,YAAY,CAACE,OAAO,IAAI,CAACW,oBAAoB,CAACb,YAAY,CAACE,OAAO,EAAES,OAAO,CAAC,EAAE;MACjFtC,MAAM,CAAC6B,OAAO,CAACY,YAAY,CAAC,QAAQ,EAAEH,OAAO,CAAC;MAC9CX,YAAY,CAACE,OAAO,GAAGS,OAAO;IAChC;EACF,CAAC,EAAE,CAACtC,MAAM,CAAC,CAAC;EACZ,MAAM0C,mBAAmB,GAAG9F,KAAK,CAACqF,WAAW,CAAC,MAAM;IAClD,MAAMnC,UAAU,GAAGlC,sBAAsB,CAACoC,MAAM,CAAC6B,OAAO,CAAChC,KAAK,CAAC;IAC/D,IAAI,CAACC,UAAU,CAACtB,OAAO,EAAE;MACvB,OAAO,CAAC;IACV;IACA,MAAMmE,WAAW,GAAG3E,cAAc,CAACgC,MAAM,EAAE;MACzC4C,UAAU,EAAE3C,KAAK,CAAC2C,UAAU;MAC5BC,cAAc,EAAE5C,KAAK,CAAC4C;IACxB,CAAC,CAAC;;IAEF;IACA;IACA,IAAI5C,KAAK,CAAC6C,YAAY,EAAE;MACtB,MAAMC,aAAa,GAAGjF,yBAAyB,CAACkC,MAAM,CAAC;MACvD,MAAMgD,gBAAgB,GAAGD,aAAa,CAACE,YAAY,GAAGF,aAAa,CAACG,aAAa;MACjF,OAAOxC,IAAI,CAACyC,GAAG,CAACH,gBAAgB,GAAG,CAAC,EAAEL,WAAW,CAACS,IAAI,CAACC,MAAM,CAAC;IAChE;IACA,MAAMC,+BAA+B,GAAG5C,IAAI,CAACC,KAAK,CAACb,UAAU,CAACnB,iBAAiB,CAACL,MAAM,GAAGe,SAAS,CAAC;IACnG,OAAOqB,IAAI,CAACyC,GAAG,CAACG,+BAA+B,EAAEX,WAAW,CAACS,IAAI,CAACC,MAAM,CAAC;EAC3E,CAAC,EAAE,CAACrD,MAAM,EAAEC,KAAK,CAAC2C,UAAU,EAAE3C,KAAK,CAAC4C,cAAc,EAAE5C,KAAK,CAAC6C,YAAY,EAAEzD,SAAS,CAAC,CAAC;EACnF,MAAMkE,gBAAgB,GAAG3G,KAAK,CAACqF,WAAW,CAAC,MAAM;IAC/C,MAAMuB,WAAW,GAAGxD,MAAM,CAAC6B,OAAO,CAAC4B,cAAc,CAAC5B,OAAO;IACzD,MAAM6B,gBAAgB,GAAGxF,yBAAyB,CAAC8B,MAAM,CAAC;IAC1D,MAAMhB,aAAa,GAAG2E,oBAAoB,CAACH,WAAW,EAAElE,iBAAiB,EAAEW,KAAK,CAACjB,aAAa,CAAC;IAC/F,MAAMU,kBAAkB,GAAGD,kBAAkB,GAAGiE,gBAAgB,CAACE,GAAG;IACpE,MAAMjE,qBAAqB,GAAG+D,gBAAgB,CAACG,MAAM;IACrD,MAAMC,0BAA0B,GAAGxE,iBAAiB,GAAGC,eAAe,GAAGC,gBAAgB;IACzF,MAAMZ,WAAW,GAAG;MAClBP,KAAK,EAAEyF,0BAA0B;MACjCxF,MAAM,EAAEiC,QAAQ,CAACwD;IACnB,CAAC;IACD,IAAIrF,iBAAiB;IACrB,IAAIC,iBAAiB;IACrB,IAAIG,UAAU,GAAG,KAAK;IACtB,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIkB,KAAK,CAAC+D,UAAU,EAAE;MACpBjF,UAAU,GAAG,KAAK;MAClBD,UAAU,GAAG4B,IAAI,CAACuD,KAAK,CAAC3E,iBAAiB,CAAC,GAAGoB,IAAI,CAACuD,KAAK,CAAC5D,iBAAiB,CAACwB,OAAO,CAACxD,KAAK,CAAC;MACxFK,iBAAiB,GAAG;QAClBL,KAAK,EAAEgC,iBAAiB,CAACwB,OAAO,CAACxD,KAAK;QACtCC,MAAM,EAAEoB,kBAAkB,GAAGC,qBAAqB,GAAGf,WAAW,CAACN;MACnE,CAAC;MACDK,iBAAiB,GAAG;QAClBN,KAAK,EAAEqC,IAAI,CAACwD,GAAG,CAAC,CAAC,EAAExF,iBAAiB,CAACL,KAAK,IAAIU,UAAU,GAAGC,aAAa,GAAG,CAAC,CAAC,CAAC;QAC9EV,MAAM,EAAEoC,IAAI,CAACwD,GAAG,CAAC,CAAC,EAAExF,iBAAiB,CAACJ,MAAM,IAAIQ,UAAU,GAAGE,aAAa,GAAG,CAAC,CAAC;MACjF,CAAC;IACH,CAAC,MAAM;MACLN,iBAAiB,GAAG;QAClBL,KAAK,EAAEgC,iBAAiB,CAACwB,OAAO,CAACxD,KAAK;QACtCC,MAAM,EAAE+B,iBAAiB,CAACwB,OAAO,CAACvD;MACpC,CAAC;MACDK,iBAAiB,GAAG;QAClBN,KAAK,EAAEqC,IAAI,CAACwD,GAAG,CAAC,CAAC,EAAExF,iBAAiB,CAACL,KAAK,GAAGkB,eAAe,GAAGC,gBAAgB,CAAC;QAChFlB,MAAM,EAAEoC,IAAI,CAACwD,GAAG,CAAC,CAAC,EAAExF,iBAAiB,CAACJ,MAAM,GAAGoB,kBAAkB,GAAGC,qBAAqB;MAC3F,CAAC;MACD,MAAMwE,OAAO,GAAGvF,WAAW;MAC3B,MAAMwF,SAAS,GAAGzF,iBAAiB;MACnC,MAAM0F,wBAAwB,GAAGF,OAAO,CAAC9F,KAAK,GAAG+F,SAAS,CAAC/F,KAAK;MAChE,MAAMiG,wBAAwB,GAAGH,OAAO,CAAC7F,MAAM,GAAG8F,SAAS,CAAC9F,MAAM;MAClE,IAAI+F,wBAAwB,IAAIC,wBAAwB,EAAE;QACxDvF,UAAU,GAAGuF,wBAAwB;QACrCxF,UAAU,GAAGqF,OAAO,CAAC9F,KAAK,IAAIU,UAAU,GAAGC,aAAa,GAAG,CAAC,CAAC,GAAGoF,SAAS,CAAC/F,KAAK;;QAE/E;QACA,IAAIS,UAAU,EAAE;UACdC,UAAU,GAAGoF,OAAO,CAAC7F,MAAM,GAAGU,aAAa,GAAGoF,SAAS,CAAC9F,MAAM;QAChE;MACF;MACA,IAAIS,UAAU,EAAE;QACdJ,iBAAiB,CAACN,KAAK,IAAIW,aAAa;MAC1C;MACA,IAAIF,UAAU,EAAE;QACdH,iBAAiB,CAACL,MAAM,IAAIU,aAAa;MAC3C;IACF;IACA,MAAMI,QAAQ,GAAGsB,IAAI,CAACwD,GAAG,CAACxF,iBAAiB,CAACL,KAAK,EAAEiB,iBAAiB,IAAIP,UAAU,GAAGC,aAAa,GAAG,CAAC,CAAC,CAAC;IACxG,MAAMH,WAAW,GAAG;MAClBR,KAAK,EAAEiB,iBAAiB;MACxBhB,MAAM,EAAEoB,kBAAkB,GAAGd,WAAW,CAACN,MAAM,GAAGqB;IACpD,CAAC;IACD,MAAM4E,aAAa,GAAG;MACpB/F,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE4B,iBAAiB,CAACwB,OAAO;MAC/BnD,iBAAiB;MACjBC,iBAAiB;MACjBC,WAAW;MACXC,WAAW;MACXC,UAAU;MACVC,UAAU;MACVC,aAAa;MACbC,YAAY;MACZC,iBAAiB;MACjBC,kBAAkB;MAClBC,QAAQ;MACRC,SAAS;MACTC,iBAAiB;MACjBC,eAAe;MACfC,gBAAgB;MAChBC,kBAAkB;MAClBC,kBAAkB;MAClBC;IACF,CAAC;IACD,MAAM6E,cAAc,GAAGxE,MAAM,CAAC6B,OAAO,CAAChC,KAAK,CAACC,UAAU;IACtDgC,aAAa,CAACyC,aAAa,CAAC;IAC5B,IAAI,CAAC/B,oBAAoB,CAAC+B,aAAa,CAAC5F,iBAAiB,EAAE6F,cAAc,CAAC7F,iBAAiB,CAAC,EAAE;MAC5FqB,MAAM,CAAC6B,OAAO,CAACY,YAAY,CAAC,yBAAyB,EAAE8B,aAAa,CAAC5F,iBAAiB,CAAC;IACzF;IACAqB,MAAM,CAAC6B,OAAO,CAAC4C,mBAAmB,GAAG,CAAC;EACxC,CAAC,EAAE,CAACzE,MAAM,EAAE8B,aAAa,EAAE7B,KAAK,CAACjB,aAAa,EAAEiB,KAAK,CAAC+D,UAAU,EAAEzD,QAAQ,CAACwD,sBAAsB,EAAE1E,SAAS,EAAEJ,YAAY,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEG,iBAAiB,EAAEG,kBAAkB,EAAEF,eAAe,EAAEC,gBAAgB,CAAC,CAAC;EAC7O,MAAMkF,SAAS,GAAG;IAChB1C,MAAM;IACNJ;EACF,CAAC;EACD,MAAM+C,UAAU,GAAG;IACjBpB,gBAAgB;IAChBb;EACF,CAAC;EACDlF,gBAAgB,CAACwC,MAAM,EAAE0E,SAAS,EAAE,QAAQ,CAAC;EAC7ClH,gBAAgB,CAACwC,MAAM,EAAE2E,UAAU,EAAE,SAAS,CAAC;EAC/C3H,iBAAiB,CAAC,MAAM;IACtB,IAAIqE,SAAS,EAAE;MACbkC,gBAAgB,CAAC,CAAC;MAClBvD,MAAM,CAAC6B,OAAO,CAACY,YAAY,CAAC,iBAAiB,EAAEpC,iBAAiB,CAACwB,OAAO,CAAC;IAC3E;EACF,CAAC,EAAE,CAAC7B,MAAM,EAAEqB,SAAS,EAAEkC,gBAAgB,CAAC,CAAC;EACzC,MAAM9E,IAAI,GAAGuB,MAAM,CAAC6B,OAAO,CAAC4B,cAAc,CAAC5B,OAAO;EAClD7E,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAACyB,IAAI,EAAE;MACT;IACF;IACA,MAAMmG,GAAG,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKrG,IAAI,CAACsG,KAAK,CAACC,WAAW,CAACH,CAAC,EAAEC,CAAC,CAAC;IAClDF,GAAG,CAAC,kBAAkB,EAAE,GAAGtE,eAAe,CAAC5B,iBAAiB,CAACL,KAAK,IAAI,CAAC;IACvEuG,GAAG,CAAC,uBAAuB,EAAE,GAAGK,MAAM,CAAC3E,eAAe,CAACxB,UAAU,CAAC,EAAE,CAAC;IACrE8F,GAAG,CAAC,uBAAuB,EAAE,GAAGK,MAAM,CAAC3E,eAAe,CAACvB,UAAU,CAAC,EAAE,CAAC;IACrE6F,GAAG,CAAC,0BAA0B,EAAE,GAAGtE,eAAe,CAACtB,aAAa,IAAI,CAAC;IACrE4F,GAAG,CAAC,qBAAqB,EAAE,GAAGtE,eAAe,CAAClB,QAAQ,IAAI,CAAC;IAC3DwF,GAAG,CAAC,8BAA8B,EAAE,GAAGtE,eAAe,CAAChB,iBAAiB,IAAI,CAAC;IAC7EsF,GAAG,CAAC,4BAA4B,EAAE,GAAGtE,eAAe,CAACf,eAAe,IAAI,CAAC;IACzEqF,GAAG,CAAC,6BAA6B,EAAE,GAAGtE,eAAe,CAACd,gBAAgB,IAAI,CAAC;IAC3EoF,GAAG,CAAC,yBAAyB,EAAE,GAAGtE,eAAe,CAACrB,YAAY,IAAI,CAAC;IACnE2F,GAAG,CAAC,+BAA+B,EAAE,GAAGtE,eAAe,CAACb,kBAAkB,IAAI,CAAC;IAC/EmF,GAAG,CAAC,+BAA+B,EAAE,GAAGtE,eAAe,CAACZ,kBAAkB,IAAI,CAAC;IAC/EkF,GAAG,CAAC,kCAAkC,EAAE,GAAGtE,eAAe,CAACX,qBAAqB,IAAI,CAAC;IACrFiF,GAAG,CAAC,UAAU,EAAE,GAAGtE,eAAe,CAACjB,SAAS,IAAI,CAAC;EACnD,CAAC,EAAE,CAACZ,IAAI,EAAE6B,eAAe,CAAC,CAAC;EAC3B,MAAM4E,aAAa,GAAGtI,KAAK,CAACwD,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM+E,YAAY,GAAGvI,KAAK,CAACqF,WAAW,CAACmD,IAAI,IAAI;IAC7C/E,iBAAiB,CAACwB,OAAO,GAAGuD,IAAI;;IAEhC;IACA,MAAMC,OAAO,GAAG,OAAO,CAACC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,SAAS,CAAC;IACxD,IAAIL,IAAI,CAAC9G,MAAM,KAAK,CAAC,IAAI,CAAC6B,UAAU,CAAC0B,OAAO,IAAI,CAAC5B,KAAK,CAAC+D,UAAU,IAAI,CAACqB,OAAO,EAAE;MAC7EnF,MAAM,CAACwF,KAAK,CAAC,CAAC,8DAA8D,EAAE,6DAA6D,EAAE,yCAAyC,EAAE,EAAE,EAAE,4DAA4D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MACrQxF,UAAU,CAAC0B,OAAO,GAAG,IAAI;IAC3B;IACA,IAAIuD,IAAI,CAAC/G,KAAK,KAAK,CAAC,IAAI,CAAC8B,UAAU,CAAC0B,OAAO,IAAI,CAACwD,OAAO,EAAE;MACvDnF,MAAM,CAACwF,KAAK,CAAC,CAAC,6DAA6D,EAAE,4DAA4D,EAAE,wCAAwC,EAAE,EAAE,EAAE,4DAA4D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAClQxF,UAAU,CAAC0B,OAAO,GAAG,IAAI;IAC3B;IACA,IAAIqD,aAAa,CAACrD,OAAO,EAAE;MACzB;MACAP,YAAY,CAAC8D,IAAI,CAAC;MAClBF,aAAa,CAACrD,OAAO,GAAG,KAAK;MAC7B;IACF;IACAL,qBAAqB,CAAC4D,IAAI,CAAC;EAC7B,CAAC,EAAE,CAACnF,KAAK,CAAC+D,UAAU,EAAExC,qBAAqB,EAAEtB,MAAM,CAAC,CAAC;EACrDlD,iBAAiB,CAACuG,gBAAgB,EAAE,CAACA,gBAAgB,CAAC,CAAC;EACvDhG,uBAAuB,CAACyC,MAAM,EAAE,eAAe,EAAEuD,gBAAgB,CAAC;EAClEhG,uBAAuB,CAACyC,MAAM,EAAE,uBAAuB,EAAEuD,gBAAgB,CAAC;EAC1EhG,uBAAuB,CAACyC,MAAM,EAAE,eAAe,EAAEuD,gBAAgB,CAAC;EAClEjG,sBAAsB,CAAC0C,MAAM,EAAE,QAAQ,EAAEmF,YAAY,CAAC;EACtD5H,uBAAuB,CAACyC,MAAM,EAAE,iBAAiB,EAAEC,KAAK,CAAC2F,QAAQ,CAAC;AACpE;AACA,SAASjC,oBAAoBA,CAACH,WAAW,EAAElE,iBAAiB,EAAEN,aAAa,EAAE;EAC3E,IAAIA,aAAa,KAAK6G,SAAS,EAAE;IAC/B,OAAO7G,aAAa;EACtB;EACA,IAAIwE,WAAW,KAAK,IAAI,IAAIlE,iBAAiB,KAAK,CAAC,EAAE;IACnD,OAAO,CAAC;EACV;EACA,MAAMwG,GAAG,GAAGhJ,aAAa,CAAC0G,WAAW,CAAC;EACtC,MAAMuC,SAAS,GAAGD,GAAG,CAACE,aAAa,CAAC,KAAK,CAAC;EAC1CD,SAAS,CAAChB,KAAK,CAAC1G,KAAK,GAAG,MAAM;EAC9B0H,SAAS,CAAChB,KAAK,CAACzG,MAAM,GAAG,MAAM;EAC/ByH,SAAS,CAAChB,KAAK,CAACkB,QAAQ,GAAG,UAAU;EACrCF,SAAS,CAAChB,KAAK,CAACmB,QAAQ,GAAG,QAAQ;EACnCH,SAAS,CAACI,SAAS,GAAG,WAAW;EACjC3C,WAAW,CAAC4C,WAAW,CAACL,SAAS,CAAC;EAClC,MAAMX,IAAI,GAAGW,SAAS,CAACM,WAAW,GAAGN,SAAS,CAACO,WAAW;EAC1D9C,WAAW,CAAC+C,WAAW,CAACR,SAAS,CAAC;EAClC,OAAOX,IAAI;AACb;;AAEA;AACA;AACA,SAAStE,oBAAoBA,CAAC0F,KAAK,EAAEC,QAAQ,EAAE;EAC7C,OAAO/F,IAAI,CAACuD,KAAK,CAACuC,KAAK,GAAG,EAAE,IAAIC,QAAQ,CAAC,GAAG,EAAE,IAAIA,QAAQ;AAC5D;AACA,SAASjE,oBAAoBA,CAACkE,CAAC,EAAEC,CAAC,EAAE;EAClC,OAAOD,CAAC,CAACrI,KAAK,KAAKsI,CAAC,CAACtI,KAAK,IAAIqI,CAAC,CAACpI,MAAM,KAAKqI,CAAC,CAACrI,MAAM;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}