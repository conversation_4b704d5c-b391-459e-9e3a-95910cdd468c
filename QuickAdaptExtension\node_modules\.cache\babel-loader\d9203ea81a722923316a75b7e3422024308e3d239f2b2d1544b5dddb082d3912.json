{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel } from \"./gridPaginationUtils.js\";\nimport { useGridPaginationModel } from \"./useGridPaginationModel.js\";\nimport { useGridRowCount } from \"./useGridRowCount.js\";\nimport { useGridPaginationMeta } from \"./useGridPaginationMeta.js\";\nexport const paginationStateInitializer = (state, props) => {\n  const paginationModel = _extends({}, getDefaultGridPaginationModel(props.autoPageSize), props.paginationModel ?? props.initialState?.pagination?.paginationModel);\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, props.signature);\n  const rowCount = props.rowCount ?? props.initialState?.pagination?.rowCount;\n  const meta = props.paginationMeta ?? props.initialState?.pagination?.meta ?? {};\n  return _extends({}, state, {\n    pagination: {\n      paginationModel,\n      rowCount,\n      meta\n    }\n  });\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPagination = (apiRef, props) => {\n  useGridPaginationMeta(apiRef, props);\n  useGridPaginationModel(apiRef, props);\n  useGridRowCount(apiRef, props);\n};", "map": {"version": 3, "names": ["_extends", "throwIfPageSizeExceedsTheLimit", "getDefaultGridPaginationModel", "useGridPaginationModel", "useGridRowCount", "useGridPaginationMeta", "paginationStateInitializer", "state", "props", "paginationModel", "autoPageSize", "initialState", "pagination", "pageSize", "signature", "rowCount", "meta", "paginationMeta", "useGridPagination", "apiRef"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/pagination/useGridPagination.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel } from \"./gridPaginationUtils.js\";\nimport { useGridPaginationModel } from \"./useGridPaginationModel.js\";\nimport { useGridRowCount } from \"./useGridRowCount.js\";\nimport { useGridPaginationMeta } from \"./useGridPaginationMeta.js\";\nexport const paginationStateInitializer = (state, props) => {\n  const paginationModel = _extends({}, getDefaultGridPaginationModel(props.autoPageSize), props.paginationModel ?? props.initialState?.pagination?.paginationModel);\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, props.signature);\n  const rowCount = props.rowCount ?? props.initialState?.pagination?.rowCount;\n  const meta = props.paginationMeta ?? props.initialState?.pagination?.meta ?? {};\n  return _extends({}, state, {\n    pagination: {\n      paginationModel,\n      rowCount,\n      meta\n    }\n  });\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPagination = (apiRef, props) => {\n  useGridPaginationMeta(apiRef, props);\n  useGridPaginationModel(apiRef, props);\n  useGridRowCount(apiRef, props);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,8BAA8B,EAAEC,6BAA6B,QAAQ,0BAA0B;AACxG,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,OAAO,MAAMC,0BAA0B,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAC1D,MAAMC,eAAe,GAAGT,QAAQ,CAAC,CAAC,CAAC,EAAEE,6BAA6B,CAACM,KAAK,CAACE,YAAY,CAAC,EAAEF,KAAK,CAACC,eAAe,IAAID,KAAK,CAACG,YAAY,EAAEC,UAAU,EAAEH,eAAe,CAAC;EACjKR,8BAA8B,CAACQ,eAAe,CAACI,QAAQ,EAAEL,KAAK,CAACM,SAAS,CAAC;EACzE,MAAMC,QAAQ,GAAGP,KAAK,CAACO,QAAQ,IAAIP,KAAK,CAACG,YAAY,EAAEC,UAAU,EAAEG,QAAQ;EAC3E,MAAMC,IAAI,GAAGR,KAAK,CAACS,cAAc,IAAIT,KAAK,CAACG,YAAY,EAAEC,UAAU,EAAEI,IAAI,IAAI,CAAC,CAAC;EAC/E,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;IACzBK,UAAU,EAAE;MACVH,eAAe;MACfM,QAAQ;MACRC;IACF;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAME,iBAAiB,GAAGA,CAACC,MAAM,EAAEX,KAAK,KAAK;EAClDH,qBAAqB,CAACc,MAAM,EAAEX,KAAK,CAAC;EACpCL,sBAAsB,CAACgB,MAAM,EAAEX,KAAK,CAAC;EACrCJ,eAAe,CAACe,MAAM,EAAEX,KAAK,CAAC;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}