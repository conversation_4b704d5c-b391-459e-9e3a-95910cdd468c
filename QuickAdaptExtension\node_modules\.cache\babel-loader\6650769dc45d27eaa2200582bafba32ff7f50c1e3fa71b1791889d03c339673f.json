{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { gridClasses, getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['topContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, {});\n};\nconst Element = styled('div')({\n  position: 'sticky',\n  zIndex: 4,\n  top: 0\n});\nexport function GridTopContainer(props) {\n  const classes = useUtilityClasses();\n  return /*#__PURE__*/_jsx(Element, _extends({}, props, {\n    className: clsx(classes.root, gridClasses['container--top']),\n    role: \"presentation\"\n  }));\n}", "map": {"version": 3, "names": ["_extends", "React", "clsx", "styled", "composeClasses", "gridClasses", "getDataGridUtilityClass", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "Element", "position", "zIndex", "top", "GridTopContainer", "props", "classes", "className", "role"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/virtualization/GridTopContainer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { gridClasses, getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['topContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, {});\n};\nconst Element = styled('div')({\n  position: 'sticky',\n  zIndex: 4,\n  top: 0\n});\nexport function GridTopContainer(props) {\n  const classes = useUtilityClasses();\n  return /*#__PURE__*/_jsx(Element, _extends({}, props, {\n    className: clsx(classes.root, gridClasses['container--top']),\n    role: \"presentation\"\n  }));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,WAAW,EAAEC,uBAAuB,QAAQ,gCAAgC;AACrF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,cAAc;EACvB,CAAC;EACD,OAAOP,cAAc,CAACM,KAAK,EAAEJ,uBAAuB,EAAE,CAAC,CAAC,CAAC;AAC3D,CAAC;AACD,MAAMM,OAAO,GAAGT,MAAM,CAAC,KAAK,CAAC,CAAC;EAC5BU,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE;AACP,CAAC,CAAC;AACF,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,MAAMC,OAAO,GAAGT,iBAAiB,CAAC,CAAC;EACnC,OAAO,aAAaD,IAAI,CAACI,OAAO,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEiB,KAAK,EAAE;IACpDE,SAAS,EAAEjB,IAAI,CAACgB,OAAO,CAACP,IAAI,EAAEN,WAAW,CAAC,gBAAgB,CAAC,CAAC;IAC5De,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}