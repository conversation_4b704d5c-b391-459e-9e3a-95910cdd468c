{"ast": null, "code": "var identity = require('./identity');\n\n/**\n * Casts `value` to `identity` if it's not a function.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Function} Returns cast function.\n */\nfunction castFunction(value) {\n  return typeof value == 'function' ? value : identity;\n}\nmodule.exports = castFunction;", "map": {"version": 3, "names": ["identity", "require", "castFunction", "value", "module", "exports"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash/_castFunction.js"], "sourcesContent": ["var identity = require('./identity');\n\n/**\n * Casts `value` to `identity` if it's not a function.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Function} Returns cast function.\n */\nfunction castFunction(value) {\n  return typeof value == 'function' ? value : identity;\n}\n\nmodule.exports = castFunction;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,OAAOA,KAAK,IAAI,UAAU,GAAGA,KAAK,GAAGH,QAAQ;AACtD;AAEAI,MAAM,CAACC,OAAO,GAAGH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}