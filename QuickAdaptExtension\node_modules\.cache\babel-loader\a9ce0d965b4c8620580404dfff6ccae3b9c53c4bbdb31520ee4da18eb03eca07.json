{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridRowsMetaSelector } from \"../../hooks/features/rows/index.js\";\nimport { gridRenderContextSelector } from \"../../hooks/features/virtualization/index.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['virtualScrollerRenderZone']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerRenderZoneRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScrollerRenderZone',\n  overridesResolver: (props, styles) => styles.virtualScrollerRenderZone\n})({\n  position: 'absolute',\n  display: 'flex',\n  // Prevents margin collapsing when using `getRowSpacing`\n  flexDirection: 'column'\n});\nconst GridVirtualScrollerRenderZone = /*#__PURE__*/React.forwardRef(function GridVirtualScrollerRenderZone(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const offsetTop = useGridSelector(apiRef, () => {\n    const renderContext = gridRenderContextSelector(apiRef);\n    const rowsMeta = gridRowsMetaSelector(apiRef.current.state);\n    return rowsMeta.positions[renderContext.firstRowIndex] ?? 0;\n  });\n  return /*#__PURE__*/_jsx(VirtualScrollerRenderZoneRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: rootProps,\n    style: {\n      transform: `translate3d(0, ${offsetTop}px, 0)`\n    }\n  }, other));\n});\nexport { GridVirtualScrollerRenderZone };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "styled", "composeClasses", "useGridApiContext", "useGridSelector", "gridRowsMetaSelector", "gridRenderContextSelector", "useGridRootProps", "getDataGridUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "VirtualScrollerRenderZoneRoot", "name", "slot", "overridesResolver", "props", "styles", "virtualScrollerRenderZone", "position", "display", "flexDirection", "GridVirtualScrollerRenderZone", "forwardRef", "ref", "className", "other", "apiRef", "rootProps", "offsetTop", "renderContext", "rowsMeta", "current", "state", "positions", "firstRowIndex", "style", "transform"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/virtualization/GridVirtualScrollerRenderZone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridRowsMetaSelector } from \"../../hooks/features/rows/index.js\";\nimport { gridRenderContextSelector } from \"../../hooks/features/virtualization/index.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['virtualScrollerRenderZone']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerRenderZoneRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScrollerRenderZone',\n  overridesResolver: (props, styles) => styles.virtualScrollerRenderZone\n})({\n  position: 'absolute',\n  display: 'flex',\n  // Prevents margin collapsing when using `getRowSpacing`\n  flexDirection: 'column'\n});\nconst GridVirtualScrollerRenderZone = /*#__PURE__*/React.forwardRef(function GridVirtualScrollerRenderZone(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const offsetTop = useGridSelector(apiRef, () => {\n    const renderContext = gridRenderContextSelector(apiRef);\n    const rowsMeta = gridRowsMetaSelector(apiRef.current.state);\n    return rowsMeta.positions[renderContext.firstRowIndex] ?? 0;\n  });\n  return /*#__PURE__*/_jsx(VirtualScrollerRenderZoneRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: rootProps,\n    style: {\n      transform: `translate3d(0, ${offsetTop}px, 0)`\n    }\n  }, other));\n});\nexport { GridVirtualScrollerRenderZone };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,oBAAoB,QAAQ,oCAAoC;AACzE,SAASC,yBAAyB,QAAQ,8CAA8C;AACxF,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,2BAA2B;EACpC,CAAC;EACD,OAAOb,cAAc,CAACY,KAAK,EAAEN,uBAAuB,EAAEK,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,6BAA6B,GAAGf,MAAM,CAAC,KAAK,EAAE;EAClDgB,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,2BAA2B;EACjCC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;AAC/C,CAAC,CAAC,CAAC;EACDC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,MAAM;EACf;EACAC,aAAa,EAAE;AACjB,CAAC,CAAC;AACF,MAAMC,6BAA6B,GAAG,aAAa3B,KAAK,CAAC4B,UAAU,CAAC,SAASD,6BAA6BA,CAACN,KAAK,EAAEQ,GAAG,EAAE;EACrH,MAAM;MACFC;IACF,CAAC,GAAGT,KAAK;IACTU,KAAK,GAAGjC,6BAA6B,CAACuB,KAAK,EAAEtB,SAAS,CAAC;EACzD,MAAMiC,MAAM,GAAG5B,iBAAiB,CAAC,CAAC;EAClC,MAAM6B,SAAS,GAAGzB,gBAAgB,CAAC,CAAC;EACpC,MAAMM,OAAO,GAAGF,iBAAiB,CAACqB,SAAS,CAAC;EAC5C,MAAMC,SAAS,GAAG7B,eAAe,CAAC2B,MAAM,EAAE,MAAM;IAC9C,MAAMG,aAAa,GAAG5B,yBAAyB,CAACyB,MAAM,CAAC;IACvD,MAAMI,QAAQ,GAAG9B,oBAAoB,CAAC0B,MAAM,CAACK,OAAO,CAACC,KAAK,CAAC;IAC3D,OAAOF,QAAQ,CAACG,SAAS,CAACJ,aAAa,CAACK,aAAa,CAAC,IAAI,CAAC;EAC7D,CAAC,CAAC;EACF,OAAO,aAAa7B,IAAI,CAACM,6BAA6B,EAAEpB,QAAQ,CAAC;IAC/DgC,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAE7B,IAAI,CAACa,OAAO,CAACE,IAAI,EAAEc,SAAS,CAAC;IACxCjB,UAAU,EAAEoB,SAAS;IACrBQ,KAAK,EAAE;MACLC,SAAS,EAAE,kBAAkBR,SAAS;IACxC;EACF,CAAC,EAAEH,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,SAASJ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}