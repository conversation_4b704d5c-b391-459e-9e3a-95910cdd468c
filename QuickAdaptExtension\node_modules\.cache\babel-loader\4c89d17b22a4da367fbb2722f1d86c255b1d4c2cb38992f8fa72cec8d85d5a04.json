{"ast": null, "code": "// Used https://gist.github.com/mudge/5830382 as a starting point.\n// See https://github.com/browserify/events/blob/master/events.js for\n// the Node.js (https://nodejs.org/api/events.html) polyfill used by webpack.\nexport class EventManager {\n  constructor() {\n    this.maxListeners = 20;\n    this.warnOnce = false;\n    this.events = {};\n  }\n  on(eventName, listener) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let collection = this.events[eventName];\n    if (!collection) {\n      collection = {\n        highPriority: new Map(),\n        regular: new Map()\n      };\n      this.events[eventName] = collection;\n    }\n    if (options.isFirst) {\n      collection.highPriority.set(listener, true);\n    } else {\n      collection.regular.set(listener, true);\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const collectionSize = collection.highPriority.size + collection.regular.size;\n      if (collectionSize > this.maxListeners && !this.warnOnce) {\n        this.warnOnce = true;\n        console.warn([`Possible EventEmitter memory leak detected. ${collectionSize} ${eventName} listeners added.`].join('\\n'));\n      }\n    }\n  }\n  removeListener(eventName, listener) {\n    if (this.events[eventName]) {\n      this.events[eventName].regular.delete(listener);\n      this.events[eventName].highPriority.delete(listener);\n    }\n  }\n  removeAllListeners() {\n    this.events = {};\n  }\n  emit(eventName) {\n    const collection = this.events[eventName];\n    if (!collection) {\n      return;\n    }\n    const highPriorityListeners = Array.from(collection.highPriority.keys());\n    const regularListeners = Array.from(collection.regular.keys());\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    for (let i = highPriorityListeners.length - 1; i >= 0; i -= 1) {\n      const listener = highPriorityListeners[i];\n      if (collection.highPriority.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n    for (let i = 0; i < regularListeners.length; i += 1) {\n      const listener = regularListeners[i];\n      if (collection.regular.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n  }\n  once(eventName, listener) {\n    // eslint-disable-next-line consistent-this\n    const that = this;\n    this.on(eventName, function oneTimeListener() {\n      that.removeListener(eventName, oneTimeListener);\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      listener.apply(that, args);\n    });\n  }\n}", "map": {"version": 3, "names": ["EventManager", "constructor", "maxListeners", "warnOnce", "events", "on", "eventName", "listener", "options", "arguments", "length", "undefined", "collection", "highPriority", "Map", "regular", "<PERSON><PERSON><PERSON><PERSON>", "set", "process", "env", "NODE_ENV", "collectionSize", "size", "console", "warn", "join", "removeListener", "delete", "removeAllListeners", "emit", "highPriorityListeners", "Array", "from", "keys", "regularListeners", "_len", "args", "_key", "i", "has", "apply", "once", "that", "oneTimeListener", "_len2", "_key2"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-internals/EventManager/EventManager.js"], "sourcesContent": ["// Used https://gist.github.com/mudge/5830382 as a starting point.\n// See https://github.com/browserify/events/blob/master/events.js for\n// the Node.js (https://nodejs.org/api/events.html) polyfill used by webpack.\nexport class EventManager {\n  constructor() {\n    this.maxListeners = 20;\n    this.warnOnce = false;\n    this.events = {};\n  }\n  on(eventName, listener, options = {}) {\n    let collection = this.events[eventName];\n    if (!collection) {\n      collection = {\n        highPriority: new Map(),\n        regular: new Map()\n      };\n      this.events[eventName] = collection;\n    }\n    if (options.isFirst) {\n      collection.highPriority.set(listener, true);\n    } else {\n      collection.regular.set(listener, true);\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const collectionSize = collection.highPriority.size + collection.regular.size;\n      if (collectionSize > this.maxListeners && !this.warnOnce) {\n        this.warnOnce = true;\n        console.warn([`Possible EventEmitter memory leak detected. ${collectionSize} ${eventName} listeners added.`].join('\\n'));\n      }\n    }\n  }\n  removeListener(eventName, listener) {\n    if (this.events[eventName]) {\n      this.events[eventName].regular.delete(listener);\n      this.events[eventName].highPriority.delete(listener);\n    }\n  }\n  removeAllListeners() {\n    this.events = {};\n  }\n  emit(eventName, ...args) {\n    const collection = this.events[eventName];\n    if (!collection) {\n      return;\n    }\n    const highPriorityListeners = Array.from(collection.highPriority.keys());\n    const regularListeners = Array.from(collection.regular.keys());\n    for (let i = highPriorityListeners.length - 1; i >= 0; i -= 1) {\n      const listener = highPriorityListeners[i];\n      if (collection.highPriority.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n    for (let i = 0; i < regularListeners.length; i += 1) {\n      const listener = regularListeners[i];\n      if (collection.regular.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n  }\n  once(eventName, listener) {\n    // eslint-disable-next-line consistent-this\n    const that = this;\n    this.on(eventName, function oneTimeListener(...args) {\n      that.removeListener(eventName, oneTimeListener);\n      listener.apply(that, args);\n    });\n  }\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;EAClB;EACAC,EAAEA,CAACC,SAAS,EAAEC,QAAQ,EAAgB;IAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAClC,IAAIG,UAAU,GAAG,IAAI,CAACR,MAAM,CAACE,SAAS,CAAC;IACvC,IAAI,CAACM,UAAU,EAAE;MACfA,UAAU,GAAG;QACXC,YAAY,EAAE,IAAIC,GAAG,CAAC,CAAC;QACvBC,OAAO,EAAE,IAAID,GAAG,CAAC;MACnB,CAAC;MACD,IAAI,CAACV,MAAM,CAACE,SAAS,CAAC,GAAGM,UAAU;IACrC;IACA,IAAIJ,OAAO,CAACQ,OAAO,EAAE;MACnBJ,UAAU,CAACC,YAAY,CAACI,GAAG,CAACV,QAAQ,EAAE,IAAI,CAAC;IAC7C,CAAC,MAAM;MACLK,UAAU,CAACG,OAAO,CAACE,GAAG,CAACV,QAAQ,EAAE,IAAI,CAAC;IACxC;IACA,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAMC,cAAc,GAAGT,UAAU,CAACC,YAAY,CAACS,IAAI,GAAGV,UAAU,CAACG,OAAO,CAACO,IAAI;MAC7E,IAAID,cAAc,GAAG,IAAI,CAACnB,YAAY,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;QACxD,IAAI,CAACA,QAAQ,GAAG,IAAI;QACpBoB,OAAO,CAACC,IAAI,CAAC,CAAC,+CAA+CH,cAAc,IAAIf,SAAS,mBAAmB,CAAC,CAACmB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1H;IACF;EACF;EACAC,cAAcA,CAACpB,SAAS,EAAEC,QAAQ,EAAE;IAClC,IAAI,IAAI,CAACH,MAAM,CAACE,SAAS,CAAC,EAAE;MAC1B,IAAI,CAACF,MAAM,CAACE,SAAS,CAAC,CAACS,OAAO,CAACY,MAAM,CAACpB,QAAQ,CAAC;MAC/C,IAAI,CAACH,MAAM,CAACE,SAAS,CAAC,CAACO,YAAY,CAACc,MAAM,CAACpB,QAAQ,CAAC;IACtD;EACF;EACAqB,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAACxB,MAAM,GAAG,CAAC,CAAC;EAClB;EACAyB,IAAIA,CAACvB,SAAS,EAAW;IACvB,MAAMM,UAAU,GAAG,IAAI,CAACR,MAAM,CAACE,SAAS,CAAC;IACzC,IAAI,CAACM,UAAU,EAAE;MACf;IACF;IACA,MAAMkB,qBAAqB,GAAGC,KAAK,CAACC,IAAI,CAACpB,UAAU,CAACC,YAAY,CAACoB,IAAI,CAAC,CAAC,CAAC;IACxE,MAAMC,gBAAgB,GAAGH,KAAK,CAACC,IAAI,CAACpB,UAAU,CAACG,OAAO,CAACkB,IAAI,CAAC,CAAC,CAAC;IAAC,SAAAE,IAAA,GAAA1B,SAAA,CAAAC,MAAA,EAN9C0B,IAAI,OAAAL,KAAA,CAAAI,IAAA,OAAAA,IAAA,WAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAJD,IAAI,CAAAC,IAAA,QAAA5B,SAAA,CAAA4B,IAAA;IAAA;IAOrB,KAAK,IAAIC,CAAC,GAAGR,qBAAqB,CAACpB,MAAM,GAAG,CAAC,EAAE4B,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7D,MAAM/B,QAAQ,GAAGuB,qBAAqB,CAACQ,CAAC,CAAC;MACzC,IAAI1B,UAAU,CAACC,YAAY,CAAC0B,GAAG,CAAChC,QAAQ,CAAC,EAAE;QACzCA,QAAQ,CAACiC,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;MAC5B;IACF;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,gBAAgB,CAACxB,MAAM,EAAE4B,CAAC,IAAI,CAAC,EAAE;MACnD,MAAM/B,QAAQ,GAAG2B,gBAAgB,CAACI,CAAC,CAAC;MACpC,IAAI1B,UAAU,CAACG,OAAO,CAACwB,GAAG,CAAChC,QAAQ,CAAC,EAAE;QACpCA,QAAQ,CAACiC,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;MAC5B;IACF;EACF;EACAK,IAAIA,CAACnC,SAAS,EAAEC,QAAQ,EAAE;IACxB;IACA,MAAMmC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACrC,EAAE,CAACC,SAAS,EAAE,SAASqC,eAAeA,CAAA,EAAU;MACnDD,IAAI,CAAChB,cAAc,CAACpB,SAAS,EAAEqC,eAAe,CAAC;MAAC,SAAAC,KAAA,GAAAnC,SAAA,CAAAC,MAAA,EADH0B,IAAI,OAAAL,KAAA,CAAAa,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJT,IAAI,CAAAS,KAAA,IAAApC,SAAA,CAAAoC,KAAA;MAAA;MAEjDtC,QAAQ,CAACiC,KAAK,CAACE,IAAI,EAAEN,IAAI,CAAC;IAC5B,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}