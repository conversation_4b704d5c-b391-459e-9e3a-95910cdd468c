{"ast": null, "code": "var Uint8Array = require('./_Uint8Array');\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\nmodule.exports = cloneArrayBuffer;", "map": {"version": 3, "names": ["Uint8Array", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arrayBuffer", "result", "constructor", "byteLength", "set", "module", "exports"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash/_cloneArrayBuffer.js"], "sourcesContent": ["var Uint8Array = require('./_Uint8Array');\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\nmodule.exports = cloneArrayBuffer;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EACrC,IAAIC,MAAM,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,UAAU,CAAC;EAChE,IAAIN,UAAU,CAACI,MAAM,CAAC,CAACG,GAAG,CAAC,IAAIP,UAAU,CAACG,WAAW,CAAC,CAAC;EACvD,OAAOC,MAAM;AACf;AAEAI,MAAM,CAACC,OAAO,GAAGP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}