{"ast": null, "code": "import { useGridApiOptionHandler } from \"../../utils/useGridApiEventHandler.js\";\n/**\n * @requires useGridFocus (event) - can be after, async only\n * @requires useGridColumns (event) - can be after, async only\n */\nexport function useGridEvents(apiRef, props) {\n  useGridApiOptionHandler(apiRef, 'columnHeaderClick', props.onColumnHeaderClick);\n  useGridApiOptionHandler(apiRef, 'columnHeaderContextMenu', props.onColumnHeaderContextMenu);\n  useGridApiOptionHandler(apiRef, 'columnHeaderDoubleClick', props.onColumnHeaderDoubleClick);\n  useGridApiOptionHandler(apiRef, 'columnHeaderOver', props.onColumnHeaderOver);\n  useGridApiOptionHandler(apiRef, 'columnHeaderOut', props.onColumnHeaderOut);\n  useGridApiOptionHandler(apiRef, 'columnHeaderEnter', props.onColumnHeaderEnter);\n  useGridApiOptionHandler(apiRef, 'columnHeaderLeave', props.onColumnHeaderLeave);\n  useGridApiOptionHandler(apiRef, 'cellClick', props.onCellClick);\n  useGridApiOptionHandler(apiRef, 'cellDoubleClick', props.onCellDoubleClick);\n  useGridApiOptionHandler(apiRef, 'cellKeyDown', props.onCellKeyDown);\n  useGridApiOptionHandler(apiRef, 'preferencePanelClose', props.onPreferencePanelClose);\n  useGridApiOptionHandler(apiRef, 'preferencePanelOpen', props.onPreferencePanelOpen);\n  useGridApiOptionHandler(apiRef, 'menuOpen', props.onMenuOpen);\n  useGridApiOptionHandler(apiRef, 'menuClose', props.onMenuClose);\n  useGridApiOptionHandler(apiRef, 'rowDoubleClick', props.onRowDoubleClick);\n  useGridApiOptionHandler(apiRef, 'rowClick', props.onRowClick);\n  useGridApiOptionHandler(apiRef, 'stateChange', props.onStateChange);\n}", "map": {"version": 3, "names": ["useGridApiOptionHandler", "useGridEvents", "apiRef", "props", "onColumnHeaderClick", "onColumnHeaderContextMenu", "onColumnHeaderDoubleClick", "onColumnHeaderOver", "onColumnHeaderOut", "onColumnHeaderEnter", "onColumnHeaderLeave", "onCellClick", "onCellDoubleClick", "onCellKeyDown", "onPreferencePanelClose", "onPreferencePanelOpen", "onMenuOpen", "onMenuClose", "onRowDoubleClick", "onRowClick", "onStateChange"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/events/useGridEvents.js"], "sourcesContent": ["import { useGridApiOptionHandler } from \"../../utils/useGridApiEventHandler.js\";\n/**\n * @requires useGridFocus (event) - can be after, async only\n * @requires useGridColumns (event) - can be after, async only\n */\nexport function useGridEvents(apiRef, props) {\n  useGridApiOptionHandler(apiRef, 'columnHeaderClick', props.onColumnHeaderClick);\n  useGridApiOptionHandler(apiRef, 'columnHeaderContextMenu', props.onColumnHeaderContextMenu);\n  useGridApiOptionHandler(apiRef, 'columnHeaderDoubleClick', props.onColumnHeaderDoubleClick);\n  useGridApiOptionHandler(apiRef, 'columnHeaderOver', props.onColumnHeaderOver);\n  useGridApiOptionHandler(apiRef, 'columnHeaderOut', props.onColumnHeaderOut);\n  useGridApiOptionHandler(apiRef, 'columnHeaderEnter', props.onColumnHeaderEnter);\n  useGridApiOptionHandler(apiRef, 'columnHeaderLeave', props.onColumnHeaderLeave);\n  useGridApiOptionHandler(apiRef, 'cellClick', props.onCellClick);\n  useGridApiOptionHandler(apiRef, 'cellDoubleClick', props.onCellDoubleClick);\n  useGridApiOptionHandler(apiRef, 'cellKeyDown', props.onCellKeyDown);\n  useGridApiOptionHandler(apiRef, 'preferencePanelClose', props.onPreferencePanelClose);\n  useGridApiOptionHandler(apiRef, 'preferencePanelOpen', props.onPreferencePanelOpen);\n  useGridApiOptionHandler(apiRef, 'menuOpen', props.onMenuOpen);\n  useGridApiOptionHandler(apiRef, 'menuClose', props.onMenuClose);\n  useGridApiOptionHandler(apiRef, 'rowDoubleClick', props.onRowDoubleClick);\n  useGridApiOptionHandler(apiRef, 'rowClick', props.onRowClick);\n  useGridApiOptionHandler(apiRef, 'stateChange', props.onStateChange);\n}"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,uCAAuC;AAC/E;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC3CH,uBAAuB,CAACE,MAAM,EAAE,mBAAmB,EAAEC,KAAK,CAACC,mBAAmB,CAAC;EAC/EJ,uBAAuB,CAACE,MAAM,EAAE,yBAAyB,EAAEC,KAAK,CAACE,yBAAyB,CAAC;EAC3FL,uBAAuB,CAACE,MAAM,EAAE,yBAAyB,EAAEC,KAAK,CAACG,yBAAyB,CAAC;EAC3FN,uBAAuB,CAACE,MAAM,EAAE,kBAAkB,EAAEC,KAAK,CAACI,kBAAkB,CAAC;EAC7EP,uBAAuB,CAACE,MAAM,EAAE,iBAAiB,EAAEC,KAAK,CAACK,iBAAiB,CAAC;EAC3ER,uBAAuB,CAACE,MAAM,EAAE,mBAAmB,EAAEC,KAAK,CAACM,mBAAmB,CAAC;EAC/ET,uBAAuB,CAACE,MAAM,EAAE,mBAAmB,EAAEC,KAAK,CAACO,mBAAmB,CAAC;EAC/EV,uBAAuB,CAACE,MAAM,EAAE,WAAW,EAAEC,KAAK,CAACQ,WAAW,CAAC;EAC/DX,uBAAuB,CAACE,MAAM,EAAE,iBAAiB,EAAEC,KAAK,CAACS,iBAAiB,CAAC;EAC3EZ,uBAAuB,CAACE,MAAM,EAAE,aAAa,EAAEC,KAAK,CAACU,aAAa,CAAC;EACnEb,uBAAuB,CAACE,MAAM,EAAE,sBAAsB,EAAEC,KAAK,CAACW,sBAAsB,CAAC;EACrFd,uBAAuB,CAACE,MAAM,EAAE,qBAAqB,EAAEC,KAAK,CAACY,qBAAqB,CAAC;EACnFf,uBAAuB,CAACE,MAAM,EAAE,UAAU,EAAEC,KAAK,CAACa,UAAU,CAAC;EAC7DhB,uBAAuB,CAACE,MAAM,EAAE,WAAW,EAAEC,KAAK,CAACc,WAAW,CAAC;EAC/DjB,uBAAuB,CAACE,MAAM,EAAE,gBAAgB,EAAEC,KAAK,CAACe,gBAAgB,CAAC;EACzElB,uBAAuB,CAACE,MAAM,EAAE,UAAU,EAAEC,KAAK,CAACgB,UAAU,CAAC;EAC7DnB,uBAAuB,CAACE,MAAM,EAAE,aAAa,EAAEC,KAAK,CAACiB,aAAa,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}