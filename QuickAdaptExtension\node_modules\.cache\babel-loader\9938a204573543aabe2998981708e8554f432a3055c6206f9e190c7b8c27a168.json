{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { gridClasses, getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { gridDimensionsSelector } from \"../../hooks/features/dimensions/gridDimensionsSelectors.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['bottomContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, {});\n};\nconst Element = styled('div')({\n  position: 'sticky',\n  zIndex: 4,\n  bottom: 'calc(var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize))'\n});\nexport function GridBottomContainer(props) {\n  const classes = useUtilityClasses();\n  const apiRef = useGridApiContext();\n  const {\n    viewportOuterSize,\n    minimumSize,\n    hasScrollX,\n    scrollbarSize\n  } = useGridSelector(apiRef, gridDimensionsSelector);\n  const scrollHeight = hasScrollX ? scrollbarSize : 0;\n  const offset = Math.max(viewportOuterSize.height - minimumSize.height -\n  // Subtract scroll height twice to account for GridVirtualScrollerFiller and horizontal scrollbar\n  2 * scrollHeight, 0);\n  return /*#__PURE__*/_jsx(Element, _extends({}, props, {\n    className: clsx(classes.root, gridClasses['container--bottom']),\n    style: {\n      transform: `translateY(${offset}px)`\n    },\n    role: \"presentation\"\n  }));\n}", "map": {"version": 3, "names": ["_extends", "React", "clsx", "styled", "composeClasses", "gridClasses", "getDataGridUtilityClass", "gridDimensionsSelector", "useGridApiContext", "useGridSelector", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "Element", "position", "zIndex", "bottom", "GridBottomContainer", "props", "classes", "apiRef", "viewportOuterSize", "minimumSize", "hasScrollX", "scrollbarSize", "scrollHeight", "offset", "Math", "max", "height", "className", "style", "transform", "role"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/virtualization/GridBottomContainer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { gridClasses, getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { gridDimensionsSelector } from \"../../hooks/features/dimensions/gridDimensionsSelectors.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['bottomContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, {});\n};\nconst Element = styled('div')({\n  position: 'sticky',\n  zIndex: 4,\n  bottom: 'calc(var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize))'\n});\nexport function GridBottomContainer(props) {\n  const classes = useUtilityClasses();\n  const apiRef = useGridApiContext();\n  const {\n    viewportOuterSize,\n    minimumSize,\n    hasScrollX,\n    scrollbarSize\n  } = useGridSelector(apiRef, gridDimensionsSelector);\n  const scrollHeight = hasScrollX ? scrollbarSize : 0;\n  const offset = Math.max(viewportOuterSize.height - minimumSize.height -\n  // Subtract scroll height twice to account for GridVirtualScrollerFiller and horizontal scrollbar\n  2 * scrollHeight, 0);\n  return /*#__PURE__*/_jsx(Element, _extends({}, props, {\n    className: clsx(classes.root, gridClasses['container--bottom']),\n    style: {\n      transform: `translateY(${offset}px)`\n    },\n    role: \"presentation\"\n  }));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,WAAW,EAAEC,uBAAuB,QAAQ,gCAAgC;AACrF,SAASC,sBAAsB,QAAQ,4DAA4D;AACnG,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,iBAAiB;EAC1B,CAAC;EACD,OAAOV,cAAc,CAACS,KAAK,EAAEP,uBAAuB,EAAE,CAAC,CAAC,CAAC;AAC3D,CAAC;AACD,MAAMS,OAAO,GAAGZ,MAAM,CAAC,KAAK,CAAC,CAAC;EAC5Ba,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;AACV,CAAC,CAAC;AACF,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACzC,MAAMC,OAAO,GAAGT,iBAAiB,CAAC,CAAC;EACnC,MAAMU,MAAM,GAAGd,iBAAiB,CAAC,CAAC;EAClC,MAAM;IACJe,iBAAiB;IACjBC,WAAW;IACXC,UAAU;IACVC;EACF,CAAC,GAAGjB,eAAe,CAACa,MAAM,EAAEf,sBAAsB,CAAC;EACnD,MAAMoB,YAAY,GAAGF,UAAU,GAAGC,aAAa,GAAG,CAAC;EACnD,MAAME,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACP,iBAAiB,CAACQ,MAAM,GAAGP,WAAW,CAACO,MAAM;EACrE;EACA,CAAC,GAAGJ,YAAY,EAAE,CAAC,CAAC;EACpB,OAAO,aAAahB,IAAI,CAACI,OAAO,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEoB,KAAK,EAAE;IACpDY,SAAS,EAAE9B,IAAI,CAACmB,OAAO,CAACP,IAAI,EAAET,WAAW,CAAC,mBAAmB,CAAC,CAAC;IAC/D4B,KAAK,EAAE;MACLC,SAAS,EAAE,cAAcN,MAAM;IACjC,CAAC;IACDO,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}