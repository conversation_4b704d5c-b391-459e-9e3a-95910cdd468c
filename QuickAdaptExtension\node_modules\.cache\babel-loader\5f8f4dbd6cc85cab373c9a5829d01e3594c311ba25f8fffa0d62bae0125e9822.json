{"ast": null, "code": "import * as React from 'react';\nimport { GridApiContext } from \"../components/GridApiContext.js\";\nimport { GridPrivateApiContext } from \"../hooks/utils/useGridPrivateApiContext.js\";\nimport { GridRootPropsContext } from \"./GridRootPropsContext.js\";\nimport { GridConfigurationContext } from \"../components/GridConfigurationContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridContextProvider(_ref) {\n  let {\n    privateApiRef,\n    configuration,\n    props,\n    children\n  } = _ref;\n  const apiRef = React.useRef(privateApiRef.current.getPublicApi());\n  return /*#__PURE__*/_jsx(GridConfigurationContext.Provider, {\n    value: configuration,\n    children: /*#__PURE__*/_jsx(GridRootPropsContext.Provider, {\n      value: props,\n      children: /*#__PURE__*/_jsx(GridPrivateApiContext.Provider, {\n        value: privateApiRef,\n        children: /*#__PURE__*/_jsx(GridApiContext.Provider, {\n          value: apiRef,\n          children: children\n        })\n      })\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "GridApiContext", "GridPrivateApiContext", "GridRootPropsContext", "GridConfigurationContext", "jsx", "_jsx", "GridContextProvider", "_ref", "privateApiRef", "configuration", "props", "children", "apiRef", "useRef", "current", "getPublicApi", "Provider", "value"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/context/GridContextProvider.js"], "sourcesContent": ["import * as React from 'react';\nimport { GridApiContext } from \"../components/GridApiContext.js\";\nimport { GridPrivateApiContext } from \"../hooks/utils/useGridPrivateApiContext.js\";\nimport { GridRootPropsContext } from \"./GridRootPropsContext.js\";\nimport { GridConfigurationContext } from \"../components/GridConfigurationContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridContextProvider({\n  privateApiRef,\n  configuration,\n  props,\n  children\n}) {\n  const apiRef = React.useRef(privateApiRef.current.getPublicApi());\n  return /*#__PURE__*/_jsx(GridConfigurationContext.Provider, {\n    value: configuration,\n    children: /*#__PURE__*/_jsx(GridRootPropsContext.Provider, {\n      value: props,\n      children: /*#__PURE__*/_jsx(GridPrivateApiContext.Provider, {\n        value: privateApiRef,\n        children: /*#__PURE__*/_jsx(GridApiContext.Provider, {\n          value: apiRef,\n          children: children\n        })\n      })\n    })\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,wBAAwB,QAAQ,2CAA2C;AACpF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,mBAAmBA,CAAAC,IAAA,EAKhC;EAAA,IALiC;IAClCC,aAAa;IACbC,aAAa;IACbC,KAAK;IACLC;EACF,CAAC,GAAAJ,IAAA;EACC,MAAMK,MAAM,GAAGb,KAAK,CAACc,MAAM,CAACL,aAAa,CAACM,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC;EACjE,OAAO,aAAaV,IAAI,CAACF,wBAAwB,CAACa,QAAQ,EAAE;IAC1DC,KAAK,EAAER,aAAa;IACpBE,QAAQ,EAAE,aAAaN,IAAI,CAACH,oBAAoB,CAACc,QAAQ,EAAE;MACzDC,KAAK,EAAEP,KAAK;MACZC,QAAQ,EAAE,aAAaN,IAAI,CAACJ,qBAAqB,CAACe,QAAQ,EAAE;QAC1DC,KAAK,EAAET,aAAa;QACpBG,QAAQ,EAAE,aAAaN,IAAI,CAACL,cAAc,CAACgB,QAAQ,EAAE;UACnDC,KAAK,EAAEL,MAAM;UACbD,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}