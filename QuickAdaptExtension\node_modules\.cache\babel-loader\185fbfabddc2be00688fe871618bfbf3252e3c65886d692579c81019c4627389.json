{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnLookupSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridSortedRowEntriesSelector, gridSortedRowIdsSelector, gridSortModelSelector } from \"./gridSortingSelector.js\";\nimport { GRID_ROOT_GROUP_ID, gridRowTreeSelector } from \"../rows/index.js\";\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nimport { useGridRegisterStrategyProcessor, GRID_DEFAULT_STRATEGY } from \"../../core/strategyProcessing/index.js\";\nimport { buildAggregatedSortingApplier, mergeStateWithSortModel, getNextGridSortDirection, sanitizeSortModel } from \"./gridSortingUtils.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { getTreeNodeDescendants } from \"../rows/gridRowsUtils.js\";\nexport const sortingStateInitializer = (state, props) => {\n  const sortModel = props.sortModel ?? props.initialState?.sorting?.sortModel ?? [];\n  return _extends({}, state, {\n    sorting: {\n      sortModel: sanitizeSortModel(sortModel, props.disableMultipleColumnsSorting),\n      sortedRows: []\n    }\n  });\n};\n\n/**\n * @requires useGridRows (event)\n * @requires useGridColumns (event)\n */\nexport const useGridSorting = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSorting');\n  apiRef.current.registerControlState({\n    stateId: 'sortModel',\n    propModel: props.sortModel,\n    propOnChange: props.onSortModelChange,\n    stateSelector: gridSortModelSelector,\n    changeEvent: 'sortModelChange'\n  });\n  const upsertSortModel = React.useCallback((field, sortItem) => {\n    const sortModel = gridSortModelSelector(apiRef);\n    const existingIdx = sortModel.findIndex(c => c.field === field);\n    let newSortModel = [...sortModel];\n    if (existingIdx > -1) {\n      if (sortItem?.sort == null) {\n        newSortModel.splice(existingIdx, 1);\n      } else {\n        newSortModel.splice(existingIdx, 1, sortItem);\n      }\n    } else {\n      newSortModel = [...sortModel, sortItem];\n    }\n    return newSortModel;\n  }, [apiRef]);\n  const createSortItem = React.useCallback((col, directionOverride) => {\n    const sortModel = gridSortModelSelector(apiRef);\n    const existing = sortModel.find(c => c.field === col.field);\n    if (existing) {\n      const nextSort = directionOverride === undefined ? getNextGridSortDirection(col.sortingOrder ?? props.sortingOrder, existing.sort) : directionOverride;\n      return nextSort === undefined ? undefined : _extends({}, existing, {\n        sort: nextSort\n      });\n    }\n    return {\n      field: col.field,\n      sort: directionOverride === undefined ? getNextGridSortDirection(col.sortingOrder ?? props.sortingOrder) : directionOverride\n    };\n  }, [apiRef, props.sortingOrder]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.sortable === false || props.disableColumnSorting) {\n      return columnMenuItems;\n    }\n    const sortingOrder = colDef.sortingOrder || props.sortingOrder;\n    if (sortingOrder.some(item => !!item)) {\n      return [...columnMenuItems, 'columnMenuSortItem'];\n    }\n    return columnMenuItems;\n  }, [props.sortingOrder, props.disableColumnSorting]);\n\n  /**\n   * API METHODS\n   */\n  const applySorting = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      if (props.sortingMode === 'server') {\n        logger.debug('Skipping sorting rows as sortingMode = server');\n        return _extends({}, state, {\n          sorting: _extends({}, state.sorting, {\n            sortedRows: getTreeNodeDescendants(gridRowTreeSelector(apiRef), GRID_ROOT_GROUP_ID, false)\n          })\n        });\n      }\n      const sortModel = gridSortModelSelector(state, apiRef.current.instanceId);\n      const sortRowList = buildAggregatedSortingApplier(sortModel, apiRef);\n      const sortedRows = apiRef.current.applyStrategyProcessor('sorting', {\n        sortRowList\n      });\n      return _extends({}, state, {\n        sorting: _extends({}, state.sorting, {\n          sortedRows\n        })\n      });\n    });\n    apiRef.current.publishEvent('sortedRowsSet');\n    apiRef.current.forceUpdate();\n  }, [apiRef, logger, props.sortingMode]);\n  const setSortModel = React.useCallback(model => {\n    const currentModel = gridSortModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug(`Setting sort model`);\n      apiRef.current.setState(mergeStateWithSortModel(model, props.disableMultipleColumnsSorting));\n      apiRef.current.forceUpdate();\n      apiRef.current.applySorting();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsSorting]);\n  const sortColumn = React.useCallback((field, direction, allowMultipleSorting) => {\n    const column = apiRef.current.getColumn(field);\n    const sortItem = createSortItem(column, direction);\n    let sortModel;\n    if (!allowMultipleSorting || props.disableMultipleColumnsSorting) {\n      sortModel = sortItem?.sort == null ? [] : [sortItem];\n    } else {\n      sortModel = upsertSortModel(column.field, sortItem);\n    }\n    apiRef.current.setSortModel(sortModel);\n  }, [apiRef, upsertSortModel, createSortItem, props.disableMultipleColumnsSorting]);\n  const getSortModel = React.useCallback(() => gridSortModelSelector(apiRef), [apiRef]);\n  const getSortedRows = React.useCallback(() => {\n    const sortedRows = gridSortedRowEntriesSelector(apiRef);\n    return sortedRows.map(row => row.model);\n  }, [apiRef]);\n  const getSortedRowIds = React.useCallback(() => gridSortedRowIdsSelector(apiRef), [apiRef]);\n  const getRowIdFromRowIndex = React.useCallback(index => apiRef.current.getSortedRowIds()[index], [apiRef]);\n  const sortApi = {\n    getSortModel,\n    getSortedRows,\n    getSortedRowIds,\n    getRowIdFromRowIndex,\n    setSortModel,\n    sortColumn,\n    applySorting\n  };\n  useGridApiMethod(apiRef, sortApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const sortModelToExport = gridSortModelSelector(apiRef);\n    const shouldExportSortModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.sortModel != null ||\n    // Always export if the model has been initialized\n    props.initialState?.sorting?.sortModel != null ||\n    // Export if the model is not empty\n    sortModelToExport.length > 0;\n    if (!shouldExportSortModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      sorting: {\n        sortModel: sortModelToExport\n      }\n    });\n  }, [apiRef, props.sortModel, props.initialState?.sorting?.sortModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const sortModel = context.stateToRestore.sorting?.sortModel;\n    if (sortModel == null) {\n      return params;\n    }\n    apiRef.current.setState(mergeStateWithSortModel(sortModel, props.disableMultipleColumnsSorting));\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.applySorting]\n    });\n  }, [apiRef, props.disableMultipleColumnsSorting]);\n  const flatSortingMethod = React.useCallback(params => {\n    const rowTree = gridRowTreeSelector(apiRef);\n    const rootGroupNode = rowTree[GRID_ROOT_GROUP_ID];\n    const sortedChildren = params.sortRowList ? params.sortRowList(rootGroupNode.children.map(childId => rowTree[childId])) : [...rootGroupNode.children];\n    if (rootGroupNode.footerId != null) {\n      sortedChildren.push(rootGroupNode.footerId);\n    }\n    return sortedChildren;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'sorting', flatSortingMethod);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnHeaderClick = React.useCallback((_ref, event) => {\n    let {\n      field,\n      colDef\n    } = _ref;\n    if (!colDef.sortable || props.disableColumnSorting) {\n      return;\n    }\n    const allowMultipleSorting = event.shiftKey || event.metaKey || event.ctrlKey;\n    sortColumn(field, undefined, allowMultipleSorting);\n  }, [sortColumn, props.disableColumnSorting]);\n  const handleColumnHeaderKeyDown = React.useCallback((_ref2, event) => {\n    let {\n      field,\n      colDef\n    } = _ref2;\n    if (!colDef.sortable || props.disableColumnSorting) {\n      return;\n    }\n    // Ctrl + Enter opens the column menu\n    if (event.key === 'Enter' && !event.ctrlKey && !event.metaKey) {\n      sortColumn(field, undefined, event.shiftKey);\n    }\n  }, [sortColumn, props.disableColumnSorting]);\n  const handleColumnsChange = React.useCallback(() => {\n    // When the columns change we check that the sorted columns are still part of the dataset\n    const sortModel = gridSortModelSelector(apiRef);\n    const latestColumns = gridColumnLookupSelector(apiRef);\n    if (sortModel.length > 0) {\n      const newModel = sortModel.filter(sortItem => latestColumns[sortItem.field]);\n      if (newModel.length < sortModel.length) {\n        apiRef.current.setSortModel(newModel);\n      }\n    }\n  }, [apiRef]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'sorting') {\n      apiRef.current.applySorting();\n    }\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridApiEventHandler(apiRef, 'columnHeaderClick', handleColumnHeaderClick);\n  useGridApiEventHandler(apiRef, 'columnHeaderKeyDown', handleColumnHeaderKeyDown);\n  useGridApiEventHandler(apiRef, 'rowsSet', apiRef.current.applySorting);\n  useGridApiEventHandler(apiRef, 'columnsChange', handleColumnsChange);\n  useGridApiEventHandler(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    apiRef.current.applySorting();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.sortModel !== undefined) {\n      apiRef.current.setSortModel(props.sortModel);\n    }\n  }, [apiRef, props.sortModel]);\n};", "map": {"version": 3, "names": ["_extends", "React", "unstable_useEnhancedEffect", "useEnhancedEffect", "useGridApiEventHandler", "useGridApiMethod", "useGridLogger", "gridColumnLookupSelector", "gridSortedRowEntriesSelector", "gridSortedRowIdsSelector", "gridSortModelSelector", "GRID_ROOT_GROUP_ID", "gridRowTreeSelector", "useFirstRender", "useGridRegisterStrategyProcessor", "GRID_DEFAULT_STRATEGY", "buildAggregatedSortingApplier", "mergeStateWithSortModel", "getNextGridSortDirection", "sanitizeSortModel", "useGridRegisterPipeProcessor", "getTreeNodeDescendants", "sortingStateInitializer", "state", "props", "sortModel", "initialState", "sorting", "disableMultipleColumnsSorting", "sortedRows", "useGridSorting", "apiRef", "logger", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onSortModelChange", "stateSelector", "changeEvent", "upsertSortModel", "useCallback", "field", "sortItem", "existingIdx", "findIndex", "c", "newSortModel", "sort", "splice", "createSortItem", "col", "directionOverride", "existing", "find", "nextSort", "undefined", "sortingOrder", "addColumnMenuItem", "columnMenuItems", "colDef", "sortable", "disableColumnSorting", "some", "item", "applySorting", "setState", "sortingMode", "debug", "instanceId", "sortRowList", "applyStrategyProcessor", "publishEvent", "forceUpdate", "setSortModel", "model", "currentModel", "sortColumn", "direction", "allowMultipleSorting", "column", "getColumn", "getSortModel", "getSortedRows", "map", "row", "getSortedRowIds", "getRowIdFromRowIndex", "index", "sortApi", "stateExportPreProcessing", "prevState", "context", "sortModelToExport", "shouldExportSortModel", "exportOnlyDirtyModels", "length", "stateRestorePreProcessing", "params", "stateToRestore", "callbacks", "flatSortingMethod", "rowTree", "rootGroupNode", "sorted<PERSON><PERSON><PERSON><PERSON>", "children", "childId", "footerId", "push", "handleColumnHeaderClick", "_ref", "event", "shift<PERSON>ey", "metaKey", "ctrl<PERSON>ey", "handleColumnHeaderKeyDown", "_ref2", "key", "handleColumnsChange", "latestColumns", "newModel", "filter", "handleStrategyProcessorChange", "methodName"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/sorting/useGridSorting.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnLookupSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridSortedRowEntriesSelector, gridSortedRowIdsSelector, gridSortModelSelector } from \"./gridSortingSelector.js\";\nimport { GRID_ROOT_GROUP_ID, gridRowTreeSelector } from \"../rows/index.js\";\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nimport { useGridRegisterStrategyProcessor, GRID_DEFAULT_STRATEGY } from \"../../core/strategyProcessing/index.js\";\nimport { buildAggregatedSortingApplier, mergeStateWithSortModel, getNextGridSortDirection, sanitizeSortModel } from \"./gridSortingUtils.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { getTreeNodeDescendants } from \"../rows/gridRowsUtils.js\";\nexport const sortingStateInitializer = (state, props) => {\n  const sortModel = props.sortModel ?? props.initialState?.sorting?.sortModel ?? [];\n  return _extends({}, state, {\n    sorting: {\n      sortModel: sanitizeSortModel(sortModel, props.disableMultipleColumnsSorting),\n      sortedRows: []\n    }\n  });\n};\n\n/**\n * @requires useGridRows (event)\n * @requires useGridColumns (event)\n */\nexport const useGridSorting = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSorting');\n  apiRef.current.registerControlState({\n    stateId: 'sortModel',\n    propModel: props.sortModel,\n    propOnChange: props.onSortModelChange,\n    stateSelector: gridSortModelSelector,\n    changeEvent: 'sortModelChange'\n  });\n  const upsertSortModel = React.useCallback((field, sortItem) => {\n    const sortModel = gridSortModelSelector(apiRef);\n    const existingIdx = sortModel.findIndex(c => c.field === field);\n    let newSortModel = [...sortModel];\n    if (existingIdx > -1) {\n      if (sortItem?.sort == null) {\n        newSortModel.splice(existingIdx, 1);\n      } else {\n        newSortModel.splice(existingIdx, 1, sortItem);\n      }\n    } else {\n      newSortModel = [...sortModel, sortItem];\n    }\n    return newSortModel;\n  }, [apiRef]);\n  const createSortItem = React.useCallback((col, directionOverride) => {\n    const sortModel = gridSortModelSelector(apiRef);\n    const existing = sortModel.find(c => c.field === col.field);\n    if (existing) {\n      const nextSort = directionOverride === undefined ? getNextGridSortDirection(col.sortingOrder ?? props.sortingOrder, existing.sort) : directionOverride;\n      return nextSort === undefined ? undefined : _extends({}, existing, {\n        sort: nextSort\n      });\n    }\n    return {\n      field: col.field,\n      sort: directionOverride === undefined ? getNextGridSortDirection(col.sortingOrder ?? props.sortingOrder) : directionOverride\n    };\n  }, [apiRef, props.sortingOrder]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.sortable === false || props.disableColumnSorting) {\n      return columnMenuItems;\n    }\n    const sortingOrder = colDef.sortingOrder || props.sortingOrder;\n    if (sortingOrder.some(item => !!item)) {\n      return [...columnMenuItems, 'columnMenuSortItem'];\n    }\n    return columnMenuItems;\n  }, [props.sortingOrder, props.disableColumnSorting]);\n\n  /**\n   * API METHODS\n   */\n  const applySorting = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      if (props.sortingMode === 'server') {\n        logger.debug('Skipping sorting rows as sortingMode = server');\n        return _extends({}, state, {\n          sorting: _extends({}, state.sorting, {\n            sortedRows: getTreeNodeDescendants(gridRowTreeSelector(apiRef), GRID_ROOT_GROUP_ID, false)\n          })\n        });\n      }\n      const sortModel = gridSortModelSelector(state, apiRef.current.instanceId);\n      const sortRowList = buildAggregatedSortingApplier(sortModel, apiRef);\n      const sortedRows = apiRef.current.applyStrategyProcessor('sorting', {\n        sortRowList\n      });\n      return _extends({}, state, {\n        sorting: _extends({}, state.sorting, {\n          sortedRows\n        })\n      });\n    });\n    apiRef.current.publishEvent('sortedRowsSet');\n    apiRef.current.forceUpdate();\n  }, [apiRef, logger, props.sortingMode]);\n  const setSortModel = React.useCallback(model => {\n    const currentModel = gridSortModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug(`Setting sort model`);\n      apiRef.current.setState(mergeStateWithSortModel(model, props.disableMultipleColumnsSorting));\n      apiRef.current.forceUpdate();\n      apiRef.current.applySorting();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsSorting]);\n  const sortColumn = React.useCallback((field, direction, allowMultipleSorting) => {\n    const column = apiRef.current.getColumn(field);\n    const sortItem = createSortItem(column, direction);\n    let sortModel;\n    if (!allowMultipleSorting || props.disableMultipleColumnsSorting) {\n      sortModel = sortItem?.sort == null ? [] : [sortItem];\n    } else {\n      sortModel = upsertSortModel(column.field, sortItem);\n    }\n    apiRef.current.setSortModel(sortModel);\n  }, [apiRef, upsertSortModel, createSortItem, props.disableMultipleColumnsSorting]);\n  const getSortModel = React.useCallback(() => gridSortModelSelector(apiRef), [apiRef]);\n  const getSortedRows = React.useCallback(() => {\n    const sortedRows = gridSortedRowEntriesSelector(apiRef);\n    return sortedRows.map(row => row.model);\n  }, [apiRef]);\n  const getSortedRowIds = React.useCallback(() => gridSortedRowIdsSelector(apiRef), [apiRef]);\n  const getRowIdFromRowIndex = React.useCallback(index => apiRef.current.getSortedRowIds()[index], [apiRef]);\n  const sortApi = {\n    getSortModel,\n    getSortedRows,\n    getSortedRowIds,\n    getRowIdFromRowIndex,\n    setSortModel,\n    sortColumn,\n    applySorting\n  };\n  useGridApiMethod(apiRef, sortApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const sortModelToExport = gridSortModelSelector(apiRef);\n    const shouldExportSortModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.sortModel != null ||\n    // Always export if the model has been initialized\n    props.initialState?.sorting?.sortModel != null ||\n    // Export if the model is not empty\n    sortModelToExport.length > 0;\n    if (!shouldExportSortModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      sorting: {\n        sortModel: sortModelToExport\n      }\n    });\n  }, [apiRef, props.sortModel, props.initialState?.sorting?.sortModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const sortModel = context.stateToRestore.sorting?.sortModel;\n    if (sortModel == null) {\n      return params;\n    }\n    apiRef.current.setState(mergeStateWithSortModel(sortModel, props.disableMultipleColumnsSorting));\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.applySorting]\n    });\n  }, [apiRef, props.disableMultipleColumnsSorting]);\n  const flatSortingMethod = React.useCallback(params => {\n    const rowTree = gridRowTreeSelector(apiRef);\n    const rootGroupNode = rowTree[GRID_ROOT_GROUP_ID];\n    const sortedChildren = params.sortRowList ? params.sortRowList(rootGroupNode.children.map(childId => rowTree[childId])) : [...rootGroupNode.children];\n    if (rootGroupNode.footerId != null) {\n      sortedChildren.push(rootGroupNode.footerId);\n    }\n    return sortedChildren;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'sorting', flatSortingMethod);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnHeaderClick = React.useCallback(({\n    field,\n    colDef\n  }, event) => {\n    if (!colDef.sortable || props.disableColumnSorting) {\n      return;\n    }\n    const allowMultipleSorting = event.shiftKey || event.metaKey || event.ctrlKey;\n    sortColumn(field, undefined, allowMultipleSorting);\n  }, [sortColumn, props.disableColumnSorting]);\n  const handleColumnHeaderKeyDown = React.useCallback(({\n    field,\n    colDef\n  }, event) => {\n    if (!colDef.sortable || props.disableColumnSorting) {\n      return;\n    }\n    // Ctrl + Enter opens the column menu\n    if (event.key === 'Enter' && !event.ctrlKey && !event.metaKey) {\n      sortColumn(field, undefined, event.shiftKey);\n    }\n  }, [sortColumn, props.disableColumnSorting]);\n  const handleColumnsChange = React.useCallback(() => {\n    // When the columns change we check that the sorted columns are still part of the dataset\n    const sortModel = gridSortModelSelector(apiRef);\n    const latestColumns = gridColumnLookupSelector(apiRef);\n    if (sortModel.length > 0) {\n      const newModel = sortModel.filter(sortItem => latestColumns[sortItem.field]);\n      if (newModel.length < sortModel.length) {\n        apiRef.current.setSortModel(newModel);\n      }\n    }\n  }, [apiRef]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'sorting') {\n      apiRef.current.applySorting();\n    }\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridApiEventHandler(apiRef, 'columnHeaderClick', handleColumnHeaderClick);\n  useGridApiEventHandler(apiRef, 'columnHeaderKeyDown', handleColumnHeaderKeyDown);\n  useGridApiEventHandler(apiRef, 'rowsSet', apiRef.current.applySorting);\n  useGridApiEventHandler(apiRef, 'columnsChange', handleColumnsChange);\n  useGridApiEventHandler(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    apiRef.current.applySorting();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.sortModel !== undefined) {\n      apiRef.current.setSortModel(props.sortModel);\n    }\n  }, [apiRef, props.sortModel]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC5E,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,4BAA4B,EAAEC,wBAAwB,EAAEC,qBAAqB,QAAQ,0BAA0B;AACxH,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,kBAAkB;AAC1E,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,gCAAgC,EAAEC,qBAAqB,QAAQ,wCAAwC;AAChH,SAASC,6BAA6B,EAAEC,uBAAuB,EAAEC,wBAAwB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAC3I,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EACvD,MAAMC,SAAS,GAAGD,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,YAAY,EAAEC,OAAO,EAAEF,SAAS,IAAI,EAAE;EACjF,OAAOzB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IACzBI,OAAO,EAAE;MACPF,SAAS,EAAEN,iBAAiB,CAACM,SAAS,EAAED,KAAK,CAACI,6BAA6B,CAAC;MAC5EC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAACC,MAAM,EAAEP,KAAK,KAAK;EAC/C,MAAMQ,MAAM,GAAG1B,aAAa,CAACyB,MAAM,EAAE,gBAAgB,CAAC;EACtDA,MAAM,CAACE,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAEZ,KAAK,CAACC,SAAS;IAC1BY,YAAY,EAAEb,KAAK,CAACc,iBAAiB;IACrCC,aAAa,EAAE7B,qBAAqB;IACpC8B,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,eAAe,GAAGxC,KAAK,CAACyC,WAAW,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC7D,MAAMnB,SAAS,GAAGf,qBAAqB,CAACqB,MAAM,CAAC;IAC/C,MAAMc,WAAW,GAAGpB,SAAS,CAACqB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACJ,KAAK,KAAKA,KAAK,CAAC;IAC/D,IAAIK,YAAY,GAAG,CAAC,GAAGvB,SAAS,CAAC;IACjC,IAAIoB,WAAW,GAAG,CAAC,CAAC,EAAE;MACpB,IAAID,QAAQ,EAAEK,IAAI,IAAI,IAAI,EAAE;QAC1BD,YAAY,CAACE,MAAM,CAACL,WAAW,EAAE,CAAC,CAAC;MACrC,CAAC,MAAM;QACLG,YAAY,CAACE,MAAM,CAACL,WAAW,EAAE,CAAC,EAAED,QAAQ,CAAC;MAC/C;IACF,CAAC,MAAM;MACLI,YAAY,GAAG,CAAC,GAAGvB,SAAS,EAAEmB,QAAQ,CAAC;IACzC;IACA,OAAOI,YAAY;EACrB,CAAC,EAAE,CAACjB,MAAM,CAAC,CAAC;EACZ,MAAMoB,cAAc,GAAGlD,KAAK,CAACyC,WAAW,CAAC,CAACU,GAAG,EAAEC,iBAAiB,KAAK;IACnE,MAAM5B,SAAS,GAAGf,qBAAqB,CAACqB,MAAM,CAAC;IAC/C,MAAMuB,QAAQ,GAAG7B,SAAS,CAAC8B,IAAI,CAACR,CAAC,IAAIA,CAAC,CAACJ,KAAK,KAAKS,GAAG,CAACT,KAAK,CAAC;IAC3D,IAAIW,QAAQ,EAAE;MACZ,MAAME,QAAQ,GAAGH,iBAAiB,KAAKI,SAAS,GAAGvC,wBAAwB,CAACkC,GAAG,CAACM,YAAY,IAAIlC,KAAK,CAACkC,YAAY,EAAEJ,QAAQ,CAACL,IAAI,CAAC,GAAGI,iBAAiB;MACtJ,OAAOG,QAAQ,KAAKC,SAAS,GAAGA,SAAS,GAAGzD,QAAQ,CAAC,CAAC,CAAC,EAAEsD,QAAQ,EAAE;QACjEL,IAAI,EAAEO;MACR,CAAC,CAAC;IACJ;IACA,OAAO;MACLb,KAAK,EAAES,GAAG,CAACT,KAAK;MAChBM,IAAI,EAAEI,iBAAiB,KAAKI,SAAS,GAAGvC,wBAAwB,CAACkC,GAAG,CAACM,YAAY,IAAIlC,KAAK,CAACkC,YAAY,CAAC,GAAGL;IAC7G,CAAC;EACH,CAAC,EAAE,CAACtB,MAAM,EAAEP,KAAK,CAACkC,YAAY,CAAC,CAAC;EAChC,MAAMC,iBAAiB,GAAG1D,KAAK,CAACyC,WAAW,CAAC,CAACkB,eAAe,EAAEC,MAAM,KAAK;IACvE,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACC,QAAQ,KAAK,KAAK,IAAItC,KAAK,CAACuC,oBAAoB,EAAE;MAC7E,OAAOH,eAAe;IACxB;IACA,MAAMF,YAAY,GAAGG,MAAM,CAACH,YAAY,IAAIlC,KAAK,CAACkC,YAAY;IAC9D,IAAIA,YAAY,CAACM,IAAI,CAACC,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,EAAE;MACrC,OAAO,CAAC,GAAGL,eAAe,EAAE,oBAAoB,CAAC;IACnD;IACA,OAAOA,eAAe;EACxB,CAAC,EAAE,CAACpC,KAAK,CAACkC,YAAY,EAAElC,KAAK,CAACuC,oBAAoB,CAAC,CAAC;;EAEpD;AACF;AACA;EACE,MAAMG,YAAY,GAAGjE,KAAK,CAACyC,WAAW,CAAC,MAAM;IAC3CX,MAAM,CAACE,OAAO,CAACkC,QAAQ,CAAC5C,KAAK,IAAI;MAC/B,IAAIC,KAAK,CAAC4C,WAAW,KAAK,QAAQ,EAAE;QAClCpC,MAAM,CAACqC,KAAK,CAAC,+CAA+C,CAAC;QAC7D,OAAOrE,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;UACzBI,OAAO,EAAE3B,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,CAACI,OAAO,EAAE;YACnCE,UAAU,EAAER,sBAAsB,CAACT,mBAAmB,CAACmB,MAAM,CAAC,EAAEpB,kBAAkB,EAAE,KAAK;UAC3F,CAAC;QACH,CAAC,CAAC;MACJ;MACA,MAAMc,SAAS,GAAGf,qBAAqB,CAACa,KAAK,EAAEQ,MAAM,CAACE,OAAO,CAACqC,UAAU,CAAC;MACzE,MAAMC,WAAW,GAAGvD,6BAA6B,CAACS,SAAS,EAAEM,MAAM,CAAC;MACpE,MAAMF,UAAU,GAAGE,MAAM,CAACE,OAAO,CAACuC,sBAAsB,CAAC,SAAS,EAAE;QAClED;MACF,CAAC,CAAC;MACF,OAAOvE,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;QACzBI,OAAO,EAAE3B,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,CAACI,OAAO,EAAE;UACnCE;QACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IACFE,MAAM,CAACE,OAAO,CAACwC,YAAY,CAAC,eAAe,CAAC;IAC5C1C,MAAM,CAACE,OAAO,CAACyC,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAAC3C,MAAM,EAAEC,MAAM,EAAER,KAAK,CAAC4C,WAAW,CAAC,CAAC;EACvC,MAAMO,YAAY,GAAG1E,KAAK,CAACyC,WAAW,CAACkC,KAAK,IAAI;IAC9C,MAAMC,YAAY,GAAGnE,qBAAqB,CAACqB,MAAM,CAAC;IAClD,IAAI8C,YAAY,KAAKD,KAAK,EAAE;MAC1B5C,MAAM,CAACqC,KAAK,CAAC,oBAAoB,CAAC;MAClCtC,MAAM,CAACE,OAAO,CAACkC,QAAQ,CAAClD,uBAAuB,CAAC2D,KAAK,EAAEpD,KAAK,CAACI,6BAA6B,CAAC,CAAC;MAC5FG,MAAM,CAACE,OAAO,CAACyC,WAAW,CAAC,CAAC;MAC5B3C,MAAM,CAACE,OAAO,CAACiC,YAAY,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACnC,MAAM,EAAEC,MAAM,EAAER,KAAK,CAACI,6BAA6B,CAAC,CAAC;EACzD,MAAMkD,UAAU,GAAG7E,KAAK,CAACyC,WAAW,CAAC,CAACC,KAAK,EAAEoC,SAAS,EAAEC,oBAAoB,KAAK;IAC/E,MAAMC,MAAM,GAAGlD,MAAM,CAACE,OAAO,CAACiD,SAAS,CAACvC,KAAK,CAAC;IAC9C,MAAMC,QAAQ,GAAGO,cAAc,CAAC8B,MAAM,EAAEF,SAAS,CAAC;IAClD,IAAItD,SAAS;IACb,IAAI,CAACuD,oBAAoB,IAAIxD,KAAK,CAACI,6BAA6B,EAAE;MAChEH,SAAS,GAAGmB,QAAQ,EAAEK,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,CAACL,QAAQ,CAAC;IACtD,CAAC,MAAM;MACLnB,SAAS,GAAGgB,eAAe,CAACwC,MAAM,CAACtC,KAAK,EAAEC,QAAQ,CAAC;IACrD;IACAb,MAAM,CAACE,OAAO,CAAC0C,YAAY,CAAClD,SAAS,CAAC;EACxC,CAAC,EAAE,CAACM,MAAM,EAAEU,eAAe,EAAEU,cAAc,EAAE3B,KAAK,CAACI,6BAA6B,CAAC,CAAC;EAClF,MAAMuD,YAAY,GAAGlF,KAAK,CAACyC,WAAW,CAAC,MAAMhC,qBAAqB,CAACqB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACrF,MAAMqD,aAAa,GAAGnF,KAAK,CAACyC,WAAW,CAAC,MAAM;IAC5C,MAAMb,UAAU,GAAGrB,4BAA4B,CAACuB,MAAM,CAAC;IACvD,OAAOF,UAAU,CAACwD,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACV,KAAK,CAAC;EACzC,CAAC,EAAE,CAAC7C,MAAM,CAAC,CAAC;EACZ,MAAMwD,eAAe,GAAGtF,KAAK,CAACyC,WAAW,CAAC,MAAMjC,wBAAwB,CAACsB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC3F,MAAMyD,oBAAoB,GAAGvF,KAAK,CAACyC,WAAW,CAAC+C,KAAK,IAAI1D,MAAM,CAACE,OAAO,CAACsD,eAAe,CAAC,CAAC,CAACE,KAAK,CAAC,EAAE,CAAC1D,MAAM,CAAC,CAAC;EAC1G,MAAM2D,OAAO,GAAG;IACdP,YAAY;IACZC,aAAa;IACbG,eAAe;IACfC,oBAAoB;IACpBb,YAAY;IACZG,UAAU;IACVZ;EACF,CAAC;EACD7D,gBAAgB,CAAC0B,MAAM,EAAE2D,OAAO,EAAE,QAAQ,CAAC;;EAE3C;AACF;AACA;EACE,MAAMC,wBAAwB,GAAG1F,KAAK,CAACyC,WAAW,CAAC,CAACkD,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMC,iBAAiB,GAAGpF,qBAAqB,CAACqB,MAAM,CAAC;IACvD,MAAMgE,qBAAqB;IAC3B;IACA,CAACF,OAAO,CAACG,qBAAqB;IAC9B;IACAxE,KAAK,CAACC,SAAS,IAAI,IAAI;IACvB;IACAD,KAAK,CAACE,YAAY,EAAEC,OAAO,EAAEF,SAAS,IAAI,IAAI;IAC9C;IACAqE,iBAAiB,CAACG,MAAM,GAAG,CAAC;IAC5B,IAAI,CAACF,qBAAqB,EAAE;MAC1B,OAAOH,SAAS;IAClB;IACA,OAAO5F,QAAQ,CAAC,CAAC,CAAC,EAAE4F,SAAS,EAAE;MAC7BjE,OAAO,EAAE;QACPF,SAAS,EAAEqE;MACb;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/D,MAAM,EAAEP,KAAK,CAACC,SAAS,EAAED,KAAK,CAACE,YAAY,EAAEC,OAAO,EAAEF,SAAS,CAAC,CAAC;EACrE,MAAMyE,yBAAyB,GAAGjG,KAAK,CAACyC,WAAW,CAAC,CAACyD,MAAM,EAAEN,OAAO,KAAK;IACvE,MAAMpE,SAAS,GAAGoE,OAAO,CAACO,cAAc,CAACzE,OAAO,EAAEF,SAAS;IAC3D,IAAIA,SAAS,IAAI,IAAI,EAAE;MACrB,OAAO0E,MAAM;IACf;IACApE,MAAM,CAACE,OAAO,CAACkC,QAAQ,CAAClD,uBAAuB,CAACQ,SAAS,EAAED,KAAK,CAACI,6BAA6B,CAAC,CAAC;IAChG,OAAO5B,QAAQ,CAAC,CAAC,CAAC,EAAEmG,MAAM,EAAE;MAC1BE,SAAS,EAAE,CAAC,GAAGF,MAAM,CAACE,SAAS,EAAEtE,MAAM,CAACE,OAAO,CAACiC,YAAY;IAC9D,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnC,MAAM,EAAEP,KAAK,CAACI,6BAA6B,CAAC,CAAC;EACjD,MAAM0E,iBAAiB,GAAGrG,KAAK,CAACyC,WAAW,CAACyD,MAAM,IAAI;IACpD,MAAMI,OAAO,GAAG3F,mBAAmB,CAACmB,MAAM,CAAC;IAC3C,MAAMyE,aAAa,GAAGD,OAAO,CAAC5F,kBAAkB,CAAC;IACjD,MAAM8F,cAAc,GAAGN,MAAM,CAAC5B,WAAW,GAAG4B,MAAM,CAAC5B,WAAW,CAACiC,aAAa,CAACE,QAAQ,CAACrB,GAAG,CAACsB,OAAO,IAAIJ,OAAO,CAACI,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGH,aAAa,CAACE,QAAQ,CAAC;IACrJ,IAAIF,aAAa,CAACI,QAAQ,IAAI,IAAI,EAAE;MAClCH,cAAc,CAACI,IAAI,CAACL,aAAa,CAACI,QAAQ,CAAC;IAC7C;IACA,OAAOH,cAAc;EACvB,CAAC,EAAE,CAAC1E,MAAM,CAAC,CAAC;EACZX,4BAA4B,CAACW,MAAM,EAAE,aAAa,EAAE4D,wBAAwB,CAAC;EAC7EvE,4BAA4B,CAACW,MAAM,EAAE,cAAc,EAAEmE,yBAAyB,CAAC;EAC/EpF,gCAAgC,CAACiB,MAAM,EAAEhB,qBAAqB,EAAE,SAAS,EAAEuF,iBAAiB,CAAC;;EAE7F;AACF;AACA;EACE,MAAMQ,uBAAuB,GAAG7G,KAAK,CAACyC,WAAW,CAAC,CAAAqE,IAAA,EAG/CC,KAAK,KAAK;IAAA,IAHsC;MACjDrE,KAAK;MACLkB;IACF,CAAC,GAAAkD,IAAA;IACC,IAAI,CAAClD,MAAM,CAACC,QAAQ,IAAItC,KAAK,CAACuC,oBAAoB,EAAE;MAClD;IACF;IACA,MAAMiB,oBAAoB,GAAGgC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACE,OAAO,IAAIF,KAAK,CAACG,OAAO;IAC7ErC,UAAU,CAACnC,KAAK,EAAEc,SAAS,EAAEuB,oBAAoB,CAAC;EACpD,CAAC,EAAE,CAACF,UAAU,EAAEtD,KAAK,CAACuC,oBAAoB,CAAC,CAAC;EAC5C,MAAMqD,yBAAyB,GAAGnH,KAAK,CAACyC,WAAW,CAAC,CAAA2E,KAAA,EAGjDL,KAAK,KAAK;IAAA,IAHwC;MACnDrE,KAAK;MACLkB;IACF,CAAC,GAAAwD,KAAA;IACC,IAAI,CAACxD,MAAM,CAACC,QAAQ,IAAItC,KAAK,CAACuC,oBAAoB,EAAE;MAClD;IACF;IACA;IACA,IAAIiD,KAAK,CAACM,GAAG,KAAK,OAAO,IAAI,CAACN,KAAK,CAACG,OAAO,IAAI,CAACH,KAAK,CAACE,OAAO,EAAE;MAC7DpC,UAAU,CAACnC,KAAK,EAAEc,SAAS,EAAEuD,KAAK,CAACC,QAAQ,CAAC;IAC9C;EACF,CAAC,EAAE,CAACnC,UAAU,EAAEtD,KAAK,CAACuC,oBAAoB,CAAC,CAAC;EAC5C,MAAMwD,mBAAmB,GAAGtH,KAAK,CAACyC,WAAW,CAAC,MAAM;IAClD;IACA,MAAMjB,SAAS,GAAGf,qBAAqB,CAACqB,MAAM,CAAC;IAC/C,MAAMyF,aAAa,GAAGjH,wBAAwB,CAACwB,MAAM,CAAC;IACtD,IAAIN,SAAS,CAACwE,MAAM,GAAG,CAAC,EAAE;MACxB,MAAMwB,QAAQ,GAAGhG,SAAS,CAACiG,MAAM,CAAC9E,QAAQ,IAAI4E,aAAa,CAAC5E,QAAQ,CAACD,KAAK,CAAC,CAAC;MAC5E,IAAI8E,QAAQ,CAACxB,MAAM,GAAGxE,SAAS,CAACwE,MAAM,EAAE;QACtClE,MAAM,CAACE,OAAO,CAAC0C,YAAY,CAAC8C,QAAQ,CAAC;MACvC;IACF;EACF,CAAC,EAAE,CAAC1F,MAAM,CAAC,CAAC;EACZ,MAAM4F,6BAA6B,GAAG1H,KAAK,CAACyC,WAAW,CAACkF,UAAU,IAAI;IACpE,IAAIA,UAAU,KAAK,SAAS,EAAE;MAC5B7F,MAAM,CAACE,OAAO,CAACiC,YAAY,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC;EACZX,4BAA4B,CAACW,MAAM,EAAE,YAAY,EAAE4B,iBAAiB,CAAC;EACrEvD,sBAAsB,CAAC2B,MAAM,EAAE,mBAAmB,EAAE+E,uBAAuB,CAAC;EAC5E1G,sBAAsB,CAAC2B,MAAM,EAAE,qBAAqB,EAAEqF,yBAAyB,CAAC;EAChFhH,sBAAsB,CAAC2B,MAAM,EAAE,SAAS,EAAEA,MAAM,CAACE,OAAO,CAACiC,YAAY,CAAC;EACtE9D,sBAAsB,CAAC2B,MAAM,EAAE,eAAe,EAAEwF,mBAAmB,CAAC;EACpEnH,sBAAsB,CAAC2B,MAAM,EAAE,+BAA+B,EAAE4F,6BAA6B,CAAC;;EAE9F;AACF;AACA;EACE9G,cAAc,CAAC,MAAM;IACnBkB,MAAM,CAACE,OAAO,CAACiC,YAAY,CAAC,CAAC;EAC/B,CAAC,CAAC;;EAEF;AACF;AACA;EACE/D,iBAAiB,CAAC,MAAM;IACtB,IAAIqB,KAAK,CAACC,SAAS,KAAKgC,SAAS,EAAE;MACjC1B,MAAM,CAACE,OAAO,CAAC0C,YAAY,CAACnD,KAAK,CAACC,SAAS,CAAC;IAC9C;EACF,CAAC,EAAE,CAACM,MAAM,EAAEP,KAAK,CAACC,SAAS,CAAC,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}