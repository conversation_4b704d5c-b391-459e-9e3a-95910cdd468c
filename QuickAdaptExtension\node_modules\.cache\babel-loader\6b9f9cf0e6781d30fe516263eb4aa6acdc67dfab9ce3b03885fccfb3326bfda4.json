{"ast": null, "code": "import { createSelector } from \"../../../utils/createSelector.js\";\nexport const COMPACT_DENSITY_FACTOR = 0.7;\nexport const COMFORTABLE_DENSITY_FACTOR = 1.3;\nconst DENSITY_FACTORS = {\n  compact: COMPACT_DENSITY_FACTOR,\n  comfortable: COMFORTABLE_DENSITY_FACTOR,\n  standard: 1\n};\nexport const gridDensitySelector = state => state.density;\nexport const gridDensityFactorSelector = createSelector(gridDensitySelector, density => DENSITY_FACTORS[density]);", "map": {"version": 3, "names": ["createSelector", "COMPACT_DENSITY_FACTOR", "COMFORTABLE_DENSITY_FACTOR", "DENSITY_FACTORS", "compact", "comfortable", "standard", "gridDensitySelector", "state", "density", "gridDensityFactorSelector"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/density/densitySelector.js"], "sourcesContent": ["import { createSelector } from \"../../../utils/createSelector.js\";\nexport const COMPACT_DENSITY_FACTOR = 0.7;\nexport const COMFORTABLE_DENSITY_FACTOR = 1.3;\nconst DENSITY_FACTORS = {\n  compact: COMPACT_DENSITY_FACTOR,\n  comfortable: COMFORTABLE_DENSITY_FACTOR,\n  standard: 1\n};\nexport const gridDensitySelector = state => state.density;\nexport const gridDensityFactorSelector = createSelector(gridDensitySelector, density => DENSITY_FACTORS[density]);"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kCAAkC;AACjE,OAAO,MAAMC,sBAAsB,GAAG,GAAG;AACzC,OAAO,MAAMC,0BAA0B,GAAG,GAAG;AAC7C,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAEH,sBAAsB;EAC/BI,WAAW,EAAEH,0BAA0B;EACvCI,QAAQ,EAAE;AACZ,CAAC;AACD,OAAO,MAAMC,mBAAmB,GAAGC,KAAK,IAAIA,KAAK,CAACC,OAAO;AACzD,OAAO,MAAMC,yBAAyB,GAAGV,cAAc,CAACO,mBAAmB,EAAEE,OAAO,IAAIN,eAAe,CAACM,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}