{"ast": null, "code": "import Uint8Array from './_Uint8Array.js';\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\nexport default cloneArrayBuffer;", "map": {"version": 3, "names": ["Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arrayBuffer", "result", "constructor", "byteLength", "set"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash-es/_cloneArrayBuffer.js"], "sourcesContent": ["import Uint8Array from './_Uint8Array.js';\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\nexport default cloneArrayBuffer;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EACrC,IAAIC,MAAM,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,UAAU,CAAC;EAChE,IAAIL,UAAU,CAACG,MAAM,CAAC,CAACG,GAAG,CAAC,IAAIN,UAAU,CAACE,WAAW,CAAC,CAAC;EACvD,OAAOC,MAAM;AACf;AAEA,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}