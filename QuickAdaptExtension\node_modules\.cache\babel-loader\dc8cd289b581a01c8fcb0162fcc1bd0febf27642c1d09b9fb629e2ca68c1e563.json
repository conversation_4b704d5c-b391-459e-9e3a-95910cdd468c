{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { styled } from '@mui/system';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridConfiguration } from \"../../hooks/utils/useGridConfiguration.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridPanelAnchor = styled('div')({\n  position: 'absolute',\n  top: `var(--DataGrid-headersTotalHeight)`,\n  left: 0\n});\nconst Element = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'Main',\n  overridesResolver: (props, styles) => styles.main\n})({\n  flexGrow: 1,\n  position: 'relative',\n  overflow: 'hidden',\n  display: 'flex',\n  flexDirection: 'column'\n});\nexport const GridMainContainer = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const rootProps = useGridRootProps();\n  const configuration = useGridConfiguration();\n  const ariaAttributes = configuration.hooks.useGridAriaAttributes();\n  return /*#__PURE__*/_jsxs(Element, _extends({\n    ref: ref,\n    ownerState: rootProps,\n    className: props.className,\n    tabIndex: -1\n  }, ariaAttributes, {\n    children: [/*#__PURE__*/_jsx(GridPanelAnchor, {\n      role: \"presentation\",\n      \"data-id\": \"gridPanelAnchor\"\n    }), props.children]\n  }));\n});", "map": {"version": 3, "names": ["_extends", "React", "styled", "useGridRootProps", "useGridConfiguration", "jsx", "_jsx", "jsxs", "_jsxs", "GridPanelAnchor", "position", "top", "left", "Element", "name", "slot", "overridesResolver", "props", "styles", "main", "flexGrow", "overflow", "display", "flexDirection", "Grid<PERSON>ain<PERSON><PERSON><PERSON>", "forwardRef", "ref", "rootProps", "configuration", "ariaAttributes", "hooks", "useGridAriaAttributes", "ownerState", "className", "tabIndex", "children", "role"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/virtualization/GridMainContainer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { styled } from '@mui/system';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridConfiguration } from \"../../hooks/utils/useGridConfiguration.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridPanelAnchor = styled('div')({\n  position: 'absolute',\n  top: `var(--DataGrid-headersTotalHeight)`,\n  left: 0\n});\nconst Element = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'Main',\n  overridesResolver: (props, styles) => styles.main\n})({\n  flexGrow: 1,\n  position: 'relative',\n  overflow: 'hidden',\n  display: 'flex',\n  flexDirection: 'column'\n});\nexport const GridMainContainer = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const rootProps = useGridRootProps();\n  const configuration = useGridConfiguration();\n  const ariaAttributes = configuration.hooks.useGridAriaAttributes();\n  return /*#__PURE__*/_jsxs(Element, _extends({\n    ref: ref,\n    ownerState: rootProps,\n    className: props.className,\n    tabIndex: -1\n  }, ariaAttributes, {\n    children: [/*#__PURE__*/_jsx(GridPanelAnchor, {\n      role: \"presentation\",\n      \"data-id\": \"gridPanelAnchor\"\n    }), props.children]\n  }));\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,eAAe,GAAGP,MAAM,CAAC,KAAK,CAAC,CAAC;EACpCQ,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,oCAAoC;EACzCC,IAAI,EAAE;AACR,CAAC,CAAC;AACF,MAAMC,OAAO,GAAGX,MAAM,CAAC,KAAK,EAAE;EAC5BY,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;AAC/C,CAAC,CAAC,CAAC;EACDC,QAAQ,EAAE,CAAC;EACXV,QAAQ,EAAE,UAAU;EACpBW,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE;AACjB,CAAC,CAAC;AACF,OAAO,MAAMC,iBAAiB,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,CAACR,KAAK,EAAES,GAAG,KAAK;EAC7E,MAAMC,SAAS,GAAGxB,gBAAgB,CAAC,CAAC;EACpC,MAAMyB,aAAa,GAAGxB,oBAAoB,CAAC,CAAC;EAC5C,MAAMyB,cAAc,GAAGD,aAAa,CAACE,KAAK,CAACC,qBAAqB,CAAC,CAAC;EAClE,OAAO,aAAavB,KAAK,CAACK,OAAO,EAAEb,QAAQ,CAAC;IAC1C0B,GAAG,EAAEA,GAAG;IACRM,UAAU,EAAEL,SAAS;IACrBM,SAAS,EAAEhB,KAAK,CAACgB,SAAS;IAC1BC,QAAQ,EAAE,CAAC;EACb,CAAC,EAAEL,cAAc,EAAE;IACjBM,QAAQ,EAAE,CAAC,aAAa7B,IAAI,CAACG,eAAe,EAAE;MAC5C2B,IAAI,EAAE,cAAc;MACpB,SAAS,EAAE;IACb,CAAC,CAAC,EAAEnB,KAAK,CAACkB,QAAQ;EACpB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}