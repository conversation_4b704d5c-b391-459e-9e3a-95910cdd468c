{"ast": null, "code": "// This file is autogenerated. It's used to publish ESM to npm.\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\n// https://github.com/bgrins/TinyColor\n// <PERSON>, MIT License\n\nvar trimLeft = /^\\s+/;\nvar trimRight = /\\s+$/;\nfunction tinycolor(color, opts) {\n  color = color ? color : \"\";\n  opts = opts || {};\n\n  // If input is already a tinycolor, return itself\n  if (color instanceof tinycolor) {\n    return color;\n  }\n  // If we are called as a function, call using new instead\n  if (!(this instanceof tinycolor)) {\n    return new tinycolor(color, opts);\n  }\n  var rgb = inputToRGB(color);\n  this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;\n  this._gradientType = opts.gradientType;\n\n  // Don't let the range of [0,255] come back in [0,1].\n  // Potentially lose a little bit of precision here, but will fix issues where\n  // .5 gets interpreted as half of the total, instead of half of 1\n  // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n  if (this._r < 1) this._r = Math.round(this._r);\n  if (this._g < 1) this._g = Math.round(this._g);\n  if (this._b < 1) this._b = Math.round(this._b);\n  this._ok = rgb.ok;\n}\ntinycolor.prototype = {\n  isDark: function isDark() {\n    return this.getBrightness() < 128;\n  },\n  isLight: function isLight() {\n    return !this.isDark();\n  },\n  isValid: function isValid() {\n    return this._ok;\n  },\n  getOriginalInput: function getOriginalInput() {\n    return this._originalInput;\n  },\n  getFormat: function getFormat() {\n    return this._format;\n  },\n  getAlpha: function getAlpha() {\n    return this._a;\n  },\n  getBrightness: function getBrightness() {\n    //http://www.w3.org/TR/AERT#color-contrast\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  },\n  getLuminance: function getLuminance() {\n    //http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    var rgb = this.toRgb();\n    var RsRGB, GsRGB, BsRGB, R, G, B;\n    RsRGB = rgb.r / 255;\n    GsRGB = rgb.g / 255;\n    BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) R = RsRGB / 12.92;else R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    if (GsRGB <= 0.03928) G = GsRGB / 12.92;else G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    if (BsRGB <= 0.03928) B = BsRGB / 12.92;else B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  },\n  setAlpha: function setAlpha(value) {\n    this._a = boundAlpha(value);\n    this._roundA = Math.round(100 * this._a) / 100;\n    return this;\n  },\n  toHsv: function toHsv() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this._a\n    };\n  },\n  toHsvString: function toHsvString() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    var h = Math.round(hsv.h * 360),\n      s = Math.round(hsv.s * 100),\n      v = Math.round(hsv.v * 100);\n    return this._a == 1 ? \"hsv(\" + h + \", \" + s + \"%, \" + v + \"%)\" : \"hsva(\" + h + \", \" + s + \"%, \" + v + \"%, \" + this._roundA + \")\";\n  },\n  toHsl: function toHsl() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this._a\n    };\n  },\n  toHslString: function toHslString() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    var h = Math.round(hsl.h * 360),\n      s = Math.round(hsl.s * 100),\n      l = Math.round(hsl.l * 100);\n    return this._a == 1 ? \"hsl(\" + h + \", \" + s + \"%, \" + l + \"%)\" : \"hsla(\" + h + \", \" + s + \"%, \" + l + \"%, \" + this._roundA + \")\";\n  },\n  toHex: function toHex(allow3Char) {\n    return rgbToHex(this._r, this._g, this._b, allow3Char);\n  },\n  toHexString: function toHexString(allow3Char) {\n    return \"#\" + this.toHex(allow3Char);\n  },\n  toHex8: function toHex8(allow4Char) {\n    return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);\n  },\n  toHex8String: function toHex8String(allow4Char) {\n    return \"#\" + this.toHex8(allow4Char);\n  },\n  toRgb: function toRgb() {\n    return {\n      r: Math.round(this._r),\n      g: Math.round(this._g),\n      b: Math.round(this._b),\n      a: this._a\n    };\n  },\n  toRgbString: function toRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \")\" : \"rgba(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \", \" + this._roundA + \")\";\n  },\n  toPercentageRgb: function toPercentageRgb() {\n    return {\n      r: Math.round(bound01(this._r, 255) * 100) + \"%\",\n      g: Math.round(bound01(this._g, 255) * 100) + \"%\",\n      b: Math.round(bound01(this._b, 255) * 100) + \"%\",\n      a: this._a\n    };\n  },\n  toPercentageRgbString: function toPercentageRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%)\" : \"rgba(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%, \" + this._roundA + \")\";\n  },\n  toName: function toName() {\n    if (this._a === 0) {\n      return \"transparent\";\n    }\n    if (this._a < 1) {\n      return false;\n    }\n    return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;\n  },\n  toFilter: function toFilter(secondColor) {\n    var hex8String = \"#\" + rgbaToArgbHex(this._r, this._g, this._b, this._a);\n    var secondHex8String = hex8String;\n    var gradientType = this._gradientType ? \"GradientType = 1, \" : \"\";\n    if (secondColor) {\n      var s = tinycolor(secondColor);\n      secondHex8String = \"#\" + rgbaToArgbHex(s._r, s._g, s._b, s._a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\" + gradientType + \"startColorstr=\" + hex8String + \",endColorstr=\" + secondHex8String + \")\";\n  },\n  toString: function toString(format) {\n    var formatSet = !!format;\n    format = format || this._format;\n    var formattedString = false;\n    var hasAlpha = this._a < 1 && this._a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format === \"hex\" || format === \"hex6\" || format === \"hex3\" || format === \"hex4\" || format === \"hex8\" || format === \"name\");\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === \"name\" && this._a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === \"rgb\") {\n      formattedString = this.toRgbString();\n    }\n    if (format === \"prgb\") {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === \"hex\" || format === \"hex6\") {\n      formattedString = this.toHexString();\n    }\n    if (format === \"hex3\") {\n      formattedString = this.toHexString(true);\n    }\n    if (format === \"hex4\") {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === \"hex8\") {\n      formattedString = this.toHex8String();\n    }\n    if (format === \"name\") {\n      formattedString = this.toName();\n    }\n    if (format === \"hsl\") {\n      formattedString = this.toHslString();\n    }\n    if (format === \"hsv\") {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  },\n  clone: function clone() {\n    return tinycolor(this.toString());\n  },\n  _applyModification: function _applyModification(fn, args) {\n    var color = fn.apply(null, [this].concat([].slice.call(args)));\n    this._r = color._r;\n    this._g = color._g;\n    this._b = color._b;\n    this.setAlpha(color._a);\n    return this;\n  },\n  lighten: function lighten() {\n    return this._applyModification(_lighten, arguments);\n  },\n  brighten: function brighten() {\n    return this._applyModification(_brighten, arguments);\n  },\n  darken: function darken() {\n    return this._applyModification(_darken, arguments);\n  },\n  desaturate: function desaturate() {\n    return this._applyModification(_desaturate, arguments);\n  },\n  saturate: function saturate() {\n    return this._applyModification(_saturate, arguments);\n  },\n  greyscale: function greyscale() {\n    return this._applyModification(_greyscale, arguments);\n  },\n  spin: function spin() {\n    return this._applyModification(_spin, arguments);\n  },\n  _applyCombination: function _applyCombination(fn, args) {\n    return fn.apply(null, [this].concat([].slice.call(args)));\n  },\n  analogous: function analogous() {\n    return this._applyCombination(_analogous, arguments);\n  },\n  complement: function complement() {\n    return this._applyCombination(_complement, arguments);\n  },\n  monochromatic: function monochromatic() {\n    return this._applyCombination(_monochromatic, arguments);\n  },\n  splitcomplement: function splitcomplement() {\n    return this._applyCombination(_splitcomplement, arguments);\n  },\n  // Disabled until https://github.com/bgrins/TinyColor/issues/254\n  // polyad: function (number) {\n  //   return this._applyCombination(polyad, [number]);\n  // },\n  triad: function triad() {\n    return this._applyCombination(polyad, [3]);\n  },\n  tetrad: function tetrad() {\n    return this._applyCombination(polyad, [4]);\n  }\n};\n\n// If input is an object, force 1 into \"1.0\" to handle ratios properly\n// String input requires \"1.0\" as input, so 1 will be treated as 1\ntinycolor.fromRatio = function (color, opts) {\n  if (_typeof(color) == \"object\") {\n    var newColor = {};\n    for (var i in color) {\n      if (color.hasOwnProperty(i)) {\n        if (i === \"a\") {\n          newColor[i] = color[i];\n        } else {\n          newColor[i] = convertToPercentage(color[i]);\n        }\n      }\n    }\n    color = newColor;\n  }\n  return tinycolor(color, opts);\n};\n\n// Given a string or object, convert that input to RGB\n// Possible string inputs:\n//\n//     \"red\"\n//     \"#f00\" or \"f00\"\n//     \"#ff0000\" or \"ff0000\"\n//     \"#ff000000\" or \"ff000000\"\n//     \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n//     \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n//     \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n//     \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n//     \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n//     \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n//     \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n//\nfunction inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color == \"string\") {\n    color = stringInputToObject(color);\n  }\n  if (_typeof(color) == \"object\") {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = \"hsv\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = \"hsl\";\n    }\n    if (color.hasOwnProperty(\"a\")) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n\n// Conversion Functions\n// --------------------\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n\n// `rgbToRgb`\n// Handle bounds / percentage checking to conform to CSS color spec\n// <http://www.w3.org/TR/css3-color/>\n// *Assumes:* r, g, b in [0, 255] or [0, 1]\n// *Returns:* { r, g, b } in [0, 255]\nfunction rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\n\n// `rgbToHsl`\n// Converts an RGB color value to HSL.\n// *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n// *Returns:* { h, s, l } in [0,1]\nfunction rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    l = (max + min) / 2;\n  if (max == min) {\n    h = s = 0; // achromatic\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    l: l\n  };\n}\n\n// `hslToRgb`\n// Converts an HSL color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hslToRgb(h, s, l) {\n  var r, g, b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  function hue2rgb(p, q, t) {\n    if (t < 0) t += 1;\n    if (t > 1) t -= 1;\n    if (t < 1 / 6) return p + (q - p) * 6 * t;\n    if (t < 1 / 2) return q;\n    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n  }\n  if (s === 0) {\n    r = g = b = l; // achromatic\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHsv`\n// Converts an RGB color value to HSV\n// *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n// *Returns:* { h, s, v } in [0,1]\nfunction rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    v = max;\n  var d = max - min;\n  s = max === 0 ? 0 : d / max;\n  if (max == min) {\n    h = 0; // achromatic\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    v: v\n  };\n}\n\n// `hsvToRgb`\n// Converts an HSV color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  var i = Math.floor(h),\n    f = h - i,\n    p = v * (1 - s),\n    q = v * (1 - f * s),\n    t = v * (1 - (1 - f) * s),\n    mod = i % 6,\n    r = [v, q, p, p, t, v][mod],\n    g = [t, v, v, q, p, p][mod],\n    b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHex`\n// Converts an RGB color to hex\n// Assumes r, g, and b are contained in the set [0, 255]\n// Returns a 3 or 6 character hex\nfunction rgbToHex(r, g, b, allow3Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n\n  // Return a 3 character hex if possible\n  if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToHex`\n// Converts an RGBA color plus alpha transparency to hex\n// Assumes r, g, b are contained in the set [0, 255] and\n// a in [0, 1]. Returns a 4 or 8 character rgba hex\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n\n  // Return a 4 character hex if possible\n  if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToArgbHex`\n// Converts an RGBA color to an ARGB Hex8 string\n// Rarely used, but required for \"toFilter()\"\nfunction rgbaToArgbHex(r, g, b, a) {\n  var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join(\"\");\n}\n\n// `equals`\n// Can be called with any tinycolor input\ntinycolor.equals = function (color1, color2) {\n  if (!color1 || !color2) return false;\n  return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();\n};\ntinycolor.random = function () {\n  return tinycolor.fromRatio({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n};\n\n// Modification Functions\n// ----------------------\n// Thanks to less.js for some of the basics here\n// <https://github.com/cloudhead/less.js/blob/master/lib/less/functions.js>\n\nfunction _desaturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s -= amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _saturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s += amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _greyscale(color) {\n  return tinycolor(color).desaturate(100);\n}\nfunction _lighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l += amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\nfunction _brighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var rgb = tinycolor(color).toRgb();\n  rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n  rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n  rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n  return tinycolor(rgb);\n}\nfunction _darken(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l -= amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\n\n// Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n// Values outside of this range will be wrapped into this range.\nfunction _spin(color, amount) {\n  var hsl = tinycolor(color).toHsl();\n  var hue = (hsl.h + amount) % 360;\n  hsl.h = hue < 0 ? 360 + hue : hue;\n  return tinycolor(hsl);\n}\n\n// Combination Functions\n// ---------------------\n// Thanks to jQuery xColor for some of the ideas behind these\n// <https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js>\n\nfunction _complement(color) {\n  var hsl = tinycolor(color).toHsl();\n  hsl.h = (hsl.h + 180) % 360;\n  return tinycolor(hsl);\n}\nfunction polyad(color, number) {\n  if (isNaN(number) || number <= 0) {\n    throw new Error(\"Argument to polyad must be a positive number\");\n  }\n  var hsl = tinycolor(color).toHsl();\n  var result = [tinycolor(color)];\n  var step = 360 / number;\n  for (var i = 1; i < number; i++) {\n    result.push(tinycolor({\n      h: (hsl.h + i * step) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }));\n  }\n  return result;\n}\nfunction _splitcomplement(color) {\n  var hsl = tinycolor(color).toHsl();\n  var h = hsl.h;\n  return [tinycolor(color), tinycolor({\n    h: (h + 72) % 360,\n    s: hsl.s,\n    l: hsl.l\n  }), tinycolor({\n    h: (h + 216) % 360,\n    s: hsl.s,\n    l: hsl.l\n  })];\n}\nfunction _analogous(color, results, slices) {\n  results = results || 6;\n  slices = slices || 30;\n  var hsl = tinycolor(color).toHsl();\n  var part = 360 / slices;\n  var ret = [tinycolor(color)];\n  for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n    hsl.h = (hsl.h + part) % 360;\n    ret.push(tinycolor(hsl));\n  }\n  return ret;\n}\nfunction _monochromatic(color, results) {\n  results = results || 6;\n  var hsv = tinycolor(color).toHsv();\n  var h = hsv.h,\n    s = hsv.s,\n    v = hsv.v;\n  var ret = [];\n  var modification = 1 / results;\n  while (results--) {\n    ret.push(tinycolor({\n      h: h,\n      s: s,\n      v: v\n    }));\n    v = (v + modification) % 1;\n  }\n  return ret;\n}\n\n// Utility Functions\n// ---------------------\n\ntinycolor.mix = function (color1, color2, amount) {\n  amount = amount === 0 ? 0 : amount || 50;\n  var rgb1 = tinycolor(color1).toRgb();\n  var rgb2 = tinycolor(color2).toRgb();\n  var p = amount / 100;\n  var rgba = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b,\n    a: (rgb2.a - rgb1.a) * p + rgb1.a\n  };\n  return tinycolor(rgba);\n};\n\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n\n// `contrast`\n// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\ntinycolor.readability = function (color1, color2) {\n  var c1 = tinycolor(color1);\n  var c2 = tinycolor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n};\n\n// `isReadable`\n// Ensure that foreground and background color combinations meet WCAG2 guidelines.\n// The third argument is an optional Object.\n//      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n//      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n// If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n\n// *Example*\n//    tinycolor.isReadable(\"#000\", \"#111\") => false\n//    tinycolor.isReadable(\"#000\", \"#111\",{level:\"AA\",size:\"large\"}) => false\ntinycolor.isReadable = function (color1, color2, wcag2) {\n  var readability = tinycolor.readability(color1, color2);\n  var wcag2Parms, out;\n  out = false;\n  wcag2Parms = validateWCAG2Parms(wcag2);\n  switch (wcag2Parms.level + wcag2Parms.size) {\n    case \"AAsmall\":\n    case \"AAAlarge\":\n      out = readability >= 4.5;\n      break;\n    case \"AAlarge\":\n      out = readability >= 3;\n      break;\n    case \"AAAsmall\":\n      out = readability >= 7;\n      break;\n  }\n  return out;\n};\n\n// `mostReadable`\n// Given a base color and a list of possible foreground or background\n// colors for that base, returns the most readable color.\n// Optionally returns Black or White if the most readable color is unreadable.\n// *Example*\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:false}).toHexString(); // \"#112255\"\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:true}).toHexString();  // \"#ffffff\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"large\"}).toHexString(); // \"#faf3f3\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"small\"}).toHexString(); // \"#ffffff\"\ntinycolor.mostReadable = function (baseColor, colorList, args) {\n  var bestColor = null;\n  var bestScore = 0;\n  var readability;\n  var includeFallbackColors, level, size;\n  args = args || {};\n  includeFallbackColors = args.includeFallbackColors;\n  level = args.level;\n  size = args.size;\n  for (var i = 0; i < colorList.length; i++) {\n    readability = tinycolor.readability(baseColor, colorList[i]);\n    if (readability > bestScore) {\n      bestScore = readability;\n      bestColor = tinycolor(colorList[i]);\n    }\n  }\n  if (tinycolor.isReadable(baseColor, bestColor, {\n    level: level,\n    size: size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  } else {\n    args.includeFallbackColors = false;\n    return tinycolor.mostReadable(baseColor, [\"#fff\", \"#000\"], args);\n  }\n};\n\n// Big List of Colors\n// ------------------\n// <https://www.w3.org/TR/css-color-4/#named-colors>\nvar names = tinycolor.names = {\n  aliceblue: \"f0f8ff\",\n  antiquewhite: \"faebd7\",\n  aqua: \"0ff\",\n  aquamarine: \"7fffd4\",\n  azure: \"f0ffff\",\n  beige: \"f5f5dc\",\n  bisque: \"ffe4c4\",\n  black: \"000\",\n  blanchedalmond: \"ffebcd\",\n  blue: \"00f\",\n  blueviolet: \"8a2be2\",\n  brown: \"a52a2a\",\n  burlywood: \"deb887\",\n  burntsienna: \"ea7e5d\",\n  cadetblue: \"5f9ea0\",\n  chartreuse: \"7fff00\",\n  chocolate: \"d2691e\",\n  coral: \"ff7f50\",\n  cornflowerblue: \"6495ed\",\n  cornsilk: \"fff8dc\",\n  crimson: \"dc143c\",\n  cyan: \"0ff\",\n  darkblue: \"00008b\",\n  darkcyan: \"008b8b\",\n  darkgoldenrod: \"b8860b\",\n  darkgray: \"a9a9a9\",\n  darkgreen: \"006400\",\n  darkgrey: \"a9a9a9\",\n  darkkhaki: \"bdb76b\",\n  darkmagenta: \"8b008b\",\n  darkolivegreen: \"556b2f\",\n  darkorange: \"ff8c00\",\n  darkorchid: \"9932cc\",\n  darkred: \"8b0000\",\n  darksalmon: \"e9967a\",\n  darkseagreen: \"8fbc8f\",\n  darkslateblue: \"483d8b\",\n  darkslategray: \"2f4f4f\",\n  darkslategrey: \"2f4f4f\",\n  darkturquoise: \"00ced1\",\n  darkviolet: \"9400d3\",\n  deeppink: \"ff1493\",\n  deepskyblue: \"00bfff\",\n  dimgray: \"696969\",\n  dimgrey: \"696969\",\n  dodgerblue: \"1e90ff\",\n  firebrick: \"b22222\",\n  floralwhite: \"fffaf0\",\n  forestgreen: \"228b22\",\n  fuchsia: \"f0f\",\n  gainsboro: \"dcdcdc\",\n  ghostwhite: \"f8f8ff\",\n  gold: \"ffd700\",\n  goldenrod: \"daa520\",\n  gray: \"808080\",\n  green: \"008000\",\n  greenyellow: \"adff2f\",\n  grey: \"808080\",\n  honeydew: \"f0fff0\",\n  hotpink: \"ff69b4\",\n  indianred: \"cd5c5c\",\n  indigo: \"4b0082\",\n  ivory: \"fffff0\",\n  khaki: \"f0e68c\",\n  lavender: \"e6e6fa\",\n  lavenderblush: \"fff0f5\",\n  lawngreen: \"7cfc00\",\n  lemonchiffon: \"fffacd\",\n  lightblue: \"add8e6\",\n  lightcoral: \"f08080\",\n  lightcyan: \"e0ffff\",\n  lightgoldenrodyellow: \"fafad2\",\n  lightgray: \"d3d3d3\",\n  lightgreen: \"90ee90\",\n  lightgrey: \"d3d3d3\",\n  lightpink: \"ffb6c1\",\n  lightsalmon: \"ffa07a\",\n  lightseagreen: \"20b2aa\",\n  lightskyblue: \"87cefa\",\n  lightslategray: \"789\",\n  lightslategrey: \"789\",\n  lightsteelblue: \"b0c4de\",\n  lightyellow: \"ffffe0\",\n  lime: \"0f0\",\n  limegreen: \"32cd32\",\n  linen: \"faf0e6\",\n  magenta: \"f0f\",\n  maroon: \"800000\",\n  mediumaquamarine: \"66cdaa\",\n  mediumblue: \"0000cd\",\n  mediumorchid: \"ba55d3\",\n  mediumpurple: \"9370db\",\n  mediumseagreen: \"3cb371\",\n  mediumslateblue: \"7b68ee\",\n  mediumspringgreen: \"00fa9a\",\n  mediumturquoise: \"48d1cc\",\n  mediumvioletred: \"c71585\",\n  midnightblue: \"191970\",\n  mintcream: \"f5fffa\",\n  mistyrose: \"ffe4e1\",\n  moccasin: \"ffe4b5\",\n  navajowhite: \"ffdead\",\n  navy: \"000080\",\n  oldlace: \"fdf5e6\",\n  olive: \"808000\",\n  olivedrab: \"6b8e23\",\n  orange: \"ffa500\",\n  orangered: \"ff4500\",\n  orchid: \"da70d6\",\n  palegoldenrod: \"eee8aa\",\n  palegreen: \"98fb98\",\n  paleturquoise: \"afeeee\",\n  palevioletred: \"db7093\",\n  papayawhip: \"ffefd5\",\n  peachpuff: \"ffdab9\",\n  peru: \"cd853f\",\n  pink: \"ffc0cb\",\n  plum: \"dda0dd\",\n  powderblue: \"b0e0e6\",\n  purple: \"800080\",\n  rebeccapurple: \"663399\",\n  red: \"f00\",\n  rosybrown: \"bc8f8f\",\n  royalblue: \"4169e1\",\n  saddlebrown: \"8b4513\",\n  salmon: \"fa8072\",\n  sandybrown: \"f4a460\",\n  seagreen: \"2e8b57\",\n  seashell: \"fff5ee\",\n  sienna: \"a0522d\",\n  silver: \"c0c0c0\",\n  skyblue: \"87ceeb\",\n  slateblue: \"6a5acd\",\n  slategray: \"708090\",\n  slategrey: \"708090\",\n  snow: \"fffafa\",\n  springgreen: \"00ff7f\",\n  steelblue: \"4682b4\",\n  tan: \"d2b48c\",\n  teal: \"008080\",\n  thistle: \"d8bfd8\",\n  tomato: \"ff6347\",\n  turquoise: \"40e0d0\",\n  violet: \"ee82ee\",\n  wheat: \"f5deb3\",\n  white: \"fff\",\n  whitesmoke: \"f5f5f5\",\n  yellow: \"ff0\",\n  yellowgreen: \"9acd32\"\n};\n\n// Make it easy to access colors via `hexNames[hex]`\nvar hexNames = tinycolor.hexNames = flip(names);\n\n// Utilities\n// ---------\n\n// `{ 'name1': 'val1' }` becomes `{ 'val1': 'name1' }`\nfunction flip(o) {\n  var flipped = {};\n  for (var i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\n\n// Return a valid alpha value [0,1] with all invalid values being set to 1\nfunction boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n\n// Take input from [0, n] and return it as [0, 1]\nfunction bound01(n, max) {\n  if (isOnePointZero(n)) n = \"100%\";\n  var processPercent = isPercentage(n);\n  n = Math.min(max, Math.max(0, parseFloat(n)));\n\n  // Automatically convert percentage into number\n  if (processPercent) {\n    n = parseInt(n * max, 10) / 100;\n  }\n\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n\n  // Convert into [0, 1] range if it isn't already\n  return n % max / parseFloat(max);\n}\n\n// Force a number between 0 and 1\nfunction clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n\n// Parse a base-16 hex value into a base-10 integer\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\n\n// Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n// <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\nfunction isOnePointZero(n) {\n  return typeof n == \"string\" && n.indexOf(\".\") != -1 && parseFloat(n) === 1;\n}\n\n// Check to see if string passed in is a percentage\nfunction isPercentage(n) {\n  return typeof n === \"string\" && n.indexOf(\"%\") != -1;\n}\n\n// Force a hex value to have 2 characters\nfunction pad2(c) {\n  return c.length == 1 ? \"0\" + c : \"\" + c;\n}\n\n// Replace a decimal with it's percentage value\nfunction convertToPercentage(n) {\n  if (n <= 1) {\n    n = n * 100 + \"%\";\n  }\n  return n;\n}\n\n// Converts a decimal to a hex value\nfunction convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\n// Converts a hex value to a decimal\nfunction convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\nvar matchers = function () {\n  // <http://www.w3.org/TR/css3-values/#integers>\n  var CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n\n  // <http://www.w3.org/TR/css3-values/#number-value>\n  var CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n\n  // Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\n  var CSS_UNIT = \"(?:\" + CSS_NUMBER + \")|(?:\" + CSS_INTEGER + \")\";\n\n  // Actual matching.\n  // Parentheses and commas are optional, but not required.\n  // Whitespace can take the place of commas or opening paren\n  var PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  var PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  return {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n    rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n    hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n    hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n    hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n    hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n  };\n}();\n\n// `isValidCSSUnit`\n// Take in a single string / number and check to see if it looks like a CSS unit\n// (see `matchers` above for definition).\nfunction isValidCSSUnit(color) {\n  return !!matchers.CSS_UNIT.exec(color);\n}\n\n// `stringInputToObject`\n// Permissive string parsing.  Take in a number of formats, and output an object\n// based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\nfunction stringInputToObject(color) {\n  color = color.replace(trimLeft, \"\").replace(trimRight, \"\").toLowerCase();\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color == \"transparent\") {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: \"name\"\n    };\n  }\n\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match;\n  if (match = matchers.rgb.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  if (match = matchers.rgba.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsl.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  if (match = matchers.hsla.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsv.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  if (match = matchers.hsva.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hex8.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex6.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  if (match = matchers.hex4.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      a: convertHexToDecimal(match[4] + \"\" + match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex3.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  return false;\n}\nfunction validateWCAG2Parms(parms) {\n  // return valid WCAG2 parms for isReadable.\n  // If input parms are invalid, return {\"level\":\"AA\", \"size\":\"small\"}\n  var level, size;\n  parms = parms || {\n    level: \"AA\",\n    size: \"small\"\n  };\n  level = (parms.level || \"AA\").toUpperCase();\n  size = (parms.size || \"small\").toLowerCase();\n  if (level !== \"AA\" && level !== \"AAA\") {\n    level = \"AA\";\n  }\n  if (size !== \"small\" && size !== \"large\") {\n    size = \"small\";\n  }\n  return {\n    level: level,\n    size: size\n  };\n}\nexport { tinycolor as default };", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "trimLeft", "trimRight", "tinycolor", "color", "opts", "rgb", "inputToRGB", "_originalInput", "_r", "r", "_g", "g", "_b", "b", "_a", "a", "_roundA", "Math", "round", "_format", "format", "_gradientType", "gradientType", "_ok", "ok", "isDark", "getBrightness", "isLight", "<PERSON><PERSON><PERSON><PERSON>", "getOriginalInput", "getFormat", "get<PERSON><PERSON><PERSON>", "toRgb", "getLuminance", "RsRGB", "GsRGB", "BsRGB", "R", "G", "B", "pow", "<PERSON><PERSON><PERSON><PERSON>", "value", "boundAlpha", "toHsv", "hsv", "rgbToHsv", "h", "s", "v", "toHsvString", "toHsl", "hsl", "rgbToHsl", "l", "toHslString", "toHex", "allow3Char", "rgbToHex", "toHexString", "toHex8", "allow4Char", "rgbaToHex", "toHex8String", "toRgbString", "toPercentageRgb", "bound01", "toPercentageRgbString", "to<PERSON>ame", "hexNames", "to<PERSON><PERSON>er", "secondColor", "hex8String", "rgbaToArgbHex", "secondHex8String", "toString", "formatSet", "formattedString", "has<PERSON><PERSON><PERSON>", "needsAlphaFormat", "clone", "_applyModification", "fn", "args", "apply", "concat", "slice", "call", "lighten", "_lighten", "arguments", "brighten", "_brighten", "darken", "_darken", "desaturate", "_desaturate", "saturate", "_saturate", "greyscale", "_greyscale", "spin", "_spin", "_applyCombination", "analogous", "_analogous", "complement", "_complement", "monochromatic", "_monochromatic", "splitcomplement", "_splitcomplement", "triad", "polyad", "tetrad", "fromRatio", "newColor", "i", "hasOwnProperty", "convertToPercentage", "stringInputToObject", "isValidCSSUnit", "rgbToRgb", "String", "substr", "hsvToRgb", "hslToRgb", "min", "max", "d", "hue2rgb", "p", "q", "t", "floor", "f", "mod", "hex", "pad2", "char<PERSON>t", "join", "convertDecimalToHex", "equals", "color1", "color2", "random", "amount", "clamp01", "hue", "number", "isNaN", "Error", "result", "step", "push", "results", "slices", "part", "ret", "modification", "mix", "rgb1", "rgb2", "rgba", "readability", "c1", "c2", "isReadable", "wcag2", "wcag2Parms", "out", "validateWCAG2Parms", "level", "size", "mostReadable", "baseColor", "colorList", "bestColor", "bestScore", "includeFallbackColors", "length", "names", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "<PERSON><PERSON><PERSON>", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "flip", "o", "flipped", "parseFloat", "n", "isOnePointZero", "processPercent", "isPercentage", "parseInt", "abs", "val", "parseIntFromHex", "indexOf", "c", "convertHexToDecimal", "matchers", "CSS_INTEGER", "CSS_NUMBER", "CSS_UNIT", "PERMISSIVE_MATCH3", "PERMISSIVE_MATCH4", "RegExp", "hsla", "hsva", "hex3", "hex6", "hex4", "hex8", "exec", "replace", "toLowerCase", "named", "match", "parms", "toUpperCase", "default"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/tinycolor2/esm/tinycolor.js"], "sourcesContent": ["// This file is autogenerated. It's used to publish ESM to npm.\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\n// https://github.com/bgrins/TinyColor\n// <PERSON>, MIT License\n\nvar trimLeft = /^\\s+/;\nvar trimRight = /\\s+$/;\nfunction tinycolor(color, opts) {\n  color = color ? color : \"\";\n  opts = opts || {};\n\n  // If input is already a tinycolor, return itself\n  if (color instanceof tinycolor) {\n    return color;\n  }\n  // If we are called as a function, call using new instead\n  if (!(this instanceof tinycolor)) {\n    return new tinycolor(color, opts);\n  }\n  var rgb = inputToRGB(color);\n  this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;\n  this._gradientType = opts.gradientType;\n\n  // Don't let the range of [0,255] come back in [0,1].\n  // Potentially lose a little bit of precision here, but will fix issues where\n  // .5 gets interpreted as half of the total, instead of half of 1\n  // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n  if (this._r < 1) this._r = Math.round(this._r);\n  if (this._g < 1) this._g = Math.round(this._g);\n  if (this._b < 1) this._b = Math.round(this._b);\n  this._ok = rgb.ok;\n}\ntinycolor.prototype = {\n  isDark: function isDark() {\n    return this.getBrightness() < 128;\n  },\n  isLight: function isLight() {\n    return !this.isDark();\n  },\n  isValid: function isValid() {\n    return this._ok;\n  },\n  getOriginalInput: function getOriginalInput() {\n    return this._originalInput;\n  },\n  getFormat: function getFormat() {\n    return this._format;\n  },\n  getAlpha: function getAlpha() {\n    return this._a;\n  },\n  getBrightness: function getBrightness() {\n    //http://www.w3.org/TR/AERT#color-contrast\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  },\n  getLuminance: function getLuminance() {\n    //http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    var rgb = this.toRgb();\n    var RsRGB, GsRGB, BsRGB, R, G, B;\n    RsRGB = rgb.r / 255;\n    GsRGB = rgb.g / 255;\n    BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) R = RsRGB / 12.92;else R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    if (GsRGB <= 0.03928) G = GsRGB / 12.92;else G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    if (BsRGB <= 0.03928) B = BsRGB / 12.92;else B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  },\n  setAlpha: function setAlpha(value) {\n    this._a = boundAlpha(value);\n    this._roundA = Math.round(100 * this._a) / 100;\n    return this;\n  },\n  toHsv: function toHsv() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this._a\n    };\n  },\n  toHsvString: function toHsvString() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    var h = Math.round(hsv.h * 360),\n      s = Math.round(hsv.s * 100),\n      v = Math.round(hsv.v * 100);\n    return this._a == 1 ? \"hsv(\" + h + \", \" + s + \"%, \" + v + \"%)\" : \"hsva(\" + h + \", \" + s + \"%, \" + v + \"%, \" + this._roundA + \")\";\n  },\n  toHsl: function toHsl() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this._a\n    };\n  },\n  toHslString: function toHslString() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    var h = Math.round(hsl.h * 360),\n      s = Math.round(hsl.s * 100),\n      l = Math.round(hsl.l * 100);\n    return this._a == 1 ? \"hsl(\" + h + \", \" + s + \"%, \" + l + \"%)\" : \"hsla(\" + h + \", \" + s + \"%, \" + l + \"%, \" + this._roundA + \")\";\n  },\n  toHex: function toHex(allow3Char) {\n    return rgbToHex(this._r, this._g, this._b, allow3Char);\n  },\n  toHexString: function toHexString(allow3Char) {\n    return \"#\" + this.toHex(allow3Char);\n  },\n  toHex8: function toHex8(allow4Char) {\n    return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);\n  },\n  toHex8String: function toHex8String(allow4Char) {\n    return \"#\" + this.toHex8(allow4Char);\n  },\n  toRgb: function toRgb() {\n    return {\n      r: Math.round(this._r),\n      g: Math.round(this._g),\n      b: Math.round(this._b),\n      a: this._a\n    };\n  },\n  toRgbString: function toRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \")\" : \"rgba(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \", \" + this._roundA + \")\";\n  },\n  toPercentageRgb: function toPercentageRgb() {\n    return {\n      r: Math.round(bound01(this._r, 255) * 100) + \"%\",\n      g: Math.round(bound01(this._g, 255) * 100) + \"%\",\n      b: Math.round(bound01(this._b, 255) * 100) + \"%\",\n      a: this._a\n    };\n  },\n  toPercentageRgbString: function toPercentageRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%)\" : \"rgba(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%, \" + this._roundA + \")\";\n  },\n  toName: function toName() {\n    if (this._a === 0) {\n      return \"transparent\";\n    }\n    if (this._a < 1) {\n      return false;\n    }\n    return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;\n  },\n  toFilter: function toFilter(secondColor) {\n    var hex8String = \"#\" + rgbaToArgbHex(this._r, this._g, this._b, this._a);\n    var secondHex8String = hex8String;\n    var gradientType = this._gradientType ? \"GradientType = 1, \" : \"\";\n    if (secondColor) {\n      var s = tinycolor(secondColor);\n      secondHex8String = \"#\" + rgbaToArgbHex(s._r, s._g, s._b, s._a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\" + gradientType + \"startColorstr=\" + hex8String + \",endColorstr=\" + secondHex8String + \")\";\n  },\n  toString: function toString(format) {\n    var formatSet = !!format;\n    format = format || this._format;\n    var formattedString = false;\n    var hasAlpha = this._a < 1 && this._a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format === \"hex\" || format === \"hex6\" || format === \"hex3\" || format === \"hex4\" || format === \"hex8\" || format === \"name\");\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === \"name\" && this._a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === \"rgb\") {\n      formattedString = this.toRgbString();\n    }\n    if (format === \"prgb\") {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === \"hex\" || format === \"hex6\") {\n      formattedString = this.toHexString();\n    }\n    if (format === \"hex3\") {\n      formattedString = this.toHexString(true);\n    }\n    if (format === \"hex4\") {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === \"hex8\") {\n      formattedString = this.toHex8String();\n    }\n    if (format === \"name\") {\n      formattedString = this.toName();\n    }\n    if (format === \"hsl\") {\n      formattedString = this.toHslString();\n    }\n    if (format === \"hsv\") {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  },\n  clone: function clone() {\n    return tinycolor(this.toString());\n  },\n  _applyModification: function _applyModification(fn, args) {\n    var color = fn.apply(null, [this].concat([].slice.call(args)));\n    this._r = color._r;\n    this._g = color._g;\n    this._b = color._b;\n    this.setAlpha(color._a);\n    return this;\n  },\n  lighten: function lighten() {\n    return this._applyModification(_lighten, arguments);\n  },\n  brighten: function brighten() {\n    return this._applyModification(_brighten, arguments);\n  },\n  darken: function darken() {\n    return this._applyModification(_darken, arguments);\n  },\n  desaturate: function desaturate() {\n    return this._applyModification(_desaturate, arguments);\n  },\n  saturate: function saturate() {\n    return this._applyModification(_saturate, arguments);\n  },\n  greyscale: function greyscale() {\n    return this._applyModification(_greyscale, arguments);\n  },\n  spin: function spin() {\n    return this._applyModification(_spin, arguments);\n  },\n  _applyCombination: function _applyCombination(fn, args) {\n    return fn.apply(null, [this].concat([].slice.call(args)));\n  },\n  analogous: function analogous() {\n    return this._applyCombination(_analogous, arguments);\n  },\n  complement: function complement() {\n    return this._applyCombination(_complement, arguments);\n  },\n  monochromatic: function monochromatic() {\n    return this._applyCombination(_monochromatic, arguments);\n  },\n  splitcomplement: function splitcomplement() {\n    return this._applyCombination(_splitcomplement, arguments);\n  },\n  // Disabled until https://github.com/bgrins/TinyColor/issues/254\n  // polyad: function (number) {\n  //   return this._applyCombination(polyad, [number]);\n  // },\n  triad: function triad() {\n    return this._applyCombination(polyad, [3]);\n  },\n  tetrad: function tetrad() {\n    return this._applyCombination(polyad, [4]);\n  }\n};\n\n// If input is an object, force 1 into \"1.0\" to handle ratios properly\n// String input requires \"1.0\" as input, so 1 will be treated as 1\ntinycolor.fromRatio = function (color, opts) {\n  if (_typeof(color) == \"object\") {\n    var newColor = {};\n    for (var i in color) {\n      if (color.hasOwnProperty(i)) {\n        if (i === \"a\") {\n          newColor[i] = color[i];\n        } else {\n          newColor[i] = convertToPercentage(color[i]);\n        }\n      }\n    }\n    color = newColor;\n  }\n  return tinycolor(color, opts);\n};\n\n// Given a string or object, convert that input to RGB\n// Possible string inputs:\n//\n//     \"red\"\n//     \"#f00\" or \"f00\"\n//     \"#ff0000\" or \"ff0000\"\n//     \"#ff000000\" or \"ff000000\"\n//     \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n//     \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n//     \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n//     \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n//     \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n//     \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n//     \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n//\nfunction inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color == \"string\") {\n    color = stringInputToObject(color);\n  }\n  if (_typeof(color) == \"object\") {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = \"hsv\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = \"hsl\";\n    }\n    if (color.hasOwnProperty(\"a\")) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n\n// Conversion Functions\n// --------------------\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n\n// `rgbToRgb`\n// Handle bounds / percentage checking to conform to CSS color spec\n// <http://www.w3.org/TR/css3-color/>\n// *Assumes:* r, g, b in [0, 255] or [0, 1]\n// *Returns:* { r, g, b } in [0, 255]\nfunction rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\n\n// `rgbToHsl`\n// Converts an RGB color value to HSL.\n// *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n// *Returns:* { h, s, l } in [0,1]\nfunction rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    l = (max + min) / 2;\n  if (max == min) {\n    h = s = 0; // achromatic\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    l: l\n  };\n}\n\n// `hslToRgb`\n// Converts an HSL color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hslToRgb(h, s, l) {\n  var r, g, b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  function hue2rgb(p, q, t) {\n    if (t < 0) t += 1;\n    if (t > 1) t -= 1;\n    if (t < 1 / 6) return p + (q - p) * 6 * t;\n    if (t < 1 / 2) return q;\n    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n  }\n  if (s === 0) {\n    r = g = b = l; // achromatic\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHsv`\n// Converts an RGB color value to HSV\n// *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n// *Returns:* { h, s, v } in [0,1]\nfunction rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    v = max;\n  var d = max - min;\n  s = max === 0 ? 0 : d / max;\n  if (max == min) {\n    h = 0; // achromatic\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    v: v\n  };\n}\n\n// `hsvToRgb`\n// Converts an HSV color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  var i = Math.floor(h),\n    f = h - i,\n    p = v * (1 - s),\n    q = v * (1 - f * s),\n    t = v * (1 - (1 - f) * s),\n    mod = i % 6,\n    r = [v, q, p, p, t, v][mod],\n    g = [t, v, v, q, p, p][mod],\n    b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHex`\n// Converts an RGB color to hex\n// Assumes r, g, and b are contained in the set [0, 255]\n// Returns a 3 or 6 character hex\nfunction rgbToHex(r, g, b, allow3Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n\n  // Return a 3 character hex if possible\n  if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToHex`\n// Converts an RGBA color plus alpha transparency to hex\n// Assumes r, g, b are contained in the set [0, 255] and\n// a in [0, 1]. Returns a 4 or 8 character rgba hex\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n\n  // Return a 4 character hex if possible\n  if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToArgbHex`\n// Converts an RGBA color to an ARGB Hex8 string\n// Rarely used, but required for \"toFilter()\"\nfunction rgbaToArgbHex(r, g, b, a) {\n  var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join(\"\");\n}\n\n// `equals`\n// Can be called with any tinycolor input\ntinycolor.equals = function (color1, color2) {\n  if (!color1 || !color2) return false;\n  return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();\n};\ntinycolor.random = function () {\n  return tinycolor.fromRatio({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n};\n\n// Modification Functions\n// ----------------------\n// Thanks to less.js for some of the basics here\n// <https://github.com/cloudhead/less.js/blob/master/lib/less/functions.js>\n\nfunction _desaturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s -= amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _saturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s += amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _greyscale(color) {\n  return tinycolor(color).desaturate(100);\n}\nfunction _lighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l += amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\nfunction _brighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var rgb = tinycolor(color).toRgb();\n  rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n  rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n  rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n  return tinycolor(rgb);\n}\nfunction _darken(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l -= amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\n\n// Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n// Values outside of this range will be wrapped into this range.\nfunction _spin(color, amount) {\n  var hsl = tinycolor(color).toHsl();\n  var hue = (hsl.h + amount) % 360;\n  hsl.h = hue < 0 ? 360 + hue : hue;\n  return tinycolor(hsl);\n}\n\n// Combination Functions\n// ---------------------\n// Thanks to jQuery xColor for some of the ideas behind these\n// <https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js>\n\nfunction _complement(color) {\n  var hsl = tinycolor(color).toHsl();\n  hsl.h = (hsl.h + 180) % 360;\n  return tinycolor(hsl);\n}\nfunction polyad(color, number) {\n  if (isNaN(number) || number <= 0) {\n    throw new Error(\"Argument to polyad must be a positive number\");\n  }\n  var hsl = tinycolor(color).toHsl();\n  var result = [tinycolor(color)];\n  var step = 360 / number;\n  for (var i = 1; i < number; i++) {\n    result.push(tinycolor({\n      h: (hsl.h + i * step) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }));\n  }\n  return result;\n}\nfunction _splitcomplement(color) {\n  var hsl = tinycolor(color).toHsl();\n  var h = hsl.h;\n  return [tinycolor(color), tinycolor({\n    h: (h + 72) % 360,\n    s: hsl.s,\n    l: hsl.l\n  }), tinycolor({\n    h: (h + 216) % 360,\n    s: hsl.s,\n    l: hsl.l\n  })];\n}\nfunction _analogous(color, results, slices) {\n  results = results || 6;\n  slices = slices || 30;\n  var hsl = tinycolor(color).toHsl();\n  var part = 360 / slices;\n  var ret = [tinycolor(color)];\n  for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n    hsl.h = (hsl.h + part) % 360;\n    ret.push(tinycolor(hsl));\n  }\n  return ret;\n}\nfunction _monochromatic(color, results) {\n  results = results || 6;\n  var hsv = tinycolor(color).toHsv();\n  var h = hsv.h,\n    s = hsv.s,\n    v = hsv.v;\n  var ret = [];\n  var modification = 1 / results;\n  while (results--) {\n    ret.push(tinycolor({\n      h: h,\n      s: s,\n      v: v\n    }));\n    v = (v + modification) % 1;\n  }\n  return ret;\n}\n\n// Utility Functions\n// ---------------------\n\ntinycolor.mix = function (color1, color2, amount) {\n  amount = amount === 0 ? 0 : amount || 50;\n  var rgb1 = tinycolor(color1).toRgb();\n  var rgb2 = tinycolor(color2).toRgb();\n  var p = amount / 100;\n  var rgba = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b,\n    a: (rgb2.a - rgb1.a) * p + rgb1.a\n  };\n  return tinycolor(rgba);\n};\n\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n\n// `contrast`\n// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\ntinycolor.readability = function (color1, color2) {\n  var c1 = tinycolor(color1);\n  var c2 = tinycolor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n};\n\n// `isReadable`\n// Ensure that foreground and background color combinations meet WCAG2 guidelines.\n// The third argument is an optional Object.\n//      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n//      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n// If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n\n// *Example*\n//    tinycolor.isReadable(\"#000\", \"#111\") => false\n//    tinycolor.isReadable(\"#000\", \"#111\",{level:\"AA\",size:\"large\"}) => false\ntinycolor.isReadable = function (color1, color2, wcag2) {\n  var readability = tinycolor.readability(color1, color2);\n  var wcag2Parms, out;\n  out = false;\n  wcag2Parms = validateWCAG2Parms(wcag2);\n  switch (wcag2Parms.level + wcag2Parms.size) {\n    case \"AAsmall\":\n    case \"AAAlarge\":\n      out = readability >= 4.5;\n      break;\n    case \"AAlarge\":\n      out = readability >= 3;\n      break;\n    case \"AAAsmall\":\n      out = readability >= 7;\n      break;\n  }\n  return out;\n};\n\n// `mostReadable`\n// Given a base color and a list of possible foreground or background\n// colors for that base, returns the most readable color.\n// Optionally returns Black or White if the most readable color is unreadable.\n// *Example*\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:false}).toHexString(); // \"#112255\"\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:true}).toHexString();  // \"#ffffff\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"large\"}).toHexString(); // \"#faf3f3\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"small\"}).toHexString(); // \"#ffffff\"\ntinycolor.mostReadable = function (baseColor, colorList, args) {\n  var bestColor = null;\n  var bestScore = 0;\n  var readability;\n  var includeFallbackColors, level, size;\n  args = args || {};\n  includeFallbackColors = args.includeFallbackColors;\n  level = args.level;\n  size = args.size;\n  for (var i = 0; i < colorList.length; i++) {\n    readability = tinycolor.readability(baseColor, colorList[i]);\n    if (readability > bestScore) {\n      bestScore = readability;\n      bestColor = tinycolor(colorList[i]);\n    }\n  }\n  if (tinycolor.isReadable(baseColor, bestColor, {\n    level: level,\n    size: size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  } else {\n    args.includeFallbackColors = false;\n    return tinycolor.mostReadable(baseColor, [\"#fff\", \"#000\"], args);\n  }\n};\n\n// Big List of Colors\n// ------------------\n// <https://www.w3.org/TR/css-color-4/#named-colors>\nvar names = tinycolor.names = {\n  aliceblue: \"f0f8ff\",\n  antiquewhite: \"faebd7\",\n  aqua: \"0ff\",\n  aquamarine: \"7fffd4\",\n  azure: \"f0ffff\",\n  beige: \"f5f5dc\",\n  bisque: \"ffe4c4\",\n  black: \"000\",\n  blanchedalmond: \"ffebcd\",\n  blue: \"00f\",\n  blueviolet: \"8a2be2\",\n  brown: \"a52a2a\",\n  burlywood: \"deb887\",\n  burntsienna: \"ea7e5d\",\n  cadetblue: \"5f9ea0\",\n  chartreuse: \"7fff00\",\n  chocolate: \"d2691e\",\n  coral: \"ff7f50\",\n  cornflowerblue: \"6495ed\",\n  cornsilk: \"fff8dc\",\n  crimson: \"dc143c\",\n  cyan: \"0ff\",\n  darkblue: \"00008b\",\n  darkcyan: \"008b8b\",\n  darkgoldenrod: \"b8860b\",\n  darkgray: \"a9a9a9\",\n  darkgreen: \"006400\",\n  darkgrey: \"a9a9a9\",\n  darkkhaki: \"bdb76b\",\n  darkmagenta: \"8b008b\",\n  darkolivegreen: \"556b2f\",\n  darkorange: \"ff8c00\",\n  darkorchid: \"9932cc\",\n  darkred: \"8b0000\",\n  darksalmon: \"e9967a\",\n  darkseagreen: \"8fbc8f\",\n  darkslateblue: \"483d8b\",\n  darkslategray: \"2f4f4f\",\n  darkslategrey: \"2f4f4f\",\n  darkturquoise: \"00ced1\",\n  darkviolet: \"9400d3\",\n  deeppink: \"ff1493\",\n  deepskyblue: \"00bfff\",\n  dimgray: \"696969\",\n  dimgrey: \"696969\",\n  dodgerblue: \"1e90ff\",\n  firebrick: \"b22222\",\n  floralwhite: \"fffaf0\",\n  forestgreen: \"228b22\",\n  fuchsia: \"f0f\",\n  gainsboro: \"dcdcdc\",\n  ghostwhite: \"f8f8ff\",\n  gold: \"ffd700\",\n  goldenrod: \"daa520\",\n  gray: \"808080\",\n  green: \"008000\",\n  greenyellow: \"adff2f\",\n  grey: \"808080\",\n  honeydew: \"f0fff0\",\n  hotpink: \"ff69b4\",\n  indianred: \"cd5c5c\",\n  indigo: \"4b0082\",\n  ivory: \"fffff0\",\n  khaki: \"f0e68c\",\n  lavender: \"e6e6fa\",\n  lavenderblush: \"fff0f5\",\n  lawngreen: \"7cfc00\",\n  lemonchiffon: \"fffacd\",\n  lightblue: \"add8e6\",\n  lightcoral: \"f08080\",\n  lightcyan: \"e0ffff\",\n  lightgoldenrodyellow: \"fafad2\",\n  lightgray: \"d3d3d3\",\n  lightgreen: \"90ee90\",\n  lightgrey: \"d3d3d3\",\n  lightpink: \"ffb6c1\",\n  lightsalmon: \"ffa07a\",\n  lightseagreen: \"20b2aa\",\n  lightskyblue: \"87cefa\",\n  lightslategray: \"789\",\n  lightslategrey: \"789\",\n  lightsteelblue: \"b0c4de\",\n  lightyellow: \"ffffe0\",\n  lime: \"0f0\",\n  limegreen: \"32cd32\",\n  linen: \"faf0e6\",\n  magenta: \"f0f\",\n  maroon: \"800000\",\n  mediumaquamarine: \"66cdaa\",\n  mediumblue: \"0000cd\",\n  mediumorchid: \"ba55d3\",\n  mediumpurple: \"9370db\",\n  mediumseagreen: \"3cb371\",\n  mediumslateblue: \"7b68ee\",\n  mediumspringgreen: \"00fa9a\",\n  mediumturquoise: \"48d1cc\",\n  mediumvioletred: \"c71585\",\n  midnightblue: \"191970\",\n  mintcream: \"f5fffa\",\n  mistyrose: \"ffe4e1\",\n  moccasin: \"ffe4b5\",\n  navajowhite: \"ffdead\",\n  navy: \"000080\",\n  oldlace: \"fdf5e6\",\n  olive: \"808000\",\n  olivedrab: \"6b8e23\",\n  orange: \"ffa500\",\n  orangered: \"ff4500\",\n  orchid: \"da70d6\",\n  palegoldenrod: \"eee8aa\",\n  palegreen: \"98fb98\",\n  paleturquoise: \"afeeee\",\n  palevioletred: \"db7093\",\n  papayawhip: \"ffefd5\",\n  peachpuff: \"ffdab9\",\n  peru: \"cd853f\",\n  pink: \"ffc0cb\",\n  plum: \"dda0dd\",\n  powderblue: \"b0e0e6\",\n  purple: \"800080\",\n  rebeccapurple: \"663399\",\n  red: \"f00\",\n  rosybrown: \"bc8f8f\",\n  royalblue: \"4169e1\",\n  saddlebrown: \"8b4513\",\n  salmon: \"fa8072\",\n  sandybrown: \"f4a460\",\n  seagreen: \"2e8b57\",\n  seashell: \"fff5ee\",\n  sienna: \"a0522d\",\n  silver: \"c0c0c0\",\n  skyblue: \"87ceeb\",\n  slateblue: \"6a5acd\",\n  slategray: \"708090\",\n  slategrey: \"708090\",\n  snow: \"fffafa\",\n  springgreen: \"00ff7f\",\n  steelblue: \"4682b4\",\n  tan: \"d2b48c\",\n  teal: \"008080\",\n  thistle: \"d8bfd8\",\n  tomato: \"ff6347\",\n  turquoise: \"40e0d0\",\n  violet: \"ee82ee\",\n  wheat: \"f5deb3\",\n  white: \"fff\",\n  whitesmoke: \"f5f5f5\",\n  yellow: \"ff0\",\n  yellowgreen: \"9acd32\"\n};\n\n// Make it easy to access colors via `hexNames[hex]`\nvar hexNames = tinycolor.hexNames = flip(names);\n\n// Utilities\n// ---------\n\n// `{ 'name1': 'val1' }` becomes `{ 'val1': 'name1' }`\nfunction flip(o) {\n  var flipped = {};\n  for (var i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\n\n// Return a valid alpha value [0,1] with all invalid values being set to 1\nfunction boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n\n// Take input from [0, n] and return it as [0, 1]\nfunction bound01(n, max) {\n  if (isOnePointZero(n)) n = \"100%\";\n  var processPercent = isPercentage(n);\n  n = Math.min(max, Math.max(0, parseFloat(n)));\n\n  // Automatically convert percentage into number\n  if (processPercent) {\n    n = parseInt(n * max, 10) / 100;\n  }\n\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n\n  // Convert into [0, 1] range if it isn't already\n  return n % max / parseFloat(max);\n}\n\n// Force a number between 0 and 1\nfunction clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n\n// Parse a base-16 hex value into a base-10 integer\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\n\n// Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n// <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\nfunction isOnePointZero(n) {\n  return typeof n == \"string\" && n.indexOf(\".\") != -1 && parseFloat(n) === 1;\n}\n\n// Check to see if string passed in is a percentage\nfunction isPercentage(n) {\n  return typeof n === \"string\" && n.indexOf(\"%\") != -1;\n}\n\n// Force a hex value to have 2 characters\nfunction pad2(c) {\n  return c.length == 1 ? \"0\" + c : \"\" + c;\n}\n\n// Replace a decimal with it's percentage value\nfunction convertToPercentage(n) {\n  if (n <= 1) {\n    n = n * 100 + \"%\";\n  }\n  return n;\n}\n\n// Converts a decimal to a hex value\nfunction convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\n// Converts a hex value to a decimal\nfunction convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\nvar matchers = function () {\n  // <http://www.w3.org/TR/css3-values/#integers>\n  var CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n\n  // <http://www.w3.org/TR/css3-values/#number-value>\n  var CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n\n  // Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\n  var CSS_UNIT = \"(?:\" + CSS_NUMBER + \")|(?:\" + CSS_INTEGER + \")\";\n\n  // Actual matching.\n  // Parentheses and commas are optional, but not required.\n  // Whitespace can take the place of commas or opening paren\n  var PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  var PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  return {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n    rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n    hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n    hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n    hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n    hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n  };\n}();\n\n// `isValidCSSUnit`\n// Take in a single string / number and check to see if it looks like a CSS unit\n// (see `matchers` above for definition).\nfunction isValidCSSUnit(color) {\n  return !!matchers.CSS_UNIT.exec(color);\n}\n\n// `stringInputToObject`\n// Permissive string parsing.  Take in a number of formats, and output an object\n// based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\nfunction stringInputToObject(color) {\n  color = color.replace(trimLeft, \"\").replace(trimRight, \"\").toLowerCase();\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color == \"transparent\") {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: \"name\"\n    };\n  }\n\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match;\n  if (match = matchers.rgb.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  if (match = matchers.rgba.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsl.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  if (match = matchers.hsla.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsv.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  if (match = matchers.hsva.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hex8.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex6.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  if (match = matchers.hex4.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      a: convertHexToDecimal(match[4] + \"\" + match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex3.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  return false;\n}\nfunction validateWCAG2Parms(parms) {\n  // return valid WCAG2 parms for isReadable.\n  // If input parms are invalid, return {\"level\":\"AA\", \"size\":\"small\"}\n  var level, size;\n  parms = parms || {\n    level: \"AA\",\n    size: \"small\"\n  };\n  level = (parms.level || \"AA\").toUpperCase();\n  size = (parms.size || \"small\").toLowerCase();\n  if (level !== \"AA\" && level !== \"AAA\") {\n    level = \"AA\";\n  }\n  if (size !== \"small\" && size !== \"large\") {\n    size = \"small\";\n  }\n  return {\n    level: level,\n    size: size\n  };\n}\n\nexport { tinycolor as default };\n"], "mappings": "AAAA;AACA,SAASA,OAAOA,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAClG,OAAO,OAAOA,GAAG;EACnB,CAAC,GAAG,UAAUA,GAAG,EAAE;IACjB,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAC7H,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AACjB;;AAEA;AACA;;AAEA,IAAIK,QAAQ,GAAG,MAAM;AACrB,IAAIC,SAAS,GAAG,MAAM;AACtB,SAASC,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC9BD,KAAK,GAAGA,KAAK,GAAGA,KAAK,GAAG,EAAE;EAC1BC,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;;EAEjB;EACA,IAAID,KAAK,YAAYD,SAAS,EAAE;IAC9B,OAAOC,KAAK;EACd;EACA;EACA,IAAI,EAAE,IAAI,YAAYD,SAAS,CAAC,EAAE;IAChC,OAAO,IAAIA,SAAS,CAACC,KAAK,EAAEC,IAAI,CAAC;EACnC;EACA,IAAIC,GAAG,GAAGC,UAAU,CAACH,KAAK,CAAC;EAC3B,IAAI,CAACI,cAAc,GAAGJ,KAAK,EAAE,IAAI,CAACK,EAAE,GAAGH,GAAG,CAACI,CAAC,EAAE,IAAI,CAACC,EAAE,GAAGL,GAAG,CAACM,CAAC,EAAE,IAAI,CAACC,EAAE,GAAGP,GAAG,CAACQ,CAAC,EAAE,IAAI,CAACC,EAAE,GAAGT,GAAG,CAACU,CAAC,EAAE,IAAI,CAACC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,IAAI,CAACJ,EAAE,CAAC,GAAG,GAAG,EAAE,IAAI,CAACK,OAAO,GAAGf,IAAI,CAACgB,MAAM,IAAIf,GAAG,CAACe,MAAM;EACzL,IAAI,CAACC,aAAa,GAAGjB,IAAI,CAACkB,YAAY;;EAEtC;EACA;EACA;EACA;EACA,IAAI,IAAI,CAACd,EAAE,GAAG,CAAC,EAAE,IAAI,CAACA,EAAE,GAAGS,IAAI,CAACC,KAAK,CAAC,IAAI,CAACV,EAAE,CAAC;EAC9C,IAAI,IAAI,CAACE,EAAE,GAAG,CAAC,EAAE,IAAI,CAACA,EAAE,GAAGO,IAAI,CAACC,KAAK,CAAC,IAAI,CAACR,EAAE,CAAC;EAC9C,IAAI,IAAI,CAACE,EAAE,GAAG,CAAC,EAAE,IAAI,CAACA,EAAE,GAAGK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,EAAE,CAAC;EAC9C,IAAI,CAACW,GAAG,GAAGlB,GAAG,CAACmB,EAAE;AACnB;AACAtB,SAAS,CAACH,SAAS,GAAG;EACpB0B,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC,GAAG,GAAG;EACnC,CAAC;EACDC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,OAAO,CAAC,IAAI,CAACF,MAAM,CAAC,CAAC;EACvB,CAAC;EACDG,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACL,GAAG;EACjB,CAAC;EACDM,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;IAC5C,OAAO,IAAI,CAACtB,cAAc;EAC5B,CAAC;EACDuB,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAACX,OAAO;EACrB,CAAC;EACDY,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAACjB,EAAE;EAChB,CAAC;EACDY,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;IACtC;IACA,IAAIrB,GAAG,GAAG,IAAI,CAAC2B,KAAK,CAAC,CAAC;IACtB,OAAO,CAAC3B,GAAG,CAACI,CAAC,GAAG,GAAG,GAAGJ,GAAG,CAACM,CAAC,GAAG,GAAG,GAAGN,GAAG,CAACQ,CAAC,GAAG,GAAG,IAAI,IAAI;EACzD,CAAC;EACDoB,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;IACpC;IACA,IAAI5B,GAAG,GAAG,IAAI,CAAC2B,KAAK,CAAC,CAAC;IACtB,IAAIE,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC;IAChCL,KAAK,GAAG7B,GAAG,CAACI,CAAC,GAAG,GAAG;IACnB0B,KAAK,GAAG9B,GAAG,CAACM,CAAC,GAAG,GAAG;IACnByB,KAAK,GAAG/B,GAAG,CAACQ,CAAC,GAAG,GAAG;IACnB,IAAIqB,KAAK,IAAI,OAAO,EAAEG,CAAC,GAAGH,KAAK,GAAG,KAAK,CAAC,KAAKG,CAAC,GAAGpB,IAAI,CAACuB,GAAG,CAAC,CAACN,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IACvF,IAAIC,KAAK,IAAI,OAAO,EAAEG,CAAC,GAAGH,KAAK,GAAG,KAAK,CAAC,KAAKG,CAAC,GAAGrB,IAAI,CAACuB,GAAG,CAAC,CAACL,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IACvF,IAAIC,KAAK,IAAI,OAAO,EAAEG,CAAC,GAAGH,KAAK,GAAG,KAAK,CAAC,KAAKG,CAAC,GAAGtB,IAAI,CAACuB,GAAG,CAAC,CAACJ,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IACvF,OAAO,MAAM,GAAGC,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,MAAM,GAAGC,CAAC;EAC7C,CAAC;EACDE,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;IACjC,IAAI,CAAC5B,EAAE,GAAG6B,UAAU,CAACD,KAAK,CAAC;IAC3B,IAAI,CAAC1B,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,IAAI,CAACJ,EAAE,CAAC,GAAG,GAAG;IAC9C,OAAO,IAAI;EACb,CAAC;EACD8B,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;IACtB,IAAIC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACtC,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,CAAC;IAC7C,OAAO;MACLmC,CAAC,EAAEF,GAAG,CAACE,CAAC,GAAG,GAAG;MACdC,CAAC,EAAEH,GAAG,CAACG,CAAC;MACRC,CAAC,EAAEJ,GAAG,CAACI,CAAC;MACRlC,CAAC,EAAE,IAAI,CAACD;IACV,CAAC;EACH,CAAC;EACDoC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;IAClC,IAAIL,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACtC,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,CAAC;IAC7C,IAAImC,CAAC,GAAG9B,IAAI,CAACC,KAAK,CAAC2B,GAAG,CAACE,CAAC,GAAG,GAAG,CAAC;MAC7BC,CAAC,GAAG/B,IAAI,CAACC,KAAK,CAAC2B,GAAG,CAACG,CAAC,GAAG,GAAG,CAAC;MAC3BC,CAAC,GAAGhC,IAAI,CAACC,KAAK,CAAC2B,GAAG,CAACI,CAAC,GAAG,GAAG,CAAC;IAC7B,OAAO,IAAI,CAACnC,EAAE,IAAI,CAAC,GAAG,MAAM,GAAGiC,CAAC,GAAG,IAAI,GAAGC,CAAC,GAAG,KAAK,GAAGC,CAAC,GAAG,IAAI,GAAG,OAAO,GAAGF,CAAC,GAAG,IAAI,GAAGC,CAAC,GAAG,KAAK,GAAGC,CAAC,GAAG,KAAK,GAAG,IAAI,CAACjC,OAAO,GAAG,GAAG;EAClI,CAAC;EACDmC,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;IACtB,IAAIC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAAC7C,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,CAAC;IAC7C,OAAO;MACLmC,CAAC,EAAEK,GAAG,CAACL,CAAC,GAAG,GAAG;MACdC,CAAC,EAAEI,GAAG,CAACJ,CAAC;MACRM,CAAC,EAAEF,GAAG,CAACE,CAAC;MACRvC,CAAC,EAAE,IAAI,CAACD;IACV,CAAC;EACH,CAAC;EACDyC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;IAClC,IAAIH,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAAC7C,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,CAAC;IAC7C,IAAImC,CAAC,GAAG9B,IAAI,CAACC,KAAK,CAACkC,GAAG,CAACL,CAAC,GAAG,GAAG,CAAC;MAC7BC,CAAC,GAAG/B,IAAI,CAACC,KAAK,CAACkC,GAAG,CAACJ,CAAC,GAAG,GAAG,CAAC;MAC3BM,CAAC,GAAGrC,IAAI,CAACC,KAAK,CAACkC,GAAG,CAACE,CAAC,GAAG,GAAG,CAAC;IAC7B,OAAO,IAAI,CAACxC,EAAE,IAAI,CAAC,GAAG,MAAM,GAAGiC,CAAC,GAAG,IAAI,GAAGC,CAAC,GAAG,KAAK,GAAGM,CAAC,GAAG,IAAI,GAAG,OAAO,GAAGP,CAAC,GAAG,IAAI,GAAGC,CAAC,GAAG,KAAK,GAAGM,CAAC,GAAG,KAAK,GAAG,IAAI,CAACtC,OAAO,GAAG,GAAG;EAClI,CAAC;EACDwC,KAAK,EAAE,SAASA,KAAKA,CAACC,UAAU,EAAE;IAChC,OAAOC,QAAQ,CAAC,IAAI,CAAClD,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE6C,UAAU,CAAC;EACxD,CAAC;EACDE,WAAW,EAAE,SAASA,WAAWA,CAACF,UAAU,EAAE;IAC5C,OAAO,GAAG,GAAG,IAAI,CAACD,KAAK,CAACC,UAAU,CAAC;EACrC,CAAC;EACDG,MAAM,EAAE,SAASA,MAAMA,CAACC,UAAU,EAAE;IAClC,OAAOC,SAAS,CAAC,IAAI,CAACtD,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE+C,UAAU,CAAC;EAClE,CAAC;EACDE,YAAY,EAAE,SAASA,YAAYA,CAACF,UAAU,EAAE;IAC9C,OAAO,GAAG,GAAG,IAAI,CAACD,MAAM,CAACC,UAAU,CAAC;EACtC,CAAC;EACD7B,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;IACtB,OAAO;MACLvB,CAAC,EAAEQ,IAAI,CAACC,KAAK,CAAC,IAAI,CAACV,EAAE,CAAC;MACtBG,CAAC,EAAEM,IAAI,CAACC,KAAK,CAAC,IAAI,CAACR,EAAE,CAAC;MACtBG,CAAC,EAAEI,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,EAAE,CAAC;MACtBG,CAAC,EAAE,IAAI,CAACD;IACV,CAAC;EACH,CAAC;EACDkD,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;IAClC,OAAO,IAAI,CAAClD,EAAE,IAAI,CAAC,GAAG,MAAM,GAAGG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACV,EAAE,CAAC,GAAG,IAAI,GAAGS,IAAI,CAACC,KAAK,CAAC,IAAI,CAACR,EAAE,CAAC,GAAG,IAAI,GAAGO,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,EAAE,CAAC,GAAG,GAAG,GAAG,OAAO,GAAGK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACV,EAAE,CAAC,GAAG,IAAI,GAAGS,IAAI,CAACC,KAAK,CAAC,IAAI,CAACR,EAAE,CAAC,GAAG,IAAI,GAAGO,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAACI,OAAO,GAAG,GAAG;EAC1O,CAAC;EACDiD,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;IAC1C,OAAO;MACLxD,CAAC,EAAEQ,IAAI,CAACC,KAAK,CAACgD,OAAO,CAAC,IAAI,CAAC1D,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;MAChDG,CAAC,EAAEM,IAAI,CAACC,KAAK,CAACgD,OAAO,CAAC,IAAI,CAACxD,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;MAChDG,CAAC,EAAEI,IAAI,CAACC,KAAK,CAACgD,OAAO,CAAC,IAAI,CAACtD,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;MAChDG,CAAC,EAAE,IAAI,CAACD;IACV,CAAC;EACH,CAAC;EACDqD,qBAAqB,EAAE,SAASA,qBAAqBA,CAAA,EAAG;IACtD,OAAO,IAAI,CAACrD,EAAE,IAAI,CAAC,GAAG,MAAM,GAAGG,IAAI,CAACC,KAAK,CAACgD,OAAO,CAAC,IAAI,CAAC1D,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAGS,IAAI,CAACC,KAAK,CAACgD,OAAO,CAAC,IAAI,CAACxD,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAGO,IAAI,CAACC,KAAK,CAACgD,OAAO,CAAC,IAAI,CAACtD,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,OAAO,GAAGK,IAAI,CAACC,KAAK,CAACgD,OAAO,CAAC,IAAI,CAAC1D,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAGS,IAAI,CAACC,KAAK,CAACgD,OAAO,CAAC,IAAI,CAACxD,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAGO,IAAI,CAACC,KAAK,CAACgD,OAAO,CAAC,IAAI,CAACtD,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAACI,OAAO,GAAG,GAAG;EACxW,CAAC;EACDoD,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACtD,EAAE,KAAK,CAAC,EAAE;MACjB,OAAO,aAAa;IACtB;IACA,IAAI,IAAI,CAACA,EAAE,GAAG,CAAC,EAAE;MACf,OAAO,KAAK;IACd;IACA,OAAOuD,QAAQ,CAACX,QAAQ,CAAC,IAAI,CAAClD,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,KAAK;EACrE,CAAC;EACD0D,QAAQ,EAAE,SAASA,QAAQA,CAACC,WAAW,EAAE;IACvC,IAAIC,UAAU,GAAG,GAAG,GAAGC,aAAa,CAAC,IAAI,CAACjE,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACE,EAAE,CAAC;IACxE,IAAI4D,gBAAgB,GAAGF,UAAU;IACjC,IAAIlD,YAAY,GAAG,IAAI,CAACD,aAAa,GAAG,oBAAoB,GAAG,EAAE;IACjE,IAAIkD,WAAW,EAAE;MACf,IAAIvB,CAAC,GAAG9C,SAAS,CAACqE,WAAW,CAAC;MAC9BG,gBAAgB,GAAG,GAAG,GAAGD,aAAa,CAACzB,CAAC,CAACxC,EAAE,EAAEwC,CAAC,CAACtC,EAAE,EAAEsC,CAAC,CAACpC,EAAE,EAAEoC,CAAC,CAAClC,EAAE,CAAC;IAChE;IACA,OAAO,6CAA6C,GAAGQ,YAAY,GAAG,gBAAgB,GAAGkD,UAAU,GAAG,eAAe,GAAGE,gBAAgB,GAAG,GAAG;EAChJ,CAAC;EACDC,QAAQ,EAAE,SAASA,QAAQA,CAACvD,MAAM,EAAE;IAClC,IAAIwD,SAAS,GAAG,CAAC,CAACxD,MAAM;IACxBA,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACD,OAAO;IAC/B,IAAI0D,eAAe,GAAG,KAAK;IAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAChE,EAAE,GAAG,CAAC,IAAI,IAAI,CAACA,EAAE,IAAI,CAAC;IAC1C,IAAIiE,gBAAgB,GAAG,CAACH,SAAS,IAAIE,QAAQ,KAAK1D,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,MAAM,CAAC;IAC5K,IAAI2D,gBAAgB,EAAE;MACpB;MACA;MACA,IAAI3D,MAAM,KAAK,MAAM,IAAI,IAAI,CAACN,EAAE,KAAK,CAAC,EAAE;QACtC,OAAO,IAAI,CAACsD,MAAM,CAAC,CAAC;MACtB;MACA,OAAO,IAAI,CAACJ,WAAW,CAAC,CAAC;IAC3B;IACA,IAAI5C,MAAM,KAAK,KAAK,EAAE;MACpByD,eAAe,GAAG,IAAI,CAACb,WAAW,CAAC,CAAC;IACtC;IACA,IAAI5C,MAAM,KAAK,MAAM,EAAE;MACrByD,eAAe,GAAG,IAAI,CAACV,qBAAqB,CAAC,CAAC;IAChD;IACA,IAAI/C,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;MACzCyD,eAAe,GAAG,IAAI,CAAClB,WAAW,CAAC,CAAC;IACtC;IACA,IAAIvC,MAAM,KAAK,MAAM,EAAE;MACrByD,eAAe,GAAG,IAAI,CAAClB,WAAW,CAAC,IAAI,CAAC;IAC1C;IACA,IAAIvC,MAAM,KAAK,MAAM,EAAE;MACrByD,eAAe,GAAG,IAAI,CAACd,YAAY,CAAC,IAAI,CAAC;IAC3C;IACA,IAAI3C,MAAM,KAAK,MAAM,EAAE;MACrByD,eAAe,GAAG,IAAI,CAACd,YAAY,CAAC,CAAC;IACvC;IACA,IAAI3C,MAAM,KAAK,MAAM,EAAE;MACrByD,eAAe,GAAG,IAAI,CAACT,MAAM,CAAC,CAAC;IACjC;IACA,IAAIhD,MAAM,KAAK,KAAK,EAAE;MACpByD,eAAe,GAAG,IAAI,CAACtB,WAAW,CAAC,CAAC;IACtC;IACA,IAAInC,MAAM,KAAK,KAAK,EAAE;MACpByD,eAAe,GAAG,IAAI,CAAC3B,WAAW,CAAC,CAAC;IACtC;IACA,OAAO2B,eAAe,IAAI,IAAI,CAAClB,WAAW,CAAC,CAAC;EAC9C,CAAC;EACDqB,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;IACtB,OAAO9E,SAAS,CAAC,IAAI,CAACyE,QAAQ,CAAC,CAAC,CAAC;EACnC,CAAC;EACDM,kBAAkB,EAAE,SAASA,kBAAkBA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACxD,IAAIhF,KAAK,GAAG+E,EAAE,CAACE,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,EAAE,CAACC,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC;IAC9D,IAAI,CAAC3E,EAAE,GAAGL,KAAK,CAACK,EAAE;IAClB,IAAI,CAACE,EAAE,GAAGP,KAAK,CAACO,EAAE;IAClB,IAAI,CAACE,EAAE,GAAGT,KAAK,CAACS,EAAE;IAClB,IAAI,CAAC6B,QAAQ,CAACtC,KAAK,CAACW,EAAE,CAAC;IACvB,OAAO,IAAI;EACb,CAAC;EACD0E,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACP,kBAAkB,CAACQ,QAAQ,EAAEC,SAAS,CAAC;EACrD,CAAC;EACDC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAACV,kBAAkB,CAACW,SAAS,EAAEF,SAAS,CAAC;EACtD,CAAC;EACDG,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACZ,kBAAkB,CAACa,OAAO,EAAEJ,SAAS,CAAC;EACpD,CAAC;EACDK,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,OAAO,IAAI,CAACd,kBAAkB,CAACe,WAAW,EAAEN,SAAS,CAAC;EACxD,CAAC;EACDO,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAAChB,kBAAkB,CAACiB,SAAS,EAAER,SAAS,CAAC;EACtD,CAAC;EACDS,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAAClB,kBAAkB,CAACmB,UAAU,EAAEV,SAAS,CAAC;EACvD,CAAC;EACDW,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpB,kBAAkB,CAACqB,KAAK,EAAEZ,SAAS,CAAC;EAClD,CAAC;EACDa,iBAAiB,EAAE,SAASA,iBAAiBA,CAACrB,EAAE,EAAEC,IAAI,EAAE;IACtD,OAAOD,EAAE,CAACE,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,EAAE,CAACC,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC;EAC3D,CAAC;EACDqB,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAACD,iBAAiB,CAACE,UAAU,EAAEf,SAAS,CAAC;EACtD,CAAC;EACDgB,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,OAAO,IAAI,CAACH,iBAAiB,CAACI,WAAW,EAAEjB,SAAS,CAAC;EACvD,CAAC;EACDkB,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;IACtC,OAAO,IAAI,CAACL,iBAAiB,CAACM,cAAc,EAAEnB,SAAS,CAAC;EAC1D,CAAC;EACDoB,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;IAC1C,OAAO,IAAI,CAACP,iBAAiB,CAACQ,gBAAgB,EAAErB,SAAS,CAAC;EAC5D,CAAC;EACD;EACA;EACA;EACA;EACAsB,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACT,iBAAiB,CAACU,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC;EACDC,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACX,iBAAiB,CAACU,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5C;AACF,CAAC;;AAED;AACA;AACA/G,SAAS,CAACiH,SAAS,GAAG,UAAUhH,KAAK,EAAEC,IAAI,EAAE;EAC3C,IAAIV,OAAO,CAACS,KAAK,CAAC,IAAI,QAAQ,EAAE;IAC9B,IAAIiH,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAIC,CAAC,IAAIlH,KAAK,EAAE;MACnB,IAAIA,KAAK,CAACmH,cAAc,CAACD,CAAC,CAAC,EAAE;QAC3B,IAAIA,CAAC,KAAK,GAAG,EAAE;UACbD,QAAQ,CAACC,CAAC,CAAC,GAAGlH,KAAK,CAACkH,CAAC,CAAC;QACxB,CAAC,MAAM;UACLD,QAAQ,CAACC,CAAC,CAAC,GAAGE,mBAAmB,CAACpH,KAAK,CAACkH,CAAC,CAAC,CAAC;QAC7C;MACF;IACF;IACAlH,KAAK,GAAGiH,QAAQ;EAClB;EACA,OAAOlH,SAAS,CAACC,KAAK,EAAEC,IAAI,CAAC;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACH,KAAK,EAAE;EACzB,IAAIE,GAAG,GAAG;IACRI,CAAC,EAAE,CAAC;IACJE,CAAC,EAAE,CAAC;IACJE,CAAC,EAAE;EACL,CAAC;EACD,IAAIE,CAAC,GAAG,CAAC;EACT,IAAIiC,CAAC,GAAG,IAAI;EACZ,IAAIC,CAAC,GAAG,IAAI;EACZ,IAAIK,CAAC,GAAG,IAAI;EACZ,IAAI9B,EAAE,GAAG,KAAK;EACd,IAAIJ,MAAM,GAAG,KAAK;EAClB,IAAI,OAAOjB,KAAK,IAAI,QAAQ,EAAE;IAC5BA,KAAK,GAAGqH,mBAAmB,CAACrH,KAAK,CAAC;EACpC;EACA,IAAIT,OAAO,CAACS,KAAK,CAAC,IAAI,QAAQ,EAAE;IAC9B,IAAIsH,cAAc,CAACtH,KAAK,CAACM,CAAC,CAAC,IAAIgH,cAAc,CAACtH,KAAK,CAACQ,CAAC,CAAC,IAAI8G,cAAc,CAACtH,KAAK,CAACU,CAAC,CAAC,EAAE;MACjFR,GAAG,GAAGqH,QAAQ,CAACvH,KAAK,CAACM,CAAC,EAAEN,KAAK,CAACQ,CAAC,EAAER,KAAK,CAACU,CAAC,CAAC;MACzCW,EAAE,GAAG,IAAI;MACTJ,MAAM,GAAGuG,MAAM,CAACxH,KAAK,CAACM,CAAC,CAAC,CAACmH,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK;IAC9D,CAAC,MAAM,IAAIH,cAAc,CAACtH,KAAK,CAAC4C,CAAC,CAAC,IAAI0E,cAAc,CAACtH,KAAK,CAAC6C,CAAC,CAAC,IAAIyE,cAAc,CAACtH,KAAK,CAAC8C,CAAC,CAAC,EAAE;MACxFD,CAAC,GAAGuE,mBAAmB,CAACpH,KAAK,CAAC6C,CAAC,CAAC;MAChCC,CAAC,GAAGsE,mBAAmB,CAACpH,KAAK,CAAC8C,CAAC,CAAC;MAChC5C,GAAG,GAAGwH,QAAQ,CAAC1H,KAAK,CAAC4C,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAC7BzB,EAAE,GAAG,IAAI;MACTJ,MAAM,GAAG,KAAK;IAChB,CAAC,MAAM,IAAIqG,cAAc,CAACtH,KAAK,CAAC4C,CAAC,CAAC,IAAI0E,cAAc,CAACtH,KAAK,CAAC6C,CAAC,CAAC,IAAIyE,cAAc,CAACtH,KAAK,CAACmD,CAAC,CAAC,EAAE;MACxFN,CAAC,GAAGuE,mBAAmB,CAACpH,KAAK,CAAC6C,CAAC,CAAC;MAChCM,CAAC,GAAGiE,mBAAmB,CAACpH,KAAK,CAACmD,CAAC,CAAC;MAChCjD,GAAG,GAAGyH,QAAQ,CAAC3H,KAAK,CAAC4C,CAAC,EAAEC,CAAC,EAAEM,CAAC,CAAC;MAC7B9B,EAAE,GAAG,IAAI;MACTJ,MAAM,GAAG,KAAK;IAChB;IACA,IAAIjB,KAAK,CAACmH,cAAc,CAAC,GAAG,CAAC,EAAE;MAC7BvG,CAAC,GAAGZ,KAAK,CAACY,CAAC;IACb;EACF;EACAA,CAAC,GAAG4B,UAAU,CAAC5B,CAAC,CAAC;EACjB,OAAO;IACLS,EAAE,EAAEA,EAAE;IACNJ,MAAM,EAAEjB,KAAK,CAACiB,MAAM,IAAIA,MAAM;IAC9BX,CAAC,EAAEQ,IAAI,CAAC8G,GAAG,CAAC,GAAG,EAAE9G,IAAI,CAAC+G,GAAG,CAAC3H,GAAG,CAACI,CAAC,EAAE,CAAC,CAAC,CAAC;IACpCE,CAAC,EAAEM,IAAI,CAAC8G,GAAG,CAAC,GAAG,EAAE9G,IAAI,CAAC+G,GAAG,CAAC3H,GAAG,CAACM,CAAC,EAAE,CAAC,CAAC,CAAC;IACpCE,CAAC,EAAEI,IAAI,CAAC8G,GAAG,CAAC,GAAG,EAAE9G,IAAI,CAAC+G,GAAG,CAAC3H,GAAG,CAACQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACpCE,CAAC,EAAEA;EACL,CAAC;AACH;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS2G,QAAQA,CAACjH,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAE;EACzB,OAAO;IACLJ,CAAC,EAAEyD,OAAO,CAACzD,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG;IACxBE,CAAC,EAAEuD,OAAO,CAACvD,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG;IACxBE,CAAC,EAAEqD,OAAO,CAACrD,CAAC,EAAE,GAAG,CAAC,GAAG;EACvB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASwC,QAAQA,CAAC5C,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAE;EACzBJ,CAAC,GAAGyD,OAAO,CAACzD,CAAC,EAAE,GAAG,CAAC;EACnBE,CAAC,GAAGuD,OAAO,CAACvD,CAAC,EAAE,GAAG,CAAC;EACnBE,CAAC,GAAGqD,OAAO,CAACrD,CAAC,EAAE,GAAG,CAAC;EACnB,IAAImH,GAAG,GAAG/G,IAAI,CAAC+G,GAAG,CAACvH,CAAC,EAAEE,CAAC,EAAEE,CAAC,CAAC;IACzBkH,GAAG,GAAG9G,IAAI,CAAC8G,GAAG,CAACtH,CAAC,EAAEE,CAAC,EAAEE,CAAC,CAAC;EACzB,IAAIkC,CAAC;IACHC,CAAC;IACDM,CAAC,GAAG,CAAC0E,GAAG,GAAGD,GAAG,IAAI,CAAC;EACrB,IAAIC,GAAG,IAAID,GAAG,EAAE;IACdhF,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,CAAC;EACb,CAAC,MAAM;IACL,IAAIiF,CAAC,GAAGD,GAAG,GAAGD,GAAG;IACjB/E,CAAC,GAAGM,CAAC,GAAG,GAAG,GAAG2E,CAAC,IAAI,CAAC,GAAGD,GAAG,GAAGD,GAAG,CAAC,GAAGE,CAAC,IAAID,GAAG,GAAGD,GAAG,CAAC;IACnD,QAAQC,GAAG;MACT,KAAKvH,CAAC;QACJsC,CAAC,GAAG,CAACpC,CAAC,GAAGE,CAAC,IAAIoH,CAAC,IAAItH,CAAC,GAAGE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC;MACF,KAAKF,CAAC;QACJoC,CAAC,GAAG,CAAClC,CAAC,GAAGJ,CAAC,IAAIwH,CAAC,GAAG,CAAC;QACnB;MACF,KAAKpH,CAAC;QACJkC,CAAC,GAAG,CAACtC,CAAC,GAAGE,CAAC,IAAIsH,CAAC,GAAG,CAAC;QACnB;IACJ;IACAlF,CAAC,IAAI,CAAC;EACR;EACA,OAAO;IACLA,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJM,CAAC,EAAEA;EACL,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASwE,QAAQA,CAAC/E,CAAC,EAAEC,CAAC,EAAEM,CAAC,EAAE;EACzB,IAAI7C,CAAC,EAAEE,CAAC,EAAEE,CAAC;EACXkC,CAAC,GAAGmB,OAAO,CAACnB,CAAC,EAAE,GAAG,CAAC;EACnBC,CAAC,GAAGkB,OAAO,CAAClB,CAAC,EAAE,GAAG,CAAC;EACnBM,CAAC,GAAGY,OAAO,CAACZ,CAAC,EAAE,GAAG,CAAC;EACnB,SAAS4E,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACxB,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC;IACjB,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC;IACjB,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,IAAI,CAAC,GAAGE,CAAC;IACzC,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAOD,CAAC;IACvB,IAAIC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGE,CAAC,CAAC,GAAG,CAAC;IACnD,OAAOF,CAAC;EACV;EACA,IAAInF,CAAC,KAAK,CAAC,EAAE;IACXvC,CAAC,GAAGE,CAAC,GAAGE,CAAC,GAAGyC,CAAC,CAAC,CAAC;EACjB,CAAC,MAAM;IACL,IAAI8E,CAAC,GAAG9E,CAAC,GAAG,GAAG,GAAGA,CAAC,IAAI,CAAC,GAAGN,CAAC,CAAC,GAAGM,CAAC,GAAGN,CAAC,GAAGM,CAAC,GAAGN,CAAC;IAC7C,IAAImF,CAAC,GAAG,CAAC,GAAG7E,CAAC,GAAG8E,CAAC;IACjB3H,CAAC,GAAGyH,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAErF,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5BpC,CAAC,GAAGuH,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAErF,CAAC,CAAC;IACpBlC,CAAC,GAAGqH,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAErF,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC9B;EACA,OAAO;IACLtC,CAAC,EAAEA,CAAC,GAAG,GAAG;IACVE,CAAC,EAAEA,CAAC,GAAG,GAAG;IACVE,CAAC,EAAEA,CAAC,GAAG;EACT,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASiC,QAAQA,CAACrC,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAE;EACzBJ,CAAC,GAAGyD,OAAO,CAACzD,CAAC,EAAE,GAAG,CAAC;EACnBE,CAAC,GAAGuD,OAAO,CAACvD,CAAC,EAAE,GAAG,CAAC;EACnBE,CAAC,GAAGqD,OAAO,CAACrD,CAAC,EAAE,GAAG,CAAC;EACnB,IAAImH,GAAG,GAAG/G,IAAI,CAAC+G,GAAG,CAACvH,CAAC,EAAEE,CAAC,EAAEE,CAAC,CAAC;IACzBkH,GAAG,GAAG9G,IAAI,CAAC8G,GAAG,CAACtH,CAAC,EAAEE,CAAC,EAAEE,CAAC,CAAC;EACzB,IAAIkC,CAAC;IACHC,CAAC;IACDC,CAAC,GAAG+E,GAAG;EACT,IAAIC,CAAC,GAAGD,GAAG,GAAGD,GAAG;EACjB/E,CAAC,GAAGgF,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGC,CAAC,GAAGD,GAAG;EAC3B,IAAIA,GAAG,IAAID,GAAG,EAAE;IACdhF,CAAC,GAAG,CAAC,CAAC,CAAC;EACT,CAAC,MAAM;IACL,QAAQiF,GAAG;MACT,KAAKvH,CAAC;QACJsC,CAAC,GAAG,CAACpC,CAAC,GAAGE,CAAC,IAAIoH,CAAC,IAAItH,CAAC,GAAGE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC;MACF,KAAKF,CAAC;QACJoC,CAAC,GAAG,CAAClC,CAAC,GAAGJ,CAAC,IAAIwH,CAAC,GAAG,CAAC;QACnB;MACF,KAAKpH,CAAC;QACJkC,CAAC,GAAG,CAACtC,CAAC,GAAGE,CAAC,IAAIsH,CAAC,GAAG,CAAC;QACnB;IACJ;IACAlF,CAAC,IAAI,CAAC;EACR;EACA,OAAO;IACLA,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA;EACL,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAAS4E,QAAQA,CAAC9E,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACzBF,CAAC,GAAGmB,OAAO,CAACnB,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC;EACvBC,CAAC,GAAGkB,OAAO,CAAClB,CAAC,EAAE,GAAG,CAAC;EACnBC,CAAC,GAAGiB,OAAO,CAACjB,CAAC,EAAE,GAAG,CAAC;EACnB,IAAIoE,CAAC,GAAGpG,IAAI,CAACqH,KAAK,CAACvF,CAAC,CAAC;IACnBwF,CAAC,GAAGxF,CAAC,GAAGsE,CAAC;IACTc,CAAC,GAAGlF,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAC;IACfoF,CAAC,GAAGnF,CAAC,IAAI,CAAC,GAAGsF,CAAC,GAAGvF,CAAC,CAAC;IACnBqF,CAAC,GAAGpF,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGsF,CAAC,IAAIvF,CAAC,CAAC;IACzBwF,GAAG,GAAGnB,CAAC,GAAG,CAAC;IACX5G,CAAC,GAAG,CAACwC,CAAC,EAAEmF,CAAC,EAAED,CAAC,EAAEA,CAAC,EAAEE,CAAC,EAAEpF,CAAC,CAAC,CAACuF,GAAG,CAAC;IAC3B7H,CAAC,GAAG,CAAC0H,CAAC,EAAEpF,CAAC,EAAEA,CAAC,EAAEmF,CAAC,EAAED,CAAC,EAAEA,CAAC,CAAC,CAACK,GAAG,CAAC;IAC3B3H,CAAC,GAAG,CAACsH,CAAC,EAAEA,CAAC,EAAEE,CAAC,EAAEpF,CAAC,EAAEA,CAAC,EAAEmF,CAAC,CAAC,CAACI,GAAG,CAAC;EAC7B,OAAO;IACL/H,CAAC,EAAEA,CAAC,GAAG,GAAG;IACVE,CAAC,EAAEA,CAAC,GAAG,GAAG;IACVE,CAAC,EAAEA,CAAC,GAAG;EACT,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAAS6C,QAAQA,CAACjD,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAE4C,UAAU,EAAE;EACrC,IAAIgF,GAAG,GAAG,CAACC,IAAI,CAACzH,IAAI,CAACC,KAAK,CAACT,CAAC,CAAC,CAACkE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE+D,IAAI,CAACzH,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC,CAACgE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE+D,IAAI,CAACzH,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC,CAAC8D,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEhH;EACA,IAAIlB,UAAU,IAAIgF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,EAAE;IACtI,OAAOF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC;EAC/D;EACA,OAAOF,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;AACrB;;AAEA;AACA;AACA;AACA;AACA,SAAS9E,SAASA,CAACrD,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAE8C,UAAU,EAAE;EACzC,IAAI4E,GAAG,GAAG,CAACC,IAAI,CAACzH,IAAI,CAACC,KAAK,CAACT,CAAC,CAAC,CAACkE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE+D,IAAI,CAACzH,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC,CAACgE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE+D,IAAI,CAACzH,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC,CAAC8D,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE+D,IAAI,CAACG,mBAAmB,CAAC9H,CAAC,CAAC,CAAC,CAAC;;EAE9I;EACA,IAAI8C,UAAU,IAAI4E,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,EAAE;IAC9K,OAAOF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC;EAClF;EACA,OAAOF,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;AACrB;;AAEA;AACA;AACA;AACA,SAASnE,aAAaA,CAAChE,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAE;EACjC,IAAI0H,GAAG,GAAG,CAACC,IAAI,CAACG,mBAAmB,CAAC9H,CAAC,CAAC,CAAC,EAAE2H,IAAI,CAACzH,IAAI,CAACC,KAAK,CAACT,CAAC,CAAC,CAACkE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE+D,IAAI,CAACzH,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC,CAACgE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE+D,IAAI,CAACzH,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC,CAAC8D,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9I,OAAO8D,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;AACrB;;AAEA;AACA;AACA1I,SAAS,CAAC4I,MAAM,GAAG,UAAUC,MAAM,EAAEC,MAAM,EAAE;EAC3C,IAAI,CAACD,MAAM,IAAI,CAACC,MAAM,EAAE,OAAO,KAAK;EACpC,OAAO9I,SAAS,CAAC6I,MAAM,CAAC,CAAC/E,WAAW,CAAC,CAAC,IAAI9D,SAAS,CAAC8I,MAAM,CAAC,CAAChF,WAAW,CAAC,CAAC;AAC3E,CAAC;AACD9D,SAAS,CAAC+I,MAAM,GAAG,YAAY;EAC7B,OAAO/I,SAAS,CAACiH,SAAS,CAAC;IACzB1G,CAAC,EAAEQ,IAAI,CAACgI,MAAM,CAAC,CAAC;IAChBtI,CAAC,EAAEM,IAAI,CAACgI,MAAM,CAAC,CAAC;IAChBpI,CAAC,EAAEI,IAAI,CAACgI,MAAM,CAAC;EACjB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;;AAEA,SAASjD,WAAWA,CAAC7F,KAAK,EAAE+I,MAAM,EAAE;EAClCA,MAAM,GAAGA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,IAAI,EAAE;EACxC,IAAI9F,GAAG,GAAGlD,SAAS,CAACC,KAAK,CAAC,CAACgD,KAAK,CAAC,CAAC;EAClCC,GAAG,CAACJ,CAAC,IAAIkG,MAAM,GAAG,GAAG;EACrB9F,GAAG,CAACJ,CAAC,GAAGmG,OAAO,CAAC/F,GAAG,CAACJ,CAAC,CAAC;EACtB,OAAO9C,SAAS,CAACkD,GAAG,CAAC;AACvB;AACA,SAAS8C,SAASA,CAAC/F,KAAK,EAAE+I,MAAM,EAAE;EAChCA,MAAM,GAAGA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,IAAI,EAAE;EACxC,IAAI9F,GAAG,GAAGlD,SAAS,CAACC,KAAK,CAAC,CAACgD,KAAK,CAAC,CAAC;EAClCC,GAAG,CAACJ,CAAC,IAAIkG,MAAM,GAAG,GAAG;EACrB9F,GAAG,CAACJ,CAAC,GAAGmG,OAAO,CAAC/F,GAAG,CAACJ,CAAC,CAAC;EACtB,OAAO9C,SAAS,CAACkD,GAAG,CAAC;AACvB;AACA,SAASgD,UAAUA,CAACjG,KAAK,EAAE;EACzB,OAAOD,SAAS,CAACC,KAAK,CAAC,CAAC4F,UAAU,CAAC,GAAG,CAAC;AACzC;AACA,SAASN,QAAQA,CAACtF,KAAK,EAAE+I,MAAM,EAAE;EAC/BA,MAAM,GAAGA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,IAAI,EAAE;EACxC,IAAI9F,GAAG,GAAGlD,SAAS,CAACC,KAAK,CAAC,CAACgD,KAAK,CAAC,CAAC;EAClCC,GAAG,CAACE,CAAC,IAAI4F,MAAM,GAAG,GAAG;EACrB9F,GAAG,CAACE,CAAC,GAAG6F,OAAO,CAAC/F,GAAG,CAACE,CAAC,CAAC;EACtB,OAAOpD,SAAS,CAACkD,GAAG,CAAC;AACvB;AACA,SAASwC,SAASA,CAACzF,KAAK,EAAE+I,MAAM,EAAE;EAChCA,MAAM,GAAGA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,IAAI,EAAE;EACxC,IAAI7I,GAAG,GAAGH,SAAS,CAACC,KAAK,CAAC,CAAC6B,KAAK,CAAC,CAAC;EAClC3B,GAAG,CAACI,CAAC,GAAGQ,IAAI,CAAC+G,GAAG,CAAC,CAAC,EAAE/G,IAAI,CAAC8G,GAAG,CAAC,GAAG,EAAE1H,GAAG,CAACI,CAAC,GAAGQ,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,EAAEgI,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7E7I,GAAG,CAACM,CAAC,GAAGM,IAAI,CAAC+G,GAAG,CAAC,CAAC,EAAE/G,IAAI,CAAC8G,GAAG,CAAC,GAAG,EAAE1H,GAAG,CAACM,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,EAAEgI,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7E7I,GAAG,CAACQ,CAAC,GAAGI,IAAI,CAAC+G,GAAG,CAAC,CAAC,EAAE/G,IAAI,CAAC8G,GAAG,CAAC,GAAG,EAAE1H,GAAG,CAACQ,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,EAAEgI,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7E,OAAOhJ,SAAS,CAACG,GAAG,CAAC;AACvB;AACA,SAASyF,OAAOA,CAAC3F,KAAK,EAAE+I,MAAM,EAAE;EAC9BA,MAAM,GAAGA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,IAAI,EAAE;EACxC,IAAI9F,GAAG,GAAGlD,SAAS,CAACC,KAAK,CAAC,CAACgD,KAAK,CAAC,CAAC;EAClCC,GAAG,CAACE,CAAC,IAAI4F,MAAM,GAAG,GAAG;EACrB9F,GAAG,CAACE,CAAC,GAAG6F,OAAO,CAAC/F,GAAG,CAACE,CAAC,CAAC;EACtB,OAAOpD,SAAS,CAACkD,GAAG,CAAC;AACvB;;AAEA;AACA;AACA,SAASkD,KAAKA,CAACnG,KAAK,EAAE+I,MAAM,EAAE;EAC5B,IAAI9F,GAAG,GAAGlD,SAAS,CAACC,KAAK,CAAC,CAACgD,KAAK,CAAC,CAAC;EAClC,IAAIiG,GAAG,GAAG,CAAChG,GAAG,CAACL,CAAC,GAAGmG,MAAM,IAAI,GAAG;EAChC9F,GAAG,CAACL,CAAC,GAAGqG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAGA,GAAG,GAAGA,GAAG;EACjC,OAAOlJ,SAAS,CAACkD,GAAG,CAAC;AACvB;;AAEA;AACA;AACA;AACA;;AAEA,SAASuD,WAAWA,CAACxG,KAAK,EAAE;EAC1B,IAAIiD,GAAG,GAAGlD,SAAS,CAACC,KAAK,CAAC,CAACgD,KAAK,CAAC,CAAC;EAClCC,GAAG,CAACL,CAAC,GAAG,CAACK,GAAG,CAACL,CAAC,GAAG,GAAG,IAAI,GAAG;EAC3B,OAAO7C,SAAS,CAACkD,GAAG,CAAC;AACvB;AACA,SAAS6D,MAAMA,CAAC9G,KAAK,EAAEkJ,MAAM,EAAE;EAC7B,IAAIC,KAAK,CAACD,MAAM,CAAC,IAAIA,MAAM,IAAI,CAAC,EAAE;IAChC,MAAM,IAAIE,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,IAAInG,GAAG,GAAGlD,SAAS,CAACC,KAAK,CAAC,CAACgD,KAAK,CAAC,CAAC;EAClC,IAAIqG,MAAM,GAAG,CAACtJ,SAAS,CAACC,KAAK,CAAC,CAAC;EAC/B,IAAIsJ,IAAI,GAAG,GAAG,GAAGJ,MAAM;EACvB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,MAAM,EAAEhC,CAAC,EAAE,EAAE;IAC/BmC,MAAM,CAACE,IAAI,CAACxJ,SAAS,CAAC;MACpB6C,CAAC,EAAE,CAACK,GAAG,CAACL,CAAC,GAAGsE,CAAC,GAAGoC,IAAI,IAAI,GAAG;MAC3BzG,CAAC,EAAEI,GAAG,CAACJ,CAAC;MACRM,CAAC,EAAEF,GAAG,CAACE;IACT,CAAC,CAAC,CAAC;EACL;EACA,OAAOkG,MAAM;AACf;AACA,SAASzC,gBAAgBA,CAAC5G,KAAK,EAAE;EAC/B,IAAIiD,GAAG,GAAGlD,SAAS,CAACC,KAAK,CAAC,CAACgD,KAAK,CAAC,CAAC;EAClC,IAAIJ,CAAC,GAAGK,GAAG,CAACL,CAAC;EACb,OAAO,CAAC7C,SAAS,CAACC,KAAK,CAAC,EAAED,SAAS,CAAC;IAClC6C,CAAC,EAAE,CAACA,CAAC,GAAG,EAAE,IAAI,GAAG;IACjBC,CAAC,EAAEI,GAAG,CAACJ,CAAC;IACRM,CAAC,EAAEF,GAAG,CAACE;EACT,CAAC,CAAC,EAAEpD,SAAS,CAAC;IACZ6C,CAAC,EAAE,CAACA,CAAC,GAAG,GAAG,IAAI,GAAG;IAClBC,CAAC,EAAEI,GAAG,CAACJ,CAAC;IACRM,CAAC,EAAEF,GAAG,CAACE;EACT,CAAC,CAAC,CAAC;AACL;AACA,SAASmD,UAAUA,CAACtG,KAAK,EAAEwJ,OAAO,EAAEC,MAAM,EAAE;EAC1CD,OAAO,GAAGA,OAAO,IAAI,CAAC;EACtBC,MAAM,GAAGA,MAAM,IAAI,EAAE;EACrB,IAAIxG,GAAG,GAAGlD,SAAS,CAACC,KAAK,CAAC,CAACgD,KAAK,CAAC,CAAC;EAClC,IAAI0G,IAAI,GAAG,GAAG,GAAGD,MAAM;EACvB,IAAIE,GAAG,GAAG,CAAC5J,SAAS,CAACC,KAAK,CAAC,CAAC;EAC5B,KAAKiD,GAAG,CAACL,CAAC,GAAG,CAACK,GAAG,CAACL,CAAC,IAAI8G,IAAI,GAAGF,OAAO,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,EAAEA,OAAO,GAAG;IACpEvG,GAAG,CAACL,CAAC,GAAG,CAACK,GAAG,CAACL,CAAC,GAAG8G,IAAI,IAAI,GAAG;IAC5BC,GAAG,CAACJ,IAAI,CAACxJ,SAAS,CAACkD,GAAG,CAAC,CAAC;EAC1B;EACA,OAAO0G,GAAG;AACZ;AACA,SAASjD,cAAcA,CAAC1G,KAAK,EAAEwJ,OAAO,EAAE;EACtCA,OAAO,GAAGA,OAAO,IAAI,CAAC;EACtB,IAAI9G,GAAG,GAAG3C,SAAS,CAACC,KAAK,CAAC,CAACyC,KAAK,CAAC,CAAC;EAClC,IAAIG,CAAC,GAAGF,GAAG,CAACE,CAAC;IACXC,CAAC,GAAGH,GAAG,CAACG,CAAC;IACTC,CAAC,GAAGJ,GAAG,CAACI,CAAC;EACX,IAAI6G,GAAG,GAAG,EAAE;EACZ,IAAIC,YAAY,GAAG,CAAC,GAAGJ,OAAO;EAC9B,OAAOA,OAAO,EAAE,EAAE;IAChBG,GAAG,CAACJ,IAAI,CAACxJ,SAAS,CAAC;MACjB6C,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA;IACL,CAAC,CAAC,CAAC;IACHA,CAAC,GAAG,CAACA,CAAC,GAAG8G,YAAY,IAAI,CAAC;EAC5B;EACA,OAAOD,GAAG;AACZ;;AAEA;AACA;;AAEA5J,SAAS,CAAC8J,GAAG,GAAG,UAAUjB,MAAM,EAAEC,MAAM,EAAEE,MAAM,EAAE;EAChDA,MAAM,GAAGA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,IAAI,EAAE;EACxC,IAAIe,IAAI,GAAG/J,SAAS,CAAC6I,MAAM,CAAC,CAAC/G,KAAK,CAAC,CAAC;EACpC,IAAIkI,IAAI,GAAGhK,SAAS,CAAC8I,MAAM,CAAC,CAAChH,KAAK,CAAC,CAAC;EACpC,IAAImG,CAAC,GAAGe,MAAM,GAAG,GAAG;EACpB,IAAIiB,IAAI,GAAG;IACT1J,CAAC,EAAE,CAACyJ,IAAI,CAACzJ,CAAC,GAAGwJ,IAAI,CAACxJ,CAAC,IAAI0H,CAAC,GAAG8B,IAAI,CAACxJ,CAAC;IACjCE,CAAC,EAAE,CAACuJ,IAAI,CAACvJ,CAAC,GAAGsJ,IAAI,CAACtJ,CAAC,IAAIwH,CAAC,GAAG8B,IAAI,CAACtJ,CAAC;IACjCE,CAAC,EAAE,CAACqJ,IAAI,CAACrJ,CAAC,GAAGoJ,IAAI,CAACpJ,CAAC,IAAIsH,CAAC,GAAG8B,IAAI,CAACpJ,CAAC;IACjCE,CAAC,EAAE,CAACmJ,IAAI,CAACnJ,CAAC,GAAGkJ,IAAI,CAAClJ,CAAC,IAAIoH,CAAC,GAAG8B,IAAI,CAAClJ;EAClC,CAAC;EACD,OAAOb,SAAS,CAACiK,IAAI,CAAC;AACxB,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACAjK,SAAS,CAACkK,WAAW,GAAG,UAAUrB,MAAM,EAAEC,MAAM,EAAE;EAChD,IAAIqB,EAAE,GAAGnK,SAAS,CAAC6I,MAAM,CAAC;EAC1B,IAAIuB,EAAE,GAAGpK,SAAS,CAAC8I,MAAM,CAAC;EAC1B,OAAO,CAAC/H,IAAI,CAAC+G,GAAG,CAACqC,EAAE,CAACpI,YAAY,CAAC,CAAC,EAAEqI,EAAE,CAACrI,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,KAAKhB,IAAI,CAAC8G,GAAG,CAACsC,EAAE,CAACpI,YAAY,CAAC,CAAC,EAAEqI,EAAE,CAACrI,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC1H,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA/B,SAAS,CAACqK,UAAU,GAAG,UAAUxB,MAAM,EAAEC,MAAM,EAAEwB,KAAK,EAAE;EACtD,IAAIJ,WAAW,GAAGlK,SAAS,CAACkK,WAAW,CAACrB,MAAM,EAAEC,MAAM,CAAC;EACvD,IAAIyB,UAAU,EAAEC,GAAG;EACnBA,GAAG,GAAG,KAAK;EACXD,UAAU,GAAGE,kBAAkB,CAACH,KAAK,CAAC;EACtC,QAAQC,UAAU,CAACG,KAAK,GAAGH,UAAU,CAACI,IAAI;IACxC,KAAK,SAAS;IACd,KAAK,UAAU;MACbH,GAAG,GAAGN,WAAW,IAAI,GAAG;MACxB;IACF,KAAK,SAAS;MACZM,GAAG,GAAGN,WAAW,IAAI,CAAC;MACtB;IACF,KAAK,UAAU;MACbM,GAAG,GAAGN,WAAW,IAAI,CAAC;MACtB;EACJ;EACA,OAAOM,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxK,SAAS,CAAC4K,YAAY,GAAG,UAAUC,SAAS,EAAEC,SAAS,EAAE7F,IAAI,EAAE;EAC7D,IAAI8F,SAAS,GAAG,IAAI;EACpB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAId,WAAW;EACf,IAAIe,qBAAqB,EAAEP,KAAK,EAAEC,IAAI;EACtC1F,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjBgG,qBAAqB,GAAGhG,IAAI,CAACgG,qBAAqB;EAClDP,KAAK,GAAGzF,IAAI,CAACyF,KAAK;EAClBC,IAAI,GAAG1F,IAAI,CAAC0F,IAAI;EAChB,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,SAAS,CAACI,MAAM,EAAE/D,CAAC,EAAE,EAAE;IACzC+C,WAAW,GAAGlK,SAAS,CAACkK,WAAW,CAACW,SAAS,EAAEC,SAAS,CAAC3D,CAAC,CAAC,CAAC;IAC5D,IAAI+C,WAAW,GAAGc,SAAS,EAAE;MAC3BA,SAAS,GAAGd,WAAW;MACvBa,SAAS,GAAG/K,SAAS,CAAC8K,SAAS,CAAC3D,CAAC,CAAC,CAAC;IACrC;EACF;EACA,IAAInH,SAAS,CAACqK,UAAU,CAACQ,SAAS,EAAEE,SAAS,EAAE;IAC7CL,KAAK,EAAEA,KAAK;IACZC,IAAI,EAAEA;EACR,CAAC,CAAC,IAAI,CAACM,qBAAqB,EAAE;IAC5B,OAAOF,SAAS;EAClB,CAAC,MAAM;IACL9F,IAAI,CAACgG,qBAAqB,GAAG,KAAK;IAClC,OAAOjL,SAAS,CAAC4K,YAAY,CAACC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE5F,IAAI,CAAC;EAClE;AACF,CAAC;;AAED;AACA;AACA;AACA,IAAIkG,KAAK,GAAGnL,SAAS,CAACmL,KAAK,GAAG;EAC5BC,SAAS,EAAE,QAAQ;EACnBC,YAAY,EAAE,QAAQ;EACtBC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE,QAAQ;EACpBC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,KAAK;EACZC,cAAc,EAAE,QAAQ;EACxBC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE,QAAQ;EACpBC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,WAAW,EAAE,QAAQ;EACrBC,SAAS,EAAE,QAAQ;EACnBC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,cAAc,EAAE,QAAQ;EACxBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,aAAa,EAAE,QAAQ;EACvBC,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,QAAQ;EACnBC,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,QAAQ;EACnBC,WAAW,EAAE,QAAQ;EACrBC,cAAc,EAAE,QAAQ;EACxBC,UAAU,EAAE,QAAQ;EACpBC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE,QAAQ;EACjBC,UAAU,EAAE,QAAQ;EACpBC,YAAY,EAAE,QAAQ;EACtBC,aAAa,EAAE,QAAQ;EACvBC,aAAa,EAAE,QAAQ;EACvBC,aAAa,EAAE,QAAQ;EACvBC,aAAa,EAAE,QAAQ;EACvBC,UAAU,EAAE,QAAQ;EACpBC,QAAQ,EAAE,QAAQ;EAClBC,WAAW,EAAE,QAAQ;EACrBC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,QAAQ;EACjBC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,QAAQ;EACnBC,WAAW,EAAE,QAAQ;EACrBC,WAAW,EAAE,QAAQ;EACrBC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,QAAQ;EACnBC,UAAU,EAAE,QAAQ;EACpBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE,QAAQ;EACrBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,QAAQ;EACjBC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,QAAQ;EAClBC,aAAa,EAAE,QAAQ;EACvBC,SAAS,EAAE,QAAQ;EACnBC,YAAY,EAAE,QAAQ;EACtBC,SAAS,EAAE,QAAQ;EACnBC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,QAAQ;EACnBC,oBAAoB,EAAE,QAAQ;EAC9BC,SAAS,EAAE,QAAQ;EACnBC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,QAAQ;EACnBC,SAAS,EAAE,QAAQ;EACnBC,WAAW,EAAE,QAAQ;EACrBC,aAAa,EAAE,QAAQ;EACvBC,YAAY,EAAE,QAAQ;EACtBC,cAAc,EAAE,KAAK;EACrBC,cAAc,EAAE,KAAK;EACrBC,cAAc,EAAE,QAAQ;EACxBC,WAAW,EAAE,QAAQ;EACrBC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,KAAK;EACdC,MAAM,EAAE,QAAQ;EAChBC,gBAAgB,EAAE,QAAQ;EAC1BC,UAAU,EAAE,QAAQ;EACpBC,YAAY,EAAE,QAAQ;EACtBC,YAAY,EAAE,QAAQ;EACtBC,cAAc,EAAE,QAAQ;EACxBC,eAAe,EAAE,QAAQ;EACzBC,iBAAiB,EAAE,QAAQ;EAC3BC,eAAe,EAAE,QAAQ;EACzBC,eAAe,EAAE,QAAQ;EACzBC,YAAY,EAAE,QAAQ;EACtBC,SAAS,EAAE,QAAQ;EACnBC,SAAS,EAAE,QAAQ;EACnBC,QAAQ,EAAE,QAAQ;EAClBC,WAAW,EAAE,QAAQ;EACrBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,aAAa,EAAE,QAAQ;EACvBC,SAAS,EAAE,QAAQ;EACnBC,aAAa,EAAE,QAAQ;EACvBC,aAAa,EAAE,QAAQ;EACvBC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,QAAQ;EACnBC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,UAAU,EAAE,QAAQ;EACpBC,MAAM,EAAE,QAAQ;EAChBC,aAAa,EAAE,QAAQ;EACvBC,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE,QAAQ;EACnBC,SAAS,EAAE,QAAQ;EACnBC,WAAW,EAAE,QAAQ;EACrBC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,QAAQ;EACpBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,SAAS,EAAE,QAAQ;EACnBC,SAAS,EAAE,QAAQ;EACnBC,SAAS,EAAE,QAAQ;EACnBC,IAAI,EAAE,QAAQ;EACdC,WAAW,EAAE,QAAQ;EACrBC,SAAS,EAAE,QAAQ;EACnBC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,KAAK;EACZC,UAAU,EAAE,QAAQ;EACpBC,MAAM,EAAE,KAAK;EACbC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,IAAIrQ,QAAQ,GAAGnE,SAAS,CAACmE,QAAQ,GAAGsQ,IAAI,CAACtJ,KAAK,CAAC;;AAE/C;AACA;;AAEA;AACA,SAASsJ,IAAIA,CAACC,CAAC,EAAE;EACf,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,KAAK,IAAIxN,CAAC,IAAIuN,CAAC,EAAE;IACf,IAAIA,CAAC,CAACtN,cAAc,CAACD,CAAC,CAAC,EAAE;MACvBwN,OAAO,CAACD,CAAC,CAACvN,CAAC,CAAC,CAAC,GAAGA,CAAC;IACnB;EACF;EACA,OAAOwN,OAAO;AAChB;;AAEA;AACA,SAASlS,UAAUA,CAAC5B,CAAC,EAAE;EACrBA,CAAC,GAAG+T,UAAU,CAAC/T,CAAC,CAAC;EACjB,IAAIuI,KAAK,CAACvI,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;IAC9BA,CAAC,GAAG,CAAC;EACP;EACA,OAAOA,CAAC;AACV;;AAEA;AACA,SAASmD,OAAOA,CAAC6Q,CAAC,EAAE/M,GAAG,EAAE;EACvB,IAAIgN,cAAc,CAACD,CAAC,CAAC,EAAEA,CAAC,GAAG,MAAM;EACjC,IAAIE,cAAc,GAAGC,YAAY,CAACH,CAAC,CAAC;EACpCA,CAAC,GAAG9T,IAAI,CAAC8G,GAAG,CAACC,GAAG,EAAE/G,IAAI,CAAC+G,GAAG,CAAC,CAAC,EAAE8M,UAAU,CAACC,CAAC,CAAC,CAAC,CAAC;;EAE7C;EACA,IAAIE,cAAc,EAAE;IAClBF,CAAC,GAAGI,QAAQ,CAACJ,CAAC,GAAG/M,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;EACjC;;EAEA;EACA,IAAI/G,IAAI,CAACmU,GAAG,CAACL,CAAC,GAAG/M,GAAG,CAAC,GAAG,QAAQ,EAAE;IAChC,OAAO,CAAC;EACV;;EAEA;EACA,OAAO+M,CAAC,GAAG/M,GAAG,GAAG8M,UAAU,CAAC9M,GAAG,CAAC;AAClC;;AAEA;AACA,SAASmB,OAAOA,CAACkM,GAAG,EAAE;EACpB,OAAOpU,IAAI,CAAC8G,GAAG,CAAC,CAAC,EAAE9G,IAAI,CAAC+G,GAAG,CAAC,CAAC,EAAEqN,GAAG,CAAC,CAAC;AACtC;;AAEA;AACA,SAASC,eAAeA,CAACD,GAAG,EAAE;EAC5B,OAAOF,QAAQ,CAACE,GAAG,EAAE,EAAE,CAAC;AAC1B;;AAEA;AACA;AACA,SAASL,cAAcA,CAACD,CAAC,EAAE;EACzB,OAAO,OAAOA,CAAC,IAAI,QAAQ,IAAIA,CAAC,CAACQ,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAIT,UAAU,CAACC,CAAC,CAAC,KAAK,CAAC;AAC5E;;AAEA;AACA,SAASG,YAAYA,CAACH,CAAC,EAAE;EACvB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACQ,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACtD;;AAEA;AACA,SAAS7M,IAAIA,CAAC8M,CAAC,EAAE;EACf,OAAOA,CAAC,CAACpK,MAAM,IAAI,CAAC,GAAG,GAAG,GAAGoK,CAAC,GAAG,EAAE,GAAGA,CAAC;AACzC;;AAEA;AACA,SAASjO,mBAAmBA,CAACwN,CAAC,EAAE;EAC9B,IAAIA,CAAC,IAAI,CAAC,EAAE;IACVA,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG;EACnB;EACA,OAAOA,CAAC;AACV;;AAEA;AACA,SAASlM,mBAAmBA,CAACZ,CAAC,EAAE;EAC9B,OAAOhH,IAAI,CAACC,KAAK,CAAC4T,UAAU,CAAC7M,CAAC,CAAC,GAAG,GAAG,CAAC,CAACtD,QAAQ,CAAC,EAAE,CAAC;AACrD;AACA;AACA,SAAS8Q,mBAAmBA,CAAC1S,CAAC,EAAE;EAC9B,OAAOuS,eAAe,CAACvS,CAAC,CAAC,GAAG,GAAG;AACjC;AACA,IAAI2S,QAAQ,GAAG,YAAY;EACzB;EACA,IAAIC,WAAW,GAAG,eAAe;;EAEjC;EACA,IAAIC,UAAU,GAAG,sBAAsB;;EAEvC;EACA,IAAIC,QAAQ,GAAG,KAAK,GAAGD,UAAU,GAAG,OAAO,GAAGD,WAAW,GAAG,GAAG;;EAE/D;EACA;EACA;EACA,IAAIG,iBAAiB,GAAG,aAAa,GAAGD,QAAQ,GAAG,YAAY,GAAGA,QAAQ,GAAG,YAAY,GAAGA,QAAQ,GAAG,WAAW;EAClH,IAAIE,iBAAiB,GAAG,aAAa,GAAGF,QAAQ,GAAG,YAAY,GAAGA,QAAQ,GAAG,YAAY,GAAGA,QAAQ,GAAG,YAAY,GAAGA,QAAQ,GAAG,WAAW;EAC5I,OAAO;IACLA,QAAQ,EAAE,IAAIG,MAAM,CAACH,QAAQ,CAAC;IAC9BxV,GAAG,EAAE,IAAI2V,MAAM,CAAC,KAAK,GAAGF,iBAAiB,CAAC;IAC1C3L,IAAI,EAAE,IAAI6L,MAAM,CAAC,MAAM,GAAGD,iBAAiB,CAAC;IAC5C3S,GAAG,EAAE,IAAI4S,MAAM,CAAC,KAAK,GAAGF,iBAAiB,CAAC;IAC1CG,IAAI,EAAE,IAAID,MAAM,CAAC,MAAM,GAAGD,iBAAiB,CAAC;IAC5ClT,GAAG,EAAE,IAAImT,MAAM,CAAC,KAAK,GAAGF,iBAAiB,CAAC;IAC1CI,IAAI,EAAE,IAAIF,MAAM,CAAC,MAAM,GAAGD,iBAAiB,CAAC;IAC5CI,IAAI,EAAE,sDAAsD;IAC5DC,IAAI,EAAE,sDAAsD;IAC5DC,IAAI,EAAE,sEAAsE;IAC5EC,IAAI,EAAE;EACR,CAAC;AACH,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA,SAAS7O,cAAcA,CAACtH,KAAK,EAAE;EAC7B,OAAO,CAAC,CAACuV,QAAQ,CAACG,QAAQ,CAACU,IAAI,CAACpW,KAAK,CAAC;AACxC;;AAEA;AACA;AACA;AACA,SAASqH,mBAAmBA,CAACrH,KAAK,EAAE;EAClCA,KAAK,GAAGA,KAAK,CAACqW,OAAO,CAACxW,QAAQ,EAAE,EAAE,CAAC,CAACwW,OAAO,CAACvW,SAAS,EAAE,EAAE,CAAC,CAACwW,WAAW,CAAC,CAAC;EACxE,IAAIC,KAAK,GAAG,KAAK;EACjB,IAAIrL,KAAK,CAAClL,KAAK,CAAC,EAAE;IAChBA,KAAK,GAAGkL,KAAK,CAAClL,KAAK,CAAC;IACpBuW,KAAK,GAAG,IAAI;EACd,CAAC,MAAM,IAAIvW,KAAK,IAAI,aAAa,EAAE;IACjC,OAAO;MACLM,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJK,MAAM,EAAE;IACV,CAAC;EACH;;EAEA;EACA;EACA;EACA;EACA,IAAIuV,KAAK;EACT,IAAIA,KAAK,GAAGjB,QAAQ,CAACrV,GAAG,CAACkW,IAAI,CAACpW,KAAK,CAAC,EAAE;IACpC,OAAO;MACLM,CAAC,EAAEkW,KAAK,CAAC,CAAC,CAAC;MACXhW,CAAC,EAAEgW,KAAK,CAAC,CAAC,CAAC;MACX9V,CAAC,EAAE8V,KAAK,CAAC,CAAC;IACZ,CAAC;EACH;EACA,IAAIA,KAAK,GAAGjB,QAAQ,CAACvL,IAAI,CAACoM,IAAI,CAACpW,KAAK,CAAC,EAAE;IACrC,OAAO;MACLM,CAAC,EAAEkW,KAAK,CAAC,CAAC,CAAC;MACXhW,CAAC,EAAEgW,KAAK,CAAC,CAAC,CAAC;MACX9V,CAAC,EAAE8V,KAAK,CAAC,CAAC,CAAC;MACX5V,CAAC,EAAE4V,KAAK,CAAC,CAAC;IACZ,CAAC;EACH;EACA,IAAIA,KAAK,GAAGjB,QAAQ,CAACtS,GAAG,CAACmT,IAAI,CAACpW,KAAK,CAAC,EAAE;IACpC,OAAO;MACL4C,CAAC,EAAE4T,KAAK,CAAC,CAAC,CAAC;MACX3T,CAAC,EAAE2T,KAAK,CAAC,CAAC,CAAC;MACXrT,CAAC,EAAEqT,KAAK,CAAC,CAAC;IACZ,CAAC;EACH;EACA,IAAIA,KAAK,GAAGjB,QAAQ,CAACO,IAAI,CAACM,IAAI,CAACpW,KAAK,CAAC,EAAE;IACrC,OAAO;MACL4C,CAAC,EAAE4T,KAAK,CAAC,CAAC,CAAC;MACX3T,CAAC,EAAE2T,KAAK,CAAC,CAAC,CAAC;MACXrT,CAAC,EAAEqT,KAAK,CAAC,CAAC,CAAC;MACX5V,CAAC,EAAE4V,KAAK,CAAC,CAAC;IACZ,CAAC;EACH;EACA,IAAIA,KAAK,GAAGjB,QAAQ,CAAC7S,GAAG,CAAC0T,IAAI,CAACpW,KAAK,CAAC,EAAE;IACpC,OAAO;MACL4C,CAAC,EAAE4T,KAAK,CAAC,CAAC,CAAC;MACX3T,CAAC,EAAE2T,KAAK,CAAC,CAAC,CAAC;MACX1T,CAAC,EAAE0T,KAAK,CAAC,CAAC;IACZ,CAAC;EACH;EACA,IAAIA,KAAK,GAAGjB,QAAQ,CAACQ,IAAI,CAACK,IAAI,CAACpW,KAAK,CAAC,EAAE;IACrC,OAAO;MACL4C,CAAC,EAAE4T,KAAK,CAAC,CAAC,CAAC;MACX3T,CAAC,EAAE2T,KAAK,CAAC,CAAC,CAAC;MACX1T,CAAC,EAAE0T,KAAK,CAAC,CAAC,CAAC;MACX5V,CAAC,EAAE4V,KAAK,CAAC,CAAC;IACZ,CAAC;EACH;EACA,IAAIA,KAAK,GAAGjB,QAAQ,CAACY,IAAI,CAACC,IAAI,CAACpW,KAAK,CAAC,EAAE;IACrC,OAAO;MACLM,CAAC,EAAE6U,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5BhW,CAAC,EAAE2U,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5B9V,CAAC,EAAEyU,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5B5V,CAAC,EAAE0U,mBAAmB,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;MAChCvV,MAAM,EAAEsV,KAAK,GAAG,MAAM,GAAG;IAC3B,CAAC;EACH;EACA,IAAIC,KAAK,GAAGjB,QAAQ,CAACU,IAAI,CAACG,IAAI,CAACpW,KAAK,CAAC,EAAE;IACrC,OAAO;MACLM,CAAC,EAAE6U,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5BhW,CAAC,EAAE2U,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5B9V,CAAC,EAAEyU,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5BvV,MAAM,EAAEsV,KAAK,GAAG,MAAM,GAAG;IAC3B,CAAC;EACH;EACA,IAAIC,KAAK,GAAGjB,QAAQ,CAACW,IAAI,CAACE,IAAI,CAACpW,KAAK,CAAC,EAAE;IACrC,OAAO;MACLM,CAAC,EAAE6U,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5ChW,CAAC,EAAE2U,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5C9V,CAAC,EAAEyU,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5C5V,CAAC,EAAE0U,mBAAmB,CAACkB,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAChDvV,MAAM,EAAEsV,KAAK,GAAG,MAAM,GAAG;IAC3B,CAAC;EACH;EACA,IAAIC,KAAK,GAAGjB,QAAQ,CAACS,IAAI,CAACI,IAAI,CAACpW,KAAK,CAAC,EAAE;IACrC,OAAO;MACLM,CAAC,EAAE6U,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5ChW,CAAC,EAAE2U,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5C9V,CAAC,EAAEyU,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5CvV,MAAM,EAAEsV,KAAK,GAAG,MAAM,GAAG;IAC3B,CAAC;EACH;EACA,OAAO,KAAK;AACd;AACA,SAAS/L,kBAAkBA,CAACiM,KAAK,EAAE;EACjC;EACA;EACA,IAAIhM,KAAK,EAAEC,IAAI;EACf+L,KAAK,GAAGA,KAAK,IAAI;IACfhM,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC;EACDD,KAAK,GAAG,CAACgM,KAAK,CAAChM,KAAK,IAAI,IAAI,EAAEiM,WAAW,CAAC,CAAC;EAC3ChM,IAAI,GAAG,CAAC+L,KAAK,CAAC/L,IAAI,IAAI,OAAO,EAAE4L,WAAW,CAAC,CAAC;EAC5C,IAAI7L,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,EAAE;IACrCA,KAAK,GAAG,IAAI;EACd;EACA,IAAIC,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO,EAAE;IACxCA,IAAI,GAAG,OAAO;EAChB;EACA,OAAO;IACLD,KAAK,EAAEA,KAAK;IACZC,IAAI,EAAEA;EACR,CAAC;AACH;AAEA,SAAS3K,SAAS,IAAI4W,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}