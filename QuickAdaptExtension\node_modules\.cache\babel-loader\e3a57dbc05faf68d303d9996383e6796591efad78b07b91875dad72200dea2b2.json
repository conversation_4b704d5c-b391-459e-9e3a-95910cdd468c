{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { styled } from '@mui/material/styles';\nimport PropTypes from 'prop-types';\nimport TablePagination, { tablePaginationClasses } from '@mui/material/TablePagination';\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { gridPaginationModelSelector, gridPaginationRowCountSelector, gridPageCountSelector } from \"../hooks/features/pagination/gridPaginationSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridPaginationRoot = styled(TablePagination)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    maxHeight: 'calc(100% + 1px)',\n    // border width\n    [`& .${tablePaginationClasses.selectLabel}`]: {\n      display: 'none',\n      [theme.breakpoints.up('sm')]: {\n        display: 'block'\n      }\n    },\n    [`& .${tablePaginationClasses.input}`]: {\n      display: 'none',\n      [theme.breakpoints.up('sm')]: {\n        display: 'inline-flex'\n      }\n    }\n  };\n});\nconst wrapLabelDisplayedRows = (labelDisplayedRows, estimated) => {\n  return _ref2 => {\n    let {\n      from,\n      to,\n      count,\n      page\n    } = _ref2;\n    return labelDisplayedRows({\n      from,\n      to,\n      count,\n      page,\n      estimated\n    });\n  };\n};\nconst defaultLabelDisplayedRows = _ref3 => {\n  let {\n    from,\n    to,\n    count,\n    estimated\n  } = _ref3;\n  if (!estimated) {\n    return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n  }\n  return `${from}–${to} of ${count !== -1 ? count : `more than ${estimated > to ? estimated : to}`}`;\n};\n\n// A mutable version of a readonly array.\n\nconst GridPagination = /*#__PURE__*/React.forwardRef(function GridPagination(props, ref) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const rowCount = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  const pageCount = useGridSelector(apiRef, gridPageCountSelector);\n  const {\n    paginationMode,\n    loading,\n    estimatedRowCount\n  } = rootProps;\n  const computedProps = React.useMemo(() => {\n    if (rowCount === -1 && paginationMode === 'server' && loading) {\n      return {\n        backIconButtonProps: {\n          disabled: true\n        },\n        nextIconButtonProps: {\n          disabled: true\n        }\n      };\n    }\n    return {};\n  }, [loading, paginationMode, rowCount]);\n  const lastPage = React.useMemo(() => Math.max(0, pageCount - 1), [pageCount]);\n  const computedPage = React.useMemo(() => {\n    if (rowCount === -1) {\n      return paginationModel.page;\n    }\n    return paginationModel.page <= lastPage ? paginationModel.page : lastPage;\n  }, [lastPage, paginationModel.page, rowCount]);\n  const handlePageSizeChange = React.useCallback(event => {\n    const pageSize = Number(event.target.value);\n    apiRef.current.setPageSize(pageSize);\n  }, [apiRef]);\n  const handlePageChange = React.useCallback((_, page) => {\n    apiRef.current.setPage(page);\n  }, [apiRef]);\n  const isPageSizeIncludedInPageSizeOptions = pageSize => {\n    for (let i = 0; i < rootProps.pageSizeOptions.length; i += 1) {\n      const option = rootProps.pageSizeOptions[i];\n      if (typeof option === 'number') {\n        if (option === pageSize) {\n          return true;\n        }\n      } else if (option.value === pageSize) {\n        return true;\n      }\n    }\n    return false;\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const warnedOnceMissingInPageSizeOptions = React.useRef(false);\n    const pageSize = rootProps.paginationModel?.pageSize ?? paginationModel.pageSize;\n    if (!warnedOnceMissingInPageSizeOptions.current && !rootProps.autoPageSize && !isPageSizeIncludedInPageSizeOptions(pageSize)) {\n      console.warn([`MUI X: The page size \\`${paginationModel.pageSize}\\` is not present in the \\`pageSizeOptions\\`.`, `Add it to show the pagination select.`].join('\\n'));\n      warnedOnceMissingInPageSizeOptions.current = true;\n    }\n  }\n  const pageSizeOptions = isPageSizeIncludedInPageSizeOptions(paginationModel.pageSize) ? rootProps.pageSizeOptions : [];\n  const locales = apiRef.current.getLocaleText('MuiTablePagination');\n  const wrappedLabelDisplayedRows = wrapLabelDisplayedRows(locales.labelDisplayedRows || defaultLabelDisplayedRows, estimatedRowCount);\n  return /*#__PURE__*/_jsx(GridPaginationRoot, _extends({\n    ref: ref,\n    component: \"div\",\n    count: rowCount,\n    page: computedPage\n    // TODO: Remove the cast once the type is fixed in Material UI and that the min Material UI version\n    // for x-data-grid is past the fix.\n    // Note that Material UI will not mutate the array, so this is safe.\n    ,\n\n    rowsPerPageOptions: pageSizeOptions,\n    rowsPerPage: paginationModel.pageSize,\n    onPageChange: handlePageChange,\n    onRowsPerPageChange: handlePageSizeChange\n  }, computedProps, locales, {\n    labelDisplayedRows: wrappedLabelDisplayedRows\n  }, props));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridPagination.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  component: PropTypes.elementType\n} : void 0;\nexport { GridPagination };", "map": {"version": 3, "names": ["_extends", "React", "styled", "PropTypes", "TablePagination", "tablePaginationClasses", "useGridSelector", "useGridApiContext", "useGridRootProps", "gridPaginationModelSelector", "gridPaginationRowCountSelector", "gridPageCountSelector", "jsx", "_jsx", "GridPaginationRoot", "_ref", "theme", "maxHeight", "selectLabel", "display", "breakpoints", "up", "input", "wrapLabelDisplayedRows", "labelDisplayedRows", "estimated", "_ref2", "from", "to", "count", "page", "defaultLabelDisplayedRows", "_ref3", "GridPagination", "forwardRef", "props", "ref", "apiRef", "rootProps", "paginationModel", "rowCount", "pageCount", "paginationMode", "loading", "estimatedRowCount", "computedProps", "useMemo", "backIconButtonProps", "disabled", "nextIconButtonProps", "lastPage", "Math", "max", "computedPage", "handlePageSizeChange", "useCallback", "event", "pageSize", "Number", "target", "value", "current", "setPageSize", "handlePageChange", "_", "setPage", "isPageSizeIncludedInPageSizeOptions", "i", "pageSizeOptions", "length", "option", "process", "env", "NODE_ENV", "warnedOnceMissingInPageSizeOptions", "useRef", "autoPageSize", "console", "warn", "join", "locales", "getLocaleText", "wrappedLabelDisplayedRows", "component", "rowsPerPageOptions", "rowsPerPage", "onPageChange", "onRowsPerPageChange", "propTypes", "elementType"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/GridPagination.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { styled } from '@mui/material/styles';\nimport PropTypes from 'prop-types';\nimport TablePagination, { tablePaginationClasses } from '@mui/material/TablePagination';\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { gridPaginationModelSelector, gridPaginationRowCountSelector, gridPageCountSelector } from \"../hooks/features/pagination/gridPaginationSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridPaginationRoot = styled(TablePagination)(({\n  theme\n}) => ({\n  maxHeight: 'calc(100% + 1px)',\n  // border width\n  [`& .${tablePaginationClasses.selectLabel}`]: {\n    display: 'none',\n    [theme.breakpoints.up('sm')]: {\n      display: 'block'\n    }\n  },\n  [`& .${tablePaginationClasses.input}`]: {\n    display: 'none',\n    [theme.breakpoints.up('sm')]: {\n      display: 'inline-flex'\n    }\n  }\n}));\nconst wrapLabelDisplayedRows = (labelDisplayedRows, estimated) => {\n  return ({\n    from,\n    to,\n    count,\n    page\n  }) => labelDisplayedRows({\n    from,\n    to,\n    count,\n    page,\n    estimated\n  });\n};\nconst defaultLabelDisplayedRows = ({\n  from,\n  to,\n  count,\n  estimated\n}) => {\n  if (!estimated) {\n    return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n  }\n  return `${from}–${to} of ${count !== -1 ? count : `more than ${estimated > to ? estimated : to}`}`;\n};\n\n// A mutable version of a readonly array.\n\nconst GridPagination = /*#__PURE__*/React.forwardRef(function GridPagination(props, ref) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const rowCount = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  const pageCount = useGridSelector(apiRef, gridPageCountSelector);\n  const {\n    paginationMode,\n    loading,\n    estimatedRowCount\n  } = rootProps;\n  const computedProps = React.useMemo(() => {\n    if (rowCount === -1 && paginationMode === 'server' && loading) {\n      return {\n        backIconButtonProps: {\n          disabled: true\n        },\n        nextIconButtonProps: {\n          disabled: true\n        }\n      };\n    }\n    return {};\n  }, [loading, paginationMode, rowCount]);\n  const lastPage = React.useMemo(() => Math.max(0, pageCount - 1), [pageCount]);\n  const computedPage = React.useMemo(() => {\n    if (rowCount === -1) {\n      return paginationModel.page;\n    }\n    return paginationModel.page <= lastPage ? paginationModel.page : lastPage;\n  }, [lastPage, paginationModel.page, rowCount]);\n  const handlePageSizeChange = React.useCallback(event => {\n    const pageSize = Number(event.target.value);\n    apiRef.current.setPageSize(pageSize);\n  }, [apiRef]);\n  const handlePageChange = React.useCallback((_, page) => {\n    apiRef.current.setPage(page);\n  }, [apiRef]);\n  const isPageSizeIncludedInPageSizeOptions = pageSize => {\n    for (let i = 0; i < rootProps.pageSizeOptions.length; i += 1) {\n      const option = rootProps.pageSizeOptions[i];\n      if (typeof option === 'number') {\n        if (option === pageSize) {\n          return true;\n        }\n      } else if (option.value === pageSize) {\n        return true;\n      }\n    }\n    return false;\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const warnedOnceMissingInPageSizeOptions = React.useRef(false);\n    const pageSize = rootProps.paginationModel?.pageSize ?? paginationModel.pageSize;\n    if (!warnedOnceMissingInPageSizeOptions.current && !rootProps.autoPageSize && !isPageSizeIncludedInPageSizeOptions(pageSize)) {\n      console.warn([`MUI X: The page size \\`${paginationModel.pageSize}\\` is not present in the \\`pageSizeOptions\\`.`, `Add it to show the pagination select.`].join('\\n'));\n      warnedOnceMissingInPageSizeOptions.current = true;\n    }\n  }\n  const pageSizeOptions = isPageSizeIncludedInPageSizeOptions(paginationModel.pageSize) ? rootProps.pageSizeOptions : [];\n  const locales = apiRef.current.getLocaleText('MuiTablePagination');\n  const wrappedLabelDisplayedRows = wrapLabelDisplayedRows(locales.labelDisplayedRows || defaultLabelDisplayedRows, estimatedRowCount);\n  return /*#__PURE__*/_jsx(GridPaginationRoot, _extends({\n    ref: ref,\n    component: \"div\",\n    count: rowCount,\n    page: computedPage\n    // TODO: Remove the cast once the type is fixed in Material UI and that the min Material UI version\n    // for x-data-grid is past the fix.\n    // Note that Material UI will not mutate the array, so this is safe.\n    ,\n    rowsPerPageOptions: pageSizeOptions,\n    rowsPerPage: paginationModel.pageSize,\n    onPageChange: handlePageChange,\n    onRowsPerPageChange: handlePageSizeChange\n  }, computedProps, locales, {\n    labelDisplayedRows: wrappedLabelDisplayedRows\n  }, props));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridPagination.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  component: PropTypes.elementType\n} : void 0;\nexport { GridPagination };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,eAAe,IAAIC,sBAAsB,QAAQ,+BAA+B;AACvF,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,2BAA2B,EAAEC,8BAA8B,EAAEC,qBAAqB,QAAQ,wDAAwD;AAC3J,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGZ,MAAM,CAACE,eAAe,CAAC,CAACW,IAAA;EAAA,IAAC;IAClDC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,SAAS,EAAE,kBAAkB;IAC7B;IACA,CAAC,MAAMZ,sBAAsB,CAACa,WAAW,EAAE,GAAG;MAC5CC,OAAO,EAAE,MAAM;MACf,CAACH,KAAK,CAACI,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC5BF,OAAO,EAAE;MACX;IACF,CAAC;IACD,CAAC,MAAMd,sBAAsB,CAACiB,KAAK,EAAE,GAAG;MACtCH,OAAO,EAAE,MAAM;MACf,CAACH,KAAK,CAACI,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC5BF,OAAO,EAAE;MACX;IACF;EACF,CAAC;AAAA,CAAC,CAAC;AACH,MAAMI,sBAAsB,GAAGA,CAACC,kBAAkB,EAAEC,SAAS,KAAK;EAChE,OAAOC,KAAA;IAAA,IAAC;MACNC,IAAI;MACJC,EAAE;MACFC,KAAK;MACLC;IACF,CAAC,GAAAJ,KAAA;IAAA,OAAKF,kBAAkB,CAAC;MACvBG,IAAI;MACJC,EAAE;MACFC,KAAK;MACLC,IAAI;MACJL;IACF,CAAC,CAAC;EAAA;AACJ,CAAC;AACD,MAAMM,yBAAyB,GAAGC,KAAA,IAK5B;EAAA,IAL6B;IACjCL,IAAI;IACJC,EAAE;IACFC,KAAK;IACLJ;EACF,CAAC,GAAAO,KAAA;EACC,IAAI,CAACP,SAAS,EAAE;IACd,OAAO,GAAGE,IAAI,IAAIC,EAAE,OAAOC,KAAK,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAG,aAAaD,EAAE,EAAE,EAAE;EACvE;EACA,OAAO,GAAGD,IAAI,IAAIC,EAAE,OAAOC,KAAK,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAG,aAAaJ,SAAS,GAAGG,EAAE,GAAGH,SAAS,GAAGG,EAAE,EAAE,EAAE;AACpG,CAAC;;AAED;;AAEA,MAAMK,cAAc,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,SAASD,cAAcA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACvF,MAAMC,MAAM,GAAG9B,iBAAiB,CAAC,CAAC;EAClC,MAAM+B,SAAS,GAAG9B,gBAAgB,CAAC,CAAC;EACpC,MAAM+B,eAAe,GAAGjC,eAAe,CAAC+B,MAAM,EAAE5B,2BAA2B,CAAC;EAC5E,MAAM+B,QAAQ,GAAGlC,eAAe,CAAC+B,MAAM,EAAE3B,8BAA8B,CAAC;EACxE,MAAM+B,SAAS,GAAGnC,eAAe,CAAC+B,MAAM,EAAE1B,qBAAqB,CAAC;EAChE,MAAM;IACJ+B,cAAc;IACdC,OAAO;IACPC;EACF,CAAC,GAAGN,SAAS;EACb,MAAMO,aAAa,GAAG5C,KAAK,CAAC6C,OAAO,CAAC,MAAM;IACxC,IAAIN,QAAQ,KAAK,CAAC,CAAC,IAAIE,cAAc,KAAK,QAAQ,IAAIC,OAAO,EAAE;MAC7D,OAAO;QACLI,mBAAmB,EAAE;UACnBC,QAAQ,EAAE;QACZ,CAAC;QACDC,mBAAmB,EAAE;UACnBD,QAAQ,EAAE;QACZ;MACF,CAAC;IACH;IACA,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACL,OAAO,EAAED,cAAc,EAAEF,QAAQ,CAAC,CAAC;EACvC,MAAMU,QAAQ,GAAGjD,KAAK,CAAC6C,OAAO,CAAC,MAAMK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEX,SAAS,GAAG,CAAC,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAC7E,MAAMY,YAAY,GAAGpD,KAAK,CAAC6C,OAAO,CAAC,MAAM;IACvC,IAAIN,QAAQ,KAAK,CAAC,CAAC,EAAE;MACnB,OAAOD,eAAe,CAACT,IAAI;IAC7B;IACA,OAAOS,eAAe,CAACT,IAAI,IAAIoB,QAAQ,GAAGX,eAAe,CAACT,IAAI,GAAGoB,QAAQ;EAC3E,CAAC,EAAE,CAACA,QAAQ,EAAEX,eAAe,CAACT,IAAI,EAAEU,QAAQ,CAAC,CAAC;EAC9C,MAAMc,oBAAoB,GAAGrD,KAAK,CAACsD,WAAW,CAACC,KAAK,IAAI;IACtD,MAAMC,QAAQ,GAAGC,MAAM,CAACF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC;IAC3CvB,MAAM,CAACwB,OAAO,CAACC,WAAW,CAACL,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EACZ,MAAM0B,gBAAgB,GAAG9D,KAAK,CAACsD,WAAW,CAAC,CAACS,CAAC,EAAElC,IAAI,KAAK;IACtDO,MAAM,CAACwB,OAAO,CAACI,OAAO,CAACnC,IAAI,CAAC;EAC9B,CAAC,EAAE,CAACO,MAAM,CAAC,CAAC;EACZ,MAAM6B,mCAAmC,GAAGT,QAAQ,IAAI;IACtD,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7B,SAAS,CAAC8B,eAAe,CAACC,MAAM,EAAEF,CAAC,IAAI,CAAC,EAAE;MAC5D,MAAMG,MAAM,GAAGhC,SAAS,CAAC8B,eAAe,CAACD,CAAC,CAAC;MAC3C,IAAI,OAAOG,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAIA,MAAM,KAAKb,QAAQ,EAAE;UACvB,OAAO,IAAI;QACb;MACF,CAAC,MAAM,IAAIa,MAAM,CAACV,KAAK,KAAKH,QAAQ,EAAE;QACpC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC;EACD,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAMC,kCAAkC,GAAGzE,KAAK,CAAC0E,MAAM,CAAC,KAAK,CAAC;IAC9D,MAAMlB,QAAQ,GAAGnB,SAAS,CAACC,eAAe,EAAEkB,QAAQ,IAAIlB,eAAe,CAACkB,QAAQ;IAChF,IAAI,CAACiB,kCAAkC,CAACb,OAAO,IAAI,CAACvB,SAAS,CAACsC,YAAY,IAAI,CAACV,mCAAmC,CAACT,QAAQ,CAAC,EAAE;MAC5HoB,OAAO,CAACC,IAAI,CAAC,CAAC,0BAA0BvC,eAAe,CAACkB,QAAQ,+CAA+C,EAAE,uCAAuC,CAAC,CAACsB,IAAI,CAAC,IAAI,CAAC,CAAC;MACrKL,kCAAkC,CAACb,OAAO,GAAG,IAAI;IACnD;EACF;EACA,MAAMO,eAAe,GAAGF,mCAAmC,CAAC3B,eAAe,CAACkB,QAAQ,CAAC,GAAGnB,SAAS,CAAC8B,eAAe,GAAG,EAAE;EACtH,MAAMY,OAAO,GAAG3C,MAAM,CAACwB,OAAO,CAACoB,aAAa,CAAC,oBAAoB,CAAC;EAClE,MAAMC,yBAAyB,GAAG3D,sBAAsB,CAACyD,OAAO,CAACxD,kBAAkB,IAAIO,yBAAyB,EAAEa,iBAAiB,CAAC;EACpI,OAAO,aAAa/B,IAAI,CAACC,kBAAkB,EAAEd,QAAQ,CAAC;IACpDoC,GAAG,EAAEA,GAAG;IACR+C,SAAS,EAAE,KAAK;IAChBtD,KAAK,EAAEW,QAAQ;IACfV,IAAI,EAAEuB;IACN;IACA;IACA;IAAA;;IAEA+B,kBAAkB,EAAEhB,eAAe;IACnCiB,WAAW,EAAE9C,eAAe,CAACkB,QAAQ;IACrC6B,YAAY,EAAEvB,gBAAgB;IAC9BwB,mBAAmB,EAAEjC;EACvB,CAAC,EAAET,aAAa,EAAEmC,OAAO,EAAE;IACzBxD,kBAAkB,EAAE0D;EACtB,CAAC,EAAE/C,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFoC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,cAAc,CAACuD,SAAS,GAAG;EACjE;EACA;EACA;EACA;EACAL,SAAS,EAAEhF,SAAS,CAACsF;AACvB,CAAC,GAAG,KAAK,CAAC;AACV,SAASxD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}