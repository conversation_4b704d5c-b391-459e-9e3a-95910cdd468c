{"ast": null, "code": "export const gridDimensionsSelector = state => state.dimensions;", "map": {"version": 3, "names": ["gridDimensionsSelector", "state", "dimensions"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/dimensions/gridDimensionsSelectors.js"], "sourcesContent": ["export const gridDimensionsSelector = state => state.dimensions;"], "mappings": "AAAA,OAAO,MAAMA,sBAAsB,GAAGC,KAAK,IAAIA,KAAK,CAACC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}