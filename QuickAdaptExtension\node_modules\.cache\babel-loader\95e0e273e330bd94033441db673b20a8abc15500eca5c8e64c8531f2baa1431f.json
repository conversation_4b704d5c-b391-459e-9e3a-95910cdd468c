{"ast": null, "code": "export class FinalizationRegistryBasedCleanupTracking {\n  constructor() {\n    this.registry = new FinalizationRegistry(unsubscribe => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n    });\n  }\n  register(object, unsubscribe, unregisterToken) {\n    this.registry.register(object, unsubscribe, unregisterToken);\n  }\n  unregister(unregisterToken) {\n    this.registry.unregister(unregisterToken);\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  reset() {}\n}", "map": {"version": 3, "names": ["FinalizationRegistryBasedCleanupTracking", "constructor", "registry", "FinalizationRegistry", "unsubscribe", "register", "object", "unregisterToken", "unregister", "reset"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking.js"], "sourcesContent": ["export class FinalizationRegistryBasedCleanupTracking {\n  constructor() {\n    this.registry = new FinalizationRegistry(unsubscribe => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n    });\n  }\n  register(object, unsubscribe, unregisterToken) {\n    this.registry.register(object, unsubscribe, unregisterToken);\n  }\n  unregister(unregisterToken) {\n    this.registry.unregister(unregisterToken);\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  reset() {}\n}"], "mappings": "AAAA,OAAO,MAAMA,wCAAwC,CAAC;EACpDC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,IAAIC,oBAAoB,CAACC,WAAW,IAAI;MACtD,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;QACrCA,WAAW,CAAC,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EACAC,QAAQA,CAACC,MAAM,EAAEF,WAAW,EAAEG,eAAe,EAAE;IAC7C,IAAI,CAACL,QAAQ,CAACG,QAAQ,CAACC,MAAM,EAAEF,WAAW,EAAEG,eAAe,CAAC;EAC9D;EACAC,UAAUA,CAACD,eAAe,EAAE;IAC1B,IAAI,CAACL,QAAQ,CAACM,UAAU,CAACD,eAAe,CAAC;EAC3C;;EAEA;EACAE,KAAKA,CAAA,EAAG,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}