{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.hover = undefined;\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nvar _react = require('react');\nvar _react2 = _interopRequireDefault(_react);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\nvar hover = exports.hover = function hover(Component) {\n  var Span = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'span';\n  return function (_React$Component) {\n    _inherits(Hover, _React$Component);\n    function Hover() {\n      var _ref;\n      var _temp, _this, _ret;\n      _classCallCheck(this, Hover);\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return _ret = (_temp = (_this = _possibleConstructorReturn(this, (_ref = Hover.__proto__ || Object.getPrototypeOf(Hover)).call.apply(_ref, [this].concat(args))), _this), _this.state = {\n        hover: false\n      }, _this.handleMouseOver = function () {\n        return _this.setState({\n          hover: true\n        });\n      }, _this.handleMouseOut = function () {\n        return _this.setState({\n          hover: false\n        });\n      }, _this.render = function () {\n        return _react2.default.createElement(Span, {\n          onMouseOver: _this.handleMouseOver,\n          onMouseOut: _this.handleMouseOut\n        }, _react2.default.createElement(Component, _extends({}, _this.props, _this.state)));\n      }, _temp), _possibleConstructorReturn(_this, _ret);\n    }\n    return Hover;\n  }(_react2.default.Component);\n};\nexports.default = hover;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "hover", "undefined", "_extends", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "_react", "require", "_react2", "_interopRequireDefault", "obj", "__esModule", "default", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_possibleConstructorReturn", "self", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "enumerable", "writable", "configurable", "setPrototypeOf", "__proto__", "Component", "Span", "_React$Component", "Hover", "_ref", "_temp", "_this", "_ret", "_len", "args", "Array", "_key", "getPrototypeOf", "apply", "concat", "state", "handleMouseOver", "setState", "handleMouseOut", "render", "createElement", "onMouseOver", "onMouseOut", "props"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/reactcss/lib/components/hover.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.hover = undefined;\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _react = require('react');\n\nvar _react2 = _interopRequireDefault(_react);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar hover = exports.hover = function hover(Component) {\n  var Span = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'span';\n\n  return function (_React$Component) {\n    _inherits(Hover, _React$Component);\n\n    function Hover() {\n      var _ref;\n\n      var _temp, _this, _ret;\n\n      _classCallCheck(this, Hover);\n\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      return _ret = (_temp = (_this = _possibleConstructorReturn(this, (_ref = Hover.__proto__ || Object.getPrototypeOf(Hover)).call.apply(_ref, [this].concat(args))), _this), _this.state = { hover: false }, _this.handleMouseOver = function () {\n        return _this.setState({ hover: true });\n      }, _this.handleMouseOut = function () {\n        return _this.setState({ hover: false });\n      }, _this.render = function () {\n        return _react2.default.createElement(\n          Span,\n          { onMouseOver: _this.handleMouseOver, onMouseOut: _this.handleMouseOut },\n          _react2.default.createElement(Component, _extends({}, _this.props, _this.state))\n        );\n      }, _temp), _possibleConstructorReturn(_this, _ret);\n    }\n\n    return Hover;\n  }(_react2.default.Component);\n};\n\nexports.default = hover;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,KAAK,GAAGC,SAAS;AAEzB,IAAIC,QAAQ,GAAGN,MAAM,CAACO,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIZ,MAAM,CAACc,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,IAAIS,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEb,IAAI,EAAE;EAAE,IAAI,CAACa,IAAI,EAAE;IAAE,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOd,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGa,IAAI;AAAE;AAE/O,SAASE,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIN,SAAS,CAAC,0DAA0D,GAAG,OAAOM,UAAU,CAAC;EAAE;EAAED,QAAQ,CAAClB,SAAS,GAAGd,MAAM,CAACkC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACnB,SAAS,EAAE;IAAEqB,WAAW,EAAE;MAAEhC,KAAK,EAAE6B,QAAQ;MAAEI,UAAU,EAAE,KAAK;MAAEC,QAAQ,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIL,UAAU,EAAEjC,MAAM,CAACuC,cAAc,GAAGvC,MAAM,CAACuC,cAAc,CAACP,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACQ,SAAS,GAAGP,UAAU;AAAE;AAE7e,IAAI7B,KAAK,GAAGF,OAAO,CAACE,KAAK,GAAG,SAASA,KAAKA,CAACqC,SAAS,EAAE;EACpD,IAAIC,IAAI,GAAGhC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKL,SAAS,GAAGK,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;EAErF,OAAO,UAAUiC,gBAAgB,EAAE;IACjCZ,SAAS,CAACa,KAAK,EAAED,gBAAgB,CAAC;IAElC,SAASC,KAAKA,CAAA,EAAG;MACf,IAAIC,IAAI;MAER,IAAIC,KAAK,EAAEC,KAAK,EAAEC,IAAI;MAEtBxB,eAAe,CAAC,IAAI,EAAEoB,KAAK,CAAC;MAE5B,KAAK,IAAIK,IAAI,GAAGvC,SAAS,CAACC,MAAM,EAAEuC,IAAI,GAAGC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;QACnFF,IAAI,CAACE,IAAI,CAAC,GAAG1C,SAAS,CAAC0C,IAAI,CAAC;MAC9B;MAEA,OAAOJ,IAAI,IAAIF,KAAK,IAAIC,KAAK,GAAGnB,0BAA0B,CAAC,IAAI,EAAE,CAACiB,IAAI,GAAGD,KAAK,CAACJ,SAAS,IAAIxC,MAAM,CAACqD,cAAc,CAACT,KAAK,CAAC,EAAE5B,IAAI,CAACsC,KAAK,CAACT,IAAI,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC,CAAC,EAAEH,KAAK,CAAC,EAAEA,KAAK,CAACS,KAAK,GAAG;QAAEpD,KAAK,EAAE;MAAM,CAAC,EAAE2C,KAAK,CAACU,eAAe,GAAG,YAAY;QAC5O,OAAOV,KAAK,CAACW,QAAQ,CAAC;UAAEtD,KAAK,EAAE;QAAK,CAAC,CAAC;MACxC,CAAC,EAAE2C,KAAK,CAACY,cAAc,GAAG,YAAY;QACpC,OAAOZ,KAAK,CAACW,QAAQ,CAAC;UAAEtD,KAAK,EAAE;QAAM,CAAC,CAAC;MACzC,CAAC,EAAE2C,KAAK,CAACa,MAAM,GAAG,YAAY;QAC5B,OAAOzC,OAAO,CAACI,OAAO,CAACsC,aAAa,CAClCnB,IAAI,EACJ;UAAEoB,WAAW,EAAEf,KAAK,CAACU,eAAe;UAAEM,UAAU,EAAEhB,KAAK,CAACY;QAAe,CAAC,EACxExC,OAAO,CAACI,OAAO,CAACsC,aAAa,CAACpB,SAAS,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAACiB,KAAK,EAAEjB,KAAK,CAACS,KAAK,CAAC,CACjF,CAAC;MACH,CAAC,EAAEV,KAAK,CAAC,EAAElB,0BAA0B,CAACmB,KAAK,EAAEC,IAAI,CAAC;IACpD;IAEA,OAAOJ,KAAK;EACd,CAAC,CAACzB,OAAO,CAACI,OAAO,CAACkB,SAAS,CAAC;AAC9B,CAAC;AAEDvC,OAAO,CAACqB,OAAO,GAAGnB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}