{"ast": null, "code": "var BI_RM = \"0123456789abcdefghijklmnopqrstuvwxyz\";\nexport function int2char(n) {\n  return BI_RM.charAt(n);\n}\n//#region BIT_OPERATIONS\n// (public) this & a\nexport function op_and(x, y) {\n  return x & y;\n}\n// (public) this | a\nexport function op_or(x, y) {\n  return x | y;\n}\n// (public) this ^ a\nexport function op_xor(x, y) {\n  return x ^ y;\n}\n// (public) this & ~a\nexport function op_andnot(x, y) {\n  return x & ~y;\n}\n// return index of lowest 1-bit in x, x < 2^31\nexport function lbit(x) {\n  if (x == 0) {\n    return -1;\n  }\n  var r = 0;\n  if ((x & 0xffff) == 0) {\n    x >>= 16;\n    r += 16;\n  }\n  if ((x & 0xff) == 0) {\n    x >>= 8;\n    r += 8;\n  }\n  if ((x & 0xf) == 0) {\n    x >>= 4;\n    r += 4;\n  }\n  if ((x & 3) == 0) {\n    x >>= 2;\n    r += 2;\n  }\n  if ((x & 1) == 0) {\n    ++r;\n  }\n  return r;\n}\n// return number of 1 bits in x\nexport function cbit(x) {\n  var r = 0;\n  while (x != 0) {\n    x &= x - 1;\n    ++r;\n  }\n  return r;\n}\n//#endregion BIT_OPERATIONS", "map": {"version": 3, "names": ["BI_RM", "int2char", "n", "char<PERSON>t", "op_and", "x", "y", "op_or", "op_xor", "op_andnot", "lbit", "r", "cbit"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/jsencrypt/lib/lib/jsbn/util.js"], "sourcesContent": ["var BI_RM = \"0123456789abcdefghijklmnopqrstuvwxyz\";\nexport function int2char(n) {\n    return BI_RM.charAt(n);\n}\n//#region BIT_OPERATIONS\n// (public) this & a\nexport function op_and(x, y) {\n    return x & y;\n}\n// (public) this | a\nexport function op_or(x, y) {\n    return x | y;\n}\n// (public) this ^ a\nexport function op_xor(x, y) {\n    return x ^ y;\n}\n// (public) this & ~a\nexport function op_andnot(x, y) {\n    return x & ~y;\n}\n// return index of lowest 1-bit in x, x < 2^31\nexport function lbit(x) {\n    if (x == 0) {\n        return -1;\n    }\n    var r = 0;\n    if ((x & 0xffff) == 0) {\n        x >>= 16;\n        r += 16;\n    }\n    if ((x & 0xff) == 0) {\n        x >>= 8;\n        r += 8;\n    }\n    if ((x & 0xf) == 0) {\n        x >>= 4;\n        r += 4;\n    }\n    if ((x & 3) == 0) {\n        x >>= 2;\n        r += 2;\n    }\n    if ((x & 1) == 0) {\n        ++r;\n    }\n    return r;\n}\n// return number of 1 bits in x\nexport function cbit(x) {\n    var r = 0;\n    while (x != 0) {\n        x &= x - 1;\n        ++r;\n    }\n    return r;\n}\n//#endregion BIT_OPERATIONS\n"], "mappings": "AAAA,IAAIA,KAAK,GAAG,sCAAsC;AAClD,OAAO,SAASC,QAAQA,CAACC,CAAC,EAAE;EACxB,OAAOF,KAAK,CAACG,MAAM,CAACD,CAAC,CAAC;AAC1B;AACA;AACA;AACA,OAAO,SAASE,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOD,CAAC,GAAGC,CAAC;AAChB;AACA;AACA,OAAO,SAASC,KAAKA,CAACF,CAAC,EAAEC,CAAC,EAAE;EACxB,OAAOD,CAAC,GAAGC,CAAC;AAChB;AACA;AACA,OAAO,SAASE,MAAMA,CAACH,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOD,CAAC,GAAGC,CAAC;AAChB;AACA;AACA,OAAO,SAASG,SAASA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOD,CAAC,GAAG,CAACC,CAAC;AACjB;AACA;AACA,OAAO,SAASI,IAAIA,CAACL,CAAC,EAAE;EACpB,IAAIA,CAAC,IAAI,CAAC,EAAE;IACR,OAAO,CAAC,CAAC;EACb;EACA,IAAIM,CAAC,GAAG,CAAC;EACT,IAAI,CAACN,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE;IACnBA,CAAC,KAAK,EAAE;IACRM,CAAC,IAAI,EAAE;EACX;EACA,IAAI,CAACN,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;IACjBA,CAAC,KAAK,CAAC;IACPM,CAAC,IAAI,CAAC;EACV;EACA,IAAI,CAACN,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE;IAChBA,CAAC,KAAK,CAAC;IACPM,CAAC,IAAI,CAAC;EACV;EACA,IAAI,CAACN,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IACdA,CAAC,KAAK,CAAC;IACPM,CAAC,IAAI,CAAC;EACV;EACA,IAAI,CAACN,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IACd,EAAEM,CAAC;EACP;EACA,OAAOA,CAAC;AACZ;AACA;AACA,OAAO,SAASC,IAAIA,CAACP,CAAC,EAAE;EACpB,IAAIM,CAAC,GAAG,CAAC;EACT,OAAON,CAAC,IAAI,CAAC,EAAE;IACXA,CAAC,IAAIA,CAAC,GAAG,CAAC;IACV,EAAEM,CAAC;EACP;EACA,OAAOA,CAAC;AACZ;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}