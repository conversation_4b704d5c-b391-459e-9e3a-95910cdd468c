{"ast": null, "code": "// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function GridDetailPanels(_) {\n  return null;\n}", "map": {"version": 3, "names": ["GridDetailPanels", "_"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/GridDetailPanels.js"], "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function GridDetailPanels(_) {\n  return null;\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,gBAAgBA,CAACC,CAAC,EAAE;EAClC,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}