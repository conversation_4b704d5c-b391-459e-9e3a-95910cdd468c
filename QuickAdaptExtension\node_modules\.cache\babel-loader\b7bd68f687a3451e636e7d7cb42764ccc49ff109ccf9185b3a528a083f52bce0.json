{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport { getThemeProps } from '@mui/system/useThemeProps';\nimport useTheme from \"../styles/useTheme.js\";\nimport useEnhancedEffect from \"../utils/useEnhancedEffect.js\";\nimport useMediaQuery from \"../useMediaQuery/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\n\n// By default, returns true if screen width is the same or greater than the given breakpoint.\nexport const isWidthUp = function (breakpoint, width) {\n  let inclusive = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (inclusive) {\n    return breakpointKeys.indexOf(breakpoint) <= breakpointKeys.indexOf(width);\n  }\n  return breakpointKeys.indexOf(breakpoint) < breakpointKeys.indexOf(width);\n};\n\n// By default, returns true if screen width is less than the given breakpoint.\nexport const isWidthDown = function (breakpoint, width) {\n  let inclusive = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (inclusive) {\n    return breakpointKeys.indexOf(width) <= breakpointKeys.indexOf(breakpoint);\n  }\n  return breakpointKeys.indexOf(width) < breakpointKeys.indexOf(breakpoint);\n};\nconst withWidth = function () {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Component => {\n    const {\n      withTheme: withThemeOption = false,\n      noSSR = false,\n      initialWidth: initialWidthOption\n    } = options;\n    function WithWidth(props) {\n      const contextTheme = useTheme();\n      const theme = props.theme || contextTheme;\n      const {\n        initialWidth,\n        width,\n        ...other\n      } = getThemeProps({\n        theme,\n        name: 'MuiWithWidth',\n        props\n      });\n      const [mountedState, setMountedState] = React.useState(false);\n      useEnhancedEffect(() => {\n        setMountedState(true);\n      }, []);\n\n      /**\n       * innerWidth |xs      sm      md      lg      xl\n       *            |-------|-------|-------|-------|------>\n       * width      |  xs   |  sm   |  md   |  lg   |  xl\n       */\n      const keys = theme.breakpoints.keys.slice().reverse();\n      const widthComputed = keys.reduce((output, key) => {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const matches = useMediaQuery(theme.breakpoints.up(key));\n        return !output && matches ? key : output;\n      }, null);\n      const more = {\n        width: width || (mountedState || noSSR ? widthComputed : undefined) || initialWidth || initialWidthOption,\n        ...(withThemeOption ? {\n          theme\n        } : {}),\n        ...other\n      };\n\n      // When rendering the component on the server,\n      // we have no idea about the client browser screen width.\n      // In order to prevent blinks and help the reconciliation of the React tree\n      // we are not rendering the child component.\n      //\n      // An alternative is to use the `initialWidth` property.\n      if (more.width === undefined) {\n        return null;\n      }\n      return /*#__PURE__*/_jsx(Component, {\n        ...more\n      });\n    }\n    process.env.NODE_ENV !== \"production\" ? WithWidth.propTypes = {\n      /**\n       * As `window.innerWidth` is unavailable on the server,\n       * we default to rendering an empty component during the first mount.\n       * You might want to use a heuristic to approximate\n       * the screen width of the client browser screen width.\n       *\n       * For instance, you could be using the user-agent or the client-hints.\n       * https://caniuse.com/#search=client%20hint\n       */\n      initialWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),\n      /**\n       * @ignore\n       */\n      theme: PropTypes.object,\n      /**\n       * Bypass the width calculation logic.\n       */\n      width: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl'])\n    } : void 0;\n    if (process.env.NODE_ENV !== 'production') {\n      WithWidth.displayName = `WithWidth(${getDisplayName(Component)})`;\n    }\n    return WithWidth;\n  };\n};\nexport default withWidth;", "map": {"version": 3, "names": ["React", "PropTypes", "getDisplayName", "getThemeProps", "useTheme", "useEnhancedEffect", "useMediaQuery", "jsx", "_jsx", "breakpoint<PERSON><PERSON><PERSON>", "isWidthUp", "breakpoint", "width", "inclusive", "arguments", "length", "undefined", "indexOf", "isWidthDown", "with<PERSON><PERSON><PERSON>", "options", "Component", "withTheme", "withThemeOption", "noSSR", "initialWidth", "initialWidthOption", "<PERSON><PERSON><PERSON><PERSON>", "props", "contextTheme", "theme", "other", "name", "mountedState", "setMountedState", "useState", "keys", "breakpoints", "slice", "reverse", "widthComputed", "reduce", "output", "key", "matches", "up", "more", "process", "env", "NODE_ENV", "propTypes", "oneOf", "object", "displayName"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/material/Hidden/withWidth.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport { getThemeProps } from '@mui/system/useThemeProps';\nimport useTheme from \"../styles/useTheme.js\";\nimport useEnhancedEffect from \"../utils/useEnhancedEffect.js\";\nimport useMediaQuery from \"../useMediaQuery/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\n\n// By default, returns true if screen width is the same or greater than the given breakpoint.\nexport const isWidthUp = (breakpoint, width, inclusive = true) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(breakpoint) <= breakpointKeys.indexOf(width);\n  }\n  return breakpointKeys.indexOf(breakpoint) < breakpointKeys.indexOf(width);\n};\n\n// By default, returns true if screen width is less than the given breakpoint.\nexport const isWidthDown = (breakpoint, width, inclusive = false) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(width) <= breakpointKeys.indexOf(breakpoint);\n  }\n  return breakpointKeys.indexOf(width) < breakpointKeys.indexOf(breakpoint);\n};\nconst withWidth = (options = {}) => Component => {\n  const {\n    withTheme: withThemeOption = false,\n    noSSR = false,\n    initialWidth: initialWidthOption\n  } = options;\n  function WithWidth(props) {\n    const contextTheme = useTheme();\n    const theme = props.theme || contextTheme;\n    const {\n      initialWidth,\n      width,\n      ...other\n    } = getThemeProps({\n      theme,\n      name: 'MuiWithWidth',\n      props\n    });\n    const [mountedState, setMountedState] = React.useState(false);\n    useEnhancedEffect(() => {\n      setMountedState(true);\n    }, []);\n\n    /**\n     * innerWidth |xs      sm      md      lg      xl\n     *            |-------|-------|-------|-------|------>\n     * width      |  xs   |  sm   |  md   |  lg   |  xl\n     */\n    const keys = theme.breakpoints.keys.slice().reverse();\n    const widthComputed = keys.reduce((output, key) => {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      const matches = useMediaQuery(theme.breakpoints.up(key));\n      return !output && matches ? key : output;\n    }, null);\n    const more = {\n      width: width || (mountedState || noSSR ? widthComputed : undefined) || initialWidth || initialWidthOption,\n      ...(withThemeOption ? {\n        theme\n      } : {}),\n      ...other\n    };\n\n    // When rendering the component on the server,\n    // we have no idea about the client browser screen width.\n    // In order to prevent blinks and help the reconciliation of the React tree\n    // we are not rendering the child component.\n    //\n    // An alternative is to use the `initialWidth` property.\n    if (more.width === undefined) {\n      return null;\n    }\n    return /*#__PURE__*/_jsx(Component, {\n      ...more\n    });\n  }\n  process.env.NODE_ENV !== \"production\" ? WithWidth.propTypes = {\n    /**\n     * As `window.innerWidth` is unavailable on the server,\n     * we default to rendering an empty component during the first mount.\n     * You might want to use a heuristic to approximate\n     * the screen width of the client browser screen width.\n     *\n     * For instance, you could be using the user-agent or the client-hints.\n     * https://caniuse.com/#search=client%20hint\n     */\n    initialWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),\n    /**\n     * @ignore\n     */\n    theme: PropTypes.object,\n    /**\n     * Bypass the width calculation logic.\n     */\n    width: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl'])\n  } : void 0;\n  if (process.env.NODE_ENV !== 'production') {\n    WithWidth.displayName = `WithWidth(${getDisplayName(Component)})`;\n  }\n  return WithWidth;\n};\nexport default withWidth;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;AAErD;AACA,OAAO,MAAMC,SAAS,GAAG,SAAAA,CAACC,UAAU,EAAEC,KAAK,EAAuB;EAAA,IAArBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAC3D,IAAID,SAAS,EAAE;IACb,OAAOJ,cAAc,CAACQ,OAAO,CAACN,UAAU,CAAC,IAAIF,cAAc,CAACQ,OAAO,CAACL,KAAK,CAAC;EAC5E;EACA,OAAOH,cAAc,CAACQ,OAAO,CAACN,UAAU,CAAC,GAAGF,cAAc,CAACQ,OAAO,CAACL,KAAK,CAAC;AAC3E,CAAC;;AAED;AACA,OAAO,MAAMM,WAAW,GAAG,SAAAA,CAACP,UAAU,EAAEC,KAAK,EAAwB;EAAA,IAAtBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC9D,IAAID,SAAS,EAAE;IACb,OAAOJ,cAAc,CAACQ,OAAO,CAACL,KAAK,CAAC,IAAIH,cAAc,CAACQ,OAAO,CAACN,UAAU,CAAC;EAC5E;EACA,OAAOF,cAAc,CAACQ,OAAO,CAACL,KAAK,CAAC,GAAGH,cAAc,CAACQ,OAAO,CAACN,UAAU,CAAC;AAC3E,CAAC;AACD,MAAMQ,SAAS,GAAG,SAAAA,CAAA;EAAA,IAACC,OAAO,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,OAAKO,SAAS,IAAI;IAC/C,MAAM;MACJC,SAAS,EAAEC,eAAe,GAAG,KAAK;MAClCC,KAAK,GAAG,KAAK;MACbC,YAAY,EAAEC;IAChB,CAAC,GAAGN,OAAO;IACX,SAASO,SAASA,CAACC,KAAK,EAAE;MACxB,MAAMC,YAAY,GAAGzB,QAAQ,CAAC,CAAC;MAC/B,MAAM0B,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAID,YAAY;MACzC,MAAM;QACJJ,YAAY;QACZb,KAAK;QACL,GAAGmB;MACL,CAAC,GAAG5B,aAAa,CAAC;QAChB2B,KAAK;QACLE,IAAI,EAAE,cAAc;QACpBJ;MACF,CAAC,CAAC;MACF,MAAM,CAACK,YAAY,EAAEC,eAAe,CAAC,GAAGlC,KAAK,CAACmC,QAAQ,CAAC,KAAK,CAAC;MAC7D9B,iBAAiB,CAAC,MAAM;QACtB6B,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,EAAE,EAAE,CAAC;;MAEN;AACJ;AACA;AACA;AACA;MACI,MAAME,IAAI,GAAGN,KAAK,CAACO,WAAW,CAACD,IAAI,CAACE,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MACrD,MAAMC,aAAa,GAAGJ,IAAI,CAACK,MAAM,CAAC,CAACC,MAAM,EAAEC,GAAG,KAAK;QACjD;QACA,MAAMC,OAAO,GAAGtC,aAAa,CAACwB,KAAK,CAACO,WAAW,CAACQ,EAAE,CAACF,GAAG,CAAC,CAAC;QACxD,OAAO,CAACD,MAAM,IAAIE,OAAO,GAAGD,GAAG,GAAGD,MAAM;MAC1C,CAAC,EAAE,IAAI,CAAC;MACR,MAAMI,IAAI,GAAG;QACXlC,KAAK,EAAEA,KAAK,KAAKqB,YAAY,IAAIT,KAAK,GAAGgB,aAAa,GAAGxB,SAAS,CAAC,IAAIS,YAAY,IAAIC,kBAAkB;QACzG,IAAIH,eAAe,GAAG;UACpBO;QACF,CAAC,GAAG,CAAC,CAAC,CAAC;QACP,GAAGC;MACL,CAAC;;MAED;MACA;MACA;MACA;MACA;MACA;MACA,IAAIe,IAAI,CAAClC,KAAK,KAAKI,SAAS,EAAE;QAC5B,OAAO,IAAI;MACb;MACA,OAAO,aAAaR,IAAI,CAACa,SAAS,EAAE;QAClC,GAAGyB;MACL,CAAC,CAAC;IACJ;IACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,SAAS,CAACuB,SAAS,GAAG;MAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACIzB,YAAY,EAAExB,SAAS,CAACkD,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;MAC7D;AACJ;AACA;MACIrB,KAAK,EAAE7B,SAAS,CAACmD,MAAM;MACvB;AACJ;AACA;MACIxC,KAAK,EAAEX,SAAS,CAACkD,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACvD,CAAC,GAAG,KAAK,CAAC;IACV,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCtB,SAAS,CAAC0B,WAAW,GAAG,aAAanD,cAAc,CAACmB,SAAS,CAAC,GAAG;IACnE;IACA,OAAOM,SAAS;EAClB,CAAC;AAAA;AACD,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}