{"ast": null, "code": "import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { gridVisibleColumnDefinitionsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { useGridVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../colDef/gridCheckboxSelectionColDef.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../constants/gridDetailPanelToggleField.js\";\nimport { gridFocusColumnGroupHeaderSelector } from \"../focus/index.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nimport { gridHeaderFilteringEditFieldSelector, gridHeaderFilteringMenuSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { enrichPageRowsWithPinnedRows, getLeftColumnIndex, getRightColumnIndex, findNonRowSpannedCell } from \"./utils.js\";\n\n/**\n * @requires useGridSorting (method) - can be after\n * @requires useGridFilter (state) - can be after\n * @requires useGridColumns (state, method) - can be after\n * @requires useGridDimensions (method) - can be after\n * @requires useGridFocus (method) - can be after\n * @requires useGridScroll (method) - can be after\n * @requires useGridColumnSpanning (method) - can be after\n */\nexport const useGridKeyboardNavigation = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridKeyboardNavigation');\n  const initialCurrentPageRows = useGridVisibleRows(apiRef, props).rows;\n  const isRtl = useRtl();\n  const currentPageRows = React.useMemo(() => enrichPageRowsWithPinnedRows(apiRef, initialCurrentPageRows), [apiRef, initialCurrentPageRows]);\n  const headerFilteringEnabled = props.signature !== 'DataGrid' && props.headerFilters;\n\n  /**\n   * @param {number} colIndex Index of the column to focus\n   * @param {GridRowId} rowId index of the row to focus\n   * @param {string} closestColumnToUse Which closest column cell to use when the cell is spanned by `colSpan`.\n   * @param {string} rowSpanScanDirection Which direction to search to find the next cell not hidden by `rowSpan`.\n   * TODO replace with apiRef.current.moveFocusToRelativeCell()\n   */\n  const goToCell = React.useCallback(function (colIndex, rowId) {\n    let closestColumnToUse = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'left';\n    let rowSpanScanDirection = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'up';\n    const visibleSortedRows = gridExpandedSortedRowEntriesSelector(apiRef);\n    const nextCellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, colIndex);\n    if (nextCellColSpanInfo && nextCellColSpanInfo.spannedByColSpan) {\n      if (closestColumnToUse === 'left') {\n        colIndex = nextCellColSpanInfo.leftVisibleCellIndex;\n      } else if (closestColumnToUse === 'right') {\n        colIndex = nextCellColSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    const field = gridVisibleColumnFieldsSelector(apiRef)[colIndex];\n    const nonRowSpannedRowId = findNonRowSpannedCell(apiRef, rowId, field, rowSpanScanDirection);\n    // `scrollToIndexes` requires a rowIndex relative to all visible rows.\n    // Those rows do not include pinned rows, but pinned rows do not need scroll anyway.\n    const rowIndexRelativeToAllRows = visibleSortedRows.findIndex(row => row.id === nonRowSpannedRowId);\n    logger.debug(`Navigating to cell row ${rowIndexRelativeToAllRows}, col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex,\n      rowIndex: rowIndexRelativeToAllRows\n    });\n    apiRef.current.setCellFocus(nonRowSpannedRowId, field);\n  }, [apiRef, logger]);\n  const goToHeader = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef, logger]);\n  const goToHeaderFilter = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header filter col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFilterFocus(field, event);\n  }, [apiRef, logger]);\n  const goToGroupHeader = React.useCallback((colIndex, depth, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const {\n      field\n    } = apiRef.current.getVisibleColumns()[colIndex];\n    apiRef.current.setColumnGroupHeaderFocus(field, depth, event);\n  }, [apiRef, logger]);\n  const getRowIdFromIndex = React.useCallback(rowIndex => {\n    return currentPageRows[rowIndex]?.id;\n  }, [currentPageRows]);\n  const handleColumnHeaderKeyDown = React.useCallback((params, event) => {\n    const headerTitleNode = event.currentTarget.querySelector(`.${gridClasses.columnHeaderTitleContainerContent}`);\n    const isFromInsideContent = !!headerTitleNode && headerTitleNode.contains(event.target);\n    if (isFromInsideContent && params.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // When focus is on a nested input, keyboard events have no effect to avoid conflicts with native events.\n      // There is one exception for the checkBoxHeader\n      return;\n    }\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = currentPageRows.length > 0 ? 0 : null;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    const columnGroupMaxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (firstRowIndexInPage !== null) {\n            if (headerFilteringEnabled) {\n              goToHeaderFilter(colIndexBefore, event);\n            } else {\n              goToCell(colIndexBefore, getRowIdFromIndex(firstRowIndexInPage));\n            }\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeader(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeader(leftColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (columnGroupMaxDepth > 0) {\n            goToGroupHeader(colIndexBefore, columnGroupMaxDepth - 1, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeader(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeader(lastColIndex, event);\n          break;\n        }\n      case 'Enter':\n        {\n          if (event.ctrlKey || event.metaKey) {\n            apiRef.current.toggleColumnMenu(params.field);\n          }\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, currentPageRows.length, headerFilteringEnabled, goToHeaderFilter, goToCell, getRowIdFromIndex, isRtl, goToHeader, goToGroupHeader]);\n  const handleHeaderFilterKeyDown = React.useCallback((params, event) => {\n    const isEditing = gridHeaderFilteringEditFieldSelector(apiRef) === params.field;\n    const isHeaderMenuOpen = gridHeaderFilteringMenuSelector(apiRef) === params.field;\n    if (isEditing || isHeaderMenuOpen || !isNavigationKey(event.key)) {\n      return;\n    }\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          const rowId = getRowIdFromIndex(firstRowIndexInPage);\n          if (firstRowIndexInPage !== null && rowId != null) {\n            goToCell(colIndexBefore, rowId);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeaderFilter(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeaderFilter(leftColIndex, event);\n          } else {\n            apiRef.current.setColumnHeaderFilterFocus(params.field, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          goToHeader(colIndexBefore, event);\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeaderFilter(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeaderFilter(lastColIndex, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, currentPageRows.length, goToHeaderFilter, isRtl, goToHeader, goToCell, getRowIdFromIndex]);\n  const handleColumnGroupHeaderKeyDown = React.useCallback((params, event) => {\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup === null) {\n      return;\n    }\n    const {\n      field: currentField,\n      depth: currentDepth\n    } = focusedColumnGroup;\n    const {\n      fields,\n      depth,\n      maxDepth\n    } = params;\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const currentColIndex = apiRef.current.getColumnIndex(currentField);\n    const colIndexBefore = currentField ? apiRef.current.getColumnIndex(currentField) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (depth === maxDepth - 1) {\n            goToHeader(currentColIndex, event);\n          } else {\n            goToGroupHeader(currentColIndex, currentDepth + 1, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (depth > 0) {\n            goToGroupHeader(currentColIndex, currentDepth - 1, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const remainingRightColumns = fields.length - fields.indexOf(currentField) - 1;\n          if (currentColIndex + remainingRightColumns + 1 <= lastColIndex) {\n            goToGroupHeader(currentColIndex + remainingRightColumns + 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const remainingLeftColumns = fields.indexOf(currentField);\n          if (currentColIndex - remainingLeftColumns - 1 >= firstColIndex) {\n            goToGroupHeader(currentColIndex - remainingLeftColumns - 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToGroupHeader(firstColIndex, currentDepth, event);\n          break;\n        }\n      case 'End':\n        {\n          goToGroupHeader(lastColIndex, currentDepth, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, currentPageRows.length, goToHeader, goToGroupHeader, goToCell, getRowIdFromIndex]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Ignore portal\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n\n    // Get the most recent params because the cell mode may have changed by another listener\n    const cellParams = apiRef.current.getCellParams(params.id, params.field);\n    if (cellParams.cellMode === GridCellModes.Edit || !isNavigationKey(event.key)) {\n      return;\n    }\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    if (currentPageRows.length === 0) {\n      return;\n    }\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const rowIndexBefore = currentPageRows.findIndex(row => row.id === params.id);\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          // \"Enter\" is only triggered by the row / cell editing feature\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore + 1), isRtl ? 'right' : 'left', 'down');\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (rowIndexBefore > firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore - 1));\n          } else if (headerFilteringEnabled) {\n            goToHeaderFilter(colIndexBefore, event);\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToCell(rightColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'left' : 'right');\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToCell(leftColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'right' : 'left');\n          }\n          break;\n        }\n      case 'Tab':\n        {\n          // \"Tab\" is only triggered by the row / cell editing feature\n          if (event.shiftKey && colIndexBefore > firstColIndex) {\n            goToCell(colIndexBefore - 1, getRowIdFromIndex(rowIndexBefore), 'left');\n          } else if (!event.shiftKey && colIndexBefore < lastColIndex) {\n            goToCell(colIndexBefore + 1, getRowIdFromIndex(rowIndexBefore), 'right');\n          }\n          break;\n        }\n      case ' ':\n        {\n          const field = params.field;\n          if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n            break;\n          }\n          const colDef = params.colDef;\n          if (colDef &&\n          // `GRID_TREE_DATA_GROUPING_FIELD` from the Pro package\n          colDef.field === '__tree_data_group__') {\n            break;\n          }\n          if (!event.shiftKey && rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageUp':\n        {\n          // Go to the first row before going to header\n          const nextRowIndex = Math.max(rowIndexBefore - viewportPageSize, firstRowIndexInPage);\n          if (nextRowIndex !== rowIndexBefore && nextRowIndex >= firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(nextRowIndex));\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'Home':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(firstColIndex, getRowIdFromIndex(firstRowIndexInPage));\n          } else {\n            goToCell(firstColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      case 'End':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(lastColIndex, getRowIdFromIndex(lastRowIndexInPage));\n          } else {\n            goToCell(lastColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, currentPageRows, isRtl, goToCell, getRowIdFromIndex, headerFilteringEnabled, goToHeaderFilter, goToHeader]);\n  const checkIfCanStartEditing = React.useCallback((initialValue, _ref) => {\n    let {\n      event\n    } = _ref;\n    if (event.key === ' ') {\n      // Space scrolls to the last row\n      return false;\n    }\n    return initialValue;\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'canStartEditing', checkIfCanStartEditing);\n  useGridApiEventHandler(apiRef, 'columnHeaderKeyDown', handleColumnHeaderKeyDown);\n  useGridApiEventHandler(apiRef, 'headerFilterKeyDown', handleHeaderFilterKeyDown);\n  useGridApiEventHandler(apiRef, 'columnGroupHeaderKeyDown', handleColumnGroupHeaderKeyDown);\n  useGridApiEventHandler(apiRef, 'cellKeyDown', handleCellKeyDown);\n};", "map": {"version": 3, "names": ["React", "useRtl", "gridVisibleColumnDefinitionsSelector", "gridVisibleColumnFieldsSelector", "useGridLogger", "useGridApiEventHandler", "gridExpandedSortedRowEntriesSelector", "useGridVisibleRows", "GRID_CHECKBOX_SELECTION_COL_DEF", "gridClasses", "GridCellModes", "isNavigationKey", "GRID_DETAIL_PANEL_TOGGLE_FIELD", "gridFocusColumnGroupHeaderSelector", "gridColumnGroupsHeaderMaxDepthSelector", "gridHeaderFilteringEditFieldSelector", "gridHeaderFilteringMenuSelector", "useGridRegisterPipeProcessor", "isEventTargetInPortal", "enrichPageRowsWithPinnedRows", "getLeftColumnIndex", "getRightColumnIndex", "findNonRowSpannedCell", "useGridKeyboardNavigation", "apiRef", "props", "logger", "initialCurrentPageRows", "rows", "isRtl", "currentPageRows", "useMemo", "headerFilteringEnabled", "signature", "headerFilters", "goToCell", "useCallback", "colIndex", "rowId", "closestColumnToUse", "arguments", "length", "undefined", "rowSpanScanDirection", "visibleSortedRows", "nextCellColSpanInfo", "current", "unstable_getCellColSpanInfo", "spannedByColSpan", "leftVisibleCellIndex", "rightVisibleCellIndex", "field", "nonRowSpannedRowId", "rowIndexRelativeToAllRows", "findIndex", "row", "id", "debug", "scrollToIndexes", "rowIndex", "setCellFocus", "goToHeader", "event", "getVisibleColumns", "setColumnHeaderFocus", "goToHeaderFilter", "setColumnHeaderFilterFocus", "goToGroupHeader", "depth", "setColumnGroupHeaderFocus", "getRowIdFromIndex", "handleColumnHeaderKeyDown", "params", "headerTitleNode", "currentTarget", "querySelector", "columnHeaderTitleContainerContent", "isFromInsideContent", "contains", "target", "viewportPageSize", "getViewportPageSize", "colIndexBefore", "getColumnIndex", "firstRowIndexInPage", "lastRowIndexInPage", "firstColIndex", "lastColIndex", "columnGroupMaxDepth", "shouldPreventDefault", "key", "rightColIndex", "currentColIndex", "leftColIndex", "Math", "min", "ctrl<PERSON>ey", "metaKey", "toggleColumnMenu", "preventDefault", "handleHeaderFilterKeyDown", "isEditing", "isHeaderMenuOpen", "handleColumnGroupHeaderKeyDown", "focusedColumnGroup", "current<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "fields", "max<PERSON><PERSON><PERSON>", "remainingRightColumns", "indexOf", "remainingLeftColumns", "handleCellKeyDown", "cellParams", "getCellParams", "cellMode", "Edit", "canUpdateFocus", "unstable_applyPipeProcessors", "cell", "rowIndexBefore", "shift<PERSON>ey", "colDef", "nextRowIndex", "max", "checkIfCanStartEditing", "initialValue", "_ref"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/keyboardNavigation/useGridKeyboardNavigation.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { gridVisibleColumnDefinitionsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { useGridVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../colDef/gridCheckboxSelectionColDef.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../constants/gridDetailPanelToggleField.js\";\nimport { gridFocusColumnGroupHeaderSelector } from \"../focus/index.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nimport { gridHeaderFilteringEditFieldSelector, gridHeaderFilteringMenuSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { enrichPageRowsWithPinnedRows, getLeftColumnIndex, getRightColumnIndex, findNonRowSpannedCell } from \"./utils.js\";\n\n/**\n * @requires useGridSorting (method) - can be after\n * @requires useGridFilter (state) - can be after\n * @requires useGridColumns (state, method) - can be after\n * @requires useGridDimensions (method) - can be after\n * @requires useGridFocus (method) - can be after\n * @requires useGridScroll (method) - can be after\n * @requires useGridColumnSpanning (method) - can be after\n */\nexport const useGridKeyboardNavigation = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridKeyboardNavigation');\n  const initialCurrentPageRows = useGridVisibleRows(apiRef, props).rows;\n  const isRtl = useRtl();\n  const currentPageRows = React.useMemo(() => enrichPageRowsWithPinnedRows(apiRef, initialCurrentPageRows), [apiRef, initialCurrentPageRows]);\n  const headerFilteringEnabled = props.signature !== 'DataGrid' && props.headerFilters;\n\n  /**\n   * @param {number} colIndex Index of the column to focus\n   * @param {GridRowId} rowId index of the row to focus\n   * @param {string} closestColumnToUse Which closest column cell to use when the cell is spanned by `colSpan`.\n   * @param {string} rowSpanScanDirection Which direction to search to find the next cell not hidden by `rowSpan`.\n   * TODO replace with apiRef.current.moveFocusToRelativeCell()\n   */\n  const goToCell = React.useCallback((colIndex, rowId, closestColumnToUse = 'left', rowSpanScanDirection = 'up') => {\n    const visibleSortedRows = gridExpandedSortedRowEntriesSelector(apiRef);\n    const nextCellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, colIndex);\n    if (nextCellColSpanInfo && nextCellColSpanInfo.spannedByColSpan) {\n      if (closestColumnToUse === 'left') {\n        colIndex = nextCellColSpanInfo.leftVisibleCellIndex;\n      } else if (closestColumnToUse === 'right') {\n        colIndex = nextCellColSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    const field = gridVisibleColumnFieldsSelector(apiRef)[colIndex];\n    const nonRowSpannedRowId = findNonRowSpannedCell(apiRef, rowId, field, rowSpanScanDirection);\n    // `scrollToIndexes` requires a rowIndex relative to all visible rows.\n    // Those rows do not include pinned rows, but pinned rows do not need scroll anyway.\n    const rowIndexRelativeToAllRows = visibleSortedRows.findIndex(row => row.id === nonRowSpannedRowId);\n    logger.debug(`Navigating to cell row ${rowIndexRelativeToAllRows}, col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex,\n      rowIndex: rowIndexRelativeToAllRows\n    });\n    apiRef.current.setCellFocus(nonRowSpannedRowId, field);\n  }, [apiRef, logger]);\n  const goToHeader = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef, logger]);\n  const goToHeaderFilter = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header filter col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFilterFocus(field, event);\n  }, [apiRef, logger]);\n  const goToGroupHeader = React.useCallback((colIndex, depth, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const {\n      field\n    } = apiRef.current.getVisibleColumns()[colIndex];\n    apiRef.current.setColumnGroupHeaderFocus(field, depth, event);\n  }, [apiRef, logger]);\n  const getRowIdFromIndex = React.useCallback(rowIndex => {\n    return currentPageRows[rowIndex]?.id;\n  }, [currentPageRows]);\n  const handleColumnHeaderKeyDown = React.useCallback((params, event) => {\n    const headerTitleNode = event.currentTarget.querySelector(`.${gridClasses.columnHeaderTitleContainerContent}`);\n    const isFromInsideContent = !!headerTitleNode && headerTitleNode.contains(event.target);\n    if (isFromInsideContent && params.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // When focus is on a nested input, keyboard events have no effect to avoid conflicts with native events.\n      // There is one exception for the checkBoxHeader\n      return;\n    }\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = currentPageRows.length > 0 ? 0 : null;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    const columnGroupMaxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (firstRowIndexInPage !== null) {\n            if (headerFilteringEnabled) {\n              goToHeaderFilter(colIndexBefore, event);\n            } else {\n              goToCell(colIndexBefore, getRowIdFromIndex(firstRowIndexInPage));\n            }\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeader(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeader(leftColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (columnGroupMaxDepth > 0) {\n            goToGroupHeader(colIndexBefore, columnGroupMaxDepth - 1, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeader(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeader(lastColIndex, event);\n          break;\n        }\n      case 'Enter':\n        {\n          if (event.ctrlKey || event.metaKey) {\n            apiRef.current.toggleColumnMenu(params.field);\n          }\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, currentPageRows.length, headerFilteringEnabled, goToHeaderFilter, goToCell, getRowIdFromIndex, isRtl, goToHeader, goToGroupHeader]);\n  const handleHeaderFilterKeyDown = React.useCallback((params, event) => {\n    const isEditing = gridHeaderFilteringEditFieldSelector(apiRef) === params.field;\n    const isHeaderMenuOpen = gridHeaderFilteringMenuSelector(apiRef) === params.field;\n    if (isEditing || isHeaderMenuOpen || !isNavigationKey(event.key)) {\n      return;\n    }\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          const rowId = getRowIdFromIndex(firstRowIndexInPage);\n          if (firstRowIndexInPage !== null && rowId != null) {\n            goToCell(colIndexBefore, rowId);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeaderFilter(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeaderFilter(leftColIndex, event);\n          } else {\n            apiRef.current.setColumnHeaderFilterFocus(params.field, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          goToHeader(colIndexBefore, event);\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeaderFilter(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeaderFilter(lastColIndex, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, currentPageRows.length, goToHeaderFilter, isRtl, goToHeader, goToCell, getRowIdFromIndex]);\n  const handleColumnGroupHeaderKeyDown = React.useCallback((params, event) => {\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup === null) {\n      return;\n    }\n    const {\n      field: currentField,\n      depth: currentDepth\n    } = focusedColumnGroup;\n    const {\n      fields,\n      depth,\n      maxDepth\n    } = params;\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const currentColIndex = apiRef.current.getColumnIndex(currentField);\n    const colIndexBefore = currentField ? apiRef.current.getColumnIndex(currentField) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (depth === maxDepth - 1) {\n            goToHeader(currentColIndex, event);\n          } else {\n            goToGroupHeader(currentColIndex, currentDepth + 1, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (depth > 0) {\n            goToGroupHeader(currentColIndex, currentDepth - 1, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const remainingRightColumns = fields.length - fields.indexOf(currentField) - 1;\n          if (currentColIndex + remainingRightColumns + 1 <= lastColIndex) {\n            goToGroupHeader(currentColIndex + remainingRightColumns + 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const remainingLeftColumns = fields.indexOf(currentField);\n          if (currentColIndex - remainingLeftColumns - 1 >= firstColIndex) {\n            goToGroupHeader(currentColIndex - remainingLeftColumns - 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToGroupHeader(firstColIndex, currentDepth, event);\n          break;\n        }\n      case 'End':\n        {\n          goToGroupHeader(lastColIndex, currentDepth, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, currentPageRows.length, goToHeader, goToGroupHeader, goToCell, getRowIdFromIndex]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Ignore portal\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n\n    // Get the most recent params because the cell mode may have changed by another listener\n    const cellParams = apiRef.current.getCellParams(params.id, params.field);\n    if (cellParams.cellMode === GridCellModes.Edit || !isNavigationKey(event.key)) {\n      return;\n    }\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    if (currentPageRows.length === 0) {\n      return;\n    }\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const rowIndexBefore = currentPageRows.findIndex(row => row.id === params.id);\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          // \"Enter\" is only triggered by the row / cell editing feature\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore + 1), isRtl ? 'right' : 'left', 'down');\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (rowIndexBefore > firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore - 1));\n          } else if (headerFilteringEnabled) {\n            goToHeaderFilter(colIndexBefore, event);\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToCell(rightColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'left' : 'right');\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToCell(leftColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'right' : 'left');\n          }\n          break;\n        }\n      case 'Tab':\n        {\n          // \"Tab\" is only triggered by the row / cell editing feature\n          if (event.shiftKey && colIndexBefore > firstColIndex) {\n            goToCell(colIndexBefore - 1, getRowIdFromIndex(rowIndexBefore), 'left');\n          } else if (!event.shiftKey && colIndexBefore < lastColIndex) {\n            goToCell(colIndexBefore + 1, getRowIdFromIndex(rowIndexBefore), 'right');\n          }\n          break;\n        }\n      case ' ':\n        {\n          const field = params.field;\n          if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n            break;\n          }\n          const colDef = params.colDef;\n          if (colDef &&\n          // `GRID_TREE_DATA_GROUPING_FIELD` from the Pro package\n          colDef.field === '__tree_data_group__') {\n            break;\n          }\n          if (!event.shiftKey && rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageUp':\n        {\n          // Go to the first row before going to header\n          const nextRowIndex = Math.max(rowIndexBefore - viewportPageSize, firstRowIndexInPage);\n          if (nextRowIndex !== rowIndexBefore && nextRowIndex >= firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(nextRowIndex));\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'Home':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(firstColIndex, getRowIdFromIndex(firstRowIndexInPage));\n          } else {\n            goToCell(firstColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      case 'End':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(lastColIndex, getRowIdFromIndex(lastRowIndexInPage));\n          } else {\n            goToCell(lastColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, currentPageRows, isRtl, goToCell, getRowIdFromIndex, headerFilteringEnabled, goToHeaderFilter, goToHeader]);\n  const checkIfCanStartEditing = React.useCallback((initialValue, {\n    event\n  }) => {\n    if (event.key === ' ') {\n      // Space scrolls to the last row\n      return false;\n    }\n    return initialValue;\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'canStartEditing', checkIfCanStartEditing);\n  useGridApiEventHandler(apiRef, 'columnHeaderKeyDown', handleColumnHeaderKeyDown);\n  useGridApiEventHandler(apiRef, 'headerFilterKeyDown', handleHeaderFilterKeyDown);\n  useGridApiEventHandler(apiRef, 'columnGroupHeaderKeyDown', handleColumnGroupHeaderKeyDown);\n  useGridApiEventHandler(apiRef, 'cellKeyDown', handleCellKeyDown);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,oCAAoC,EAAEC,+BAA+B,QAAQ,mCAAmC;AACzH,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,oCAAoC,QAAQ,iCAAiC;AACtF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,+BAA+B,QAAQ,gDAAgD;AAChG,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,8BAA8B,QAAQ,kDAAkD;AACjG,SAASC,kCAAkC,QAAQ,mBAAmB;AACtE,SAASC,sCAAsC,QAAQ,+CAA+C;AACtG,SAASC,oCAAoC,EAAEC,+BAA+B,QAAQ,oDAAoD;AAC1I,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,4BAA4B,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,qBAAqB,QAAQ,YAAY;;AAEzH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,yBAAyB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC1D,MAAMC,MAAM,GAAGtB,aAAa,CAACoB,MAAM,EAAE,2BAA2B,CAAC;EACjE,MAAMG,sBAAsB,GAAGpB,kBAAkB,CAACiB,MAAM,EAAEC,KAAK,CAAC,CAACG,IAAI;EACrE,MAAMC,KAAK,GAAG5B,MAAM,CAAC,CAAC;EACtB,MAAM6B,eAAe,GAAG9B,KAAK,CAAC+B,OAAO,CAAC,MAAMZ,4BAA4B,CAACK,MAAM,EAAEG,sBAAsB,CAAC,EAAE,CAACH,MAAM,EAAEG,sBAAsB,CAAC,CAAC;EAC3I,MAAMK,sBAAsB,GAAGP,KAAK,CAACQ,SAAS,KAAK,UAAU,IAAIR,KAAK,CAACS,aAAa;;EAEpF;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,QAAQ,GAAGnC,KAAK,CAACoC,WAAW,CAAC,UAACC,QAAQ,EAAEC,KAAK,EAA+D;IAAA,IAA7DC,kBAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;IAAA,IAAEG,oBAAoB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAC3G,MAAMI,iBAAiB,GAAGtC,oCAAoC,CAACkB,MAAM,CAAC;IACtE,MAAMqB,mBAAmB,GAAGrB,MAAM,CAACsB,OAAO,CAACC,2BAA2B,CAACT,KAAK,EAAED,QAAQ,CAAC;IACvF,IAAIQ,mBAAmB,IAAIA,mBAAmB,CAACG,gBAAgB,EAAE;MAC/D,IAAIT,kBAAkB,KAAK,MAAM,EAAE;QACjCF,QAAQ,GAAGQ,mBAAmB,CAACI,oBAAoB;MACrD,CAAC,MAAM,IAAIV,kBAAkB,KAAK,OAAO,EAAE;QACzCF,QAAQ,GAAGQ,mBAAmB,CAACK,qBAAqB;MACtD;IACF;IACA,MAAMC,KAAK,GAAGhD,+BAA+B,CAACqB,MAAM,CAAC,CAACa,QAAQ,CAAC;IAC/D,MAAMe,kBAAkB,GAAG9B,qBAAqB,CAACE,MAAM,EAAEc,KAAK,EAAEa,KAAK,EAAER,oBAAoB,CAAC;IAC5F;IACA;IACA,MAAMU,yBAAyB,GAAGT,iBAAiB,CAACU,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKJ,kBAAkB,CAAC;IACnG1B,MAAM,CAAC+B,KAAK,CAAC,0BAA0BJ,yBAAyB,SAAShB,QAAQ,EAAE,CAAC;IACpFb,MAAM,CAACsB,OAAO,CAACY,eAAe,CAAC;MAC7BrB,QAAQ;MACRsB,QAAQ,EAAEN;IACZ,CAAC,CAAC;IACF7B,MAAM,CAACsB,OAAO,CAACc,YAAY,CAACR,kBAAkB,EAAED,KAAK,CAAC;EACxD,CAAC,EAAE,CAAC3B,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMmC,UAAU,GAAG7D,KAAK,CAACoC,WAAW,CAAC,CAACC,QAAQ,EAAEyB,KAAK,KAAK;IACxDpC,MAAM,CAAC+B,KAAK,CAAC,4BAA4BpB,QAAQ,EAAE,CAAC;IACpDb,MAAM,CAACsB,OAAO,CAACY,eAAe,CAAC;MAC7BrB;IACF,CAAC,CAAC;IACF,MAAMc,KAAK,GAAG3B,MAAM,CAACsB,OAAO,CAACiB,iBAAiB,CAAC,CAAC,CAAC1B,QAAQ,CAAC,CAACc,KAAK;IAChE3B,MAAM,CAACsB,OAAO,CAACkB,oBAAoB,CAACb,KAAK,EAAEW,KAAK,CAAC;EACnD,CAAC,EAAE,CAACtC,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMuC,gBAAgB,GAAGjE,KAAK,CAACoC,WAAW,CAAC,CAACC,QAAQ,EAAEyB,KAAK,KAAK;IAC9DpC,MAAM,CAAC+B,KAAK,CAAC,mCAAmCpB,QAAQ,EAAE,CAAC;IAC3Db,MAAM,CAACsB,OAAO,CAACY,eAAe,CAAC;MAC7BrB;IACF,CAAC,CAAC;IACF,MAAMc,KAAK,GAAG3B,MAAM,CAACsB,OAAO,CAACiB,iBAAiB,CAAC,CAAC,CAAC1B,QAAQ,CAAC,CAACc,KAAK;IAChE3B,MAAM,CAACsB,OAAO,CAACoB,0BAA0B,CAACf,KAAK,EAAEW,KAAK,CAAC;EACzD,CAAC,EAAE,CAACtC,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMyC,eAAe,GAAGnE,KAAK,CAACoC,WAAW,CAAC,CAACC,QAAQ,EAAE+B,KAAK,EAAEN,KAAK,KAAK;IACpEpC,MAAM,CAAC+B,KAAK,CAAC,4BAA4BpB,QAAQ,EAAE,CAAC;IACpDb,MAAM,CAACsB,OAAO,CAACY,eAAe,CAAC;MAC7BrB;IACF,CAAC,CAAC;IACF,MAAM;MACJc;IACF,CAAC,GAAG3B,MAAM,CAACsB,OAAO,CAACiB,iBAAiB,CAAC,CAAC,CAAC1B,QAAQ,CAAC;IAChDb,MAAM,CAACsB,OAAO,CAACuB,yBAAyB,CAAClB,KAAK,EAAEiB,KAAK,EAAEN,KAAK,CAAC;EAC/D,CAAC,EAAE,CAACtC,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAM4C,iBAAiB,GAAGtE,KAAK,CAACoC,WAAW,CAACuB,QAAQ,IAAI;IACtD,OAAO7B,eAAe,CAAC6B,QAAQ,CAAC,EAAEH,EAAE;EACtC,CAAC,EAAE,CAAC1B,eAAe,CAAC,CAAC;EACrB,MAAMyC,yBAAyB,GAAGvE,KAAK,CAACoC,WAAW,CAAC,CAACoC,MAAM,EAAEV,KAAK,KAAK;IACrE,MAAMW,eAAe,GAAGX,KAAK,CAACY,aAAa,CAACC,aAAa,CAAC,IAAIlE,WAAW,CAACmE,iCAAiC,EAAE,CAAC;IAC9G,MAAMC,mBAAmB,GAAG,CAAC,CAACJ,eAAe,IAAIA,eAAe,CAACK,QAAQ,CAAChB,KAAK,CAACiB,MAAM,CAAC;IACvF,IAAIF,mBAAmB,IAAIL,MAAM,CAACrB,KAAK,KAAK3C,+BAA+B,CAAC2C,KAAK,EAAE;MACjF;MACA;MACA;IACF;IACA,MAAM6B,gBAAgB,GAAGxD,MAAM,CAACsB,OAAO,CAACmC,mBAAmB,CAAC,CAAC;IAC7D,MAAMC,cAAc,GAAGV,MAAM,CAACrB,KAAK,GAAG3B,MAAM,CAACsB,OAAO,CAACqC,cAAc,CAACX,MAAM,CAACrB,KAAK,CAAC,GAAG,CAAC;IACrF,MAAMiC,mBAAmB,GAAGtD,eAAe,CAACW,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;IACjE,MAAM4C,kBAAkB,GAAGvD,eAAe,CAACW,MAAM,GAAG,CAAC;IACrD,MAAM6C,aAAa,GAAG,CAAC;IACvB,MAAMC,YAAY,GAAGrF,oCAAoC,CAACsB,MAAM,CAAC,CAACiB,MAAM,GAAG,CAAC;IAC5E,MAAM+C,mBAAmB,GAAG1E,sCAAsC,CAACU,MAAM,CAAC;IAC1E,IAAIiE,oBAAoB,GAAG,IAAI;IAC/B,QAAQ3B,KAAK,CAAC4B,GAAG;MACf,KAAK,WAAW;QACd;UACE,IAAIN,mBAAmB,KAAK,IAAI,EAAE;YAChC,IAAIpD,sBAAsB,EAAE;cAC1BiC,gBAAgB,CAACiB,cAAc,EAAEpB,KAAK,CAAC;YACzC,CAAC,MAAM;cACL3B,QAAQ,CAAC+C,cAAc,EAAEZ,iBAAiB,CAACc,mBAAmB,CAAC,CAAC;YAClE;UACF;UACA;QACF;MACF,KAAK,YAAY;QACf;UACE,MAAMO,aAAa,GAAGtE,mBAAmB,CAAC;YACxCuE,eAAe,EAAEV,cAAc;YAC/BI,aAAa;YACbC,YAAY;YACZ1D;UACF,CAAC,CAAC;UACF,IAAI8D,aAAa,KAAK,IAAI,EAAE;YAC1B9B,UAAU,CAAC8B,aAAa,EAAE7B,KAAK,CAAC;UAClC;UACA;QACF;MACF,KAAK,WAAW;QACd;UACE,MAAM+B,YAAY,GAAGzE,kBAAkB,CAAC;YACtCwE,eAAe,EAAEV,cAAc;YAC/BI,aAAa;YACbC,YAAY;YACZ1D;UACF,CAAC,CAAC;UACF,IAAIgE,YAAY,KAAK,IAAI,EAAE;YACzBhC,UAAU,CAACgC,YAAY,EAAE/B,KAAK,CAAC;UACjC;UACA;QACF;MACF,KAAK,SAAS;QACZ;UACE,IAAI0B,mBAAmB,GAAG,CAAC,EAAE;YAC3BrB,eAAe,CAACe,cAAc,EAAEM,mBAAmB,GAAG,CAAC,EAAE1B,KAAK,CAAC;UACjE;UACA;QACF;MACF,KAAK,UAAU;QACb;UACE,IAAIsB,mBAAmB,KAAK,IAAI,IAAIC,kBAAkB,KAAK,IAAI,EAAE;YAC/DlD,QAAQ,CAAC+C,cAAc,EAAEZ,iBAAiB,CAACwB,IAAI,CAACC,GAAG,CAACX,mBAAmB,GAAGJ,gBAAgB,EAAEK,kBAAkB,CAAC,CAAC,CAAC;UACnH;UACA;QACF;MACF,KAAK,MAAM;QACT;UACExB,UAAU,CAACyB,aAAa,EAAExB,KAAK,CAAC;UAChC;QACF;MACF,KAAK,KAAK;QACR;UACED,UAAU,CAAC0B,YAAY,EAAEzB,KAAK,CAAC;UAC/B;QACF;MACF,KAAK,OAAO;QACV;UACE,IAAIA,KAAK,CAACkC,OAAO,IAAIlC,KAAK,CAACmC,OAAO,EAAE;YAClCzE,MAAM,CAACsB,OAAO,CAACoD,gBAAgB,CAAC1B,MAAM,CAACrB,KAAK,CAAC;UAC/C;UACA;QACF;MACF,KAAK,GAAG;QACN;UACE;UACA;QACF;MACF;QACE;UACEsC,oBAAoB,GAAG,KAAK;QAC9B;IACJ;IACA,IAAIA,oBAAoB,EAAE;MACxB3B,KAAK,CAACqC,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC3E,MAAM,EAAEM,eAAe,CAACW,MAAM,EAAET,sBAAsB,EAAEiC,gBAAgB,EAAE9B,QAAQ,EAAEmC,iBAAiB,EAAEzC,KAAK,EAAEgC,UAAU,EAAEM,eAAe,CAAC,CAAC;EAC/I,MAAMiC,yBAAyB,GAAGpG,KAAK,CAACoC,WAAW,CAAC,CAACoC,MAAM,EAAEV,KAAK,KAAK;IACrE,MAAMuC,SAAS,GAAGtF,oCAAoC,CAACS,MAAM,CAAC,KAAKgD,MAAM,CAACrB,KAAK;IAC/E,MAAMmD,gBAAgB,GAAGtF,+BAA+B,CAACQ,MAAM,CAAC,KAAKgD,MAAM,CAACrB,KAAK;IACjF,IAAIkD,SAAS,IAAIC,gBAAgB,IAAI,CAAC3F,eAAe,CAACmD,KAAK,CAAC4B,GAAG,CAAC,EAAE;MAChE;IACF;IACA,MAAMV,gBAAgB,GAAGxD,MAAM,CAACsB,OAAO,CAACmC,mBAAmB,CAAC,CAAC;IAC7D,MAAMC,cAAc,GAAGV,MAAM,CAACrB,KAAK,GAAG3B,MAAM,CAACsB,OAAO,CAACqC,cAAc,CAACX,MAAM,CAACrB,KAAK,CAAC,GAAG,CAAC;IACrF,MAAMiC,mBAAmB,GAAG,CAAC;IAC7B,MAAMC,kBAAkB,GAAGvD,eAAe,CAACW,MAAM,GAAG,CAAC;IACrD,MAAM6C,aAAa,GAAG,CAAC;IACvB,MAAMC,YAAY,GAAGrF,oCAAoC,CAACsB,MAAM,CAAC,CAACiB,MAAM,GAAG,CAAC;IAC5E,IAAIgD,oBAAoB,GAAG,IAAI;IAC/B,QAAQ3B,KAAK,CAAC4B,GAAG;MACf,KAAK,WAAW;QACd;UACE,MAAMpD,KAAK,GAAGgC,iBAAiB,CAACc,mBAAmB,CAAC;UACpD,IAAIA,mBAAmB,KAAK,IAAI,IAAI9C,KAAK,IAAI,IAAI,EAAE;YACjDH,QAAQ,CAAC+C,cAAc,EAAE5C,KAAK,CAAC;UACjC;UACA;QACF;MACF,KAAK,YAAY;QACf;UACE,MAAMqD,aAAa,GAAGtE,mBAAmB,CAAC;YACxCuE,eAAe,EAAEV,cAAc;YAC/BI,aAAa;YACbC,YAAY;YACZ1D;UACF,CAAC,CAAC;UACF,IAAI8D,aAAa,KAAK,IAAI,EAAE;YAC1B1B,gBAAgB,CAAC0B,aAAa,EAAE7B,KAAK,CAAC;UACxC;UACA;QACF;MACF,KAAK,WAAW;QACd;UACE,MAAM+B,YAAY,GAAGzE,kBAAkB,CAAC;YACtCwE,eAAe,EAAEV,cAAc;YAC/BI,aAAa;YACbC,YAAY;YACZ1D;UACF,CAAC,CAAC;UACF,IAAIgE,YAAY,KAAK,IAAI,EAAE;YACzB5B,gBAAgB,CAAC4B,YAAY,EAAE/B,KAAK,CAAC;UACvC,CAAC,MAAM;YACLtC,MAAM,CAACsB,OAAO,CAACoB,0BAA0B,CAACM,MAAM,CAACrB,KAAK,EAAEW,KAAK,CAAC;UAChE;UACA;QACF;MACF,KAAK,SAAS;QACZ;UACED,UAAU,CAACqB,cAAc,EAAEpB,KAAK,CAAC;UACjC;QACF;MACF,KAAK,UAAU;QACb;UACE,IAAIsB,mBAAmB,KAAK,IAAI,IAAIC,kBAAkB,KAAK,IAAI,EAAE;YAC/DlD,QAAQ,CAAC+C,cAAc,EAAEZ,iBAAiB,CAACwB,IAAI,CAACC,GAAG,CAACX,mBAAmB,GAAGJ,gBAAgB,EAAEK,kBAAkB,CAAC,CAAC,CAAC;UACnH;UACA;QACF;MACF,KAAK,MAAM;QACT;UACEpB,gBAAgB,CAACqB,aAAa,EAAExB,KAAK,CAAC;UACtC;QACF;MACF,KAAK,KAAK;QACR;UACEG,gBAAgB,CAACsB,YAAY,EAAEzB,KAAK,CAAC;UACrC;QACF;MACF,KAAK,GAAG;QACN;UACE;UACA;QACF;MACF;QACE;UACE2B,oBAAoB,GAAG,KAAK;QAC9B;IACJ;IACA,IAAIA,oBAAoB,EAAE;MACxB3B,KAAK,CAACqC,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC3E,MAAM,EAAEM,eAAe,CAACW,MAAM,EAAEwB,gBAAgB,EAAEpC,KAAK,EAAEgC,UAAU,EAAE1B,QAAQ,EAAEmC,iBAAiB,CAAC,CAAC;EACtG,MAAMiC,8BAA8B,GAAGvG,KAAK,CAACoC,WAAW,CAAC,CAACoC,MAAM,EAAEV,KAAK,KAAK;IAC1E,MAAM0C,kBAAkB,GAAG3F,kCAAkC,CAACW,MAAM,CAAC;IACrE,IAAIgF,kBAAkB,KAAK,IAAI,EAAE;MAC/B;IACF;IACA,MAAM;MACJrD,KAAK,EAAEsD,YAAY;MACnBrC,KAAK,EAAEsC;IACT,CAAC,GAAGF,kBAAkB;IACtB,MAAM;MACJG,MAAM;MACNvC,KAAK;MACLwC;IACF,CAAC,GAAGpC,MAAM;IACV,MAAMQ,gBAAgB,GAAGxD,MAAM,CAACsB,OAAO,CAACmC,mBAAmB,CAAC,CAAC;IAC7D,MAAMW,eAAe,GAAGpE,MAAM,CAACsB,OAAO,CAACqC,cAAc,CAACsB,YAAY,CAAC;IACnE,MAAMvB,cAAc,GAAGuB,YAAY,GAAGjF,MAAM,CAACsB,OAAO,CAACqC,cAAc,CAACsB,YAAY,CAAC,GAAG,CAAC;IACrF,MAAMrB,mBAAmB,GAAG,CAAC;IAC7B,MAAMC,kBAAkB,GAAGvD,eAAe,CAACW,MAAM,GAAG,CAAC;IACrD,MAAM6C,aAAa,GAAG,CAAC;IACvB,MAAMC,YAAY,GAAGrF,oCAAoC,CAACsB,MAAM,CAAC,CAACiB,MAAM,GAAG,CAAC;IAC5E,IAAIgD,oBAAoB,GAAG,IAAI;IAC/B,QAAQ3B,KAAK,CAAC4B,GAAG;MACf,KAAK,WAAW;QACd;UACE,IAAItB,KAAK,KAAKwC,QAAQ,GAAG,CAAC,EAAE;YAC1B/C,UAAU,CAAC+B,eAAe,EAAE9B,KAAK,CAAC;UACpC,CAAC,MAAM;YACLK,eAAe,CAACyB,eAAe,EAAEc,YAAY,GAAG,CAAC,EAAE5C,KAAK,CAAC;UAC3D;UACA;QACF;MACF,KAAK,SAAS;QACZ;UACE,IAAIM,KAAK,GAAG,CAAC,EAAE;YACbD,eAAe,CAACyB,eAAe,EAAEc,YAAY,GAAG,CAAC,EAAE5C,KAAK,CAAC;UAC3D;UACA;QACF;MACF,KAAK,YAAY;QACf;UACE,MAAM+C,qBAAqB,GAAGF,MAAM,CAAClE,MAAM,GAAGkE,MAAM,CAACG,OAAO,CAACL,YAAY,CAAC,GAAG,CAAC;UAC9E,IAAIb,eAAe,GAAGiB,qBAAqB,GAAG,CAAC,IAAItB,YAAY,EAAE;YAC/DpB,eAAe,CAACyB,eAAe,GAAGiB,qBAAqB,GAAG,CAAC,EAAEH,YAAY,EAAE5C,KAAK,CAAC;UACnF;UACA;QACF;MACF,KAAK,WAAW;QACd;UACE,MAAMiD,oBAAoB,GAAGJ,MAAM,CAACG,OAAO,CAACL,YAAY,CAAC;UACzD,IAAIb,eAAe,GAAGmB,oBAAoB,GAAG,CAAC,IAAIzB,aAAa,EAAE;YAC/DnB,eAAe,CAACyB,eAAe,GAAGmB,oBAAoB,GAAG,CAAC,EAAEL,YAAY,EAAE5C,KAAK,CAAC;UAClF;UACA;QACF;MACF,KAAK,UAAU;QACb;UACE,IAAIsB,mBAAmB,KAAK,IAAI,IAAIC,kBAAkB,KAAK,IAAI,EAAE;YAC/DlD,QAAQ,CAAC+C,cAAc,EAAEZ,iBAAiB,CAACwB,IAAI,CAACC,GAAG,CAACX,mBAAmB,GAAGJ,gBAAgB,EAAEK,kBAAkB,CAAC,CAAC,CAAC;UACnH;UACA;QACF;MACF,KAAK,MAAM;QACT;UACElB,eAAe,CAACmB,aAAa,EAAEoB,YAAY,EAAE5C,KAAK,CAAC;UACnD;QACF;MACF,KAAK,KAAK;QACR;UACEK,eAAe,CAACoB,YAAY,EAAEmB,YAAY,EAAE5C,KAAK,CAAC;UAClD;QACF;MACF,KAAK,GAAG;QACN;UACE;UACA;QACF;MACF;QACE;UACE2B,oBAAoB,GAAG,KAAK;QAC9B;IACJ;IACA,IAAIA,oBAAoB,EAAE;MACxB3B,KAAK,CAACqC,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC3E,MAAM,EAAEM,eAAe,CAACW,MAAM,EAAEoB,UAAU,EAAEM,eAAe,EAAEhC,QAAQ,EAAEmC,iBAAiB,CAAC,CAAC;EAC9F,MAAM0C,iBAAiB,GAAGhH,KAAK,CAACoC,WAAW,CAAC,CAACoC,MAAM,EAAEV,KAAK,KAAK;IAC7D;IACA,IAAI5C,qBAAqB,CAAC4C,KAAK,CAAC,EAAE;MAChC;IACF;;IAEA;IACA,MAAMmD,UAAU,GAAGzF,MAAM,CAACsB,OAAO,CAACoE,aAAa,CAAC1C,MAAM,CAAChB,EAAE,EAAEgB,MAAM,CAACrB,KAAK,CAAC;IACxE,IAAI8D,UAAU,CAACE,QAAQ,KAAKzG,aAAa,CAAC0G,IAAI,IAAI,CAACzG,eAAe,CAACmD,KAAK,CAAC4B,GAAG,CAAC,EAAE;MAC7E;IACF;IACA,MAAM2B,cAAc,GAAG7F,MAAM,CAACsB,OAAO,CAACwE,4BAA4B,CAAC,gBAAgB,EAAE,IAAI,EAAE;MACzFxD,KAAK;MACLyD,IAAI,EAAEN;IACR,CAAC,CAAC;IACF,IAAI,CAACI,cAAc,EAAE;MACnB;IACF;IACA,IAAIvF,eAAe,CAACW,MAAM,KAAK,CAAC,EAAE;MAChC;IACF;IACA,MAAMuC,gBAAgB,GAAGxD,MAAM,CAACsB,OAAO,CAACmC,mBAAmB,CAAC,CAAC;IAC7D,MAAMC,cAAc,GAAGV,MAAM,CAACrB,KAAK,GAAG3B,MAAM,CAACsB,OAAO,CAACqC,cAAc,CAACX,MAAM,CAACrB,KAAK,CAAC,GAAG,CAAC;IACrF,MAAMqE,cAAc,GAAG1F,eAAe,CAACwB,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKgB,MAAM,CAAChB,EAAE,CAAC;IAC7E,MAAM4B,mBAAmB,GAAG,CAAC;IAC7B,MAAMC,kBAAkB,GAAGvD,eAAe,CAACW,MAAM,GAAG,CAAC;IACrD,MAAM6C,aAAa,GAAG,CAAC;IACvB,MAAMC,YAAY,GAAGrF,oCAAoC,CAACsB,MAAM,CAAC,CAACiB,MAAM,GAAG,CAAC;IAC5E,IAAIgD,oBAAoB,GAAG,IAAI;IAC/B,QAAQ3B,KAAK,CAAC4B,GAAG;MACf,KAAK,WAAW;QACd;UACE;UACA,IAAI8B,cAAc,GAAGnC,kBAAkB,EAAE;YACvClD,QAAQ,CAAC+C,cAAc,EAAEZ,iBAAiB,CAACkD,cAAc,GAAG,CAAC,CAAC,EAAE3F,KAAK,GAAG,OAAO,GAAG,MAAM,EAAE,MAAM,CAAC;UACnG;UACA;QACF;MACF,KAAK,SAAS;QACZ;UACE,IAAI2F,cAAc,GAAGpC,mBAAmB,EAAE;YACxCjD,QAAQ,CAAC+C,cAAc,EAAEZ,iBAAiB,CAACkD,cAAc,GAAG,CAAC,CAAC,CAAC;UACjE,CAAC,MAAM,IAAIxF,sBAAsB,EAAE;YACjCiC,gBAAgB,CAACiB,cAAc,EAAEpB,KAAK,CAAC;UACzC,CAAC,MAAM;YACLD,UAAU,CAACqB,cAAc,EAAEpB,KAAK,CAAC;UACnC;UACA;QACF;MACF,KAAK,YAAY;QACf;UACE,MAAM6B,aAAa,GAAGtE,mBAAmB,CAAC;YACxCuE,eAAe,EAAEV,cAAc;YAC/BI,aAAa;YACbC,YAAY;YACZ1D;UACF,CAAC,CAAC;UACF,IAAI8D,aAAa,KAAK,IAAI,EAAE;YAC1BxD,QAAQ,CAACwD,aAAa,EAAErB,iBAAiB,CAACkD,cAAc,CAAC,EAAE3F,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;UACtF;UACA;QACF;MACF,KAAK,WAAW;QACd;UACE,MAAMgE,YAAY,GAAGzE,kBAAkB,CAAC;YACtCwE,eAAe,EAAEV,cAAc;YAC/BI,aAAa;YACbC,YAAY;YACZ1D;UACF,CAAC,CAAC;UACF,IAAIgE,YAAY,KAAK,IAAI,EAAE;YACzB1D,QAAQ,CAAC0D,YAAY,EAAEvB,iBAAiB,CAACkD,cAAc,CAAC,EAAE3F,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;UACrF;UACA;QACF;MACF,KAAK,KAAK;QACR;UACE;UACA,IAAIiC,KAAK,CAAC2D,QAAQ,IAAIvC,cAAc,GAAGI,aAAa,EAAE;YACpDnD,QAAQ,CAAC+C,cAAc,GAAG,CAAC,EAAEZ,iBAAiB,CAACkD,cAAc,CAAC,EAAE,MAAM,CAAC;UACzE,CAAC,MAAM,IAAI,CAAC1D,KAAK,CAAC2D,QAAQ,IAAIvC,cAAc,GAAGK,YAAY,EAAE;YAC3DpD,QAAQ,CAAC+C,cAAc,GAAG,CAAC,EAAEZ,iBAAiB,CAACkD,cAAc,CAAC,EAAE,OAAO,CAAC;UAC1E;UACA;QACF;MACF,KAAK,GAAG;QACN;UACE,MAAMrE,KAAK,GAAGqB,MAAM,CAACrB,KAAK;UAC1B,IAAIA,KAAK,KAAKvC,8BAA8B,EAAE;YAC5C;UACF;UACA,MAAM8G,MAAM,GAAGlD,MAAM,CAACkD,MAAM;UAC5B,IAAIA,MAAM;UACV;UACAA,MAAM,CAACvE,KAAK,KAAK,qBAAqB,EAAE;YACtC;UACF;UACA,IAAI,CAACW,KAAK,CAAC2D,QAAQ,IAAID,cAAc,GAAGnC,kBAAkB,EAAE;YAC1DlD,QAAQ,CAAC+C,cAAc,EAAEZ,iBAAiB,CAACwB,IAAI,CAACC,GAAG,CAACyB,cAAc,GAAGxC,gBAAgB,EAAEK,kBAAkB,CAAC,CAAC,CAAC;UAC9G;UACA;QACF;MACF,KAAK,UAAU;QACb;UACE,IAAImC,cAAc,GAAGnC,kBAAkB,EAAE;YACvClD,QAAQ,CAAC+C,cAAc,EAAEZ,iBAAiB,CAACwB,IAAI,CAACC,GAAG,CAACyB,cAAc,GAAGxC,gBAAgB,EAAEK,kBAAkB,CAAC,CAAC,CAAC;UAC9G;UACA;QACF;MACF,KAAK,QAAQ;QACX;UACE;UACA,MAAMsC,YAAY,GAAG7B,IAAI,CAAC8B,GAAG,CAACJ,cAAc,GAAGxC,gBAAgB,EAAEI,mBAAmB,CAAC;UACrF,IAAIuC,YAAY,KAAKH,cAAc,IAAIG,YAAY,IAAIvC,mBAAmB,EAAE;YAC1EjD,QAAQ,CAAC+C,cAAc,EAAEZ,iBAAiB,CAACqD,YAAY,CAAC,CAAC;UAC3D,CAAC,MAAM;YACL9D,UAAU,CAACqB,cAAc,EAAEpB,KAAK,CAAC;UACnC;UACA;QACF;MACF,KAAK,MAAM;QACT;UACE,IAAIA,KAAK,CAACkC,OAAO,IAAIlC,KAAK,CAACmC,OAAO,IAAInC,KAAK,CAAC2D,QAAQ,EAAE;YACpDtF,QAAQ,CAACmD,aAAa,EAAEhB,iBAAiB,CAACc,mBAAmB,CAAC,CAAC;UACjE,CAAC,MAAM;YACLjD,QAAQ,CAACmD,aAAa,EAAEhB,iBAAiB,CAACkD,cAAc,CAAC,CAAC;UAC5D;UACA;QACF;MACF,KAAK,KAAK;QACR;UACE,IAAI1D,KAAK,CAACkC,OAAO,IAAIlC,KAAK,CAACmC,OAAO,IAAInC,KAAK,CAAC2D,QAAQ,EAAE;YACpDtF,QAAQ,CAACoD,YAAY,EAAEjB,iBAAiB,CAACe,kBAAkB,CAAC,CAAC;UAC/D,CAAC,MAAM;YACLlD,QAAQ,CAACoD,YAAY,EAAEjB,iBAAiB,CAACkD,cAAc,CAAC,CAAC;UAC3D;UACA;QACF;MACF;QACE;UACE/B,oBAAoB,GAAG,KAAK;QAC9B;IACJ;IACA,IAAIA,oBAAoB,EAAE;MACxB3B,KAAK,CAACqC,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC3E,MAAM,EAAEM,eAAe,EAAED,KAAK,EAAEM,QAAQ,EAAEmC,iBAAiB,EAAEtC,sBAAsB,EAAEiC,gBAAgB,EAAEJ,UAAU,CAAC,CAAC;EACvH,MAAMgE,sBAAsB,GAAG7H,KAAK,CAACoC,WAAW,CAAC,CAAC0F,YAAY,EAAAC,IAAA,KAExD;IAAA,IAF0D;MAC9DjE;IACF,CAAC,GAAAiE,IAAA;IACC,IAAIjE,KAAK,CAAC4B,GAAG,KAAK,GAAG,EAAE;MACrB;MACA,OAAO,KAAK;IACd;IACA,OAAOoC,YAAY;EACrB,CAAC,EAAE,EAAE,CAAC;EACN7G,4BAA4B,CAACO,MAAM,EAAE,iBAAiB,EAAEqG,sBAAsB,CAAC;EAC/ExH,sBAAsB,CAACmB,MAAM,EAAE,qBAAqB,EAAE+C,yBAAyB,CAAC;EAChFlE,sBAAsB,CAACmB,MAAM,EAAE,qBAAqB,EAAE4E,yBAAyB,CAAC;EAChF/F,sBAAsB,CAACmB,MAAM,EAAE,0BAA0B,EAAE+E,8BAA8B,CAAC;EAC1FlG,sBAAsB,CAACmB,MAAM,EAAE,aAAa,EAAEwF,iBAAiB,CAAC;AAClE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}