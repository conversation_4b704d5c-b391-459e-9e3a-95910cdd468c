{"ast": null, "code": "export function getPublicApiRef(apiRef) {\n  return {\n    current: apiRef.current.getPublicApi()\n  };\n}", "map": {"version": 3, "names": ["getPublicApiRef", "apiRef", "current", "getPublicApi"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/utils/getPublicApiRef.js"], "sourcesContent": ["export function getPublicApiRef(apiRef) {\n  return {\n    current: apiRef.current.getPublicApi()\n  };\n}"], "mappings": "AAAA,OAAO,SAASA,eAAeA,CAACC,MAAM,EAAE;EACtC,OAAO;IACLC,OAAO,EAAED,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC;EACvC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}