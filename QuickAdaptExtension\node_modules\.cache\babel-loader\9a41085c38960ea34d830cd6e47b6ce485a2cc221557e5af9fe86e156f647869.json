{"ast": null, "code": "import * as React from 'react';\nimport { localStorageAvailable } from \"../../utils/utils.js\";\nimport { useGridApiMethod } from \"../utils/index.js\";\nconst forceDebug = localStorageAvailable() && window.localStorage.getItem('DEBUG') != null;\nconst noop = () => {};\nconst noopLogger = {\n  debug: noop,\n  info: noop,\n  warn: noop,\n  error: noop\n};\nconst LOG_LEVELS = ['debug', 'info', 'warn', 'error'];\nfunction getAppender(name, logLevel) {\n  let appender = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : console;\n  const minLogLevelIdx = LOG_LEVELS.indexOf(logLevel);\n  if (minLogLevelIdx === -1) {\n    throw new Error(`MUI X: Log level ${logLevel} not recognized.`);\n  }\n  const logger = LOG_LEVELS.reduce((loggerObj, method, idx) => {\n    if (idx >= minLogLevelIdx) {\n      loggerObj[method] = function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        const [message, ...other] = args;\n        appender[method](`MUI X: ${name} - ${message}`, ...other);\n      };\n    } else {\n      loggerObj[method] = noop;\n    }\n    return loggerObj;\n  }, {});\n  return logger;\n}\nexport const useGridLoggerFactory = (apiRef, props) => {\n  const getLogger = React.useCallback(name => {\n    if (forceDebug) {\n      return getAppender(name, 'debug', props.logger);\n    }\n    if (!props.logLevel) {\n      return noopLogger;\n    }\n    return getAppender(name, props.logLevel.toString(), props.logger);\n  }, [props.logLevel, props.logger]);\n  useGridApiMethod(apiRef, {\n    getLogger\n  }, 'private');\n};", "map": {"version": 3, "names": ["React", "localStorageAvailable", "useGridApiMethod", "forceDebug", "window", "localStorage", "getItem", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "debug", "info", "warn", "error", "LOG_LEVELS", "get<PERSON><PERSON><PERSON>", "name", "logLevel", "appender", "arguments", "length", "undefined", "console", "minLogLevelIdx", "indexOf", "Error", "logger", "reduce", "loggerObj", "method", "idx", "_len", "args", "Array", "_key", "message", "other", "useGridLoggerFactory", "apiRef", "props", "<PERSON><PERSON><PERSON><PERSON>", "useCallback", "toString"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/core/useGridLoggerFactory.js"], "sourcesContent": ["import * as React from 'react';\nimport { localStorageAvailable } from \"../../utils/utils.js\";\nimport { useGridApiMethod } from \"../utils/index.js\";\nconst forceDebug = localStorageAvailable() && window.localStorage.getItem('DEBUG') != null;\nconst noop = () => {};\nconst noopLogger = {\n  debug: noop,\n  info: noop,\n  warn: noop,\n  error: noop\n};\nconst LOG_LEVELS = ['debug', 'info', 'warn', 'error'];\nfunction getAppender(name, logLevel, appender = console) {\n  const minLogLevelIdx = LOG_LEVELS.indexOf(logLevel);\n  if (minLogLevelIdx === -1) {\n    throw new Error(`MUI X: Log level ${logLevel} not recognized.`);\n  }\n  const logger = LOG_LEVELS.reduce((loggerObj, method, idx) => {\n    if (idx >= minLogLevelIdx) {\n      loggerObj[method] = (...args) => {\n        const [message, ...other] = args;\n        appender[method](`MUI X: ${name} - ${message}`, ...other);\n      };\n    } else {\n      loggerObj[method] = noop;\n    }\n    return loggerObj;\n  }, {});\n  return logger;\n}\nexport const useGridLoggerFactory = (apiRef, props) => {\n  const getLogger = React.useCallback(name => {\n    if (forceDebug) {\n      return getAppender(name, 'debug', props.logger);\n    }\n    if (!props.logLevel) {\n      return noopLogger;\n    }\n    return getAppender(name, props.logLevel.toString(), props.logger);\n  }, [props.logLevel, props.logger]);\n  useGridApiMethod(apiRef, {\n    getLogger\n  }, 'private');\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,sBAAsB;AAC5D,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,MAAMC,UAAU,GAAGF,qBAAqB,CAAC,CAAC,IAAIG,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI;AAC1F,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB,MAAMC,UAAU,GAAG;EACjBC,KAAK,EAAEF,IAAI;EACXG,IAAI,EAAEH,IAAI;EACVI,IAAI,EAAEJ,IAAI;EACVK,KAAK,EAAEL;AACT,CAAC;AACD,MAAMM,UAAU,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACrD,SAASC,WAAWA,CAACC,IAAI,EAAEC,QAAQ,EAAsB;EAAA,IAApBC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,OAAO;EACrD,MAAMC,cAAc,GAAGT,UAAU,CAACU,OAAO,CAACP,QAAQ,CAAC;EACnD,IAAIM,cAAc,KAAK,CAAC,CAAC,EAAE;IACzB,MAAM,IAAIE,KAAK,CAAC,oBAAoBR,QAAQ,kBAAkB,CAAC;EACjE;EACA,MAAMS,MAAM,GAAGZ,UAAU,CAACa,MAAM,CAAC,CAACC,SAAS,EAAEC,MAAM,EAAEC,GAAG,KAAK;IAC3D,IAAIA,GAAG,IAAIP,cAAc,EAAE;MACzBK,SAAS,CAACC,MAAM,CAAC,GAAG,YAAa;QAAA,SAAAE,IAAA,GAAAZ,SAAA,CAAAC,MAAA,EAATY,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;UAAJF,IAAI,CAAAE,IAAA,IAAAf,SAAA,CAAAe,IAAA;QAAA;QAC1B,MAAM,CAACC,OAAO,EAAE,GAAGC,KAAK,CAAC,GAAGJ,IAAI;QAChCd,QAAQ,CAACW,MAAM,CAAC,CAAC,UAAUb,IAAI,MAAMmB,OAAO,EAAE,EAAE,GAAGC,KAAK,CAAC;MAC3D,CAAC;IACH,CAAC,MAAM;MACLR,SAAS,CAACC,MAAM,CAAC,GAAGrB,IAAI;IAC1B;IACA,OAAOoB,SAAS;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOF,MAAM;AACf;AACA,OAAO,MAAMW,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACrD,MAAMC,SAAS,GAAGvC,KAAK,CAACwC,WAAW,CAACzB,IAAI,IAAI;IAC1C,IAAIZ,UAAU,EAAE;MACd,OAAOW,WAAW,CAACC,IAAI,EAAE,OAAO,EAAEuB,KAAK,CAACb,MAAM,CAAC;IACjD;IACA,IAAI,CAACa,KAAK,CAACtB,QAAQ,EAAE;MACnB,OAAOR,UAAU;IACnB;IACA,OAAOM,WAAW,CAACC,IAAI,EAAEuB,KAAK,CAACtB,QAAQ,CAACyB,QAAQ,CAAC,CAAC,EAAEH,KAAK,CAACb,MAAM,CAAC;EACnE,CAAC,EAAE,CAACa,KAAK,CAACtB,QAAQ,EAAEsB,KAAK,CAACb,MAAM,CAAC,CAAC;EAClCvB,gBAAgB,CAACmC,MAAM,EAAE;IACvBE;EACF,CAAC,EAAE,SAAS,CAAC;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}