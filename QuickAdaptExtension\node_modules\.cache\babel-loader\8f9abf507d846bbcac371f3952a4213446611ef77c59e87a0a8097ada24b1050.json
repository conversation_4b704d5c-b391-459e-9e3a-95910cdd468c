{"ast": null, "code": "import * as React from 'react';\nimport { EventManager } from '@mui/x-internals/EventManager';\nimport { Store } from \"../../utils/Store.js\";\nimport { useGridApiMethod } from \"../utils/useGridApiMethod.js\";\nimport { GridSignature } from \"../utils/useGridApiEventHandler.js\";\nconst SYMBOL_API_PRIVATE = Symbol('mui.api_private');\nconst isSyntheticEvent = event => {\n  return event.isPropagationStopped !== undefined;\n};\nexport function unwrapPrivateAPI(publicApi) {\n  return publicApi[SYMBOL_API_PRIVATE];\n}\nlet globalId = 0;\nfunction createPrivateAPI(publicApiRef) {\n  const existingPrivateApi = publicApiRef.current?.[SYMBOL_API_PRIVATE];\n  if (existingPrivateApi) {\n    return existingPrivateApi;\n  }\n  const state = {};\n  const privateApi = {\n    state,\n    store: Store.create(state),\n    instanceId: {\n      id: globalId\n    }\n  };\n  globalId += 1;\n  privateApi.getPublicApi = () => publicApiRef.current;\n  privateApi.register = (visibility, methods) => {\n    Object.keys(methods).forEach(methodName => {\n      const method = methods[methodName];\n      const currentPrivateMethod = privateApi[methodName];\n      if (currentPrivateMethod?.spying === true) {\n        currentPrivateMethod.target = method;\n      } else {\n        privateApi[methodName] = method;\n      }\n      if (visibility === 'public') {\n        const publicApi = publicApiRef.current;\n        const currentPublicMethod = publicApi[methodName];\n        if (currentPublicMethod?.spying === true) {\n          currentPublicMethod.target = method;\n        } else {\n          publicApi[methodName] = method;\n        }\n      }\n    });\n  };\n  privateApi.register('private', {\n    caches: {},\n    eventManager: new EventManager()\n  });\n  return privateApi;\n}\nfunction createPublicAPI(privateApiRef) {\n  const publicApi = {\n    get state() {\n      return privateApiRef.current.state;\n    },\n    get store() {\n      return privateApiRef.current.store;\n    },\n    get instanceId() {\n      return privateApiRef.current.instanceId;\n    },\n    [SYMBOL_API_PRIVATE]: privateApiRef.current\n  };\n  return publicApi;\n}\nexport function useGridApiInitialization(inputApiRef, props) {\n  const publicApiRef = React.useRef();\n  const privateApiRef = React.useRef();\n  if (!privateApiRef.current) {\n    privateApiRef.current = createPrivateAPI(publicApiRef);\n  }\n  if (!publicApiRef.current) {\n    publicApiRef.current = createPublicAPI(privateApiRef);\n  }\n  const publishEvent = React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    const [name, params, event = {}] = args;\n    event.defaultMuiPrevented = false;\n    if (isSyntheticEvent(event) && event.isPropagationStopped()) {\n      return;\n    }\n    const details = props.signature === GridSignature.DataGridPro || props.signature === GridSignature.DataGridPremium ? {\n      api: privateApiRef.current.getPublicApi()\n    } : {};\n    privateApiRef.current.eventManager.emit(name, params, event, details);\n  }, [privateApiRef, props.signature]);\n  const subscribeEvent = React.useCallback((event, handler, options) => {\n    privateApiRef.current.eventManager.on(event, handler, options);\n    const api = privateApiRef.current;\n    return () => {\n      api.eventManager.removeListener(event, handler);\n    };\n  }, [privateApiRef]);\n  useGridApiMethod(privateApiRef, {\n    subscribeEvent,\n    publishEvent\n  }, 'public');\n  if (inputApiRef && !inputApiRef.current?.state) {\n    inputApiRef.current = publicApiRef.current;\n  }\n  React.useImperativeHandle(inputApiRef, () => publicApiRef.current, [publicApiRef]);\n  React.useEffect(() => {\n    const api = privateApiRef.current;\n    return () => {\n      api.publishEvent('unmount');\n    };\n  }, [privateApiRef]);\n  return privateApiRef;\n}", "map": {"version": 3, "names": ["React", "EventManager", "Store", "useGridApiMethod", "GridSignature", "SYMBOL_API_PRIVATE", "Symbol", "isSyntheticEvent", "event", "isPropagationStopped", "undefined", "unwrapPrivateAPI", "publicApi", "globalId", "createPrivateAPI", "publicApiRef", "existingPrivateApi", "current", "state", "privateApi", "store", "create", "instanceId", "id", "getPublicApi", "register", "visibility", "methods", "Object", "keys", "for<PERSON>ach", "methodName", "method", "currentPrivateMethod", "spying", "target", "currentPublicMethod", "caches", "eventManager", "createPublicAPI", "privateApiRef", "useGridApiInitialization", "inputApiRef", "props", "useRef", "publishEvent", "useCallback", "_len", "arguments", "length", "args", "Array", "_key", "name", "params", "defaultMuiPrevented", "details", "signature", "DataGridPro", "DataGridPremium", "api", "emit", "subscribeEvent", "handler", "options", "on", "removeListener", "useImperativeHandle", "useEffect"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/core/useGridApiInitialization.js"], "sourcesContent": ["import * as React from 'react';\nimport { EventManager } from '@mui/x-internals/EventManager';\nimport { Store } from \"../../utils/Store.js\";\nimport { useGridApiMethod } from \"../utils/useGridApiMethod.js\";\nimport { GridSignature } from \"../utils/useGridApiEventHandler.js\";\nconst SYMBOL_API_PRIVATE = Symbol('mui.api_private');\nconst isSyntheticEvent = event => {\n  return event.isPropagationStopped !== undefined;\n};\nexport function unwrapPrivateAPI(publicApi) {\n  return publicApi[SYMBOL_API_PRIVATE];\n}\nlet globalId = 0;\nfunction createPrivateAPI(publicApiRef) {\n  const existingPrivateApi = publicApiRef.current?.[SYMBOL_API_PRIVATE];\n  if (existingPrivateApi) {\n    return existingPrivateApi;\n  }\n  const state = {};\n  const privateApi = {\n    state,\n    store: Store.create(state),\n    instanceId: {\n      id: globalId\n    }\n  };\n  globalId += 1;\n  privateApi.getPublicApi = () => publicApiRef.current;\n  privateApi.register = (visibility, methods) => {\n    Object.keys(methods).forEach(methodName => {\n      const method = methods[methodName];\n      const currentPrivateMethod = privateApi[methodName];\n      if (currentPrivateMethod?.spying === true) {\n        currentPrivateMethod.target = method;\n      } else {\n        privateApi[methodName] = method;\n      }\n      if (visibility === 'public') {\n        const publicApi = publicApiRef.current;\n        const currentPublicMethod = publicApi[methodName];\n        if (currentPublicMethod?.spying === true) {\n          currentPublicMethod.target = method;\n        } else {\n          publicApi[methodName] = method;\n        }\n      }\n    });\n  };\n  privateApi.register('private', {\n    caches: {},\n    eventManager: new EventManager()\n  });\n  return privateApi;\n}\nfunction createPublicAPI(privateApiRef) {\n  const publicApi = {\n    get state() {\n      return privateApiRef.current.state;\n    },\n    get store() {\n      return privateApiRef.current.store;\n    },\n    get instanceId() {\n      return privateApiRef.current.instanceId;\n    },\n    [SYMBOL_API_PRIVATE]: privateApiRef.current\n  };\n  return publicApi;\n}\nexport function useGridApiInitialization(inputApiRef, props) {\n  const publicApiRef = React.useRef();\n  const privateApiRef = React.useRef();\n  if (!privateApiRef.current) {\n    privateApiRef.current = createPrivateAPI(publicApiRef);\n  }\n  if (!publicApiRef.current) {\n    publicApiRef.current = createPublicAPI(privateApiRef);\n  }\n  const publishEvent = React.useCallback((...args) => {\n    const [name, params, event = {}] = args;\n    event.defaultMuiPrevented = false;\n    if (isSyntheticEvent(event) && event.isPropagationStopped()) {\n      return;\n    }\n    const details = props.signature === GridSignature.DataGridPro || props.signature === GridSignature.DataGridPremium ? {\n      api: privateApiRef.current.getPublicApi()\n    } : {};\n    privateApiRef.current.eventManager.emit(name, params, event, details);\n  }, [privateApiRef, props.signature]);\n  const subscribeEvent = React.useCallback((event, handler, options) => {\n    privateApiRef.current.eventManager.on(event, handler, options);\n    const api = privateApiRef.current;\n    return () => {\n      api.eventManager.removeListener(event, handler);\n    };\n  }, [privateApiRef]);\n  useGridApiMethod(privateApiRef, {\n    subscribeEvent,\n    publishEvent\n  }, 'public');\n  if (inputApiRef && !inputApiRef.current?.state) {\n    inputApiRef.current = publicApiRef.current;\n  }\n  React.useImperativeHandle(inputApiRef, () => publicApiRef.current, [publicApiRef]);\n  React.useEffect(() => {\n    const api = privateApiRef.current;\n    return () => {\n      api.publishEvent('unmount');\n    };\n  }, [privateApiRef]);\n  return privateApiRef;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,QAAQ,oCAAoC;AAClE,MAAMC,kBAAkB,GAAGC,MAAM,CAAC,iBAAiB,CAAC;AACpD,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,OAAOA,KAAK,CAACC,oBAAoB,KAAKC,SAAS;AACjD,CAAC;AACD,OAAO,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EAC1C,OAAOA,SAAS,CAACP,kBAAkB,CAAC;AACtC;AACA,IAAIQ,QAAQ,GAAG,CAAC;AAChB,SAASC,gBAAgBA,CAACC,YAAY,EAAE;EACtC,MAAMC,kBAAkB,GAAGD,YAAY,CAACE,OAAO,GAAGZ,kBAAkB,CAAC;EACrE,IAAIW,kBAAkB,EAAE;IACtB,OAAOA,kBAAkB;EAC3B;EACA,MAAME,KAAK,GAAG,CAAC,CAAC;EAChB,MAAMC,UAAU,GAAG;IACjBD,KAAK;IACLE,KAAK,EAAElB,KAAK,CAACmB,MAAM,CAACH,KAAK,CAAC;IAC1BI,UAAU,EAAE;MACVC,EAAE,EAAEV;IACN;EACF,CAAC;EACDA,QAAQ,IAAI,CAAC;EACbM,UAAU,CAACK,YAAY,GAAG,MAAMT,YAAY,CAACE,OAAO;EACpDE,UAAU,CAACM,QAAQ,GAAG,CAACC,UAAU,EAAEC,OAAO,KAAK;IAC7CC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,CAACC,UAAU,IAAI;MACzC,MAAMC,MAAM,GAAGL,OAAO,CAACI,UAAU,CAAC;MAClC,MAAME,oBAAoB,GAAGd,UAAU,CAACY,UAAU,CAAC;MACnD,IAAIE,oBAAoB,EAAEC,MAAM,KAAK,IAAI,EAAE;QACzCD,oBAAoB,CAACE,MAAM,GAAGH,MAAM;MACtC,CAAC,MAAM;QACLb,UAAU,CAACY,UAAU,CAAC,GAAGC,MAAM;MACjC;MACA,IAAIN,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAMd,SAAS,GAAGG,YAAY,CAACE,OAAO;QACtC,MAAMmB,mBAAmB,GAAGxB,SAAS,CAACmB,UAAU,CAAC;QACjD,IAAIK,mBAAmB,EAAEF,MAAM,KAAK,IAAI,EAAE;UACxCE,mBAAmB,CAACD,MAAM,GAAGH,MAAM;QACrC,CAAC,MAAM;UACLpB,SAAS,CAACmB,UAAU,CAAC,GAAGC,MAAM;QAChC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACDb,UAAU,CAACM,QAAQ,CAAC,SAAS,EAAE;IAC7BY,MAAM,EAAE,CAAC,CAAC;IACVC,YAAY,EAAE,IAAIrC,YAAY,CAAC;EACjC,CAAC,CAAC;EACF,OAAOkB,UAAU;AACnB;AACA,SAASoB,eAAeA,CAACC,aAAa,EAAE;EACtC,MAAM5B,SAAS,GAAG;IAChB,IAAIM,KAAKA,CAAA,EAAG;MACV,OAAOsB,aAAa,CAACvB,OAAO,CAACC,KAAK;IACpC,CAAC;IACD,IAAIE,KAAKA,CAAA,EAAG;MACV,OAAOoB,aAAa,CAACvB,OAAO,CAACG,KAAK;IACpC,CAAC;IACD,IAAIE,UAAUA,CAAA,EAAG;MACf,OAAOkB,aAAa,CAACvB,OAAO,CAACK,UAAU;IACzC,CAAC;IACD,CAACjB,kBAAkB,GAAGmC,aAAa,CAACvB;EACtC,CAAC;EACD,OAAOL,SAAS;AAClB;AACA,OAAO,SAAS6B,wBAAwBA,CAACC,WAAW,EAAEC,KAAK,EAAE;EAC3D,MAAM5B,YAAY,GAAGf,KAAK,CAAC4C,MAAM,CAAC,CAAC;EACnC,MAAMJ,aAAa,GAAGxC,KAAK,CAAC4C,MAAM,CAAC,CAAC;EACpC,IAAI,CAACJ,aAAa,CAACvB,OAAO,EAAE;IAC1BuB,aAAa,CAACvB,OAAO,GAAGH,gBAAgB,CAACC,YAAY,CAAC;EACxD;EACA,IAAI,CAACA,YAAY,CAACE,OAAO,EAAE;IACzBF,YAAY,CAACE,OAAO,GAAGsB,eAAe,CAACC,aAAa,CAAC;EACvD;EACA,MAAMK,YAAY,GAAG7C,KAAK,CAAC8C,WAAW,CAAC,YAAa;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAC7C,MAAM,CAACC,IAAI,EAAEC,MAAM,EAAE9C,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG0C,IAAI;IACvC1C,KAAK,CAAC+C,mBAAmB,GAAG,KAAK;IACjC,IAAIhD,gBAAgB,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACC,oBAAoB,CAAC,CAAC,EAAE;MAC3D;IACF;IACA,MAAM+C,OAAO,GAAGb,KAAK,CAACc,SAAS,KAAKrD,aAAa,CAACsD,WAAW,IAAIf,KAAK,CAACc,SAAS,KAAKrD,aAAa,CAACuD,eAAe,GAAG;MACnHC,GAAG,EAAEpB,aAAa,CAACvB,OAAO,CAACO,YAAY,CAAC;IAC1C,CAAC,GAAG,CAAC,CAAC;IACNgB,aAAa,CAACvB,OAAO,CAACqB,YAAY,CAACuB,IAAI,CAACR,IAAI,EAAEC,MAAM,EAAE9C,KAAK,EAAEgD,OAAO,CAAC;EACvE,CAAC,EAAE,CAAChB,aAAa,EAAEG,KAAK,CAACc,SAAS,CAAC,CAAC;EACpC,MAAMK,cAAc,GAAG9D,KAAK,CAAC8C,WAAW,CAAC,CAACtC,KAAK,EAAEuD,OAAO,EAAEC,OAAO,KAAK;IACpExB,aAAa,CAACvB,OAAO,CAACqB,YAAY,CAAC2B,EAAE,CAACzD,KAAK,EAAEuD,OAAO,EAAEC,OAAO,CAAC;IAC9D,MAAMJ,GAAG,GAAGpB,aAAa,CAACvB,OAAO;IACjC,OAAO,MAAM;MACX2C,GAAG,CAACtB,YAAY,CAAC4B,cAAc,CAAC1D,KAAK,EAAEuD,OAAO,CAAC;IACjD,CAAC;EACH,CAAC,EAAE,CAACvB,aAAa,CAAC,CAAC;EACnBrC,gBAAgB,CAACqC,aAAa,EAAE;IAC9BsB,cAAc;IACdjB;EACF,CAAC,EAAE,QAAQ,CAAC;EACZ,IAAIH,WAAW,IAAI,CAACA,WAAW,CAACzB,OAAO,EAAEC,KAAK,EAAE;IAC9CwB,WAAW,CAACzB,OAAO,GAAGF,YAAY,CAACE,OAAO;EAC5C;EACAjB,KAAK,CAACmE,mBAAmB,CAACzB,WAAW,EAAE,MAAM3B,YAAY,CAACE,OAAO,EAAE,CAACF,YAAY,CAAC,CAAC;EAClFf,KAAK,CAACoE,SAAS,CAAC,MAAM;IACpB,MAAMR,GAAG,GAAGpB,aAAa,CAACvB,OAAO;IACjC,OAAO,MAAM;MACX2C,GAAG,CAACf,YAAY,CAAC,SAAS,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,CAACL,aAAa,CAAC,CAAC;EACnB,OAAOA,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}