{"ast": null, "code": "/**\n * Creates a base function for methods like `_.forIn` and `_.forOwn`.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseFor(fromRight) {\n  return function (object, iteratee, keysFunc) {\n    var index = -1,\n      iterable = Object(object),\n      props = keysFunc(object),\n      length = props.length;\n    while (length--) {\n      var key = props[fromRight ? length : ++index];\n      if (iteratee(iterable[key], key, iterable) === false) {\n        break;\n      }\n    }\n    return object;\n  };\n}\nmodule.exports = createBaseFor;", "map": {"version": 3, "names": ["createBaseFor", "fromRight", "object", "iteratee", "keysFunc", "index", "iterable", "Object", "props", "length", "key", "module", "exports"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash/_createBaseFor.js"], "sourcesContent": ["/**\n * Creates a base function for methods like `_.forIn` and `_.forOwn`.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseFor(fromRight) {\n  return function(object, iteratee, keysFunc) {\n    var index = -1,\n        iterable = Object(object),\n        props = keysFunc(object),\n        length = props.length;\n\n    while (length--) {\n      var key = props[fromRight ? length : ++index];\n      if (iteratee(iterable[key], key, iterable) === false) {\n        break;\n      }\n    }\n    return object;\n  };\n}\n\nmodule.exports = createBaseFor;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAaA,CAACC,SAAS,EAAE;EAChC,OAAO,UAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IAC1C,IAAIC,KAAK,GAAG,CAAC,CAAC;MACVC,QAAQ,GAAGC,MAAM,CAACL,MAAM,CAAC;MACzBM,KAAK,GAAGJ,QAAQ,CAACF,MAAM,CAAC;MACxBO,MAAM,GAAGD,KAAK,CAACC,MAAM;IAEzB,OAAOA,MAAM,EAAE,EAAE;MACf,IAAIC,GAAG,GAAGF,KAAK,CAACP,SAAS,GAAGQ,MAAM,GAAG,EAAEJ,KAAK,CAAC;MAC7C,IAAIF,QAAQ,CAACG,QAAQ,CAACI,GAAG,CAAC,EAAEA,GAAG,EAAEJ,QAAQ,CAAC,KAAK,KAAK,EAAE;QACpD;MACF;IACF;IACA,OAAOJ,MAAM;EACf,CAAC;AACH;AAEAS,MAAM,CAACC,OAAO,GAAGZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}