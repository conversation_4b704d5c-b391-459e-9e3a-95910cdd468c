{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\QADPT Bugs DEV\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\GuidesPreview\\\\HotspotPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, Typography } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\n\n// Viewport boundary detection utilities\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Utility function to get viewport bounds\nconst getViewportBounds = () => ({\n  top: 0,\n  left: 0,\n  right: window.innerWidth,\n  bottom: window.innerHeight\n});\n\n// Utility function to calculate available space around an element\nconst getAvailableSpace = elementRect => {\n  const viewport = getViewportBounds();\n  return {\n    top: elementRect.top - viewport.top,\n    bottom: viewport.bottom - elementRect.bottom,\n    left: elementRect.left - viewport.left,\n    right: viewport.right - elementRect.right\n  };\n};\n\n// Utility function to determine optimal placement based on available space\nconst calculateOptimalPlacement = (elementRect, popupDimensions) => {\n  const space = getAvailableSpace(elementRect);\n  const {\n    width,\n    height\n  } = popupDimensions;\n\n  // Add some padding for better UX\n  const padding = 20;\n\n  // Check if popup fits in each direction\n  const fitsTop = space.top >= height + padding;\n  const fitsBottom = space.bottom >= height + padding;\n  const fitsLeft = space.left >= width + padding;\n  const fitsRight = space.right >= width + padding;\n\n  // Prefer bottom first (most common), then top, then sides\n  if (fitsBottom) return 'bottom';\n  if (fitsTop) return 'top';\n  if (fitsRight) return 'right';\n  if (fitsLeft) return 'left';\n\n  // If nothing fits perfectly, choose the side with most space\n  const maxSpace = Math.max(space.top, space.bottom, space.left, space.right);\n  if (maxSpace === space.bottom) return 'bottom';\n  if (maxSpace === space.top) return 'top';\n  if (maxSpace === space.right) return 'right';\n  return 'left';\n};\n\n// Utility function to calculate popup position with boundary detection\nconst calculatePopupPositionWithBounds = (elementRect, popupDimensions, placement, hotspotOffset = {\n  x: 0,\n  y: 0\n}, hotspotSize = 30) => {\n  const viewport = getViewportBounds();\n  const padding = 10;\n  const beaconSpacing = 10; // Exact spacing from beacon edge\n\n  let top;\n  let left;\n  let anchorOrigin;\n  let transformOrigin;\n\n  // Calculate hotspot center position\n  const hotspotCenterX = elementRect.left + hotspotOffset.x + hotspotSize / 2;\n  const hotspotCenterY = elementRect.top + hotspotOffset.y + hotspotSize / 2;\n  switch (placement) {\n    case 'top':\n      top = hotspotCenterY - hotspotSize / 2 - popupDimensions.height - beaconSpacing;\n      left = hotspotCenterX - popupDimensions.width / 2;\n      anchorOrigin = {\n        vertical: 'top',\n        horizontal: 'center'\n      };\n      transformOrigin = {\n        vertical: 'bottom',\n        horizontal: 'center'\n      };\n      break;\n    case 'bottom':\n      top = hotspotCenterY + hotspotSize / 2 + beaconSpacing;\n      left = hotspotCenterX - popupDimensions.width / 2;\n      anchorOrigin = {\n        vertical: 'bottom',\n        horizontal: 'center'\n      };\n      transformOrigin = {\n        vertical: 'top',\n        horizontal: 'center'\n      };\n      break;\n    case 'left':\n      top = hotspotCenterY - popupDimensions.height / 2;\n      left = hotspotCenterX - hotspotSize / 2 - popupDimensions.width - beaconSpacing;\n      anchorOrigin = {\n        vertical: 'center',\n        horizontal: 'left'\n      };\n      transformOrigin = {\n        vertical: 'center',\n        horizontal: 'right'\n      };\n      break;\n    case 'right':\n      top = hotspotCenterY - popupDimensions.height / 2;\n      left = hotspotCenterX + hotspotSize / 2 + beaconSpacing;\n      anchorOrigin = {\n        vertical: 'center',\n        horizontal: 'right'\n      };\n      transformOrigin = {\n        vertical: 'center',\n        horizontal: 'left'\n      };\n      break;\n  }\n\n  // Ensure popup stays within viewport bounds\n  left = Math.max(padding, Math.min(left, viewport.right - popupDimensions.width - padding));\n  top = Math.max(padding, Math.min(top, viewport.bottom - popupDimensions.height - padding));\n  return {\n    top: top + window.scrollY,\n    left: left + window.scrollX,\n    placement,\n    anchorOrigin,\n    transformOrigin\n  };\n};\nconst HotspotPreview = ({\n  anchorEl,\n  guideStep,\n  title,\n  text,\n  imageUrl,\n  onClose,\n  onPrevious,\n  onContinue,\n  videoUrl,\n  currentStep,\n  totalSteps,\n  onDontShowAgain,\n  progress,\n  textFieldProperties,\n  imageProperties,\n  customButton,\n  modalProperties,\n  canvasProperties,\n  htmlSnippet,\n  previousButtonStyles,\n  continueButtonStyles,\n  OverlayValue,\n  savedGuideData,\n  hotspotProperties,\n  handleHotspotHover,\n  handleHotspotClick,\n  isHotspotPopupOpen,\n  showHotspotenduser\n}) => {\n  _s();\n  var _savedGuideData$Guide, _savedGuideData$Guide2, _textFieldProperties$, _textFieldProperties$2, _textFieldProperties$3, _imageProperties, _imageProperties$Cust, _imageProperties$Cust2, _savedGuideData$Guide31, _savedGuideData$Guide32, _savedGuideData$Guide33;\n  const {\n    setCurrentStep,\n    selectedTemplate,\n    toolTipGuideMetaData,\n    elementSelected,\n    axisData,\n    tooltipXaxis,\n    tooltipYaxis,\n    setOpenTooltip,\n    openTooltip,\n    pulseAnimationsH,\n    hotspotGuideMetaData,\n    selectedTemplateTour,\n    selectedOption,\n    ProgressColor\n  } = useDrawerStore(state => state);\n  const [targetElement, setTargetElement] = useState(null);\n  // Enhanced state management for smart positioning\n  const [popupPosition, setPopupPosition] = useState(null);\n  const [optimalPosition, setOptimalPosition] = useState(null);\n  const [popupDimensions, setPopupDimensions] = useState({\n    width: 300,\n    height: 200\n  });\n  const [dynamicWidth, setDynamicWidth] = useState(null);\n  const [hotspotSize, setHotspotSize] = useState(30);\n  const contentRef = useRef(null);\n  const buttonContainerRef = useRef(null);\n  const popupRef = useRef(null);\n  let hotspot;\n  const getElementByXPath = xpath => {\n    const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n    const node = result.singleNodeValue;\n    if (node instanceof HTMLElement) {\n      return node;\n    } else if (node !== null && node !== void 0 && node.parentElement) {\n      return node.parentElement; // Return parent if it's a text node\n    } else {\n      return null;\n    }\n  };\n  let xpath;\n  if (savedGuideData) xpath = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : (_savedGuideData$Guide2 = _savedGuideData$Guide[0]) === null || _savedGuideData$Guide2 === void 0 ? void 0 : _savedGuideData$Guide2.ElementPath;\n\n  // State to track if scrolling is needed\n  const [needsScrolling, setNeedsScrolling] = useState(false);\n  const scrollbarRef = useRef(null);\n  // Function to calculate popup position below the hotspot\n  const calculatePopupPosition = (elementRect, hotspotSize, xOffset, yOffset) => {\n    const hotspotLeft = elementRect.x + xOffset;\n    const hotspotTop = elementRect.y + yOffset;\n\n    // Position popup below the hotspot for better user experience\n    const dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot\n    const dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing\n\n    return {\n      top: hotspotTop + window.scrollY + dynamicOffsetY,\n      left: hotspotLeft + window.scrollX + dynamicOffsetX\n    };\n  };\n  // Initialize smart positioning when xpath changes\n  useEffect(() => {\n    if (xpath) {\n      const element = getElementByXPath(xpath);\n      if (element) {\n        const dimensions = calculateOptimalDimensions();\n        setPopupDimensions(dimensions);\n        updateSmartPosition(element, dimensions);\n      }\n    }\n  }, [xpath]);\n  useEffect(() => {\n    const element = getElementByXPath(xpath);\n    // setTargetElement(element);\n    if (element) {}\n  }, [savedGuideData]);\n  useEffect(() => {\n    var _guideStep;\n    const element = getElementByXPath(guideStep === null || guideStep === void 0 ? void 0 : (_guideStep = guideStep[currentStep - 1]) === null || _guideStep === void 0 ? void 0 : _guideStep.ElementPath);\n    setTargetElement(element);\n    if (element) {\n      element.style.backgroundColor = \"red !important\";\n\n      // Update popup position when target element changes\n      const rect = element.getBoundingClientRect();\n      setPopupPosition({\n        top: rect.top + window.scrollY,\n        left: rect.left + window.scrollX\n      });\n    }\n  }, [guideStep, currentStep]);\n\n  // Hotspot styles are applied directly in the applyHotspotStyles function\n  // State for overlay value\n  const [, setOverlayValue] = useState(false);\n  const handleContinue = () => {\n    if (selectedTemplate !== \"Tour\") {\n      if (currentStep < totalSteps) {\n        setCurrentStep(currentStep + 1);\n        onContinue();\n        renderNextPopup(currentStep < totalSteps);\n      }\n    } else {\n      setCurrentStep(currentStep + 1);\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.style.display = \"none\";\n        existingHotspot.remove();\n      }\n    }\n  };\n  const renderNextPopup = shouldRenderNextPopup => {\n    var _savedGuideData$Guide3, _savedGuideData$Guide4, _savedGuideData$Guide5, _savedGuideData$Guide6, _savedGuideData$Guide7, _savedGuideData$Guide8, _savedGuideData$Guide9, _savedGuideData$Guide10, _savedGuideData$Guide11, _savedGuideData$Guide12;\n    return shouldRenderNextPopup ? /*#__PURE__*/_jsxDEV(HotspotPreview, {\n      isHotspotPopupOpen: isHotspotPopupOpen,\n      showHotspotenduser: showHotspotenduser,\n      handleHotspotHover: handleHotspotHover,\n      handleHotspotClick: handleHotspotClick,\n      anchorEl: anchorEl,\n      savedGuideData: savedGuideData,\n      guideStep: guideStep,\n      onClose: onClose,\n      onPrevious: handlePrevious,\n      onContinue: handleContinue,\n      title: title,\n      text: text,\n      imageUrl: imageUrl,\n      currentStep: currentStep + 1,\n      totalSteps: totalSteps,\n      onDontShowAgain: onDontShowAgain,\n      progress: progress,\n      textFieldProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide3 = savedGuideData.GuideStep) === null || _savedGuideData$Guide3 === void 0 ? void 0 : (_savedGuideData$Guide4 = _savedGuideData$Guide3[currentStep]) === null || _savedGuideData$Guide4 === void 0 ? void 0 : _savedGuideData$Guide4.TextFieldProperties,\n      imageProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide5 = savedGuideData.GuideStep) === null || _savedGuideData$Guide5 === void 0 ? void 0 : (_savedGuideData$Guide6 = _savedGuideData$Guide5[currentStep]) === null || _savedGuideData$Guide6 === void 0 ? void 0 : _savedGuideData$Guide6.ImageProperties,\n      customButton: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide7 = savedGuideData.GuideStep) === null || _savedGuideData$Guide7 === void 0 ? void 0 : (_savedGuideData$Guide8 = _savedGuideData$Guide7[currentStep]) === null || _savedGuideData$Guide8 === void 0 ? void 0 : (_savedGuideData$Guide9 = _savedGuideData$Guide8.ButtonSection) === null || _savedGuideData$Guide9 === void 0 ? void 0 : (_savedGuideData$Guide10 = _savedGuideData$Guide9.map(section => section.CustomButtons.map(button => ({\n        ...button,\n        ContainerId: section.Id // Attach the container ID for grouping\n      })))) === null || _savedGuideData$Guide10 === void 0 ? void 0 : _savedGuideData$Guide10.reduce((acc, curr) => acc.concat(curr), [])) || [],\n      modalProperties: modalProperties,\n      canvasProperties: canvasProperties,\n      htmlSnippet: htmlSnippet,\n      OverlayValue: OverlayValue,\n      hotspotProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide11 = savedGuideData.GuideStep) === null || _savedGuideData$Guide11 === void 0 ? void 0 : (_savedGuideData$Guide12 = _savedGuideData$Guide11[currentStep - 1]) === null || _savedGuideData$Guide12 === void 0 ? void 0 : _savedGuideData$Guide12.Hotspot) || {}\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 4\n    }, this) : null;\n  };\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n      onPrevious();\n    }\n  };\n  useEffect(() => {\n    if (OverlayValue) {\n      setOverlayValue(true);\n    } else {\n      setOverlayValue(false);\n    }\n  }, [OverlayValue]);\n  // Image fit is used directly in the component\n  const getAnchorAndTransformOrigins = position => {\n    switch (position) {\n      case \"top-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          }\n        };\n      case \"top-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          }\n        };\n      case \"bottom-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      case \"center-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"top-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          }\n        };\n      case \"left-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"right-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      default:\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n    }\n  };\n\n  // Use smart positioning if available, otherwise fall back to canvas properties\n  const {\n    anchorOrigin,\n    transformOrigin\n  } = optimalPosition ? {\n    anchorOrigin: optimalPosition.anchorOrigin,\n    transformOrigin: optimalPosition.transformOrigin\n  } : getAnchorAndTransformOrigins((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center center\");\n  const textStyle = {\n    fontWeight: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$ = textFieldProperties.TextProperties) !== null && _textFieldProperties$ !== void 0 && _textFieldProperties$.Bold ? \"bold\" : \"normal\",\n    fontStyle: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$2 = textFieldProperties.TextProperties) !== null && _textFieldProperties$2 !== void 0 && _textFieldProperties$2.Italic ? \"italic\" : \"normal\",\n    color: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : (_textFieldProperties$3 = textFieldProperties.TextProperties) === null || _textFieldProperties$3 === void 0 ? void 0 : _textFieldProperties$3.TextColor) || \"#000000\",\n    textAlign: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.Alignment) || \"left\"\n  };\n\n  // Image styles are applied directly in the component\n\n  const renderHtmlSnippet = snippet => {\n    // Return the raw HTML snippet for rendering\n    return {\n      __html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\n        return `${p1}${p2}\" target=\"_blank\"${p3}`;\n      })\n    };\n  };\n\n  // Helper function to check if popup has only buttons (no text or images)\n  const hasOnlyButtons = () => {\n    const hasImage = imageProperties && imageProperties.length > 0 && imageProperties.some(prop => prop.CustomImage && prop.CustomImage.some(img => img.Url));\n    const hasText = textFieldProperties && textFieldProperties.length > 0 && textFieldProperties.some(field => field.Text && field.Text.trim() !== \"\");\n    const hasButtons = customButton && customButton.length > 0;\n    return hasButtons && !hasImage && !hasText;\n  };\n\n  // Helper function to check if popup has only text (no buttons or images)\n  const hasOnlyText = () => {\n    const hasImage = imageProperties && imageProperties.length > 0 && imageProperties.some(prop => prop.CustomImage && prop.CustomImage.some(img => img.Url));\n    const hasText = textFieldProperties && textFieldProperties.length > 0 && textFieldProperties.some(field => field.Text && field.Text.trim() !== \"\");\n    const hasButtons = customButton && customButton.length > 0;\n    return hasText && !hasImage && !hasButtons;\n  };\n\n  // Enhanced function to calculate optimal width and height based on content\n  const calculateOptimalDimensions = () => {\n    var _contentRef$current2, _buttonContainerRef$c2;\n    // If we have a fixed width from canvas settings and not a compact popup, use that\n    if (canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width && !hasOnlyButtons() && !hasOnlyText()) {\n      const width = parseInt(canvasProperties.Width);\n      return {\n        width: width,\n        height: estimateContentHeight(width)\n      };\n    }\n\n    // For popups with only buttons or only text, calculate minimal dimensions\n    if (hasOnlyButtons() || hasOnlyText()) {\n      var _contentRef$current, _buttonContainerRef$c;\n      const contentWidth = ((_contentRef$current = contentRef.current) === null || _contentRef$current === void 0 ? void 0 : _contentRef$current.scrollWidth) || 0;\n      const buttonWidth = ((_buttonContainerRef$c = buttonContainerRef.current) === null || _buttonContainerRef$c === void 0 ? void 0 : _buttonContainerRef$c.scrollWidth) || 0;\n      const width = Math.max(contentWidth, buttonWidth, 150); // Minimum 150px\n\n      return {\n        width: width + 40,\n        // Add padding\n        height: estimateContentHeight(width + 40)\n      };\n    }\n\n    // Get the width of content and button container\n    const contentWidth = ((_contentRef$current2 = contentRef.current) === null || _contentRef$current2 === void 0 ? void 0 : _contentRef$current2.scrollWidth) || 0;\n    const buttonWidth = ((_buttonContainerRef$c2 = buttonContainerRef.current) === null || _buttonContainerRef$c2 === void 0 ? void 0 : _buttonContainerRef$c2.scrollWidth) || 0;\n\n    // Use the larger of the two, with some minimum and maximum constraints\n    const optimalWidth = Math.max(contentWidth, buttonWidth);\n\n    // Add some padding to ensure text has room to wrap naturally\n    const paddedWidth = optimalWidth + 40; // 20px padding on each side\n\n    // Ensure width is between reasonable bounds\n    const minWidth = 250; // Minimum width\n    const maxWidth = Math.min(800, window.innerWidth * 0.9); // Max 90% of viewport width\n\n    const finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\n    return {\n      width: finalWidth,\n      height: estimateContentHeight(finalWidth)\n    };\n  };\n\n  // Helper function to estimate content height\n  const estimateContentHeight = width => {\n    let height = 0;\n\n    // Add height for images\n    if (imageProperties && imageProperties.length > 0) {\n      imageProperties.forEach(imageProp => {\n        if (imageProp.CustomImage && imageProp.CustomImage.length > 0) {\n          imageProp.CustomImage.forEach(img => {\n            height += parseInt(img.SectionHeight || '250') + 20; // Add margin\n          });\n        }\n      });\n    }\n\n    // Add height for text (rough estimation)\n    if (textFieldProperties && textFieldProperties.length > 0) {\n      textFieldProperties.forEach(field => {\n        if (field.Text && field.Text.trim() !== '') {\n          // Rough estimation: 20px per line, assuming ~50 characters per line\n          const textLength = field.Text.length;\n          const estimatedLines = Math.ceil(textLength / (width / 10)); // Rough character width estimation\n          height += Math.max(estimatedLines * 20, 40); // Minimum 40px per text field\n        }\n      });\n    }\n\n    // Add height for buttons\n    if (customButton && customButton.length > 0) {\n      height += 60; // Standard button height + padding\n    }\n\n    // Add height for progress bar if enabled\n    if (enableProgress && totalSteps > 1) {\n      height += 50;\n    }\n\n    // Add base padding\n    height += 40;\n\n    // Ensure reasonable bounds\n    const minHeight = 100;\n    const maxHeight = Math.min(400, window.innerHeight * 0.8); // Max 80% of viewport height\n\n    return Math.max(minHeight, Math.min(height, maxHeight));\n  };\n\n  // Update dynamic dimensions and positioning when content changes\n  useEffect(() => {\n    // Use requestAnimationFrame to ensure DOM has been updated\n    requestAnimationFrame(() => {\n      const newDimensions = calculateOptimalDimensions();\n      setPopupDimensions(newDimensions);\n      setDynamicWidth(`${newDimensions.width}px`);\n\n      // Recalculate position if we have a target element\n      if (xpath) {\n        const element = getElementByXPath(xpath);\n        if (element) {\n          updateSmartPosition(element, newDimensions);\n        }\n      }\n    });\n  }, [textFieldProperties, imageProperties, customButton, currentStep, xpath]);\n\n  // Smart positioning function\n  const updateSmartPosition = (element, dimensions) => {\n    var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n    const rect = element.getBoundingClientRect();\n    const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData !== void 0 && _toolTipGuideMetaData.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData2 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.hotspots;\n    const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n    const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n\n    // Calculate optimal placement\n    const placement = calculateOptimalPlacement(rect, dimensions);\n\n    // Calculate position with boundary detection\n    const optimalPos = calculatePopupPositionWithBounds(rect, dimensions, placement, {\n      x: xOffset,\n      y: yOffset\n    });\n    setOptimalPosition(optimalPos);\n    setPopupPosition({\n      top: optimalPos.top,\n      left: optimalPos.left\n    });\n  };\n\n  // Recalculate popup position when hotspot size changes\n  useEffect(() => {\n    if (xpath && hotspotSize) {\n      const element = getElementByXPath(xpath);\n      if (element) {\n        updateSmartPosition(element, popupDimensions);\n      }\n    }\n  }, [hotspotSize, xpath, toolTipGuideMetaData, popupDimensions]);\n\n  // Recalculate popup position on window resize and scroll\n  useEffect(() => {\n    const handleViewportChange = () => {\n      if (xpath) {\n        const element = getElementByXPath(xpath);\n        if (element) {\n          updateSmartPosition(element, popupDimensions);\n        }\n      }\n    };\n    window.addEventListener('resize', handleViewportChange);\n    window.addEventListener('scroll', handleViewportChange);\n    return () => {\n      window.removeEventListener('resize', handleViewportChange);\n      window.removeEventListener('scroll', handleViewportChange);\n    };\n  }, [xpath, popupDimensions]);\n  const groupedButtons = customButton.reduce((acc, button) => {\n    const containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\n    if (!acc[containerId]) {\n      acc[containerId] = [];\n    }\n    acc[containerId].push(button);\n    return acc;\n  }, {});\n  const canvasStyle = {\n    position: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\",\n    borderRadius: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Radius) || \"4px\",\n    borderWidth: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderSize) || \"0px\",\n    borderColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderColor) || \"black\",\n    borderStyle: \"solid\",\n    backgroundColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BackgroundColor) || \"white\",\n    // Enhanced width calculation with viewport awareness\n    maxWidth: hasOnlyButtons() || hasOnlyText() ? \"none !important\" : `min(${popupDimensions.width}px, 90vw) !important`,\n    width: hasOnlyButtons() || hasOnlyText() ? \"auto !important\" : `min(${popupDimensions.width}px, 90vw) !important`,\n    // Enhanced height with max constraints\n    maxHeight: `min(${popupDimensions.height}px, 80vh) !important`\n  };\n  const sectionHeight = ((_imageProperties = imageProperties[currentStep - 1]) === null || _imageProperties === void 0 ? void 0 : (_imageProperties$Cust = _imageProperties.CustomImage) === null || _imageProperties$Cust === void 0 ? void 0 : (_imageProperties$Cust2 = _imageProperties$Cust[currentStep - 1]) === null || _imageProperties$Cust2 === void 0 ? void 0 : _imageProperties$Cust2.SectionHeight) || \"auto\";\n  const handleButtonAction = action => {\n    if (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\n      const targetUrl = action.TargetUrl;\n      if (action.ActionValue === \"same-tab\") {\n        // Open the URL in the same tab\n        window.location.href = targetUrl;\n      } else {\n        // Open the URL in a new tab\n        window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n      }\n    } else {\n      if (action.Action == \"Previous\" || action.Action == \"previous\" || action.ActionValue == \"Previous\" || action.ActionValue == \"Previous\") {\n        handlePrevious();\n      } else if (action.Action == \"Next\" || action.Action == \"next\" || action.ActionValue == \"Next\" || action.ActionValue == \"next\") {\n        handleContinue();\n      } else if (action.Action == \"Restart\" || action.ActionValue == \"Restart\") {\n        var _savedGuideData$Guide13, _savedGuideData$Guide14;\n        // Reset to the first step\n        setCurrentStep(1);\n        // If there's a specific URL for the first step, navigate to it\n        if (savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide13 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide13 !== void 0 && (_savedGuideData$Guide14 = _savedGuideData$Guide13[0]) !== null && _savedGuideData$Guide14 !== void 0 && _savedGuideData$Guide14.ElementPath) {\n          const firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\n          if (firstStepElement) {\n            firstStepElement.scrollIntoView({\n              behavior: 'smooth'\n            });\n          }\n        }\n      }\n    }\n    setOverlayValue(false);\n  };\n  useEffect(() => {\n    var _guideStep2, _guideStep2$Hotspot;\n    if (guideStep !== null && guideStep !== void 0 && (_guideStep2 = guideStep[currentStep - 1]) !== null && _guideStep2 !== void 0 && (_guideStep2$Hotspot = _guideStep2.Hotspot) !== null && _guideStep2$Hotspot !== void 0 && _guideStep2$Hotspot.ShowByDefault) {\n      // Show tooltip by default\n      setOpenTooltip(true);\n    }\n  }, [guideStep === null || guideStep === void 0 ? void 0 : guideStep[currentStep - 1], currentStep, setOpenTooltip]);\n\n  // Add effect to handle isHotspotPopupOpen prop changes\n  useEffect(() => {\n    if (isHotspotPopupOpen) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4, _savedGuideData$Guide15, _savedGuideData$Guide16, _savedGuideData$Guide17, _savedGuideData$Guide18;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData3 !== void 0 && _toolTipGuideMetaData3.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData4 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide15 = savedGuideData.GuideStep) === null || _savedGuideData$Guide15 === void 0 ? void 0 : (_savedGuideData$Guide16 = _savedGuideData$Guide15[currentStep - 1]) === null || _savedGuideData$Guide16 === void 0 ? void 0 : _savedGuideData$Guide16.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide17 = savedGuideData.GuideStep) === null || _savedGuideData$Guide17 === void 0 ? void 0 : (_savedGuideData$Guide18 = _savedGuideData$Guide17[0]) === null || _savedGuideData$Guide18 === void 0 ? void 0 : _savedGuideData$Guide18.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      // For \"Hovering Hotspot\", we'll wait for the hover event\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [isHotspotPopupOpen, toolTipGuideMetaData]);\n\n  // Add effect to handle showHotspotenduser prop changes\n  useEffect(() => {\n    if (showHotspotenduser) {\n      var _toolTipGuideMetaData5, _toolTipGuideMetaData6, _savedGuideData$Guide19, _savedGuideData$Guide20, _savedGuideData$Guide21, _savedGuideData$Guide22;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData5 !== void 0 && _toolTipGuideMetaData5.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData6 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide19 = savedGuideData.GuideStep) === null || _savedGuideData$Guide19 === void 0 ? void 0 : (_savedGuideData$Guide20 = _savedGuideData$Guide19[currentStep - 1]) === null || _savedGuideData$Guide20 === void 0 ? void 0 : _savedGuideData$Guide20.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide21 = savedGuideData.GuideStep) === null || _savedGuideData$Guide21 === void 0 ? void 0 : (_savedGuideData$Guide22 = _savedGuideData$Guide21[0]) === null || _savedGuideData$Guide22 === void 0 ? void 0 : _savedGuideData$Guide22.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [showHotspotenduser, toolTipGuideMetaData]);\n\n  // Add a global click handler to detect clicks outside the hotspot to close the tooltip\n  useEffect(() => {\n    const handleGlobalClick = e => {\n      const hotspotElement = document.getElementById(\"hotspotBlink\");\n\n      // Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\n      if (hotspotElement && hotspotElement.contains(e.target)) {\n        return;\n      }\n\n      // We want to keep the tooltip open once it's been displayed\n      // So we're not closing it on clicks outside anymore\n    };\n    document.addEventListener(\"click\", handleGlobalClick);\n    return () => {\n      document.removeEventListener(\"click\", handleGlobalClick);\n    };\n  }, [toolTipGuideMetaData]);\n  // Check if content needs scrolling with improved detection\n  useEffect(() => {\n    const checkScrollNeeded = () => {\n      if (contentRef.current) {\n        // Force a reflow to get accurate measurements\n        contentRef.current.style.height = 'auto';\n        const contentHeight = contentRef.current.scrollHeight;\n        const containerHeight = 320; // max-height value\n        const shouldScroll = contentHeight > containerHeight;\n        setNeedsScrolling(shouldScroll);\n\n        // Force update scrollbar\n        if (scrollbarRef.current) {\n          // Try multiple methods to update the scrollbar\n          if (scrollbarRef.current.updateScroll) {\n            scrollbarRef.current.updateScroll();\n          }\n          // Force re-initialization if needed\n          setTimeout(() => {\n            if (scrollbarRef.current && scrollbarRef.current.updateScroll) {\n              scrollbarRef.current.updateScroll();\n            }\n          }, 10);\n        }\n      }\n    };\n    checkScrollNeeded();\n    const timeouts = [setTimeout(checkScrollNeeded, 50), setTimeout(checkScrollNeeded, 100), setTimeout(checkScrollNeeded, 200), setTimeout(checkScrollNeeded, 500)];\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (contentRef.current && window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      resizeObserver.observe(contentRef.current);\n    }\n    if (contentRef.current && window.MutationObserver) {\n      mutationObserver = new MutationObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      mutationObserver.observe(contentRef.current, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      timeouts.forEach(clearTimeout);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [currentStep]);\n  // We no longer need the persistent monitoring effect since we want the tooltip\n  // to close when the mouse leaves the hotspot\n\n  function getAlignment(alignment) {\n    switch (alignment) {\n      case \"start\":\n        return \"flex-start\";\n      case \"end\":\n        return \"flex-end\";\n      case \"center\":\n      default:\n        return \"center\";\n    }\n  }\n  const getCanvasPosition = (position = \"center-center\") => {\n    switch (position) {\n      case \"bottom-left\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-right\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-center\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"center-center\":\n        return {\n          top: \"25% !important\"\n        };\n      case \"left-center\":\n        return {\n          top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\"\n        };\n      case \"right-center\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-left\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-right\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-center\":\n        return {\n          top: \"9% !important\"\n        };\n      default:\n        return {\n          top: \"25% !important\"\n        };\n    }\n  };\n\n  // function to get the correct property value based on tour vs normal hotspot\n  const getHotspotProperty = (propName, hotspotPropData, hotspotData) => {\n    if (selectedTemplateTour === \"Hotspot\") {\n      // For tour hotspots, use saved data first, fallback to metadata\n      switch (propName) {\n        case 'PulseAnimation':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.PulseAnimation) !== undefined ? hotspotData.PulseAnimation : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.PulseAnimation;\n        case 'StopAnimation':\n          // Always use stopAnimationUponInteraction for consistency\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.stopAnimationUponInteraction) !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n        case 'ShowUpon':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowUpon) !== undefined ? hotspotData.ShowUpon : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowUpon;\n        case 'ShowByDefault':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowByDefault) !== undefined ? hotspotData.ShowByDefault : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowByDefault;\n        default:\n          return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n      }\n    } else {\n      // For normal hotspots, use metadata\n      if (propName === 'StopAnimation') {\n        return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n      }\n      return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n    }\n  };\n  const applyHotspotStyles = (hotspot, hotspotPropData, hotspotData, left, top) => {\n    hotspot.style.position = \"absolute\";\n    hotspot.style.left = `${left}px`;\n    hotspot.style.top = `${top}px`;\n    hotspot.style.width = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`; // Default size if not provided\n    hotspot.style.height = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`;\n    hotspot.style.backgroundColor = hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Color;\n    hotspot.style.borderRadius = \"50%\";\n    hotspot.style.zIndex = \"auto !important\"; // Increased z-index\n    hotspot.style.transition = \"none\";\n    hotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\n    hotspot.innerHTML = \"\";\n    if ((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Info\" || (hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Question\") {\n      const textSpan = document.createElement(\"span\");\n      textSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\n      textSpan.style.color = \"white\";\n      textSpan.style.fontSize = \"14px\";\n      textSpan.style.fontWeight = \"bold\";\n      textSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\n      textSpan.style.display = \"flex\";\n      textSpan.style.alignItems = \"center\";\n      textSpan.style.justifyContent = \"center\";\n      textSpan.style.width = \"100%\";\n      textSpan.style.height = \"100%\";\n      hotspot.appendChild(textSpan);\n    }\n\n    // Apply animation class if needed\n    // Track if pulse has been stopped by hover\n    const pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\n    const shouldPulse = selectedTemplateTour === \"Hotspot\" ? pulseAnimationEnabled !== false && !hotspot._pulseStopped : hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped;\n    if (shouldPulse) {\n      hotspot.classList.add(\"pulse-animation\");\n      hotspot.classList.remove(\"pulse-animation-removed\");\n    } else {\n      hotspot.classList.remove(\"pulse-animation\");\n      hotspot.classList.add(\"pulse-animation-removed\");\n    }\n\n    // Ensure the hotspot is visible and clickable\n    hotspot.style.display = \"flex\";\n    hotspot.style.pointerEvents = \"auto\";\n\n    // No need for separate animation control functions here\n    // Animation will be controlled directly in the event handlers\n    // Set initial state of openTooltip based on ShowByDefault and ShowUpon\n    const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n    if (showByDefault) {\n      setOpenTooltip(true);\n    } else {\n      // If not showing by default, only show based on interaction type\n      //setOpenTooltip(false);\n    }\n\n    // Only clone and replace if the hotspot doesn't have event listeners already\n    // This prevents losing the _pulseStopped state unnecessarily\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      const newHotspot = hotspot.cloneNode(true);\n      // Copy the _pulseStopped property if it exists\n      if (hotspot._pulseStopped !== undefined) {\n        newHotspot._pulseStopped = hotspot._pulseStopped;\n      }\n      if (hotspot.parentNode) {\n        hotspot.parentNode.replaceChild(newHotspot, hotspot);\n        hotspot = newHotspot;\n      }\n    }\n\n    // Ensure pointer events are enabled\n    hotspot.style.pointerEvents = \"auto\";\n\n    // Define combined event handlers that handle both animation and tooltip\n    const showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\n    const handleHover = e => {\n      e.stopPropagation();\n      console.log(\"Hover detected on hotspot\");\n\n      // Show tooltip if ShowUpon is \"Hovering Hotspot\"\n      if (showUpon === \"Hovering Hotspot\") {\n        // Set openTooltip to true when hovering\n        setOpenTooltip(true);\n\n        // Call the passed hover handler if it exists\n        if (typeof handleHotspotHover === \"function\") {\n          handleHotspotHover();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n    const handleMouseOut = e => {\n      e.stopPropagation();\n\n      // Hide tooltip when mouse leaves the hotspot\n      // Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\n      const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n      if (showUpon === \"Hovering Hotspot\" && !showByDefault) {\n        // setOpenTooltip(false);\n      }\n    };\n    const handleClick = e => {\n      e.stopPropagation();\n      console.log(\"Click detected on hotspot\");\n\n      // Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\n      if (showUpon === \"Clicking Hotspot\" || !showUpon) {\n        // Toggle the tooltip state\n        setOpenTooltip(!openTooltip);\n\n        // Call the passed click handler if it exists\n        if (typeof handleHotspotClick === \"function\") {\n          handleHotspotClick();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n\n    // Add appropriate event listeners based on ShowUpon property\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      if (showUpon === \"Hovering Hotspot\") {\n        // For hover interaction\n        hotspot.addEventListener(\"mouseover\", handleHover);\n        hotspot.addEventListener(\"mouseout\", handleMouseOut);\n\n        // Also add click handler for better user experience\n        hotspot.addEventListener(\"click\", handleClick);\n      } else {\n        // For click interaction (default)\n        hotspot.addEventListener(\"click\", handleClick);\n      }\n\n      // Mark that listeners have been attached\n      hotspot.setAttribute('data-listeners-attached', 'true');\n    }\n  };\n  useEffect(() => {\n    let element;\n    let steps;\n    const fetchGuideDetails = async () => {\n      try {\n        var _savedGuideData$Guide23, _savedGuideData$Guide24, _steps, _steps$;\n        //   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\n        steps = (savedGuideData === null || savedGuideData === void 0 ? void 0 : savedGuideData.GuideStep) || [];\n\n        // For tour hotspots, use the current step's element path\n        const elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide23 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide23 !== void 0 && (_savedGuideData$Guide24 = _savedGuideData$Guide23[currentStep - 1]) !== null && _savedGuideData$Guide24 !== void 0 && _savedGuideData$Guide24.ElementPath ? savedGuideData.GuideStep[currentStep - 1].ElementPath : ((_steps = steps) === null || _steps === void 0 ? void 0 : (_steps$ = _steps[0]) === null || _steps$ === void 0 ? void 0 : _steps$.ElementPath) || \"\";\n        element = getElementByXPath(elementPath || \"\");\n        setTargetElement(element);\n        if (element) {\n          // element.style.outline = \"2px solid red\";\n        }\n\n        // Check if this is a hotspot scenario (normal or tour)\n        const isHotspotScenario = selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" || title === \"Hotspot\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\";\n        if (isHotspotScenario) {\n          var _toolTipGuideMetaData7, _toolTipGuideMetaData8, _hotspotPropData, _hotspotPropData2, _hotspotPropData3, _hotspotPropData4;\n          // Get hotspot properties - prioritize tour data for tour hotspots\n          let hotspotPropData;\n          let hotspotData;\n          if (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.hotspots) {\n            var _savedGuideData$Guide25, _savedGuideData$Guide26;\n            // Tour hotspot - use current step metadata\n            hotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide25 = savedGuideData.GuideStep) === null || _savedGuideData$Guide25 === void 0 ? void 0 : (_savedGuideData$Guide26 = _savedGuideData$Guide25[currentStep - 1]) === null || _savedGuideData$Guide26 === void 0 ? void 0 : _savedGuideData$Guide26.Hotspot;\n          } else if (toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData8 = toolTipGuideMetaData[0]) !== null && _toolTipGuideMetaData8 !== void 0 && _toolTipGuideMetaData8.hotspots) {\n            var _savedGuideData$Guide27, _savedGuideData$Guide28;\n            // Normal hotspot - use first metadata entry\n            hotspotPropData = toolTipGuideMetaData[0].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide27 = savedGuideData.GuideStep) === null || _savedGuideData$Guide27 === void 0 ? void 0 : (_savedGuideData$Guide28 = _savedGuideData$Guide27[0]) === null || _savedGuideData$Guide28 === void 0 ? void 0 : _savedGuideData$Guide28.Hotspot;\n          } else {\n            var _savedGuideData$Guide29, _savedGuideData$Guide30;\n            // Fallback to default values for tour hotspots without metadata\n            hotspotPropData = {\n              XPosition: \"4\",\n              YPosition: \"4\",\n              Type: \"Question\",\n              Color: \"yellow\",\n              Size: \"16\",\n              PulseAnimation: true,\n              stopAnimationUponInteraction: true,\n              ShowUpon: \"Hovering Hotspot\",\n              ShowByDefault: false\n            };\n            hotspotData = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide29 = savedGuideData.GuideStep) === null || _savedGuideData$Guide29 === void 0 ? void 0 : (_savedGuideData$Guide30 = _savedGuideData$Guide29[currentStep - 1]) === null || _savedGuideData$Guide30 === void 0 ? void 0 : _savedGuideData$Guide30.Hotspot) || {};\n          }\n          const xOffset = parseFloat(((_hotspotPropData = hotspotPropData) === null || _hotspotPropData === void 0 ? void 0 : _hotspotPropData.XPosition) || \"4\");\n          const yOffset = parseFloat(((_hotspotPropData2 = hotspotPropData) === null || _hotspotPropData2 === void 0 ? void 0 : _hotspotPropData2.YPosition) || \"4\");\n          const currentHotspotSize = parseFloat(((_hotspotPropData3 = hotspotPropData) === null || _hotspotPropData3 === void 0 ? void 0 : _hotspotPropData3.Size) || \"30\");\n\n          // Update hotspot size state\n          setHotspotSize(currentHotspotSize);\n          let left, top;\n          if (element) {\n            const rect = element.getBoundingClientRect();\n            left = rect.x + xOffset;\n            top = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\n\n            // Calculate popup position below the hotspot\n            const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\n            setPopupPosition(popupPos);\n          }\n\n          // Check if hotspot already exists, preserve it to maintain _pulseStopped state\n          const existingHotspot = document.getElementById(\"hotspotBlink\");\n          if (existingHotspot) {\n            hotspot = existingHotspot;\n            // Don't reset _pulseStopped if it already exists\n          } else {\n            // Create new hotspot only if it doesn't exist\n            hotspot = document.createElement(\"div\");\n            hotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\n            hotspot._pulseStopped = false; // Set only on creation\n            document.body.appendChild(hotspot);\n          }\n          hotspot.style.cursor = \"pointer\";\n          hotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\n\n          // Make sure the hotspot is visible and clickable\n          hotspot.style.zIndex = \"9999\";\n\n          // If ShowByDefault is true, set openTooltip to true immediately\n          if ((_hotspotPropData4 = hotspotPropData) !== null && _hotspotPropData4 !== void 0 && _hotspotPropData4.ShowByDefault) {\n            setOpenTooltip(true);\n          }\n\n          // Set styles first\n          applyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\n\n          // Set initial tooltip visibility based on ShowByDefault\n          const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n          if (showByDefault) {\n            setOpenTooltip(true);\n          } else {\n            //setOpenTooltip(false);\n          }\n\n          // We don't need to add event listeners here as they're already added in applyHotspotStyles\n        }\n      } catch (error) {\n        console.error(\"Error in fetchGuideDetails:\", error);\n      }\n    };\n    fetchGuideDetails();\n    return () => {\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.onclick = null;\n        existingHotspot.onmouseover = null;\n        existingHotspot.onmouseout = null;\n      }\n    };\n  }, [savedGuideData, toolTipGuideMetaData, isHotspotPopupOpen, showHotspotenduser, selectedTemplateTour, currentStep\n  // Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n  ]);\n  const enableProgress = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide31 = savedGuideData.GuideStep) === null || _savedGuideData$Guide31 === void 0 ? void 0 : (_savedGuideData$Guide32 = _savedGuideData$Guide31[0]) === null || _savedGuideData$Guide32 === void 0 ? void 0 : (_savedGuideData$Guide33 = _savedGuideData$Guide32.Tooltip) === null || _savedGuideData$Guide33 === void 0 ? void 0 : _savedGuideData$Guide33.EnableProgress) || false;\n  function getProgressTemplate(selectedOption) {\n    var _savedGuideData$Guide34, _savedGuideData$Guide35, _savedGuideData$Guide36;\n    if (selectedOption === 1) {\n      return \"dots\";\n    } else if (selectedOption === 2) {\n      return \"linear\";\n    } else if (selectedOption === 3) {\n      return \"BreadCrumbs\";\n    } else if (selectedOption === 4) {\n      return \"breadcrumbs\";\n    }\n    return (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide34 = savedGuideData.GuideStep) === null || _savedGuideData$Guide34 === void 0 ? void 0 : (_savedGuideData$Guide35 = _savedGuideData$Guide34[0]) === null || _savedGuideData$Guide35 === void 0 ? void 0 : (_savedGuideData$Guide36 = _savedGuideData$Guide35.Tooltip) === null || _savedGuideData$Guide36 === void 0 ? void 0 : _savedGuideData$Guide36.ProgressTemplate) || \"dots\";\n  }\n  const progressTemplate = getProgressTemplate(selectedOption);\n  const renderProgress = () => {\n    if (!enableProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: totalSteps,\n        position: \"static\",\n        activeStep: currentStep - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          position: \"inherit !important\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1328,\n          columnNumber: 18\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1329,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1316,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"5px\",\n          padding: \"8px\"\n        },\n        children: Array.from({\n          length: totalSteps\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"14px\",\n            height: \"4px\",\n            backgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\",\n            // Active color and inactive color\n            borderRadius: \"100px\"\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1339,\n          columnNumber: 7\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1335,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"flex-start\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            padding: \"8px\",\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStep, \" of \", totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1355,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1354,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: progress,\n            sx: {\n              height: \"6px\",\n              borderRadius: \"20px\",\n              margin: \"6px 10px\",\n              \"& .MuiLinearProgress-bar\": {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1366,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1365,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1364,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [targetElement && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: openTooltip && /*#__PURE__*/_jsxDEV(Popover, {\n        open: Boolean(popupPosition) || Boolean(anchorEl),\n        anchorEl: anchorEl,\n        onClose: () => {\n          // We want to keep the tooltip open once it's been displayed\n          // So we're not closing it on Popover close events\n        },\n        anchorOrigin: anchorOrigin,\n        transformOrigin: transformOrigin,\n        anchorReference: \"anchorPosition\",\n        anchorPosition: popupPosition ? {\n          // Use smart positioning if available, otherwise use legacy positioning\n          top: optimalPosition ? optimalPosition.top : popupPosition.top + 10 + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\n          left: optimalPosition ? optimalPosition.left : popupPosition.left + 10 + parseFloat(tooltipXaxis || \"0\")\n        } : undefined,\n        sx: {\n          \"pointer-events\": anchorEl ? \"auto\" : \"auto\",\n          '& .MuiPaper-root:not(.MuiMobileStepper-root)': {\n            zIndex: 1000,\n            ...canvasStyle,\n            // Smart positioning overrides legacy positioning when available\n            ...(optimalPosition ? {} : getCanvasPosition((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\")),\n            // Only apply legacy positioning if smart positioning is not available\n            ...(optimalPosition ? {} : {\n              top: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.top) || 0) + (tooltipYaxis && tooltipYaxis != 'undefined' ? parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\")) : 0)}px !important`,\n              left: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.left) || 0) + (tooltipXaxis && tooltipXaxis != 'undefined' ? parseFloat(tooltipXaxis) || 0 : 0)}px !important`\n            }),\n            overflow: \"hidden\"\n          }\n        },\n        disableScrollLock: true,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            placeContent: \"end\",\n            display: \"flex\"\n          },\n          children: (modalProperties === null || modalProperties === void 0 ? void 0 : modalProperties.DismissOption) && /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => {\n              // Only close if explicitly requested by user clicking the close button\n              //setOpenTooltip(false);\n            },\n            sx: {\n              position: \"fixed\",\n              boxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\n              left: \"auto\",\n              right: \"auto\",\n              margin: \"-15px\",\n              background: \"#fff !important\",\n              border: \"1px solid #ccc\",\n              zIndex: \"999999\",\n              borderRadius: \"50px\",\n              padding: \"5px !important\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              sx: {\n                zoom: 1,\n                color: \"#000\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1470,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1452,\n            columnNumber: 10\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1450,\n          columnNumber: 8\n        }, this), /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n          ref: scrollbarRef,\n          style: {\n            maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\"\n          },\n          options: {\n            suppressScrollY: !needsScrolling,\n            suppressScrollX: true,\n            wheelPropagation: false,\n            swipeEasing: true,\n            minScrollbarLength: 20,\n            scrollingThreshold: 1000,\n            scrollYMarginOffset: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\n              overflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\n              width: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\n              margin: hasOnlyButtons() ? \"0\" : undefined\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              style: {\n                padding: hasOnlyButtons() ? \"0\" : hasOnlyText() ? \"0\" : (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Padding) || \"10px\",\n                height: hasOnlyButtons() ? \"auto\" : sectionHeight,\n                width: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\n                margin: hasOnlyButtons() ? \"0\" : undefined\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                ref: contentRef,\n                display: \"flex\",\n                flexDirection: \"column\",\n                flexWrap: \"wrap\",\n                justifyContent: \"center\",\n                sx: {\n                  width: hasOnlyText() ? \"auto\" : \"100%\",\n                  padding: hasOnlyText() ? \"0\" : undefined\n                },\n                children: [imageProperties === null || imageProperties === void 0 ? void 0 : imageProperties.map(imageProp => imageProp.CustomImage.map((customImg, imgIndex) => /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"img\",\n                  src: customImg.Url,\n                  alt: customImg.AltText || \"Image\",\n                  sx: {\n                    maxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\n                    textAlign: imageProp.Alignment || \"center\",\n                    objectFit: customImg.Fit || \"contain\",\n                    //  width: \"500px\",\n                    height: `${customImg.SectionHeight || 250}px`,\n                    background: customImg.BackgroundColor || \"#ffffff\",\n                    margin: \"10px 0\"\n                  },\n                  onClick: () => {\n                    if (imageProp.Hyperlink) {\n                      const targetUrl = imageProp.Hyperlink;\n                      window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n                    }\n                  },\n                  style: {\n                    cursor: imageProp.Hyperlink ? \"pointer\" : \"default\"\n                  }\n                }, `${imageProp.Id}-${imgIndex}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1514,\n                  columnNumber: 13\n                }, this))), textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.map((textField, index) => {\n                  var _textField$TextProper, _textField$TextProper2;\n                  return textField.Text && /*#__PURE__*/_jsxDEV(Typography, {\n                    className: \"qadpt-preview\",\n                    // Use a unique key, either Id or index\n                    sx: {\n                      textAlign: ((_textField$TextProper = textField.TextProperties) === null || _textField$TextProper === void 0 ? void 0 : _textField$TextProper.TextFormat) || textStyle.textAlign,\n                      color: ((_textField$TextProper2 = textField.TextProperties) === null || _textField$TextProper2 === void 0 ? void 0 : _textField$TextProper2.TextColor) || textStyle.color,\n                      whiteSpace: \"pre-wrap\",\n                      wordBreak: \"break-word\",\n                      padding: \"0 5px\"\n                    },\n                    dangerouslySetInnerHTML: renderHtmlSnippet(textField.Text) // Render the raw HTML\n                  }, textField.Id || index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1542,\n                    columnNumber: 14\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1501,\n                columnNumber: 10\n              }, this), Object.keys(groupedButtons).map(containerId => {\n                var _groupedButtons$conta, _groupedButtons$conta2;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  ref: buttonContainerRef,\n                  sx: {\n                    display: \"flex\",\n                    justifyContent: getAlignment((_groupedButtons$conta = groupedButtons[containerId][0]) === null || _groupedButtons$conta === void 0 ? void 0 : _groupedButtons$conta.Alignment),\n                    flexWrap: \"wrap\",\n                    margin: hasOnlyButtons() ? 0 : \"5px 0\",\n                    backgroundColor: (_groupedButtons$conta2 = groupedButtons[containerId][0]) === null || _groupedButtons$conta2 === void 0 ? void 0 : _groupedButtons$conta2.BackgroundColor,\n                    padding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\n                    width: hasOnlyButtons() ? \"auto\" : \"100%\",\n                    borderRadius: hasOnlyButtons() ? \"15px\" : undefined\n                  },\n                  children: groupedButtons[containerId].map((button, index) => {\n                    var _button$ButtonPropert, _button$ButtonPropert2, _button$ButtonPropert3, _button$ButtonPropert4, _button$ButtonPropert5, _button$ButtonPropert6, _button$ButtonPropert7;\n                    return /*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => handleButtonAction(button.ButtonAction),\n                      variant: \"contained\",\n                      sx: {\n                        marginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\n                        margin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\n                        backgroundColor: ((_button$ButtonPropert = button.ButtonProperties) === null || _button$ButtonPropert === void 0 ? void 0 : _button$ButtonPropert.ButtonBackgroundColor) || \"#007bff\",\n                        color: ((_button$ButtonPropert2 = button.ButtonProperties) === null || _button$ButtonPropert2 === void 0 ? void 0 : _button$ButtonPropert2.ButtonTextColor) || \"#fff\",\n                        border: ((_button$ButtonPropert3 = button.ButtonProperties) === null || _button$ButtonPropert3 === void 0 ? void 0 : _button$ButtonPropert3.ButtonBorderColor) || \"transparent\",\n                        fontSize: ((_button$ButtonPropert4 = button.ButtonProperties) === null || _button$ButtonPropert4 === void 0 ? void 0 : _button$ButtonPropert4.FontSize) || \"15px\",\n                        width: ((_button$ButtonPropert5 = button.ButtonProperties) === null || _button$ButtonPropert5 === void 0 ? void 0 : _button$ButtonPropert5.Width) || \"auto\",\n                        padding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\n                        lineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\n                        textTransform: \"none\",\n                        borderRadius: ((_button$ButtonPropert6 = button.ButtonProperties) === null || _button$ButtonPropert6 === void 0 ? void 0 : _button$ButtonPropert6.BorderRadius) || \"8px\",\n                        minWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\n                        boxShadow: \"none !important\",\n                        // Remove box shadow in normal state\n                        \"&:hover\": {\n                          backgroundColor: ((_button$ButtonPropert7 = button.ButtonProperties) === null || _button$ButtonPropert7 === void 0 ? void 0 : _button$ButtonPropert7.ButtonBackgroundColor) || \"#007bff\",\n                          // Keep the same background color on hover\n                          opacity: 0.9,\n                          // Slightly reduce opacity on hover for visual feedback\n                          boxShadow: \"none !important\" // Remove box shadow in hover state\n                        }\n                      },\n                      children: button.ButtonName\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1574,\n                      columnNumber: 13\n                    }, this);\n                  })\n                }, containerId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1559,\n                  columnNumber: 11\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1494,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1488,\n            columnNumber: 8\n          }, this)\n        }, `scrollbar-${needsScrolling}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1474,\n          columnNumber: 8\n        }, this), enableProgress && totalSteps > 1 && selectedTemplate === \"Tour\" && /*#__PURE__*/_jsxDEV(Box, {\n          children: renderProgress()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1610,\n          columnNumber: 75\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1403,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1388,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1616,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(HotspotPreview, \"YYXDyfFJt200bTa/2x07jmP22bU=\", false, function () {\n  return [useDrawerStore];\n});\n_c = HotspotPreview;\nexport default HotspotPreview;\nvar _c;\n$RefreshReg$(_c, \"HotspotPreview\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getViewportBounds", "top", "left", "right", "window", "innerWidth", "bottom", "innerHeight", "getAvailableSpace", "elementRect", "viewport", "calculateOptimalPlacement", "popupDimensions", "space", "width", "height", "padding", "fitsTop", "fitsBottom", "fitsLeft", "fitsRight", "maxSpace", "Math", "max", "calculatePopupPositionWithBounds", "placement", "hotspotOffset", "x", "y", "hotspotSize", "beaconSpacing", "anchor<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "hotspotCenterX", "hotspotCenterY", "vertical", "horizontal", "min", "scrollY", "scrollX", "HotspotPreview", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "_s", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "optimalPosition", "setOptimalPosition", "setPopupDimensions", "dynamicWidth", "setDynamicWidth", "setHotspotSize", "contentRef", "buttonContainerRef", "popupRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "GuideStep", "<PERSON>ement<PERSON><PERSON>", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "calculatePopupPosition", "xOffset", "yOffset", "hotspotLeft", "hotspotTop", "dynamicOffsetX", "dynamicOffsetY", "element", "dimensions", "calculateOptimalDimensions", "updateSmartPosition", "_guideStep", "style", "backgroundColor", "rect", "getBoundingClientRect", "setOverlayValue", "handleContinue", "renderNextPopup", "existingHotspot", "getElementById", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "Hotspot", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAnchorAndTransformOrigins", "position", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "replace", "_match", "p1", "p2", "p3", "hasOnlyButtons", "hasImage", "length", "some", "prop", "CustomImage", "img", "Url", "hasText", "field", "Text", "trim", "hasButtons", "hasOnlyText", "_contentRef$current2", "_buttonContainerRef$c2", "<PERSON><PERSON><PERSON>", "parseInt", "estimateContentHeight", "_contentRef$current", "_buttonContainerRef$c", "contentWidth", "current", "scrollWidth", "buttonWidth", "optimalWidth", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "finalWidth", "for<PERSON>ach", "imageProp", "SectionHeight", "textLength", "estimatedLines", "ceil", "enableProgress", "minHeight", "maxHeight", "requestAnimationFrame", "newDimensions", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "hotspotPropData", "hotspots", "parseFloat", "XPosition", "YPosition", "optimalPos", "handleViewportChange", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "sectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "firstStepElement", "scrollIntoView", "behavior", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_savedGuideData$Guide15", "_savedGuideData$Guide16", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "hotspotData", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "clearTimeout", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "getHotspotProperty", "propName", "PulseAnimation", "undefined", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Size", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "console", "log", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_steps", "_steps$", "elementPath", "isHotspotScenario", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "currentHotspotSize", "abs", "popupPos", "id", "body", "cursor", "error", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "boxShadow", "background", "border", "zoom", "ref", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "Padding", "flexDirection", "flexWrap", "customImg", "imgIndex", "component", "src", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "className", "TextFormat", "whiteSpace", "wordBreak", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "marginRight", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName", "_c", "$RefreshReg$"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n// Viewport boundary detection utilities\r\ninterface ViewportBounds {\r\n\ttop: number;\r\n\tleft: number;\r\n\tright: number;\r\n\tbottom: number;\r\n}\r\n\r\ninterface PopupDimensions {\r\n\twidth: number;\r\n\theight: number;\r\n}\r\n\r\ninterface OptimalPosition {\r\n\ttop: number;\r\n\tleft: number;\r\n\tplacement: 'top' | 'bottom' | 'left' | 'right';\r\n\tanchorOrigin: PopoverOrigin;\r\n\ttransformOrigin: PopoverOrigin;\r\n}\r\n\r\n// Utility function to get viewport bounds\r\nconst getViewportBounds = (): ViewportBounds => ({\r\n\ttop: 0,\r\n\tleft: 0,\r\n\tright: window.innerWidth,\r\n\tbottom: window.innerHeight\r\n});\r\n\r\n// Utility function to calculate available space around an element\r\nconst getAvailableSpace = (elementRect: DOMRect) => {\r\n\tconst viewport = getViewportBounds();\r\n\treturn {\r\n\t\ttop: elementRect.top - viewport.top,\r\n\t\tbottom: viewport.bottom - elementRect.bottom,\r\n\t\tleft: elementRect.left - viewport.left,\r\n\t\tright: viewport.right - elementRect.right\r\n\t};\r\n};\r\n\r\n// Utility function to determine optimal placement based on available space\r\nconst calculateOptimalPlacement = (\r\n\telementRect: DOMRect,\r\n\tpopupDimensions: PopupDimensions\r\n): 'top' | 'bottom' | 'left' | 'right' => {\r\n\tconst space = getAvailableSpace(elementRect);\r\n\tconst { width, height } = popupDimensions;\r\n\r\n\t// Add some padding for better UX\r\n\tconst padding = 20;\r\n\r\n\t// Check if popup fits in each direction\r\n\tconst fitsTop = space.top >= height + padding;\r\n\tconst fitsBottom = space.bottom >= height + padding;\r\n\tconst fitsLeft = space.left >= width + padding;\r\n\tconst fitsRight = space.right >= width + padding;\r\n\r\n\t// Prefer bottom first (most common), then top, then sides\r\n\tif (fitsBottom) return 'bottom';\r\n\tif (fitsTop) return 'top';\r\n\tif (fitsRight) return 'right';\r\n\tif (fitsLeft) return 'left';\r\n\r\n\t// If nothing fits perfectly, choose the side with most space\r\n\tconst maxSpace = Math.max(space.top, space.bottom, space.left, space.right);\r\n\tif (maxSpace === space.bottom) return 'bottom';\r\n\tif (maxSpace === space.top) return 'top';\r\n\tif (maxSpace === space.right) return 'right';\r\n\treturn 'left';\r\n};\r\n\r\n// Utility function to calculate popup position with boundary detection\r\nconst calculatePopupPositionWithBounds = (\r\n\telementRect: DOMRect,\r\n\tpopupDimensions: PopupDimensions,\r\n\tplacement: 'top' | 'bottom' | 'left' | 'right',\r\n\thotspotOffset: { x: number; y: number } = { x: 0, y: 0 },\r\n\thotspotSize: number = 30\r\n): OptimalPosition => {\r\n\tconst viewport = getViewportBounds();\r\n\tconst padding = 10;\r\n\tconst beaconSpacing = 10; // Exact spacing from beacon edge\r\n\r\n\tlet top: number;\r\n\tlet left: number;\r\n\tlet anchorOrigin: PopoverOrigin;\r\n\tlet transformOrigin: PopoverOrigin;\r\n\r\n\t// Calculate hotspot center position\r\n\tconst hotspotCenterX = elementRect.left + hotspotOffset.x + (hotspotSize / 2);\r\n\tconst hotspotCenterY = elementRect.top + hotspotOffset.y + (hotspotSize / 2);\r\n\r\n\tswitch (placement) {\r\n\t\tcase 'top':\r\n\t\t\ttop = hotspotCenterY - (hotspotSize / 2) - popupDimensions.height - beaconSpacing;\r\n\t\t\tleft = hotspotCenterX - (popupDimensions.width / 2);\r\n\t\t\tanchorOrigin = { vertical: 'top', horizontal: 'center' };\r\n\t\t\ttransformOrigin = { vertical: 'bottom', horizontal: 'center' };\r\n\t\t\tbreak;\r\n\r\n\t\tcase 'bottom':\r\n\t\t\ttop = hotspotCenterY + (hotspotSize / 2) + beaconSpacing;\r\n\t\t\tleft = hotspotCenterX - (popupDimensions.width / 2);\r\n\t\t\tanchorOrigin = { vertical: 'bottom', horizontal: 'center' };\r\n\t\t\ttransformOrigin = { vertical: 'top', horizontal: 'center' };\r\n\t\t\tbreak;\r\n\r\n\t\tcase 'left':\r\n\t\t\ttop = hotspotCenterY - (popupDimensions.height / 2);\r\n\t\t\tleft = hotspotCenterX - (hotspotSize / 2) - popupDimensions.width - beaconSpacing;\r\n\t\t\tanchorOrigin = { vertical: 'center', horizontal: 'left' };\r\n\t\t\ttransformOrigin = { vertical: 'center', horizontal: 'right' };\r\n\t\t\tbreak;\r\n\r\n\t\tcase 'right':\r\n\t\t\ttop = hotspotCenterY - (popupDimensions.height / 2);\r\n\t\t\tleft = hotspotCenterX + (hotspotSize / 2) + beaconSpacing;\r\n\t\t\tanchorOrigin = { vertical: 'center', horizontal: 'right' };\r\n\t\t\ttransformOrigin = { vertical: 'center', horizontal: 'left' };\r\n\t\t\tbreak;\r\n\t}\r\n\r\n\t// Ensure popup stays within viewport bounds\r\n\tleft = Math.max(padding, Math.min(left, viewport.right - popupDimensions.width - padding));\r\n\ttop = Math.max(padding, Math.min(top, viewport.bottom - popupDimensions.height - padding));\r\n\r\n\treturn {\r\n\t\ttop: top + window.scrollY,\r\n\t\tleft: left + window.scrollX,\r\n\t\tplacement,\r\n\t\tanchorOrigin,\r\n\t\ttransformOrigin\r\n\t};\r\n};\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// Enhanced state management for smart positioning\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [optimalPosition, setOptimalPosition] = useState<OptimalPosition | null>(null);\r\n\tconst [popupDimensions, setPopupDimensions] = useState<PopupDimensions>({ width: 300, height: 200 });\r\n\tconst [dynamicWidth, setDynamicWidth] = useState<string | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30);\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tconst popupRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\tconst node = result.singleNodeValue;\r\n\t\tif (node instanceof HTMLElement) {\r\n\t\t\treturn node;\r\n\t\t} else if (node?.parentElement) {\r\n\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t} else {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to calculate popup position below the hotspot\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Position popup below the hotspot for better user experience\r\n\t\tconst dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot\r\n\t\tconst dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing\r\n\r\n\t\treturn {\r\n\t\t\ttop: hotspotTop + window.scrollY + dynamicOffsetY,\r\n\t\t\tleft: hotspotLeft + window.scrollX + dynamicOffsetX\r\n\t\t};\r\n\t};\r\n\t// Initialize smart positioning when xpath changes\r\n\tuseEffect(() => {\r\n\t\tif (xpath) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst dimensions = calculateOptimalDimensions();\r\n\t\t\t\tsetPopupDimensions(dimensions);\r\n\t\t\t\tupdateSmartPosition(element, dimensions);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\t// setTargetElement(element);\r\n\t\tif (element) {\r\n\t\t}\r\n\t}, [savedGuideData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Update popup position when target element changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY,\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [guideStep, currentStep]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\t// Use smart positioning if available, otherwise fall back to canvas properties\r\n\tconst { anchorOrigin, transformOrigin } = optimalPosition\r\n\t\t? { anchorOrigin: optimalPosition.anchorOrigin, transformOrigin: optimalPosition.transformOrigin }\r\n\t\t: getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\t// Helper function to check if popup has only buttons (no text or images)\r\n\tconst hasOnlyButtons = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasButtons && !hasImage && !hasText;\r\n\t};\r\n\r\n\t// Helper function to check if popup has only text (no buttons or images)\r\n\tconst hasOnlyText = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasText && !hasImage && !hasButtons;\r\n\t};\r\n\r\n\t// Enhanced function to calculate optimal width and height based on content\r\n\tconst calculateOptimalDimensions = (): PopupDimensions => {\r\n\t\t// If we have a fixed width from canvas settings and not a compact popup, use that\r\n\t\tif (canvasProperties?.Width && !hasOnlyButtons() && !hasOnlyText()) {\r\n\t\t\tconst width = parseInt(canvasProperties.Width);\r\n\t\t\treturn {\r\n\t\t\t\twidth: width,\r\n\t\t\t\theight: estimateContentHeight(width)\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// For popups with only buttons or only text, calculate minimal dimensions\r\n\t\tif (hasOnlyButtons() || hasOnlyText()) {\r\n\t\t\tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t\t\tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\t\t\tconst width = Math.max(contentWidth, buttonWidth, 150); // Minimum 150px\r\n\r\n\t\t\treturn {\r\n\t\t\t\twidth: width + 40, // Add padding\r\n\t\t\t\theight: estimateContentHeight(width + 40)\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Get the width of content and button container\r\n\t\tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t\tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\r\n\t\t// Use the larger of the two, with some minimum and maximum constraints\r\n\t\tconst optimalWidth = Math.max(contentWidth, buttonWidth);\r\n\r\n\t\t// Add some padding to ensure text has room to wrap naturally\r\n\t\tconst paddedWidth = optimalWidth + 40; // 20px padding on each side\r\n\r\n\t\t// Ensure width is between reasonable bounds\r\n\t\tconst minWidth = 250; // Minimum width\r\n\t\tconst maxWidth = Math.min(800, window.innerWidth * 0.9); // Max 90% of viewport width\r\n\r\n\t\tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\r\n\r\n\t\treturn {\r\n\t\t\twidth: finalWidth,\r\n\t\t\theight: estimateContentHeight(finalWidth)\r\n\t\t};\r\n\t};\r\n\r\n\t// Helper function to estimate content height\r\n\tconst estimateContentHeight = (width: number): number => {\r\n\t\tlet height = 0;\r\n\r\n\t\t// Add height for images\r\n\t\tif (imageProperties && imageProperties.length > 0) {\r\n\t\t\timageProperties.forEach((imageProp: any) => {\r\n\t\t\t\tif (imageProp.CustomImage && imageProp.CustomImage.length > 0) {\r\n\t\t\t\t\timageProp.CustomImage.forEach((img: any) => {\r\n\t\t\t\t\t\theight += parseInt(img.SectionHeight || '250') + 20; // Add margin\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// Add height for text (rough estimation)\r\n\t\tif (textFieldProperties && textFieldProperties.length > 0) {\r\n\t\t\ttextFieldProperties.forEach((field: any) => {\r\n\t\t\t\tif (field.Text && field.Text.trim() !== '') {\r\n\t\t\t\t\t// Rough estimation: 20px per line, assuming ~50 characters per line\r\n\t\t\t\t\tconst textLength = field.Text.length;\r\n\t\t\t\t\tconst estimatedLines = Math.ceil(textLength / (width / 10)); // Rough character width estimation\r\n\t\t\t\t\theight += Math.max(estimatedLines * 20, 40); // Minimum 40px per text field\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// Add height for buttons\r\n\t\tif (customButton && customButton.length > 0) {\r\n\t\t\theight += 60; // Standard button height + padding\r\n\t\t}\r\n\r\n\t\t// Add height for progress bar if enabled\r\n\t\tif (enableProgress && totalSteps > 1) {\r\n\t\t\theight += 50;\r\n\t\t}\r\n\r\n\t\t// Add base padding\r\n\t\theight += 40;\r\n\r\n\t\t// Ensure reasonable bounds\r\n\t\tconst minHeight = 100;\r\n\t\tconst maxHeight = Math.min(400, window.innerHeight * 0.8); // Max 80% of viewport height\r\n\r\n\t\treturn Math.max(minHeight, Math.min(height, maxHeight));\r\n\t};\r\n\r\n\r\n\r\n\t// Update dynamic dimensions and positioning when content changes\r\n\tuseEffect(() => {\r\n\t\t// Use requestAnimationFrame to ensure DOM has been updated\r\n\t\trequestAnimationFrame(() => {\r\n\t\t\tconst newDimensions = calculateOptimalDimensions();\r\n\t\t\tsetPopupDimensions(newDimensions);\r\n\t\t\tsetDynamicWidth(`${newDimensions.width}px`);\r\n\r\n\t\t\t// Recalculate position if we have a target element\r\n\t\t\tif (xpath) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tupdateSmartPosition(element, newDimensions);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t});\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep, xpath]);\r\n\r\n\t// Smart positioning function\r\n\tconst updateSmartPosition = (element: HTMLElement, dimensions: PopupDimensions) => {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\r\n\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t// Calculate optimal placement\r\n\t\tconst placement = calculateOptimalPlacement(rect, dimensions);\r\n\r\n\t\t// Calculate position with boundary detection\r\n\t\tconst optimalPos = calculatePopupPositionWithBounds(\r\n\t\t\trect,\r\n\t\t\tdimensions,\r\n\t\t\tplacement,\r\n\t\t\t{ x: xOffset, y: yOffset }\r\n\t\t);\r\n\r\n\t\tsetOptimalPosition(optimalPos);\r\n\t\tsetPopupPosition({\r\n\t\t\ttop: optimalPos.top,\r\n\t\t\tleft: optimalPos.left\r\n\t\t});\r\n\t};\r\n\r\n\t// Recalculate popup position when hotspot size changes\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tupdateSmartPosition(element, popupDimensions);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData, popupDimensions]);\r\n\r\n\t// Recalculate popup position on window resize and scroll\r\n\tuseEffect(() => {\r\n\t\tconst handleViewportChange = () => {\r\n\t\t\tif (xpath) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tupdateSmartPosition(element, popupDimensions);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleViewportChange);\r\n\t\twindow.addEventListener('scroll', handleViewportChange);\r\n\t\treturn () => {\r\n\t\t\twindow.removeEventListener('resize', handleViewportChange);\r\n\t\t\twindow.removeEventListener('scroll', handleViewportChange);\r\n\t\t};\r\n\t}, [xpath, popupDimensions]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\t// Enhanced width calculation with viewport awareness\r\n\t\tmaxWidth: (hasOnlyButtons() || hasOnlyText()) ? \"none !important\" :\r\n\t\t\t\t  `min(${popupDimensions.width}px, 90vw) !important`,\r\n\t\twidth: (hasOnlyButtons() || hasOnlyText()) ? \"auto !important\" :\r\n\t\t\t   `min(${popupDimensions.width}px, 90vw) !important`,\r\n\t\t// Enhanced height with max constraints\r\n\t\tmaxHeight: `min(${popupDimensions.height}px, 80vh) !important`,\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\tfirstStepElement.scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element;\r\n\t\tlet steps;\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\t// Use smart positioning if available, otherwise use legacy positioning\r\n\t\t\t\t\t\t\t\t\t\t\ttop: optimalPosition\r\n\t\t\t\t\t\t\t\t\t\t\t\t? optimalPosition.top\r\n\t\t\t\t\t\t\t\t\t\t\t\t: popupPosition.top + 10 + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: optimalPosition\r\n\t\t\t\t\t\t\t\t\t\t\t\t? optimalPosition.left\r\n\t\t\t\t\t\t\t\t\t\t\t\t: popupPosition.left + 10 + parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t// Smart positioning overrides legacy positioning when available\r\n\t\t\t\t\t\t\t\t\t...(optimalPosition ? {} : getCanvasPosition(canvasProperties?.Position || \"center-center\")),\r\n\t\t\t\t\t\t\t\t\t// Only apply legacy positioning if smart positioning is not available\r\n\t\t\t\t\t\t\t\t\t...(optimalPosition ? {} : {\r\n\t\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\t}),\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",}}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\r\n\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"0\" :\r\n\t\t\t\t\t\t\t\t\t\t\thasOnlyText() ? \"0\" : (canvasProperties?.Padding || \"10px\"),\r\n\t\t\t\t\t\t\t\t\theight: hasOnlyButtons() ? \"auto\" : sectionHeight,\r\n\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyText() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyText() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? 0 : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: hasOnlyButtons() ? \"15px\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tminWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,aAAa,EAAEC,OAAO,EAAiBC,UAAU,QAAQ,eAAe;AAE1H,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAuB,yBAAyB;AACrE;AACA,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;;AAEpD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqBA;AACA,MAAMC,iBAAiB,GAAGA,CAAA,MAAuB;EAChDC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAEC,MAAM,CAACC,UAAU;EACxBC,MAAM,EAAEF,MAAM,CAACG;AAChB,CAAC,CAAC;;AAEF;AACA,MAAMC,iBAAiB,GAAIC,WAAoB,IAAK;EACnD,MAAMC,QAAQ,GAAGV,iBAAiB,CAAC,CAAC;EACpC,OAAO;IACNC,GAAG,EAAEQ,WAAW,CAACR,GAAG,GAAGS,QAAQ,CAACT,GAAG;IACnCK,MAAM,EAAEI,QAAQ,CAACJ,MAAM,GAAGG,WAAW,CAACH,MAAM;IAC5CJ,IAAI,EAAEO,WAAW,CAACP,IAAI,GAAGQ,QAAQ,CAACR,IAAI;IACtCC,KAAK,EAAEO,QAAQ,CAACP,KAAK,GAAGM,WAAW,CAACN;EACrC,CAAC;AACF,CAAC;;AAED;AACA,MAAMQ,yBAAyB,GAAGA,CACjCF,WAAoB,EACpBG,eAAgC,KACS;EACzC,MAAMC,KAAK,GAAGL,iBAAiB,CAACC,WAAW,CAAC;EAC5C,MAAM;IAAEK,KAAK;IAAEC;EAAO,CAAC,GAAGH,eAAe;;EAEzC;EACA,MAAMI,OAAO,GAAG,EAAE;;EAElB;EACA,MAAMC,OAAO,GAAGJ,KAAK,CAACZ,GAAG,IAAIc,MAAM,GAAGC,OAAO;EAC7C,MAAME,UAAU,GAAGL,KAAK,CAACP,MAAM,IAAIS,MAAM,GAAGC,OAAO;EACnD,MAAMG,QAAQ,GAAGN,KAAK,CAACX,IAAI,IAAIY,KAAK,GAAGE,OAAO;EAC9C,MAAMI,SAAS,GAAGP,KAAK,CAACV,KAAK,IAAIW,KAAK,GAAGE,OAAO;;EAEhD;EACA,IAAIE,UAAU,EAAE,OAAO,QAAQ;EAC/B,IAAID,OAAO,EAAE,OAAO,KAAK;EACzB,IAAIG,SAAS,EAAE,OAAO,OAAO;EAC7B,IAAID,QAAQ,EAAE,OAAO,MAAM;;EAE3B;EACA,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACV,KAAK,CAACZ,GAAG,EAAEY,KAAK,CAACP,MAAM,EAAEO,KAAK,CAACX,IAAI,EAAEW,KAAK,CAACV,KAAK,CAAC;EAC3E,IAAIkB,QAAQ,KAAKR,KAAK,CAACP,MAAM,EAAE,OAAO,QAAQ;EAC9C,IAAIe,QAAQ,KAAKR,KAAK,CAACZ,GAAG,EAAE,OAAO,KAAK;EACxC,IAAIoB,QAAQ,KAAKR,KAAK,CAACV,KAAK,EAAE,OAAO,OAAO;EAC5C,OAAO,MAAM;AACd,CAAC;;AAED;AACA,MAAMqB,gCAAgC,GAAGA,CACxCf,WAAoB,EACpBG,eAAgC,EAChCa,SAA8C,EAC9CC,aAAuC,GAAG;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE;AAAE,CAAC,EACxDC,WAAmB,GAAG,EAAE,KACH;EACrB,MAAMnB,QAAQ,GAAGV,iBAAiB,CAAC,CAAC;EACpC,MAAMgB,OAAO,GAAG,EAAE;EAClB,MAAMc,aAAa,GAAG,EAAE,CAAC,CAAC;;EAE1B,IAAI7B,GAAW;EACf,IAAIC,IAAY;EAChB,IAAI6B,YAA2B;EAC/B,IAAIC,eAA8B;;EAElC;EACA,MAAMC,cAAc,GAAGxB,WAAW,CAACP,IAAI,GAAGwB,aAAa,CAACC,CAAC,GAAIE,WAAW,GAAG,CAAE;EAC7E,MAAMK,cAAc,GAAGzB,WAAW,CAACR,GAAG,GAAGyB,aAAa,CAACE,CAAC,GAAIC,WAAW,GAAG,CAAE;EAE5E,QAAQJ,SAAS;IAChB,KAAK,KAAK;MACTxB,GAAG,GAAGiC,cAAc,GAAIL,WAAW,GAAG,CAAE,GAAGjB,eAAe,CAACG,MAAM,GAAGe,aAAa;MACjF5B,IAAI,GAAG+B,cAAc,GAAIrB,eAAe,CAACE,KAAK,GAAG,CAAE;MACnDiB,YAAY,GAAG;QAAEI,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAC;MACxDJ,eAAe,GAAG;QAAEG,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAC;MAC9D;IAED,KAAK,QAAQ;MACZnC,GAAG,GAAGiC,cAAc,GAAIL,WAAW,GAAG,CAAE,GAAGC,aAAa;MACxD5B,IAAI,GAAG+B,cAAc,GAAIrB,eAAe,CAACE,KAAK,GAAG,CAAE;MACnDiB,YAAY,GAAG;QAAEI,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAC;MAC3DJ,eAAe,GAAG;QAAEG,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAC;MAC3D;IAED,KAAK,MAAM;MACVnC,GAAG,GAAGiC,cAAc,GAAItB,eAAe,CAACG,MAAM,GAAG,CAAE;MACnDb,IAAI,GAAG+B,cAAc,GAAIJ,WAAW,GAAG,CAAE,GAAGjB,eAAe,CAACE,KAAK,GAAGgB,aAAa;MACjFC,YAAY,GAAG;QAAEI,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAO,CAAC;MACzDJ,eAAe,GAAG;QAAEG,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAC;MAC7D;IAED,KAAK,OAAO;MACXnC,GAAG,GAAGiC,cAAc,GAAItB,eAAe,CAACG,MAAM,GAAG,CAAE;MACnDb,IAAI,GAAG+B,cAAc,GAAIJ,WAAW,GAAG,CAAE,GAAGC,aAAa;MACzDC,YAAY,GAAG;QAAEI,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAC;MAC1DJ,eAAe,GAAG;QAAEG,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAO,CAAC;MAC5D;EACF;;EAEA;EACAlC,IAAI,GAAGoB,IAAI,CAACC,GAAG,CAACP,OAAO,EAAEM,IAAI,CAACe,GAAG,CAACnC,IAAI,EAAEQ,QAAQ,CAACP,KAAK,GAAGS,eAAe,CAACE,KAAK,GAAGE,OAAO,CAAC,CAAC;EAC1Ff,GAAG,GAAGqB,IAAI,CAACC,GAAG,CAACP,OAAO,EAAEM,IAAI,CAACe,GAAG,CAACpC,GAAG,EAAES,QAAQ,CAACJ,MAAM,GAAGM,eAAe,CAACG,MAAM,GAAGC,OAAO,CAAC,CAAC;EAE1F,OAAO;IACNf,GAAG,EAAEA,GAAG,GAAGG,MAAM,CAACkC,OAAO;IACzBpC,IAAI,EAAEA,IAAI,GAAGE,MAAM,CAACmC,OAAO;IAC3Bd,SAAS;IACTM,YAAY;IACZC;EACD,CAAC;AACF,CAAC;AA6GD,MAAMQ,cAAoC,GAAGA,CAAC;EAC1CC,QAAQ;EACRC,SAAS;EACTC,KAAK;EACLC,IAAI;EACJC,QAAQ;EACRC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,QAAQ;EACRC,WAAW;EACXC,UAAU;EACVC,eAAe;EACfC,QAAQ;EACRC,mBAAmB;EACnBC,eAAe;EACfC,YAAY;EACZC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,oBAAoB;EACpBC,oBAAoB;EACpBC,YAAY;EACZC,cAAc;EACdC,iBAAiB;EACjBC,kBAAkB;EAClBC,kBAAkB;EAClBC,kBAAkB;EACnBC;AAEH,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACL,MAAM;IACLC,cAAc;IACdC,gBAAgB;IAChBC,oBAAoB;IACpBC,eAAe;IACfC,QAAQ;IACRC,YAAY;IACZC,YAAY;IACZC,cAAc;IACdC,WAAW;IACXC,gBAAgB;IAChBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC;EACD,CAAC,GAAGpG,cAAc,CAAEqG,KAAkB,IAAKA,KAAK,CAAC;EACjD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjH,QAAQ,CAAqB,IAAI,CAAC;EAC5E;EACA,MAAM,CAACkH,aAAa,EAAEC,gBAAgB,CAAC,GAAGnH,QAAQ,CAAuC,IAAI,CAAC;EAC9F,MAAM,CAACoH,eAAe,EAAEC,kBAAkB,CAAC,GAAGrH,QAAQ,CAAyB,IAAI,CAAC;EACpF,MAAM,CAAC4B,eAAe,EAAE0F,kBAAkB,CAAC,GAAGtH,QAAQ,CAAkB;IAAE8B,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,CAAC;EACpG,MAAM,CAACwF,YAAY,EAAEC,eAAe,CAAC,GAAGxH,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC6C,WAAW,EAAE4E,cAAc,CAAC,GAAGzH,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM0H,UAAU,GAAGzH,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM0H,kBAAkB,GAAG1H,MAAM,CAAiB,IAAI,CAAC;EACvD,MAAM2H,QAAQ,GAAG3H,MAAM,CAAiB,IAAI,CAAC;EAC7C,IAAI4H,OAAY;EAChB,MAAMC,iBAAiB,GAAIC,KAAa,IAAyB;IAChE,MAAMC,MAAM,GAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,EAAEE,QAAQ,EAAE,IAAI,EAAEE,WAAW,CAACC,uBAAuB,EAAE,IAAI,CAAC;IAClG,MAAMC,IAAI,GAAGL,MAAM,CAACM,eAAe;IACnC,IAAID,IAAI,YAAYE,WAAW,EAAE;MAChC,OAAOF,IAAI;IACZ,CAAC,MAAM,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,aAAa,EAAE;MAC/B,OAAOH,IAAI,CAACG,aAAa,CAAC,CAAC;IAC5B,CAAC,MAAM;MACN,OAAO,IAAI;IACZ;EACD,CAAC;EACD,IAAIT,KAAU;EACd,IAAIhD,cAAc,EAAEgD,KAAK,GAAGhD,cAAc,aAAdA,cAAc,wBAAAO,qBAAA,GAAdP,cAAc,CAAE0D,SAAS,cAAAnD,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,uBAA9BA,sBAAA,CAAgCmD,WAAW;;EAErE;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG5I,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM6I,YAAY,GAAG5I,MAAM,CAAM,IAAI,CAAC;EACxC;EACA,MAAM6I,sBAAsB,GAAGA,CAACrH,WAAoB,EAAEoB,WAAmB,EAAEkG,OAAe,EAAEC,OAAe,KAAK;IAC/G,MAAMC,WAAW,GAAGxH,WAAW,CAACkB,CAAC,GAAGoG,OAAO;IAC3C,MAAMG,UAAU,GAAGzH,WAAW,CAACmB,CAAC,GAAGoG,OAAO;;IAE1C;IACA,MAAMG,cAAc,GAAGtG,WAAW,GAAG,CAAC,CAAC,CAAC;IACxC,MAAMuG,cAAc,GAAGvG,WAAW,GAAG,EAAE,CAAC,CAAC;;IAEzC,OAAO;MACN5B,GAAG,EAAEiI,UAAU,GAAG9H,MAAM,CAACkC,OAAO,GAAG8F,cAAc;MACjDlI,IAAI,EAAE+H,WAAW,GAAG7H,MAAM,CAACmC,OAAO,GAAG4F;IACtC,CAAC;EACF,CAAC;EACD;EACApJ,SAAS,CAAC,MAAM;IACf,IAAIgI,KAAK,EAAE;MACV,MAAMsB,OAAO,GAAGvB,iBAAiB,CAACC,KAAK,CAAC;MACxC,IAAIsB,OAAO,EAAE;QACZ,MAAMC,UAAU,GAAGC,0BAA0B,CAAC,CAAC;QAC/CjC,kBAAkB,CAACgC,UAAU,CAAC;QAC9BE,mBAAmB,CAACH,OAAO,EAAEC,UAAU,CAAC;MACzC;IACD;EACD,CAAC,EAAE,CAACvB,KAAK,CAAC,CAAC;EACXhI,SAAS,CAAC,MAAM;IACf,MAAMsJ,OAAO,GAAGvB,iBAAiB,CAACC,KAAK,CAAC;IACxC;IACA,IAAIsB,OAAO,EAAE,CACb;EACD,CAAC,EAAE,CAACtE,cAAc,CAAC,CAAC;EAEpBhF,SAAS,CAAC,MAAM;IAAA,IAAA0J,UAAA;IACf,MAAMJ,OAAO,GAAGvB,iBAAiB,CAACpE,SAAS,aAATA,SAAS,wBAAA+F,UAAA,GAAT/F,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAAuF,UAAA,uBAA5BA,UAAA,CAA8Bf,WAAW,CAAC;IAC5EzB,gBAAgB,CAACoC,OAAO,CAAC;IACzB,IAAIA,OAAO,EAAE;MACZA,OAAO,CAACK,KAAK,CAACC,eAAe,GAAG,gBAAgB;;MAEhD;MACA,MAAMC,IAAI,GAAGP,OAAO,CAACQ,qBAAqB,CAAC,CAAC;MAC5C1C,gBAAgB,CAAC;QAChBlG,GAAG,EAAE2I,IAAI,CAAC3I,GAAG,GAAGG,MAAM,CAACkC,OAAO;QAC9BpC,IAAI,EAAE0I,IAAI,CAAC1I,IAAI,GAAGE,MAAM,CAACmC;MAC1B,CAAC,CAAC;IACH;EACD,CAAC,EAAE,CAACG,SAAS,EAAEQ,WAAW,CAAC,CAAC;;EAE5B;EACA;EACA,MAAM,GAAG4F,eAAe,CAAC,GAAG9J,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM+J,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAI7D,gBAAgB,KAAK,MAAM,EAAE;MAChC,IAAIhC,WAAW,GAAGC,UAAU,EAAE;QAC7B8B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;QAC/BF,UAAU,CAAC,CAAC;QACZgG,eAAe,CAAC9F,WAAW,GAAGC,UAAU,CAAC;MAC1C;IACD,CAAC,MAAM;MACN8B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/B,MAAM+F,eAAe,GAAGhC,QAAQ,CAACiC,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACP,KAAK,CAACS,OAAO,GAAG,MAAM;QACtCF,eAAe,CAACG,MAAM,CAAC,CAAC;MACzB;IACD;EACD,CAAC;EAED,MAAMJ,eAAe,GAAIK,qBAA8B,IAAK;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IAC3D,OAAOV,qBAAqB,gBAC3BxJ,OAAA,CAAC2C,cAAc;MACd2B,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCH,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCzB,QAAQ,EAAEA,QAAS;MACnBsB,cAAc,EAAEA,cAAe;MAC/BrB,SAAS,EAAEA,SAAU;MACrBI,OAAO,EAAEA,OAAQ;MACjBC,UAAU,EAAEiH,cAAe;MAC3BhH,UAAU,EAAE+F,cAAe;MAC3BpG,KAAK,EAAEA,KAAM;MACbC,IAAI,EAAEA,IAAK;MACXC,QAAQ,EAAEA,QAAS;MACnBK,WAAW,EAAEA,WAAW,GAAG,CAAE;MAC7BC,UAAU,EAAEA,UAAW;MACvBC,eAAe,EAAEA,eAAgB;MACjCC,QAAQ,EAAEA,QAAS;MACnBC,mBAAmB,EAAES,cAAc,aAAdA,cAAc,wBAAAuF,sBAAA,GAAdvF,cAAc,CAAE0D,SAAS,cAAA6B,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BpG,WAAW,CAAC,cAAAqG,sBAAA,uBAAxCA,sBAAA,CAA0CU,mBAAoB;MACnF1G,eAAe,EAAEQ,cAAc,aAAdA,cAAc,wBAAAyF,sBAAA,GAAdzF,cAAc,CAAE0D,SAAS,cAAA+B,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BtG,WAAW,CAAC,cAAAuG,sBAAA,uBAAxCA,sBAAA,CAA0CS,eAAgB;MAC3E1G,YAAY,EACX,CAAAO,cAAc,aAAdA,cAAc,wBAAA2F,sBAAA,GAAd3F,cAAc,CAAE0D,SAAS,cAAAiC,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BxG,WAAW,CAAC,cAAAyG,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0CQ,aAAa,cAAAP,sBAAA,wBAAAC,uBAAA,GAAvDD,sBAAA,CAAyDQ,GAAG,CAAEC,OAAY,IACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,KAAM;QAC3C,GAAGA,MAAM;QACTC,WAAW,EAAEH,OAAO,CAACI,EAAE,CAAE;MAC1B,CAAC,CAAC,CACH,CAAC,cAAAZ,uBAAA,uBALDA,uBAAA,CAKGa,MAAM,CAAC,CAACC,GAAmB,EAAEC,IAAS,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,KAAI,EACvE;MACDnH,eAAe,EAAEA,eAAgB;MACjCC,gBAAgB,EAAEA,gBAAiB;MACnCC,WAAW,EAAEA,WAAY;MACzBG,YAAY,EAAEA,YAAa;MAC3BE,iBAAiB,EAAE,CAAAD,cAAc,aAAdA,cAAc,wBAAA+F,uBAAA,GAAd/F,cAAc,CAAE0D,SAAS,cAAAqC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B5G,WAAW,GAAG,CAAC,CAAC,cAAA6G,uBAAA,uBAA5CA,uBAAA,CAA8Ce,OAAO,KAAI,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,GACC,IAAI;EACT,CAAC;EAED,MAAMlB,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAI9G,WAAW,GAAG,CAAC,EAAE;MACpB+B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/BH,UAAU,CAAC,CAAC;IACb;EACD,CAAC;EACDhE,SAAS,CAAC,MAAM;IACf,IAAI+E,YAAY,EAAE;MACjBgF,eAAe,CAAC,IAAI,CAAC;IACtB,CAAC,MAAM;MACNA,eAAe,CAAC,KAAK,CAAC;IACvB;EACD,CAAC,EAAE,CAAChF,YAAY,CAAC,CAAC;EAClB;EACA,MAAMqH,4BAA4B,GACjCC,QAAgB,IACqD;IACrE,QAAQA,QAAQ;MACf,KAAK,UAAU;QACd,OAAO;UACNrJ,YAAY,EAAE;YAAEI,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAO,CAAC;UACrDJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,WAAW;QACf,OAAO;UACNL,YAAY,EAAE;YAAEI,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACtDJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNL,YAAY,EAAE;YAAEI,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ;QACzD,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNL,YAAY,EAAE;YAAEI,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNL,YAAY,EAAE;YAAEI,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,YAAY;QAChB,OAAO;UACNL,YAAY,EAAE;YAAEI,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAS,CAAC;UACvDJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNL,YAAY,EAAE;YAAEI,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNL,YAAY,EAAE;YAAEI,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNL,YAAY,EAAE;YAAEI,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF;QACC,OAAO;UACNL,YAAY,EAAE;YAAEI,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DJ,eAAe,EAAE;YAAEG,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;IACH;EACD,CAAC;;EAED;EACA,MAAM;IAAEL,YAAY;IAAEC;EAAgB,CAAC,GAAGoE,eAAe,GACtD;IAAErE,YAAY,EAAEqE,eAAe,CAACrE,YAAY;IAAEC,eAAe,EAAEoE,eAAe,CAACpE;EAAgB,CAAC,GAChGmJ,4BAA4B,CAAC,CAAAzH,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2H,QAAQ,KAAI,eAAe,CAAC;EAE9E,MAAMC,SAAS,GAAG;IACjBC,UAAU,EAAEjI,mBAAmB,aAAnBA,mBAAmB,gBAAAkB,qBAAA,GAAnBlB,mBAAmB,CAAEkI,cAAc,cAAAhH,qBAAA,eAAnCA,qBAAA,CAAqCiH,IAAI,GAAG,MAAM,GAAG,QAAQ;IACzEC,SAAS,EAAEpI,mBAAmB,aAAnBA,mBAAmB,gBAAAmB,sBAAA,GAAnBnB,mBAAmB,CAAEkI,cAAc,cAAA/G,sBAAA,eAAnCA,sBAAA,CAAqCkH,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5EC,KAAK,EAAE,CAAAtI,mBAAmB,aAAnBA,mBAAmB,wBAAAoB,sBAAA,GAAnBpB,mBAAmB,CAAEkI,cAAc,cAAA9G,sBAAA,uBAAnCA,sBAAA,CAAqCmH,SAAS,KAAI,SAAS;IAClEC,SAAS,EAAE,CAAAxI,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEyI,SAAS,KAAI;EAC9C,CAAC;;EAED;;EAEA,MAAMC,iBAAiB,GAAIC,OAAe,IAAK;IAC9C;IACA,OAAO;MACNC,MAAM,EAAED,OAAO,CAACE,OAAO,CAAC,qCAAqC,EAAE,CAACC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;QACtF,OAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE;MAC1C,CAAC;IACF,CAAC;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC5B,MAAMC,QAAQ,GAAGlJ,eAAe,IAAIA,eAAe,CAACmJ,MAAM,GAAG,CAAC,IAC7DnJ,eAAe,CAACoJ,IAAI,CAAEC,IAAS,IAC9BA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,IAAKA,GAAG,CAACC,GAAG,CAChE,CAAC;IAEF,MAAMC,OAAO,GAAG1J,mBAAmB,IAAIA,mBAAmB,CAACoJ,MAAM,GAAG,CAAC,IACpEpJ,mBAAmB,CAACqJ,IAAI,CAAEM,KAAU,IAAKA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAEjF,MAAMC,UAAU,GAAG5J,YAAY,IAAIA,YAAY,CAACkJ,MAAM,GAAG,CAAC;IAE1D,OAAOU,UAAU,IAAI,CAACX,QAAQ,IAAI,CAACO,OAAO;EAC3C,CAAC;;EAED;EACA,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACzB,MAAMZ,QAAQ,GAAGlJ,eAAe,IAAIA,eAAe,CAACmJ,MAAM,GAAG,CAAC,IAC7DnJ,eAAe,CAACoJ,IAAI,CAAEC,IAAS,IAC9BA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,IAAKA,GAAG,CAACC,GAAG,CAChE,CAAC;IAEF,MAAMC,OAAO,GAAG1J,mBAAmB,IAAIA,mBAAmB,CAACoJ,MAAM,GAAG,CAAC,IACpEpJ,mBAAmB,CAACqJ,IAAI,CAAEM,KAAU,IAAKA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAEjF,MAAMC,UAAU,GAAG5J,YAAY,IAAIA,YAAY,CAACkJ,MAAM,GAAG,CAAC;IAE1D,OAAOM,OAAO,IAAI,CAACP,QAAQ,IAAI,CAACW,UAAU;EAC3C,CAAC;;EAED;EACA,MAAM7E,0BAA0B,GAAGA,CAAA,KAAuB;IAAA,IAAA+E,oBAAA,EAAAC,sBAAA;IACzD;IACA,IAAI7J,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE8J,KAAK,IAAI,CAAChB,cAAc,CAAC,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC,EAAE;MACnE,MAAMvM,KAAK,GAAG2M,QAAQ,CAAC/J,gBAAgB,CAAC8J,KAAK,CAAC;MAC9C,OAAO;QACN1M,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAE2M,qBAAqB,CAAC5M,KAAK;MACpC,CAAC;IACF;;IAEA;IACA,IAAI0L,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,EAAE;MAAA,IAAAM,mBAAA,EAAAC,qBAAA;MACtC,MAAMC,YAAY,GAAG,EAAAF,mBAAA,GAAAjH,UAAU,CAACoH,OAAO,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAoBI,WAAW,KAAI,CAAC;MACzD,MAAMC,WAAW,GAAG,EAAAJ,qBAAA,GAAAjH,kBAAkB,CAACmH,OAAO,cAAAF,qBAAA,uBAA1BA,qBAAA,CAA4BG,WAAW,KAAI,CAAC;MAChE,MAAMjN,KAAK,GAAGQ,IAAI,CAACC,GAAG,CAACsM,YAAY,EAAEG,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;;MAExD,OAAO;QACNlN,KAAK,EAAEA,KAAK,GAAG,EAAE;QAAE;QACnBC,MAAM,EAAE2M,qBAAqB,CAAC5M,KAAK,GAAG,EAAE;MACzC,CAAC;IACF;;IAEA;IACA,MAAM+M,YAAY,GAAG,EAAAP,oBAAA,GAAA5G,UAAU,CAACoH,OAAO,cAAAR,oBAAA,uBAAlBA,oBAAA,CAAoBS,WAAW,KAAI,CAAC;IACzD,MAAMC,WAAW,GAAG,EAAAT,sBAAA,GAAA5G,kBAAkB,CAACmH,OAAO,cAAAP,sBAAA,uBAA1BA,sBAAA,CAA4BQ,WAAW,KAAI,CAAC;;IAEhE;IACA,MAAME,YAAY,GAAG3M,IAAI,CAACC,GAAG,CAACsM,YAAY,EAAEG,WAAW,CAAC;;IAExD;IACA,MAAME,WAAW,GAAGD,YAAY,GAAG,EAAE,CAAC,CAAC;;IAEvC;IACA,MAAME,QAAQ,GAAG,GAAG,CAAC,CAAC;IACtB,MAAMC,QAAQ,GAAG9M,IAAI,CAACe,GAAG,CAAC,GAAG,EAAEjC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;;IAEzD,MAAMgO,UAAU,GAAG/M,IAAI,CAACC,GAAG,CAAC4M,QAAQ,EAAE7M,IAAI,CAACe,GAAG,CAAC6L,WAAW,EAAEE,QAAQ,CAAC,CAAC;IAEtE,OAAO;MACNtN,KAAK,EAAEuN,UAAU;MACjBtN,MAAM,EAAE2M,qBAAqB,CAACW,UAAU;IACzC,CAAC;EACF,CAAC;;EAED;EACA,MAAMX,qBAAqB,GAAI5M,KAAa,IAAa;IACxD,IAAIC,MAAM,GAAG,CAAC;;IAEd;IACA,IAAIwC,eAAe,IAAIA,eAAe,CAACmJ,MAAM,GAAG,CAAC,EAAE;MAClDnJ,eAAe,CAAC+K,OAAO,CAAEC,SAAc,IAAK;QAC3C,IAAIA,SAAS,CAAC1B,WAAW,IAAI0B,SAAS,CAAC1B,WAAW,CAACH,MAAM,GAAG,CAAC,EAAE;UAC9D6B,SAAS,CAAC1B,WAAW,CAACyB,OAAO,CAAExB,GAAQ,IAAK;YAC3C/L,MAAM,IAAI0M,QAAQ,CAACX,GAAG,CAAC0B,aAAa,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;UACtD,CAAC,CAAC;QACH;MACD,CAAC,CAAC;IACH;;IAEA;IACA,IAAIlL,mBAAmB,IAAIA,mBAAmB,CAACoJ,MAAM,GAAG,CAAC,EAAE;MAC1DpJ,mBAAmB,CAACgL,OAAO,CAAErB,KAAU,IAAK;QAC3C,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAC3C;UACA,MAAMsB,UAAU,GAAGxB,KAAK,CAACC,IAAI,CAACR,MAAM;UACpC,MAAMgC,cAAc,GAAGpN,IAAI,CAACqN,IAAI,CAACF,UAAU,IAAI3N,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;UAC7DC,MAAM,IAAIO,IAAI,CAACC,GAAG,CAACmN,cAAc,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAC9C;MACD,CAAC,CAAC;IACH;;IAEA;IACA,IAAIlL,YAAY,IAAIA,YAAY,CAACkJ,MAAM,GAAG,CAAC,EAAE;MAC5C3L,MAAM,IAAI,EAAE,CAAC,CAAC;IACf;;IAEA;IACA,IAAI6N,cAAc,IAAIzL,UAAU,GAAG,CAAC,EAAE;MACrCpC,MAAM,IAAI,EAAE;IACb;;IAEA;IACAA,MAAM,IAAI,EAAE;;IAEZ;IACA,MAAM8N,SAAS,GAAG,GAAG;IACrB,MAAMC,SAAS,GAAGxN,IAAI,CAACe,GAAG,CAAC,GAAG,EAAEjC,MAAM,CAACG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;;IAE3D,OAAOe,IAAI,CAACC,GAAG,CAACsN,SAAS,EAAEvN,IAAI,CAACe,GAAG,CAACtB,MAAM,EAAE+N,SAAS,CAAC,CAAC;EACxD,CAAC;;EAID;EACA/P,SAAS,CAAC,MAAM;IACf;IACAgQ,qBAAqB,CAAC,MAAM;MAC3B,MAAMC,aAAa,GAAGzG,0BAA0B,CAAC,CAAC;MAClDjC,kBAAkB,CAAC0I,aAAa,CAAC;MACjCxI,eAAe,CAAC,GAAGwI,aAAa,CAAClO,KAAK,IAAI,CAAC;;MAE3C;MACA,IAAIiG,KAAK,EAAE;QACV,MAAMsB,OAAO,GAAGvB,iBAAiB,CAACC,KAAK,CAAC;QACxC,IAAIsB,OAAO,EAAE;UACZG,mBAAmB,CAACH,OAAO,EAAE2G,aAAa,CAAC;QAC5C;MACD;IACD,CAAC,CAAC;EACH,CAAC,EAAE,CAAC1L,mBAAmB,EAAEC,eAAe,EAAEC,YAAY,EAAEN,WAAW,EAAE6D,KAAK,CAAC,CAAC;;EAE5E;EACA,MAAMyB,mBAAmB,GAAGA,CAACH,OAAoB,EAAEC,UAA2B,KAAK;IAAA,IAAA2G,qBAAA,EAAAC,sBAAA;IAClF,MAAMtG,IAAI,GAAGP,OAAO,CAACQ,qBAAqB,CAAC,CAAC;IAC5C,MAAMsG,eAAe,GAAGvJ,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAA8J,qBAAA,GAApB9J,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAA+L,qBAAA,eAAvCA,qBAAA,CAAyCG,QAAQ,GAC5GjK,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkM,QAAQ,IAAAF,sBAAA,GAC9C/J,oBAAoB,CAAC,CAAC,CAAC,cAAA+J,sBAAA,uBAAvBA,sBAAA,CAAyBE,QAAQ;IAEpC,MAAMrH,OAAO,GAAGsH,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;IAC7D,MAAMtH,OAAO,GAAGqH,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;;IAE7D;IACA,MAAM9N,SAAS,GAAGd,yBAAyB,CAACiI,IAAI,EAAEN,UAAU,CAAC;;IAE7D;IACA,MAAMkH,UAAU,GAAGhO,gCAAgC,CAClDoH,IAAI,EACJN,UAAU,EACV7G,SAAS,EACT;MAAEE,CAAC,EAAEoG,OAAO;MAAEnG,CAAC,EAAEoG;IAAQ,CAC1B,CAAC;IAED3B,kBAAkB,CAACmJ,UAAU,CAAC;IAC9BrJ,gBAAgB,CAAC;MAChBlG,GAAG,EAAEuP,UAAU,CAACvP,GAAG;MACnBC,IAAI,EAAEsP,UAAU,CAACtP;IAClB,CAAC,CAAC;EACH,CAAC;;EAED;EACAnB,SAAS,CAAC,MAAM;IACf,IAAIgI,KAAK,IAAIlF,WAAW,EAAE;MACzB,MAAMwG,OAAO,GAAGvB,iBAAiB,CAACC,KAAK,CAAC;MACxC,IAAIsB,OAAO,EAAE;QACZG,mBAAmB,CAACH,OAAO,EAAEzH,eAAe,CAAC;MAC9C;IACD;EACD,CAAC,EAAE,CAACiB,WAAW,EAAEkF,KAAK,EAAE5B,oBAAoB,EAAEvE,eAAe,CAAC,CAAC;;EAE/D;EACA7B,SAAS,CAAC,MAAM;IACf,MAAM0Q,oBAAoB,GAAGA,CAAA,KAAM;MAClC,IAAI1I,KAAK,EAAE;QACV,MAAMsB,OAAO,GAAGvB,iBAAiB,CAACC,KAAK,CAAC;QACxC,IAAIsB,OAAO,EAAE;UACZG,mBAAmB,CAACH,OAAO,EAAEzH,eAAe,CAAC;QAC9C;MACD;IACD,CAAC;IAEDR,MAAM,CAACsP,gBAAgB,CAAC,QAAQ,EAAED,oBAAoB,CAAC;IACvDrP,MAAM,CAACsP,gBAAgB,CAAC,QAAQ,EAAED,oBAAoB,CAAC;IACvD,OAAO,MAAM;MACZrP,MAAM,CAACuP,mBAAmB,CAAC,QAAQ,EAAEF,oBAAoB,CAAC;MAC1DrP,MAAM,CAACuP,mBAAmB,CAAC,QAAQ,EAAEF,oBAAoB,CAAC;IAC3D,CAAC;EACF,CAAC,EAAE,CAAC1I,KAAK,EAAEnG,eAAe,CAAC,CAAC;EAE5B,MAAMgP,cAAc,GAAGpM,YAAY,CAACkH,MAAM,CAAC,CAACC,GAAQ,EAAEJ,MAAW,KAAK;IACrE,MAAMsF,WAAW,GAAGtF,MAAM,CAACC,WAAW,IAAI,SAAS,CAAC,CAAC;IACrD,IAAI,CAACG,GAAG,CAACkF,WAAW,CAAC,EAAE;MACtBlF,GAAG,CAACkF,WAAW,CAAC,GAAG,EAAE;IACtB;IACAlF,GAAG,CAACkF,WAAW,CAAC,CAACC,IAAI,CAACvF,MAAM,CAAC;IAC7B,OAAOI,GAAG;EACX,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMoF,WAAW,GAAG;IACnB3E,QAAQ,EAAE,CAAA1H,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2H,QAAQ,KAAI,eAAe;IACvD2E,YAAY,EAAE,CAAAtM,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuM,MAAM,KAAI,KAAK;IAC/CC,WAAW,EAAE,CAAAxM,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyM,UAAU,KAAI,KAAK;IAClDC,WAAW,EAAE,CAAA1M,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2M,WAAW,KAAI,OAAO;IACrDC,WAAW,EAAE,OAAO;IACpB3H,eAAe,EAAE,CAAAjF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6M,eAAe,KAAI,OAAO;IAC7D;IACAnC,QAAQ,EAAG5B,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAI,iBAAiB,GAC7D,OAAOzM,eAAe,CAACE,KAAK,sBAAsB;IACtDA,KAAK,EAAG0L,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAI,iBAAiB,GAC1D,OAAOzM,eAAe,CAACE,KAAK,sBAAsB;IACtD;IACAgO,SAAS,EAAE,OAAOlO,eAAe,CAACG,MAAM;EACzC,CAAC;EACD,MAAMyP,aAAa,GAAG,EAAA7L,gBAAA,GAAApB,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAhCD,gBAAA,CAAkCkI,WAAW,cAAAjI,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgD1B,WAAW,GAAG,CAAC,CAAC,cAAA2B,sBAAA,uBAAhEA,sBAAA,CAAkE2J,aAAa,KAAI,MAAM;EAC/G,MAAMiC,kBAAkB,GAAIC,MAAW,IAAK;IAC3C,IAAIA,MAAM,CAACC,MAAM,KAAK,UAAU,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,SAAS,EAAE;MAC5F,MAAMC,SAAS,GAAGF,MAAM,CAACG,SAAS;MAClC,IAAIH,MAAM,CAACI,WAAW,KAAK,UAAU,EAAE;QACtC;QACA1Q,MAAM,CAAC2Q,QAAQ,CAACC,IAAI,GAAGJ,SAAS;MACjC,CAAC,MAAM;QACN;QACAxQ,MAAM,CAAC6Q,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACxD;IACD,CAAC,MAAM;MACN,IACCF,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACI,WAAW,IAAI,UAAU,IAChCJ,MAAM,CAACI,WAAW,IAAI,UAAU,EAC/B;QACD9G,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACN0G,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACI,WAAW,IAAI,MAAM,IAC5BJ,MAAM,CAACI,WAAW,IAAI,MAAM,EAC3B;QACD/H,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACN2H,MAAM,CAACC,MAAM,IAAI,SAAS,IAC1BD,MAAM,CAACI,WAAW,IAAI,SAAS,EAC9B;QAAA,IAAAI,uBAAA,EAAAC,uBAAA;QACD;QACAlM,cAAc,CAAC,CAAC,CAAC;QACjB;QACA,IAAIlB,cAAc,aAAdA,cAAc,gBAAAmN,uBAAA,GAAdnN,cAAc,CAAE0D,SAAS,cAAAyJ,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,eAA9BA,uBAAA,CAAgCzJ,WAAW,EAAE;UAChD,MAAM0J,gBAAgB,GAAGtK,iBAAiB,CAAC/C,cAAc,CAAC0D,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UACnF,IAAI0J,gBAAgB,EAAE;YACrBA,gBAAgB,CAACC,cAAc,CAAC;cAAEC,QAAQ,EAAE;YAAS,CAAC,CAAC;UACxD;QACD;MACD;IACD;IACAxI,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD/J,SAAS,CAAC,MAAM;IAAA,IAAAwS,WAAA,EAAAC,mBAAA;IACf,IAAI9O,SAAS,aAATA,SAAS,gBAAA6O,WAAA,GAAT7O,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAAqO,WAAA,gBAAAC,mBAAA,GAA5BD,WAAA,CAA8BzG,OAAO,cAAA0G,mBAAA,eAArCA,mBAAA,CAAuCC,aAAa,EAAE;MACzD;MACAjM,cAAc,CAAC,IAAI,CAAC;IACrB;EACD,CAAC,EAAE,CAAC9C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,EAAEA,WAAW,EAAEsC,cAAc,CAAC,CAAC;;EAE/D;EACAzG,SAAS,CAAC,MAAM;IACf,IAAIoF,kBAAkB,EAAE;MAAA,IAAAuN,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAM5C,eAAe,GAAGvJ,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAuM,sBAAA,GAApBvM,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAwO,sBAAA,eAAvCA,sBAAA,CAAyCtC,QAAQ,GAC5GjK,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkM,QAAQ,IAAAuC,sBAAA,GAC9CxM,oBAAoB,CAAC,CAAC,CAAC,cAAAwM,sBAAA,uBAAvBA,sBAAA,CAAyBvC,QAAQ;MACpC,MAAM4C,WAAW,GAAGpM,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAA6N,uBAAA,GAAd7N,cAAc,CAAE0D,SAAS,cAAAmK,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B1O,WAAW,GAAG,CAAC,CAAC,cAAA2O,uBAAA,uBAA5CA,uBAAA,CAA8C/G,OAAO,GACrD/G,cAAc,aAAdA,cAAc,wBAAA+N,uBAAA,GAAd/N,cAAc,CAAE0D,SAAS,cAAAqK,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgCjH,OAAO;;MAE1C;MACA;MACA,IAAIqE,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEsC,aAAa,EAAE;QACnC;QACAjM,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACrB,kBAAkB,EAAEgB,oBAAoB,CAAC,CAAC;;EAE9C;EACApG,SAAS,CAAC,MAAM;IACf,IAAIqF,kBAAkB,EAAE;MAAA,IAAA6N,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAMnD,eAAe,GAAGvJ,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAA8M,sBAAA,GAApB9M,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAA+O,sBAAA,eAAvCA,sBAAA,CAAyC7C,QAAQ,GAC5GjK,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkM,QAAQ,IAAA8C,sBAAA,GAC9C/M,oBAAoB,CAAC,CAAC,CAAC,cAAA+M,sBAAA,uBAAvBA,sBAAA,CAAyB9C,QAAQ;MACpC,MAAM4C,WAAW,GAAGpM,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAAoO,uBAAA,GAAdpO,cAAc,CAAE0D,SAAS,cAAA0K,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BjP,WAAW,GAAG,CAAC,CAAC,cAAAkP,uBAAA,uBAA5CA,uBAAA,CAA8CtH,OAAO,GACrD/G,cAAc,aAAdA,cAAc,wBAAAsO,uBAAA,GAAdtO,cAAc,CAAE0D,SAAS,cAAA4K,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgCxH,OAAO;;MAE1C;MACA,IAAIqE,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEsC,aAAa,EAAE;QACnC;QACAjM,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACpB,kBAAkB,EAAEe,oBAAoB,CAAC,CAAC;;EAE9C;EACApG,SAAS,CAAC,MAAM;IACf,MAAMwT,iBAAiB,GAAIC,CAAa,IAAK;MAC5C,MAAMC,cAAc,GAAGxL,QAAQ,CAACiC,cAAc,CAAC,cAAc,CAAC;;MAE9D;MACA,IAAIuJ,cAAc,IAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,EAAE;QAChE;MACD;;MAEA;MACA;IACD,CAAC;IAED1L,QAAQ,CAACyI,gBAAgB,CAAC,OAAO,EAAE6C,iBAAiB,CAAC;IAErD,OAAO,MAAM;MACZtL,QAAQ,CAAC0I,mBAAmB,CAAC,OAAO,EAAE4C,iBAAiB,CAAC;IACzD,CAAC;EACF,CAAC,EAAE,CAACpN,oBAAoB,CAAC,CAAC;EAC1B;EACApG,SAAS,CAAC,MAAM;IACf,MAAM6T,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,IAAIlM,UAAU,CAACoH,OAAO,EAAE;QACvB;QACApH,UAAU,CAACoH,OAAO,CAACpF,KAAK,CAAC3H,MAAM,GAAG,MAAM;QACxC,MAAM8R,aAAa,GAAGnM,UAAU,CAACoH,OAAO,CAACgF,YAAY;QACrD,MAAMC,eAAe,GAAG,GAAG,CAAC,CAAC;QAC7B,MAAMC,YAAY,GAAGH,aAAa,GAAGE,eAAe;QAGpDnL,iBAAiB,CAACoL,YAAY,CAAC;;QAE/B;QACA,IAAInL,YAAY,CAACiG,OAAO,EAAE;UACzB;UACA,IAAIjG,YAAY,CAACiG,OAAO,CAACmF,YAAY,EAAE;YACtCpL,YAAY,CAACiG,OAAO,CAACmF,YAAY,CAAC,CAAC;UACpC;UACA;UACAC,UAAU,CAAC,MAAM;YAChB,IAAIrL,YAAY,CAACiG,OAAO,IAAIjG,YAAY,CAACiG,OAAO,CAACmF,YAAY,EAAE;cAC9DpL,YAAY,CAACiG,OAAO,CAACmF,YAAY,CAAC,CAAC;YACpC;UACD,CAAC,EAAE,EAAE,CAAC;QACP;MACD;IACD,CAAC;IAGDL,iBAAiB,CAAC,CAAC;IAGnB,MAAMO,QAAQ,GAAG,CAChBD,UAAU,CAACN,iBAAiB,EAAE,EAAE,CAAC,EACjCM,UAAU,CAACN,iBAAiB,EAAE,GAAG,CAAC,EAClCM,UAAU,CAACN,iBAAiB,EAAE,GAAG,CAAC,EAClCM,UAAU,CAACN,iBAAiB,EAAE,GAAG,CAAC,CAClC;IAGD,IAAIQ,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAI3M,UAAU,CAACoH,OAAO,IAAI1N,MAAM,CAACkT,cAAc,EAAE;MAChDF,cAAc,GAAG,IAAIE,cAAc,CAAC,MAAM;QACzCJ,UAAU,CAACN,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFQ,cAAc,CAACG,OAAO,CAAC7M,UAAU,CAACoH,OAAO,CAAC;IAC3C;IAGA,IAAIpH,UAAU,CAACoH,OAAO,IAAI1N,MAAM,CAACoT,gBAAgB,EAAE;MAClDH,gBAAgB,GAAG,IAAIG,gBAAgB,CAAC,MAAM;QAC7CN,UAAU,CAACN,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFS,gBAAgB,CAACE,OAAO,CAAC7M,UAAU,CAACoH,OAAO,EAAE;QAC5C2F,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZT,QAAQ,CAAC7E,OAAO,CAACuF,YAAY,CAAC;MAC9B,IAAIT,cAAc,EAAE;QACnBA,cAAc,CAACU,UAAU,CAAC,CAAC;MAC5B;MACA,IAAIT,gBAAgB,EAAE;QACrBA,gBAAgB,CAACS,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAAC5Q,WAAW,CAAC,CAAC;EACjB;EACA;;EAEA,SAAS6Q,YAAYA,CAACC,SAAiB,EAAE;IACxC,QAAQA,SAAS;MAChB,KAAK,OAAO;QACX,OAAO,YAAY;MACpB,KAAK,KAAK;QACT,OAAO,UAAU;MAClB,KAAK,QAAQ;MACb;QACC,OAAO,QAAQ;IACjB;EACD;EACA,MAAMC,iBAAiB,GAAGA,CAAC7I,QAAgB,GAAG,eAAe,KAAK;IACjE,QAAQA,QAAQ;MACf,KAAK,aAAa;QACjB,OAAO;UAAEnL,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,cAAc;QAClB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,aAAa;QACjB,OAAO;UAAEA,GAAG,EAAE4C,QAAQ,KAAK,EAAE,GAAG,gBAAgB,GAAG;QAAiB,CAAC;MACtE,KAAK,cAAc;QAClB,OAAO;UAAE5C,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,UAAU;QACd,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,WAAW;QACf,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,YAAY;QAChB,OAAO;UAAEA,GAAG,EAAE;QAAgB,CAAC;MAChC;QACC,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;IAClC;EACD,CAAC;;EAEA;EACD,MAAMiU,kBAAkB,GAAGA,CAACC,QAAgB,EAAEhF,eAAoB,EAAE6C,WAAgB,KAAK;IACxF,IAAIpM,oBAAoB,KAAK,SAAS,EAAE;MACvC;MACA,QAAQuO,QAAQ;QACf,KAAK,gBAAgB;UACpB,OAAO,CAAAnC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoC,cAAc,MAAKC,SAAS,GAAGrC,WAAW,CAACoC,cAAc,GAAGjF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiF,cAAc;QAChH,KAAK,eAAe;UACnB;UACA,OAAO,CAAApC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsC,4BAA4B,MAAKD,SAAS,GAAGrC,WAAW,CAACsC,4BAA4B,GAAGnF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmF,4BAA4B;QAC1J,KAAK,UAAU;UACd,OAAO,CAAAtC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuC,QAAQ,MAAKF,SAAS,GAAGrC,WAAW,CAACuC,QAAQ,GAAGpF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoF,QAAQ;QAC9F,KAAK,eAAe;UACnB,OAAO,CAAAvC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEP,aAAa,MAAK4C,SAAS,GAAGrC,WAAW,CAACP,aAAa,GAAGtC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsC,aAAa;QAC7G;UACC,OAAOtC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGgF,QAAQ,CAAC;MACpC;IACD,CAAC,MAAM;MACN;MACA,IAAIA,QAAQ,KAAK,eAAe,EAAE;QACjC,OAAOhF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmF,4BAA4B;MACrD;MACA,OAAOnF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGgF,QAAQ,CAAC;IACnC;EACD,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAC3N,OAAY,EAAEsI,eAAoB,EAAE6C,WAAgB,EAAE9R,IAAS,EAAED,GAAQ,KAAK;IACzG4G,OAAO,CAAC6B,KAAK,CAAC0C,QAAQ,GAAG,UAAU;IACnCvE,OAAO,CAAC6B,KAAK,CAACxI,IAAI,GAAG,GAAGA,IAAI,IAAI;IAChC2G,OAAO,CAAC6B,KAAK,CAACzI,GAAG,GAAG,GAAGA,GAAG,IAAI;IAC9B4G,OAAO,CAAC6B,KAAK,CAAC5H,KAAK,GAAG,GAAGqO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsF,IAAI,IAAI,CAAC,CAAC;IACpD5N,OAAO,CAAC6B,KAAK,CAAC3H,MAAM,GAAG,GAAGoO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsF,IAAI,IAAI;IACnD5N,OAAO,CAAC6B,KAAK,CAACC,eAAe,GAAGwG,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuF,KAAK;IACtD7N,OAAO,CAAC6B,KAAK,CAACsH,YAAY,GAAG,KAAK;IAClCnJ,OAAO,CAAC6B,KAAK,CAACiM,MAAM,GAAG,iBAAiB,CAAC,CAAC;IAC1C9N,OAAO,CAAC6B,KAAK,CAACkM,UAAU,GAAG,MAAM;IACjC/N,OAAO,CAAC6B,KAAK,CAACmM,aAAa,GAAG,MAAM,CAAC,CAAC;IACtChO,OAAO,CAACiO,SAAS,GAAG,EAAE;IAEtB,IAAI,CAAA3F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4F,IAAI,MAAK,MAAM,IAAI,CAAA5F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4F,IAAI,MAAK,UAAU,EAAE;MAC7E,MAAMC,QAAQ,GAAG/N,QAAQ,CAACgO,aAAa,CAAC,MAAM,CAAC;MAC/CD,QAAQ,CAACE,SAAS,GAAG/F,eAAe,CAAC4F,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;MAChEC,QAAQ,CAACtM,KAAK,CAACkD,KAAK,GAAG,OAAO;MAC9BoJ,QAAQ,CAACtM,KAAK,CAACyM,QAAQ,GAAG,MAAM;MAChCH,QAAQ,CAACtM,KAAK,CAAC6C,UAAU,GAAG,MAAM;MAClCyJ,QAAQ,CAACtM,KAAK,CAACgD,SAAS,GAAGyD,eAAe,CAAC4F,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAQ;MAChFC,QAAQ,CAACtM,KAAK,CAACS,OAAO,GAAG,MAAM;MAC/B6L,QAAQ,CAACtM,KAAK,CAAC0M,UAAU,GAAG,QAAQ;MACpCJ,QAAQ,CAACtM,KAAK,CAAC2M,cAAc,GAAG,QAAQ;MACxCL,QAAQ,CAACtM,KAAK,CAAC5H,KAAK,GAAG,MAAM;MAC7BkU,QAAQ,CAACtM,KAAK,CAAC3H,MAAM,GAAG,MAAM;MAC9B8F,OAAO,CAACyO,WAAW,CAACN,QAAQ,CAAC;IAC9B;;IAEA;IACA;IACA,MAAMO,qBAAqB,GAAGrB,kBAAkB,CAAC,gBAAgB,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;IAChG,MAAMwD,WAAW,GAAG5P,oBAAoB,KAAK,SAAS,GAClD2P,qBAAqB,KAAK,KAAK,IAAI,CAAC1O,OAAO,CAAC4O,aAAa,GACzDtG,eAAe,IAAIzJ,gBAAgB,IAAI,CAACmB,OAAO,CAAC4O,aAAc;IAElE,IAAID,WAAW,EAAE;MACP3O,OAAO,CAAC6O,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MACxC9O,OAAO,CAAC6O,SAAS,CAACtM,MAAM,CAAC,yBAAyB,CAAC;IACvD,CAAC,MAAM;MACHvC,OAAO,CAAC6O,SAAS,CAACtM,MAAM,CAAC,iBAAiB,CAAC;MAC3CvC,OAAO,CAAC6O,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACpD;;IAEN;IACA9O,OAAO,CAAC6B,KAAK,CAACS,OAAO,GAAG,MAAM;IAC9BtC,OAAO,CAAC6B,KAAK,CAACmM,aAAa,GAAG,MAAM;;IAEpC;IACA;IACA;IACA,MAAMe,aAAa,GAAG1B,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;IACvF,IAAI4D,aAAa,EAAE;MAClBpQ,cAAc,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACN;MACA;IAAA;;IAGD;IACA;IACA,IAAI,CAACqB,OAAO,CAACgP,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,MAAMC,UAAU,GAAGjP,OAAO,CAACkP,SAAS,CAAC,IAAI,CAAgB;MACzD;MACA,IAAIlP,OAAO,CAAC4O,aAAa,KAAKpB,SAAS,EAAE;QAC9ByB,UAAU,CAASL,aAAa,GAAG5O,OAAO,CAAC4O,aAAa;MAC7D;MACN,IAAI5O,OAAO,CAACmP,UAAU,EAAE;QACvBnP,OAAO,CAACmP,UAAU,CAACC,YAAY,CAACH,UAAU,EAAEjP,OAAO,CAAC;QACpDA,OAAO,GAAGiP,UAAU;MACrB;IACD;;IAEA;IACAjP,OAAO,CAAC6B,KAAK,CAACmM,aAAa,GAAG,MAAM;;IAEpC;IACA,MAAMqB,QAAQ,GAAGhC,kBAAkB,CAAC,UAAU,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;IAC7E,MAAMmE,WAAW,GAAI3D,CAAQ,IAAK;MACjCA,CAAC,CAAC4D,eAAe,CAAC,CAAC;MACnBC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAIJ,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACA1Q,cAAc,CAAC,IAAI,CAAC;;QAEpB;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACA,MAAMsS,oBAAoB,GAAGrC,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;QAC9F,IAAIuE,oBAAoB,EAAE;UACzB1P,OAAO,CAAC6O,SAAS,CAACtM,MAAM,CAAC,iBAAiB,CAAC;UAC3CvC,OAAO,CAAC6O,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChD9O,OAAO,CAAC4O,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;IAED,MAAMe,cAAc,GAAIhE,CAAQ,IAAK;MACpCA,CAAC,CAAC4D,eAAe,CAAC,CAAC;;MAEnB;MACA;MACA,MAAMR,aAAa,GAAG1B,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;MACvF,IAAIkE,QAAQ,KAAK,kBAAkB,IAAI,CAACN,aAAa,EAAE;QACtD;MAAA;IAEF,CAAC;IAED,MAAMa,WAAW,GAAIjE,CAAQ,IAAK;MACjCA,CAAC,CAAC4D,eAAe,CAAC,CAAC;MACnBC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAIJ,QAAQ,KAAK,kBAAkB,IAAI,CAACA,QAAQ,EAAE;QACjD;QACA1Q,cAAc,CAAC,CAACC,WAAW,CAAC;;QAE5B;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACJ,MAAMqS,oBAAoB,GAAGrC,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;QAC1F,IAAIuE,oBAAoB,EAAE;UACzB1P,OAAO,CAAC6O,SAAS,CAACtM,MAAM,CAAC,iBAAiB,CAAC;UAC3CvC,OAAO,CAAC6O,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChD9O,OAAO,CAAC4O,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;;IAED;IACA,IAAI,CAAC5O,OAAO,CAACgP,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,IAAIK,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACArP,OAAO,CAAC6I,gBAAgB,CAAC,WAAW,EAAEyG,WAAW,CAAC;QAClDtP,OAAO,CAAC6I,gBAAgB,CAAC,UAAU,EAAE8G,cAAc,CAAC;;QAEpD;QACA3P,OAAO,CAAC6I,gBAAgB,CAAC,OAAO,EAAE+G,WAAW,CAAC;MAC/C,CAAC,MAAM;QACN;QACA5P,OAAO,CAAC6I,gBAAgB,CAAC,OAAO,EAAE+G,WAAW,CAAC;MAC/C;;MAEA;MACA5P,OAAO,CAAC6P,YAAY,CAAC,yBAAyB,EAAE,MAAM,CAAC;IACxD;EACD,CAAC;EACD3X,SAAS,CAAC,MAAM;IACf,IAAIsJ,OAAO;IACX,IAAIsO,KAAK;IAET,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QAAA,IAAAC,uBAAA,EAAAC,uBAAA,EAAAC,MAAA,EAAAC,OAAA;QACH;QACAL,KAAK,GAAG,CAAA5S,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0D,SAAS,KAAI,EAAE;;QAEvC;QACA,MAAMwP,WAAW,GAAGrR,oBAAoB,KAAK,SAAS,IAAI7B,cAAc,aAAdA,cAAc,gBAAA8S,uBAAA,GAAd9S,cAAc,CAAE0D,SAAS,cAAAoP,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B3T,WAAW,GAAG,CAAC,CAAC,cAAA4T,uBAAA,eAA5CA,uBAAA,CAA8CpP,WAAW,GAC/G3D,cAAc,CAAC0D,SAAS,CAACvE,WAAW,GAAG,CAAC,CAAC,CAASwE,WAAW,GAC9D,EAAAqP,MAAA,GAAAJ,KAAK,cAAAI,MAAA,wBAAAC,OAAA,GAALD,MAAA,CAAQ,CAAC,CAAC,cAAAC,OAAA,uBAAVA,OAAA,CAAYtP,WAAW,KAAI,EAAE;QAEhCW,OAAO,GAAGvB,iBAAiB,CAACmQ,WAAW,IAAI,EAAE,CAAC;QAC9ChR,gBAAgB,CAACoC,OAAO,CAAC;QAEzB,IAAIA,OAAO,EAAE;UACZ;QAAA;;QAGD;QACA,MAAM6O,iBAAiB,GAAGhS,gBAAgB,KAAK,SAAS,IACvDU,oBAAoB,KAAK,SAAS,IAClCjD,KAAK,KAAK,SAAS,IAClBuC,gBAAgB,KAAK,MAAM,IAAIU,oBAAoB,KAAK,SAAU;QAEpE,IAAIsR,iBAAiB,EAAE;UAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;UAItB;UACA,IAAIrI,eAAe;UACnB,IAAI6C,WAAW;UAEf,IAAIpM,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAgS,sBAAA,GAApBhS,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAiU,sBAAA,eAAvCA,sBAAA,CAAyC/H,QAAQ,EAAE;YAAA,IAAAqI,uBAAA,EAAAC,uBAAA;YAC5F;YACAvI,eAAe,GAAGhK,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkM,QAAQ;YAChE4C,WAAW,GAAGjO,cAAc,aAAdA,cAAc,wBAAA0T,uBAAA,GAAd1T,cAAc,CAAE0D,SAAS,cAAAgQ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BvU,WAAW,GAAG,CAAC,CAAC,cAAAwU,uBAAA,uBAA5CA,uBAAA,CAA8C5M,OAAO;UACpE,CAAC,MAAM,IAAI3F,oBAAoB,aAApBA,oBAAoB,gBAAAiS,sBAAA,GAApBjS,oBAAoB,CAAG,CAAC,CAAC,cAAAiS,sBAAA,eAAzBA,sBAAA,CAA2BhI,QAAQ,EAAE;YAAA,IAAAuI,uBAAA,EAAAC,uBAAA;YAC/C;YACAzI,eAAe,GAAGhK,oBAAoB,CAAC,CAAC,CAAC,CAACiK,QAAQ;YAClD4C,WAAW,GAAGjO,cAAc,aAAdA,cAAc,wBAAA4T,uBAAA,GAAd5T,cAAc,CAAE0D,SAAS,cAAAkQ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgC9M,OAAO;UACtD,CAAC,MAAM;YAAA,IAAA+M,uBAAA,EAAAC,uBAAA;YACN;YACA3I,eAAe,GAAG;cACjBG,SAAS,EAAE,GAAG;cACdC,SAAS,EAAE,GAAG;cACdwF,IAAI,EAAE,UAAU;cAChBL,KAAK,EAAE,QAAQ;cACfD,IAAI,EAAE,IAAI;cACVL,cAAc,EAAE,IAAI;cACpBE,4BAA4B,EAAE,IAAI;cAClCC,QAAQ,EAAE,kBAAkB;cAC5B9C,aAAa,EAAE;YAChB,CAAC;YACDO,WAAW,GAAG,CAAAjO,cAAc,aAAdA,cAAc,wBAAA8T,uBAAA,GAAd9T,cAAc,CAAE0D,SAAS,cAAAoQ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B3U,WAAW,GAAG,CAAC,CAAC,cAAA4U,uBAAA,uBAA5CA,uBAAA,CAA8ChN,OAAO,KAAI,CAAC,CAAC;UAC1E;UACA,MAAM/C,OAAO,GAAGsH,UAAU,CAAC,EAAAgI,gBAAA,GAAAlI,eAAe,cAAAkI,gBAAA,uBAAfA,gBAAA,CAAiB/H,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMtH,OAAO,GAAGqH,UAAU,CAAC,EAAAiI,iBAAA,GAAAnI,eAAe,cAAAmI,iBAAA,uBAAfA,iBAAA,CAAiB/H,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMwI,kBAAkB,GAAG1I,UAAU,CAAC,EAAAkI,iBAAA,GAAApI,eAAe,cAAAoI,iBAAA,uBAAfA,iBAAA,CAAiB9C,IAAI,KAAI,IAAI,CAAC;;UAEpE;UACAhO,cAAc,CAACsR,kBAAkB,CAAC;UAElC,IAAI7X,IAAI,EAAED,GAAG;UACb,IAAIoI,OAAO,EAAE;YACZ,MAAMO,IAAI,GAAGP,OAAO,CAACQ,qBAAqB,CAAC,CAAC;YAC5C3I,IAAI,GAAG0I,IAAI,CAACjH,CAAC,GAAGoG,OAAO;YACvB9H,GAAG,GAAG2I,IAAI,CAAChH,CAAC,IAAIoG,OAAO,GAAG,CAAC,GAAG,CAACA,OAAO,GAAG1G,IAAI,CAAC0W,GAAG,CAAChQ,OAAO,CAAC,CAAC;;YAE3D;YACA,MAAMiQ,QAAQ,GAAGnQ,sBAAsB,CAACc,IAAI,EAAEmP,kBAAkB,EAAEhQ,OAAO,EAAEC,OAAO,CAAC;YACnF7B,gBAAgB,CAAC8R,QAAQ,CAAC;UAC3B;;UAEA;UACA,MAAMhP,eAAe,GAAGhC,QAAQ,CAACiC,cAAc,CAAC,cAAc,CAAC;UAC/D,IAAID,eAAe,EAAE;YACpBpC,OAAO,GAAGoC,eAAe;YACzB;UACD,CAAC,MAAM;YACN;YACApC,OAAO,GAAGI,QAAQ,CAACgO,aAAa,CAAC,KAAK,CAAC;YACvCpO,OAAO,CAACqR,EAAE,GAAG,cAAc,CAAC,CAAC;YAC7BrR,OAAO,CAAC4O,aAAa,GAAG,KAAK,CAAC,CAAC;YAC/BxO,QAAQ,CAACkR,IAAI,CAAC7C,WAAW,CAACzO,OAAO,CAAC;UACnC;UAEAA,OAAO,CAAC6B,KAAK,CAAC0P,MAAM,GAAG,SAAS;UAChCvR,OAAO,CAAC6B,KAAK,CAACmM,aAAa,GAAG,MAAM,CAAC,CAAC;;UAEtC;UACAhO,OAAO,CAAC6B,KAAK,CAACiM,MAAM,GAAG,MAAM;;UAE7B;UACA,KAAA6C,iBAAA,GAAIrI,eAAe,cAAAqI,iBAAA,eAAfA,iBAAA,CAAiB/F,aAAa,EAAE;YACnCjM,cAAc,CAAC,IAAI,CAAC;UACrB;;UAEA;UACAgP,kBAAkB,CAAC3N,OAAO,EAAEsI,eAAe,EAAE6C,WAAW,EAAE9R,IAAI,EAAED,GAAG,CAAC;;UAEpE;UACA,MAAM2V,aAAa,GAAG1B,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;UACvF,IAAI4D,aAAa,EAAE;YAClBpQ,cAAc,CAAC,IAAI,CAAC;UACrB,CAAC,MAAM;YACN;UAAA;;UAGD;QACD;MACD,CAAC,CAAC,OAAO6S,KAAK,EAAE;QACfhC,OAAO,CAACgC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACpD;IACD,CAAC;IAEDzB,iBAAiB,CAAC,CAAC;IAEnB,OAAO,MAAM;MACZ,MAAM3N,eAAe,GAAGhC,QAAQ,CAACiC,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACqP,OAAO,GAAG,IAAI;QAC9BrP,eAAe,CAACsP,WAAW,GAAG,IAAI;QAClCtP,eAAe,CAACuP,UAAU,GAAG,IAAI;MAClC;IACD,CAAC;EACF,CAAC,EAAE,CACFzU,cAAc,EACdoB,oBAAoB,EACpBhB,kBAAkB,EAClBC,kBAAkB,EAClBwB,oBAAoB,EACpB1C;EACA;EAAA,CACA,CAAC;EACF,MAAM0L,cAAc,GAAG,CAAA7K,cAAc,aAAdA,cAAc,wBAAAe,uBAAA,GAAdf,cAAc,CAAE0D,SAAS,cAAA3C,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgC0T,OAAO,cAAAzT,uBAAA,uBAAvCA,uBAAA,CAAyC0T,cAAc,KAAI,KAAK;EAEvF,SAASC,mBAAmBA,CAAC9S,cAAmB,EAAE;IAAA,IAAA+S,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IACjD,IAAIjT,cAAc,KAAK,CAAC,EAAE;MACzB,OAAO,MAAM;IACd,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,QAAQ;IAChB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB;IAEA,OAAO,CAAA9B,cAAc,aAAdA,cAAc,wBAAA6U,uBAAA,GAAd7U,cAAc,CAAE0D,SAAS,cAAAmR,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgCJ,OAAO,cAAAK,uBAAA,uBAAvCA,uBAAA,CAAyCC,gBAAgB,KAAI,MAAM;EAC3E;EACA,MAAMC,gBAAgB,GAAGL,mBAAmB,CAAC9S,cAAc,CAAC;EAC5D,MAAMoT,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACrK,cAAc,EAAE,OAAO,IAAI;IAEhC,IAAIoK,gBAAgB,KAAK,MAAM,EAAE;MAChC,oBACCnZ,OAAA,CAACP,aAAa;QACb4Z,OAAO,EAAC,MAAM;QACdvC,KAAK,EAAExT,UAAW;QAClBiI,QAAQ,EAAC,QAAQ;QACjB+N,UAAU,EAAEjW,WAAW,GAAG,CAAE;QAC5BkW,EAAE,EAAE;UACHzQ,eAAe,EAAE,aAAa;UAC9ByC,QAAQ,EAAE,oBAAoB;UAC9B,+BAA+B,EAAE;YAChCzC,eAAe,EAAE7C,aAAa,CAAE;UACjC;QACD,CAAE;QACFuT,UAAU,eAAExZ,OAAA,CAACV,MAAM;UAACuJ,KAAK,EAAE;YAAE4Q,UAAU,EAAE;UAAS;QAAE;UAAAvO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxDqO,UAAU,eAAE1Z,OAAA,CAACV,MAAM;UAACuJ,KAAK,EAAE;YAAE4Q,UAAU,EAAE;UAAS;QAAE;UAAAvO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEJ;IACA,IAAI8N,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCnZ,OAAA,CAACX,GAAG;QAACka,EAAE,EAAE;UAAEjQ,OAAO,EAAE,MAAM;UAAEiM,UAAU,EAAE,QAAQ;UAAEoE,YAAY,EAAE,QAAQ;UAAEC,GAAG,EAAE,KAAK;UAAEzY,OAAO,EAAE;QAAM,CAAE;QAAA0Y,QAAA,EAGrGC,KAAK,CAACC,IAAI,CAAC;UAAElN,MAAM,EAAEvJ;QAAW,CAAC,CAAC,CAACiH,GAAG,CAAC,CAACyP,CAAC,EAAEC,KAAK,kBAChDja,OAAA;UAEC6I,KAAK,EAAE;YACN5H,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,KAAK;YACb4H,eAAe,EAAEmR,KAAK,KAAK5W,WAAW,GAAG,CAAC,GAAG4C,aAAa,GAAG,SAAS;YAAE;YACxEkK,YAAY,EAAE;UACf;QAAE,GANG8J,KAAK;UAAA/O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAER;IACA,IAAI8N,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCnZ,OAAA,CAACX,GAAG;QAACka,EAAE,EAAE;UAAEjQ,OAAO,EAAE,MAAM;UAAEiM,UAAU,EAAE,QAAQ;UAAEoE,YAAY,EAAE;QAAa,CAAE;QAAAE,QAAA,eAC9E7Z,OAAA,CAACL,UAAU;UAAC4Z,EAAE,EAAE;YAAEpY,OAAO,EAAE,KAAK;YAAE4K,KAAK,EAAE9F;UAAc,CAAE;UAAA4T,QAAA,GAAC,OACpD,EAACxW,WAAW,EAAC,MAAI,EAACC,UAAU;QAAA;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAI8N,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACCnZ,OAAA,CAACX,GAAG;QAAAwa,QAAA,eACH7Z,OAAA,CAACL,UAAU;UAAC0Z,OAAO,EAAC,OAAO;UAAAQ,QAAA,eAC1B7Z,OAAA,CAACR,cAAc;YACd6Z,OAAO,EAAC,aAAa;YACrBa,KAAK,EAAE1W,QAAS;YAChB+V,EAAE,EAAE;cACHrY,MAAM,EAAE,KAAK;cACXiP,YAAY,EAAE,MAAM;cACpBgK,MAAM,EAAE,UAAU;cACpB,0BAA0B,EAAE;gBAC3BrR,eAAe,EAAE7C,aAAa,CAAE;cACjC;YACD;UAAE;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACZ,CAAC;EACD,oBACCrL,OAAA,CAAAE,SAAA;IAAA2Z,QAAA,GACE1T,aAAa,iBACbnG,OAAA;MAAA6Z,QAAA,EAcEjU,WAAW,iBACX5F,OAAA,CAACN,OAAO;QACP0R,IAAI,EAAEgJ,OAAO,CAAC/T,aAAa,CAAC,IAAI+T,OAAO,CAACxX,QAAQ,CAAE;QAClDA,QAAQ,EAAEA,QAAS;QACnBK,OAAO,EAAEA,CAAA,KAAM;UACd;UACA;QAAA,CACC;QACFf,YAAY,EAAEA,YAAa;QAC3BC,eAAe,EAAEA,eAAgB;QACjCkY,eAAe,EAAC,gBAAgB;QAChCC,cAAc,EACbjU,aAAa,GACV;UACA;UACAjG,GAAG,EAAEmG,eAAe,GACjBA,eAAe,CAACnG,GAAG,GACnBiG,aAAa,CAACjG,GAAG,GAAG,EAAE,IAAIoP,UAAU,CAAC9J,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC8J,UAAU,CAAC9J,YAAY,IAAI,GAAG,CAAC,GAAGjE,IAAI,CAAC0W,GAAG,CAAC3I,UAAU,CAAC9J,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC;UAChJrF,IAAI,EAAEkG,eAAe,GAClBA,eAAe,CAAClG,IAAI,GACpBgG,aAAa,CAAChG,IAAI,GAAG,EAAE,GAAGmP,UAAU,CAAC/J,YAAY,IAAI,GAAG;QAC3D,CAAC,GACD+O,SACH;QACD+E,EAAE,EAAE;UACH,gBAAgB,EAAE3W,QAAQ,GAAG,MAAM,GAAG,MAAM;UAC5C,8CAA8C,EAAE;YAC/CkS,MAAM,EAAE,IAAI;YACZ,GAAG5E,WAAW;YACd;YACA,IAAI3J,eAAe,GAAG,CAAC,CAAC,GAAG6N,iBAAiB,CAAC,CAAAvQ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2H,QAAQ,KAAI,eAAe,CAAC,CAAC;YAC5F;YACA,IAAIjF,eAAe,GAAG,CAAC,CAAC,GAAG;cAC1BnG,GAAG,EAAE,GAAG,CAAC,CAAAiG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEjG,GAAG,KAAI,CAAC,KAC5BsF,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC3C8J,UAAU,CAAC9J,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAClC,CAAC8J,UAAU,CAAC9J,YAAY,IAAI,GAAG,CAAC,GAChCjE,IAAI,CAAC0W,GAAG,CAAC3I,UAAU,CAAC9J,YAAY,IAAI,GAAG,CAAC,CAAC,GAC1C,CAAC,CAAC,eAAe;cACrBrF,IAAI,EAAE,GAAG,CAAC,CAAAgG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEhG,IAAI,KAAI,CAAC,KAAKoF,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC9E+J,UAAU,CAAC/J,YAAY,CAAC,IAAI,CAAC,GAC9B,CAAC,CAAE;YACP,CAAC,CAAC;YACF8U,QAAQ,EAAE;UACX;QACD,CAAE;QACFC,iBAAiB,EAAE,IAAK;QAAAX,QAAA,gBAExB7Z,OAAA;UAAK6I,KAAK,EAAE;YAAE8Q,YAAY,EAAE,KAAK;YAAErQ,OAAO,EAAE;UAAO,CAAE;UAAAuQ,QAAA,EACnD,CAAAjW,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE6W,aAAa,kBAC9Bza,OAAA,CAACT,UAAU;YACVmb,OAAO,EAAEA,CAAA,KAAM;cACd;cACA;YAAA,CACC;YACFnB,EAAE,EAAE;cACHhO,QAAQ,EAAE,OAAO;cACjBoP,SAAS,EAAE,iCAAiC;cAC5Cta,IAAI,EAAE,MAAM;cACZC,KAAK,EAAE,MAAM;cACb6Z,MAAM,EAAE,OAAO;cACfS,UAAU,EAAE,iBAAiB;cAC7BC,MAAM,EAAE,gBAAgB;cACxB/F,MAAM,EAAE,QAAQ;cAChB3E,YAAY,EAAE,MAAM;cACpBhP,OAAO,EAAE;YACV,CAAE;YAAA0Y,QAAA,eAEF7Z,OAAA,CAACJ,SAAS;cAAC2Z,EAAE,EAAE;gBAAEuB,IAAI,EAAE,CAAC;gBAAE/O,KAAK,EAAE;cAAO;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACNrL,OAAA,CAACF,gBAAgB;UAEpBib,GAAG,EAAE/S,YAAa;UAClBa,KAAK,EAAE;YAAEoG,SAAS,EAAEtC,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG;UAAQ,CAAE;UAC3EwN,OAAO,EAAE;YACRC,eAAe,EAAE,CAACnT,cAAc;YAChCoT,eAAe,EAAE,IAAI;YACrBC,gBAAgB,EAAE,KAAK;YACvBC,WAAW,EAAE,IAAI;YACjBC,kBAAkB,EAAE,EAAE;YACtBC,kBAAkB,EAAE,IAAI;YACxBC,mBAAmB,EAAE;UACtB,CAAE;UAAA1B,QAAA,eAEC7Z,OAAA;YAAK6I,KAAK,EAAE;cACXoG,SAAS,EAAEtC,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;cAC/D+M,QAAQ,EAAE5N,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa;cACvEvM,KAAK,EAAE0L,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAGgH,SAAS;cAC7D2F,MAAM,EAAExN,cAAc,CAAC,CAAC,GAAG,GAAG,GAAG6H;YAClC,CAAE;YAAAqF,QAAA,eACD7Z,OAAA,CAACX,GAAG;cAACwJ,KAAK,EAAE;gBACX1H,OAAO,EAAEwL,cAAc,CAAC,CAAC,GAAG,GAAG,GAC7Ba,WAAW,CAAC,CAAC,GAAG,GAAG,GAAI,CAAA3J,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2X,OAAO,KAAI,MAAO;gBAC7Dta,MAAM,EAAEyL,cAAc,CAAC,CAAC,GAAG,MAAM,GAAGgE,aAAa;gBACjD1P,KAAK,EAAE0L,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAGgH,SAAS;gBAC7D2F,MAAM,EAAExN,cAAc,CAAC,CAAC,GAAG,GAAG,GAAG6H;cAClC,CAAE;cAAAqF,QAAA,gBACD7Z,OAAA,CAACX,GAAG;gBACH0b,GAAG,EAAElU,UAAW;gBAChByC,OAAO,EAAC,MAAM;gBACdmS,aAAa,EAAC,QAAQ;gBACtBC,QAAQ,EAAC,MAAM;gBACflG,cAAc,EAAC,QAAQ;gBACvB+D,EAAE,EAAE;kBACHtY,KAAK,EAAEuM,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;kBACtCrM,OAAO,EAAEqM,WAAW,CAAC,CAAC,GAAG,GAAG,GAAGgH;gBAChC,CAAE;gBAAAqF,QAAA,GAEDnW,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE6G,GAAG,CAAEmE,SAAc,IACpCA,SAAS,CAAC1B,WAAW,CAACzC,GAAG,CAAC,CAACoR,SAAc,EAAEC,QAAgB,kBAC1D5b,OAAA,CAACX,GAAG;kBAEHwc,SAAS,EAAC,KAAK;kBACfC,GAAG,EAAEH,SAAS,CAACzO,GAAI;kBACnB6O,GAAG,EAAEJ,SAAS,CAACK,OAAO,IAAI,OAAQ;kBAClCzC,EAAE,EAAE;oBACHtK,SAAS,EAAEP,SAAS,CAACuN,cAAc,IAAIN,SAAS,CAACM,cAAc,IAAI,OAAO;oBAC1EhQ,SAAS,EAAEyC,SAAS,CAACxC,SAAS,IAAI,QAAQ;oBAC1CgQ,SAAS,EAAEP,SAAS,CAACQ,GAAG,IAAI,SAAS;oBACrC;oBACAjb,MAAM,EAAE,GAAGya,SAAS,CAAChN,aAAa,IAAI,GAAG,IAAI;oBAC7CiM,UAAU,EAAEe,SAAS,CAACjL,eAAe,IAAI,SAAS;oBAClDyJ,MAAM,EAAE;kBACT,CAAE;kBACFO,OAAO,EAAEA,CAAA,KAAM;oBACd,IAAIhM,SAAS,CAAC0N,SAAS,EAAE;sBACxB,MAAMrL,SAAS,GAAGrC,SAAS,CAAC0N,SAAS;sBACrC7b,MAAM,CAAC6Q,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;oBACxD;kBACD,CAAE;kBACFlI,KAAK,EAAE;oBAAE0P,MAAM,EAAE7J,SAAS,CAAC0N,SAAS,GAAG,SAAS,GAAG;kBAAU;gBAAE,GAnB1D,GAAG1N,SAAS,CAAC9D,EAAE,IAAIgR,QAAQ,EAAE;kBAAA1Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBlC,CACD,CACF,CAAC,EAEA5H,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE8G,GAAG,CACxB,CAAC8R,SAAc,EAAEpC,KAAU;kBAAA,IAAAqC,qBAAA,EAAAC,sBAAA;kBAAA,OAC1BF,SAAS,CAAChP,IAAI,iBACbrN,OAAA,CAACL,UAAU;oBACV6c,SAAS,EAAC,eAAe;oBACG;oBAC5BjD,EAAE,EAAE;sBACHtN,SAAS,EAAE,EAAAqQ,qBAAA,GAAAD,SAAS,CAAC1Q,cAAc,cAAA2Q,qBAAA,uBAAxBA,qBAAA,CAA0BG,UAAU,KAAIhR,SAAS,CAACQ,SAAS;sBACtEF,KAAK,EAAE,EAAAwQ,sBAAA,GAAAF,SAAS,CAAC1Q,cAAc,cAAA4Q,sBAAA,uBAAxBA,sBAAA,CAA0BvQ,SAAS,KAAIP,SAAS,CAACM,KAAK;sBAC7D2Q,UAAU,EAAE,UAAU;sBACtBC,SAAS,EAAE,YAAY;sBACvBxb,OAAO,EAAE;oBACV,CAAE;oBACFyb,uBAAuB,EAAEzQ,iBAAiB,CAACkQ,SAAS,CAAChP,IAAI,CAAE,CAAC;kBAAA,GARvDgP,SAAS,CAACzR,EAAE,IAAIqP,KAAK;oBAAA/O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAS1B,CACD;gBAAA,CACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,EAELwR,MAAM,CAACC,IAAI,CAAC/M,cAAc,CAAC,CAACxF,GAAG,CAAEyF,WAAW;gBAAA,IAAA+M,qBAAA,EAAAC,sBAAA;gBAAA,oBAC5Chd,OAAA,CAACX,GAAG;kBACH0b,GAAG,EAAEjU,kBAAmB;kBAExByS,EAAE,EAAE;oBACHjQ,OAAO,EAAE,MAAM;oBACfkM,cAAc,EAAEtB,YAAY,EAAA6I,qBAAA,GAAChN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAA+M,qBAAA,uBAA9BA,qBAAA,CAAgC7Q,SAAS,CAAC;oBACvEwP,QAAQ,EAAE,MAAM;oBAChBvB,MAAM,EAAExN,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO;oBACtC7D,eAAe,GAAAkU,sBAAA,GAAEjN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAAgN,sBAAA,uBAA9BA,sBAAA,CAAgCtM,eAAe;oBAChEvP,OAAO,EAAEwL,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO;oBAC3C1L,KAAK,EAAE0L,cAAc,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;oBACzCwD,YAAY,EAAExD,cAAc,CAAC,CAAC,GAAG,MAAM,GAAG6H;kBAC3C,CAAE;kBAAAqF,QAAA,EAED9J,cAAc,CAACC,WAAW,CAAC,CAACzF,GAAG,CAAC,CAACG,MAAW,EAAEuP,KAAa;oBAAA,IAAAgD,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;oBAAA,oBAC3Dvd,OAAA,CAACV,MAAM;sBAENob,OAAO,EAAEA,CAAA,KAAM9J,kBAAkB,CAAClG,MAAM,CAAC8S,YAAY,CAAE;sBACvDnE,OAAO,EAAC,WAAW;sBACnBE,EAAE,EAAE;wBACHkE,WAAW,EAAE9Q,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM;wBAC9CwN,MAAM,EAAExN,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,eAAe;wBAClD7D,eAAe,EAAE,EAAAmU,qBAAA,GAAAvS,MAAM,CAACgT,gBAAgB,cAAAT,qBAAA,uBAAvBA,qBAAA,CAAyBU,qBAAqB,KAAI,SAAS;wBAC5E5R,KAAK,EAAE,EAAAmR,sBAAA,GAAAxS,MAAM,CAACgT,gBAAgB,cAAAR,sBAAA,uBAAvBA,sBAAA,CAAyBU,eAAe,KAAI,MAAM;wBACzD/C,MAAM,EAAE,EAAAsC,sBAAA,GAAAzS,MAAM,CAACgT,gBAAgB,cAAAP,sBAAA,uBAAvBA,sBAAA,CAAyBU,iBAAiB,KAAI,aAAa;wBACnEvI,QAAQ,EAAE,EAAA8H,sBAAA,GAAA1S,MAAM,CAACgT,gBAAgB,cAAAN,sBAAA,uBAAvBA,sBAAA,CAAyBU,QAAQ,KAAI,MAAM;wBACrD7c,KAAK,EAAE,EAAAoc,sBAAA,GAAA3S,MAAM,CAACgT,gBAAgB,cAAAL,sBAAA,uBAAvBA,sBAAA,CAAyB1P,KAAK,KAAI,MAAM;wBAC/CxM,OAAO,EAAEwL,cAAc,CAAC,CAAC,GAAG,kCAAkC,GAAG,SAAS;wBAC1EoR,UAAU,EAAEpR,cAAc,CAAC,CAAC,GAAG,0BAA0B,GAAG,QAAQ;wBACpEqR,aAAa,EAAE,MAAM;wBACrB7N,YAAY,EAAE,EAAAmN,sBAAA,GAAA5S,MAAM,CAACgT,gBAAgB,cAAAJ,sBAAA,uBAAvBA,sBAAA,CAAyBW,YAAY,KAAI,KAAK;wBAC5D3P,QAAQ,EAAE3B,cAAc,CAAC,CAAC,GAAG,aAAa,GAAG6H,SAAS;wBACtDmG,SAAS,EAAE,iBAAiB;wBAAE;wBAC9B,SAAS,EAAE;0BACV7R,eAAe,EAAE,EAAAyU,sBAAA,GAAA7S,MAAM,CAACgT,gBAAgB,cAAAH,sBAAA,uBAAvBA,sBAAA,CAAyBI,qBAAqB,KAAI,SAAS;0BAAE;0BAC9EO,OAAO,EAAE,GAAG;0BAAE;0BACdvD,SAAS,EAAE,iBAAiB,CAAE;wBAC/B;sBACD,CAAE;sBAAAd,QAAA,EAEDnP,MAAM,CAACyT;oBAAU,GAxBblE,KAAK;sBAAA/O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAyBH,CAAC;kBAAA,CACT;gBAAC,GAxCG2E,WAAW;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyCZ,CAAC;cAAA,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGD;QAAC,GApIL,aAAavD,cAAc,EAAE;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqIZ,CAAC,EAElB0D,cAAc,IAAIzL,UAAU,GAAC,CAAC,IAAI+B,gBAAgB,KAAK,MAAM,iBAAIrF,OAAA,CAACX,GAAG;UAAAwa,QAAA,EAAET,cAAc,CAAC;QAAC;UAAAlO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAE,GAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACL,eAEDrL,OAAA;MAAA6Z,QAAA,EACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAA3O,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACP,CAAC;AAEL,CAAC;AAAC7G,EAAA,CAj3CI7B,cAAoC;EAAA,QA8CrC9C,cAAc;AAAA;AAAAue,EAAA,GA9Cbzb,cAAoC;AAm3C1C,eAAeA,cAAc;AAAC,IAAAyb,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}