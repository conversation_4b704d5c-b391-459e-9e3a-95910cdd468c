{"ast": null, "code": "// Depends on jsbn.js and rng.js\n// Version 1.1: support utf-8 encoding in pkcs1pad2\n// convert a (hex) string to a bignum object\nimport { BigInteger, nbi, parseBigInt } from \"./jsbn\";\nimport { SecureRandom } from \"./rng\";\n// function linebrk(s,n) {\n//   var ret = \"\";\n//   var i = 0;\n//   while(i + n < s.length) {\n//     ret += s.substring(i,i+n) + \"\\n\";\n//     i += n;\n//   }\n//   return ret + s.substring(i,s.length);\n// }\n// function byte2Hex(b) {\n//   if(b < 0x10)\n//     return \"0\" + b.toString(16);\n//   else\n//     return b.toString(16);\n// }\nfunction pkcs1pad1(s, n) {\n  if (n < s.length + 22) {\n    console.error(\"Message too long for RSA\");\n    return null;\n  }\n  var len = n - s.length - 6;\n  var filler = \"\";\n  for (var f = 0; f < len; f += 2) {\n    filler += \"ff\";\n  }\n  var m = \"0001\" + filler + \"00\" + s;\n  return parseBigInt(m, 16);\n}\n// PKCS#1 (type 2, random) pad input string s to n bytes, and return a bigint\nfunction pkcs1pad2(s, n) {\n  if (n < s.length + 11) {\n    // TODO: fix for utf-8\n    console.error(\"Message too long for RSA\");\n    return null;\n  }\n  var ba = [];\n  var i = s.length - 1;\n  while (i >= 0 && n > 0) {\n    var c = s.charCodeAt(i--);\n    if (c < 128) {\n      // encode using utf-8\n      ba[--n] = c;\n    } else if (c > 127 && c < 2048) {\n      ba[--n] = c & 63 | 128;\n      ba[--n] = c >> 6 | 192;\n    } else {\n      ba[--n] = c & 63 | 128;\n      ba[--n] = c >> 6 & 63 | 128;\n      ba[--n] = c >> 12 | 224;\n    }\n  }\n  ba[--n] = 0;\n  var rng = new SecureRandom();\n  var x = [];\n  while (n > 2) {\n    // random non-zero pad\n    x[0] = 0;\n    while (x[0] == 0) {\n      rng.nextBytes(x);\n    }\n    ba[--n] = x[0];\n  }\n  ba[--n] = 2;\n  ba[--n] = 0;\n  return new BigInteger(ba);\n}\n// \"empty\" RSA key constructor\nvar RSAKey = /** @class */function () {\n  function RSAKey() {\n    this.n = null;\n    this.e = 0;\n    this.d = null;\n    this.p = null;\n    this.q = null;\n    this.dmp1 = null;\n    this.dmq1 = null;\n    this.coeff = null;\n  }\n  //#region PROTECTED\n  // protected\n  // RSAKey.prototype.doPublic = RSADoPublic;\n  // Perform raw public operation on \"x\": return x^e (mod n)\n  RSAKey.prototype.doPublic = function (x) {\n    return x.modPowInt(this.e, this.n);\n  };\n  // RSAKey.prototype.doPrivate = RSADoPrivate;\n  // Perform raw private operation on \"x\": return x^d (mod n)\n  RSAKey.prototype.doPrivate = function (x) {\n    if (this.p == null || this.q == null) {\n      return x.modPow(this.d, this.n);\n    }\n    // TODO: re-calculate any missing CRT params\n    var xp = x.mod(this.p).modPow(this.dmp1, this.p);\n    var xq = x.mod(this.q).modPow(this.dmq1, this.q);\n    while (xp.compareTo(xq) < 0) {\n      xp = xp.add(this.p);\n    }\n    return xp.subtract(xq).multiply(this.coeff).mod(this.p).multiply(this.q).add(xq);\n  };\n  //#endregion PROTECTED\n  //#region PUBLIC\n  // RSAKey.prototype.setPublic = RSASetPublic;\n  // Set the public key fields N and e from hex strings\n  RSAKey.prototype.setPublic = function (N, E) {\n    if (N != null && E != null && N.length > 0 && E.length > 0) {\n      this.n = parseBigInt(N, 16);\n      this.e = parseInt(E, 16);\n    } else {\n      console.error(\"Invalid RSA public key\");\n    }\n  };\n  // RSAKey.prototype.encrypt = RSAEncrypt;\n  // Return the PKCS#1 RSA encryption of \"text\" as an even-length hex string\n  RSAKey.prototype.encrypt = function (text) {\n    var maxLength = this.n.bitLength() + 7 >> 3;\n    var m = pkcs1pad2(text, maxLength);\n    if (m == null) {\n      return null;\n    }\n    var c = this.doPublic(m);\n    if (c == null) {\n      return null;\n    }\n    var h = c.toString(16);\n    var length = h.length;\n    // fix zero before result\n    for (var i = 0; i < maxLength * 2 - length; i++) {\n      h = \"0\" + h;\n    }\n    return h;\n  };\n  // RSAKey.prototype.setPrivate = RSASetPrivate;\n  // Set the private key fields N, e, and d from hex strings\n  RSAKey.prototype.setPrivate = function (N, E, D) {\n    if (N != null && E != null && N.length > 0 && E.length > 0) {\n      this.n = parseBigInt(N, 16);\n      this.e = parseInt(E, 16);\n      this.d = parseBigInt(D, 16);\n    } else {\n      console.error(\"Invalid RSA private key\");\n    }\n  };\n  // RSAKey.prototype.setPrivateEx = RSASetPrivateEx;\n  // Set the private key fields N, e, d and CRT params from hex strings\n  RSAKey.prototype.setPrivateEx = function (N, E, D, P, Q, DP, DQ, C) {\n    if (N != null && E != null && N.length > 0 && E.length > 0) {\n      this.n = parseBigInt(N, 16);\n      this.e = parseInt(E, 16);\n      this.d = parseBigInt(D, 16);\n      this.p = parseBigInt(P, 16);\n      this.q = parseBigInt(Q, 16);\n      this.dmp1 = parseBigInt(DP, 16);\n      this.dmq1 = parseBigInt(DQ, 16);\n      this.coeff = parseBigInt(C, 16);\n    } else {\n      console.error(\"Invalid RSA private key\");\n    }\n  };\n  // RSAKey.prototype.generate = RSAGenerate;\n  // Generate a new random private key B bits long, using public expt E\n  RSAKey.prototype.generate = function (B, E) {\n    var rng = new SecureRandom();\n    var qs = B >> 1;\n    this.e = parseInt(E, 16);\n    var ee = new BigInteger(E, 16);\n    for (;;) {\n      for (;;) {\n        this.p = new BigInteger(B - qs, 1, rng);\n        if (this.p.subtract(BigInteger.ONE).gcd(ee).compareTo(BigInteger.ONE) == 0 && this.p.isProbablePrime(10)) {\n          break;\n        }\n      }\n      for (;;) {\n        this.q = new BigInteger(qs, 1, rng);\n        if (this.q.subtract(BigInteger.ONE).gcd(ee).compareTo(BigInteger.ONE) == 0 && this.q.isProbablePrime(10)) {\n          break;\n        }\n      }\n      if (this.p.compareTo(this.q) <= 0) {\n        var t = this.p;\n        this.p = this.q;\n        this.q = t;\n      }\n      var p1 = this.p.subtract(BigInteger.ONE);\n      var q1 = this.q.subtract(BigInteger.ONE);\n      var phi = p1.multiply(q1);\n      if (phi.gcd(ee).compareTo(BigInteger.ONE) == 0) {\n        this.n = this.p.multiply(this.q);\n        this.d = ee.modInverse(phi);\n        this.dmp1 = this.d.mod(p1);\n        this.dmq1 = this.d.mod(q1);\n        this.coeff = this.q.modInverse(this.p);\n        break;\n      }\n    }\n  };\n  // RSAKey.prototype.decrypt = RSADecrypt;\n  // Return the PKCS#1 RSA decryption of \"ctext\".\n  // \"ctext\" is an even-length hex string and the output is a plain string.\n  RSAKey.prototype.decrypt = function (ctext) {\n    var c = parseBigInt(ctext, 16);\n    var m = this.doPrivate(c);\n    if (m == null) {\n      return null;\n    }\n    return pkcs1unpad2(m, this.n.bitLength() + 7 >> 3);\n  };\n  // Generate a new random private key B bits long, using public expt E\n  RSAKey.prototype.generateAsync = function (B, E, callback) {\n    var rng = new SecureRandom();\n    var qs = B >> 1;\n    this.e = parseInt(E, 16);\n    var ee = new BigInteger(E, 16);\n    var rsa = this;\n    // These functions have non-descript names because they were originally for(;;) loops.\n    // I don't know about cryptography to give them better names than loop1-4.\n    var loop1 = function () {\n      var loop4 = function () {\n        if (rsa.p.compareTo(rsa.q) <= 0) {\n          var t = rsa.p;\n          rsa.p = rsa.q;\n          rsa.q = t;\n        }\n        var p1 = rsa.p.subtract(BigInteger.ONE);\n        var q1 = rsa.q.subtract(BigInteger.ONE);\n        var phi = p1.multiply(q1);\n        if (phi.gcd(ee).compareTo(BigInteger.ONE) == 0) {\n          rsa.n = rsa.p.multiply(rsa.q);\n          rsa.d = ee.modInverse(phi);\n          rsa.dmp1 = rsa.d.mod(p1);\n          rsa.dmq1 = rsa.d.mod(q1);\n          rsa.coeff = rsa.q.modInverse(rsa.p);\n          setTimeout(function () {\n            callback();\n          }, 0); // escape\n        } else {\n          setTimeout(loop1, 0);\n        }\n      };\n      var loop3 = function () {\n        rsa.q = nbi();\n        rsa.q.fromNumberAsync(qs, 1, rng, function () {\n          rsa.q.subtract(BigInteger.ONE).gcda(ee, function (r) {\n            if (r.compareTo(BigInteger.ONE) == 0 && rsa.q.isProbablePrime(10)) {\n              setTimeout(loop4, 0);\n            } else {\n              setTimeout(loop3, 0);\n            }\n          });\n        });\n      };\n      var loop2 = function () {\n        rsa.p = nbi();\n        rsa.p.fromNumberAsync(B - qs, 1, rng, function () {\n          rsa.p.subtract(BigInteger.ONE).gcda(ee, function (r) {\n            if (r.compareTo(BigInteger.ONE) == 0 && rsa.p.isProbablePrime(10)) {\n              setTimeout(loop3, 0);\n            } else {\n              setTimeout(loop2, 0);\n            }\n          });\n        });\n      };\n      setTimeout(loop2, 0);\n    };\n    setTimeout(loop1, 0);\n  };\n  RSAKey.prototype.sign = function (text, digestMethod, digestName) {\n    var header = getDigestHeader(digestName);\n    var digest = header + digestMethod(text).toString();\n    var m = pkcs1pad1(digest, this.n.bitLength() / 4);\n    if (m == null) {\n      return null;\n    }\n    var c = this.doPrivate(m);\n    if (c == null) {\n      return null;\n    }\n    var h = c.toString(16);\n    if ((h.length & 1) == 0) {\n      return h;\n    } else {\n      return \"0\" + h;\n    }\n  };\n  RSAKey.prototype.verify = function (text, signature, digestMethod) {\n    var c = parseBigInt(signature, 16);\n    var m = this.doPublic(c);\n    if (m == null) {\n      return null;\n    }\n    var unpadded = m.toString(16).replace(/^1f+00/, \"\");\n    var digest = removeDigestHeader(unpadded);\n    return digest == digestMethod(text).toString();\n  };\n  return RSAKey;\n}();\nexport { RSAKey };\n// Undo PKCS#1 (type 2, random) padding and, if valid, return the plaintext\nfunction pkcs1unpad2(d, n) {\n  var b = d.toByteArray();\n  var i = 0;\n  while (i < b.length && b[i] == 0) {\n    ++i;\n  }\n  if (b.length - i != n - 1 || b[i] != 2) {\n    return null;\n  }\n  ++i;\n  while (b[i] != 0) {\n    if (++i >= b.length) {\n      return null;\n    }\n  }\n  var ret = \"\";\n  while (++i < b.length) {\n    var c = b[i] & 255;\n    if (c < 128) {\n      // utf-8 decode\n      ret += String.fromCharCode(c);\n    } else if (c > 191 && c < 224) {\n      ret += String.fromCharCode((c & 31) << 6 | b[i + 1] & 63);\n      ++i;\n    } else {\n      ret += String.fromCharCode((c & 15) << 12 | (b[i + 1] & 63) << 6 | b[i + 2] & 63);\n      i += 2;\n    }\n  }\n  return ret;\n}\n// https://tools.ietf.org/html/rfc3447#page-43\nvar DIGEST_HEADERS = {\n  md2: \"3020300c06082a864886f70d020205000410\",\n  md5: \"3020300c06082a864886f70d020505000410\",\n  sha1: \"3021300906052b0e03021a05000414\",\n  sha224: \"302d300d06096086480165030402040500041c\",\n  sha256: \"3031300d060960864801650304020105000420\",\n  sha384: \"3041300d060960864801650304020205000430\",\n  sha512: \"3051300d060960864801650304020305000440\",\n  ripemd160: \"3021300906052b2403020105000414\"\n};\nfunction getDigestHeader(name) {\n  return DIGEST_HEADERS[name] || \"\";\n}\nfunction removeDigestHeader(str) {\n  for (var name_1 in DIGEST_HEADERS) {\n    if (DIGEST_HEADERS.hasOwnProperty(name_1)) {\n      var header = DIGEST_HEADERS[name_1];\n      var len = header.length;\n      if (str.substr(0, len) == header) {\n        return str.substr(len);\n      }\n    }\n  }\n  return str;\n}\n// Return the PKCS#1 RSA encryption of \"text\" as a Base64-encoded string\n// function RSAEncryptB64(text) {\n//  var h = this.encrypt(text);\n//  if(h) return hex2b64(h); else return null;\n// }\n// public\n// RSAKey.prototype.encrypt_b64 = RSAEncryptB64;", "map": {"version": 3, "names": ["BigInteger", "nbi", "parseBigInt", "SecureRandom", "pkcs1pad1", "s", "n", "length", "console", "error", "len", "filler", "f", "m", "pkcs1pad2", "ba", "i", "c", "charCodeAt", "rng", "x", "nextBytes", "RSAKey", "e", "d", "p", "q", "dmp1", "dmq1", "coeff", "prototype", "doPublic", "modPowInt", "doPrivate", "modPow", "xp", "mod", "xq", "compareTo", "add", "subtract", "multiply", "setPublic", "N", "E", "parseInt", "encrypt", "text", "max<PERSON><PERSON><PERSON>", "bitLength", "h", "toString", "setPrivate", "D", "setPrivateEx", "P", "Q", "DP", "DQ", "C", "generate", "B", "qs", "ee", "ONE", "gcd", "isProbablePrime", "t", "p1", "q1", "phi", "modInverse", "decrypt", "ctext", "pkcs1unpad2", "generateAsync", "callback", "rsa", "loop1", "loop4", "setTimeout", "loop3", "fromNumberAsync", "gcda", "r", "loop2", "sign", "digestMethod", "digestName", "header", "getDigestHeader", "digest", "verify", "signature", "unpadded", "replace", "removeDigest<PERSON>eader", "b", "toByteArray", "ret", "String", "fromCharCode", "DIGEST_HEADERS", "md2", "md5", "sha1", "sha224", "sha256", "sha384", "sha512", "ripemd160", "name", "str", "name_1", "hasOwnProperty", "substr"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/jsencrypt/lib/lib/jsbn/rsa.js"], "sourcesContent": ["// Depends on jsbn.js and rng.js\n// Version 1.1: support utf-8 encoding in pkcs1pad2\n// convert a (hex) string to a bignum object\nimport { BigInteger, nbi, parseBigInt } from \"./jsbn\";\nimport { SecureRandom } from \"./rng\";\n// function linebrk(s,n) {\n//   var ret = \"\";\n//   var i = 0;\n//   while(i + n < s.length) {\n//     ret += s.substring(i,i+n) + \"\\n\";\n//     i += n;\n//   }\n//   return ret + s.substring(i,s.length);\n// }\n// function byte2Hex(b) {\n//   if(b < 0x10)\n//     return \"0\" + b.toString(16);\n//   else\n//     return b.toString(16);\n// }\nfunction pkcs1pad1(s, n) {\n    if (n < s.length + 22) {\n        console.error(\"Message too long for RSA\");\n        return null;\n    }\n    var len = n - s.length - 6;\n    var filler = \"\";\n    for (var f = 0; f < len; f += 2) {\n        filler += \"ff\";\n    }\n    var m = \"0001\" + filler + \"00\" + s;\n    return parseBigInt(m, 16);\n}\n// PKCS#1 (type 2, random) pad input string s to n bytes, and return a bigint\nfunction pkcs1pad2(s, n) {\n    if (n < s.length + 11) { // TODO: fix for utf-8\n        console.error(\"Message too long for RSA\");\n        return null;\n    }\n    var ba = [];\n    var i = s.length - 1;\n    while (i >= 0 && n > 0) {\n        var c = s.charCodeAt(i--);\n        if (c < 128) { // encode using utf-8\n            ba[--n] = c;\n        }\n        else if ((c > 127) && (c < 2048)) {\n            ba[--n] = (c & 63) | 128;\n            ba[--n] = (c >> 6) | 192;\n        }\n        else {\n            ba[--n] = (c & 63) | 128;\n            ba[--n] = ((c >> 6) & 63) | 128;\n            ba[--n] = (c >> 12) | 224;\n        }\n    }\n    ba[--n] = 0;\n    var rng = new SecureRandom();\n    var x = [];\n    while (n > 2) { // random non-zero pad\n        x[0] = 0;\n        while (x[0] == 0) {\n            rng.nextBytes(x);\n        }\n        ba[--n] = x[0];\n    }\n    ba[--n] = 2;\n    ba[--n] = 0;\n    return new BigInteger(ba);\n}\n// \"empty\" RSA key constructor\nvar RSAKey = /** @class */ (function () {\n    function RSAKey() {\n        this.n = null;\n        this.e = 0;\n        this.d = null;\n        this.p = null;\n        this.q = null;\n        this.dmp1 = null;\n        this.dmq1 = null;\n        this.coeff = null;\n    }\n    //#region PROTECTED\n    // protected\n    // RSAKey.prototype.doPublic = RSADoPublic;\n    // Perform raw public operation on \"x\": return x^e (mod n)\n    RSAKey.prototype.doPublic = function (x) {\n        return x.modPowInt(this.e, this.n);\n    };\n    // RSAKey.prototype.doPrivate = RSADoPrivate;\n    // Perform raw private operation on \"x\": return x^d (mod n)\n    RSAKey.prototype.doPrivate = function (x) {\n        if (this.p == null || this.q == null) {\n            return x.modPow(this.d, this.n);\n        }\n        // TODO: re-calculate any missing CRT params\n        var xp = x.mod(this.p).modPow(this.dmp1, this.p);\n        var xq = x.mod(this.q).modPow(this.dmq1, this.q);\n        while (xp.compareTo(xq) < 0) {\n            xp = xp.add(this.p);\n        }\n        return xp.subtract(xq).multiply(this.coeff).mod(this.p).multiply(this.q).add(xq);\n    };\n    //#endregion PROTECTED\n    //#region PUBLIC\n    // RSAKey.prototype.setPublic = RSASetPublic;\n    // Set the public key fields N and e from hex strings\n    RSAKey.prototype.setPublic = function (N, E) {\n        if (N != null && E != null && N.length > 0 && E.length > 0) {\n            this.n = parseBigInt(N, 16);\n            this.e = parseInt(E, 16);\n        }\n        else {\n            console.error(\"Invalid RSA public key\");\n        }\n    };\n    // RSAKey.prototype.encrypt = RSAEncrypt;\n    // Return the PKCS#1 RSA encryption of \"text\" as an even-length hex string\n    RSAKey.prototype.encrypt = function (text) {\n        var maxLength = (this.n.bitLength() + 7) >> 3;\n        var m = pkcs1pad2(text, maxLength);\n        if (m == null) {\n            return null;\n        }\n        var c = this.doPublic(m);\n        if (c == null) {\n            return null;\n        }\n        var h = c.toString(16);\n        var length = h.length;\n        // fix zero before result\n        for (var i = 0; i < maxLength * 2 - length; i++) {\n            h = \"0\" + h;\n        }\n        return h;\n    };\n    // RSAKey.prototype.setPrivate = RSASetPrivate;\n    // Set the private key fields N, e, and d from hex strings\n    RSAKey.prototype.setPrivate = function (N, E, D) {\n        if (N != null && E != null && N.length > 0 && E.length > 0) {\n            this.n = parseBigInt(N, 16);\n            this.e = parseInt(E, 16);\n            this.d = parseBigInt(D, 16);\n        }\n        else {\n            console.error(\"Invalid RSA private key\");\n        }\n    };\n    // RSAKey.prototype.setPrivateEx = RSASetPrivateEx;\n    // Set the private key fields N, e, d and CRT params from hex strings\n    RSAKey.prototype.setPrivateEx = function (N, E, D, P, Q, DP, DQ, C) {\n        if (N != null && E != null && N.length > 0 && E.length > 0) {\n            this.n = parseBigInt(N, 16);\n            this.e = parseInt(E, 16);\n            this.d = parseBigInt(D, 16);\n            this.p = parseBigInt(P, 16);\n            this.q = parseBigInt(Q, 16);\n            this.dmp1 = parseBigInt(DP, 16);\n            this.dmq1 = parseBigInt(DQ, 16);\n            this.coeff = parseBigInt(C, 16);\n        }\n        else {\n            console.error(\"Invalid RSA private key\");\n        }\n    };\n    // RSAKey.prototype.generate = RSAGenerate;\n    // Generate a new random private key B bits long, using public expt E\n    RSAKey.prototype.generate = function (B, E) {\n        var rng = new SecureRandom();\n        var qs = B >> 1;\n        this.e = parseInt(E, 16);\n        var ee = new BigInteger(E, 16);\n        for (;;) {\n            for (;;) {\n                this.p = new BigInteger(B - qs, 1, rng);\n                if (this.p.subtract(BigInteger.ONE).gcd(ee).compareTo(BigInteger.ONE) == 0 && this.p.isProbablePrime(10)) {\n                    break;\n                }\n            }\n            for (;;) {\n                this.q = new BigInteger(qs, 1, rng);\n                if (this.q.subtract(BigInteger.ONE).gcd(ee).compareTo(BigInteger.ONE) == 0 && this.q.isProbablePrime(10)) {\n                    break;\n                }\n            }\n            if (this.p.compareTo(this.q) <= 0) {\n                var t = this.p;\n                this.p = this.q;\n                this.q = t;\n            }\n            var p1 = this.p.subtract(BigInteger.ONE);\n            var q1 = this.q.subtract(BigInteger.ONE);\n            var phi = p1.multiply(q1);\n            if (phi.gcd(ee).compareTo(BigInteger.ONE) == 0) {\n                this.n = this.p.multiply(this.q);\n                this.d = ee.modInverse(phi);\n                this.dmp1 = this.d.mod(p1);\n                this.dmq1 = this.d.mod(q1);\n                this.coeff = this.q.modInverse(this.p);\n                break;\n            }\n        }\n    };\n    // RSAKey.prototype.decrypt = RSADecrypt;\n    // Return the PKCS#1 RSA decryption of \"ctext\".\n    // \"ctext\" is an even-length hex string and the output is a plain string.\n    RSAKey.prototype.decrypt = function (ctext) {\n        var c = parseBigInt(ctext, 16);\n        var m = this.doPrivate(c);\n        if (m == null) {\n            return null;\n        }\n        return pkcs1unpad2(m, (this.n.bitLength() + 7) >> 3);\n    };\n    // Generate a new random private key B bits long, using public expt E\n    RSAKey.prototype.generateAsync = function (B, E, callback) {\n        var rng = new SecureRandom();\n        var qs = B >> 1;\n        this.e = parseInt(E, 16);\n        var ee = new BigInteger(E, 16);\n        var rsa = this;\n        // These functions have non-descript names because they were originally for(;;) loops.\n        // I don't know about cryptography to give them better names than loop1-4.\n        var loop1 = function () {\n            var loop4 = function () {\n                if (rsa.p.compareTo(rsa.q) <= 0) {\n                    var t = rsa.p;\n                    rsa.p = rsa.q;\n                    rsa.q = t;\n                }\n                var p1 = rsa.p.subtract(BigInteger.ONE);\n                var q1 = rsa.q.subtract(BigInteger.ONE);\n                var phi = p1.multiply(q1);\n                if (phi.gcd(ee).compareTo(BigInteger.ONE) == 0) {\n                    rsa.n = rsa.p.multiply(rsa.q);\n                    rsa.d = ee.modInverse(phi);\n                    rsa.dmp1 = rsa.d.mod(p1);\n                    rsa.dmq1 = rsa.d.mod(q1);\n                    rsa.coeff = rsa.q.modInverse(rsa.p);\n                    setTimeout(function () { callback(); }, 0); // escape\n                }\n                else {\n                    setTimeout(loop1, 0);\n                }\n            };\n            var loop3 = function () {\n                rsa.q = nbi();\n                rsa.q.fromNumberAsync(qs, 1, rng, function () {\n                    rsa.q.subtract(BigInteger.ONE).gcda(ee, function (r) {\n                        if (r.compareTo(BigInteger.ONE) == 0 && rsa.q.isProbablePrime(10)) {\n                            setTimeout(loop4, 0);\n                        }\n                        else {\n                            setTimeout(loop3, 0);\n                        }\n                    });\n                });\n            };\n            var loop2 = function () {\n                rsa.p = nbi();\n                rsa.p.fromNumberAsync(B - qs, 1, rng, function () {\n                    rsa.p.subtract(BigInteger.ONE).gcda(ee, function (r) {\n                        if (r.compareTo(BigInteger.ONE) == 0 && rsa.p.isProbablePrime(10)) {\n                            setTimeout(loop3, 0);\n                        }\n                        else {\n                            setTimeout(loop2, 0);\n                        }\n                    });\n                });\n            };\n            setTimeout(loop2, 0);\n        };\n        setTimeout(loop1, 0);\n    };\n    RSAKey.prototype.sign = function (text, digestMethod, digestName) {\n        var header = getDigestHeader(digestName);\n        var digest = header + digestMethod(text).toString();\n        var m = pkcs1pad1(digest, this.n.bitLength() / 4);\n        if (m == null) {\n            return null;\n        }\n        var c = this.doPrivate(m);\n        if (c == null) {\n            return null;\n        }\n        var h = c.toString(16);\n        if ((h.length & 1) == 0) {\n            return h;\n        }\n        else {\n            return \"0\" + h;\n        }\n    };\n    RSAKey.prototype.verify = function (text, signature, digestMethod) {\n        var c = parseBigInt(signature, 16);\n        var m = this.doPublic(c);\n        if (m == null) {\n            return null;\n        }\n        var unpadded = m.toString(16).replace(/^1f+00/, \"\");\n        var digest = removeDigestHeader(unpadded);\n        return digest == digestMethod(text).toString();\n    };\n    return RSAKey;\n}());\nexport { RSAKey };\n// Undo PKCS#1 (type 2, random) padding and, if valid, return the plaintext\nfunction pkcs1unpad2(d, n) {\n    var b = d.toByteArray();\n    var i = 0;\n    while (i < b.length && b[i] == 0) {\n        ++i;\n    }\n    if (b.length - i != n - 1 || b[i] != 2) {\n        return null;\n    }\n    ++i;\n    while (b[i] != 0) {\n        if (++i >= b.length) {\n            return null;\n        }\n    }\n    var ret = \"\";\n    while (++i < b.length) {\n        var c = b[i] & 255;\n        if (c < 128) { // utf-8 decode\n            ret += String.fromCharCode(c);\n        }\n        else if ((c > 191) && (c < 224)) {\n            ret += String.fromCharCode(((c & 31) << 6) | (b[i + 1] & 63));\n            ++i;\n        }\n        else {\n            ret += String.fromCharCode(((c & 15) << 12) | ((b[i + 1] & 63) << 6) | (b[i + 2] & 63));\n            i += 2;\n        }\n    }\n    return ret;\n}\n// https://tools.ietf.org/html/rfc3447#page-43\nvar DIGEST_HEADERS = {\n    md2: \"3020300c06082a864886f70d020205000410\",\n    md5: \"3020300c06082a864886f70d020505000410\",\n    sha1: \"3021300906052b0e03021a05000414\",\n    sha224: \"302d300d06096086480165030402040500041c\",\n    sha256: \"3031300d060960864801650304020105000420\",\n    sha384: \"3041300d060960864801650304020205000430\",\n    sha512: \"3051300d060960864801650304020305000440\",\n    ripemd160: \"3021300906052b2403020105000414\"\n};\nfunction getDigestHeader(name) {\n    return DIGEST_HEADERS[name] || \"\";\n}\nfunction removeDigestHeader(str) {\n    for (var name_1 in DIGEST_HEADERS) {\n        if (DIGEST_HEADERS.hasOwnProperty(name_1)) {\n            var header = DIGEST_HEADERS[name_1];\n            var len = header.length;\n            if (str.substr(0, len) == header) {\n                return str.substr(len);\n            }\n        }\n    }\n    return str;\n}\n// Return the PKCS#1 RSA encryption of \"text\" as a Base64-encoded string\n// function RSAEncryptB64(text) {\n//  var h = this.encrypt(text);\n//  if(h) return hex2b64(h); else return null;\n// }\n// public\n// RSAKey.prototype.encrypt_b64 = RSAEncryptB64;\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,UAAU,EAAEC,GAAG,EAAEC,WAAW,QAAQ,QAAQ;AACrD,SAASC,YAAY,QAAQ,OAAO;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAIA,CAAC,GAAGD,CAAC,CAACE,MAAM,GAAG,EAAE,EAAE;IACnBC,OAAO,CAACC,KAAK,CAAC,0BAA0B,CAAC;IACzC,OAAO,IAAI;EACf;EACA,IAAIC,GAAG,GAAGJ,CAAC,GAAGD,CAAC,CAACE,MAAM,GAAG,CAAC;EAC1B,IAAII,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;IAC7BD,MAAM,IAAI,IAAI;EAClB;EACA,IAAIE,CAAC,GAAG,MAAM,GAAGF,MAAM,GAAG,IAAI,GAAGN,CAAC;EAClC,OAAOH,WAAW,CAACW,CAAC,EAAE,EAAE,CAAC;AAC7B;AACA;AACA,SAASC,SAASA,CAACT,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAIA,CAAC,GAAGD,CAAC,CAACE,MAAM,GAAG,EAAE,EAAE;IAAE;IACrBC,OAAO,CAACC,KAAK,CAAC,0BAA0B,CAAC;IACzC,OAAO,IAAI;EACf;EACA,IAAIM,EAAE,GAAG,EAAE;EACX,IAAIC,CAAC,GAAGX,CAAC,CAACE,MAAM,GAAG,CAAC;EACpB,OAAOS,CAAC,IAAI,CAAC,IAAIV,CAAC,GAAG,CAAC,EAAE;IACpB,IAAIW,CAAC,GAAGZ,CAAC,CAACa,UAAU,CAACF,CAAC,EAAE,CAAC;IACzB,IAAIC,CAAC,GAAG,GAAG,EAAE;MAAE;MACXF,EAAE,CAAC,EAAET,CAAC,CAAC,GAAGW,CAAC;IACf,CAAC,MACI,IAAKA,CAAC,GAAG,GAAG,IAAMA,CAAC,GAAG,IAAK,EAAE;MAC9BF,EAAE,CAAC,EAAET,CAAC,CAAC,GAAIW,CAAC,GAAG,EAAE,GAAI,GAAG;MACxBF,EAAE,CAAC,EAAET,CAAC,CAAC,GAAIW,CAAC,IAAI,CAAC,GAAI,GAAG;IAC5B,CAAC,MACI;MACDF,EAAE,CAAC,EAAET,CAAC,CAAC,GAAIW,CAAC,GAAG,EAAE,GAAI,GAAG;MACxBF,EAAE,CAAC,EAAET,CAAC,CAAC,GAAKW,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAC/BF,EAAE,CAAC,EAAET,CAAC,CAAC,GAAIW,CAAC,IAAI,EAAE,GAAI,GAAG;IAC7B;EACJ;EACAF,EAAE,CAAC,EAAET,CAAC,CAAC,GAAG,CAAC;EACX,IAAIa,GAAG,GAAG,IAAIhB,YAAY,CAAC,CAAC;EAC5B,IAAIiB,CAAC,GAAG,EAAE;EACV,OAAOd,CAAC,GAAG,CAAC,EAAE;IAAE;IACZc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACR,OAAOA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MACdD,GAAG,CAACE,SAAS,CAACD,CAAC,CAAC;IACpB;IACAL,EAAE,CAAC,EAAET,CAAC,CAAC,GAAGc,CAAC,CAAC,CAAC,CAAC;EAClB;EACAL,EAAE,CAAC,EAAET,CAAC,CAAC,GAAG,CAAC;EACXS,EAAE,CAAC,EAAET,CAAC,CAAC,GAAG,CAAC;EACX,OAAO,IAAIN,UAAU,CAACe,EAAE,CAAC;AAC7B;AACA;AACA,IAAIO,MAAM,GAAG,aAAe,YAAY;EACpC,SAASA,MAAMA,CAAA,EAAG;IACd,IAAI,CAAChB,CAAC,GAAG,IAAI;IACb,IAAI,CAACiB,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,CAAC,GAAG,IAAI;IACb,IAAI,CAACC,CAAC,GAAG,IAAI;IACb,IAAI,CAACC,CAAC,GAAG,IAAI;IACb,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,KAAK,GAAG,IAAI;EACrB;EACA;EACA;EACA;EACA;EACAP,MAAM,CAACQ,SAAS,CAACC,QAAQ,GAAG,UAAUX,CAAC,EAAE;IACrC,OAAOA,CAAC,CAACY,SAAS,CAAC,IAAI,CAACT,CAAC,EAAE,IAAI,CAACjB,CAAC,CAAC;EACtC,CAAC;EACD;EACA;EACAgB,MAAM,CAACQ,SAAS,CAACG,SAAS,GAAG,UAAUb,CAAC,EAAE;IACtC,IAAI,IAAI,CAACK,CAAC,IAAI,IAAI,IAAI,IAAI,CAACC,CAAC,IAAI,IAAI,EAAE;MAClC,OAAON,CAAC,CAACc,MAAM,CAAC,IAAI,CAACV,CAAC,EAAE,IAAI,CAAClB,CAAC,CAAC;IACnC;IACA;IACA,IAAI6B,EAAE,GAAGf,CAAC,CAACgB,GAAG,CAAC,IAAI,CAACX,CAAC,CAAC,CAACS,MAAM,CAAC,IAAI,CAACP,IAAI,EAAE,IAAI,CAACF,CAAC,CAAC;IAChD,IAAIY,EAAE,GAAGjB,CAAC,CAACgB,GAAG,CAAC,IAAI,CAACV,CAAC,CAAC,CAACQ,MAAM,CAAC,IAAI,CAACN,IAAI,EAAE,IAAI,CAACF,CAAC,CAAC;IAChD,OAAOS,EAAE,CAACG,SAAS,CAACD,EAAE,CAAC,GAAG,CAAC,EAAE;MACzBF,EAAE,GAAGA,EAAE,CAACI,GAAG,CAAC,IAAI,CAACd,CAAC,CAAC;IACvB;IACA,OAAOU,EAAE,CAACK,QAAQ,CAACH,EAAE,CAAC,CAACI,QAAQ,CAAC,IAAI,CAACZ,KAAK,CAAC,CAACO,GAAG,CAAC,IAAI,CAACX,CAAC,CAAC,CAACgB,QAAQ,CAAC,IAAI,CAACf,CAAC,CAAC,CAACa,GAAG,CAACF,EAAE,CAAC;EACpF,CAAC;EACD;EACA;EACA;EACA;EACAf,MAAM,CAACQ,SAAS,CAACY,SAAS,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACzC,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,IAAID,CAAC,CAACpC,MAAM,GAAG,CAAC,IAAIqC,CAAC,CAACrC,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,CAACD,CAAC,GAAGJ,WAAW,CAACyC,CAAC,EAAE,EAAE,CAAC;MAC3B,IAAI,CAACpB,CAAC,GAAGsB,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC;IAC5B,CAAC,MACI;MACDpC,OAAO,CAACC,KAAK,CAAC,wBAAwB,CAAC;IAC3C;EACJ,CAAC;EACD;EACA;EACAa,MAAM,CAACQ,SAAS,CAACgB,OAAO,GAAG,UAAUC,IAAI,EAAE;IACvC,IAAIC,SAAS,GAAI,IAAI,CAAC1C,CAAC,CAAC2C,SAAS,CAAC,CAAC,GAAG,CAAC,IAAK,CAAC;IAC7C,IAAIpC,CAAC,GAAGC,SAAS,CAACiC,IAAI,EAAEC,SAAS,CAAC;IAClC,IAAInC,CAAC,IAAI,IAAI,EAAE;MACX,OAAO,IAAI;IACf;IACA,IAAII,CAAC,GAAG,IAAI,CAACc,QAAQ,CAAClB,CAAC,CAAC;IACxB,IAAII,CAAC,IAAI,IAAI,EAAE;MACX,OAAO,IAAI;IACf;IACA,IAAIiC,CAAC,GAAGjC,CAAC,CAACkC,QAAQ,CAAC,EAAE,CAAC;IACtB,IAAI5C,MAAM,GAAG2C,CAAC,CAAC3C,MAAM;IACrB;IACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,SAAS,GAAG,CAAC,GAAGzC,MAAM,EAAES,CAAC,EAAE,EAAE;MAC7CkC,CAAC,GAAG,GAAG,GAAGA,CAAC;IACf;IACA,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACA5B,MAAM,CAACQ,SAAS,CAACsB,UAAU,GAAG,UAAUT,CAAC,EAAEC,CAAC,EAAES,CAAC,EAAE;IAC7C,IAAIV,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,IAAID,CAAC,CAACpC,MAAM,GAAG,CAAC,IAAIqC,CAAC,CAACrC,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,CAACD,CAAC,GAAGJ,WAAW,CAACyC,CAAC,EAAE,EAAE,CAAC;MAC3B,IAAI,CAACpB,CAAC,GAAGsB,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC;MACxB,IAAI,CAACpB,CAAC,GAAGtB,WAAW,CAACmD,CAAC,EAAE,EAAE,CAAC;IAC/B,CAAC,MACI;MACD7C,OAAO,CAACC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;EACJ,CAAC;EACD;EACA;EACAa,MAAM,CAACQ,SAAS,CAACwB,YAAY,GAAG,UAAUX,CAAC,EAAEC,CAAC,EAAES,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE;IAChE,IAAIhB,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,IAAID,CAAC,CAACpC,MAAM,GAAG,CAAC,IAAIqC,CAAC,CAACrC,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,CAACD,CAAC,GAAGJ,WAAW,CAACyC,CAAC,EAAE,EAAE,CAAC;MAC3B,IAAI,CAACpB,CAAC,GAAGsB,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC;MACxB,IAAI,CAACpB,CAAC,GAAGtB,WAAW,CAACmD,CAAC,EAAE,EAAE,CAAC;MAC3B,IAAI,CAAC5B,CAAC,GAAGvB,WAAW,CAACqD,CAAC,EAAE,EAAE,CAAC;MAC3B,IAAI,CAAC7B,CAAC,GAAGxB,WAAW,CAACsD,CAAC,EAAE,EAAE,CAAC;MAC3B,IAAI,CAAC7B,IAAI,GAAGzB,WAAW,CAACuD,EAAE,EAAE,EAAE,CAAC;MAC/B,IAAI,CAAC7B,IAAI,GAAG1B,WAAW,CAACwD,EAAE,EAAE,EAAE,CAAC;MAC/B,IAAI,CAAC7B,KAAK,GAAG3B,WAAW,CAACyD,CAAC,EAAE,EAAE,CAAC;IACnC,CAAC,MACI;MACDnD,OAAO,CAACC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;EACJ,CAAC;EACD;EACA;EACAa,MAAM,CAACQ,SAAS,CAAC8B,QAAQ,GAAG,UAAUC,CAAC,EAAEjB,CAAC,EAAE;IACxC,IAAIzB,GAAG,GAAG,IAAIhB,YAAY,CAAC,CAAC;IAC5B,IAAI2D,EAAE,GAAGD,CAAC,IAAI,CAAC;IACf,IAAI,CAACtC,CAAC,GAAGsB,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC;IACxB,IAAImB,EAAE,GAAG,IAAI/D,UAAU,CAAC4C,CAAC,EAAE,EAAE,CAAC;IAC9B,SAAS;MACL,SAAS;QACL,IAAI,CAACnB,CAAC,GAAG,IAAIzB,UAAU,CAAC6D,CAAC,GAAGC,EAAE,EAAE,CAAC,EAAE3C,GAAG,CAAC;QACvC,IAAI,IAAI,CAACM,CAAC,CAACe,QAAQ,CAACxC,UAAU,CAACgE,GAAG,CAAC,CAACC,GAAG,CAACF,EAAE,CAAC,CAACzB,SAAS,CAACtC,UAAU,CAACgE,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAACvC,CAAC,CAACyC,eAAe,CAAC,EAAE,CAAC,EAAE;UACtG;QACJ;MACJ;MACA,SAAS;QACL,IAAI,CAACxC,CAAC,GAAG,IAAI1B,UAAU,CAAC8D,EAAE,EAAE,CAAC,EAAE3C,GAAG,CAAC;QACnC,IAAI,IAAI,CAACO,CAAC,CAACc,QAAQ,CAACxC,UAAU,CAACgE,GAAG,CAAC,CAACC,GAAG,CAACF,EAAE,CAAC,CAACzB,SAAS,CAACtC,UAAU,CAACgE,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAACtC,CAAC,CAACwC,eAAe,CAAC,EAAE,CAAC,EAAE;UACtG;QACJ;MACJ;MACA,IAAI,IAAI,CAACzC,CAAC,CAACa,SAAS,CAAC,IAAI,CAACZ,CAAC,CAAC,IAAI,CAAC,EAAE;QAC/B,IAAIyC,CAAC,GAAG,IAAI,CAAC1C,CAAC;QACd,IAAI,CAACA,CAAC,GAAG,IAAI,CAACC,CAAC;QACf,IAAI,CAACA,CAAC,GAAGyC,CAAC;MACd;MACA,IAAIC,EAAE,GAAG,IAAI,CAAC3C,CAAC,CAACe,QAAQ,CAACxC,UAAU,CAACgE,GAAG,CAAC;MACxC,IAAIK,EAAE,GAAG,IAAI,CAAC3C,CAAC,CAACc,QAAQ,CAACxC,UAAU,CAACgE,GAAG,CAAC;MACxC,IAAIM,GAAG,GAAGF,EAAE,CAAC3B,QAAQ,CAAC4B,EAAE,CAAC;MACzB,IAAIC,GAAG,CAACL,GAAG,CAACF,EAAE,CAAC,CAACzB,SAAS,CAACtC,UAAU,CAACgE,GAAG,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAI,CAAC1D,CAAC,GAAG,IAAI,CAACmB,CAAC,CAACgB,QAAQ,CAAC,IAAI,CAACf,CAAC,CAAC;QAChC,IAAI,CAACF,CAAC,GAAGuC,EAAE,CAACQ,UAAU,CAACD,GAAG,CAAC;QAC3B,IAAI,CAAC3C,IAAI,GAAG,IAAI,CAACH,CAAC,CAACY,GAAG,CAACgC,EAAE,CAAC;QAC1B,IAAI,CAACxC,IAAI,GAAG,IAAI,CAACJ,CAAC,CAACY,GAAG,CAACiC,EAAE,CAAC;QAC1B,IAAI,CAACxC,KAAK,GAAG,IAAI,CAACH,CAAC,CAAC6C,UAAU,CAAC,IAAI,CAAC9C,CAAC,CAAC;QACtC;MACJ;IACJ;EACJ,CAAC;EACD;EACA;EACA;EACAH,MAAM,CAACQ,SAAS,CAAC0C,OAAO,GAAG,UAAUC,KAAK,EAAE;IACxC,IAAIxD,CAAC,GAAGf,WAAW,CAACuE,KAAK,EAAE,EAAE,CAAC;IAC9B,IAAI5D,CAAC,GAAG,IAAI,CAACoB,SAAS,CAAChB,CAAC,CAAC;IACzB,IAAIJ,CAAC,IAAI,IAAI,EAAE;MACX,OAAO,IAAI;IACf;IACA,OAAO6D,WAAW,CAAC7D,CAAC,EAAG,IAAI,CAACP,CAAC,CAAC2C,SAAS,CAAC,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;EACxD,CAAC;EACD;EACA3B,MAAM,CAACQ,SAAS,CAAC6C,aAAa,GAAG,UAAUd,CAAC,EAAEjB,CAAC,EAAEgC,QAAQ,EAAE;IACvD,IAAIzD,GAAG,GAAG,IAAIhB,YAAY,CAAC,CAAC;IAC5B,IAAI2D,EAAE,GAAGD,CAAC,IAAI,CAAC;IACf,IAAI,CAACtC,CAAC,GAAGsB,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC;IACxB,IAAImB,EAAE,GAAG,IAAI/D,UAAU,CAAC4C,CAAC,EAAE,EAAE,CAAC;IAC9B,IAAIiC,GAAG,GAAG,IAAI;IACd;IACA;IACA,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAY;MACpB,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAY;QACpB,IAAIF,GAAG,CAACpD,CAAC,CAACa,SAAS,CAACuC,GAAG,CAACnD,CAAC,CAAC,IAAI,CAAC,EAAE;UAC7B,IAAIyC,CAAC,GAAGU,GAAG,CAACpD,CAAC;UACboD,GAAG,CAACpD,CAAC,GAAGoD,GAAG,CAACnD,CAAC;UACbmD,GAAG,CAACnD,CAAC,GAAGyC,CAAC;QACb;QACA,IAAIC,EAAE,GAAGS,GAAG,CAACpD,CAAC,CAACe,QAAQ,CAACxC,UAAU,CAACgE,GAAG,CAAC;QACvC,IAAIK,EAAE,GAAGQ,GAAG,CAACnD,CAAC,CAACc,QAAQ,CAACxC,UAAU,CAACgE,GAAG,CAAC;QACvC,IAAIM,GAAG,GAAGF,EAAE,CAAC3B,QAAQ,CAAC4B,EAAE,CAAC;QACzB,IAAIC,GAAG,CAACL,GAAG,CAACF,EAAE,CAAC,CAACzB,SAAS,CAACtC,UAAU,CAACgE,GAAG,CAAC,IAAI,CAAC,EAAE;UAC5Ca,GAAG,CAACvE,CAAC,GAAGuE,GAAG,CAACpD,CAAC,CAACgB,QAAQ,CAACoC,GAAG,CAACnD,CAAC,CAAC;UAC7BmD,GAAG,CAACrD,CAAC,GAAGuC,EAAE,CAACQ,UAAU,CAACD,GAAG,CAAC;UAC1BO,GAAG,CAAClD,IAAI,GAAGkD,GAAG,CAACrD,CAAC,CAACY,GAAG,CAACgC,EAAE,CAAC;UACxBS,GAAG,CAACjD,IAAI,GAAGiD,GAAG,CAACrD,CAAC,CAACY,GAAG,CAACiC,EAAE,CAAC;UACxBQ,GAAG,CAAChD,KAAK,GAAGgD,GAAG,CAACnD,CAAC,CAAC6C,UAAU,CAACM,GAAG,CAACpD,CAAC,CAAC;UACnCuD,UAAU,CAAC,YAAY;YAAEJ,QAAQ,CAAC,CAAC;UAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,MACI;UACDI,UAAU,CAACF,KAAK,EAAE,CAAC,CAAC;QACxB;MACJ,CAAC;MACD,IAAIG,KAAK,GAAG,SAAAA,CAAA,EAAY;QACpBJ,GAAG,CAACnD,CAAC,GAAGzB,GAAG,CAAC,CAAC;QACb4E,GAAG,CAACnD,CAAC,CAACwD,eAAe,CAACpB,EAAE,EAAE,CAAC,EAAE3C,GAAG,EAAE,YAAY;UAC1C0D,GAAG,CAACnD,CAAC,CAACc,QAAQ,CAACxC,UAAU,CAACgE,GAAG,CAAC,CAACmB,IAAI,CAACpB,EAAE,EAAE,UAAUqB,CAAC,EAAE;YACjD,IAAIA,CAAC,CAAC9C,SAAS,CAACtC,UAAU,CAACgE,GAAG,CAAC,IAAI,CAAC,IAAIa,GAAG,CAACnD,CAAC,CAACwC,eAAe,CAAC,EAAE,CAAC,EAAE;cAC/Dc,UAAU,CAACD,KAAK,EAAE,CAAC,CAAC;YACxB,CAAC,MACI;cACDC,UAAU,CAACC,KAAK,EAAE,CAAC,CAAC;YACxB;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC;MACD,IAAII,KAAK,GAAG,SAAAA,CAAA,EAAY;QACpBR,GAAG,CAACpD,CAAC,GAAGxB,GAAG,CAAC,CAAC;QACb4E,GAAG,CAACpD,CAAC,CAACyD,eAAe,CAACrB,CAAC,GAAGC,EAAE,EAAE,CAAC,EAAE3C,GAAG,EAAE,YAAY;UAC9C0D,GAAG,CAACpD,CAAC,CAACe,QAAQ,CAACxC,UAAU,CAACgE,GAAG,CAAC,CAACmB,IAAI,CAACpB,EAAE,EAAE,UAAUqB,CAAC,EAAE;YACjD,IAAIA,CAAC,CAAC9C,SAAS,CAACtC,UAAU,CAACgE,GAAG,CAAC,IAAI,CAAC,IAAIa,GAAG,CAACpD,CAAC,CAACyC,eAAe,CAAC,EAAE,CAAC,EAAE;cAC/Dc,UAAU,CAACC,KAAK,EAAE,CAAC,CAAC;YACxB,CAAC,MACI;cACDD,UAAU,CAACK,KAAK,EAAE,CAAC,CAAC;YACxB;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC;MACDL,UAAU,CAACK,KAAK,EAAE,CAAC,CAAC;IACxB,CAAC;IACDL,UAAU,CAACF,KAAK,EAAE,CAAC,CAAC;EACxB,CAAC;EACDxD,MAAM,CAACQ,SAAS,CAACwD,IAAI,GAAG,UAAUvC,IAAI,EAAEwC,YAAY,EAAEC,UAAU,EAAE;IAC9D,IAAIC,MAAM,GAAGC,eAAe,CAACF,UAAU,CAAC;IACxC,IAAIG,MAAM,GAAGF,MAAM,GAAGF,YAAY,CAACxC,IAAI,CAAC,CAACI,QAAQ,CAAC,CAAC;IACnD,IAAItC,CAAC,GAAGT,SAAS,CAACuF,MAAM,EAAE,IAAI,CAACrF,CAAC,CAAC2C,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;IACjD,IAAIpC,CAAC,IAAI,IAAI,EAAE;MACX,OAAO,IAAI;IACf;IACA,IAAII,CAAC,GAAG,IAAI,CAACgB,SAAS,CAACpB,CAAC,CAAC;IACzB,IAAII,CAAC,IAAI,IAAI,EAAE;MACX,OAAO,IAAI;IACf;IACA,IAAIiC,CAAC,GAAGjC,CAAC,CAACkC,QAAQ,CAAC,EAAE,CAAC;IACtB,IAAI,CAACD,CAAC,CAAC3C,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;MACrB,OAAO2C,CAAC;IACZ,CAAC,MACI;MACD,OAAO,GAAG,GAAGA,CAAC;IAClB;EACJ,CAAC;EACD5B,MAAM,CAACQ,SAAS,CAAC8D,MAAM,GAAG,UAAU7C,IAAI,EAAE8C,SAAS,EAAEN,YAAY,EAAE;IAC/D,IAAItE,CAAC,GAAGf,WAAW,CAAC2F,SAAS,EAAE,EAAE,CAAC;IAClC,IAAIhF,CAAC,GAAG,IAAI,CAACkB,QAAQ,CAACd,CAAC,CAAC;IACxB,IAAIJ,CAAC,IAAI,IAAI,EAAE;MACX,OAAO,IAAI;IACf;IACA,IAAIiF,QAAQ,GAAGjF,CAAC,CAACsC,QAAQ,CAAC,EAAE,CAAC,CAAC4C,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IACnD,IAAIJ,MAAM,GAAGK,kBAAkB,CAACF,QAAQ,CAAC;IACzC,OAAOH,MAAM,IAAIJ,YAAY,CAACxC,IAAI,CAAC,CAACI,QAAQ,CAAC,CAAC;EAClD,CAAC;EACD,OAAO7B,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,SAASA,MAAM;AACf;AACA,SAASoD,WAAWA,CAAClD,CAAC,EAAElB,CAAC,EAAE;EACvB,IAAI2F,CAAC,GAAGzE,CAAC,CAAC0E,WAAW,CAAC,CAAC;EACvB,IAAIlF,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGiF,CAAC,CAAC1F,MAAM,IAAI0F,CAAC,CAACjF,CAAC,CAAC,IAAI,CAAC,EAAE;IAC9B,EAAEA,CAAC;EACP;EACA,IAAIiF,CAAC,CAAC1F,MAAM,GAAGS,CAAC,IAAIV,CAAC,GAAG,CAAC,IAAI2F,CAAC,CAACjF,CAAC,CAAC,IAAI,CAAC,EAAE;IACpC,OAAO,IAAI;EACf;EACA,EAAEA,CAAC;EACH,OAAOiF,CAAC,CAACjF,CAAC,CAAC,IAAI,CAAC,EAAE;IACd,IAAI,EAAEA,CAAC,IAAIiF,CAAC,CAAC1F,MAAM,EAAE;MACjB,OAAO,IAAI;IACf;EACJ;EACA,IAAI4F,GAAG,GAAG,EAAE;EACZ,OAAO,EAAEnF,CAAC,GAAGiF,CAAC,CAAC1F,MAAM,EAAE;IACnB,IAAIU,CAAC,GAAGgF,CAAC,CAACjF,CAAC,CAAC,GAAG,GAAG;IAClB,IAAIC,CAAC,GAAG,GAAG,EAAE;MAAE;MACXkF,GAAG,IAAIC,MAAM,CAACC,YAAY,CAACpF,CAAC,CAAC;IACjC,CAAC,MACI,IAAKA,CAAC,GAAG,GAAG,IAAMA,CAAC,GAAG,GAAI,EAAE;MAC7BkF,GAAG,IAAIC,MAAM,CAACC,YAAY,CAAE,CAACpF,CAAC,GAAG,EAAE,KAAK,CAAC,GAAKgF,CAAC,CAACjF,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,CAAC;MAC7D,EAAEA,CAAC;IACP,CAAC,MACI;MACDmF,GAAG,IAAIC,MAAM,CAACC,YAAY,CAAE,CAACpF,CAAC,GAAG,EAAE,KAAK,EAAE,GAAK,CAACgF,CAAC,CAACjF,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAE,GAAIiF,CAAC,CAACjF,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,CAAC;MACvFA,CAAC,IAAI,CAAC;IACV;EACJ;EACA,OAAOmF,GAAG;AACd;AACA;AACA,IAAIG,cAAc,GAAG;EACjBC,GAAG,EAAE,sCAAsC;EAC3CC,GAAG,EAAE,sCAAsC;EAC3CC,IAAI,EAAE,gCAAgC;EACtCC,MAAM,EAAE,wCAAwC;EAChDC,MAAM,EAAE,wCAAwC;EAChDC,MAAM,EAAE,wCAAwC;EAChDC,MAAM,EAAE,wCAAwC;EAChDC,SAAS,EAAE;AACf,CAAC;AACD,SAASpB,eAAeA,CAACqB,IAAI,EAAE;EAC3B,OAAOT,cAAc,CAACS,IAAI,CAAC,IAAI,EAAE;AACrC;AACA,SAASf,kBAAkBA,CAACgB,GAAG,EAAE;EAC7B,KAAK,IAAIC,MAAM,IAAIX,cAAc,EAAE;IAC/B,IAAIA,cAAc,CAACY,cAAc,CAACD,MAAM,CAAC,EAAE;MACvC,IAAIxB,MAAM,GAAGa,cAAc,CAACW,MAAM,CAAC;MACnC,IAAIvG,GAAG,GAAG+E,MAAM,CAAClF,MAAM;MACvB,IAAIyG,GAAG,CAACG,MAAM,CAAC,CAAC,EAAEzG,GAAG,CAAC,IAAI+E,MAAM,EAAE;QAC9B,OAAOuB,GAAG,CAACG,MAAM,CAACzG,GAAG,CAAC;MAC1B;IACJ;EACJ;EACA,OAAOsG,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}