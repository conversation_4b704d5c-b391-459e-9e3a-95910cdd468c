{"ast": null, "code": "import * as React from 'react';\nimport { styled } from '@mui/system';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { gridDimensionsSelector } from \"../../hooks/features/dimensions/index.js\";\nimport { gridClasses } from \"../../constants/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Filler = styled('div')({\n  display: 'flex',\n  flexDirection: 'row',\n  width: 'var(--DataGrid-rowWidth)',\n  boxSizing: 'border-box'\n});\nconst Pinned = styled('div')({\n  position: 'sticky',\n  height: '100%',\n  boxSizing: 'border-box',\n  borderTop: '1px solid var(--rowBorderColor)',\n  backgroundColor: 'var(--DataGrid-pinnedBackground)'\n});\nconst PinnedLeft = styled(Pinned)({\n  left: 0,\n  borderRight: '1px solid var(--rowBorderColor)'\n});\nconst PinnedRight = styled(Pinned)({\n  right: 0,\n  borderLeft: '1px solid var(--rowBorderColor)'\n});\nconst Main = styled('div')({\n  flexGrow: 1,\n  borderTop: '1px solid var(--rowBorderColor)'\n});\nfunction GridVirtualScrollerFiller(_ref) {\n  let {\n    rowsLength\n  } = _ref;\n  const apiRef = useGridApiContext();\n  const {\n    viewportOuterSize,\n    minimumSize,\n    hasScrollX,\n    hasScrollY,\n    scrollbarSize,\n    leftPinnedWidth,\n    rightPinnedWidth\n  } = useGridSelector(apiRef, gridDimensionsSelector);\n  const height = hasScrollX ? scrollbarSize : 0;\n  const needsLastRowBorder = viewportOuterSize.height - minimumSize.height > 0;\n  if (height === 0 && !needsLastRowBorder) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(Filler, {\n    className: gridClasses.filler,\n    role: \"presentation\",\n    style: {\n      height,\n      '--rowBorderColor': rowsLength === 0 ? 'transparent' : 'var(--DataGrid-rowBorderColor)'\n    },\n    children: [leftPinnedWidth > 0 && /*#__PURE__*/_jsx(PinnedLeft, {\n      className: gridClasses['filler--pinnedLeft'],\n      style: {\n        width: leftPinnedWidth\n      }\n    }), /*#__PURE__*/_jsx(Main, {}), rightPinnedWidth > 0 && /*#__PURE__*/_jsx(PinnedRight, {\n      className: gridClasses['filler--pinnedRight'],\n      style: {\n        width: rightPinnedWidth + (hasScrollY ? scrollbarSize : 0)\n      }\n    })]\n  });\n}\nconst Memoized = fastMemo(GridVirtualScrollerFiller);\nexport { Memoized as GridVirtualScrollerFiller };", "map": {"version": 3, "names": ["React", "styled", "fastMemo", "useGridSelector", "useGridApiContext", "gridDimensionsSelector", "gridClasses", "jsx", "_jsx", "jsxs", "_jsxs", "Filler", "display", "flexDirection", "width", "boxSizing", "Pinned", "position", "height", "borderTop", "backgroundColor", "PinnedLeft", "left", "borderRight", "PinnedRight", "right", "borderLeft", "Main", "flexGrow", "GridVirtualScrollerFiller", "_ref", "rows<PERSON><PERSON><PERSON>", "apiRef", "viewportOuterSize", "minimumSize", "hasScrollX", "hasScrollY", "scrollbarSize", "leftPinnedWidth", "right<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needsLastRowBorder", "className", "filler", "role", "style", "children", "Memoized"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/virtualization/GridVirtualScrollerFiller.js"], "sourcesContent": ["import * as React from 'react';\nimport { styled } from '@mui/system';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { gridDimensionsSelector } from \"../../hooks/features/dimensions/index.js\";\nimport { gridClasses } from \"../../constants/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Filler = styled('div')({\n  display: 'flex',\n  flexDirection: 'row',\n  width: 'var(--DataGrid-rowWidth)',\n  boxSizing: 'border-box'\n});\nconst Pinned = styled('div')({\n  position: 'sticky',\n  height: '100%',\n  boxSizing: 'border-box',\n  borderTop: '1px solid var(--rowBorderColor)',\n  backgroundColor: 'var(--DataGrid-pinnedBackground)'\n});\nconst PinnedLeft = styled(Pinned)({\n  left: 0,\n  borderRight: '1px solid var(--rowBorderColor)'\n});\nconst PinnedRight = styled(Pinned)({\n  right: 0,\n  borderLeft: '1px solid var(--rowBorderColor)'\n});\nconst Main = styled('div')({\n  flexGrow: 1,\n  borderTop: '1px solid var(--rowBorderColor)'\n});\nfunction GridVirtualScrollerFiller({\n  rowsLength\n}) {\n  const apiRef = useGridApiContext();\n  const {\n    viewportOuterSize,\n    minimumSize,\n    hasScrollX,\n    hasScrollY,\n    scrollbarSize,\n    leftPinnedWidth,\n    rightPinnedWidth\n  } = useGridSelector(apiRef, gridDimensionsSelector);\n  const height = hasScrollX ? scrollbarSize : 0;\n  const needsLastRowBorder = viewportOuterSize.height - minimumSize.height > 0;\n  if (height === 0 && !needsLastRowBorder) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(Filler, {\n    className: gridClasses.filler,\n    role: \"presentation\",\n    style: {\n      height,\n      '--rowBorderColor': rowsLength === 0 ? 'transparent' : 'var(--DataGrid-rowBorderColor)'\n    },\n    children: [leftPinnedWidth > 0 && /*#__PURE__*/_jsx(PinnedLeft, {\n      className: gridClasses['filler--pinnedLeft'],\n      style: {\n        width: leftPinnedWidth\n      }\n    }), /*#__PURE__*/_jsx(Main, {}), rightPinnedWidth > 0 && /*#__PURE__*/_jsx(PinnedRight, {\n      className: gridClasses['filler--pinnedRight'],\n      style: {\n        width: rightPinnedWidth + (hasScrollY ? scrollbarSize : 0)\n      }\n    })]\n  });\n}\nconst Memoized = fastMemo(GridVirtualScrollerFiller);\nexport { Memoized as GridVirtualScrollerFiller };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,sBAAsB,QAAQ,0CAA0C;AACjF,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,MAAM,GAAGV,MAAM,CAAC,KAAK,CAAC,CAAC;EAC3BW,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,KAAK,EAAE,0BAA0B;EACjCC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,MAAM,GAAGf,MAAM,CAAC,KAAK,CAAC,CAAC;EAC3BgB,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,MAAM;EACdH,SAAS,EAAE,YAAY;EACvBI,SAAS,EAAE,iCAAiC;EAC5CC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF,MAAMC,UAAU,GAAGpB,MAAM,CAACe,MAAM,CAAC,CAAC;EAChCM,IAAI,EAAE,CAAC;EACPC,WAAW,EAAE;AACf,CAAC,CAAC;AACF,MAAMC,WAAW,GAAGvB,MAAM,CAACe,MAAM,CAAC,CAAC;EACjCS,KAAK,EAAE,CAAC;EACRC,UAAU,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,IAAI,GAAG1B,MAAM,CAAC,KAAK,CAAC,CAAC;EACzB2B,QAAQ,EAAE,CAAC;EACXT,SAAS,EAAE;AACb,CAAC,CAAC;AACF,SAASU,yBAAyBA,CAAAC,IAAA,EAE/B;EAAA,IAFgC;IACjCC;EACF,CAAC,GAAAD,IAAA;EACC,MAAME,MAAM,GAAG5B,iBAAiB,CAAC,CAAC;EAClC,MAAM;IACJ6B,iBAAiB;IACjBC,WAAW;IACXC,UAAU;IACVC,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC;EACF,CAAC,GAAGpC,eAAe,CAAC6B,MAAM,EAAE3B,sBAAsB,CAAC;EACnD,MAAMa,MAAM,GAAGiB,UAAU,GAAGE,aAAa,GAAG,CAAC;EAC7C,MAAMG,kBAAkB,GAAGP,iBAAiB,CAACf,MAAM,GAAGgB,WAAW,CAAChB,MAAM,GAAG,CAAC;EAC5E,IAAIA,MAAM,KAAK,CAAC,IAAI,CAACsB,kBAAkB,EAAE;IACvC,OAAO,IAAI;EACb;EACA,OAAO,aAAa9B,KAAK,CAACC,MAAM,EAAE;IAChC8B,SAAS,EAAEnC,WAAW,CAACoC,MAAM;IAC7BC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL1B,MAAM;MACN,kBAAkB,EAAEa,UAAU,KAAK,CAAC,GAAG,aAAa,GAAG;IACzD,CAAC;IACDc,QAAQ,EAAE,CAACP,eAAe,GAAG,CAAC,IAAI,aAAa9B,IAAI,CAACa,UAAU,EAAE;MAC9DoB,SAAS,EAAEnC,WAAW,CAAC,oBAAoB,CAAC;MAC5CsC,KAAK,EAAE;QACL9B,KAAK,EAAEwB;MACT;IACF,CAAC,CAAC,EAAE,aAAa9B,IAAI,CAACmB,IAAI,EAAE,CAAC,CAAC,CAAC,EAAEY,gBAAgB,GAAG,CAAC,IAAI,aAAa/B,IAAI,CAACgB,WAAW,EAAE;MACtFiB,SAAS,EAAEnC,WAAW,CAAC,qBAAqB,CAAC;MAC7CsC,KAAK,EAAE;QACL9B,KAAK,EAAEyB,gBAAgB,IAAIH,UAAU,GAAGC,aAAa,GAAG,CAAC;MAC3D;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,MAAMS,QAAQ,GAAG5C,QAAQ,CAAC2B,yBAAyB,CAAC;AACpD,SAASiB,QAAQ,IAAIjB,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}