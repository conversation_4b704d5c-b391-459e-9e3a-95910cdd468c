{"ast": null, "code": "export { useResizeObserver } from \"./useResizeObserver.js\";", "map": {"version": 3, "names": ["useResizeObserver"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-internals/useResizeObserver/index.js"], "sourcesContent": ["export { useResizeObserver } from \"./useResizeObserver.js\";"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}