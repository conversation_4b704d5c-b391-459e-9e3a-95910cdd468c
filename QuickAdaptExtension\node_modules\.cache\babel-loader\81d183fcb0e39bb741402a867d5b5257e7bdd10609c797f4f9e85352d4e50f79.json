{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\n/**\n * Implement the Pipeline Pattern\n *\n * More information and detailed example in (TODO add link to technical doc when ready)\n *\n * Some plugins contains custom logic to enrich data provided by other plugins or components.\n * For instance, the row grouping plugin needs to add / remove the grouping columns when the grid columns are updated.\n *\n * =====================================================================================================================\n *\n * The plugin containing the custom logic must use:\n *\n * - `useGridRegisterPipeProcessor` to register their processor.\n *\n * - `apiRef.current.requestPipeProcessorsApplication` to imperatively re-apply a group.\n *   This method should be used in last resort.\n *   Most of the time, the application should be triggered by an update on the deps of the processor.\n *\n * =====================================================================================================================\n *\n * The plugin or component that needs to enrich its data must use:\n *\n * - `apiRef.current.unstable_applyPipeProcessors` to run in chain all the processors of a given group.\n *\n * - `useGridRegisterPipeApplier` to re-apply the whole pipe when requested.\n *   The applier will be called when:\n *   * a processor is registered.\n *   * `apiRef.current.requestPipeProcessorsApplication` is called for the given group.\n */\nexport const useGridPipeProcessing = apiRef => {\n  const cache = React.useRef({});\n  const isRunning = React.useRef(false);\n  const runAppliers = React.useCallback(groupCache => {\n    if (isRunning.current || !groupCache) {\n      return;\n    }\n    isRunning.current = true;\n    Object.values(groupCache.appliers).forEach(callback => {\n      callback();\n    });\n    isRunning.current = false;\n  }, []);\n  const registerPipeProcessor = React.useCallback((group, id, processor) => {\n    if (!cache.current[group]) {\n      cache.current[group] = {\n        processors: new Map(),\n        processorsAsArray: [],\n        appliers: {}\n      };\n    }\n    const groupCache = cache.current[group];\n    const oldProcessor = groupCache.processors.get(id);\n    if (oldProcessor !== processor) {\n      groupCache.processors.set(id, processor);\n      groupCache.processorsAsArray = Array.from(cache.current[group].processors.values());\n      runAppliers(groupCache);\n    }\n    return () => {\n      cache.current[group].processors.delete(id);\n      cache.current[group].processorsAsArray = Array.from(cache.current[group].processors.values());\n    };\n  }, [runAppliers]);\n  const registerPipeApplier = React.useCallback((group, id, applier) => {\n    if (!cache.current[group]) {\n      cache.current[group] = {\n        processors: new Map(),\n        processorsAsArray: [],\n        appliers: {}\n      };\n    }\n    cache.current[group].appliers[id] = applier;\n    return () => {\n      const _appliers = cache.current[group].appliers,\n        otherAppliers = _objectWithoutPropertiesLoose(_appliers, [id].map(_toPropertyKey));\n      cache.current[group].appliers = otherAppliers;\n    };\n  }, []);\n  const requestPipeProcessorsApplication = React.useCallback(group => {\n    runAppliers(cache.current[group]);\n  }, [runAppliers]);\n  const applyPipeProcessors = React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    const [group, value, context] = args;\n    if (!cache.current[group]) {\n      return value;\n    }\n    const processors = cache.current[group].processorsAsArray;\n    let result = value;\n    for (let i = 0; i < processors.length; i += 1) {\n      result = processors[i](result, context);\n    }\n    return result;\n  }, []);\n  const preProcessingPrivateApi = {\n    registerPipeProcessor,\n    registerPipeApplier,\n    requestPipeProcessorsApplication\n  };\n  const preProcessingPublicApi = {\n    unstable_applyPipeProcessors: applyPipeProcessors\n  };\n  useGridApiMethod(apiRef, preProcessingPrivateApi, 'private');\n  useGridApiMethod(apiRef, preProcessingPublicApi, 'public');\n};", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "useGridApiMethod", "useGridPipeProcessing", "apiRef", "cache", "useRef", "isRunning", "runAppliers", "useCallback", "groupCache", "current", "Object", "values", "appliers", "for<PERSON>ach", "callback", "registerPipeProcessor", "group", "id", "processor", "processors", "Map", "processorsAsArray", "oldProcessor", "get", "set", "Array", "from", "delete", "registerPipeApplier", "applier", "_appliers", "otherAppliers", "map", "requestPipeProcessorsApplication", "applyPipeProcessors", "_len", "arguments", "length", "args", "_key", "value", "context", "result", "i", "preProcessingPrivateApi", "preProcessingPublicApi", "unstable_applyPipeProcessors"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/core/pipeProcessing/useGridPipeProcessing.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\n/**\n * Implement the Pipeline Pattern\n *\n * More information and detailed example in (TODO add link to technical doc when ready)\n *\n * Some plugins contains custom logic to enrich data provided by other plugins or components.\n * For instance, the row grouping plugin needs to add / remove the grouping columns when the grid columns are updated.\n *\n * =====================================================================================================================\n *\n * The plugin containing the custom logic must use:\n *\n * - `useGridRegisterPipeProcessor` to register their processor.\n *\n * - `apiRef.current.requestPipeProcessorsApplication` to imperatively re-apply a group.\n *   This method should be used in last resort.\n *   Most of the time, the application should be triggered by an update on the deps of the processor.\n *\n * =====================================================================================================================\n *\n * The plugin or component that needs to enrich its data must use:\n *\n * - `apiRef.current.unstable_applyPipeProcessors` to run in chain all the processors of a given group.\n *\n * - `useGridRegisterPipeApplier` to re-apply the whole pipe when requested.\n *   The applier will be called when:\n *   * a processor is registered.\n *   * `apiRef.current.requestPipeProcessorsApplication` is called for the given group.\n */\nexport const useGridPipeProcessing = apiRef => {\n  const cache = React.useRef({});\n  const isRunning = React.useRef(false);\n  const runAppliers = React.useCallback(groupCache => {\n    if (isRunning.current || !groupCache) {\n      return;\n    }\n    isRunning.current = true;\n    Object.values(groupCache.appliers).forEach(callback => {\n      callback();\n    });\n    isRunning.current = false;\n  }, []);\n  const registerPipeProcessor = React.useCallback((group, id, processor) => {\n    if (!cache.current[group]) {\n      cache.current[group] = {\n        processors: new Map(),\n        processorsAsArray: [],\n        appliers: {}\n      };\n    }\n    const groupCache = cache.current[group];\n    const oldProcessor = groupCache.processors.get(id);\n    if (oldProcessor !== processor) {\n      groupCache.processors.set(id, processor);\n      groupCache.processorsAsArray = Array.from(cache.current[group].processors.values());\n      runAppliers(groupCache);\n    }\n    return () => {\n      cache.current[group].processors.delete(id);\n      cache.current[group].processorsAsArray = Array.from(cache.current[group].processors.values());\n    };\n  }, [runAppliers]);\n  const registerPipeApplier = React.useCallback((group, id, applier) => {\n    if (!cache.current[group]) {\n      cache.current[group] = {\n        processors: new Map(),\n        processorsAsArray: [],\n        appliers: {}\n      };\n    }\n    cache.current[group].appliers[id] = applier;\n    return () => {\n      const _appliers = cache.current[group].appliers,\n        otherAppliers = _objectWithoutPropertiesLoose(_appliers, [id].map(_toPropertyKey));\n      cache.current[group].appliers = otherAppliers;\n    };\n  }, []);\n  const requestPipeProcessorsApplication = React.useCallback(group => {\n    runAppliers(cache.current[group]);\n  }, [runAppliers]);\n  const applyPipeProcessors = React.useCallback((...args) => {\n    const [group, value, context] = args;\n    if (!cache.current[group]) {\n      return value;\n    }\n    const processors = cache.current[group].processorsAsArray;\n    let result = value;\n    for (let i = 0; i < processors.length; i += 1) {\n      result = processors[i](result, context);\n    }\n    return result;\n  }, []);\n  const preProcessingPrivateApi = {\n    registerPipeProcessor,\n    registerPipeApplier,\n    requestPipeProcessorsApplication\n  };\n  const preProcessingPublicApi = {\n    unstable_applyPipeProcessors: applyPipeProcessors\n  };\n  useGridApiMethod(apiRef, preProcessingPrivateApi, 'private');\n  useGridApiMethod(apiRef, preProcessingPublicApi, 'public');\n};"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAGC,MAAM,IAAI;EAC7C,MAAMC,KAAK,GAAGJ,KAAK,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9B,MAAMC,SAAS,GAAGN,KAAK,CAACK,MAAM,CAAC,KAAK,CAAC;EACrC,MAAME,WAAW,GAAGP,KAAK,CAACQ,WAAW,CAACC,UAAU,IAAI;IAClD,IAAIH,SAAS,CAACI,OAAO,IAAI,CAACD,UAAU,EAAE;MACpC;IACF;IACAH,SAAS,CAACI,OAAO,GAAG,IAAI;IACxBC,MAAM,CAACC,MAAM,CAACH,UAAU,CAACI,QAAQ,CAAC,CAACC,OAAO,CAACC,QAAQ,IAAI;MACrDA,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;IACFT,SAAS,CAACI,OAAO,GAAG,KAAK;EAC3B,CAAC,EAAE,EAAE,CAAC;EACN,MAAMM,qBAAqB,GAAGhB,KAAK,CAACQ,WAAW,CAAC,CAACS,KAAK,EAAEC,EAAE,EAAEC,SAAS,KAAK;IACxE,IAAI,CAACf,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,EAAE;MACzBb,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,GAAG;QACrBG,UAAU,EAAE,IAAIC,GAAG,CAAC,CAAC;QACrBC,iBAAiB,EAAE,EAAE;QACrBT,QAAQ,EAAE,CAAC;MACb,CAAC;IACH;IACA,MAAMJ,UAAU,GAAGL,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC;IACvC,MAAMM,YAAY,GAAGd,UAAU,CAACW,UAAU,CAACI,GAAG,CAACN,EAAE,CAAC;IAClD,IAAIK,YAAY,KAAKJ,SAAS,EAAE;MAC9BV,UAAU,CAACW,UAAU,CAACK,GAAG,CAACP,EAAE,EAAEC,SAAS,CAAC;MACxCV,UAAU,CAACa,iBAAiB,GAAGI,KAAK,CAACC,IAAI,CAACvB,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,CAACG,UAAU,CAACR,MAAM,CAAC,CAAC,CAAC;MACnFL,WAAW,CAACE,UAAU,CAAC;IACzB;IACA,OAAO,MAAM;MACXL,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,CAACG,UAAU,CAACQ,MAAM,CAACV,EAAE,CAAC;MAC1Cd,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,CAACK,iBAAiB,GAAGI,KAAK,CAACC,IAAI,CAACvB,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,CAACG,UAAU,CAACR,MAAM,CAAC,CAAC,CAAC;IAC/F,CAAC;EACH,CAAC,EAAE,CAACL,WAAW,CAAC,CAAC;EACjB,MAAMsB,mBAAmB,GAAG7B,KAAK,CAACQ,WAAW,CAAC,CAACS,KAAK,EAAEC,EAAE,EAAEY,OAAO,KAAK;IACpE,IAAI,CAAC1B,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,EAAE;MACzBb,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,GAAG;QACrBG,UAAU,EAAE,IAAIC,GAAG,CAAC,CAAC;QACrBC,iBAAiB,EAAE,EAAE;QACrBT,QAAQ,EAAE,CAAC;MACb,CAAC;IACH;IACAT,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,CAACJ,QAAQ,CAACK,EAAE,CAAC,GAAGY,OAAO;IAC3C,OAAO,MAAM;MACX,MAAMC,SAAS,GAAG3B,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,CAACJ,QAAQ;QAC7CmB,aAAa,GAAGlC,6BAA6B,CAACiC,SAAS,EAAE,CAACb,EAAE,CAAC,CAACe,GAAG,CAAClC,cAAc,CAAC,CAAC;MACpFK,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,CAACJ,QAAQ,GAAGmB,aAAa;IAC/C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAME,gCAAgC,GAAGlC,KAAK,CAACQ,WAAW,CAACS,KAAK,IAAI;IAClEV,WAAW,CAACH,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,CAAC;EACnC,CAAC,EAAE,CAACV,WAAW,CAAC,CAAC;EACjB,MAAM4B,mBAAmB,GAAGnC,KAAK,CAACQ,WAAW,CAAC,YAAa;IAAA,SAAA4B,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAb,KAAA,CAAAU,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;MAAJD,IAAI,CAAAC,IAAA,IAAAH,SAAA,CAAAG,IAAA;IAAA;IACpD,MAAM,CAACvB,KAAK,EAAEwB,KAAK,EAAEC,OAAO,CAAC,GAAGH,IAAI;IACpC,IAAI,CAACnC,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,EAAE;MACzB,OAAOwB,KAAK;IACd;IACA,MAAMrB,UAAU,GAAGhB,KAAK,CAACM,OAAO,CAACO,KAAK,CAAC,CAACK,iBAAiB;IACzD,IAAIqB,MAAM,GAAGF,KAAK;IAClB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,UAAU,CAACkB,MAAM,EAAEM,CAAC,IAAI,CAAC,EAAE;MAC7CD,MAAM,GAAGvB,UAAU,CAACwB,CAAC,CAAC,CAACD,MAAM,EAAED,OAAO,CAAC;IACzC;IACA,OAAOC,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EACN,MAAME,uBAAuB,GAAG;IAC9B7B,qBAAqB;IACrBa,mBAAmB;IACnBK;EACF,CAAC;EACD,MAAMY,sBAAsB,GAAG;IAC7BC,4BAA4B,EAAEZ;EAChC,CAAC;EACDlC,gBAAgB,CAACE,MAAM,EAAE0C,uBAAuB,EAAE,SAAS,CAAC;EAC5D5C,gBAAgB,CAACE,MAAM,EAAE2C,sBAAsB,EAAE,QAAQ,CAAC;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}