{"ast": null, "code": "/**\n * @mui/styled-engine v6.1.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n/* eslint-disable no-underscore-dangle */\nimport emStyled from '@emotion/styled';\nexport default function styled(tag, options) {\n  const stylesFactory = emStyled(tag, options);\n  if (process.env.NODE_ENV !== 'production') {\n    return function () {\n      const component = typeof tag === 'string' ? `\"${tag}\"` : 'component';\n      for (var _len = arguments.length, styles = new Array(_len), _key = 0; _key < _len; _key++) {\n        styles[_key] = arguments[_key];\n      }\n      if (styles.length === 0) {\n        console.error([`MUI: Seems like you called \\`styled(${component})()\\` without a \\`style\\` argument.`, 'You must provide a `styles` argument: `styled(\"div\")(styleYouForgotToPass)`.'].join('\\n'));\n      } else if (styles.some(style => style === undefined)) {\n        console.error(`MUI: the styled(${component})(...args) API requires all its args to be defined.`);\n      }\n      return stylesFactory(...styles);\n    };\n  }\n  return stylesFactory;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const internal_processStyles = (tag, processor) => {\n  // Emotion attaches all the styles as `__emotion_styles`.\n  // Ref: https://github.com/emotion-js/emotion/blob/16d971d0da229596d6bcc39d282ba9753c9ee7cf/packages/styled/src/base.js#L186\n  if (Array.isArray(tag.__emotion_styles)) {\n    tag.__emotion_styles = processor(tag.__emotion_styles);\n  }\n};\nexport { ThemeContext, keyframes, css } from '@emotion/react';\nexport { default as StyledEngineProvider } from \"./StyledEngineProvider/index.js\";\nexport { default as GlobalStyles } from \"./GlobalStyles/index.js\";", "map": {"version": 3, "names": ["emStyled", "styled", "tag", "options", "stylesFactory", "process", "env", "NODE_ENV", "component", "_len", "arguments", "length", "styles", "Array", "_key", "console", "error", "join", "some", "style", "undefined", "internal_processStyles", "processor", "isArray", "__emotion_styles", "ThemeContext", "keyframes", "css", "default", "StyledEngineProvider", "GlobalStyles"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/styled-engine/index.js"], "sourcesContent": ["/**\n * @mui/styled-engine v6.1.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n/* eslint-disable no-underscore-dangle */\nimport emStyled from '@emotion/styled';\nexport default function styled(tag, options) {\n  const stylesFactory = emStyled(tag, options);\n  if (process.env.NODE_ENV !== 'production') {\n    return (...styles) => {\n      const component = typeof tag === 'string' ? `\"${tag}\"` : 'component';\n      if (styles.length === 0) {\n        console.error([`MUI: Seems like you called \\`styled(${component})()\\` without a \\`style\\` argument.`, 'You must provide a `styles` argument: `styled(\"div\")(styleYouForgotToPass)`.'].join('\\n'));\n      } else if (styles.some(style => style === undefined)) {\n        console.error(`MUI: the styled(${component})(...args) API requires all its args to be defined.`);\n      }\n      return stylesFactory(...styles);\n    };\n  }\n  return stylesFactory;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const internal_processStyles = (tag, processor) => {\n  // Emotion attaches all the styles as `__emotion_styles`.\n  // Ref: https://github.com/emotion-js/emotion/blob/16d971d0da229596d6bcc39d282ba9753c9ee7cf/packages/styled/src/base.js#L186\n  if (Array.isArray(tag.__emotion_styles)) {\n    tag.__emotion_styles = processor(tag.__emotion_styles);\n  }\n};\nexport { ThemeContext, keyframes, css } from '@emotion/react';\nexport { default as StyledEngineProvider } from \"./StyledEngineProvider/index.js\";\nexport { default as GlobalStyles } from \"./GlobalStyles/index.js\";"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,iBAAiB;AACtC,eAAe,SAASC,MAAMA,CAACC,GAAG,EAAEC,OAAO,EAAE;EAC3C,MAAMC,aAAa,GAAGJ,QAAQ,CAACE,GAAG,EAAEC,OAAO,CAAC;EAC5C,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,YAAe;MACpB,MAAMC,SAAS,GAAG,OAAON,GAAG,KAAK,QAAQ,GAAG,IAAIA,GAAG,GAAG,GAAG,WAAW;MAAC,SAAAO,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAD5DC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAANF,MAAM,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAEf,IAAIF,MAAM,CAACD,MAAM,KAAK,CAAC,EAAE;QACvBI,OAAO,CAACC,KAAK,CAAC,CAAC,uCAAuCR,SAAS,qCAAqC,EAAE,8EAA8E,CAAC,CAACS,IAAI,CAAC,IAAI,CAAC,CAAC;MACnM,CAAC,MAAM,IAAIL,MAAM,CAACM,IAAI,CAACC,KAAK,IAAIA,KAAK,KAAKC,SAAS,CAAC,EAAE;QACpDL,OAAO,CAACC,KAAK,CAAC,mBAAmBR,SAAS,qDAAqD,CAAC;MAClG;MACA,OAAOJ,aAAa,CAAC,GAAGQ,MAAM,CAAC;IACjC,CAAC;EACH;EACA,OAAOR,aAAa;AACtB;;AAEA;AACA,OAAO,MAAMiB,sBAAsB,GAAGA,CAACnB,GAAG,EAAEoB,SAAS,KAAK;EACxD;EACA;EACA,IAAIT,KAAK,CAACU,OAAO,CAACrB,GAAG,CAACsB,gBAAgB,CAAC,EAAE;IACvCtB,GAAG,CAACsB,gBAAgB,GAAGF,SAAS,CAACpB,GAAG,CAACsB,gBAAgB,CAAC;EACxD;AACF,CAAC;AACD,SAASC,YAAY,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AAC7D,SAASC,OAAO,IAAIC,oBAAoB,QAAQ,iCAAiC;AACjF,SAASD,OAAO,IAAIE,YAAY,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}