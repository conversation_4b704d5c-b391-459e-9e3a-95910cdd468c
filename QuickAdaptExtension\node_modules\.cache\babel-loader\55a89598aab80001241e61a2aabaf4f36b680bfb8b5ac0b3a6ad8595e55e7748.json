{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { styled } from '@mui/system';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../constants/index.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { useGridApiEventHandler } from \"../hooks/utils/useGridApiEventHandler.js\";\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { gridDimensionsSelector } from \"../hooks/features/dimensions/gridDimensionsSelectors.js\";\nimport { gridDensityFactorSelector } from \"../hooks/features/density/densitySelector.js\";\nimport { gridColumnsTotalWidthSelector } from \"../hooks/features/columns/gridColumnsSelector.js\";\nimport { useTimeout } from \"../hooks/utils/useTimeout.js\";\nimport { getTotalHeaderHeight } from \"../hooks/features/columns/gridColumnsUtils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CLIFF = 1;\nconst SLOP = 1.5;\nconst useUtilityClasses = ownerState => {\n  const {\n    scrollDirection,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['scrollArea', `scrollArea--${scrollDirection}`]\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridScrollAreaRawRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ScrollArea',\n  overridesResolver: (props, styles) => [{\n    [`&.${gridClasses['scrollArea--left']}`]: styles['scrollArea--left']\n  }, {\n    [`&.${gridClasses['scrollArea--right']}`]: styles['scrollArea--right']\n  }, styles.scrollArea]\n})(() => ({\n  position: 'absolute',\n  top: 0,\n  zIndex: 101,\n  width: 20,\n  bottom: 0,\n  [`&.${gridClasses['scrollArea--left']}`]: {\n    left: 0\n  },\n  [`&.${gridClasses['scrollArea--right']}`]: {\n    right: 0\n  }\n}));\nfunction GridScrollAreaRaw(props) {\n  const {\n    scrollDirection\n  } = props;\n  const rootRef = React.useRef(null);\n  const apiRef = useGridApiContext();\n  const timeout = useTimeout();\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  const scrollPosition = React.useRef({\n    left: 0,\n    top: 0\n  });\n  const getCanScrollMore = () => {\n    if (scrollDirection === 'left') {\n      // Only render if the user has not reached yet the start of the list\n      return scrollPosition.current.left > 0;\n    }\n    if (scrollDirection === 'right') {\n      // Only render if the user has not reached yet the end of the list\n      const maxScrollLeft = columnsTotalWidth - dimensions.viewportInnerSize.width;\n      return scrollPosition.current.left < maxScrollLeft;\n    }\n    return false;\n  };\n  const [dragging, setDragging] = React.useState(false);\n  const [canScrollMore, setCanScrollMore] = React.useState(getCanScrollMore);\n  const rootProps = useGridRootProps();\n  const ownerState = _extends({}, rootProps, {\n    scrollDirection\n  });\n  const classes = useUtilityClasses(ownerState);\n  const totalHeaderHeight = getTotalHeaderHeight(apiRef, rootProps);\n  const headerHeight = Math.floor(rootProps.columnHeaderHeight * densityFactor);\n  const style = {\n    height: headerHeight,\n    top: totalHeaderHeight - headerHeight\n  };\n  if (scrollDirection === 'left') {\n    style.left = dimensions.leftPinnedWidth;\n  } else if (scrollDirection === 'right') {\n    style.right = dimensions.rightPinnedWidth + (dimensions.hasScrollX ? dimensions.scrollbarSize : 0);\n  }\n  const handleScrolling = newScrollPosition => {\n    scrollPosition.current = newScrollPosition;\n    setCanScrollMore(getCanScrollMore);\n  };\n  const handleDragOver = useEventCallback(event => {\n    let offset;\n\n    // Prevents showing the forbidden cursor\n    event.preventDefault();\n    if (scrollDirection === 'left') {\n      offset = event.clientX - rootRef.current.getBoundingClientRect().right;\n    } else if (scrollDirection === 'right') {\n      offset = Math.max(1, event.clientX - rootRef.current.getBoundingClientRect().left);\n    } else {\n      throw new Error('MUI X: Wrong drag direction');\n    }\n    offset = (offset - CLIFF) * SLOP + CLIFF;\n\n    // Avoid freeze and inertia.\n    timeout.start(0, () => {\n      apiRef.current.scroll({\n        left: scrollPosition.current.left + offset,\n        top: scrollPosition.current.top\n      });\n    });\n  });\n  const handleColumnHeaderDragStart = useEventCallback(() => {\n    setDragging(true);\n  });\n  const handleColumnHeaderDragEnd = useEventCallback(() => {\n    setDragging(false);\n  });\n  useGridApiEventHandler(apiRef, 'scrollPositionChange', handleScrolling);\n  useGridApiEventHandler(apiRef, 'columnHeaderDragStart', handleColumnHeaderDragStart);\n  useGridApiEventHandler(apiRef, 'columnHeaderDragEnd', handleColumnHeaderDragEnd);\n  if (!dragging || !canScrollMore) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridScrollAreaRawRoot, {\n    ref: rootRef,\n    className: clsx(classes.root),\n    ownerState: ownerState,\n    onDragOver: handleDragOver,\n    style: style\n  });\n}\nexport const GridScrollArea = fastMemo(GridScrollAreaRaw);", "map": {"version": 3, "names": ["_extends", "React", "clsx", "unstable_composeClasses", "composeClasses", "unstable_useEventCallback", "useEventCallback", "styled", "fastMemo", "useGridRootProps", "getDataGridUtilityClass", "gridClasses", "useGridApiContext", "useGridApiEventHandler", "useGridSelector", "gridDimensionsSelector", "gridDensityFactorSelector", "gridColumnsTotalWidthSelector", "useTimeout", "getTotalHeaderHeight", "jsx", "_jsx", "CLIFF", "SLOP", "useUtilityClasses", "ownerState", "scrollDirection", "classes", "slots", "root", "GridScrollAreaRawRoot", "name", "slot", "overridesResolver", "props", "styles", "scrollArea", "position", "top", "zIndex", "width", "bottom", "left", "right", "GridScrollAreaRaw", "rootRef", "useRef", "apiRef", "timeout", "densityFactor", "columnsTotalWidth", "dimensions", "scrollPosition", "getCanScrollMore", "current", "maxScrollLeft", "viewportInnerSize", "dragging", "setDragging", "useState", "canScrollMore", "setCanScrollMore", "rootProps", "totalHeaderHeight", "headerHeight", "Math", "floor", "columnHeaderHeight", "style", "height", "leftPinnedWidth", "right<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasScrollX", "scrollbarSize", "handleScrolling", "newScrollPosition", "handleDragOver", "event", "offset", "preventDefault", "clientX", "getBoundingClientRect", "max", "Error", "start", "scroll", "handleColumnHeaderDragStart", "handleColumnHeaderDragEnd", "ref", "className", "onDragOver", "GridScrollArea"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/GridScrollArea.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { styled } from '@mui/system';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../constants/index.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { useGridApiEventHandler } from \"../hooks/utils/useGridApiEventHandler.js\";\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { gridDimensionsSelector } from \"../hooks/features/dimensions/gridDimensionsSelectors.js\";\nimport { gridDensityFactorSelector } from \"../hooks/features/density/densitySelector.js\";\nimport { gridColumnsTotalWidthSelector } from \"../hooks/features/columns/gridColumnsSelector.js\";\nimport { useTimeout } from \"../hooks/utils/useTimeout.js\";\nimport { getTotalHeaderHeight } from \"../hooks/features/columns/gridColumnsUtils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CLIFF = 1;\nconst SLOP = 1.5;\nconst useUtilityClasses = ownerState => {\n  const {\n    scrollDirection,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['scrollArea', `scrollArea--${scrollDirection}`]\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridScrollAreaRawRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ScrollArea',\n  overridesResolver: (props, styles) => [{\n    [`&.${gridClasses['scrollArea--left']}`]: styles['scrollArea--left']\n  }, {\n    [`&.${gridClasses['scrollArea--right']}`]: styles['scrollArea--right']\n  }, styles.scrollArea]\n})(() => ({\n  position: 'absolute',\n  top: 0,\n  zIndex: 101,\n  width: 20,\n  bottom: 0,\n  [`&.${gridClasses['scrollArea--left']}`]: {\n    left: 0\n  },\n  [`&.${gridClasses['scrollArea--right']}`]: {\n    right: 0\n  }\n}));\nfunction GridScrollAreaRaw(props) {\n  const {\n    scrollDirection\n  } = props;\n  const rootRef = React.useRef(null);\n  const apiRef = useGridApiContext();\n  const timeout = useTimeout();\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  const scrollPosition = React.useRef({\n    left: 0,\n    top: 0\n  });\n  const getCanScrollMore = () => {\n    if (scrollDirection === 'left') {\n      // Only render if the user has not reached yet the start of the list\n      return scrollPosition.current.left > 0;\n    }\n    if (scrollDirection === 'right') {\n      // Only render if the user has not reached yet the end of the list\n      const maxScrollLeft = columnsTotalWidth - dimensions.viewportInnerSize.width;\n      return scrollPosition.current.left < maxScrollLeft;\n    }\n    return false;\n  };\n  const [dragging, setDragging] = React.useState(false);\n  const [canScrollMore, setCanScrollMore] = React.useState(getCanScrollMore);\n  const rootProps = useGridRootProps();\n  const ownerState = _extends({}, rootProps, {\n    scrollDirection\n  });\n  const classes = useUtilityClasses(ownerState);\n  const totalHeaderHeight = getTotalHeaderHeight(apiRef, rootProps);\n  const headerHeight = Math.floor(rootProps.columnHeaderHeight * densityFactor);\n  const style = {\n    height: headerHeight,\n    top: totalHeaderHeight - headerHeight\n  };\n  if (scrollDirection === 'left') {\n    style.left = dimensions.leftPinnedWidth;\n  } else if (scrollDirection === 'right') {\n    style.right = dimensions.rightPinnedWidth + (dimensions.hasScrollX ? dimensions.scrollbarSize : 0);\n  }\n  const handleScrolling = newScrollPosition => {\n    scrollPosition.current = newScrollPosition;\n    setCanScrollMore(getCanScrollMore);\n  };\n  const handleDragOver = useEventCallback(event => {\n    let offset;\n\n    // Prevents showing the forbidden cursor\n    event.preventDefault();\n    if (scrollDirection === 'left') {\n      offset = event.clientX - rootRef.current.getBoundingClientRect().right;\n    } else if (scrollDirection === 'right') {\n      offset = Math.max(1, event.clientX - rootRef.current.getBoundingClientRect().left);\n    } else {\n      throw new Error('MUI X: Wrong drag direction');\n    }\n    offset = (offset - CLIFF) * SLOP + CLIFF;\n\n    // Avoid freeze and inertia.\n    timeout.start(0, () => {\n      apiRef.current.scroll({\n        left: scrollPosition.current.left + offset,\n        top: scrollPosition.current.top\n      });\n    });\n  });\n  const handleColumnHeaderDragStart = useEventCallback(() => {\n    setDragging(true);\n  });\n  const handleColumnHeaderDragEnd = useEventCallback(() => {\n    setDragging(false);\n  });\n  useGridApiEventHandler(apiRef, 'scrollPositionChange', handleScrolling);\n  useGridApiEventHandler(apiRef, 'columnHeaderDragStart', handleColumnHeaderDragStart);\n  useGridApiEventHandler(apiRef, 'columnHeaderDragEnd', handleColumnHeaderDragEnd);\n  if (!dragging || !canScrollMore) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridScrollAreaRawRoot, {\n    ref: rootRef,\n    className: clsx(classes.root),\n    ownerState: ownerState,\n    onDragOver: handleDragOver,\n    style: style\n  });\n}\nexport const GridScrollArea = fastMemo(GridScrollAreaRaw);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,uBAAuB,IAAIC,cAAc,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AACrH,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,uBAAuB,EAAEC,WAAW,QAAQ,uBAAuB;AAC5E,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,sBAAsB,QAAQ,0CAA0C;AACjF,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,sBAAsB,QAAQ,yDAAyD;AAChG,SAASC,yBAAyB,QAAQ,8CAA8C;AACxF,SAASC,6BAA6B,QAAQ,kDAAkD;AAChG,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,oBAAoB,QAAQ,+CAA+C;AACpF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,KAAK,GAAG,CAAC;AACf,MAAMC,IAAI,GAAG,GAAG;AAChB,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,eAAe;IACfC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,YAAY,EAAE,eAAeH,eAAe,EAAE;EACvD,CAAC;EACD,OAAOtB,cAAc,CAACwB,KAAK,EAAElB,uBAAuB,EAAEiB,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,qBAAqB,GAAGvB,MAAM,CAAC,KAAK,EAAE;EAC1CwB,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,YAAY;EAClBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK,CAAC;IACrC,CAAC,KAAKxB,WAAW,CAAC,kBAAkB,CAAC,EAAE,GAAGwB,MAAM,CAAC,kBAAkB;EACrE,CAAC,EAAE;IACD,CAAC,KAAKxB,WAAW,CAAC,mBAAmB,CAAC,EAAE,GAAGwB,MAAM,CAAC,mBAAmB;EACvE,CAAC,EAAEA,MAAM,CAACC,UAAU;AACtB,CAAC,CAAC,CAAC,OAAO;EACRC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,CAAC;EACNC,MAAM,EAAE,GAAG;EACXC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,CAAC;EACT,CAAC,KAAK9B,WAAW,CAAC,kBAAkB,CAAC,EAAE,GAAG;IACxC+B,IAAI,EAAE;EACR,CAAC;EACD,CAAC,KAAK/B,WAAW,CAAC,mBAAmB,CAAC,EAAE,GAAG;IACzCgC,KAAK,EAAE;EACT;AACF,CAAC,CAAC,CAAC;AACH,SAASC,iBAAiBA,CAACV,KAAK,EAAE;EAChC,MAAM;IACJR;EACF,CAAC,GAAGQ,KAAK;EACT,MAAMW,OAAO,GAAG5C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,MAAM,GAAGnC,iBAAiB,CAAC,CAAC;EAClC,MAAMoC,OAAO,GAAG9B,UAAU,CAAC,CAAC;EAC5B,MAAM+B,aAAa,GAAGnC,eAAe,CAACiC,MAAM,EAAE/B,yBAAyB,CAAC;EACxE,MAAMkC,iBAAiB,GAAGpC,eAAe,CAACiC,MAAM,EAAE9B,6BAA6B,CAAC;EAChF,MAAMkC,UAAU,GAAGrC,eAAe,CAACiC,MAAM,EAAEhC,sBAAsB,CAAC;EAClE,MAAMqC,cAAc,GAAGnD,KAAK,CAAC6C,MAAM,CAAC;IAClCJ,IAAI,EAAE,CAAC;IACPJ,GAAG,EAAE;EACP,CAAC,CAAC;EACF,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI3B,eAAe,KAAK,MAAM,EAAE;MAC9B;MACA,OAAO0B,cAAc,CAACE,OAAO,CAACZ,IAAI,GAAG,CAAC;IACxC;IACA,IAAIhB,eAAe,KAAK,OAAO,EAAE;MAC/B;MACA,MAAM6B,aAAa,GAAGL,iBAAiB,GAAGC,UAAU,CAACK,iBAAiB,CAAChB,KAAK;MAC5E,OAAOY,cAAc,CAACE,OAAO,CAACZ,IAAI,GAAGa,aAAa;IACpD;IACA,OAAO,KAAK;EACd,CAAC;EACD,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAGzD,KAAK,CAAC0D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,KAAK,CAAC0D,QAAQ,CAACN,gBAAgB,CAAC;EAC1E,MAAMS,SAAS,GAAGrD,gBAAgB,CAAC,CAAC;EACpC,MAAMgB,UAAU,GAAGzB,QAAQ,CAAC,CAAC,CAAC,EAAE8D,SAAS,EAAE;IACzCpC;EACF,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMsC,iBAAiB,GAAG5C,oBAAoB,CAAC4B,MAAM,EAAEe,SAAS,CAAC;EACjE,MAAME,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACJ,SAAS,CAACK,kBAAkB,GAAGlB,aAAa,CAAC;EAC7E,MAAMmB,KAAK,GAAG;IACZC,MAAM,EAAEL,YAAY;IACpB1B,GAAG,EAAEyB,iBAAiB,GAAGC;EAC3B,CAAC;EACD,IAAItC,eAAe,KAAK,MAAM,EAAE;IAC9B0C,KAAK,CAAC1B,IAAI,GAAGS,UAAU,CAACmB,eAAe;EACzC,CAAC,MAAM,IAAI5C,eAAe,KAAK,OAAO,EAAE;IACtC0C,KAAK,CAACzB,KAAK,GAAGQ,UAAU,CAACoB,gBAAgB,IAAIpB,UAAU,CAACqB,UAAU,GAAGrB,UAAU,CAACsB,aAAa,GAAG,CAAC,CAAC;EACpG;EACA,MAAMC,eAAe,GAAGC,iBAAiB,IAAI;IAC3CvB,cAAc,CAACE,OAAO,GAAGqB,iBAAiB;IAC1Cd,gBAAgB,CAACR,gBAAgB,CAAC;EACpC,CAAC;EACD,MAAMuB,cAAc,GAAGtE,gBAAgB,CAACuE,KAAK,IAAI;IAC/C,IAAIC,MAAM;;IAEV;IACAD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB,IAAIrD,eAAe,KAAK,MAAM,EAAE;MAC9BoD,MAAM,GAAGD,KAAK,CAACG,OAAO,GAAGnC,OAAO,CAACS,OAAO,CAAC2B,qBAAqB,CAAC,CAAC,CAACtC,KAAK;IACxE,CAAC,MAAM,IAAIjB,eAAe,KAAK,OAAO,EAAE;MACtCoD,MAAM,GAAGb,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEL,KAAK,CAACG,OAAO,GAAGnC,OAAO,CAACS,OAAO,CAAC2B,qBAAqB,CAAC,CAAC,CAACvC,IAAI,CAAC;IACpF,CAAC,MAAM;MACL,MAAM,IAAIyC,KAAK,CAAC,6BAA6B,CAAC;IAChD;IACAL,MAAM,GAAG,CAACA,MAAM,GAAGxD,KAAK,IAAIC,IAAI,GAAGD,KAAK;;IAExC;IACA0B,OAAO,CAACoC,KAAK,CAAC,CAAC,EAAE,MAAM;MACrBrC,MAAM,CAACO,OAAO,CAAC+B,MAAM,CAAC;QACpB3C,IAAI,EAAEU,cAAc,CAACE,OAAO,CAACZ,IAAI,GAAGoC,MAAM;QAC1CxC,GAAG,EAAEc,cAAc,CAACE,OAAO,CAAChB;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMgD,2BAA2B,GAAGhF,gBAAgB,CAAC,MAAM;IACzDoD,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC,CAAC;EACF,MAAM6B,yBAAyB,GAAGjF,gBAAgB,CAAC,MAAM;IACvDoD,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC,CAAC;EACF7C,sBAAsB,CAACkC,MAAM,EAAE,sBAAsB,EAAE2B,eAAe,CAAC;EACvE7D,sBAAsB,CAACkC,MAAM,EAAE,uBAAuB,EAAEuC,2BAA2B,CAAC;EACpFzE,sBAAsB,CAACkC,MAAM,EAAE,qBAAqB,EAAEwC,yBAAyB,CAAC;EAChF,IAAI,CAAC9B,QAAQ,IAAI,CAACG,aAAa,EAAE;IAC/B,OAAO,IAAI;EACb;EACA,OAAO,aAAavC,IAAI,CAACS,qBAAqB,EAAE;IAC9C0D,GAAG,EAAE3C,OAAO;IACZ4C,SAAS,EAAEvF,IAAI,CAACyB,OAAO,CAACE,IAAI,CAAC;IAC7BJ,UAAU,EAAEA,UAAU;IACtBiE,UAAU,EAAEd,cAAc;IAC1BR,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AACA,OAAO,MAAMuB,cAAc,GAAGnF,QAAQ,CAACoC,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}