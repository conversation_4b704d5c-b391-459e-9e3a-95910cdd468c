{"ast": null, "code": "import * as React from 'react';\nimport { getGridCellElement, getGridColumnHeaderElement, getGridRowElement } from \"../../../utils/domUtils.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from \"../focus/gridFocusStateSelector.js\";\nexport class MissingRowIdError extends Error {}\n\n/**\n * @requires useGridColumns (method)\n * @requires useGridRows (method)\n * @requires useGridFocus (state)\n * @requires useGridEditing (method)\n * TODO: Impossible priority - useGridEditing also needs to be after useGridParamsApi\n * TODO: Impossible priority - useGridFocus also needs to be after useGridParamsApi\n */\nexport function useGridParamsApi(apiRef) {\n  const getColumnHeaderParams = React.useCallback(field => ({\n    field,\n    colDef: apiRef.current.getColumn(field)\n  }), [apiRef]);\n  const getRowParams = React.useCallback(id => {\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const params = {\n      id,\n      columns: apiRef.current.getAllColumns(),\n      row\n    };\n    return params;\n  }, [apiRef]);\n  const getCellParams = React.useCallback((id, field) => {\n    const colDef = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    const rowNode = apiRef.current.getRowNode(id);\n    if (!row || !rowNode) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const rawValue = row[field];\n    const value = colDef?.valueGetter ? colDef.valueGetter(rawValue, row, colDef, apiRef) : rawValue;\n    const cellFocus = gridFocusCellSelector(apiRef);\n    const cellTabIndex = gridTabIndexCellSelector(apiRef);\n    const params = {\n      id,\n      field,\n      row,\n      rowNode,\n      colDef,\n      cellMode: apiRef.current.getCellMode(id, field),\n      hasFocus: cellFocus !== null && cellFocus.field === field && cellFocus.id === id,\n      tabIndex: cellTabIndex && cellTabIndex.field === field && cellTabIndex.id === id ? 0 : -1,\n      value,\n      formattedValue: value,\n      isEditable: false,\n      api: {}\n    };\n    if (colDef && colDef.valueFormatter) {\n      params.formattedValue = colDef.valueFormatter(value, row, colDef, apiRef);\n    }\n    params.isEditable = colDef && apiRef.current.isCellEditable(params);\n    return params;\n  }, [apiRef]);\n  const getCellValue = React.useCallback((id, field) => {\n    const colDef = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    if (!colDef || !colDef.valueGetter) {\n      return row[field];\n    }\n    return colDef.valueGetter(row[colDef.field], row, colDef, apiRef);\n  }, [apiRef]);\n  const getRowValue = React.useCallback((row, colDef) => {\n    const field = colDef.field;\n    if (!colDef || !colDef.valueGetter) {\n      return row[field];\n    }\n    const value = row[colDef.field];\n    return colDef.valueGetter(value, row, colDef, apiRef);\n  }, [apiRef]);\n  const getRowFormattedValue = React.useCallback((row, colDef) => {\n    const value = getRowValue(row, colDef);\n    if (!colDef || !colDef.valueFormatter) {\n      return value;\n    }\n    return colDef.valueFormatter(value, row, colDef, apiRef);\n  }, [apiRef, getRowValue]);\n  const getColumnHeaderElement = React.useCallback(field => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridColumnHeaderElement(apiRef.current.rootElementRef.current, field);\n  }, [apiRef]);\n  const getRowElement = React.useCallback(id => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridRowElement(apiRef.current.rootElementRef.current, id);\n  }, [apiRef]);\n  const getCellElement = React.useCallback((id, field) => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridCellElement(apiRef.current.rootElementRef.current, {\n      id,\n      field\n    });\n  }, [apiRef]);\n  const paramsApi = {\n    getCellValue,\n    getCellParams,\n    getCellElement,\n    getRowValue,\n    getRowFormattedValue,\n    getRowParams,\n    getRowElement,\n    getColumnHeaderParams,\n    getColumnHeaderElement\n  };\n  useGridApiMethod(apiRef, paramsApi, 'public');\n}", "map": {"version": 3, "names": ["React", "getGridCellElement", "getGridColumnHeaderElement", "getGridRowElement", "useGridApiMethod", "gridFocusCellSelector", "gridTabIndexCellSelector", "MissingRowIdError", "Error", "useGridParamsApi", "apiRef", "getColumnHeaderParams", "useCallback", "field", "colDef", "current", "getColumn", "getRowParams", "id", "row", "getRow", "params", "columns", "getAllColumns", "getCellParams", "rowNode", "getRowNode", "rawValue", "value", "valueGetter", "cellFocus", "cellTabIndex", "cellMode", "getCellMode", "hasFocus", "tabIndex", "formattedValue", "isEditable", "api", "valueFormatter", "isCellEditable", "getCellValue", "getRowValue", "getRowFormattedValue", "getColumnHeaderElement", "rootElementRef", "getRowElement", "getCellElement", "params<PERSON><PERSON>"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/rows/useGridParamsApi.js"], "sourcesContent": ["import * as React from 'react';\nimport { getGridCellElement, getGridColumnHeaderElement, getGridRowElement } from \"../../../utils/domUtils.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from \"../focus/gridFocusStateSelector.js\";\nexport class MissingRowIdError extends Error {}\n\n/**\n * @requires useGridColumns (method)\n * @requires useGridRows (method)\n * @requires useGridFocus (state)\n * @requires useGridEditing (method)\n * TODO: Impossible priority - useGridEditing also needs to be after useGridParamsApi\n * TODO: Impossible priority - useGridFocus also needs to be after useGridParamsApi\n */\nexport function useGridParamsApi(apiRef) {\n  const getColumnHeaderParams = React.useCallback(field => ({\n    field,\n    colDef: apiRef.current.getColumn(field)\n  }), [apiRef]);\n  const getRowParams = React.useCallback(id => {\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const params = {\n      id,\n      columns: apiRef.current.getAllColumns(),\n      row\n    };\n    return params;\n  }, [apiRef]);\n  const getCellParams = React.useCallback((id, field) => {\n    const colDef = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    const rowNode = apiRef.current.getRowNode(id);\n    if (!row || !rowNode) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const rawValue = row[field];\n    const value = colDef?.valueGetter ? colDef.valueGetter(rawValue, row, colDef, apiRef) : rawValue;\n    const cellFocus = gridFocusCellSelector(apiRef);\n    const cellTabIndex = gridTabIndexCellSelector(apiRef);\n    const params = {\n      id,\n      field,\n      row,\n      rowNode,\n      colDef,\n      cellMode: apiRef.current.getCellMode(id, field),\n      hasFocus: cellFocus !== null && cellFocus.field === field && cellFocus.id === id,\n      tabIndex: cellTabIndex && cellTabIndex.field === field && cellTabIndex.id === id ? 0 : -1,\n      value,\n      formattedValue: value,\n      isEditable: false,\n      api: {}\n    };\n    if (colDef && colDef.valueFormatter) {\n      params.formattedValue = colDef.valueFormatter(value, row, colDef, apiRef);\n    }\n    params.isEditable = colDef && apiRef.current.isCellEditable(params);\n    return params;\n  }, [apiRef]);\n  const getCellValue = React.useCallback((id, field) => {\n    const colDef = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    if (!colDef || !colDef.valueGetter) {\n      return row[field];\n    }\n    return colDef.valueGetter(row[colDef.field], row, colDef, apiRef);\n  }, [apiRef]);\n  const getRowValue = React.useCallback((row, colDef) => {\n    const field = colDef.field;\n    if (!colDef || !colDef.valueGetter) {\n      return row[field];\n    }\n    const value = row[colDef.field];\n    return colDef.valueGetter(value, row, colDef, apiRef);\n  }, [apiRef]);\n  const getRowFormattedValue = React.useCallback((row, colDef) => {\n    const value = getRowValue(row, colDef);\n    if (!colDef || !colDef.valueFormatter) {\n      return value;\n    }\n    return colDef.valueFormatter(value, row, colDef, apiRef);\n  }, [apiRef, getRowValue]);\n  const getColumnHeaderElement = React.useCallback(field => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridColumnHeaderElement(apiRef.current.rootElementRef.current, field);\n  }, [apiRef]);\n  const getRowElement = React.useCallback(id => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridRowElement(apiRef.current.rootElementRef.current, id);\n  }, [apiRef]);\n  const getCellElement = React.useCallback((id, field) => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridCellElement(apiRef.current.rootElementRef.current, {\n      id,\n      field\n    });\n  }, [apiRef]);\n  const paramsApi = {\n    getCellValue,\n    getCellParams,\n    getCellElement,\n    getRowValue,\n    getRowFormattedValue,\n    getRowParams,\n    getRowElement,\n    getColumnHeaderParams,\n    getColumnHeaderElement\n  };\n  useGridApiMethod(apiRef, paramsApi, 'public');\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,0BAA0B,EAAEC,iBAAiB,QAAQ,4BAA4B;AAC9G,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,qBAAqB,EAAEC,wBAAwB,QAAQ,oCAAoC;AACpG,OAAO,MAAMC,iBAAiB,SAASC,KAAK,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EACvC,MAAMC,qBAAqB,GAAGX,KAAK,CAACY,WAAW,CAACC,KAAK,KAAK;IACxDA,KAAK;IACLC,MAAM,EAAEJ,MAAM,CAACK,OAAO,CAACC,SAAS,CAACH,KAAK;EACxC,CAAC,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;EACb,MAAMO,YAAY,GAAGjB,KAAK,CAACY,WAAW,CAACM,EAAE,IAAI;IAC3C,MAAMC,GAAG,GAAGT,MAAM,CAACK,OAAO,CAACK,MAAM,CAACF,EAAE,CAAC;IACrC,IAAI,CAACC,GAAG,EAAE;MACR,MAAM,IAAIZ,iBAAiB,CAAC,mBAAmBW,EAAE,QAAQ,CAAC;IAC5D;IACA,MAAMG,MAAM,GAAG;MACbH,EAAE;MACFI,OAAO,EAAEZ,MAAM,CAACK,OAAO,CAACQ,aAAa,CAAC,CAAC;MACvCJ;IACF,CAAC;IACD,OAAOE,MAAM;EACf,CAAC,EAAE,CAACX,MAAM,CAAC,CAAC;EACZ,MAAMc,aAAa,GAAGxB,KAAK,CAACY,WAAW,CAAC,CAACM,EAAE,EAAEL,KAAK,KAAK;IACrD,MAAMC,MAAM,GAAGJ,MAAM,CAACK,OAAO,CAACC,SAAS,CAACH,KAAK,CAAC;IAC9C,MAAMM,GAAG,GAAGT,MAAM,CAACK,OAAO,CAACK,MAAM,CAACF,EAAE,CAAC;IACrC,MAAMO,OAAO,GAAGf,MAAM,CAACK,OAAO,CAACW,UAAU,CAACR,EAAE,CAAC;IAC7C,IAAI,CAACC,GAAG,IAAI,CAACM,OAAO,EAAE;MACpB,MAAM,IAAIlB,iBAAiB,CAAC,mBAAmBW,EAAE,QAAQ,CAAC;IAC5D;IACA,MAAMS,QAAQ,GAAGR,GAAG,CAACN,KAAK,CAAC;IAC3B,MAAMe,KAAK,GAAGd,MAAM,EAAEe,WAAW,GAAGf,MAAM,CAACe,WAAW,CAACF,QAAQ,EAAER,GAAG,EAAEL,MAAM,EAAEJ,MAAM,CAAC,GAAGiB,QAAQ;IAChG,MAAMG,SAAS,GAAGzB,qBAAqB,CAACK,MAAM,CAAC;IAC/C,MAAMqB,YAAY,GAAGzB,wBAAwB,CAACI,MAAM,CAAC;IACrD,MAAMW,MAAM,GAAG;MACbH,EAAE;MACFL,KAAK;MACLM,GAAG;MACHM,OAAO;MACPX,MAAM;MACNkB,QAAQ,EAAEtB,MAAM,CAACK,OAAO,CAACkB,WAAW,CAACf,EAAE,EAAEL,KAAK,CAAC;MAC/CqB,QAAQ,EAAEJ,SAAS,KAAK,IAAI,IAAIA,SAAS,CAACjB,KAAK,KAAKA,KAAK,IAAIiB,SAAS,CAACZ,EAAE,KAAKA,EAAE;MAChFiB,QAAQ,EAAEJ,YAAY,IAAIA,YAAY,CAAClB,KAAK,KAAKA,KAAK,IAAIkB,YAAY,CAACb,EAAE,KAAKA,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;MACzFU,KAAK;MACLQ,cAAc,EAAER,KAAK;MACrBS,UAAU,EAAE,KAAK;MACjBC,GAAG,EAAE,CAAC;IACR,CAAC;IACD,IAAIxB,MAAM,IAAIA,MAAM,CAACyB,cAAc,EAAE;MACnClB,MAAM,CAACe,cAAc,GAAGtB,MAAM,CAACyB,cAAc,CAACX,KAAK,EAAET,GAAG,EAAEL,MAAM,EAAEJ,MAAM,CAAC;IAC3E;IACAW,MAAM,CAACgB,UAAU,GAAGvB,MAAM,IAAIJ,MAAM,CAACK,OAAO,CAACyB,cAAc,CAACnB,MAAM,CAAC;IACnE,OAAOA,MAAM;EACf,CAAC,EAAE,CAACX,MAAM,CAAC,CAAC;EACZ,MAAM+B,YAAY,GAAGzC,KAAK,CAACY,WAAW,CAAC,CAACM,EAAE,EAAEL,KAAK,KAAK;IACpD,MAAMC,MAAM,GAAGJ,MAAM,CAACK,OAAO,CAACC,SAAS,CAACH,KAAK,CAAC;IAC9C,MAAMM,GAAG,GAAGT,MAAM,CAACK,OAAO,CAACK,MAAM,CAACF,EAAE,CAAC;IACrC,IAAI,CAACC,GAAG,EAAE;MACR,MAAM,IAAIZ,iBAAiB,CAAC,mBAAmBW,EAAE,QAAQ,CAAC;IAC5D;IACA,IAAI,CAACJ,MAAM,IAAI,CAACA,MAAM,CAACe,WAAW,EAAE;MAClC,OAAOV,GAAG,CAACN,KAAK,CAAC;IACnB;IACA,OAAOC,MAAM,CAACe,WAAW,CAACV,GAAG,CAACL,MAAM,CAACD,KAAK,CAAC,EAAEM,GAAG,EAAEL,MAAM,EAAEJ,MAAM,CAAC;EACnE,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAMgC,WAAW,GAAG1C,KAAK,CAACY,WAAW,CAAC,CAACO,GAAG,EAAEL,MAAM,KAAK;IACrD,MAAMD,KAAK,GAAGC,MAAM,CAACD,KAAK;IAC1B,IAAI,CAACC,MAAM,IAAI,CAACA,MAAM,CAACe,WAAW,EAAE;MAClC,OAAOV,GAAG,CAACN,KAAK,CAAC;IACnB;IACA,MAAMe,KAAK,GAAGT,GAAG,CAACL,MAAM,CAACD,KAAK,CAAC;IAC/B,OAAOC,MAAM,CAACe,WAAW,CAACD,KAAK,EAAET,GAAG,EAAEL,MAAM,EAAEJ,MAAM,CAAC;EACvD,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAMiC,oBAAoB,GAAG3C,KAAK,CAACY,WAAW,CAAC,CAACO,GAAG,EAAEL,MAAM,KAAK;IAC9D,MAAMc,KAAK,GAAGc,WAAW,CAACvB,GAAG,EAAEL,MAAM,CAAC;IACtC,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACyB,cAAc,EAAE;MACrC,OAAOX,KAAK;IACd;IACA,OAAOd,MAAM,CAACyB,cAAc,CAACX,KAAK,EAAET,GAAG,EAAEL,MAAM,EAAEJ,MAAM,CAAC;EAC1D,CAAC,EAAE,CAACA,MAAM,EAAEgC,WAAW,CAAC,CAAC;EACzB,MAAME,sBAAsB,GAAG5C,KAAK,CAACY,WAAW,CAACC,KAAK,IAAI;IACxD,IAAI,CAACH,MAAM,CAACK,OAAO,CAAC8B,cAAc,CAAC9B,OAAO,EAAE;MAC1C,OAAO,IAAI;IACb;IACA,OAAOb,0BAA0B,CAACQ,MAAM,CAACK,OAAO,CAAC8B,cAAc,CAAC9B,OAAO,EAAEF,KAAK,CAAC;EACjF,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;EACZ,MAAMoC,aAAa,GAAG9C,KAAK,CAACY,WAAW,CAACM,EAAE,IAAI;IAC5C,IAAI,CAACR,MAAM,CAACK,OAAO,CAAC8B,cAAc,CAAC9B,OAAO,EAAE;MAC1C,OAAO,IAAI;IACb;IACA,OAAOZ,iBAAiB,CAACO,MAAM,CAACK,OAAO,CAAC8B,cAAc,CAAC9B,OAAO,EAAEG,EAAE,CAAC;EACrE,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;EACZ,MAAMqC,cAAc,GAAG/C,KAAK,CAACY,WAAW,CAAC,CAACM,EAAE,EAAEL,KAAK,KAAK;IACtD,IAAI,CAACH,MAAM,CAACK,OAAO,CAAC8B,cAAc,CAAC9B,OAAO,EAAE;MAC1C,OAAO,IAAI;IACb;IACA,OAAOd,kBAAkB,CAACS,MAAM,CAACK,OAAO,CAAC8B,cAAc,CAAC9B,OAAO,EAAE;MAC/DG,EAAE;MACFL;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;EACZ,MAAMsC,SAAS,GAAG;IAChBP,YAAY;IACZjB,aAAa;IACbuB,cAAc;IACdL,WAAW;IACXC,oBAAoB;IACpB1B,YAAY;IACZ6B,aAAa;IACbnC,qBAAqB;IACrBiC;EACF,CAAC;EACDxC,gBAAgB,CAACM,MAAM,EAAEsC,SAAS,EAAE,QAAQ,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}