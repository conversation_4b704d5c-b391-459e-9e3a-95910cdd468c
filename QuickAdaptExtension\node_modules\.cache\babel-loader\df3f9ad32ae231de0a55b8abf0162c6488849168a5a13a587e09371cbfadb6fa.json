{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../utils/index.js\";\nimport { isFunction } from \"../../utils/utils.js\";\nexport const useGridStateInitialization = apiRef => {\n  const controlStateMapRef = React.useRef({});\n  const [, rawForceUpdate] = React.useState();\n  const registerControlState = React.useCallback(controlStateItem => {\n    controlStateMapRef.current[controlStateItem.stateId] = controlStateItem;\n  }, []);\n  const setState = React.useCallback((state, reason) => {\n    let newState;\n    if (isFunction(state)) {\n      newState = state(apiRef.current.state);\n    } else {\n      newState = state;\n    }\n    if (apiRef.current.state === newState) {\n      return false;\n    }\n    let ignoreSetState = false;\n\n    // Apply the control state constraints\n    const updatedControlStateIds = [];\n    Object.keys(controlStateMapRef.current).forEach(stateId => {\n      const controlState = controlStateMapRef.current[stateId];\n      const oldSubState = controlState.stateSelector(apiRef.current.state, apiRef.current.instanceId);\n      const newSubState = controlState.stateSelector(newState, apiRef.current.instanceId);\n      if (newSubState === oldSubState) {\n        return;\n      }\n      updatedControlStateIds.push({\n        stateId: controlState.stateId,\n        hasPropChanged: newSubState !== controlState.propModel\n      });\n\n      // The state is controlled, the prop should always win\n      if (controlState.propModel !== undefined && newSubState !== controlState.propModel) {\n        ignoreSetState = true;\n      }\n    });\n    if (updatedControlStateIds.length > 1) {\n      // Each hook modify its own state, and it should not leak\n      // Events are here to forward to other hooks and apply changes.\n      // You are trying to update several states in a no isolated way.\n      throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${updatedControlStateIds[0].stateId}, therefore, you're not allowed to update ${updatedControlStateIds.map(el => el.stateId).join(', ')} in the same transaction.`);\n    }\n    if (!ignoreSetState) {\n      // We always assign it as we mutate rows for perf reason.\n      apiRef.current.state = newState;\n      apiRef.current.publishEvent('stateChange', newState);\n      apiRef.current.store.update(newState);\n    }\n    if (updatedControlStateIds.length === 1) {\n      const {\n        stateId,\n        hasPropChanged\n      } = updatedControlStateIds[0];\n      const controlState = controlStateMapRef.current[stateId];\n      const model = controlState.stateSelector(newState, apiRef.current.instanceId);\n      if (controlState.propOnChange && hasPropChanged) {\n        controlState.propOnChange(model, {\n          reason,\n          api: apiRef.current\n        });\n      }\n      if (!ignoreSetState) {\n        apiRef.current.publishEvent(controlState.changeEvent, model, {\n          reason\n        });\n      }\n    }\n    return !ignoreSetState;\n  }, [apiRef]);\n  const updateControlState = React.useCallback((key, state, reason) => {\n    return apiRef.current.setState(previousState => {\n      return _extends({}, previousState, {\n        [key]: state(previousState[key])\n      });\n    }, reason);\n  }, [apiRef]);\n  const forceUpdate = React.useCallback(() => rawForceUpdate(() => apiRef.current.state), [apiRef]);\n  const publicStateApi = {\n    setState,\n    forceUpdate\n  };\n  const privateStateApi = {\n    updateControlState,\n    registerControlState\n  };\n  useGridApiMethod(apiRef, publicStateApi, 'public');\n  useGridApiMethod(apiRef, privateStateApi, 'private');\n};", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "isFunction", "useGridStateInitialization", "apiRef", "controlStateMapRef", "useRef", "rawForceUpdate", "useState", "registerControlState", "useCallback", "controlStateItem", "current", "stateId", "setState", "state", "reason", "newState", "ignoreSetState", "updatedControlStateIds", "Object", "keys", "for<PERSON>ach", "controlState", "oldSubState", "stateSelector", "instanceId", "newSubState", "push", "hasPropChanged", "propModel", "undefined", "length", "Error", "map", "el", "join", "publishEvent", "store", "update", "model", "propOnChange", "api", "changeEvent", "updateControlState", "key", "previousState", "forceUpdate", "publicStateApi", "privateStateApi"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/core/useGridStateInitialization.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../utils/index.js\";\nimport { isFunction } from \"../../utils/utils.js\";\nexport const useGridStateInitialization = apiRef => {\n  const controlStateMapRef = React.useRef({});\n  const [, rawForceUpdate] = React.useState();\n  const registerControlState = React.useCallback(controlStateItem => {\n    controlStateMapRef.current[controlStateItem.stateId] = controlStateItem;\n  }, []);\n  const setState = React.useCallback((state, reason) => {\n    let newState;\n    if (isFunction(state)) {\n      newState = state(apiRef.current.state);\n    } else {\n      newState = state;\n    }\n    if (apiRef.current.state === newState) {\n      return false;\n    }\n    let ignoreSetState = false;\n\n    // Apply the control state constraints\n    const updatedControlStateIds = [];\n    Object.keys(controlStateMapRef.current).forEach(stateId => {\n      const controlState = controlStateMapRef.current[stateId];\n      const oldSubState = controlState.stateSelector(apiRef.current.state, apiRef.current.instanceId);\n      const newSubState = controlState.stateSelector(newState, apiRef.current.instanceId);\n      if (newSubState === oldSubState) {\n        return;\n      }\n      updatedControlStateIds.push({\n        stateId: controlState.stateId,\n        hasPropChanged: newSubState !== controlState.propModel\n      });\n\n      // The state is controlled, the prop should always win\n      if (controlState.propModel !== undefined && newSubState !== controlState.propModel) {\n        ignoreSetState = true;\n      }\n    });\n    if (updatedControlStateIds.length > 1) {\n      // Each hook modify its own state, and it should not leak\n      // Events are here to forward to other hooks and apply changes.\n      // You are trying to update several states in a no isolated way.\n      throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${updatedControlStateIds[0].stateId}, therefore, you're not allowed to update ${updatedControlStateIds.map(el => el.stateId).join(', ')} in the same transaction.`);\n    }\n    if (!ignoreSetState) {\n      // We always assign it as we mutate rows for perf reason.\n      apiRef.current.state = newState;\n      apiRef.current.publishEvent('stateChange', newState);\n      apiRef.current.store.update(newState);\n    }\n    if (updatedControlStateIds.length === 1) {\n      const {\n        stateId,\n        hasPropChanged\n      } = updatedControlStateIds[0];\n      const controlState = controlStateMapRef.current[stateId];\n      const model = controlState.stateSelector(newState, apiRef.current.instanceId);\n      if (controlState.propOnChange && hasPropChanged) {\n        controlState.propOnChange(model, {\n          reason,\n          api: apiRef.current\n        });\n      }\n      if (!ignoreSetState) {\n        apiRef.current.publishEvent(controlState.changeEvent, model, {\n          reason\n        });\n      }\n    }\n    return !ignoreSetState;\n  }, [apiRef]);\n  const updateControlState = React.useCallback((key, state, reason) => {\n    return apiRef.current.setState(previousState => {\n      return _extends({}, previousState, {\n        [key]: state(previousState[key])\n      });\n    }, reason);\n  }, [apiRef]);\n  const forceUpdate = React.useCallback(() => rawForceUpdate(() => apiRef.current.state), [apiRef]);\n  const publicStateApi = {\n    setState,\n    forceUpdate\n  };\n  const privateStateApi = {\n    updateControlState,\n    registerControlState\n  };\n  useGridApiMethod(apiRef, publicStateApi, 'public');\n  useGridApiMethod(apiRef, privateStateApi, 'private');\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAO,MAAMC,0BAA0B,GAAGC,MAAM,IAAI;EAClD,MAAMC,kBAAkB,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3C,MAAM,GAAGC,cAAc,CAAC,GAAGP,KAAK,CAACQ,QAAQ,CAAC,CAAC;EAC3C,MAAMC,oBAAoB,GAAGT,KAAK,CAACU,WAAW,CAACC,gBAAgB,IAAI;IACjEN,kBAAkB,CAACO,OAAO,CAACD,gBAAgB,CAACE,OAAO,CAAC,GAAGF,gBAAgB;EACzE,CAAC,EAAE,EAAE,CAAC;EACN,MAAMG,QAAQ,GAAGd,KAAK,CAACU,WAAW,CAAC,CAACK,KAAK,EAAEC,MAAM,KAAK;IACpD,IAAIC,QAAQ;IACZ,IAAIf,UAAU,CAACa,KAAK,CAAC,EAAE;MACrBE,QAAQ,GAAGF,KAAK,CAACX,MAAM,CAACQ,OAAO,CAACG,KAAK,CAAC;IACxC,CAAC,MAAM;MACLE,QAAQ,GAAGF,KAAK;IAClB;IACA,IAAIX,MAAM,CAACQ,OAAO,CAACG,KAAK,KAAKE,QAAQ,EAAE;MACrC,OAAO,KAAK;IACd;IACA,IAAIC,cAAc,GAAG,KAAK;;IAE1B;IACA,MAAMC,sBAAsB,GAAG,EAAE;IACjCC,MAAM,CAACC,IAAI,CAAChB,kBAAkB,CAACO,OAAO,CAAC,CAACU,OAAO,CAACT,OAAO,IAAI;MACzD,MAAMU,YAAY,GAAGlB,kBAAkB,CAACO,OAAO,CAACC,OAAO,CAAC;MACxD,MAAMW,WAAW,GAAGD,YAAY,CAACE,aAAa,CAACrB,MAAM,CAACQ,OAAO,CAACG,KAAK,EAAEX,MAAM,CAACQ,OAAO,CAACc,UAAU,CAAC;MAC/F,MAAMC,WAAW,GAAGJ,YAAY,CAACE,aAAa,CAACR,QAAQ,EAAEb,MAAM,CAACQ,OAAO,CAACc,UAAU,CAAC;MACnF,IAAIC,WAAW,KAAKH,WAAW,EAAE;QAC/B;MACF;MACAL,sBAAsB,CAACS,IAAI,CAAC;QAC1Bf,OAAO,EAAEU,YAAY,CAACV,OAAO;QAC7BgB,cAAc,EAAEF,WAAW,KAAKJ,YAAY,CAACO;MAC/C,CAAC,CAAC;;MAEF;MACA,IAAIP,YAAY,CAACO,SAAS,KAAKC,SAAS,IAAIJ,WAAW,KAAKJ,YAAY,CAACO,SAAS,EAAE;QAClFZ,cAAc,GAAG,IAAI;MACvB;IACF,CAAC,CAAC;IACF,IAAIC,sBAAsB,CAACa,MAAM,GAAG,CAAC,EAAE;MACrC;MACA;MACA;MACA,MAAM,IAAIC,KAAK,CAAC,0FAA0Fd,sBAAsB,CAAC,CAAC,CAAC,CAACN,OAAO,6CAA6CM,sBAAsB,CAACe,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACtB,OAAO,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC;IAC7Q;IACA,IAAI,CAAClB,cAAc,EAAE;MACnB;MACAd,MAAM,CAACQ,OAAO,CAACG,KAAK,GAAGE,QAAQ;MAC/Bb,MAAM,CAACQ,OAAO,CAACyB,YAAY,CAAC,aAAa,EAAEpB,QAAQ,CAAC;MACpDb,MAAM,CAACQ,OAAO,CAAC0B,KAAK,CAACC,MAAM,CAACtB,QAAQ,CAAC;IACvC;IACA,IAAIE,sBAAsB,CAACa,MAAM,KAAK,CAAC,EAAE;MACvC,MAAM;QACJnB,OAAO;QACPgB;MACF,CAAC,GAAGV,sBAAsB,CAAC,CAAC,CAAC;MAC7B,MAAMI,YAAY,GAAGlB,kBAAkB,CAACO,OAAO,CAACC,OAAO,CAAC;MACxD,MAAM2B,KAAK,GAAGjB,YAAY,CAACE,aAAa,CAACR,QAAQ,EAAEb,MAAM,CAACQ,OAAO,CAACc,UAAU,CAAC;MAC7E,IAAIH,YAAY,CAACkB,YAAY,IAAIZ,cAAc,EAAE;QAC/CN,YAAY,CAACkB,YAAY,CAACD,KAAK,EAAE;UAC/BxB,MAAM;UACN0B,GAAG,EAAEtC,MAAM,CAACQ;QACd,CAAC,CAAC;MACJ;MACA,IAAI,CAACM,cAAc,EAAE;QACnBd,MAAM,CAACQ,OAAO,CAACyB,YAAY,CAACd,YAAY,CAACoB,WAAW,EAAEH,KAAK,EAAE;UAC3DxB;QACF,CAAC,CAAC;MACJ;IACF;IACA,OAAO,CAACE,cAAc;EACxB,CAAC,EAAE,CAACd,MAAM,CAAC,CAAC;EACZ,MAAMwC,kBAAkB,GAAG5C,KAAK,CAACU,WAAW,CAAC,CAACmC,GAAG,EAAE9B,KAAK,EAAEC,MAAM,KAAK;IACnE,OAAOZ,MAAM,CAACQ,OAAO,CAACE,QAAQ,CAACgC,aAAa,IAAI;MAC9C,OAAO/C,QAAQ,CAAC,CAAC,CAAC,EAAE+C,aAAa,EAAE;QACjC,CAACD,GAAG,GAAG9B,KAAK,CAAC+B,aAAa,CAACD,GAAG,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,EAAE7B,MAAM,CAAC;EACZ,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EACZ,MAAM2C,WAAW,GAAG/C,KAAK,CAACU,WAAW,CAAC,MAAMH,cAAc,CAAC,MAAMH,MAAM,CAACQ,OAAO,CAACG,KAAK,CAAC,EAAE,CAACX,MAAM,CAAC,CAAC;EACjG,MAAM4C,cAAc,GAAG;IACrBlC,QAAQ;IACRiC;EACF,CAAC;EACD,MAAME,eAAe,GAAG;IACtBL,kBAAkB;IAClBnC;EACF,CAAC;EACDR,gBAAgB,CAACG,MAAM,EAAE4C,cAAc,EAAE,QAAQ,CAAC;EAClD/C,gBAAgB,CAACG,MAAM,EAAE6C,eAAe,EAAE,SAAS,CAAC;AACtD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}