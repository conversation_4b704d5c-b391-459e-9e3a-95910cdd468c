{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport resolveProps from '@mui/utils/resolveProps';\nimport { DEFAULT_GRID_COL_TYPE_KEY, GRID_STRING_COL_DEF, getGridDefaultColumnTypes } from \"../../../colDef/index.js\";\nimport { gridColumnsStateSelector, gridColumnVisibilityModelSelector } from \"./gridColumnsSelector.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridDensityFactorSelector } from \"../density/densitySelector.js\";\nimport { gridHeaderFilteringEnabledSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nexport const COLUMNS_DIMENSION_PROPERTIES = ['maxWidth', 'minWidth', 'width', 'flex'];\nconst COLUMN_TYPES = getGridDefaultColumnTypes();\n\n/**\n * Computes width for flex columns.\n * Based on CSS Flexbox specification:\n * https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n */\nexport function computeFlexColumnsWidth(_ref) {\n  let {\n    initialFreeSpace,\n    totalFlexUnits,\n    flexColumns\n  } = _ref;\n  const uniqueFlexColumns = new Set(flexColumns.map(col => col.field));\n  const flexColumnsLookup = {\n    all: {},\n    frozenFields: [],\n    freeze: field => {\n      const value = flexColumnsLookup.all[field];\n      if (value && value.frozen !== true) {\n        flexColumnsLookup.all[field].frozen = true;\n        flexColumnsLookup.frozenFields.push(field);\n      }\n    }\n  };\n\n  // Step 5 of https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n  function loopOverFlexItems() {\n    // 5a: If all the flex items on the line are frozen, free space has been distributed.\n    if (flexColumnsLookup.frozenFields.length === uniqueFlexColumns.size) {\n      return;\n    }\n    const violationsLookup = {\n      min: {},\n      max: {}\n    };\n    let remainingFreeSpace = initialFreeSpace;\n    let flexUnits = totalFlexUnits;\n    let totalViolation = 0;\n\n    // 5b: Calculate the remaining free space\n    flexColumnsLookup.frozenFields.forEach(field => {\n      remainingFreeSpace -= flexColumnsLookup.all[field].computedWidth;\n      flexUnits -= flexColumnsLookup.all[field].flex;\n    });\n    for (let i = 0; i < flexColumns.length; i += 1) {\n      const column = flexColumns[i];\n      if (flexColumnsLookup.all[column.field] && flexColumnsLookup.all[column.field].frozen === true) {\n        continue;\n      }\n\n      // 5c: Distribute remaining free space proportional to the flex factors\n      const widthPerFlexUnit = remainingFreeSpace / flexUnits;\n      let computedWidth = widthPerFlexUnit * column.flex;\n\n      // 5d: Fix min/max violations\n      if (computedWidth < column.minWidth) {\n        totalViolation += column.minWidth - computedWidth;\n        computedWidth = column.minWidth;\n        violationsLookup.min[column.field] = true;\n      } else if (computedWidth > column.maxWidth) {\n        totalViolation += column.maxWidth - computedWidth;\n        computedWidth = column.maxWidth;\n        violationsLookup.max[column.field] = true;\n      }\n      flexColumnsLookup.all[column.field] = {\n        frozen: false,\n        computedWidth,\n        flex: column.flex\n      };\n    }\n\n    // 5e: Freeze over-flexed items\n    if (totalViolation < 0) {\n      // Freeze all the items with max violations\n      Object.keys(violationsLookup.max).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else if (totalViolation > 0) {\n      // Freeze all the items with min violations\n      Object.keys(violationsLookup.min).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else {\n      // Freeze all items\n      flexColumns.forEach(_ref2 => {\n        let {\n          field\n        } = _ref2;\n        flexColumnsLookup.freeze(field);\n      });\n    }\n\n    // 5f: Return to the start of this loop\n    loopOverFlexItems();\n  }\n  loopOverFlexItems();\n  return flexColumnsLookup.all;\n}\n\n/**\n * Compute the `computedWidth` (ie: the width the column should have during rendering) based on the `width` / `flex` / `minWidth` / `maxWidth` properties of `GridColDef`.\n * The columns already have been merged with there `type` default values for `minWidth`, `maxWidth` and `width`, thus the `!` for those properties below.\n * TODO: Unit test this function in depth and only keep basic cases for the whole grid testing.\n * TODO: Improve the `GridColDef` typing to reflect the fact that `minWidth` / `maxWidth` and `width` can't be null after the merge with the `type` default values.\n */\nexport const hydrateColumnsWidth = (rawState, dimensions) => {\n  const columnsLookup = {};\n  let totalFlexUnits = 0;\n  let widthAllocatedBeforeFlex = 0;\n  const flexColumns = [];\n\n  // For the non-flex columns, compute their width\n  // For the flex columns, compute their minimum width and how much width must be allocated during the flex allocation\n  rawState.orderedFields.forEach(columnField => {\n    let column = rawState.lookup[columnField];\n    let computedWidth = 0;\n    let isFlex = false;\n    if (rawState.columnVisibilityModel[columnField] !== false) {\n      if (column.flex && column.flex > 0) {\n        totalFlexUnits += column.flex;\n        isFlex = true;\n      } else {\n        computedWidth = clamp(column.width || GRID_STRING_COL_DEF.width, column.minWidth || GRID_STRING_COL_DEF.minWidth, column.maxWidth || GRID_STRING_COL_DEF.maxWidth);\n      }\n      widthAllocatedBeforeFlex += computedWidth;\n    }\n    if (column.computedWidth !== computedWidth) {\n      column = _extends({}, column, {\n        computedWidth\n      });\n    }\n    if (isFlex) {\n      flexColumns.push(column);\n    }\n    columnsLookup[columnField] = column;\n  });\n  const availableWidth = dimensions === undefined ? 0 : dimensions.viewportOuterSize.width - (dimensions.hasScrollY ? dimensions.scrollbarSize : 0);\n  const initialFreeSpace = Math.max(availableWidth - widthAllocatedBeforeFlex, 0);\n\n  // Allocate the remaining space to the flex columns\n  if (totalFlexUnits > 0 && availableWidth > 0) {\n    const computedColumnWidths = computeFlexColumnsWidth({\n      initialFreeSpace,\n      totalFlexUnits,\n      flexColumns\n    });\n    Object.keys(computedColumnWidths).forEach(field => {\n      columnsLookup[field].computedWidth = computedColumnWidths[field].computedWidth;\n    });\n  }\n  return _extends({}, rawState, {\n    lookup: columnsLookup\n  });\n};\n\n/**\n * Apply the order and the dimensions of the initial state.\n * The columns not registered in `orderedFields` will be placed after the imported columns.\n */\nexport const applyInitialState = (columnsState, initialState) => {\n  if (!initialState) {\n    return columnsState;\n  }\n  const {\n    orderedFields = [],\n    dimensions = {}\n  } = initialState;\n  const columnsWithUpdatedDimensions = Object.keys(dimensions);\n  if (columnsWithUpdatedDimensions.length === 0 && orderedFields.length === 0) {\n    return columnsState;\n  }\n  const orderedFieldsLookup = {};\n  const cleanOrderedFields = [];\n  for (let i = 0; i < orderedFields.length; i += 1) {\n    const field = orderedFields[i];\n\n    // Ignores the fields in the initialState that matches no field on the current column state\n    if (columnsState.lookup[field]) {\n      orderedFieldsLookup[field] = true;\n      cleanOrderedFields.push(field);\n    }\n  }\n  const newOrderedFields = cleanOrderedFields.length === 0 ? columnsState.orderedFields : [...cleanOrderedFields, ...columnsState.orderedFields.filter(field => !orderedFieldsLookup[field])];\n  const newColumnLookup = _extends({}, columnsState.lookup);\n  for (let i = 0; i < columnsWithUpdatedDimensions.length; i += 1) {\n    const field = columnsWithUpdatedDimensions[i];\n    const newColDef = _extends({}, newColumnLookup[field], {\n      hasBeenResized: true\n    });\n    Object.entries(dimensions[field]).forEach(_ref3 => {\n      let [key, value] = _ref3;\n      newColDef[key] = value === -1 ? Infinity : value;\n    });\n    newColumnLookup[field] = newColDef;\n  }\n  const newColumnsState = _extends({}, columnsState, {\n    orderedFields: newOrderedFields,\n    lookup: newColumnLookup\n  });\n  return newColumnsState;\n};\nfunction getDefaultColTypeDef(type) {\n  let colDef = COLUMN_TYPES[DEFAULT_GRID_COL_TYPE_KEY];\n  if (type && COLUMN_TYPES[type]) {\n    colDef = COLUMN_TYPES[type];\n  }\n  return colDef;\n}\nexport const createColumnsState = _ref4 => {\n  let {\n    apiRef,\n    columnsToUpsert,\n    initialState,\n    columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef),\n    keepOnlyColumnsToUpsert = false\n  } = _ref4;\n  const isInsideStateInitializer = !apiRef.current.state.columns;\n  let columnsState;\n  if (isInsideStateInitializer) {\n    columnsState = {\n      orderedFields: [],\n      lookup: {},\n      columnVisibilityModel\n    };\n  } else {\n    const currentState = gridColumnsStateSelector(apiRef.current.state);\n    columnsState = {\n      orderedFields: keepOnlyColumnsToUpsert ? [] : [...currentState.orderedFields],\n      lookup: _extends({}, currentState.lookup),\n      // Will be cleaned later if keepOnlyColumnsToUpsert=true\n      columnVisibilityModel\n    };\n  }\n  let columnsToKeep = {};\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    columnsToKeep = Object.keys(columnsState.lookup).reduce((acc, key) => _extends({}, acc, {\n      [key]: false\n    }), {});\n  }\n  const columnsToUpsertLookup = {};\n  columnsToUpsert.forEach(newColumn => {\n    const {\n      field\n    } = newColumn;\n    columnsToUpsertLookup[field] = true;\n    columnsToKeep[field] = true;\n    let existingState = columnsState.lookup[field];\n    if (existingState == null) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field,\n        hasBeenResized: false\n      });\n      columnsState.orderedFields.push(field);\n    } else if (keepOnlyColumnsToUpsert) {\n      columnsState.orderedFields.push(field);\n    }\n\n    // If the column type has changed - merge the existing state with the default column type definition\n    if (existingState && existingState.type !== newColumn.type) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field\n      });\n    }\n    let hasBeenResized = existingState.hasBeenResized;\n    COLUMNS_DIMENSION_PROPERTIES.forEach(key => {\n      if (newColumn[key] !== undefined) {\n        hasBeenResized = true;\n        if (newColumn[key] === -1) {\n          newColumn[key] = Infinity;\n        }\n      }\n    });\n    columnsState.lookup[field] = resolveProps(existingState, _extends({}, newColumn, {\n      hasBeenResized\n    }));\n  });\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    Object.keys(columnsState.lookup).forEach(field => {\n      if (!columnsToKeep[field]) {\n        delete columnsState.lookup[field];\n      }\n    });\n  }\n  const columnsStateWithPreProcessing = apiRef.current.unstable_applyPipeProcessors('hydrateColumns', columnsState);\n  const columnsStateWithPortableColumns = applyInitialState(columnsStateWithPreProcessing, initialState);\n  return hydrateColumnsWidth(columnsStateWithPortableColumns, apiRef.current.getRootDimensions?.() ?? undefined);\n};\nexport function getFirstNonSpannedColumnToRender(_ref5) {\n  let {\n    firstColumnToRender,\n    apiRef,\n    firstRowToRender,\n    lastRowToRender,\n    visibleRows\n  } = _ref5;\n  let firstNonSpannedColumnToRender = firstColumnToRender;\n  for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n    const row = visibleRows[i];\n    if (row) {\n      const rowId = visibleRows[i].id;\n      const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, firstColumnToRender);\n      if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan) {\n        firstNonSpannedColumnToRender = cellColSpanInfo.leftVisibleCellIndex;\n      }\n    }\n  }\n  return firstNonSpannedColumnToRender;\n}\nexport function getTotalHeaderHeight(apiRef, props) {\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  const maxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n  const isHeaderFilteringEnabled = gridHeaderFilteringEnabledSelector(apiRef);\n  const columnHeadersHeight = Math.floor(props.columnHeaderHeight * densityFactor);\n  const filterHeadersHeight = isHeaderFilteringEnabled ? Math.floor((props.headerFilterHeight ?? props.columnHeaderHeight) * densityFactor) : 0;\n  return columnHeadersHeight * (1 + (maxDepth ?? 0)) + filterHeadersHeight;\n}", "map": {"version": 3, "names": ["_extends", "resolveProps", "DEFAULT_GRID_COL_TYPE_KEY", "GRID_STRING_COL_DEF", "getGridDefaultColumnTypes", "gridColumnsStateSelector", "gridColumnVisibilityModelSelector", "clamp", "gridDensityFactorSelector", "gridHeaderFilteringEnabledSelector", "gridColumnGroupsHeaderMaxDepthSelector", "COLUMNS_DIMENSION_PROPERTIES", "COLUMN_TYPES", "computeFlexColumnsWidth", "_ref", "initialFreeSpace", "totalFlexUnits", "flexColumns", "uniqueFlexColumns", "Set", "map", "col", "field", "flexColumnsLookup", "all", "frozenFields", "freeze", "value", "frozen", "push", "loopOverFlexItems", "length", "size", "violationsLookup", "min", "max", "remainingFreeSpace", "flexUnits", "totalViolation", "for<PERSON>ach", "computedWidth", "flex", "i", "column", "widthPerFlexUnit", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "Object", "keys", "_ref2", "hydrateColumnsWidth", "rawState", "dimensions", "columnsLookup", "widthAllocatedBeforeFlex", "orderedFields", "columnField", "lookup", "isFlex", "columnVisibilityModel", "width", "availableWidth", "undefined", "viewportOuterSize", "hasScrollY", "scrollbarSize", "Math", "computedColumnWidths", "applyInitialState", "columnsState", "initialState", "columnsWithUpdatedDimensions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanOrderedFields", "new<PERSON><PERSON><PERSON><PERSON><PERSON>s", "filter", "newColumnLookup", "newColDef", "hasBeenResized", "entries", "_ref3", "key", "Infinity", "newColumnsState", "getDefaultColTypeDef", "type", "colDef", "createColumnsState", "_ref4", "apiRef", "columnsToUpsert", "keepOnlyColumnsToUpsert", "isInsideStateInitializer", "current", "state", "columns", "currentState", "columnsToKeep", "reduce", "acc", "columnsToUpsertLookup", "newColumn", "existingState", "columnsStateWithPreProcessing", "unstable_applyPipeProcessors", "columnsStateWithPortableColumns", "getRootDimensions", "getFirstNonSpannedColumnToRender", "_ref5", "firstColumnToRender", "firstRowToRender", "lastRowToRender", "visibleRows", "firstNonSpannedColumnToRender", "row", "rowId", "id", "cellColSpanInfo", "unstable_getCellColSpanInfo", "spannedByColSpan", "leftVisibleCellIndex", "getTotalHeaderHeight", "props", "densityFactor", "max<PERSON><PERSON><PERSON>", "isHeaderFilteringEnabled", "columnHeadersHeight", "floor", "columnHeaderHeight", "filterHeadersHeight", "headerFilterHeight"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/columns/gridColumnsUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport resolveProps from '@mui/utils/resolveProps';\nimport { DEFAULT_GRID_COL_TYPE_KEY, GRID_STRING_COL_DEF, getGridDefaultColumnTypes } from \"../../../colDef/index.js\";\nimport { gridColumnsStateSelector, gridColumnVisibilityModelSelector } from \"./gridColumnsSelector.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridDensityFactorSelector } from \"../density/densitySelector.js\";\nimport { gridHeaderFilteringEnabledSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nexport const COLUMNS_DIMENSION_PROPERTIES = ['maxWidth', 'minWidth', 'width', 'flex'];\nconst COLUMN_TYPES = getGridDefaultColumnTypes();\n\n/**\n * Computes width for flex columns.\n * Based on CSS Flexbox specification:\n * https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n */\nexport function computeFlexColumnsWidth({\n  initialFreeSpace,\n  totalFlexUnits,\n  flexColumns\n}) {\n  const uniqueFlexColumns = new Set(flexColumns.map(col => col.field));\n  const flexColumnsLookup = {\n    all: {},\n    frozenFields: [],\n    freeze: field => {\n      const value = flexColumnsLookup.all[field];\n      if (value && value.frozen !== true) {\n        flexColumnsLookup.all[field].frozen = true;\n        flexColumnsLookup.frozenFields.push(field);\n      }\n    }\n  };\n\n  // Step 5 of https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n  function loopOverFlexItems() {\n    // 5a: If all the flex items on the line are frozen, free space has been distributed.\n    if (flexColumnsLookup.frozenFields.length === uniqueFlexColumns.size) {\n      return;\n    }\n    const violationsLookup = {\n      min: {},\n      max: {}\n    };\n    let remainingFreeSpace = initialFreeSpace;\n    let flexUnits = totalFlexUnits;\n    let totalViolation = 0;\n\n    // 5b: Calculate the remaining free space\n    flexColumnsLookup.frozenFields.forEach(field => {\n      remainingFreeSpace -= flexColumnsLookup.all[field].computedWidth;\n      flexUnits -= flexColumnsLookup.all[field].flex;\n    });\n    for (let i = 0; i < flexColumns.length; i += 1) {\n      const column = flexColumns[i];\n      if (flexColumnsLookup.all[column.field] && flexColumnsLookup.all[column.field].frozen === true) {\n        continue;\n      }\n\n      // 5c: Distribute remaining free space proportional to the flex factors\n      const widthPerFlexUnit = remainingFreeSpace / flexUnits;\n      let computedWidth = widthPerFlexUnit * column.flex;\n\n      // 5d: Fix min/max violations\n      if (computedWidth < column.minWidth) {\n        totalViolation += column.minWidth - computedWidth;\n        computedWidth = column.minWidth;\n        violationsLookup.min[column.field] = true;\n      } else if (computedWidth > column.maxWidth) {\n        totalViolation += column.maxWidth - computedWidth;\n        computedWidth = column.maxWidth;\n        violationsLookup.max[column.field] = true;\n      }\n      flexColumnsLookup.all[column.field] = {\n        frozen: false,\n        computedWidth,\n        flex: column.flex\n      };\n    }\n\n    // 5e: Freeze over-flexed items\n    if (totalViolation < 0) {\n      // Freeze all the items with max violations\n      Object.keys(violationsLookup.max).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else if (totalViolation > 0) {\n      // Freeze all the items with min violations\n      Object.keys(violationsLookup.min).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else {\n      // Freeze all items\n      flexColumns.forEach(({\n        field\n      }) => {\n        flexColumnsLookup.freeze(field);\n      });\n    }\n\n    // 5f: Return to the start of this loop\n    loopOverFlexItems();\n  }\n  loopOverFlexItems();\n  return flexColumnsLookup.all;\n}\n\n/**\n * Compute the `computedWidth` (ie: the width the column should have during rendering) based on the `width` / `flex` / `minWidth` / `maxWidth` properties of `GridColDef`.\n * The columns already have been merged with there `type` default values for `minWidth`, `maxWidth` and `width`, thus the `!` for those properties below.\n * TODO: Unit test this function in depth and only keep basic cases for the whole grid testing.\n * TODO: Improve the `GridColDef` typing to reflect the fact that `minWidth` / `maxWidth` and `width` can't be null after the merge with the `type` default values.\n */\nexport const hydrateColumnsWidth = (rawState, dimensions) => {\n  const columnsLookup = {};\n  let totalFlexUnits = 0;\n  let widthAllocatedBeforeFlex = 0;\n  const flexColumns = [];\n\n  // For the non-flex columns, compute their width\n  // For the flex columns, compute their minimum width and how much width must be allocated during the flex allocation\n  rawState.orderedFields.forEach(columnField => {\n    let column = rawState.lookup[columnField];\n    let computedWidth = 0;\n    let isFlex = false;\n    if (rawState.columnVisibilityModel[columnField] !== false) {\n      if (column.flex && column.flex > 0) {\n        totalFlexUnits += column.flex;\n        isFlex = true;\n      } else {\n        computedWidth = clamp(column.width || GRID_STRING_COL_DEF.width, column.minWidth || GRID_STRING_COL_DEF.minWidth, column.maxWidth || GRID_STRING_COL_DEF.maxWidth);\n      }\n      widthAllocatedBeforeFlex += computedWidth;\n    }\n    if (column.computedWidth !== computedWidth) {\n      column = _extends({}, column, {\n        computedWidth\n      });\n    }\n    if (isFlex) {\n      flexColumns.push(column);\n    }\n    columnsLookup[columnField] = column;\n  });\n  const availableWidth = dimensions === undefined ? 0 : dimensions.viewportOuterSize.width - (dimensions.hasScrollY ? dimensions.scrollbarSize : 0);\n  const initialFreeSpace = Math.max(availableWidth - widthAllocatedBeforeFlex, 0);\n\n  // Allocate the remaining space to the flex columns\n  if (totalFlexUnits > 0 && availableWidth > 0) {\n    const computedColumnWidths = computeFlexColumnsWidth({\n      initialFreeSpace,\n      totalFlexUnits,\n      flexColumns\n    });\n    Object.keys(computedColumnWidths).forEach(field => {\n      columnsLookup[field].computedWidth = computedColumnWidths[field].computedWidth;\n    });\n  }\n  return _extends({}, rawState, {\n    lookup: columnsLookup\n  });\n};\n\n/**\n * Apply the order and the dimensions of the initial state.\n * The columns not registered in `orderedFields` will be placed after the imported columns.\n */\nexport const applyInitialState = (columnsState, initialState) => {\n  if (!initialState) {\n    return columnsState;\n  }\n  const {\n    orderedFields = [],\n    dimensions = {}\n  } = initialState;\n  const columnsWithUpdatedDimensions = Object.keys(dimensions);\n  if (columnsWithUpdatedDimensions.length === 0 && orderedFields.length === 0) {\n    return columnsState;\n  }\n  const orderedFieldsLookup = {};\n  const cleanOrderedFields = [];\n  for (let i = 0; i < orderedFields.length; i += 1) {\n    const field = orderedFields[i];\n\n    // Ignores the fields in the initialState that matches no field on the current column state\n    if (columnsState.lookup[field]) {\n      orderedFieldsLookup[field] = true;\n      cleanOrderedFields.push(field);\n    }\n  }\n  const newOrderedFields = cleanOrderedFields.length === 0 ? columnsState.orderedFields : [...cleanOrderedFields, ...columnsState.orderedFields.filter(field => !orderedFieldsLookup[field])];\n  const newColumnLookup = _extends({}, columnsState.lookup);\n  for (let i = 0; i < columnsWithUpdatedDimensions.length; i += 1) {\n    const field = columnsWithUpdatedDimensions[i];\n    const newColDef = _extends({}, newColumnLookup[field], {\n      hasBeenResized: true\n    });\n    Object.entries(dimensions[field]).forEach(([key, value]) => {\n      newColDef[key] = value === -1 ? Infinity : value;\n    });\n    newColumnLookup[field] = newColDef;\n  }\n  const newColumnsState = _extends({}, columnsState, {\n    orderedFields: newOrderedFields,\n    lookup: newColumnLookup\n  });\n  return newColumnsState;\n};\nfunction getDefaultColTypeDef(type) {\n  let colDef = COLUMN_TYPES[DEFAULT_GRID_COL_TYPE_KEY];\n  if (type && COLUMN_TYPES[type]) {\n    colDef = COLUMN_TYPES[type];\n  }\n  return colDef;\n}\nexport const createColumnsState = ({\n  apiRef,\n  columnsToUpsert,\n  initialState,\n  columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef),\n  keepOnlyColumnsToUpsert = false\n}) => {\n  const isInsideStateInitializer = !apiRef.current.state.columns;\n  let columnsState;\n  if (isInsideStateInitializer) {\n    columnsState = {\n      orderedFields: [],\n      lookup: {},\n      columnVisibilityModel\n    };\n  } else {\n    const currentState = gridColumnsStateSelector(apiRef.current.state);\n    columnsState = {\n      orderedFields: keepOnlyColumnsToUpsert ? [] : [...currentState.orderedFields],\n      lookup: _extends({}, currentState.lookup),\n      // Will be cleaned later if keepOnlyColumnsToUpsert=true\n      columnVisibilityModel\n    };\n  }\n  let columnsToKeep = {};\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    columnsToKeep = Object.keys(columnsState.lookup).reduce((acc, key) => _extends({}, acc, {\n      [key]: false\n    }), {});\n  }\n  const columnsToUpsertLookup = {};\n  columnsToUpsert.forEach(newColumn => {\n    const {\n      field\n    } = newColumn;\n    columnsToUpsertLookup[field] = true;\n    columnsToKeep[field] = true;\n    let existingState = columnsState.lookup[field];\n    if (existingState == null) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field,\n        hasBeenResized: false\n      });\n      columnsState.orderedFields.push(field);\n    } else if (keepOnlyColumnsToUpsert) {\n      columnsState.orderedFields.push(field);\n    }\n\n    // If the column type has changed - merge the existing state with the default column type definition\n    if (existingState && existingState.type !== newColumn.type) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field\n      });\n    }\n    let hasBeenResized = existingState.hasBeenResized;\n    COLUMNS_DIMENSION_PROPERTIES.forEach(key => {\n      if (newColumn[key] !== undefined) {\n        hasBeenResized = true;\n        if (newColumn[key] === -1) {\n          newColumn[key] = Infinity;\n        }\n      }\n    });\n    columnsState.lookup[field] = resolveProps(existingState, _extends({}, newColumn, {\n      hasBeenResized\n    }));\n  });\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    Object.keys(columnsState.lookup).forEach(field => {\n      if (!columnsToKeep[field]) {\n        delete columnsState.lookup[field];\n      }\n    });\n  }\n  const columnsStateWithPreProcessing = apiRef.current.unstable_applyPipeProcessors('hydrateColumns', columnsState);\n  const columnsStateWithPortableColumns = applyInitialState(columnsStateWithPreProcessing, initialState);\n  return hydrateColumnsWidth(columnsStateWithPortableColumns, apiRef.current.getRootDimensions?.() ?? undefined);\n};\nexport function getFirstNonSpannedColumnToRender({\n  firstColumnToRender,\n  apiRef,\n  firstRowToRender,\n  lastRowToRender,\n  visibleRows\n}) {\n  let firstNonSpannedColumnToRender = firstColumnToRender;\n  for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n    const row = visibleRows[i];\n    if (row) {\n      const rowId = visibleRows[i].id;\n      const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, firstColumnToRender);\n      if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan) {\n        firstNonSpannedColumnToRender = cellColSpanInfo.leftVisibleCellIndex;\n      }\n    }\n  }\n  return firstNonSpannedColumnToRender;\n}\nexport function getTotalHeaderHeight(apiRef, props) {\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  const maxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n  const isHeaderFilteringEnabled = gridHeaderFilteringEnabledSelector(apiRef);\n  const columnHeadersHeight = Math.floor(props.columnHeaderHeight * densityFactor);\n  const filterHeadersHeight = isHeaderFilteringEnabled ? Math.floor((props.headerFilterHeight ?? props.columnHeaderHeight) * densityFactor) : 0;\n  return columnHeadersHeight * (1 + (maxDepth ?? 0)) + filterHeadersHeight;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,yBAAyB,EAAEC,mBAAmB,EAAEC,yBAAyB,QAAQ,0BAA0B;AACpH,SAASC,wBAAwB,EAAEC,iCAAiC,QAAQ,0BAA0B;AACtG,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,kCAAkC,QAAQ,oDAAoD;AACvG,SAASC,sCAAsC,QAAQ,+CAA+C;AACtG,OAAO,MAAMC,4BAA4B,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC;AACrF,MAAMC,YAAY,GAAGR,yBAAyB,CAAC,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,uBAAuBA,CAAAC,IAAA,EAIpC;EAAA,IAJqC;IACtCC,gBAAgB;IAChBC,cAAc;IACdC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMI,iBAAiB,GAAG,IAAIC,GAAG,CAACF,WAAW,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,CAAC,CAAC;EACpE,MAAMC,iBAAiB,GAAG;IACxBC,GAAG,EAAE,CAAC,CAAC;IACPC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAEJ,KAAK,IAAI;MACf,MAAMK,KAAK,GAAGJ,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC;MAC1C,IAAIK,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,EAAE;QAClCL,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACM,MAAM,GAAG,IAAI;QAC1CL,iBAAiB,CAACE,YAAY,CAACI,IAAI,CAACP,KAAK,CAAC;MAC5C;IACF;EACF,CAAC;;EAED;EACA,SAASQ,iBAAiBA,CAAA,EAAG;IAC3B;IACA,IAAIP,iBAAiB,CAACE,YAAY,CAACM,MAAM,KAAKb,iBAAiB,CAACc,IAAI,EAAE;MACpE;IACF;IACA,MAAMC,gBAAgB,GAAG;MACvBC,GAAG,EAAE,CAAC,CAAC;MACPC,GAAG,EAAE,CAAC;IACR,CAAC;IACD,IAAIC,kBAAkB,GAAGrB,gBAAgB;IACzC,IAAIsB,SAAS,GAAGrB,cAAc;IAC9B,IAAIsB,cAAc,GAAG,CAAC;;IAEtB;IACAf,iBAAiB,CAACE,YAAY,CAACc,OAAO,CAACjB,KAAK,IAAI;MAC9Cc,kBAAkB,IAAIb,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACkB,aAAa;MAChEH,SAAS,IAAId,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACmB,IAAI;IAChD,CAAC,CAAC;IACF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,WAAW,CAACc,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;MAC9C,MAAMC,MAAM,GAAG1B,WAAW,CAACyB,CAAC,CAAC;MAC7B,IAAInB,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,IAAIC,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,CAACM,MAAM,KAAK,IAAI,EAAE;QAC9F;MACF;;MAEA;MACA,MAAMgB,gBAAgB,GAAGR,kBAAkB,GAAGC,SAAS;MACvD,IAAIG,aAAa,GAAGI,gBAAgB,GAAGD,MAAM,CAACF,IAAI;;MAElD;MACA,IAAID,aAAa,GAAGG,MAAM,CAACE,QAAQ,EAAE;QACnCP,cAAc,IAAIK,MAAM,CAACE,QAAQ,GAAGL,aAAa;QACjDA,aAAa,GAAGG,MAAM,CAACE,QAAQ;QAC/BZ,gBAAgB,CAACC,GAAG,CAACS,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;MAC3C,CAAC,MAAM,IAAIkB,aAAa,GAAGG,MAAM,CAACG,QAAQ,EAAE;QAC1CR,cAAc,IAAIK,MAAM,CAACG,QAAQ,GAAGN,aAAa;QACjDA,aAAa,GAAGG,MAAM,CAACG,QAAQ;QAC/Bb,gBAAgB,CAACE,GAAG,CAACQ,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;MAC3C;MACAC,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,GAAG;QACpCM,MAAM,EAAE,KAAK;QACbY,aAAa;QACbC,IAAI,EAAEE,MAAM,CAACF;MACf,CAAC;IACH;;IAEA;IACA,IAAIH,cAAc,GAAG,CAAC,EAAE;MACtB;MACAS,MAAM,CAACC,IAAI,CAACf,gBAAgB,CAACE,GAAG,CAAC,CAACI,OAAO,CAACjB,KAAK,IAAI;QACjDC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIgB,cAAc,GAAG,CAAC,EAAE;MAC7B;MACAS,MAAM,CAACC,IAAI,CAACf,gBAAgB,CAACC,GAAG,CAAC,CAACK,OAAO,CAACjB,KAAK,IAAI;QACjDC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAL,WAAW,CAACsB,OAAO,CAACU,KAAA,IAEd;QAAA,IAFe;UACnB3B;QACF,CAAC,GAAA2B,KAAA;QACC1B,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ;;IAEA;IACAQ,iBAAiB,CAAC,CAAC;EACrB;EACAA,iBAAiB,CAAC,CAAC;EACnB,OAAOP,iBAAiB,CAACC,GAAG;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0B,mBAAmB,GAAGA,CAACC,QAAQ,EAAEC,UAAU,KAAK;EAC3D,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,IAAIrC,cAAc,GAAG,CAAC;EACtB,IAAIsC,wBAAwB,GAAG,CAAC;EAChC,MAAMrC,WAAW,GAAG,EAAE;;EAEtB;EACA;EACAkC,QAAQ,CAACI,aAAa,CAAChB,OAAO,CAACiB,WAAW,IAAI;IAC5C,IAAIb,MAAM,GAAGQ,QAAQ,CAACM,MAAM,CAACD,WAAW,CAAC;IACzC,IAAIhB,aAAa,GAAG,CAAC;IACrB,IAAIkB,MAAM,GAAG,KAAK;IAClB,IAAIP,QAAQ,CAACQ,qBAAqB,CAACH,WAAW,CAAC,KAAK,KAAK,EAAE;MACzD,IAAIb,MAAM,CAACF,IAAI,IAAIE,MAAM,CAACF,IAAI,GAAG,CAAC,EAAE;QAClCzB,cAAc,IAAI2B,MAAM,CAACF,IAAI;QAC7BiB,MAAM,GAAG,IAAI;MACf,CAAC,MAAM;QACLlB,aAAa,GAAGjC,KAAK,CAACoC,MAAM,CAACiB,KAAK,IAAIzD,mBAAmB,CAACyD,KAAK,EAAEjB,MAAM,CAACE,QAAQ,IAAI1C,mBAAmB,CAAC0C,QAAQ,EAAEF,MAAM,CAACG,QAAQ,IAAI3C,mBAAmB,CAAC2C,QAAQ,CAAC;MACpK;MACAQ,wBAAwB,IAAId,aAAa;IAC3C;IACA,IAAIG,MAAM,CAACH,aAAa,KAAKA,aAAa,EAAE;MAC1CG,MAAM,GAAG3C,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,EAAE;QAC5BH;MACF,CAAC,CAAC;IACJ;IACA,IAAIkB,MAAM,EAAE;MACVzC,WAAW,CAACY,IAAI,CAACc,MAAM,CAAC;IAC1B;IACAU,aAAa,CAACG,WAAW,CAAC,GAAGb,MAAM;EACrC,CAAC,CAAC;EACF,MAAMkB,cAAc,GAAGT,UAAU,KAAKU,SAAS,GAAG,CAAC,GAAGV,UAAU,CAACW,iBAAiB,CAACH,KAAK,IAAIR,UAAU,CAACY,UAAU,GAAGZ,UAAU,CAACa,aAAa,GAAG,CAAC,CAAC;EACjJ,MAAMlD,gBAAgB,GAAGmD,IAAI,CAAC/B,GAAG,CAAC0B,cAAc,GAAGP,wBAAwB,EAAE,CAAC,CAAC;;EAE/E;EACA,IAAItC,cAAc,GAAG,CAAC,IAAI6C,cAAc,GAAG,CAAC,EAAE;IAC5C,MAAMM,oBAAoB,GAAGtD,uBAAuB,CAAC;MACnDE,gBAAgB;MAChBC,cAAc;MACdC;IACF,CAAC,CAAC;IACF8B,MAAM,CAACC,IAAI,CAACmB,oBAAoB,CAAC,CAAC5B,OAAO,CAACjB,KAAK,IAAI;MACjD+B,aAAa,CAAC/B,KAAK,CAAC,CAACkB,aAAa,GAAG2B,oBAAoB,CAAC7C,KAAK,CAAC,CAACkB,aAAa;IAChF,CAAC,CAAC;EACJ;EACA,OAAOxC,QAAQ,CAAC,CAAC,CAAC,EAAEmD,QAAQ,EAAE;IAC5BM,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMe,iBAAiB,GAAGA,CAACC,YAAY,EAAEC,YAAY,KAAK;EAC/D,IAAI,CAACA,YAAY,EAAE;IACjB,OAAOD,YAAY;EACrB;EACA,MAAM;IACJd,aAAa,GAAG,EAAE;IAClBH,UAAU,GAAG,CAAC;EAChB,CAAC,GAAGkB,YAAY;EAChB,MAAMC,4BAA4B,GAAGxB,MAAM,CAACC,IAAI,CAACI,UAAU,CAAC;EAC5D,IAAImB,4BAA4B,CAACxC,MAAM,KAAK,CAAC,IAAIwB,aAAa,CAACxB,MAAM,KAAK,CAAC,EAAE;IAC3E,OAAOsC,YAAY;EACrB;EACA,MAAMG,mBAAmB,GAAG,CAAC,CAAC;EAC9B,MAAMC,kBAAkB,GAAG,EAAE;EAC7B,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,aAAa,CAACxB,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;IAChD,MAAMpB,KAAK,GAAGiC,aAAa,CAACb,CAAC,CAAC;;IAE9B;IACA,IAAI2B,YAAY,CAACZ,MAAM,CAACnC,KAAK,CAAC,EAAE;MAC9BkD,mBAAmB,CAAClD,KAAK,CAAC,GAAG,IAAI;MACjCmD,kBAAkB,CAAC5C,IAAI,CAACP,KAAK,CAAC;IAChC;EACF;EACA,MAAMoD,gBAAgB,GAAGD,kBAAkB,CAAC1C,MAAM,KAAK,CAAC,GAAGsC,YAAY,CAACd,aAAa,GAAG,CAAC,GAAGkB,kBAAkB,EAAE,GAAGJ,YAAY,CAACd,aAAa,CAACoB,MAAM,CAACrD,KAAK,IAAI,CAACkD,mBAAmB,CAAClD,KAAK,CAAC,CAAC,CAAC;EAC3L,MAAMsD,eAAe,GAAG5E,QAAQ,CAAC,CAAC,CAAC,EAAEqE,YAAY,CAACZ,MAAM,CAAC;EACzD,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,4BAA4B,CAACxC,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;IAC/D,MAAMpB,KAAK,GAAGiD,4BAA4B,CAAC7B,CAAC,CAAC;IAC7C,MAAMmC,SAAS,GAAG7E,QAAQ,CAAC,CAAC,CAAC,EAAE4E,eAAe,CAACtD,KAAK,CAAC,EAAE;MACrDwD,cAAc,EAAE;IAClB,CAAC,CAAC;IACF/B,MAAM,CAACgC,OAAO,CAAC3B,UAAU,CAAC9B,KAAK,CAAC,CAAC,CAACiB,OAAO,CAACyC,KAAA,IAAkB;MAAA,IAAjB,CAACC,GAAG,EAAEtD,KAAK,CAAC,GAAAqD,KAAA;MACrDH,SAAS,CAACI,GAAG,CAAC,GAAGtD,KAAK,KAAK,CAAC,CAAC,GAAGuD,QAAQ,GAAGvD,KAAK;IAClD,CAAC,CAAC;IACFiD,eAAe,CAACtD,KAAK,CAAC,GAAGuD,SAAS;EACpC;EACA,MAAMM,eAAe,GAAGnF,QAAQ,CAAC,CAAC,CAAC,EAAEqE,YAAY,EAAE;IACjDd,aAAa,EAAEmB,gBAAgB;IAC/BjB,MAAM,EAAEmB;EACV,CAAC,CAAC;EACF,OAAOO,eAAe;AACxB,CAAC;AACD,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EAClC,IAAIC,MAAM,GAAG1E,YAAY,CAACV,yBAAyB,CAAC;EACpD,IAAImF,IAAI,IAAIzE,YAAY,CAACyE,IAAI,CAAC,EAAE;IAC9BC,MAAM,GAAG1E,YAAY,CAACyE,IAAI,CAAC;EAC7B;EACA,OAAOC,MAAM;AACf;AACA,OAAO,MAAMC,kBAAkB,GAAGC,KAAA,IAM5B;EAAA,IAN6B;IACjCC,MAAM;IACNC,eAAe;IACfpB,YAAY;IACZX,qBAAqB,GAAGrD,iCAAiC,CAACmF,MAAM,CAAC;IACjEE,uBAAuB,GAAG;EAC5B,CAAC,GAAAH,KAAA;EACC,MAAMI,wBAAwB,GAAG,CAACH,MAAM,CAACI,OAAO,CAACC,KAAK,CAACC,OAAO;EAC9D,IAAI1B,YAAY;EAChB,IAAIuB,wBAAwB,EAAE;IAC5BvB,YAAY,GAAG;MACbd,aAAa,EAAE,EAAE;MACjBE,MAAM,EAAE,CAAC,CAAC;MACVE;IACF,CAAC;EACH,CAAC,MAAM;IACL,MAAMqC,YAAY,GAAG3F,wBAAwB,CAACoF,MAAM,CAACI,OAAO,CAACC,KAAK,CAAC;IACnEzB,YAAY,GAAG;MACbd,aAAa,EAAEoC,uBAAuB,GAAG,EAAE,GAAG,CAAC,GAAGK,YAAY,CAACzC,aAAa,CAAC;MAC7EE,MAAM,EAAEzD,QAAQ,CAAC,CAAC,CAAC,EAAEgG,YAAY,CAACvC,MAAM,CAAC;MACzC;MACAE;IACF,CAAC;EACH;EACA,IAAIsC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIN,uBAAuB,IAAI,CAACC,wBAAwB,EAAE;IACxDK,aAAa,GAAGlD,MAAM,CAACC,IAAI,CAACqB,YAAY,CAACZ,MAAM,CAAC,CAACyC,MAAM,CAAC,CAACC,GAAG,EAAElB,GAAG,KAAKjF,QAAQ,CAAC,CAAC,CAAC,EAAEmG,GAAG,EAAE;MACtF,CAAClB,GAAG,GAAG;IACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACT;EACA,MAAMmB,qBAAqB,GAAG,CAAC,CAAC;EAChCV,eAAe,CAACnD,OAAO,CAAC8D,SAAS,IAAI;IACnC,MAAM;MACJ/E;IACF,CAAC,GAAG+E,SAAS;IACbD,qBAAqB,CAAC9E,KAAK,CAAC,GAAG,IAAI;IACnC2E,aAAa,CAAC3E,KAAK,CAAC,GAAG,IAAI;IAC3B,IAAIgF,aAAa,GAAGjC,YAAY,CAACZ,MAAM,CAACnC,KAAK,CAAC;IAC9C,IAAIgF,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,GAAGtG,QAAQ,CAAC,CAAC,CAAC,EAAEoF,oBAAoB,CAACiB,SAAS,CAAChB,IAAI,CAAC,EAAE;QACjE/D,KAAK;QACLwD,cAAc,EAAE;MAClB,CAAC,CAAC;MACFT,YAAY,CAACd,aAAa,CAAC1B,IAAI,CAACP,KAAK,CAAC;IACxC,CAAC,MAAM,IAAIqE,uBAAuB,EAAE;MAClCtB,YAAY,CAACd,aAAa,CAAC1B,IAAI,CAACP,KAAK,CAAC;IACxC;;IAEA;IACA,IAAIgF,aAAa,IAAIA,aAAa,CAACjB,IAAI,KAAKgB,SAAS,CAAChB,IAAI,EAAE;MAC1DiB,aAAa,GAAGtG,QAAQ,CAAC,CAAC,CAAC,EAAEoF,oBAAoB,CAACiB,SAAS,CAAChB,IAAI,CAAC,EAAE;QACjE/D;MACF,CAAC,CAAC;IACJ;IACA,IAAIwD,cAAc,GAAGwB,aAAa,CAACxB,cAAc;IACjDnE,4BAA4B,CAAC4B,OAAO,CAAC0C,GAAG,IAAI;MAC1C,IAAIoB,SAAS,CAACpB,GAAG,CAAC,KAAKnB,SAAS,EAAE;QAChCgB,cAAc,GAAG,IAAI;QACrB,IAAIuB,SAAS,CAACpB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;UACzBoB,SAAS,CAACpB,GAAG,CAAC,GAAGC,QAAQ;QAC3B;MACF;IACF,CAAC,CAAC;IACFb,YAAY,CAACZ,MAAM,CAACnC,KAAK,CAAC,GAAGrB,YAAY,CAACqG,aAAa,EAAEtG,QAAQ,CAAC,CAAC,CAAC,EAAEqG,SAAS,EAAE;MAC/EvB;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,IAAIa,uBAAuB,IAAI,CAACC,wBAAwB,EAAE;IACxD7C,MAAM,CAACC,IAAI,CAACqB,YAAY,CAACZ,MAAM,CAAC,CAAClB,OAAO,CAACjB,KAAK,IAAI;MAChD,IAAI,CAAC2E,aAAa,CAAC3E,KAAK,CAAC,EAAE;QACzB,OAAO+C,YAAY,CAACZ,MAAM,CAACnC,KAAK,CAAC;MACnC;IACF,CAAC,CAAC;EACJ;EACA,MAAMiF,6BAA6B,GAAGd,MAAM,CAACI,OAAO,CAACW,4BAA4B,CAAC,gBAAgB,EAAEnC,YAAY,CAAC;EACjH,MAAMoC,+BAA+B,GAAGrC,iBAAiB,CAACmC,6BAA6B,EAAEjC,YAAY,CAAC;EACtG,OAAOpB,mBAAmB,CAACuD,+BAA+B,EAAEhB,MAAM,CAACI,OAAO,CAACa,iBAAiB,GAAG,CAAC,IAAI5C,SAAS,CAAC;AAChH,CAAC;AACD,OAAO,SAAS6C,gCAAgCA,CAAAC,KAAA,EAM7C;EAAA,IAN8C;IAC/CC,mBAAmB;IACnBpB,MAAM;IACNqB,gBAAgB;IAChBC,eAAe;IACfC;EACF,CAAC,GAAAJ,KAAA;EACC,IAAIK,6BAA6B,GAAGJ,mBAAmB;EACvD,KAAK,IAAInE,CAAC,GAAGoE,gBAAgB,EAAEpE,CAAC,GAAGqE,eAAe,EAAErE,CAAC,IAAI,CAAC,EAAE;IAC1D,MAAMwE,GAAG,GAAGF,WAAW,CAACtE,CAAC,CAAC;IAC1B,IAAIwE,GAAG,EAAE;MACP,MAAMC,KAAK,GAAGH,WAAW,CAACtE,CAAC,CAAC,CAAC0E,EAAE;MAC/B,MAAMC,eAAe,GAAG5B,MAAM,CAACI,OAAO,CAACyB,2BAA2B,CAACH,KAAK,EAAEN,mBAAmB,CAAC;MAC9F,IAAIQ,eAAe,IAAIA,eAAe,CAACE,gBAAgB,EAAE;QACvDN,6BAA6B,GAAGI,eAAe,CAACG,oBAAoB;MACtE;IACF;EACF;EACA,OAAOP,6BAA6B;AACtC;AACA,OAAO,SAASQ,oBAAoBA,CAAChC,MAAM,EAAEiC,KAAK,EAAE;EAClD,MAAMC,aAAa,GAAGnH,yBAAyB,CAACiF,MAAM,CAAC;EACvD,MAAMmC,QAAQ,GAAGlH,sCAAsC,CAAC+E,MAAM,CAAC;EAC/D,MAAMoC,wBAAwB,GAAGpH,kCAAkC,CAACgF,MAAM,CAAC;EAC3E,MAAMqC,mBAAmB,GAAG5D,IAAI,CAAC6D,KAAK,CAACL,KAAK,CAACM,kBAAkB,GAAGL,aAAa,CAAC;EAChF,MAAMM,mBAAmB,GAAGJ,wBAAwB,GAAG3D,IAAI,CAAC6D,KAAK,CAAC,CAACL,KAAK,CAACQ,kBAAkB,IAAIR,KAAK,CAACM,kBAAkB,IAAIL,aAAa,CAAC,GAAG,CAAC;EAC7I,OAAOG,mBAAmB,IAAI,CAAC,IAAIF,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAGK,mBAAmB;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}