{"ast": null, "code": "// Copyright (c) 2005  <PERSON> Wu\n// All Rights Reserved.\n// See \"LICENSE\" for details.\n// Basic JavaScript BN library - subset useful for RSA encryption.\nimport { cbit, int2char, lbit, op_and, op_andnot, op_or, op_xor } from \"./util\";\n// Bits per digit\nvar dbits;\n// JavaScript engine analysis\nvar canary = 0xdeadbeefcafe;\nvar j_lm = (canary & 0xffffff) == 0xefcafe;\n//#region\nvar lowprimes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269, 271, 277, 281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379, 383, 389, 397, 401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521, 523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607, 613, 617, 619, 631, 641, 643, 647, 653, 659, 661, 673, 677, 683, 691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773, 787, 797, 809, 811, 821, 823, 827, 829, 839, 853, 857, 859, 863, 877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967, 971, 977, 983, 991, 997];\nvar lplim = (1 << 26) / lowprimes[lowprimes.length - 1];\n//#endregion\n// (public) Constructor\nvar BigInteger = /** @class */function () {\n  function BigInteger(a, b, c) {\n    if (a != null) {\n      if (\"number\" == typeof a) {\n        this.fromNumber(a, b, c);\n      } else if (b == null && \"string\" != typeof a) {\n        this.fromString(a, 256);\n      } else {\n        this.fromString(a, b);\n      }\n    }\n  }\n  //#region PUBLIC\n  // BigInteger.prototype.toString = bnToString;\n  // (public) return string representation in given radix\n  BigInteger.prototype.toString = function (b) {\n    if (this.s < 0) {\n      return \"-\" + this.negate().toString(b);\n    }\n    var k;\n    if (b == 16) {\n      k = 4;\n    } else if (b == 8) {\n      k = 3;\n    } else if (b == 2) {\n      k = 1;\n    } else if (b == 32) {\n      k = 5;\n    } else if (b == 4) {\n      k = 2;\n    } else {\n      return this.toRadix(b);\n    }\n    var km = (1 << k) - 1;\n    var d;\n    var m = false;\n    var r = \"\";\n    var i = this.t;\n    var p = this.DB - i * this.DB % k;\n    if (i-- > 0) {\n      if (p < this.DB && (d = this[i] >> p) > 0) {\n        m = true;\n        r = int2char(d);\n      }\n      while (i >= 0) {\n        if (p < k) {\n          d = (this[i] & (1 << p) - 1) << k - p;\n          d |= this[--i] >> (p += this.DB - k);\n        } else {\n          d = this[i] >> (p -= k) & km;\n          if (p <= 0) {\n            p += this.DB;\n            --i;\n          }\n        }\n        if (d > 0) {\n          m = true;\n        }\n        if (m) {\n          r += int2char(d);\n        }\n      }\n    }\n    return m ? r : \"0\";\n  };\n  // BigInteger.prototype.negate = bnNegate;\n  // (public) -this\n  BigInteger.prototype.negate = function () {\n    var r = nbi();\n    BigInteger.ZERO.subTo(this, r);\n    return r;\n  };\n  // BigInteger.prototype.abs = bnAbs;\n  // (public) |this|\n  BigInteger.prototype.abs = function () {\n    return this.s < 0 ? this.negate() : this;\n  };\n  // BigInteger.prototype.compareTo = bnCompareTo;\n  // (public) return + if this > a, - if this < a, 0 if equal\n  BigInteger.prototype.compareTo = function (a) {\n    var r = this.s - a.s;\n    if (r != 0) {\n      return r;\n    }\n    var i = this.t;\n    r = i - a.t;\n    if (r != 0) {\n      return this.s < 0 ? -r : r;\n    }\n    while (--i >= 0) {\n      if ((r = this[i] - a[i]) != 0) {\n        return r;\n      }\n    }\n    return 0;\n  };\n  // BigInteger.prototype.bitLength = bnBitLength;\n  // (public) return the number of bits in \"this\"\n  BigInteger.prototype.bitLength = function () {\n    if (this.t <= 0) {\n      return 0;\n    }\n    return this.DB * (this.t - 1) + nbits(this[this.t - 1] ^ this.s & this.DM);\n  };\n  // BigInteger.prototype.mod = bnMod;\n  // (public) this mod a\n  BigInteger.prototype.mod = function (a) {\n    var r = nbi();\n    this.abs().divRemTo(a, null, r);\n    if (this.s < 0 && r.compareTo(BigInteger.ZERO) > 0) {\n      a.subTo(r, r);\n    }\n    return r;\n  };\n  // BigInteger.prototype.modPowInt = bnModPowInt;\n  // (public) this^e % m, 0 <= e < 2^32\n  BigInteger.prototype.modPowInt = function (e, m) {\n    var z;\n    if (e < 256 || m.isEven()) {\n      z = new Classic(m);\n    } else {\n      z = new Montgomery(m);\n    }\n    return this.exp(e, z);\n  };\n  // BigInteger.prototype.clone = bnClone;\n  // (public)\n  BigInteger.prototype.clone = function () {\n    var r = nbi();\n    this.copyTo(r);\n    return r;\n  };\n  // BigInteger.prototype.intValue = bnIntValue;\n  // (public) return value as integer\n  BigInteger.prototype.intValue = function () {\n    if (this.s < 0) {\n      if (this.t == 1) {\n        return this[0] - this.DV;\n      } else if (this.t == 0) {\n        return -1;\n      }\n    } else if (this.t == 1) {\n      return this[0];\n    } else if (this.t == 0) {\n      return 0;\n    }\n    // assumes 16 < DB < 32\n    return (this[1] & (1 << 32 - this.DB) - 1) << this.DB | this[0];\n  };\n  // BigInteger.prototype.byteValue = bnByteValue;\n  // (public) return value as byte\n  BigInteger.prototype.byteValue = function () {\n    return this.t == 0 ? this.s : this[0] << 24 >> 24;\n  };\n  // BigInteger.prototype.shortValue = bnShortValue;\n  // (public) return value as short (assumes DB>=16)\n  BigInteger.prototype.shortValue = function () {\n    return this.t == 0 ? this.s : this[0] << 16 >> 16;\n  };\n  // BigInteger.prototype.signum = bnSigNum;\n  // (public) 0 if this == 0, 1 if this > 0\n  BigInteger.prototype.signum = function () {\n    if (this.s < 0) {\n      return -1;\n    } else if (this.t <= 0 || this.t == 1 && this[0] <= 0) {\n      return 0;\n    } else {\n      return 1;\n    }\n  };\n  // BigInteger.prototype.toByteArray = bnToByteArray;\n  // (public) convert to bigendian byte array\n  BigInteger.prototype.toByteArray = function () {\n    var i = this.t;\n    var r = [];\n    r[0] = this.s;\n    var p = this.DB - i * this.DB % 8;\n    var d;\n    var k = 0;\n    if (i-- > 0) {\n      if (p < this.DB && (d = this[i] >> p) != (this.s & this.DM) >> p) {\n        r[k++] = d | this.s << this.DB - p;\n      }\n      while (i >= 0) {\n        if (p < 8) {\n          d = (this[i] & (1 << p) - 1) << 8 - p;\n          d |= this[--i] >> (p += this.DB - 8);\n        } else {\n          d = this[i] >> (p -= 8) & 0xff;\n          if (p <= 0) {\n            p += this.DB;\n            --i;\n          }\n        }\n        if ((d & 0x80) != 0) {\n          d |= -256;\n        }\n        if (k == 0 && (this.s & 0x80) != (d & 0x80)) {\n          ++k;\n        }\n        if (k > 0 || d != this.s) {\n          r[k++] = d;\n        }\n      }\n    }\n    return r;\n  };\n  // BigInteger.prototype.equals = bnEquals;\n  BigInteger.prototype.equals = function (a) {\n    return this.compareTo(a) == 0;\n  };\n  // BigInteger.prototype.min = bnMin;\n  BigInteger.prototype.min = function (a) {\n    return this.compareTo(a) < 0 ? this : a;\n  };\n  // BigInteger.prototype.max = bnMax;\n  BigInteger.prototype.max = function (a) {\n    return this.compareTo(a) > 0 ? this : a;\n  };\n  // BigInteger.prototype.and = bnAnd;\n  BigInteger.prototype.and = function (a) {\n    var r = nbi();\n    this.bitwiseTo(a, op_and, r);\n    return r;\n  };\n  // BigInteger.prototype.or = bnOr;\n  BigInteger.prototype.or = function (a) {\n    var r = nbi();\n    this.bitwiseTo(a, op_or, r);\n    return r;\n  };\n  // BigInteger.prototype.xor = bnXor;\n  BigInteger.prototype.xor = function (a) {\n    var r = nbi();\n    this.bitwiseTo(a, op_xor, r);\n    return r;\n  };\n  // BigInteger.prototype.andNot = bnAndNot;\n  BigInteger.prototype.andNot = function (a) {\n    var r = nbi();\n    this.bitwiseTo(a, op_andnot, r);\n    return r;\n  };\n  // BigInteger.prototype.not = bnNot;\n  // (public) ~this\n  BigInteger.prototype.not = function () {\n    var r = nbi();\n    for (var i = 0; i < this.t; ++i) {\n      r[i] = this.DM & ~this[i];\n    }\n    r.t = this.t;\n    r.s = ~this.s;\n    return r;\n  };\n  // BigInteger.prototype.shiftLeft = bnShiftLeft;\n  // (public) this << n\n  BigInteger.prototype.shiftLeft = function (n) {\n    var r = nbi();\n    if (n < 0) {\n      this.rShiftTo(-n, r);\n    } else {\n      this.lShiftTo(n, r);\n    }\n    return r;\n  };\n  // BigInteger.prototype.shiftRight = bnShiftRight;\n  // (public) this >> n\n  BigInteger.prototype.shiftRight = function (n) {\n    var r = nbi();\n    if (n < 0) {\n      this.lShiftTo(-n, r);\n    } else {\n      this.rShiftTo(n, r);\n    }\n    return r;\n  };\n  // BigInteger.prototype.getLowestSetBit = bnGetLowestSetBit;\n  // (public) returns index of lowest 1-bit (or -1 if none)\n  BigInteger.prototype.getLowestSetBit = function () {\n    for (var i = 0; i < this.t; ++i) {\n      if (this[i] != 0) {\n        return i * this.DB + lbit(this[i]);\n      }\n    }\n    if (this.s < 0) {\n      return this.t * this.DB;\n    }\n    return -1;\n  };\n  // BigInteger.prototype.bitCount = bnBitCount;\n  // (public) return number of set bits\n  BigInteger.prototype.bitCount = function () {\n    var r = 0;\n    var x = this.s & this.DM;\n    for (var i = 0; i < this.t; ++i) {\n      r += cbit(this[i] ^ x);\n    }\n    return r;\n  };\n  // BigInteger.prototype.testBit = bnTestBit;\n  // (public) true iff nth bit is set\n  BigInteger.prototype.testBit = function (n) {\n    var j = Math.floor(n / this.DB);\n    if (j >= this.t) {\n      return this.s != 0;\n    }\n    return (this[j] & 1 << n % this.DB) != 0;\n  };\n  // BigInteger.prototype.setBit = bnSetBit;\n  // (public) this | (1<<n)\n  BigInteger.prototype.setBit = function (n) {\n    return this.changeBit(n, op_or);\n  };\n  // BigInteger.prototype.clearBit = bnClearBit;\n  // (public) this & ~(1<<n)\n  BigInteger.prototype.clearBit = function (n) {\n    return this.changeBit(n, op_andnot);\n  };\n  // BigInteger.prototype.flipBit = bnFlipBit;\n  // (public) this ^ (1<<n)\n  BigInteger.prototype.flipBit = function (n) {\n    return this.changeBit(n, op_xor);\n  };\n  // BigInteger.prototype.add = bnAdd;\n  // (public) this + a\n  BigInteger.prototype.add = function (a) {\n    var r = nbi();\n    this.addTo(a, r);\n    return r;\n  };\n  // BigInteger.prototype.subtract = bnSubtract;\n  // (public) this - a\n  BigInteger.prototype.subtract = function (a) {\n    var r = nbi();\n    this.subTo(a, r);\n    return r;\n  };\n  // BigInteger.prototype.multiply = bnMultiply;\n  // (public) this * a\n  BigInteger.prototype.multiply = function (a) {\n    var r = nbi();\n    this.multiplyTo(a, r);\n    return r;\n  };\n  // BigInteger.prototype.divide = bnDivide;\n  // (public) this / a\n  BigInteger.prototype.divide = function (a) {\n    var r = nbi();\n    this.divRemTo(a, r, null);\n    return r;\n  };\n  // BigInteger.prototype.remainder = bnRemainder;\n  // (public) this % a\n  BigInteger.prototype.remainder = function (a) {\n    var r = nbi();\n    this.divRemTo(a, null, r);\n    return r;\n  };\n  // BigInteger.prototype.divideAndRemainder = bnDivideAndRemainder;\n  // (public) [this/a,this%a]\n  BigInteger.prototype.divideAndRemainder = function (a) {\n    var q = nbi();\n    var r = nbi();\n    this.divRemTo(a, q, r);\n    return [q, r];\n  };\n  // BigInteger.prototype.modPow = bnModPow;\n  // (public) this^e % m (HAC 14.85)\n  BigInteger.prototype.modPow = function (e, m) {\n    var i = e.bitLength();\n    var k;\n    var r = nbv(1);\n    var z;\n    if (i <= 0) {\n      return r;\n    } else if (i < 18) {\n      k = 1;\n    } else if (i < 48) {\n      k = 3;\n    } else if (i < 144) {\n      k = 4;\n    } else if (i < 768) {\n      k = 5;\n    } else {\n      k = 6;\n    }\n    if (i < 8) {\n      z = new Classic(m);\n    } else if (m.isEven()) {\n      z = new Barrett(m);\n    } else {\n      z = new Montgomery(m);\n    }\n    // precomputation\n    var g = [];\n    var n = 3;\n    var k1 = k - 1;\n    var km = (1 << k) - 1;\n    g[1] = z.convert(this);\n    if (k > 1) {\n      var g2 = nbi();\n      z.sqrTo(g[1], g2);\n      while (n <= km) {\n        g[n] = nbi();\n        z.mulTo(g2, g[n - 2], g[n]);\n        n += 2;\n      }\n    }\n    var j = e.t - 1;\n    var w;\n    var is1 = true;\n    var r2 = nbi();\n    var t;\n    i = nbits(e[j]) - 1;\n    while (j >= 0) {\n      if (i >= k1) {\n        w = e[j] >> i - k1 & km;\n      } else {\n        w = (e[j] & (1 << i + 1) - 1) << k1 - i;\n        if (j > 0) {\n          w |= e[j - 1] >> this.DB + i - k1;\n        }\n      }\n      n = k;\n      while ((w & 1) == 0) {\n        w >>= 1;\n        --n;\n      }\n      if ((i -= n) < 0) {\n        i += this.DB;\n        --j;\n      }\n      if (is1) {\n        // ret == 1, don't bother squaring or multiplying it\n        g[w].copyTo(r);\n        is1 = false;\n      } else {\n        while (n > 1) {\n          z.sqrTo(r, r2);\n          z.sqrTo(r2, r);\n          n -= 2;\n        }\n        if (n > 0) {\n          z.sqrTo(r, r2);\n        } else {\n          t = r;\n          r = r2;\n          r2 = t;\n        }\n        z.mulTo(r2, g[w], r);\n      }\n      while (j >= 0 && (e[j] & 1 << i) == 0) {\n        z.sqrTo(r, r2);\n        t = r;\n        r = r2;\n        r2 = t;\n        if (--i < 0) {\n          i = this.DB - 1;\n          --j;\n        }\n      }\n    }\n    return z.revert(r);\n  };\n  // BigInteger.prototype.modInverse = bnModInverse;\n  // (public) 1/this % m (HAC 14.61)\n  BigInteger.prototype.modInverse = function (m) {\n    var ac = m.isEven();\n    if (this.isEven() && ac || m.signum() == 0) {\n      return BigInteger.ZERO;\n    }\n    var u = m.clone();\n    var v = this.clone();\n    var a = nbv(1);\n    var b = nbv(0);\n    var c = nbv(0);\n    var d = nbv(1);\n    while (u.signum() != 0) {\n      while (u.isEven()) {\n        u.rShiftTo(1, u);\n        if (ac) {\n          if (!a.isEven() || !b.isEven()) {\n            a.addTo(this, a);\n            b.subTo(m, b);\n          }\n          a.rShiftTo(1, a);\n        } else if (!b.isEven()) {\n          b.subTo(m, b);\n        }\n        b.rShiftTo(1, b);\n      }\n      while (v.isEven()) {\n        v.rShiftTo(1, v);\n        if (ac) {\n          if (!c.isEven() || !d.isEven()) {\n            c.addTo(this, c);\n            d.subTo(m, d);\n          }\n          c.rShiftTo(1, c);\n        } else if (!d.isEven()) {\n          d.subTo(m, d);\n        }\n        d.rShiftTo(1, d);\n      }\n      if (u.compareTo(v) >= 0) {\n        u.subTo(v, u);\n        if (ac) {\n          a.subTo(c, a);\n        }\n        b.subTo(d, b);\n      } else {\n        v.subTo(u, v);\n        if (ac) {\n          c.subTo(a, c);\n        }\n        d.subTo(b, d);\n      }\n    }\n    if (v.compareTo(BigInteger.ONE) != 0) {\n      return BigInteger.ZERO;\n    }\n    if (d.compareTo(m) >= 0) {\n      return d.subtract(m);\n    }\n    if (d.signum() < 0) {\n      d.addTo(m, d);\n    } else {\n      return d;\n    }\n    if (d.signum() < 0) {\n      return d.add(m);\n    } else {\n      return d;\n    }\n  };\n  // BigInteger.prototype.pow = bnPow;\n  // (public) this^e\n  BigInteger.prototype.pow = function (e) {\n    return this.exp(e, new NullExp());\n  };\n  // BigInteger.prototype.gcd = bnGCD;\n  // (public) gcd(this,a) (HAC 14.54)\n  BigInteger.prototype.gcd = function (a) {\n    var x = this.s < 0 ? this.negate() : this.clone();\n    var y = a.s < 0 ? a.negate() : a.clone();\n    if (x.compareTo(y) < 0) {\n      var t = x;\n      x = y;\n      y = t;\n    }\n    var i = x.getLowestSetBit();\n    var g = y.getLowestSetBit();\n    if (g < 0) {\n      return x;\n    }\n    if (i < g) {\n      g = i;\n    }\n    if (g > 0) {\n      x.rShiftTo(g, x);\n      y.rShiftTo(g, y);\n    }\n    while (x.signum() > 0) {\n      if ((i = x.getLowestSetBit()) > 0) {\n        x.rShiftTo(i, x);\n      }\n      if ((i = y.getLowestSetBit()) > 0) {\n        y.rShiftTo(i, y);\n      }\n      if (x.compareTo(y) >= 0) {\n        x.subTo(y, x);\n        x.rShiftTo(1, x);\n      } else {\n        y.subTo(x, y);\n        y.rShiftTo(1, y);\n      }\n    }\n    if (g > 0) {\n      y.lShiftTo(g, y);\n    }\n    return y;\n  };\n  // BigInteger.prototype.isProbablePrime = bnIsProbablePrime;\n  // (public) test primality with certainty >= 1-.5^t\n  BigInteger.prototype.isProbablePrime = function (t) {\n    var i;\n    var x = this.abs();\n    if (x.t == 1 && x[0] <= lowprimes[lowprimes.length - 1]) {\n      for (i = 0; i < lowprimes.length; ++i) {\n        if (x[0] == lowprimes[i]) {\n          return true;\n        }\n      }\n      return false;\n    }\n    if (x.isEven()) {\n      return false;\n    }\n    i = 1;\n    while (i < lowprimes.length) {\n      var m = lowprimes[i];\n      var j = i + 1;\n      while (j < lowprimes.length && m < lplim) {\n        m *= lowprimes[j++];\n      }\n      m = x.modInt(m);\n      while (i < j) {\n        if (m % lowprimes[i++] == 0) {\n          return false;\n        }\n      }\n    }\n    return x.millerRabin(t);\n  };\n  //#endregion PUBLIC\n  //#region PROTECTED\n  // BigInteger.prototype.copyTo = bnpCopyTo;\n  // (protected) copy this to r\n  BigInteger.prototype.copyTo = function (r) {\n    for (var i = this.t - 1; i >= 0; --i) {\n      r[i] = this[i];\n    }\n    r.t = this.t;\n    r.s = this.s;\n  };\n  // BigInteger.prototype.fromInt = bnpFromInt;\n  // (protected) set from integer value x, -DV <= x < DV\n  BigInteger.prototype.fromInt = function (x) {\n    this.t = 1;\n    this.s = x < 0 ? -1 : 0;\n    if (x > 0) {\n      this[0] = x;\n    } else if (x < -1) {\n      this[0] = x + this.DV;\n    } else {\n      this.t = 0;\n    }\n  };\n  // BigInteger.prototype.fromString = bnpFromString;\n  // (protected) set from string and radix\n  BigInteger.prototype.fromString = function (s, b) {\n    var k;\n    if (b == 16) {\n      k = 4;\n    } else if (b == 8) {\n      k = 3;\n    } else if (b == 256) {\n      k = 8;\n      /* byte array */\n    } else if (b == 2) {\n      k = 1;\n    } else if (b == 32) {\n      k = 5;\n    } else if (b == 4) {\n      k = 2;\n    } else {\n      this.fromRadix(s, b);\n      return;\n    }\n    this.t = 0;\n    this.s = 0;\n    var i = s.length;\n    var mi = false;\n    var sh = 0;\n    while (--i >= 0) {\n      var x = k == 8 ? +s[i] & 0xff : intAt(s, i);\n      if (x < 0) {\n        if (s.charAt(i) == \"-\") {\n          mi = true;\n        }\n        continue;\n      }\n      mi = false;\n      if (sh == 0) {\n        this[this.t++] = x;\n      } else if (sh + k > this.DB) {\n        this[this.t - 1] |= (x & (1 << this.DB - sh) - 1) << sh;\n        this[this.t++] = x >> this.DB - sh;\n      } else {\n        this[this.t - 1] |= x << sh;\n      }\n      sh += k;\n      if (sh >= this.DB) {\n        sh -= this.DB;\n      }\n    }\n    if (k == 8 && (+s[0] & 0x80) != 0) {\n      this.s = -1;\n      if (sh > 0) {\n        this[this.t - 1] |= (1 << this.DB - sh) - 1 << sh;\n      }\n    }\n    this.clamp();\n    if (mi) {\n      BigInteger.ZERO.subTo(this, this);\n    }\n  };\n  // BigInteger.prototype.clamp = bnpClamp;\n  // (protected) clamp off excess high words\n  BigInteger.prototype.clamp = function () {\n    var c = this.s & this.DM;\n    while (this.t > 0 && this[this.t - 1] == c) {\n      --this.t;\n    }\n  };\n  // BigInteger.prototype.dlShiftTo = bnpDLShiftTo;\n  // (protected) r = this << n*DB\n  BigInteger.prototype.dlShiftTo = function (n, r) {\n    var i;\n    for (i = this.t - 1; i >= 0; --i) {\n      r[i + n] = this[i];\n    }\n    for (i = n - 1; i >= 0; --i) {\n      r[i] = 0;\n    }\n    r.t = this.t + n;\n    r.s = this.s;\n  };\n  // BigInteger.prototype.drShiftTo = bnpDRShiftTo;\n  // (protected) r = this >> n*DB\n  BigInteger.prototype.drShiftTo = function (n, r) {\n    for (var i = n; i < this.t; ++i) {\n      r[i - n] = this[i];\n    }\n    r.t = Math.max(this.t - n, 0);\n    r.s = this.s;\n  };\n  // BigInteger.prototype.lShiftTo = bnpLShiftTo;\n  // (protected) r = this << n\n  BigInteger.prototype.lShiftTo = function (n, r) {\n    var bs = n % this.DB;\n    var cbs = this.DB - bs;\n    var bm = (1 << cbs) - 1;\n    var ds = Math.floor(n / this.DB);\n    var c = this.s << bs & this.DM;\n    for (var i = this.t - 1; i >= 0; --i) {\n      r[i + ds + 1] = this[i] >> cbs | c;\n      c = (this[i] & bm) << bs;\n    }\n    for (var i = ds - 1; i >= 0; --i) {\n      r[i] = 0;\n    }\n    r[ds] = c;\n    r.t = this.t + ds + 1;\n    r.s = this.s;\n    r.clamp();\n  };\n  // BigInteger.prototype.rShiftTo = bnpRShiftTo;\n  // (protected) r = this >> n\n  BigInteger.prototype.rShiftTo = function (n, r) {\n    r.s = this.s;\n    var ds = Math.floor(n / this.DB);\n    if (ds >= this.t) {\n      r.t = 0;\n      return;\n    }\n    var bs = n % this.DB;\n    var cbs = this.DB - bs;\n    var bm = (1 << bs) - 1;\n    r[0] = this[ds] >> bs;\n    for (var i = ds + 1; i < this.t; ++i) {\n      r[i - ds - 1] |= (this[i] & bm) << cbs;\n      r[i - ds] = this[i] >> bs;\n    }\n    if (bs > 0) {\n      r[this.t - ds - 1] |= (this.s & bm) << cbs;\n    }\n    r.t = this.t - ds;\n    r.clamp();\n  };\n  // BigInteger.prototype.subTo = bnpSubTo;\n  // (protected) r = this - a\n  BigInteger.prototype.subTo = function (a, r) {\n    var i = 0;\n    var c = 0;\n    var m = Math.min(a.t, this.t);\n    while (i < m) {\n      c += this[i] - a[i];\n      r[i++] = c & this.DM;\n      c >>= this.DB;\n    }\n    if (a.t < this.t) {\n      c -= a.s;\n      while (i < this.t) {\n        c += this[i];\n        r[i++] = c & this.DM;\n        c >>= this.DB;\n      }\n      c += this.s;\n    } else {\n      c += this.s;\n      while (i < a.t) {\n        c -= a[i];\n        r[i++] = c & this.DM;\n        c >>= this.DB;\n      }\n      c -= a.s;\n    }\n    r.s = c < 0 ? -1 : 0;\n    if (c < -1) {\n      r[i++] = this.DV + c;\n    } else if (c > 0) {\n      r[i++] = c;\n    }\n    r.t = i;\n    r.clamp();\n  };\n  // BigInteger.prototype.multiplyTo = bnpMultiplyTo;\n  // (protected) r = this * a, r != this,a (HAC 14.12)\n  // \"this\" should be the larger one if appropriate.\n  BigInteger.prototype.multiplyTo = function (a, r) {\n    var x = this.abs();\n    var y = a.abs();\n    var i = x.t;\n    r.t = i + y.t;\n    while (--i >= 0) {\n      r[i] = 0;\n    }\n    for (i = 0; i < y.t; ++i) {\n      r[i + x.t] = x.am(0, y[i], r, i, 0, x.t);\n    }\n    r.s = 0;\n    r.clamp();\n    if (this.s != a.s) {\n      BigInteger.ZERO.subTo(r, r);\n    }\n  };\n  // BigInteger.prototype.squareTo = bnpSquareTo;\n  // (protected) r = this^2, r != this (HAC 14.16)\n  BigInteger.prototype.squareTo = function (r) {\n    var x = this.abs();\n    var i = r.t = 2 * x.t;\n    while (--i >= 0) {\n      r[i] = 0;\n    }\n    for (i = 0; i < x.t - 1; ++i) {\n      var c = x.am(i, x[i], r, 2 * i, 0, 1);\n      if ((r[i + x.t] += x.am(i + 1, 2 * x[i], r, 2 * i + 1, c, x.t - i - 1)) >= x.DV) {\n        r[i + x.t] -= x.DV;\n        r[i + x.t + 1] = 1;\n      }\n    }\n    if (r.t > 0) {\n      r[r.t - 1] += x.am(i, x[i], r, 2 * i, 0, 1);\n    }\n    r.s = 0;\n    r.clamp();\n  };\n  // BigInteger.prototype.divRemTo = bnpDivRemTo;\n  // (protected) divide this by m, quotient and remainder to q, r (HAC 14.20)\n  // r != q, this != m.  q or r may be null.\n  BigInteger.prototype.divRemTo = function (m, q, r) {\n    var pm = m.abs();\n    if (pm.t <= 0) {\n      return;\n    }\n    var pt = this.abs();\n    if (pt.t < pm.t) {\n      if (q != null) {\n        q.fromInt(0);\n      }\n      if (r != null) {\n        this.copyTo(r);\n      }\n      return;\n    }\n    if (r == null) {\n      r = nbi();\n    }\n    var y = nbi();\n    var ts = this.s;\n    var ms = m.s;\n    var nsh = this.DB - nbits(pm[pm.t - 1]); // normalize modulus\n    if (nsh > 0) {\n      pm.lShiftTo(nsh, y);\n      pt.lShiftTo(nsh, r);\n    } else {\n      pm.copyTo(y);\n      pt.copyTo(r);\n    }\n    var ys = y.t;\n    var y0 = y[ys - 1];\n    if (y0 == 0) {\n      return;\n    }\n    var yt = y0 * (1 << this.F1) + (ys > 1 ? y[ys - 2] >> this.F2 : 0);\n    var d1 = this.FV / yt;\n    var d2 = (1 << this.F1) / yt;\n    var e = 1 << this.F2;\n    var i = r.t;\n    var j = i - ys;\n    var t = q == null ? nbi() : q;\n    y.dlShiftTo(j, t);\n    if (r.compareTo(t) >= 0) {\n      r[r.t++] = 1;\n      r.subTo(t, r);\n    }\n    BigInteger.ONE.dlShiftTo(ys, t);\n    t.subTo(y, y); // \"negative\" y so we can replace sub with am later\n    while (y.t < ys) {\n      y[y.t++] = 0;\n    }\n    while (--j >= 0) {\n      // Estimate quotient digit\n      var qd = r[--i] == y0 ? this.DM : Math.floor(r[i] * d1 + (r[i - 1] + e) * d2);\n      if ((r[i] += y.am(0, qd, r, j, 0, ys)) < qd) {\n        // Try it out\n        y.dlShiftTo(j, t);\n        r.subTo(t, r);\n        while (r[i] < --qd) {\n          r.subTo(t, r);\n        }\n      }\n    }\n    if (q != null) {\n      r.drShiftTo(ys, q);\n      if (ts != ms) {\n        BigInteger.ZERO.subTo(q, q);\n      }\n    }\n    r.t = ys;\n    r.clamp();\n    if (nsh > 0) {\n      r.rShiftTo(nsh, r);\n    } // Denormalize remainder\n    if (ts < 0) {\n      BigInteger.ZERO.subTo(r, r);\n    }\n  };\n  // BigInteger.prototype.invDigit = bnpInvDigit;\n  // (protected) return \"-1/this % 2^DB\"; useful for Mont. reduction\n  // justification:\n  //         xy == 1 (mod m)\n  //         xy =  1+km\n  //   xy(2-xy) = (1+km)(1-km)\n  // x[y(2-xy)] = 1-k^2m^2\n  // x[y(2-xy)] == 1 (mod m^2)\n  // if y is 1/x mod m, then y(2-xy) is 1/x mod m^2\n  // should reduce x and y(2-xy) by m^2 at each step to keep size bounded.\n  // JS multiply \"overflows\" differently from C/C++, so care is needed here.\n  BigInteger.prototype.invDigit = function () {\n    if (this.t < 1) {\n      return 0;\n    }\n    var x = this[0];\n    if ((x & 1) == 0) {\n      return 0;\n    }\n    var y = x & 3; // y == 1/x mod 2^2\n    y = y * (2 - (x & 0xf) * y) & 0xf; // y == 1/x mod 2^4\n    y = y * (2 - (x & 0xff) * y) & 0xff; // y == 1/x mod 2^8\n    y = y * (2 - ((x & 0xffff) * y & 0xffff)) & 0xffff; // y == 1/x mod 2^16\n    // last step - calculate inverse mod DV directly;\n    // assumes 16 < DB <= 32 and assumes ability to handle 48-bit ints\n    y = y * (2 - x * y % this.DV) % this.DV; // y == 1/x mod 2^dbits\n    // we really want the negative inverse, and -DV < y < DV\n    return y > 0 ? this.DV - y : -y;\n  };\n  // BigInteger.prototype.isEven = bnpIsEven;\n  // (protected) true iff this is even\n  BigInteger.prototype.isEven = function () {\n    return (this.t > 0 ? this[0] & 1 : this.s) == 0;\n  };\n  // BigInteger.prototype.exp = bnpExp;\n  // (protected) this^e, e < 2^32, doing sqr and mul with \"r\" (HAC 14.79)\n  BigInteger.prototype.exp = function (e, z) {\n    if (e > 0xffffffff || e < 1) {\n      return BigInteger.ONE;\n    }\n    var r = nbi();\n    var r2 = nbi();\n    var g = z.convert(this);\n    var i = nbits(e) - 1;\n    g.copyTo(r);\n    while (--i >= 0) {\n      z.sqrTo(r, r2);\n      if ((e & 1 << i) > 0) {\n        z.mulTo(r2, g, r);\n      } else {\n        var t = r;\n        r = r2;\n        r2 = t;\n      }\n    }\n    return z.revert(r);\n  };\n  // BigInteger.prototype.chunkSize = bnpChunkSize;\n  // (protected) return x s.t. r^x < DV\n  BigInteger.prototype.chunkSize = function (r) {\n    return Math.floor(Math.LN2 * this.DB / Math.log(r));\n  };\n  // BigInteger.prototype.toRadix = bnpToRadix;\n  // (protected) convert to radix string\n  BigInteger.prototype.toRadix = function (b) {\n    if (b == null) {\n      b = 10;\n    }\n    if (this.signum() == 0 || b < 2 || b > 36) {\n      return \"0\";\n    }\n    var cs = this.chunkSize(b);\n    var a = Math.pow(b, cs);\n    var d = nbv(a);\n    var y = nbi();\n    var z = nbi();\n    var r = \"\";\n    this.divRemTo(d, y, z);\n    while (y.signum() > 0) {\n      r = (a + z.intValue()).toString(b).substr(1) + r;\n      y.divRemTo(d, y, z);\n    }\n    return z.intValue().toString(b) + r;\n  };\n  // BigInteger.prototype.fromRadix = bnpFromRadix;\n  // (protected) convert from radix string\n  BigInteger.prototype.fromRadix = function (s, b) {\n    this.fromInt(0);\n    if (b == null) {\n      b = 10;\n    }\n    var cs = this.chunkSize(b);\n    var d = Math.pow(b, cs);\n    var mi = false;\n    var j = 0;\n    var w = 0;\n    for (var i = 0; i < s.length; ++i) {\n      var x = intAt(s, i);\n      if (x < 0) {\n        if (s.charAt(i) == \"-\" && this.signum() == 0) {\n          mi = true;\n        }\n        continue;\n      }\n      w = b * w + x;\n      if (++j >= cs) {\n        this.dMultiply(d);\n        this.dAddOffset(w, 0);\n        j = 0;\n        w = 0;\n      }\n    }\n    if (j > 0) {\n      this.dMultiply(Math.pow(b, j));\n      this.dAddOffset(w, 0);\n    }\n    if (mi) {\n      BigInteger.ZERO.subTo(this, this);\n    }\n  };\n  // BigInteger.prototype.fromNumber = bnpFromNumber;\n  // (protected) alternate constructor\n  BigInteger.prototype.fromNumber = function (a, b, c) {\n    if (\"number\" == typeof b) {\n      // new BigInteger(int,int,RNG)\n      if (a < 2) {\n        this.fromInt(1);\n      } else {\n        this.fromNumber(a, c);\n        if (!this.testBit(a - 1)) {\n          // force MSB set\n          this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), op_or, this);\n        }\n        if (this.isEven()) {\n          this.dAddOffset(1, 0);\n        } // force odd\n        while (!this.isProbablePrime(b)) {\n          this.dAddOffset(2, 0);\n          if (this.bitLength() > a) {\n            this.subTo(BigInteger.ONE.shiftLeft(a - 1), this);\n          }\n        }\n      }\n    } else {\n      // new BigInteger(int,RNG)\n      var x = [];\n      var t = a & 7;\n      x.length = (a >> 3) + 1;\n      b.nextBytes(x);\n      if (t > 0) {\n        x[0] &= (1 << t) - 1;\n      } else {\n        x[0] = 0;\n      }\n      this.fromString(x, 256);\n    }\n  };\n  // BigInteger.prototype.bitwiseTo = bnpBitwiseTo;\n  // (protected) r = this op a (bitwise)\n  BigInteger.prototype.bitwiseTo = function (a, op, r) {\n    var i;\n    var f;\n    var m = Math.min(a.t, this.t);\n    for (i = 0; i < m; ++i) {\n      r[i] = op(this[i], a[i]);\n    }\n    if (a.t < this.t) {\n      f = a.s & this.DM;\n      for (i = m; i < this.t; ++i) {\n        r[i] = op(this[i], f);\n      }\n      r.t = this.t;\n    } else {\n      f = this.s & this.DM;\n      for (i = m; i < a.t; ++i) {\n        r[i] = op(f, a[i]);\n      }\n      r.t = a.t;\n    }\n    r.s = op(this.s, a.s);\n    r.clamp();\n  };\n  // BigInteger.prototype.changeBit = bnpChangeBit;\n  // (protected) this op (1<<n)\n  BigInteger.prototype.changeBit = function (n, op) {\n    var r = BigInteger.ONE.shiftLeft(n);\n    this.bitwiseTo(r, op, r);\n    return r;\n  };\n  // BigInteger.prototype.addTo = bnpAddTo;\n  // (protected) r = this + a\n  BigInteger.prototype.addTo = function (a, r) {\n    var i = 0;\n    var c = 0;\n    var m = Math.min(a.t, this.t);\n    while (i < m) {\n      c += this[i] + a[i];\n      r[i++] = c & this.DM;\n      c >>= this.DB;\n    }\n    if (a.t < this.t) {\n      c += a.s;\n      while (i < this.t) {\n        c += this[i];\n        r[i++] = c & this.DM;\n        c >>= this.DB;\n      }\n      c += this.s;\n    } else {\n      c += this.s;\n      while (i < a.t) {\n        c += a[i];\n        r[i++] = c & this.DM;\n        c >>= this.DB;\n      }\n      c += a.s;\n    }\n    r.s = c < 0 ? -1 : 0;\n    if (c > 0) {\n      r[i++] = c;\n    } else if (c < -1) {\n      r[i++] = this.DV + c;\n    }\n    r.t = i;\n    r.clamp();\n  };\n  // BigInteger.prototype.dMultiply = bnpDMultiply;\n  // (protected) this *= n, this >= 0, 1 < n < DV\n  BigInteger.prototype.dMultiply = function (n) {\n    this[this.t] = this.am(0, n - 1, this, 0, 0, this.t);\n    ++this.t;\n    this.clamp();\n  };\n  // BigInteger.prototype.dAddOffset = bnpDAddOffset;\n  // (protected) this += n << w words, this >= 0\n  BigInteger.prototype.dAddOffset = function (n, w) {\n    if (n == 0) {\n      return;\n    }\n    while (this.t <= w) {\n      this[this.t++] = 0;\n    }\n    this[w] += n;\n    while (this[w] >= this.DV) {\n      this[w] -= this.DV;\n      if (++w >= this.t) {\n        this[this.t++] = 0;\n      }\n      ++this[w];\n    }\n  };\n  // BigInteger.prototype.multiplyLowerTo = bnpMultiplyLowerTo;\n  // (protected) r = lower n words of \"this * a\", a.t <= n\n  // \"this\" should be the larger one if appropriate.\n  BigInteger.prototype.multiplyLowerTo = function (a, n, r) {\n    var i = Math.min(this.t + a.t, n);\n    r.s = 0; // assumes a,this >= 0\n    r.t = i;\n    while (i > 0) {\n      r[--i] = 0;\n    }\n    for (var j = r.t - this.t; i < j; ++i) {\n      r[i + this.t] = this.am(0, a[i], r, i, 0, this.t);\n    }\n    for (var j = Math.min(a.t, n); i < j; ++i) {\n      this.am(0, a[i], r, i, 0, n - i);\n    }\n    r.clamp();\n  };\n  // BigInteger.prototype.multiplyUpperTo = bnpMultiplyUpperTo;\n  // (protected) r = \"this * a\" without lower n words, n > 0\n  // \"this\" should be the larger one if appropriate.\n  BigInteger.prototype.multiplyUpperTo = function (a, n, r) {\n    --n;\n    var i = r.t = this.t + a.t - n;\n    r.s = 0; // assumes a,this >= 0\n    while (--i >= 0) {\n      r[i] = 0;\n    }\n    for (i = Math.max(n - this.t, 0); i < a.t; ++i) {\n      r[this.t + i - n] = this.am(n - i, a[i], r, 0, 0, this.t + i - n);\n    }\n    r.clamp();\n    r.drShiftTo(1, r);\n  };\n  // BigInteger.prototype.modInt = bnpModInt;\n  // (protected) this % n, n < 2^26\n  BigInteger.prototype.modInt = function (n) {\n    if (n <= 0) {\n      return 0;\n    }\n    var d = this.DV % n;\n    var r = this.s < 0 ? n - 1 : 0;\n    if (this.t > 0) {\n      if (d == 0) {\n        r = this[0] % n;\n      } else {\n        for (var i = this.t - 1; i >= 0; --i) {\n          r = (d * r + this[i]) % n;\n        }\n      }\n    }\n    return r;\n  };\n  // BigInteger.prototype.millerRabin = bnpMillerRabin;\n  // (protected) true if probably prime (HAC 4.24, Miller-Rabin)\n  BigInteger.prototype.millerRabin = function (t) {\n    var n1 = this.subtract(BigInteger.ONE);\n    var k = n1.getLowestSetBit();\n    if (k <= 0) {\n      return false;\n    }\n    var r = n1.shiftRight(k);\n    t = t + 1 >> 1;\n    if (t > lowprimes.length) {\n      t = lowprimes.length;\n    }\n    var a = nbi();\n    for (var i = 0; i < t; ++i) {\n      // Pick bases at random, instead of starting at 2\n      a.fromInt(lowprimes[Math.floor(Math.random() * lowprimes.length)]);\n      var y = a.modPow(r, this);\n      if (y.compareTo(BigInteger.ONE) != 0 && y.compareTo(n1) != 0) {\n        var j = 1;\n        while (j++ < k && y.compareTo(n1) != 0) {\n          y = y.modPowInt(2, this);\n          if (y.compareTo(BigInteger.ONE) == 0) {\n            return false;\n          }\n        }\n        if (y.compareTo(n1) != 0) {\n          return false;\n        }\n      }\n    }\n    return true;\n  };\n  // BigInteger.prototype.square = bnSquare;\n  // (public) this^2\n  BigInteger.prototype.square = function () {\n    var r = nbi();\n    this.squareTo(r);\n    return r;\n  };\n  //#region ASYNC\n  // Public API method\n  BigInteger.prototype.gcda = function (a, callback) {\n    var x = this.s < 0 ? this.negate() : this.clone();\n    var y = a.s < 0 ? a.negate() : a.clone();\n    if (x.compareTo(y) < 0) {\n      var t = x;\n      x = y;\n      y = t;\n    }\n    var i = x.getLowestSetBit();\n    var g = y.getLowestSetBit();\n    if (g < 0) {\n      callback(x);\n      return;\n    }\n    if (i < g) {\n      g = i;\n    }\n    if (g > 0) {\n      x.rShiftTo(g, x);\n      y.rShiftTo(g, y);\n    }\n    // Workhorse of the algorithm, gets called 200 - 800 times per 512 bit keygen.\n    var gcda1 = function () {\n      if ((i = x.getLowestSetBit()) > 0) {\n        x.rShiftTo(i, x);\n      }\n      if ((i = y.getLowestSetBit()) > 0) {\n        y.rShiftTo(i, y);\n      }\n      if (x.compareTo(y) >= 0) {\n        x.subTo(y, x);\n        x.rShiftTo(1, x);\n      } else {\n        y.subTo(x, y);\n        y.rShiftTo(1, y);\n      }\n      if (!(x.signum() > 0)) {\n        if (g > 0) {\n          y.lShiftTo(g, y);\n        }\n        setTimeout(function () {\n          callback(y);\n        }, 0); // escape\n      } else {\n        setTimeout(gcda1, 0);\n      }\n    };\n    setTimeout(gcda1, 10);\n  };\n  // (protected) alternate constructor\n  BigInteger.prototype.fromNumberAsync = function (a, b, c, callback) {\n    if (\"number\" == typeof b) {\n      if (a < 2) {\n        this.fromInt(1);\n      } else {\n        this.fromNumber(a, c);\n        if (!this.testBit(a - 1)) {\n          this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), op_or, this);\n        }\n        if (this.isEven()) {\n          this.dAddOffset(1, 0);\n        }\n        var bnp_1 = this;\n        var bnpfn1_1 = function () {\n          bnp_1.dAddOffset(2, 0);\n          if (bnp_1.bitLength() > a) {\n            bnp_1.subTo(BigInteger.ONE.shiftLeft(a - 1), bnp_1);\n          }\n          if (bnp_1.isProbablePrime(b)) {\n            setTimeout(function () {\n              callback();\n            }, 0); // escape\n          } else {\n            setTimeout(bnpfn1_1, 0);\n          }\n        };\n        setTimeout(bnpfn1_1, 0);\n      }\n    } else {\n      var x = [];\n      var t = a & 7;\n      x.length = (a >> 3) + 1;\n      b.nextBytes(x);\n      if (t > 0) {\n        x[0] &= (1 << t) - 1;\n      } else {\n        x[0] = 0;\n      }\n      this.fromString(x, 256);\n    }\n  };\n  return BigInteger;\n}();\nexport { BigInteger };\n//#region REDUCERS\n//#region NullExp\nvar NullExp = /** @class */function () {\n  function NullExp() {}\n  // NullExp.prototype.convert = nNop;\n  NullExp.prototype.convert = function (x) {\n    return x;\n  };\n  // NullExp.prototype.revert = nNop;\n  NullExp.prototype.revert = function (x) {\n    return x;\n  };\n  // NullExp.prototype.mulTo = nMulTo;\n  NullExp.prototype.mulTo = function (x, y, r) {\n    x.multiplyTo(y, r);\n  };\n  // NullExp.prototype.sqrTo = nSqrTo;\n  NullExp.prototype.sqrTo = function (x, r) {\n    x.squareTo(r);\n  };\n  return NullExp;\n}();\n// Modular reduction using \"classic\" algorithm\nvar Classic = /** @class */function () {\n  function Classic(m) {\n    this.m = m;\n  }\n  // Classic.prototype.convert = cConvert;\n  Classic.prototype.convert = function (x) {\n    if (x.s < 0 || x.compareTo(this.m) >= 0) {\n      return x.mod(this.m);\n    } else {\n      return x;\n    }\n  };\n  // Classic.prototype.revert = cRevert;\n  Classic.prototype.revert = function (x) {\n    return x;\n  };\n  // Classic.prototype.reduce = cReduce;\n  Classic.prototype.reduce = function (x) {\n    x.divRemTo(this.m, null, x);\n  };\n  // Classic.prototype.mulTo = cMulTo;\n  Classic.prototype.mulTo = function (x, y, r) {\n    x.multiplyTo(y, r);\n    this.reduce(r);\n  };\n  // Classic.prototype.sqrTo = cSqrTo;\n  Classic.prototype.sqrTo = function (x, r) {\n    x.squareTo(r);\n    this.reduce(r);\n  };\n  return Classic;\n}();\n//#endregion\n//#region Montgomery\n// Montgomery reduction\nvar Montgomery = /** @class */function () {\n  function Montgomery(m) {\n    this.m = m;\n    this.mp = m.invDigit();\n    this.mpl = this.mp & 0x7fff;\n    this.mph = this.mp >> 15;\n    this.um = (1 << m.DB - 15) - 1;\n    this.mt2 = 2 * m.t;\n  }\n  // Montgomery.prototype.convert = montConvert;\n  // xR mod m\n  Montgomery.prototype.convert = function (x) {\n    var r = nbi();\n    x.abs().dlShiftTo(this.m.t, r);\n    r.divRemTo(this.m, null, r);\n    if (x.s < 0 && r.compareTo(BigInteger.ZERO) > 0) {\n      this.m.subTo(r, r);\n    }\n    return r;\n  };\n  // Montgomery.prototype.revert = montRevert;\n  // x/R mod m\n  Montgomery.prototype.revert = function (x) {\n    var r = nbi();\n    x.copyTo(r);\n    this.reduce(r);\n    return r;\n  };\n  // Montgomery.prototype.reduce = montReduce;\n  // x = x/R mod m (HAC 14.32)\n  Montgomery.prototype.reduce = function (x) {\n    while (x.t <= this.mt2) {\n      // pad x so am has enough room later\n      x[x.t++] = 0;\n    }\n    for (var i = 0; i < this.m.t; ++i) {\n      // faster way of calculating u0 = x[i]*mp mod DV\n      var j = x[i] & 0x7fff;\n      var u0 = j * this.mpl + ((j * this.mph + (x[i] >> 15) * this.mpl & this.um) << 15) & x.DM;\n      // use am to combine the multiply-shift-add into one call\n      j = i + this.m.t;\n      x[j] += this.m.am(0, u0, x, i, 0, this.m.t);\n      // propagate carry\n      while (x[j] >= x.DV) {\n        x[j] -= x.DV;\n        x[++j]++;\n      }\n    }\n    x.clamp();\n    x.drShiftTo(this.m.t, x);\n    if (x.compareTo(this.m) >= 0) {\n      x.subTo(this.m, x);\n    }\n  };\n  // Montgomery.prototype.mulTo = montMulTo;\n  // r = \"xy/R mod m\"; x,y != r\n  Montgomery.prototype.mulTo = function (x, y, r) {\n    x.multiplyTo(y, r);\n    this.reduce(r);\n  };\n  // Montgomery.prototype.sqrTo = montSqrTo;\n  // r = \"x^2/R mod m\"; x != r\n  Montgomery.prototype.sqrTo = function (x, r) {\n    x.squareTo(r);\n    this.reduce(r);\n  };\n  return Montgomery;\n}();\n//#endregion Montgomery\n//#region Barrett\n// Barrett modular reduction\nvar Barrett = /** @class */function () {\n  function Barrett(m) {\n    this.m = m;\n    // setup Barrett\n    this.r2 = nbi();\n    this.q3 = nbi();\n    BigInteger.ONE.dlShiftTo(2 * m.t, this.r2);\n    this.mu = this.r2.divide(m);\n  }\n  // Barrett.prototype.convert = barrettConvert;\n  Barrett.prototype.convert = function (x) {\n    if (x.s < 0 || x.t > 2 * this.m.t) {\n      return x.mod(this.m);\n    } else if (x.compareTo(this.m) < 0) {\n      return x;\n    } else {\n      var r = nbi();\n      x.copyTo(r);\n      this.reduce(r);\n      return r;\n    }\n  };\n  // Barrett.prototype.revert = barrettRevert;\n  Barrett.prototype.revert = function (x) {\n    return x;\n  };\n  // Barrett.prototype.reduce = barrettReduce;\n  // x = x mod m (HAC 14.42)\n  Barrett.prototype.reduce = function (x) {\n    x.drShiftTo(this.m.t - 1, this.r2);\n    if (x.t > this.m.t + 1) {\n      x.t = this.m.t + 1;\n      x.clamp();\n    }\n    this.mu.multiplyUpperTo(this.r2, this.m.t + 1, this.q3);\n    this.m.multiplyLowerTo(this.q3, this.m.t + 1, this.r2);\n    while (x.compareTo(this.r2) < 0) {\n      x.dAddOffset(1, this.m.t + 1);\n    }\n    x.subTo(this.r2, x);\n    while (x.compareTo(this.m) >= 0) {\n      x.subTo(this.m, x);\n    }\n  };\n  // Barrett.prototype.mulTo = barrettMulTo;\n  // r = x*y mod m; x,y != r\n  Barrett.prototype.mulTo = function (x, y, r) {\n    x.multiplyTo(y, r);\n    this.reduce(r);\n  };\n  // Barrett.prototype.sqrTo = barrettSqrTo;\n  // r = x^2 mod m; x != r\n  Barrett.prototype.sqrTo = function (x, r) {\n    x.squareTo(r);\n    this.reduce(r);\n  };\n  return Barrett;\n}();\n//#endregion\n//#endregion REDUCERS\n// return new, unset BigInteger\nexport function nbi() {\n  return new BigInteger(null);\n}\nexport function parseBigInt(str, r) {\n  return new BigInteger(str, r);\n}\n// am: Compute w_j += (x*this_i), propagate carries,\n// c is initial carry, returns final carry.\n// c < 3*dvalue, x < 2*dvalue, this_i < dvalue\n// We need to select the fastest one that works in this environment.\nvar inBrowser = typeof navigator !== \"undefined\";\nif (inBrowser && j_lm && navigator.appName == \"Microsoft Internet Explorer\") {\n  // am2 avoids a big mult-and-extract completely.\n  // Max digit bits should be <= 30 because we do bitwise ops\n  // on values up to 2*hdvalue^2-hdvalue-1 (< 2^31)\n  BigInteger.prototype.am = function am2(i, x, w, j, c, n) {\n    var xl = x & 0x7fff;\n    var xh = x >> 15;\n    while (--n >= 0) {\n      var l = this[i] & 0x7fff;\n      var h = this[i++] >> 15;\n      var m = xh * l + h * xl;\n      l = xl * l + ((m & 0x7fff) << 15) + w[j] + (c & 0x3fffffff);\n      c = (l >>> 30) + (m >>> 15) + xh * h + (c >>> 30);\n      w[j++] = l & 0x3fffffff;\n    }\n    return c;\n  };\n  dbits = 30;\n} else if (inBrowser && j_lm && navigator.appName != \"Netscape\") {\n  // am1: use a single mult and divide to get the high bits,\n  // max digit bits should be 26 because\n  // max internal value = 2*dvalue^2-2*dvalue (< 2^53)\n  BigInteger.prototype.am = function am1(i, x, w, j, c, n) {\n    while (--n >= 0) {\n      var v = x * this[i++] + w[j] + c;\n      c = Math.floor(v / 0x4000000);\n      w[j++] = v & 0x3ffffff;\n    }\n    return c;\n  };\n  dbits = 26;\n} else {\n  // Mozilla/Netscape seems to prefer am3\n  // Alternately, set max digit bits to 28 since some\n  // browsers slow down when dealing with 32-bit numbers.\n  BigInteger.prototype.am = function am3(i, x, w, j, c, n) {\n    var xl = x & 0x3fff;\n    var xh = x >> 14;\n    while (--n >= 0) {\n      var l = this[i] & 0x3fff;\n      var h = this[i++] >> 14;\n      var m = xh * l + h * xl;\n      l = xl * l + ((m & 0x3fff) << 14) + w[j] + c;\n      c = (l >> 28) + (m >> 14) + xh * h;\n      w[j++] = l & 0xfffffff;\n    }\n    return c;\n  };\n  dbits = 28;\n}\nBigInteger.prototype.DB = dbits;\nBigInteger.prototype.DM = (1 << dbits) - 1;\nBigInteger.prototype.DV = 1 << dbits;\nvar BI_FP = 52;\nBigInteger.prototype.FV = Math.pow(2, BI_FP);\nBigInteger.prototype.F1 = BI_FP - dbits;\nBigInteger.prototype.F2 = 2 * dbits - BI_FP;\n// Digit conversions\nvar BI_RC = [];\nvar rr;\nvar vv;\nrr = \"0\".charCodeAt(0);\nfor (vv = 0; vv <= 9; ++vv) {\n  BI_RC[rr++] = vv;\n}\nrr = \"a\".charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv) {\n  BI_RC[rr++] = vv;\n}\nrr = \"A\".charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv) {\n  BI_RC[rr++] = vv;\n}\nexport function intAt(s, i) {\n  var c = BI_RC[s.charCodeAt(i)];\n  return c == null ? -1 : c;\n}\n// return bigint initialized to value\nexport function nbv(i) {\n  var r = nbi();\n  r.fromInt(i);\n  return r;\n}\n// returns bit length of the integer x\nexport function nbits(x) {\n  var r = 1;\n  var t;\n  if ((t = x >>> 16) != 0) {\n    x = t;\n    r += 16;\n  }\n  if ((t = x >> 8) != 0) {\n    x = t;\n    r += 8;\n  }\n  if ((t = x >> 4) != 0) {\n    x = t;\n    r += 4;\n  }\n  if ((t = x >> 2) != 0) {\n    x = t;\n    r += 2;\n  }\n  if ((t = x >> 1) != 0) {\n    x = t;\n    r += 1;\n  }\n  return r;\n}\n// \"constants\"\nBigInteger.ZERO = nbv(0);\nBigInteger.ONE = nbv(1);", "map": {"version": 3, "names": ["cbit", "int2char", "lbit", "op_and", "op_andnot", "op_or", "op_xor", "dbits", "canary", "j_lm", "lowprimes", "lplim", "length", "BigInteger", "a", "b", "c", "fromNumber", "fromString", "prototype", "toString", "s", "negate", "k", "toRadix", "km", "d", "m", "r", "i", "t", "p", "DB", "nbi", "ZERO", "subTo", "abs", "compareTo", "bitLength", "nbits", "DM", "mod", "divRemTo", "modPowInt", "e", "z", "isEven", "Classic", "<PERSON>", "exp", "clone", "copyTo", "intValue", "DV", "byteValue", "shortValue", "signum", "toByteArray", "equals", "min", "max", "and", "bitwiseTo", "or", "xor", "andNot", "not", "shiftLeft", "n", "rShiftTo", "lShiftTo", "shiftRight", "getLowestSetBit", "bitCount", "x", "testBit", "j", "Math", "floor", "setBit", "changeBit", "clearBit", "flipBit", "add", "addTo", "subtract", "multiply", "multiplyTo", "divide", "remainder", "divideAndRemainder", "q", "modPow", "nbv", "<PERSON>", "g", "k1", "convert", "g2", "sqrTo", "mulTo", "w", "is1", "r2", "revert", "modInverse", "ac", "u", "v", "ONE", "pow", "NullExp", "gcd", "y", "isProbablePrime", "modInt", "millerRabin", "fromInt", "fromRadix", "mi", "sh", "intAt", "char<PERSON>t", "clamp", "dlShiftTo", "drShiftTo", "bs", "cbs", "bm", "ds", "am", "squareTo", "pm", "pt", "ts", "ms", "nsh", "ys", "y0", "yt", "F1", "F2", "d1", "FV", "d2", "qd", "invDigit", "chunkSize", "LN2", "log", "cs", "substr", "d<PERSON><PERSON><PERSON><PERSON>", "dAddOffset", "nextBytes", "op", "f", "multiplyLowerTo", "multiplyUpperTo", "n1", "random", "square", "gcda", "callback", "gcda1", "setTimeout", "fromNumberAsync", "bnp_1", "bnpfn1_1", "reduce", "mp", "mpl", "mph", "um", "mt2", "u0", "q3", "mu", "parseBigInt", "str", "inBrowser", "navigator", "appName", "am2", "xl", "xh", "l", "h", "am1", "am3", "BI_FP", "BI_RC", "rr", "vv", "charCodeAt"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/jsencrypt/lib/lib/jsbn/jsbn.js"], "sourcesContent": ["// Copyright (c) 2005  <PERSON> Wu\n// All Rights Reserved.\n// See \"LICENSE\" for details.\n// Basic JavaScript BN library - subset useful for RSA encryption.\nimport { cbit, int2char, lbit, op_and, op_andnot, op_or, op_xor } from \"./util\";\n// Bits per digit\nvar dbits;\n// JavaScript engine analysis\nvar canary = 0xdeadbeefcafe;\nvar j_lm = ((canary & 0xffffff) == 0xefcafe);\n//#region\nvar lowprimes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269, 271, 277, 281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379, 383, 389, 397, 401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521, 523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607, 613, 617, 619, 631, 641, 643, 647, 653, 659, 661, 673, 677, 683, 691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773, 787, 797, 809, 811, 821, 823, 827, 829, 839, 853, 857, 859, 863, 877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967, 971, 977, 983, 991, 997];\nvar lplim = (1 << 26) / lowprimes[lowprimes.length - 1];\n//#endregion\n// (public) Constructor\nvar BigInteger = /** @class */ (function () {\n    function BigInteger(a, b, c) {\n        if (a != null) {\n            if (\"number\" == typeof a) {\n                this.fromNumber(a, b, c);\n            }\n            else if (b == null && \"string\" != typeof a) {\n                this.fromString(a, 256);\n            }\n            else {\n                this.fromString(a, b);\n            }\n        }\n    }\n    //#region PUBLIC\n    // BigInteger.prototype.toString = bnToString;\n    // (public) return string representation in given radix\n    BigInteger.prototype.toString = function (b) {\n        if (this.s < 0) {\n            return \"-\" + this.negate().toString(b);\n        }\n        var k;\n        if (b == 16) {\n            k = 4;\n        }\n        else if (b == 8) {\n            k = 3;\n        }\n        else if (b == 2) {\n            k = 1;\n        }\n        else if (b == 32) {\n            k = 5;\n        }\n        else if (b == 4) {\n            k = 2;\n        }\n        else {\n            return this.toRadix(b);\n        }\n        var km = (1 << k) - 1;\n        var d;\n        var m = false;\n        var r = \"\";\n        var i = this.t;\n        var p = this.DB - (i * this.DB) % k;\n        if (i-- > 0) {\n            if (p < this.DB && (d = this[i] >> p) > 0) {\n                m = true;\n                r = int2char(d);\n            }\n            while (i >= 0) {\n                if (p < k) {\n                    d = (this[i] & ((1 << p) - 1)) << (k - p);\n                    d |= this[--i] >> (p += this.DB - k);\n                }\n                else {\n                    d = (this[i] >> (p -= k)) & km;\n                    if (p <= 0) {\n                        p += this.DB;\n                        --i;\n                    }\n                }\n                if (d > 0) {\n                    m = true;\n                }\n                if (m) {\n                    r += int2char(d);\n                }\n            }\n        }\n        return m ? r : \"0\";\n    };\n    // BigInteger.prototype.negate = bnNegate;\n    // (public) -this\n    BigInteger.prototype.negate = function () {\n        var r = nbi();\n        BigInteger.ZERO.subTo(this, r);\n        return r;\n    };\n    // BigInteger.prototype.abs = bnAbs;\n    // (public) |this|\n    BigInteger.prototype.abs = function () {\n        return (this.s < 0) ? this.negate() : this;\n    };\n    // BigInteger.prototype.compareTo = bnCompareTo;\n    // (public) return + if this > a, - if this < a, 0 if equal\n    BigInteger.prototype.compareTo = function (a) {\n        var r = this.s - a.s;\n        if (r != 0) {\n            return r;\n        }\n        var i = this.t;\n        r = i - a.t;\n        if (r != 0) {\n            return (this.s < 0) ? -r : r;\n        }\n        while (--i >= 0) {\n            if ((r = this[i] - a[i]) != 0) {\n                return r;\n            }\n        }\n        return 0;\n    };\n    // BigInteger.prototype.bitLength = bnBitLength;\n    // (public) return the number of bits in \"this\"\n    BigInteger.prototype.bitLength = function () {\n        if (this.t <= 0) {\n            return 0;\n        }\n        return this.DB * (this.t - 1) + nbits(this[this.t - 1] ^ (this.s & this.DM));\n    };\n    // BigInteger.prototype.mod = bnMod;\n    // (public) this mod a\n    BigInteger.prototype.mod = function (a) {\n        var r = nbi();\n        this.abs().divRemTo(a, null, r);\n        if (this.s < 0 && r.compareTo(BigInteger.ZERO) > 0) {\n            a.subTo(r, r);\n        }\n        return r;\n    };\n    // BigInteger.prototype.modPowInt = bnModPowInt;\n    // (public) this^e % m, 0 <= e < 2^32\n    BigInteger.prototype.modPowInt = function (e, m) {\n        var z;\n        if (e < 256 || m.isEven()) {\n            z = new Classic(m);\n        }\n        else {\n            z = new Montgomery(m);\n        }\n        return this.exp(e, z);\n    };\n    // BigInteger.prototype.clone = bnClone;\n    // (public)\n    BigInteger.prototype.clone = function () {\n        var r = nbi();\n        this.copyTo(r);\n        return r;\n    };\n    // BigInteger.prototype.intValue = bnIntValue;\n    // (public) return value as integer\n    BigInteger.prototype.intValue = function () {\n        if (this.s < 0) {\n            if (this.t == 1) {\n                return this[0] - this.DV;\n            }\n            else if (this.t == 0) {\n                return -1;\n            }\n        }\n        else if (this.t == 1) {\n            return this[0];\n        }\n        else if (this.t == 0) {\n            return 0;\n        }\n        // assumes 16 < DB < 32\n        return ((this[1] & ((1 << (32 - this.DB)) - 1)) << this.DB) | this[0];\n    };\n    // BigInteger.prototype.byteValue = bnByteValue;\n    // (public) return value as byte\n    BigInteger.prototype.byteValue = function () {\n        return (this.t == 0) ? this.s : (this[0] << 24) >> 24;\n    };\n    // BigInteger.prototype.shortValue = bnShortValue;\n    // (public) return value as short (assumes DB>=16)\n    BigInteger.prototype.shortValue = function () {\n        return (this.t == 0) ? this.s : (this[0] << 16) >> 16;\n    };\n    // BigInteger.prototype.signum = bnSigNum;\n    // (public) 0 if this == 0, 1 if this > 0\n    BigInteger.prototype.signum = function () {\n        if (this.s < 0) {\n            return -1;\n        }\n        else if (this.t <= 0 || (this.t == 1 && this[0] <= 0)) {\n            return 0;\n        }\n        else {\n            return 1;\n        }\n    };\n    // BigInteger.prototype.toByteArray = bnToByteArray;\n    // (public) convert to bigendian byte array\n    BigInteger.prototype.toByteArray = function () {\n        var i = this.t;\n        var r = [];\n        r[0] = this.s;\n        var p = this.DB - (i * this.DB) % 8;\n        var d;\n        var k = 0;\n        if (i-- > 0) {\n            if (p < this.DB && (d = this[i] >> p) != (this.s & this.DM) >> p) {\n                r[k++] = d | (this.s << (this.DB - p));\n            }\n            while (i >= 0) {\n                if (p < 8) {\n                    d = (this[i] & ((1 << p) - 1)) << (8 - p);\n                    d |= this[--i] >> (p += this.DB - 8);\n                }\n                else {\n                    d = (this[i] >> (p -= 8)) & 0xff;\n                    if (p <= 0) {\n                        p += this.DB;\n                        --i;\n                    }\n                }\n                if ((d & 0x80) != 0) {\n                    d |= -256;\n                }\n                if (k == 0 && (this.s & 0x80) != (d & 0x80)) {\n                    ++k;\n                }\n                if (k > 0 || d != this.s) {\n                    r[k++] = d;\n                }\n            }\n        }\n        return r;\n    };\n    // BigInteger.prototype.equals = bnEquals;\n    BigInteger.prototype.equals = function (a) {\n        return (this.compareTo(a) == 0);\n    };\n    // BigInteger.prototype.min = bnMin;\n    BigInteger.prototype.min = function (a) {\n        return (this.compareTo(a) < 0) ? this : a;\n    };\n    // BigInteger.prototype.max = bnMax;\n    BigInteger.prototype.max = function (a) {\n        return (this.compareTo(a) > 0) ? this : a;\n    };\n    // BigInteger.prototype.and = bnAnd;\n    BigInteger.prototype.and = function (a) {\n        var r = nbi();\n        this.bitwiseTo(a, op_and, r);\n        return r;\n    };\n    // BigInteger.prototype.or = bnOr;\n    BigInteger.prototype.or = function (a) {\n        var r = nbi();\n        this.bitwiseTo(a, op_or, r);\n        return r;\n    };\n    // BigInteger.prototype.xor = bnXor;\n    BigInteger.prototype.xor = function (a) {\n        var r = nbi();\n        this.bitwiseTo(a, op_xor, r);\n        return r;\n    };\n    // BigInteger.prototype.andNot = bnAndNot;\n    BigInteger.prototype.andNot = function (a) {\n        var r = nbi();\n        this.bitwiseTo(a, op_andnot, r);\n        return r;\n    };\n    // BigInteger.prototype.not = bnNot;\n    // (public) ~this\n    BigInteger.prototype.not = function () {\n        var r = nbi();\n        for (var i = 0; i < this.t; ++i) {\n            r[i] = this.DM & ~this[i];\n        }\n        r.t = this.t;\n        r.s = ~this.s;\n        return r;\n    };\n    // BigInteger.prototype.shiftLeft = bnShiftLeft;\n    // (public) this << n\n    BigInteger.prototype.shiftLeft = function (n) {\n        var r = nbi();\n        if (n < 0) {\n            this.rShiftTo(-n, r);\n        }\n        else {\n            this.lShiftTo(n, r);\n        }\n        return r;\n    };\n    // BigInteger.prototype.shiftRight = bnShiftRight;\n    // (public) this >> n\n    BigInteger.prototype.shiftRight = function (n) {\n        var r = nbi();\n        if (n < 0) {\n            this.lShiftTo(-n, r);\n        }\n        else {\n            this.rShiftTo(n, r);\n        }\n        return r;\n    };\n    // BigInteger.prototype.getLowestSetBit = bnGetLowestSetBit;\n    // (public) returns index of lowest 1-bit (or -1 if none)\n    BigInteger.prototype.getLowestSetBit = function () {\n        for (var i = 0; i < this.t; ++i) {\n            if (this[i] != 0) {\n                return i * this.DB + lbit(this[i]);\n            }\n        }\n        if (this.s < 0) {\n            return this.t * this.DB;\n        }\n        return -1;\n    };\n    // BigInteger.prototype.bitCount = bnBitCount;\n    // (public) return number of set bits\n    BigInteger.prototype.bitCount = function () {\n        var r = 0;\n        var x = this.s & this.DM;\n        for (var i = 0; i < this.t; ++i) {\n            r += cbit(this[i] ^ x);\n        }\n        return r;\n    };\n    // BigInteger.prototype.testBit = bnTestBit;\n    // (public) true iff nth bit is set\n    BigInteger.prototype.testBit = function (n) {\n        var j = Math.floor(n / this.DB);\n        if (j >= this.t) {\n            return (this.s != 0);\n        }\n        return ((this[j] & (1 << (n % this.DB))) != 0);\n    };\n    // BigInteger.prototype.setBit = bnSetBit;\n    // (public) this | (1<<n)\n    BigInteger.prototype.setBit = function (n) {\n        return this.changeBit(n, op_or);\n    };\n    // BigInteger.prototype.clearBit = bnClearBit;\n    // (public) this & ~(1<<n)\n    BigInteger.prototype.clearBit = function (n) {\n        return this.changeBit(n, op_andnot);\n    };\n    // BigInteger.prototype.flipBit = bnFlipBit;\n    // (public) this ^ (1<<n)\n    BigInteger.prototype.flipBit = function (n) {\n        return this.changeBit(n, op_xor);\n    };\n    // BigInteger.prototype.add = bnAdd;\n    // (public) this + a\n    BigInteger.prototype.add = function (a) {\n        var r = nbi();\n        this.addTo(a, r);\n        return r;\n    };\n    // BigInteger.prototype.subtract = bnSubtract;\n    // (public) this - a\n    BigInteger.prototype.subtract = function (a) {\n        var r = nbi();\n        this.subTo(a, r);\n        return r;\n    };\n    // BigInteger.prototype.multiply = bnMultiply;\n    // (public) this * a\n    BigInteger.prototype.multiply = function (a) {\n        var r = nbi();\n        this.multiplyTo(a, r);\n        return r;\n    };\n    // BigInteger.prototype.divide = bnDivide;\n    // (public) this / a\n    BigInteger.prototype.divide = function (a) {\n        var r = nbi();\n        this.divRemTo(a, r, null);\n        return r;\n    };\n    // BigInteger.prototype.remainder = bnRemainder;\n    // (public) this % a\n    BigInteger.prototype.remainder = function (a) {\n        var r = nbi();\n        this.divRemTo(a, null, r);\n        return r;\n    };\n    // BigInteger.prototype.divideAndRemainder = bnDivideAndRemainder;\n    // (public) [this/a,this%a]\n    BigInteger.prototype.divideAndRemainder = function (a) {\n        var q = nbi();\n        var r = nbi();\n        this.divRemTo(a, q, r);\n        return [q, r];\n    };\n    // BigInteger.prototype.modPow = bnModPow;\n    // (public) this^e % m (HAC 14.85)\n    BigInteger.prototype.modPow = function (e, m) {\n        var i = e.bitLength();\n        var k;\n        var r = nbv(1);\n        var z;\n        if (i <= 0) {\n            return r;\n        }\n        else if (i < 18) {\n            k = 1;\n        }\n        else if (i < 48) {\n            k = 3;\n        }\n        else if (i < 144) {\n            k = 4;\n        }\n        else if (i < 768) {\n            k = 5;\n        }\n        else {\n            k = 6;\n        }\n        if (i < 8) {\n            z = new Classic(m);\n        }\n        else if (m.isEven()) {\n            z = new Barrett(m);\n        }\n        else {\n            z = new Montgomery(m);\n        }\n        // precomputation\n        var g = [];\n        var n = 3;\n        var k1 = k - 1;\n        var km = (1 << k) - 1;\n        g[1] = z.convert(this);\n        if (k > 1) {\n            var g2 = nbi();\n            z.sqrTo(g[1], g2);\n            while (n <= km) {\n                g[n] = nbi();\n                z.mulTo(g2, g[n - 2], g[n]);\n                n += 2;\n            }\n        }\n        var j = e.t - 1;\n        var w;\n        var is1 = true;\n        var r2 = nbi();\n        var t;\n        i = nbits(e[j]) - 1;\n        while (j >= 0) {\n            if (i >= k1) {\n                w = (e[j] >> (i - k1)) & km;\n            }\n            else {\n                w = (e[j] & ((1 << (i + 1)) - 1)) << (k1 - i);\n                if (j > 0) {\n                    w |= e[j - 1] >> (this.DB + i - k1);\n                }\n            }\n            n = k;\n            while ((w & 1) == 0) {\n                w >>= 1;\n                --n;\n            }\n            if ((i -= n) < 0) {\n                i += this.DB;\n                --j;\n            }\n            if (is1) { // ret == 1, don't bother squaring or multiplying it\n                g[w].copyTo(r);\n                is1 = false;\n            }\n            else {\n                while (n > 1) {\n                    z.sqrTo(r, r2);\n                    z.sqrTo(r2, r);\n                    n -= 2;\n                }\n                if (n > 0) {\n                    z.sqrTo(r, r2);\n                }\n                else {\n                    t = r;\n                    r = r2;\n                    r2 = t;\n                }\n                z.mulTo(r2, g[w], r);\n            }\n            while (j >= 0 && (e[j] & (1 << i)) == 0) {\n                z.sqrTo(r, r2);\n                t = r;\n                r = r2;\n                r2 = t;\n                if (--i < 0) {\n                    i = this.DB - 1;\n                    --j;\n                }\n            }\n        }\n        return z.revert(r);\n    };\n    // BigInteger.prototype.modInverse = bnModInverse;\n    // (public) 1/this % m (HAC 14.61)\n    BigInteger.prototype.modInverse = function (m) {\n        var ac = m.isEven();\n        if ((this.isEven() && ac) || m.signum() == 0) {\n            return BigInteger.ZERO;\n        }\n        var u = m.clone();\n        var v = this.clone();\n        var a = nbv(1);\n        var b = nbv(0);\n        var c = nbv(0);\n        var d = nbv(1);\n        while (u.signum() != 0) {\n            while (u.isEven()) {\n                u.rShiftTo(1, u);\n                if (ac) {\n                    if (!a.isEven() || !b.isEven()) {\n                        a.addTo(this, a);\n                        b.subTo(m, b);\n                    }\n                    a.rShiftTo(1, a);\n                }\n                else if (!b.isEven()) {\n                    b.subTo(m, b);\n                }\n                b.rShiftTo(1, b);\n            }\n            while (v.isEven()) {\n                v.rShiftTo(1, v);\n                if (ac) {\n                    if (!c.isEven() || !d.isEven()) {\n                        c.addTo(this, c);\n                        d.subTo(m, d);\n                    }\n                    c.rShiftTo(1, c);\n                }\n                else if (!d.isEven()) {\n                    d.subTo(m, d);\n                }\n                d.rShiftTo(1, d);\n            }\n            if (u.compareTo(v) >= 0) {\n                u.subTo(v, u);\n                if (ac) {\n                    a.subTo(c, a);\n                }\n                b.subTo(d, b);\n            }\n            else {\n                v.subTo(u, v);\n                if (ac) {\n                    c.subTo(a, c);\n                }\n                d.subTo(b, d);\n            }\n        }\n        if (v.compareTo(BigInteger.ONE) != 0) {\n            return BigInteger.ZERO;\n        }\n        if (d.compareTo(m) >= 0) {\n            return d.subtract(m);\n        }\n        if (d.signum() < 0) {\n            d.addTo(m, d);\n        }\n        else {\n            return d;\n        }\n        if (d.signum() < 0) {\n            return d.add(m);\n        }\n        else {\n            return d;\n        }\n    };\n    // BigInteger.prototype.pow = bnPow;\n    // (public) this^e\n    BigInteger.prototype.pow = function (e) {\n        return this.exp(e, new NullExp());\n    };\n    // BigInteger.prototype.gcd = bnGCD;\n    // (public) gcd(this,a) (HAC 14.54)\n    BigInteger.prototype.gcd = function (a) {\n        var x = (this.s < 0) ? this.negate() : this.clone();\n        var y = (a.s < 0) ? a.negate() : a.clone();\n        if (x.compareTo(y) < 0) {\n            var t = x;\n            x = y;\n            y = t;\n        }\n        var i = x.getLowestSetBit();\n        var g = y.getLowestSetBit();\n        if (g < 0) {\n            return x;\n        }\n        if (i < g) {\n            g = i;\n        }\n        if (g > 0) {\n            x.rShiftTo(g, x);\n            y.rShiftTo(g, y);\n        }\n        while (x.signum() > 0) {\n            if ((i = x.getLowestSetBit()) > 0) {\n                x.rShiftTo(i, x);\n            }\n            if ((i = y.getLowestSetBit()) > 0) {\n                y.rShiftTo(i, y);\n            }\n            if (x.compareTo(y) >= 0) {\n                x.subTo(y, x);\n                x.rShiftTo(1, x);\n            }\n            else {\n                y.subTo(x, y);\n                y.rShiftTo(1, y);\n            }\n        }\n        if (g > 0) {\n            y.lShiftTo(g, y);\n        }\n        return y;\n    };\n    // BigInteger.prototype.isProbablePrime = bnIsProbablePrime;\n    // (public) test primality with certainty >= 1-.5^t\n    BigInteger.prototype.isProbablePrime = function (t) {\n        var i;\n        var x = this.abs();\n        if (x.t == 1 && x[0] <= lowprimes[lowprimes.length - 1]) {\n            for (i = 0; i < lowprimes.length; ++i) {\n                if (x[0] == lowprimes[i]) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        if (x.isEven()) {\n            return false;\n        }\n        i = 1;\n        while (i < lowprimes.length) {\n            var m = lowprimes[i];\n            var j = i + 1;\n            while (j < lowprimes.length && m < lplim) {\n                m *= lowprimes[j++];\n            }\n            m = x.modInt(m);\n            while (i < j) {\n                if (m % lowprimes[i++] == 0) {\n                    return false;\n                }\n            }\n        }\n        return x.millerRabin(t);\n    };\n    //#endregion PUBLIC\n    //#region PROTECTED\n    // BigInteger.prototype.copyTo = bnpCopyTo;\n    // (protected) copy this to r\n    BigInteger.prototype.copyTo = function (r) {\n        for (var i = this.t - 1; i >= 0; --i) {\n            r[i] = this[i];\n        }\n        r.t = this.t;\n        r.s = this.s;\n    };\n    // BigInteger.prototype.fromInt = bnpFromInt;\n    // (protected) set from integer value x, -DV <= x < DV\n    BigInteger.prototype.fromInt = function (x) {\n        this.t = 1;\n        this.s = (x < 0) ? -1 : 0;\n        if (x > 0) {\n            this[0] = x;\n        }\n        else if (x < -1) {\n            this[0] = x + this.DV;\n        }\n        else {\n            this.t = 0;\n        }\n    };\n    // BigInteger.prototype.fromString = bnpFromString;\n    // (protected) set from string and radix\n    BigInteger.prototype.fromString = function (s, b) {\n        var k;\n        if (b == 16) {\n            k = 4;\n        }\n        else if (b == 8) {\n            k = 3;\n        }\n        else if (b == 256) {\n            k = 8;\n            /* byte array */\n        }\n        else if (b == 2) {\n            k = 1;\n        }\n        else if (b == 32) {\n            k = 5;\n        }\n        else if (b == 4) {\n            k = 2;\n        }\n        else {\n            this.fromRadix(s, b);\n            return;\n        }\n        this.t = 0;\n        this.s = 0;\n        var i = s.length;\n        var mi = false;\n        var sh = 0;\n        while (--i >= 0) {\n            var x = (k == 8) ? (+s[i]) & 0xff : intAt(s, i);\n            if (x < 0) {\n                if (s.charAt(i) == \"-\") {\n                    mi = true;\n                }\n                continue;\n            }\n            mi = false;\n            if (sh == 0) {\n                this[this.t++] = x;\n            }\n            else if (sh + k > this.DB) {\n                this[this.t - 1] |= (x & ((1 << (this.DB - sh)) - 1)) << sh;\n                this[this.t++] = (x >> (this.DB - sh));\n            }\n            else {\n                this[this.t - 1] |= x << sh;\n            }\n            sh += k;\n            if (sh >= this.DB) {\n                sh -= this.DB;\n            }\n        }\n        if (k == 8 && ((+s[0]) & 0x80) != 0) {\n            this.s = -1;\n            if (sh > 0) {\n                this[this.t - 1] |= ((1 << (this.DB - sh)) - 1) << sh;\n            }\n        }\n        this.clamp();\n        if (mi) {\n            BigInteger.ZERO.subTo(this, this);\n        }\n    };\n    // BigInteger.prototype.clamp = bnpClamp;\n    // (protected) clamp off excess high words\n    BigInteger.prototype.clamp = function () {\n        var c = this.s & this.DM;\n        while (this.t > 0 && this[this.t - 1] == c) {\n            --this.t;\n        }\n    };\n    // BigInteger.prototype.dlShiftTo = bnpDLShiftTo;\n    // (protected) r = this << n*DB\n    BigInteger.prototype.dlShiftTo = function (n, r) {\n        var i;\n        for (i = this.t - 1; i >= 0; --i) {\n            r[i + n] = this[i];\n        }\n        for (i = n - 1; i >= 0; --i) {\n            r[i] = 0;\n        }\n        r.t = this.t + n;\n        r.s = this.s;\n    };\n    // BigInteger.prototype.drShiftTo = bnpDRShiftTo;\n    // (protected) r = this >> n*DB\n    BigInteger.prototype.drShiftTo = function (n, r) {\n        for (var i = n; i < this.t; ++i) {\n            r[i - n] = this[i];\n        }\n        r.t = Math.max(this.t - n, 0);\n        r.s = this.s;\n    };\n    // BigInteger.prototype.lShiftTo = bnpLShiftTo;\n    // (protected) r = this << n\n    BigInteger.prototype.lShiftTo = function (n, r) {\n        var bs = n % this.DB;\n        var cbs = this.DB - bs;\n        var bm = (1 << cbs) - 1;\n        var ds = Math.floor(n / this.DB);\n        var c = (this.s << bs) & this.DM;\n        for (var i = this.t - 1; i >= 0; --i) {\n            r[i + ds + 1] = (this[i] >> cbs) | c;\n            c = (this[i] & bm) << bs;\n        }\n        for (var i = ds - 1; i >= 0; --i) {\n            r[i] = 0;\n        }\n        r[ds] = c;\n        r.t = this.t + ds + 1;\n        r.s = this.s;\n        r.clamp();\n    };\n    // BigInteger.prototype.rShiftTo = bnpRShiftTo;\n    // (protected) r = this >> n\n    BigInteger.prototype.rShiftTo = function (n, r) {\n        r.s = this.s;\n        var ds = Math.floor(n / this.DB);\n        if (ds >= this.t) {\n            r.t = 0;\n            return;\n        }\n        var bs = n % this.DB;\n        var cbs = this.DB - bs;\n        var bm = (1 << bs) - 1;\n        r[0] = this[ds] >> bs;\n        for (var i = ds + 1; i < this.t; ++i) {\n            r[i - ds - 1] |= (this[i] & bm) << cbs;\n            r[i - ds] = this[i] >> bs;\n        }\n        if (bs > 0) {\n            r[this.t - ds - 1] |= (this.s & bm) << cbs;\n        }\n        r.t = this.t - ds;\n        r.clamp();\n    };\n    // BigInteger.prototype.subTo = bnpSubTo;\n    // (protected) r = this - a\n    BigInteger.prototype.subTo = function (a, r) {\n        var i = 0;\n        var c = 0;\n        var m = Math.min(a.t, this.t);\n        while (i < m) {\n            c += this[i] - a[i];\n            r[i++] = c & this.DM;\n            c >>= this.DB;\n        }\n        if (a.t < this.t) {\n            c -= a.s;\n            while (i < this.t) {\n                c += this[i];\n                r[i++] = c & this.DM;\n                c >>= this.DB;\n            }\n            c += this.s;\n        }\n        else {\n            c += this.s;\n            while (i < a.t) {\n                c -= a[i];\n                r[i++] = c & this.DM;\n                c >>= this.DB;\n            }\n            c -= a.s;\n        }\n        r.s = (c < 0) ? -1 : 0;\n        if (c < -1) {\n            r[i++] = this.DV + c;\n        }\n        else if (c > 0) {\n            r[i++] = c;\n        }\n        r.t = i;\n        r.clamp();\n    };\n    // BigInteger.prototype.multiplyTo = bnpMultiplyTo;\n    // (protected) r = this * a, r != this,a (HAC 14.12)\n    // \"this\" should be the larger one if appropriate.\n    BigInteger.prototype.multiplyTo = function (a, r) {\n        var x = this.abs();\n        var y = a.abs();\n        var i = x.t;\n        r.t = i + y.t;\n        while (--i >= 0) {\n            r[i] = 0;\n        }\n        for (i = 0; i < y.t; ++i) {\n            r[i + x.t] = x.am(0, y[i], r, i, 0, x.t);\n        }\n        r.s = 0;\n        r.clamp();\n        if (this.s != a.s) {\n            BigInteger.ZERO.subTo(r, r);\n        }\n    };\n    // BigInteger.prototype.squareTo = bnpSquareTo;\n    // (protected) r = this^2, r != this (HAC 14.16)\n    BigInteger.prototype.squareTo = function (r) {\n        var x = this.abs();\n        var i = r.t = 2 * x.t;\n        while (--i >= 0) {\n            r[i] = 0;\n        }\n        for (i = 0; i < x.t - 1; ++i) {\n            var c = x.am(i, x[i], r, 2 * i, 0, 1);\n            if ((r[i + x.t] += x.am(i + 1, 2 * x[i], r, 2 * i + 1, c, x.t - i - 1)) >= x.DV) {\n                r[i + x.t] -= x.DV;\n                r[i + x.t + 1] = 1;\n            }\n        }\n        if (r.t > 0) {\n            r[r.t - 1] += x.am(i, x[i], r, 2 * i, 0, 1);\n        }\n        r.s = 0;\n        r.clamp();\n    };\n    // BigInteger.prototype.divRemTo = bnpDivRemTo;\n    // (protected) divide this by m, quotient and remainder to q, r (HAC 14.20)\n    // r != q, this != m.  q or r may be null.\n    BigInteger.prototype.divRemTo = function (m, q, r) {\n        var pm = m.abs();\n        if (pm.t <= 0) {\n            return;\n        }\n        var pt = this.abs();\n        if (pt.t < pm.t) {\n            if (q != null) {\n                q.fromInt(0);\n            }\n            if (r != null) {\n                this.copyTo(r);\n            }\n            return;\n        }\n        if (r == null) {\n            r = nbi();\n        }\n        var y = nbi();\n        var ts = this.s;\n        var ms = m.s;\n        var nsh = this.DB - nbits(pm[pm.t - 1]); // normalize modulus\n        if (nsh > 0) {\n            pm.lShiftTo(nsh, y);\n            pt.lShiftTo(nsh, r);\n        }\n        else {\n            pm.copyTo(y);\n            pt.copyTo(r);\n        }\n        var ys = y.t;\n        var y0 = y[ys - 1];\n        if (y0 == 0) {\n            return;\n        }\n        var yt = y0 * (1 << this.F1) + ((ys > 1) ? y[ys - 2] >> this.F2 : 0);\n        var d1 = this.FV / yt;\n        var d2 = (1 << this.F1) / yt;\n        var e = 1 << this.F2;\n        var i = r.t;\n        var j = i - ys;\n        var t = (q == null) ? nbi() : q;\n        y.dlShiftTo(j, t);\n        if (r.compareTo(t) >= 0) {\n            r[r.t++] = 1;\n            r.subTo(t, r);\n        }\n        BigInteger.ONE.dlShiftTo(ys, t);\n        t.subTo(y, y); // \"negative\" y so we can replace sub with am later\n        while (y.t < ys) {\n            y[y.t++] = 0;\n        }\n        while (--j >= 0) {\n            // Estimate quotient digit\n            var qd = (r[--i] == y0) ? this.DM : Math.floor(r[i] * d1 + (r[i - 1] + e) * d2);\n            if ((r[i] += y.am(0, qd, r, j, 0, ys)) < qd) { // Try it out\n                y.dlShiftTo(j, t);\n                r.subTo(t, r);\n                while (r[i] < --qd) {\n                    r.subTo(t, r);\n                }\n            }\n        }\n        if (q != null) {\n            r.drShiftTo(ys, q);\n            if (ts != ms) {\n                BigInteger.ZERO.subTo(q, q);\n            }\n        }\n        r.t = ys;\n        r.clamp();\n        if (nsh > 0) {\n            r.rShiftTo(nsh, r);\n        } // Denormalize remainder\n        if (ts < 0) {\n            BigInteger.ZERO.subTo(r, r);\n        }\n    };\n    // BigInteger.prototype.invDigit = bnpInvDigit;\n    // (protected) return \"-1/this % 2^DB\"; useful for Mont. reduction\n    // justification:\n    //         xy == 1 (mod m)\n    //         xy =  1+km\n    //   xy(2-xy) = (1+km)(1-km)\n    // x[y(2-xy)] = 1-k^2m^2\n    // x[y(2-xy)] == 1 (mod m^2)\n    // if y is 1/x mod m, then y(2-xy) is 1/x mod m^2\n    // should reduce x and y(2-xy) by m^2 at each step to keep size bounded.\n    // JS multiply \"overflows\" differently from C/C++, so care is needed here.\n    BigInteger.prototype.invDigit = function () {\n        if (this.t < 1) {\n            return 0;\n        }\n        var x = this[0];\n        if ((x & 1) == 0) {\n            return 0;\n        }\n        var y = x & 3; // y == 1/x mod 2^2\n        y = (y * (2 - (x & 0xf) * y)) & 0xf; // y == 1/x mod 2^4\n        y = (y * (2 - (x & 0xff) * y)) & 0xff; // y == 1/x mod 2^8\n        y = (y * (2 - (((x & 0xffff) * y) & 0xffff))) & 0xffff; // y == 1/x mod 2^16\n        // last step - calculate inverse mod DV directly;\n        // assumes 16 < DB <= 32 and assumes ability to handle 48-bit ints\n        y = (y * (2 - x * y % this.DV)) % this.DV; // y == 1/x mod 2^dbits\n        // we really want the negative inverse, and -DV < y < DV\n        return (y > 0) ? this.DV - y : -y;\n    };\n    // BigInteger.prototype.isEven = bnpIsEven;\n    // (protected) true iff this is even\n    BigInteger.prototype.isEven = function () {\n        return ((this.t > 0) ? (this[0] & 1) : this.s) == 0;\n    };\n    // BigInteger.prototype.exp = bnpExp;\n    // (protected) this^e, e < 2^32, doing sqr and mul with \"r\" (HAC 14.79)\n    BigInteger.prototype.exp = function (e, z) {\n        if (e > 0xffffffff || e < 1) {\n            return BigInteger.ONE;\n        }\n        var r = nbi();\n        var r2 = nbi();\n        var g = z.convert(this);\n        var i = nbits(e) - 1;\n        g.copyTo(r);\n        while (--i >= 0) {\n            z.sqrTo(r, r2);\n            if ((e & (1 << i)) > 0) {\n                z.mulTo(r2, g, r);\n            }\n            else {\n                var t = r;\n                r = r2;\n                r2 = t;\n            }\n        }\n        return z.revert(r);\n    };\n    // BigInteger.prototype.chunkSize = bnpChunkSize;\n    // (protected) return x s.t. r^x < DV\n    BigInteger.prototype.chunkSize = function (r) {\n        return Math.floor(Math.LN2 * this.DB / Math.log(r));\n    };\n    // BigInteger.prototype.toRadix = bnpToRadix;\n    // (protected) convert to radix string\n    BigInteger.prototype.toRadix = function (b) {\n        if (b == null) {\n            b = 10;\n        }\n        if (this.signum() == 0 || b < 2 || b > 36) {\n            return \"0\";\n        }\n        var cs = this.chunkSize(b);\n        var a = Math.pow(b, cs);\n        var d = nbv(a);\n        var y = nbi();\n        var z = nbi();\n        var r = \"\";\n        this.divRemTo(d, y, z);\n        while (y.signum() > 0) {\n            r = (a + z.intValue()).toString(b).substr(1) + r;\n            y.divRemTo(d, y, z);\n        }\n        return z.intValue().toString(b) + r;\n    };\n    // BigInteger.prototype.fromRadix = bnpFromRadix;\n    // (protected) convert from radix string\n    BigInteger.prototype.fromRadix = function (s, b) {\n        this.fromInt(0);\n        if (b == null) {\n            b = 10;\n        }\n        var cs = this.chunkSize(b);\n        var d = Math.pow(b, cs);\n        var mi = false;\n        var j = 0;\n        var w = 0;\n        for (var i = 0; i < s.length; ++i) {\n            var x = intAt(s, i);\n            if (x < 0) {\n                if (s.charAt(i) == \"-\" && this.signum() == 0) {\n                    mi = true;\n                }\n                continue;\n            }\n            w = b * w + x;\n            if (++j >= cs) {\n                this.dMultiply(d);\n                this.dAddOffset(w, 0);\n                j = 0;\n                w = 0;\n            }\n        }\n        if (j > 0) {\n            this.dMultiply(Math.pow(b, j));\n            this.dAddOffset(w, 0);\n        }\n        if (mi) {\n            BigInteger.ZERO.subTo(this, this);\n        }\n    };\n    // BigInteger.prototype.fromNumber = bnpFromNumber;\n    // (protected) alternate constructor\n    BigInteger.prototype.fromNumber = function (a, b, c) {\n        if (\"number\" == typeof b) {\n            // new BigInteger(int,int,RNG)\n            if (a < 2) {\n                this.fromInt(1);\n            }\n            else {\n                this.fromNumber(a, c);\n                if (!this.testBit(a - 1)) {\n                    // force MSB set\n                    this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), op_or, this);\n                }\n                if (this.isEven()) {\n                    this.dAddOffset(1, 0);\n                } // force odd\n                while (!this.isProbablePrime(b)) {\n                    this.dAddOffset(2, 0);\n                    if (this.bitLength() > a) {\n                        this.subTo(BigInteger.ONE.shiftLeft(a - 1), this);\n                    }\n                }\n            }\n        }\n        else {\n            // new BigInteger(int,RNG)\n            var x = [];\n            var t = a & 7;\n            x.length = (a >> 3) + 1;\n            b.nextBytes(x);\n            if (t > 0) {\n                x[0] &= ((1 << t) - 1);\n            }\n            else {\n                x[0] = 0;\n            }\n            this.fromString(x, 256);\n        }\n    };\n    // BigInteger.prototype.bitwiseTo = bnpBitwiseTo;\n    // (protected) r = this op a (bitwise)\n    BigInteger.prototype.bitwiseTo = function (a, op, r) {\n        var i;\n        var f;\n        var m = Math.min(a.t, this.t);\n        for (i = 0; i < m; ++i) {\n            r[i] = op(this[i], a[i]);\n        }\n        if (a.t < this.t) {\n            f = a.s & this.DM;\n            for (i = m; i < this.t; ++i) {\n                r[i] = op(this[i], f);\n            }\n            r.t = this.t;\n        }\n        else {\n            f = this.s & this.DM;\n            for (i = m; i < a.t; ++i) {\n                r[i] = op(f, a[i]);\n            }\n            r.t = a.t;\n        }\n        r.s = op(this.s, a.s);\n        r.clamp();\n    };\n    // BigInteger.prototype.changeBit = bnpChangeBit;\n    // (protected) this op (1<<n)\n    BigInteger.prototype.changeBit = function (n, op) {\n        var r = BigInteger.ONE.shiftLeft(n);\n        this.bitwiseTo(r, op, r);\n        return r;\n    };\n    // BigInteger.prototype.addTo = bnpAddTo;\n    // (protected) r = this + a\n    BigInteger.prototype.addTo = function (a, r) {\n        var i = 0;\n        var c = 0;\n        var m = Math.min(a.t, this.t);\n        while (i < m) {\n            c += this[i] + a[i];\n            r[i++] = c & this.DM;\n            c >>= this.DB;\n        }\n        if (a.t < this.t) {\n            c += a.s;\n            while (i < this.t) {\n                c += this[i];\n                r[i++] = c & this.DM;\n                c >>= this.DB;\n            }\n            c += this.s;\n        }\n        else {\n            c += this.s;\n            while (i < a.t) {\n                c += a[i];\n                r[i++] = c & this.DM;\n                c >>= this.DB;\n            }\n            c += a.s;\n        }\n        r.s = (c < 0) ? -1 : 0;\n        if (c > 0) {\n            r[i++] = c;\n        }\n        else if (c < -1) {\n            r[i++] = this.DV + c;\n        }\n        r.t = i;\n        r.clamp();\n    };\n    // BigInteger.prototype.dMultiply = bnpDMultiply;\n    // (protected) this *= n, this >= 0, 1 < n < DV\n    BigInteger.prototype.dMultiply = function (n) {\n        this[this.t] = this.am(0, n - 1, this, 0, 0, this.t);\n        ++this.t;\n        this.clamp();\n    };\n    // BigInteger.prototype.dAddOffset = bnpDAddOffset;\n    // (protected) this += n << w words, this >= 0\n    BigInteger.prototype.dAddOffset = function (n, w) {\n        if (n == 0) {\n            return;\n        }\n        while (this.t <= w) {\n            this[this.t++] = 0;\n        }\n        this[w] += n;\n        while (this[w] >= this.DV) {\n            this[w] -= this.DV;\n            if (++w >= this.t) {\n                this[this.t++] = 0;\n            }\n            ++this[w];\n        }\n    };\n    // BigInteger.prototype.multiplyLowerTo = bnpMultiplyLowerTo;\n    // (protected) r = lower n words of \"this * a\", a.t <= n\n    // \"this\" should be the larger one if appropriate.\n    BigInteger.prototype.multiplyLowerTo = function (a, n, r) {\n        var i = Math.min(this.t + a.t, n);\n        r.s = 0; // assumes a,this >= 0\n        r.t = i;\n        while (i > 0) {\n            r[--i] = 0;\n        }\n        for (var j = r.t - this.t; i < j; ++i) {\n            r[i + this.t] = this.am(0, a[i], r, i, 0, this.t);\n        }\n        for (var j = Math.min(a.t, n); i < j; ++i) {\n            this.am(0, a[i], r, i, 0, n - i);\n        }\n        r.clamp();\n    };\n    // BigInteger.prototype.multiplyUpperTo = bnpMultiplyUpperTo;\n    // (protected) r = \"this * a\" without lower n words, n > 0\n    // \"this\" should be the larger one if appropriate.\n    BigInteger.prototype.multiplyUpperTo = function (a, n, r) {\n        --n;\n        var i = r.t = this.t + a.t - n;\n        r.s = 0; // assumes a,this >= 0\n        while (--i >= 0) {\n            r[i] = 0;\n        }\n        for (i = Math.max(n - this.t, 0); i < a.t; ++i) {\n            r[this.t + i - n] = this.am(n - i, a[i], r, 0, 0, this.t + i - n);\n        }\n        r.clamp();\n        r.drShiftTo(1, r);\n    };\n    // BigInteger.prototype.modInt = bnpModInt;\n    // (protected) this % n, n < 2^26\n    BigInteger.prototype.modInt = function (n) {\n        if (n <= 0) {\n            return 0;\n        }\n        var d = this.DV % n;\n        var r = (this.s < 0) ? n - 1 : 0;\n        if (this.t > 0) {\n            if (d == 0) {\n                r = this[0] % n;\n            }\n            else {\n                for (var i = this.t - 1; i >= 0; --i) {\n                    r = (d * r + this[i]) % n;\n                }\n            }\n        }\n        return r;\n    };\n    // BigInteger.prototype.millerRabin = bnpMillerRabin;\n    // (protected) true if probably prime (HAC 4.24, Miller-Rabin)\n    BigInteger.prototype.millerRabin = function (t) {\n        var n1 = this.subtract(BigInteger.ONE);\n        var k = n1.getLowestSetBit();\n        if (k <= 0) {\n            return false;\n        }\n        var r = n1.shiftRight(k);\n        t = (t + 1) >> 1;\n        if (t > lowprimes.length) {\n            t = lowprimes.length;\n        }\n        var a = nbi();\n        for (var i = 0; i < t; ++i) {\n            // Pick bases at random, instead of starting at 2\n            a.fromInt(lowprimes[Math.floor(Math.random() * lowprimes.length)]);\n            var y = a.modPow(r, this);\n            if (y.compareTo(BigInteger.ONE) != 0 && y.compareTo(n1) != 0) {\n                var j = 1;\n                while (j++ < k && y.compareTo(n1) != 0) {\n                    y = y.modPowInt(2, this);\n                    if (y.compareTo(BigInteger.ONE) == 0) {\n                        return false;\n                    }\n                }\n                if (y.compareTo(n1) != 0) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    // BigInteger.prototype.square = bnSquare;\n    // (public) this^2\n    BigInteger.prototype.square = function () {\n        var r = nbi();\n        this.squareTo(r);\n        return r;\n    };\n    //#region ASYNC\n    // Public API method\n    BigInteger.prototype.gcda = function (a, callback) {\n        var x = (this.s < 0) ? this.negate() : this.clone();\n        var y = (a.s < 0) ? a.negate() : a.clone();\n        if (x.compareTo(y) < 0) {\n            var t = x;\n            x = y;\n            y = t;\n        }\n        var i = x.getLowestSetBit();\n        var g = y.getLowestSetBit();\n        if (g < 0) {\n            callback(x);\n            return;\n        }\n        if (i < g) {\n            g = i;\n        }\n        if (g > 0) {\n            x.rShiftTo(g, x);\n            y.rShiftTo(g, y);\n        }\n        // Workhorse of the algorithm, gets called 200 - 800 times per 512 bit keygen.\n        var gcda1 = function () {\n            if ((i = x.getLowestSetBit()) > 0) {\n                x.rShiftTo(i, x);\n            }\n            if ((i = y.getLowestSetBit()) > 0) {\n                y.rShiftTo(i, y);\n            }\n            if (x.compareTo(y) >= 0) {\n                x.subTo(y, x);\n                x.rShiftTo(1, x);\n            }\n            else {\n                y.subTo(x, y);\n                y.rShiftTo(1, y);\n            }\n            if (!(x.signum() > 0)) {\n                if (g > 0) {\n                    y.lShiftTo(g, y);\n                }\n                setTimeout(function () { callback(y); }, 0); // escape\n            }\n            else {\n                setTimeout(gcda1, 0);\n            }\n        };\n        setTimeout(gcda1, 10);\n    };\n    // (protected) alternate constructor\n    BigInteger.prototype.fromNumberAsync = function (a, b, c, callback) {\n        if (\"number\" == typeof b) {\n            if (a < 2) {\n                this.fromInt(1);\n            }\n            else {\n                this.fromNumber(a, c);\n                if (!this.testBit(a - 1)) {\n                    this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), op_or, this);\n                }\n                if (this.isEven()) {\n                    this.dAddOffset(1, 0);\n                }\n                var bnp_1 = this;\n                var bnpfn1_1 = function () {\n                    bnp_1.dAddOffset(2, 0);\n                    if (bnp_1.bitLength() > a) {\n                        bnp_1.subTo(BigInteger.ONE.shiftLeft(a - 1), bnp_1);\n                    }\n                    if (bnp_1.isProbablePrime(b)) {\n                        setTimeout(function () { callback(); }, 0); // escape\n                    }\n                    else {\n                        setTimeout(bnpfn1_1, 0);\n                    }\n                };\n                setTimeout(bnpfn1_1, 0);\n            }\n        }\n        else {\n            var x = [];\n            var t = a & 7;\n            x.length = (a >> 3) + 1;\n            b.nextBytes(x);\n            if (t > 0) {\n                x[0] &= ((1 << t) - 1);\n            }\n            else {\n                x[0] = 0;\n            }\n            this.fromString(x, 256);\n        }\n    };\n    return BigInteger;\n}());\nexport { BigInteger };\n//#region REDUCERS\n//#region NullExp\nvar NullExp = /** @class */ (function () {\n    function NullExp() {\n    }\n    // NullExp.prototype.convert = nNop;\n    NullExp.prototype.convert = function (x) {\n        return x;\n    };\n    // NullExp.prototype.revert = nNop;\n    NullExp.prototype.revert = function (x) {\n        return x;\n    };\n    // NullExp.prototype.mulTo = nMulTo;\n    NullExp.prototype.mulTo = function (x, y, r) {\n        x.multiplyTo(y, r);\n    };\n    // NullExp.prototype.sqrTo = nSqrTo;\n    NullExp.prototype.sqrTo = function (x, r) {\n        x.squareTo(r);\n    };\n    return NullExp;\n}());\n// Modular reduction using \"classic\" algorithm\nvar Classic = /** @class */ (function () {\n    function Classic(m) {\n        this.m = m;\n    }\n    // Classic.prototype.convert = cConvert;\n    Classic.prototype.convert = function (x) {\n        if (x.s < 0 || x.compareTo(this.m) >= 0) {\n            return x.mod(this.m);\n        }\n        else {\n            return x;\n        }\n    };\n    // Classic.prototype.revert = cRevert;\n    Classic.prototype.revert = function (x) {\n        return x;\n    };\n    // Classic.prototype.reduce = cReduce;\n    Classic.prototype.reduce = function (x) {\n        x.divRemTo(this.m, null, x);\n    };\n    // Classic.prototype.mulTo = cMulTo;\n    Classic.prototype.mulTo = function (x, y, r) {\n        x.multiplyTo(y, r);\n        this.reduce(r);\n    };\n    // Classic.prototype.sqrTo = cSqrTo;\n    Classic.prototype.sqrTo = function (x, r) {\n        x.squareTo(r);\n        this.reduce(r);\n    };\n    return Classic;\n}());\n//#endregion\n//#region Montgomery\n// Montgomery reduction\nvar Montgomery = /** @class */ (function () {\n    function Montgomery(m) {\n        this.m = m;\n        this.mp = m.invDigit();\n        this.mpl = this.mp & 0x7fff;\n        this.mph = this.mp >> 15;\n        this.um = (1 << (m.DB - 15)) - 1;\n        this.mt2 = 2 * m.t;\n    }\n    // Montgomery.prototype.convert = montConvert;\n    // xR mod m\n    Montgomery.prototype.convert = function (x) {\n        var r = nbi();\n        x.abs().dlShiftTo(this.m.t, r);\n        r.divRemTo(this.m, null, r);\n        if (x.s < 0 && r.compareTo(BigInteger.ZERO) > 0) {\n            this.m.subTo(r, r);\n        }\n        return r;\n    };\n    // Montgomery.prototype.revert = montRevert;\n    // x/R mod m\n    Montgomery.prototype.revert = function (x) {\n        var r = nbi();\n        x.copyTo(r);\n        this.reduce(r);\n        return r;\n    };\n    // Montgomery.prototype.reduce = montReduce;\n    // x = x/R mod m (HAC 14.32)\n    Montgomery.prototype.reduce = function (x) {\n        while (x.t <= this.mt2) {\n            // pad x so am has enough room later\n            x[x.t++] = 0;\n        }\n        for (var i = 0; i < this.m.t; ++i) {\n            // faster way of calculating u0 = x[i]*mp mod DV\n            var j = x[i] & 0x7fff;\n            var u0 = (j * this.mpl + (((j * this.mph + (x[i] >> 15) * this.mpl) & this.um) << 15)) & x.DM;\n            // use am to combine the multiply-shift-add into one call\n            j = i + this.m.t;\n            x[j] += this.m.am(0, u0, x, i, 0, this.m.t);\n            // propagate carry\n            while (x[j] >= x.DV) {\n                x[j] -= x.DV;\n                x[++j]++;\n            }\n        }\n        x.clamp();\n        x.drShiftTo(this.m.t, x);\n        if (x.compareTo(this.m) >= 0) {\n            x.subTo(this.m, x);\n        }\n    };\n    // Montgomery.prototype.mulTo = montMulTo;\n    // r = \"xy/R mod m\"; x,y != r\n    Montgomery.prototype.mulTo = function (x, y, r) {\n        x.multiplyTo(y, r);\n        this.reduce(r);\n    };\n    // Montgomery.prototype.sqrTo = montSqrTo;\n    // r = \"x^2/R mod m\"; x != r\n    Montgomery.prototype.sqrTo = function (x, r) {\n        x.squareTo(r);\n        this.reduce(r);\n    };\n    return Montgomery;\n}());\n//#endregion Montgomery\n//#region Barrett\n// Barrett modular reduction\nvar Barrett = /** @class */ (function () {\n    function Barrett(m) {\n        this.m = m;\n        // setup Barrett\n        this.r2 = nbi();\n        this.q3 = nbi();\n        BigInteger.ONE.dlShiftTo(2 * m.t, this.r2);\n        this.mu = this.r2.divide(m);\n    }\n    // Barrett.prototype.convert = barrettConvert;\n    Barrett.prototype.convert = function (x) {\n        if (x.s < 0 || x.t > 2 * this.m.t) {\n            return x.mod(this.m);\n        }\n        else if (x.compareTo(this.m) < 0) {\n            return x;\n        }\n        else {\n            var r = nbi();\n            x.copyTo(r);\n            this.reduce(r);\n            return r;\n        }\n    };\n    // Barrett.prototype.revert = barrettRevert;\n    Barrett.prototype.revert = function (x) {\n        return x;\n    };\n    // Barrett.prototype.reduce = barrettReduce;\n    // x = x mod m (HAC 14.42)\n    Barrett.prototype.reduce = function (x) {\n        x.drShiftTo(this.m.t - 1, this.r2);\n        if (x.t > this.m.t + 1) {\n            x.t = this.m.t + 1;\n            x.clamp();\n        }\n        this.mu.multiplyUpperTo(this.r2, this.m.t + 1, this.q3);\n        this.m.multiplyLowerTo(this.q3, this.m.t + 1, this.r2);\n        while (x.compareTo(this.r2) < 0) {\n            x.dAddOffset(1, this.m.t + 1);\n        }\n        x.subTo(this.r2, x);\n        while (x.compareTo(this.m) >= 0) {\n            x.subTo(this.m, x);\n        }\n    };\n    // Barrett.prototype.mulTo = barrettMulTo;\n    // r = x*y mod m; x,y != r\n    Barrett.prototype.mulTo = function (x, y, r) {\n        x.multiplyTo(y, r);\n        this.reduce(r);\n    };\n    // Barrett.prototype.sqrTo = barrettSqrTo;\n    // r = x^2 mod m; x != r\n    Barrett.prototype.sqrTo = function (x, r) {\n        x.squareTo(r);\n        this.reduce(r);\n    };\n    return Barrett;\n}());\n//#endregion\n//#endregion REDUCERS\n// return new, unset BigInteger\nexport function nbi() { return new BigInteger(null); }\nexport function parseBigInt(str, r) {\n    return new BigInteger(str, r);\n}\n// am: Compute w_j += (x*this_i), propagate carries,\n// c is initial carry, returns final carry.\n// c < 3*dvalue, x < 2*dvalue, this_i < dvalue\n// We need to select the fastest one that works in this environment.\nvar inBrowser = typeof navigator !== \"undefined\";\nif (inBrowser && j_lm && (navigator.appName == \"Microsoft Internet Explorer\")) {\n    // am2 avoids a big mult-and-extract completely.\n    // Max digit bits should be <= 30 because we do bitwise ops\n    // on values up to 2*hdvalue^2-hdvalue-1 (< 2^31)\n    BigInteger.prototype.am = function am2(i, x, w, j, c, n) {\n        var xl = x & 0x7fff;\n        var xh = x >> 15;\n        while (--n >= 0) {\n            var l = this[i] & 0x7fff;\n            var h = this[i++] >> 15;\n            var m = xh * l + h * xl;\n            l = xl * l + ((m & 0x7fff) << 15) + w[j] + (c & 0x3fffffff);\n            c = (l >>> 30) + (m >>> 15) + xh * h + (c >>> 30);\n            w[j++] = l & 0x3fffffff;\n        }\n        return c;\n    };\n    dbits = 30;\n}\nelse if (inBrowser && j_lm && (navigator.appName != \"Netscape\")) {\n    // am1: use a single mult and divide to get the high bits,\n    // max digit bits should be 26 because\n    // max internal value = 2*dvalue^2-2*dvalue (< 2^53)\n    BigInteger.prototype.am = function am1(i, x, w, j, c, n) {\n        while (--n >= 0) {\n            var v = x * this[i++] + w[j] + c;\n            c = Math.floor(v / 0x4000000);\n            w[j++] = v & 0x3ffffff;\n        }\n        return c;\n    };\n    dbits = 26;\n}\nelse { // Mozilla/Netscape seems to prefer am3\n    // Alternately, set max digit bits to 28 since some\n    // browsers slow down when dealing with 32-bit numbers.\n    BigInteger.prototype.am = function am3(i, x, w, j, c, n) {\n        var xl = x & 0x3fff;\n        var xh = x >> 14;\n        while (--n >= 0) {\n            var l = this[i] & 0x3fff;\n            var h = this[i++] >> 14;\n            var m = xh * l + h * xl;\n            l = xl * l + ((m & 0x3fff) << 14) + w[j] + c;\n            c = (l >> 28) + (m >> 14) + xh * h;\n            w[j++] = l & 0xfffffff;\n        }\n        return c;\n    };\n    dbits = 28;\n}\nBigInteger.prototype.DB = dbits;\nBigInteger.prototype.DM = ((1 << dbits) - 1);\nBigInteger.prototype.DV = (1 << dbits);\nvar BI_FP = 52;\nBigInteger.prototype.FV = Math.pow(2, BI_FP);\nBigInteger.prototype.F1 = BI_FP - dbits;\nBigInteger.prototype.F2 = 2 * dbits - BI_FP;\n// Digit conversions\nvar BI_RC = [];\nvar rr;\nvar vv;\nrr = \"0\".charCodeAt(0);\nfor (vv = 0; vv <= 9; ++vv) {\n    BI_RC[rr++] = vv;\n}\nrr = \"a\".charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv) {\n    BI_RC[rr++] = vv;\n}\nrr = \"A\".charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv) {\n    BI_RC[rr++] = vv;\n}\nexport function intAt(s, i) {\n    var c = BI_RC[s.charCodeAt(i)];\n    return (c == null) ? -1 : c;\n}\n// return bigint initialized to value\nexport function nbv(i) {\n    var r = nbi();\n    r.fromInt(i);\n    return r;\n}\n// returns bit length of the integer x\nexport function nbits(x) {\n    var r = 1;\n    var t;\n    if ((t = x >>> 16) != 0) {\n        x = t;\n        r += 16;\n    }\n    if ((t = x >> 8) != 0) {\n        x = t;\n        r += 8;\n    }\n    if ((t = x >> 4) != 0) {\n        x = t;\n        r += 4;\n    }\n    if ((t = x >> 2) != 0) {\n        x = t;\n        r += 2;\n    }\n    if ((t = x >> 1) != 0) {\n        x = t;\n        r += 1;\n    }\n    return r;\n}\n// \"constants\"\nBigInteger.ZERO = nbv(0);\nBigInteger.ONE = nbv(1);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,QAAQ,QAAQ;AAC/E;AACA,IAAIC,KAAK;AACT;AACA,IAAIC,MAAM,GAAG,cAAc;AAC3B,IAAIC,IAAI,GAAI,CAACD,MAAM,GAAG,QAAQ,KAAK,QAAS;AAC5C;AACA,IAAIE,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3zB,IAAIC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,IAAID,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC;AACvD;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACzB,IAAIF,CAAC,IAAI,IAAI,EAAE;MACX,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE;QACtB,IAAI,CAACG,UAAU,CAACH,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAC5B,CAAC,MACI,IAAID,CAAC,IAAI,IAAI,IAAI,QAAQ,IAAI,OAAOD,CAAC,EAAE;QACxC,IAAI,CAACI,UAAU,CAACJ,CAAC,EAAE,GAAG,CAAC;MAC3B,CAAC,MACI;QACD,IAAI,CAACI,UAAU,CAACJ,CAAC,EAAEC,CAAC,CAAC;MACzB;IACJ;EACJ;EACA;EACA;EACA;EACAF,UAAU,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUL,CAAC,EAAE;IACzC,IAAI,IAAI,CAACM,CAAC,GAAG,CAAC,EAAE;MACZ,OAAO,GAAG,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,CAACF,QAAQ,CAACL,CAAC,CAAC;IAC1C;IACA,IAAIQ,CAAC;IACL,IAAIR,CAAC,IAAI,EAAE,EAAE;MACTQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIR,CAAC,IAAI,CAAC,EAAE;MACbQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIR,CAAC,IAAI,CAAC,EAAE;MACbQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIR,CAAC,IAAI,EAAE,EAAE;MACdQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIR,CAAC,IAAI,CAAC,EAAE;MACbQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI;MACD,OAAO,IAAI,CAACC,OAAO,CAACT,CAAC,CAAC;IAC1B;IACA,IAAIU,EAAE,GAAG,CAAC,CAAC,IAAIF,CAAC,IAAI,CAAC;IACrB,IAAIG,CAAC;IACL,IAAIC,CAAC,GAAG,KAAK;IACb,IAAIC,CAAC,GAAG,EAAE;IACV,IAAIC,CAAC,GAAG,IAAI,CAACC,CAAC;IACd,IAAIC,CAAC,GAAG,IAAI,CAACC,EAAE,GAAIH,CAAC,GAAG,IAAI,CAACG,EAAE,GAAIT,CAAC;IACnC,IAAIM,CAAC,EAAE,GAAG,CAAC,EAAE;MACT,IAAIE,CAAC,GAAG,IAAI,CAACC,EAAE,IAAI,CAACN,CAAC,GAAG,IAAI,CAACG,CAAC,CAAC,IAAIE,CAAC,IAAI,CAAC,EAAE;QACvCJ,CAAC,GAAG,IAAI;QACRC,CAAC,GAAG3B,QAAQ,CAACyB,CAAC,CAAC;MACnB;MACA,OAAOG,CAAC,IAAI,CAAC,EAAE;QACX,IAAIE,CAAC,GAAGR,CAAC,EAAE;UACPG,CAAC,GAAG,CAAC,IAAI,CAACG,CAAC,CAAC,GAAI,CAAC,CAAC,IAAIE,CAAC,IAAI,CAAE,KAAMR,CAAC,GAAGQ,CAAE;UACzCL,CAAC,IAAI,IAAI,CAAC,EAAEG,CAAC,CAAC,KAAKE,CAAC,IAAI,IAAI,CAACC,EAAE,GAAGT,CAAC,CAAC;QACxC,CAAC,MACI;UACDG,CAAC,GAAI,IAAI,CAACG,CAAC,CAAC,KAAKE,CAAC,IAAIR,CAAC,CAAC,GAAIE,EAAE;UAC9B,IAAIM,CAAC,IAAI,CAAC,EAAE;YACRA,CAAC,IAAI,IAAI,CAACC,EAAE;YACZ,EAAEH,CAAC;UACP;QACJ;QACA,IAAIH,CAAC,GAAG,CAAC,EAAE;UACPC,CAAC,GAAG,IAAI;QACZ;QACA,IAAIA,CAAC,EAAE;UACHC,CAAC,IAAI3B,QAAQ,CAACyB,CAAC,CAAC;QACpB;MACJ;IACJ;IACA,OAAOC,CAAC,GAAGC,CAAC,GAAG,GAAG;EACtB,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACG,MAAM,GAAG,YAAY;IACtC,IAAIM,CAAC,GAAGK,GAAG,CAAC,CAAC;IACbpB,UAAU,CAACqB,IAAI,CAACC,KAAK,CAAC,IAAI,EAAEP,CAAC,CAAC;IAC9B,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACiB,GAAG,GAAG,YAAY;IACnC,OAAQ,IAAI,CAACf,CAAC,GAAG,CAAC,GAAI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI;EAC9C,CAAC;EACD;EACA;EACAT,UAAU,CAACM,SAAS,CAACkB,SAAS,GAAG,UAAUvB,CAAC,EAAE;IAC1C,IAAIc,CAAC,GAAG,IAAI,CAACP,CAAC,GAAGP,CAAC,CAACO,CAAC;IACpB,IAAIO,CAAC,IAAI,CAAC,EAAE;MACR,OAAOA,CAAC;IACZ;IACA,IAAIC,CAAC,GAAG,IAAI,CAACC,CAAC;IACdF,CAAC,GAAGC,CAAC,GAAGf,CAAC,CAACgB,CAAC;IACX,IAAIF,CAAC,IAAI,CAAC,EAAE;MACR,OAAQ,IAAI,CAACP,CAAC,GAAG,CAAC,GAAI,CAACO,CAAC,GAAGA,CAAC;IAChC;IACA,OAAO,EAAEC,CAAC,IAAI,CAAC,EAAE;MACb,IAAI,CAACD,CAAC,GAAG,IAAI,CAACC,CAAC,CAAC,GAAGf,CAAC,CAACe,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3B,OAAOD,CAAC;MACZ;IACJ;IACA,OAAO,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACmB,SAAS,GAAG,YAAY;IACzC,IAAI,IAAI,CAACR,CAAC,IAAI,CAAC,EAAE;MACb,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACE,EAAE,IAAI,IAAI,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGS,KAAK,CAAC,IAAI,CAAC,IAAI,CAACT,CAAC,GAAG,CAAC,CAAC,GAAI,IAAI,CAACT,CAAC,GAAG,IAAI,CAACmB,EAAG,CAAC;EAChF,CAAC;EACD;EACA;EACA3B,UAAU,CAACM,SAAS,CAACsB,GAAG,GAAG,UAAU3B,CAAC,EAAE;IACpC,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAACG,GAAG,CAAC,CAAC,CAACM,QAAQ,CAAC5B,CAAC,EAAE,IAAI,EAAEc,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACP,CAAC,GAAG,CAAC,IAAIO,CAAC,CAACS,SAAS,CAACxB,UAAU,CAACqB,IAAI,CAAC,GAAG,CAAC,EAAE;MAChDpB,CAAC,CAACqB,KAAK,CAACP,CAAC,EAAEA,CAAC,CAAC;IACjB;IACA,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACwB,SAAS,GAAG,UAAUC,CAAC,EAAEjB,CAAC,EAAE;IAC7C,IAAIkB,CAAC;IACL,IAAID,CAAC,GAAG,GAAG,IAAIjB,CAAC,CAACmB,MAAM,CAAC,CAAC,EAAE;MACvBD,CAAC,GAAG,IAAIE,OAAO,CAACpB,CAAC,CAAC;IACtB,CAAC,MACI;MACDkB,CAAC,GAAG,IAAIG,UAAU,CAACrB,CAAC,CAAC;IACzB;IACA,OAAO,IAAI,CAACsB,GAAG,CAACL,CAAC,EAAEC,CAAC,CAAC;EACzB,CAAC;EACD;EACA;EACAhC,UAAU,CAACM,SAAS,CAAC+B,KAAK,GAAG,YAAY;IACrC,IAAItB,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAACkB,MAAM,CAACvB,CAAC,CAAC;IACd,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACiC,QAAQ,GAAG,YAAY;IACxC,IAAI,IAAI,CAAC/B,CAAC,GAAG,CAAC,EAAE;MACZ,IAAI,IAAI,CAACS,CAAC,IAAI,CAAC,EAAE;QACb,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAACuB,EAAE;MAC5B,CAAC,MACI,IAAI,IAAI,CAACvB,CAAC,IAAI,CAAC,EAAE;QAClB,OAAO,CAAC,CAAC;MACb;IACJ,CAAC,MACI,IAAI,IAAI,CAACA,CAAC,IAAI,CAAC,EAAE;MAClB,OAAO,IAAI,CAAC,CAAC,CAAC;IAClB,CAAC,MACI,IAAI,IAAI,CAACA,CAAC,IAAI,CAAC,EAAE;MAClB,OAAO,CAAC;IACZ;IACA;IACA,OAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAI,CAAC,CAAC,IAAK,EAAE,GAAG,IAAI,CAACE,EAAG,IAAI,CAAE,KAAK,IAAI,CAACA,EAAE,GAAI,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC;EACD;EACA;EACAnB,UAAU,CAACM,SAAS,CAACmC,SAAS,GAAG,YAAY;IACzC,OAAQ,IAAI,CAACxB,CAAC,IAAI,CAAC,GAAI,IAAI,CAACT,CAAC,GAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAK,EAAE;EACzD,CAAC;EACD;EACA;EACAR,UAAU,CAACM,SAAS,CAACoC,UAAU,GAAG,YAAY;IAC1C,OAAQ,IAAI,CAACzB,CAAC,IAAI,CAAC,GAAI,IAAI,CAACT,CAAC,GAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAK,EAAE;EACzD,CAAC;EACD;EACA;EACAR,UAAU,CAACM,SAAS,CAACqC,MAAM,GAAG,YAAY;IACtC,IAAI,IAAI,CAACnC,CAAC,GAAG,CAAC,EAAE;MACZ,OAAO,CAAC,CAAC;IACb,CAAC,MACI,IAAI,IAAI,CAACS,CAAC,IAAI,CAAC,IAAK,IAAI,CAACA,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAE,EAAE;MACnD,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ,CAAC;EACD;EACA;EACAjB,UAAU,CAACM,SAAS,CAACsC,WAAW,GAAG,YAAY;IAC3C,IAAI5B,CAAC,GAAG,IAAI,CAACC,CAAC;IACd,IAAIF,CAAC,GAAG,EAAE;IACVA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACP,CAAC;IACb,IAAIU,CAAC,GAAG,IAAI,CAACC,EAAE,GAAIH,CAAC,GAAG,IAAI,CAACG,EAAE,GAAI,CAAC;IACnC,IAAIN,CAAC;IACL,IAAIH,CAAC,GAAG,CAAC;IACT,IAAIM,CAAC,EAAE,GAAG,CAAC,EAAE;MACT,IAAIE,CAAC,GAAG,IAAI,CAACC,EAAE,IAAI,CAACN,CAAC,GAAG,IAAI,CAACG,CAAC,CAAC,IAAIE,CAAC,KAAK,CAAC,IAAI,CAACV,CAAC,GAAG,IAAI,CAACmB,EAAE,KAAKT,CAAC,EAAE;QAC9DH,CAAC,CAACL,CAAC,EAAE,CAAC,GAAGG,CAAC,GAAI,IAAI,CAACL,CAAC,IAAK,IAAI,CAACW,EAAE,GAAGD,CAAG;MAC1C;MACA,OAAOF,CAAC,IAAI,CAAC,EAAE;QACX,IAAIE,CAAC,GAAG,CAAC,EAAE;UACPL,CAAC,GAAG,CAAC,IAAI,CAACG,CAAC,CAAC,GAAI,CAAC,CAAC,IAAIE,CAAC,IAAI,CAAE,KAAM,CAAC,GAAGA,CAAE;UACzCL,CAAC,IAAI,IAAI,CAAC,EAAEG,CAAC,CAAC,KAAKE,CAAC,IAAI,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC;QACxC,CAAC,MACI;UACDN,CAAC,GAAI,IAAI,CAACG,CAAC,CAAC,KAAKE,CAAC,IAAI,CAAC,CAAC,GAAI,IAAI;UAChC,IAAIA,CAAC,IAAI,CAAC,EAAE;YACRA,CAAC,IAAI,IAAI,CAACC,EAAE;YACZ,EAAEH,CAAC;UACP;QACJ;QACA,IAAI,CAACH,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;UACjBA,CAAC,IAAI,CAAC,GAAG;QACb;QACA,IAAIH,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAACF,CAAC,GAAG,IAAI,MAAMK,CAAC,GAAG,IAAI,CAAC,EAAE;UACzC,EAAEH,CAAC;QACP;QACA,IAAIA,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAI,IAAI,CAACL,CAAC,EAAE;UACtBO,CAAC,CAACL,CAAC,EAAE,CAAC,GAAGG,CAAC;QACd;MACJ;IACJ;IACA,OAAOE,CAAC;EACZ,CAAC;EACD;EACAf,UAAU,CAACM,SAAS,CAACuC,MAAM,GAAG,UAAU5C,CAAC,EAAE;IACvC,OAAQ,IAAI,CAACuB,SAAS,CAACvB,CAAC,CAAC,IAAI,CAAC;EAClC,CAAC;EACD;EACAD,UAAU,CAACM,SAAS,CAACwC,GAAG,GAAG,UAAU7C,CAAC,EAAE;IACpC,OAAQ,IAAI,CAACuB,SAAS,CAACvB,CAAC,CAAC,GAAG,CAAC,GAAI,IAAI,GAAGA,CAAC;EAC7C,CAAC;EACD;EACAD,UAAU,CAACM,SAAS,CAACyC,GAAG,GAAG,UAAU9C,CAAC,EAAE;IACpC,OAAQ,IAAI,CAACuB,SAAS,CAACvB,CAAC,CAAC,GAAG,CAAC,GAAI,IAAI,GAAGA,CAAC;EAC7C,CAAC;EACD;EACAD,UAAU,CAACM,SAAS,CAAC0C,GAAG,GAAG,UAAU/C,CAAC,EAAE;IACpC,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAAC6B,SAAS,CAAChD,CAAC,EAAEX,MAAM,EAAEyB,CAAC,CAAC;IAC5B,OAAOA,CAAC;EACZ,CAAC;EACD;EACAf,UAAU,CAACM,SAAS,CAAC4C,EAAE,GAAG,UAAUjD,CAAC,EAAE;IACnC,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAAC6B,SAAS,CAAChD,CAAC,EAAET,KAAK,EAAEuB,CAAC,CAAC;IAC3B,OAAOA,CAAC;EACZ,CAAC;EACD;EACAf,UAAU,CAACM,SAAS,CAAC6C,GAAG,GAAG,UAAUlD,CAAC,EAAE;IACpC,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAAC6B,SAAS,CAAChD,CAAC,EAAER,MAAM,EAAEsB,CAAC,CAAC;IAC5B,OAAOA,CAAC;EACZ,CAAC;EACD;EACAf,UAAU,CAACM,SAAS,CAAC8C,MAAM,GAAG,UAAUnD,CAAC,EAAE;IACvC,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAAC6B,SAAS,CAAChD,CAAC,EAAEV,SAAS,EAAEwB,CAAC,CAAC;IAC/B,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAAC+C,GAAG,GAAG,YAAY;IACnC,IAAItC,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC7BD,CAAC,CAACC,CAAC,CAAC,GAAG,IAAI,CAACW,EAAE,GAAG,CAAC,IAAI,CAACX,CAAC,CAAC;IAC7B;IACAD,CAAC,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC;IACZF,CAAC,CAACP,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC;IACb,OAAOO,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACgD,SAAS,GAAG,UAAUC,CAAC,EAAE;IAC1C,IAAIxC,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAImC,CAAC,GAAG,CAAC,EAAE;MACP,IAAI,CAACC,QAAQ,CAAC,CAACD,CAAC,EAAExC,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAAC0C,QAAQ,CAACF,CAAC,EAAExC,CAAC,CAAC;IACvB;IACA,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACoD,UAAU,GAAG,UAAUH,CAAC,EAAE;IAC3C,IAAIxC,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAImC,CAAC,GAAG,CAAC,EAAE;MACP,IAAI,CAACE,QAAQ,CAAC,CAACF,CAAC,EAAExC,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACyC,QAAQ,CAACD,CAAC,EAAExC,CAAC,CAAC;IACvB;IACA,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACqD,eAAe,GAAG,YAAY;IAC/C,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC7B,IAAI,IAAI,CAACA,CAAC,CAAC,IAAI,CAAC,EAAE;QACd,OAAOA,CAAC,GAAG,IAAI,CAACG,EAAE,GAAG9B,IAAI,CAAC,IAAI,CAAC2B,CAAC,CAAC,CAAC;MACtC;IACJ;IACA,IAAI,IAAI,CAACR,CAAC,GAAG,CAAC,EAAE;MACZ,OAAO,IAAI,CAACS,CAAC,GAAG,IAAI,CAACE,EAAE;IAC3B;IACA,OAAO,CAAC,CAAC;EACb,CAAC;EACD;EACA;EACAnB,UAAU,CAACM,SAAS,CAACsD,QAAQ,GAAG,YAAY;IACxC,IAAI7C,CAAC,GAAG,CAAC;IACT,IAAI8C,CAAC,GAAG,IAAI,CAACrD,CAAC,GAAG,IAAI,CAACmB,EAAE;IACxB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC7BD,CAAC,IAAI5B,IAAI,CAAC,IAAI,CAAC6B,CAAC,CAAC,GAAG6C,CAAC,CAAC;IAC1B;IACA,OAAO9C,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACwD,OAAO,GAAG,UAAUP,CAAC,EAAE;IACxC,IAAIQ,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACV,CAAC,GAAG,IAAI,CAACpC,EAAE,CAAC;IAC/B,IAAI4C,CAAC,IAAI,IAAI,CAAC9C,CAAC,EAAE;MACb,OAAQ,IAAI,CAACT,CAAC,IAAI,CAAC;IACvB;IACA,OAAQ,CAAC,IAAI,CAACuD,CAAC,CAAC,GAAI,CAAC,IAAKR,CAAC,GAAG,IAAI,CAACpC,EAAI,KAAK,CAAC;EACjD,CAAC;EACD;EACA;EACAnB,UAAU,CAACM,SAAS,CAAC4D,MAAM,GAAG,UAAUX,CAAC,EAAE;IACvC,OAAO,IAAI,CAACY,SAAS,CAACZ,CAAC,EAAE/D,KAAK,CAAC;EACnC,CAAC;EACD;EACA;EACAQ,UAAU,CAACM,SAAS,CAAC8D,QAAQ,GAAG,UAAUb,CAAC,EAAE;IACzC,OAAO,IAAI,CAACY,SAAS,CAACZ,CAAC,EAAEhE,SAAS,CAAC;EACvC,CAAC;EACD;EACA;EACAS,UAAU,CAACM,SAAS,CAAC+D,OAAO,GAAG,UAAUd,CAAC,EAAE;IACxC,OAAO,IAAI,CAACY,SAAS,CAACZ,CAAC,EAAE9D,MAAM,CAAC;EACpC,CAAC;EACD;EACA;EACAO,UAAU,CAACM,SAAS,CAACgE,GAAG,GAAG,UAAUrE,CAAC,EAAE;IACpC,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAACmD,KAAK,CAACtE,CAAC,EAAEc,CAAC,CAAC;IAChB,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACkE,QAAQ,GAAG,UAAUvE,CAAC,EAAE;IACzC,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAACE,KAAK,CAACrB,CAAC,EAAEc,CAAC,CAAC;IAChB,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACmE,QAAQ,GAAG,UAAUxE,CAAC,EAAE;IACzC,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAACsD,UAAU,CAACzE,CAAC,EAAEc,CAAC,CAAC;IACrB,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACqE,MAAM,GAAG,UAAU1E,CAAC,EAAE;IACvC,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAACS,QAAQ,CAAC5B,CAAC,EAAEc,CAAC,EAAE,IAAI,CAAC;IACzB,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACsE,SAAS,GAAG,UAAU3E,CAAC,EAAE;IAC1C,IAAIc,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAACS,QAAQ,CAAC5B,CAAC,EAAE,IAAI,EAAEc,CAAC,CAAC;IACzB,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACuE,kBAAkB,GAAG,UAAU5E,CAAC,EAAE;IACnD,IAAI6E,CAAC,GAAG1D,GAAG,CAAC,CAAC;IACb,IAAIL,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAACS,QAAQ,CAAC5B,CAAC,EAAE6E,CAAC,EAAE/D,CAAC,CAAC;IACtB,OAAO,CAAC+D,CAAC,EAAE/D,CAAC,CAAC;EACjB,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACyE,MAAM,GAAG,UAAUhD,CAAC,EAAEjB,CAAC,EAAE;IAC1C,IAAIE,CAAC,GAAGe,CAAC,CAACN,SAAS,CAAC,CAAC;IACrB,IAAIf,CAAC;IACL,IAAIK,CAAC,GAAGiE,GAAG,CAAC,CAAC,CAAC;IACd,IAAIhD,CAAC;IACL,IAAIhB,CAAC,IAAI,CAAC,EAAE;MACR,OAAOD,CAAC;IACZ,CAAC,MACI,IAAIC,CAAC,GAAG,EAAE,EAAE;MACbN,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIM,CAAC,GAAG,EAAE,EAAE;MACbN,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIM,CAAC,GAAG,GAAG,EAAE;MACdN,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIM,CAAC,GAAG,GAAG,EAAE;MACdN,CAAC,GAAG,CAAC;IACT,CAAC,MACI;MACDA,CAAC,GAAG,CAAC;IACT;IACA,IAAIM,CAAC,GAAG,CAAC,EAAE;MACPgB,CAAC,GAAG,IAAIE,OAAO,CAACpB,CAAC,CAAC;IACtB,CAAC,MACI,IAAIA,CAAC,CAACmB,MAAM,CAAC,CAAC,EAAE;MACjBD,CAAC,GAAG,IAAIiD,OAAO,CAACnE,CAAC,CAAC;IACtB,CAAC,MACI;MACDkB,CAAC,GAAG,IAAIG,UAAU,CAACrB,CAAC,CAAC;IACzB;IACA;IACA,IAAIoE,CAAC,GAAG,EAAE;IACV,IAAI3B,CAAC,GAAG,CAAC;IACT,IAAI4B,EAAE,GAAGzE,CAAC,GAAG,CAAC;IACd,IAAIE,EAAE,GAAG,CAAC,CAAC,IAAIF,CAAC,IAAI,CAAC;IACrBwE,CAAC,CAAC,CAAC,CAAC,GAAGlD,CAAC,CAACoD,OAAO,CAAC,IAAI,CAAC;IACtB,IAAI1E,CAAC,GAAG,CAAC,EAAE;MACP,IAAI2E,EAAE,GAAGjE,GAAG,CAAC,CAAC;MACdY,CAAC,CAACsD,KAAK,CAACJ,CAAC,CAAC,CAAC,CAAC,EAAEG,EAAE,CAAC;MACjB,OAAO9B,CAAC,IAAI3C,EAAE,EAAE;QACZsE,CAAC,CAAC3B,CAAC,CAAC,GAAGnC,GAAG,CAAC,CAAC;QACZY,CAAC,CAACuD,KAAK,CAACF,EAAE,EAAEH,CAAC,CAAC3B,CAAC,GAAG,CAAC,CAAC,EAAE2B,CAAC,CAAC3B,CAAC,CAAC,CAAC;QAC3BA,CAAC,IAAI,CAAC;MACV;IACJ;IACA,IAAIQ,CAAC,GAAGhC,CAAC,CAACd,CAAC,GAAG,CAAC;IACf,IAAIuE,CAAC;IACL,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIC,EAAE,GAAGtE,GAAG,CAAC,CAAC;IACd,IAAIH,CAAC;IACLD,CAAC,GAAGU,KAAK,CAACK,CAAC,CAACgC,CAAC,CAAC,CAAC,GAAG,CAAC;IACnB,OAAOA,CAAC,IAAI,CAAC,EAAE;MACX,IAAI/C,CAAC,IAAImE,EAAE,EAAE;QACTK,CAAC,GAAIzD,CAAC,CAACgC,CAAC,CAAC,IAAK/C,CAAC,GAAGmE,EAAG,GAAIvE,EAAE;MAC/B,CAAC,MACI;QACD4E,CAAC,GAAG,CAACzD,CAAC,CAACgC,CAAC,CAAC,GAAI,CAAC,CAAC,IAAK/C,CAAC,GAAG,CAAE,IAAI,CAAE,KAAMmE,EAAE,GAAGnE,CAAE;QAC7C,IAAI+C,CAAC,GAAG,CAAC,EAAE;UACPyB,CAAC,IAAIzD,CAAC,CAACgC,CAAC,GAAG,CAAC,CAAC,IAAK,IAAI,CAAC5C,EAAE,GAAGH,CAAC,GAAGmE,EAAG;QACvC;MACJ;MACA5B,CAAC,GAAG7C,CAAC;MACL,OAAO,CAAC8E,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QACjBA,CAAC,KAAK,CAAC;QACP,EAAEjC,CAAC;MACP;MACA,IAAI,CAACvC,CAAC,IAAIuC,CAAC,IAAI,CAAC,EAAE;QACdvC,CAAC,IAAI,IAAI,CAACG,EAAE;QACZ,EAAE4C,CAAC;MACP;MACA,IAAI0B,GAAG,EAAE;QAAE;QACPP,CAAC,CAACM,CAAC,CAAC,CAAClD,MAAM,CAACvB,CAAC,CAAC;QACd0E,GAAG,GAAG,KAAK;MACf,CAAC,MACI;QACD,OAAOlC,CAAC,GAAG,CAAC,EAAE;UACVvB,CAAC,CAACsD,KAAK,CAACvE,CAAC,EAAE2E,EAAE,CAAC;UACd1D,CAAC,CAACsD,KAAK,CAACI,EAAE,EAAE3E,CAAC,CAAC;UACdwC,CAAC,IAAI,CAAC;QACV;QACA,IAAIA,CAAC,GAAG,CAAC,EAAE;UACPvB,CAAC,CAACsD,KAAK,CAACvE,CAAC,EAAE2E,EAAE,CAAC;QAClB,CAAC,MACI;UACDzE,CAAC,GAAGF,CAAC;UACLA,CAAC,GAAG2E,EAAE;UACNA,EAAE,GAAGzE,CAAC;QACV;QACAe,CAAC,CAACuD,KAAK,CAACG,EAAE,EAAER,CAAC,CAACM,CAAC,CAAC,EAAEzE,CAAC,CAAC;MACxB;MACA,OAAOgD,CAAC,IAAI,CAAC,IAAI,CAAChC,CAAC,CAACgC,CAAC,CAAC,GAAI,CAAC,IAAI/C,CAAE,KAAK,CAAC,EAAE;QACrCgB,CAAC,CAACsD,KAAK,CAACvE,CAAC,EAAE2E,EAAE,CAAC;QACdzE,CAAC,GAAGF,CAAC;QACLA,CAAC,GAAG2E,EAAE;QACNA,EAAE,GAAGzE,CAAC;QACN,IAAI,EAAED,CAAC,GAAG,CAAC,EAAE;UACTA,CAAC,GAAG,IAAI,CAACG,EAAE,GAAG,CAAC;UACf,EAAE4C,CAAC;QACP;MACJ;IACJ;IACA,OAAO/B,CAAC,CAAC2D,MAAM,CAAC5E,CAAC,CAAC;EACtB,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACsF,UAAU,GAAG,UAAU9E,CAAC,EAAE;IAC3C,IAAI+E,EAAE,GAAG/E,CAAC,CAACmB,MAAM,CAAC,CAAC;IACnB,IAAK,IAAI,CAACA,MAAM,CAAC,CAAC,IAAI4D,EAAE,IAAK/E,CAAC,CAAC6B,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;MAC1C,OAAO3C,UAAU,CAACqB,IAAI;IAC1B;IACA,IAAIyE,CAAC,GAAGhF,CAAC,CAACuB,KAAK,CAAC,CAAC;IACjB,IAAI0D,CAAC,GAAG,IAAI,CAAC1D,KAAK,CAAC,CAAC;IACpB,IAAIpC,CAAC,GAAG+E,GAAG,CAAC,CAAC,CAAC;IACd,IAAI9E,CAAC,GAAG8E,GAAG,CAAC,CAAC,CAAC;IACd,IAAI7E,CAAC,GAAG6E,GAAG,CAAC,CAAC,CAAC;IACd,IAAInE,CAAC,GAAGmE,GAAG,CAAC,CAAC,CAAC;IACd,OAAOc,CAAC,CAACnD,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;MACpB,OAAOmD,CAAC,CAAC7D,MAAM,CAAC,CAAC,EAAE;QACf6D,CAAC,CAACtC,QAAQ,CAAC,CAAC,EAAEsC,CAAC,CAAC;QAChB,IAAID,EAAE,EAAE;UACJ,IAAI,CAAC5F,CAAC,CAACgC,MAAM,CAAC,CAAC,IAAI,CAAC/B,CAAC,CAAC+B,MAAM,CAAC,CAAC,EAAE;YAC5BhC,CAAC,CAACsE,KAAK,CAAC,IAAI,EAAEtE,CAAC,CAAC;YAChBC,CAAC,CAACoB,KAAK,CAACR,CAAC,EAAEZ,CAAC,CAAC;UACjB;UACAD,CAAC,CAACuD,QAAQ,CAAC,CAAC,EAAEvD,CAAC,CAAC;QACpB,CAAC,MACI,IAAI,CAACC,CAAC,CAAC+B,MAAM,CAAC,CAAC,EAAE;UAClB/B,CAAC,CAACoB,KAAK,CAACR,CAAC,EAAEZ,CAAC,CAAC;QACjB;QACAA,CAAC,CAACsD,QAAQ,CAAC,CAAC,EAAEtD,CAAC,CAAC;MACpB;MACA,OAAO6F,CAAC,CAAC9D,MAAM,CAAC,CAAC,EAAE;QACf8D,CAAC,CAACvC,QAAQ,CAAC,CAAC,EAAEuC,CAAC,CAAC;QAChB,IAAIF,EAAE,EAAE;UACJ,IAAI,CAAC1F,CAAC,CAAC8B,MAAM,CAAC,CAAC,IAAI,CAACpB,CAAC,CAACoB,MAAM,CAAC,CAAC,EAAE;YAC5B9B,CAAC,CAACoE,KAAK,CAAC,IAAI,EAAEpE,CAAC,CAAC;YAChBU,CAAC,CAACS,KAAK,CAACR,CAAC,EAAED,CAAC,CAAC;UACjB;UACAV,CAAC,CAACqD,QAAQ,CAAC,CAAC,EAAErD,CAAC,CAAC;QACpB,CAAC,MACI,IAAI,CAACU,CAAC,CAACoB,MAAM,CAAC,CAAC,EAAE;UAClBpB,CAAC,CAACS,KAAK,CAACR,CAAC,EAAED,CAAC,CAAC;QACjB;QACAA,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE3C,CAAC,CAAC;MACpB;MACA,IAAIiF,CAAC,CAACtE,SAAS,CAACuE,CAAC,CAAC,IAAI,CAAC,EAAE;QACrBD,CAAC,CAACxE,KAAK,CAACyE,CAAC,EAAED,CAAC,CAAC;QACb,IAAID,EAAE,EAAE;UACJ5F,CAAC,CAACqB,KAAK,CAACnB,CAAC,EAAEF,CAAC,CAAC;QACjB;QACAC,CAAC,CAACoB,KAAK,CAACT,CAAC,EAAEX,CAAC,CAAC;MACjB,CAAC,MACI;QACD6F,CAAC,CAACzE,KAAK,CAACwE,CAAC,EAAEC,CAAC,CAAC;QACb,IAAIF,EAAE,EAAE;UACJ1F,CAAC,CAACmB,KAAK,CAACrB,CAAC,EAAEE,CAAC,CAAC;QACjB;QACAU,CAAC,CAACS,KAAK,CAACpB,CAAC,EAAEW,CAAC,CAAC;MACjB;IACJ;IACA,IAAIkF,CAAC,CAACvE,SAAS,CAACxB,UAAU,CAACgG,GAAG,CAAC,IAAI,CAAC,EAAE;MAClC,OAAOhG,UAAU,CAACqB,IAAI;IAC1B;IACA,IAAIR,CAAC,CAACW,SAAS,CAACV,CAAC,CAAC,IAAI,CAAC,EAAE;MACrB,OAAOD,CAAC,CAAC2D,QAAQ,CAAC1D,CAAC,CAAC;IACxB;IACA,IAAID,CAAC,CAAC8B,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;MAChB9B,CAAC,CAAC0D,KAAK,CAACzD,CAAC,EAAED,CAAC,CAAC;IACjB,CAAC,MACI;MACD,OAAOA,CAAC;IACZ;IACA,IAAIA,CAAC,CAAC8B,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;MAChB,OAAO9B,CAAC,CAACyD,GAAG,CAACxD,CAAC,CAAC;IACnB,CAAC,MACI;MACD,OAAOD,CAAC;IACZ;EACJ,CAAC;EACD;EACA;EACAb,UAAU,CAACM,SAAS,CAAC2F,GAAG,GAAG,UAAUlE,CAAC,EAAE;IACpC,OAAO,IAAI,CAACK,GAAG,CAACL,CAAC,EAAE,IAAImE,OAAO,CAAC,CAAC,CAAC;EACrC,CAAC;EACD;EACA;EACAlG,UAAU,CAACM,SAAS,CAAC6F,GAAG,GAAG,UAAUlG,CAAC,EAAE;IACpC,IAAI4D,CAAC,GAAI,IAAI,CAACrD,CAAC,GAAG,CAAC,GAAI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC4B,KAAK,CAAC,CAAC;IACnD,IAAI+D,CAAC,GAAInG,CAAC,CAACO,CAAC,GAAG,CAAC,GAAIP,CAAC,CAACQ,MAAM,CAAC,CAAC,GAAGR,CAAC,CAACoC,KAAK,CAAC,CAAC;IAC1C,IAAIwB,CAAC,CAACrC,SAAS,CAAC4E,CAAC,CAAC,GAAG,CAAC,EAAE;MACpB,IAAInF,CAAC,GAAG4C,CAAC;MACTA,CAAC,GAAGuC,CAAC;MACLA,CAAC,GAAGnF,CAAC;IACT;IACA,IAAID,CAAC,GAAG6C,CAAC,CAACF,eAAe,CAAC,CAAC;IAC3B,IAAIuB,CAAC,GAAGkB,CAAC,CAACzC,eAAe,CAAC,CAAC;IAC3B,IAAIuB,CAAC,GAAG,CAAC,EAAE;MACP,OAAOrB,CAAC;IACZ;IACA,IAAI7C,CAAC,GAAGkE,CAAC,EAAE;MACPA,CAAC,GAAGlE,CAAC;IACT;IACA,IAAIkE,CAAC,GAAG,CAAC,EAAE;MACPrB,CAAC,CAACL,QAAQ,CAAC0B,CAAC,EAAErB,CAAC,CAAC;MAChBuC,CAAC,CAAC5C,QAAQ,CAAC0B,CAAC,EAAEkB,CAAC,CAAC;IACpB;IACA,OAAOvC,CAAC,CAAClB,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;MACnB,IAAI,CAAC3B,CAAC,GAAG6C,CAAC,CAACF,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE;QAC/BE,CAAC,CAACL,QAAQ,CAACxC,CAAC,EAAE6C,CAAC,CAAC;MACpB;MACA,IAAI,CAAC7C,CAAC,GAAGoF,CAAC,CAACzC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE;QAC/ByC,CAAC,CAAC5C,QAAQ,CAACxC,CAAC,EAAEoF,CAAC,CAAC;MACpB;MACA,IAAIvC,CAAC,CAACrC,SAAS,CAAC4E,CAAC,CAAC,IAAI,CAAC,EAAE;QACrBvC,CAAC,CAACvC,KAAK,CAAC8E,CAAC,EAAEvC,CAAC,CAAC;QACbA,CAAC,CAACL,QAAQ,CAAC,CAAC,EAAEK,CAAC,CAAC;MACpB,CAAC,MACI;QACDuC,CAAC,CAAC9E,KAAK,CAACuC,CAAC,EAAEuC,CAAC,CAAC;QACbA,CAAC,CAAC5C,QAAQ,CAAC,CAAC,EAAE4C,CAAC,CAAC;MACpB;IACJ;IACA,IAAIlB,CAAC,GAAG,CAAC,EAAE;MACPkB,CAAC,CAAC3C,QAAQ,CAACyB,CAAC,EAAEkB,CAAC,CAAC;IACpB;IACA,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACApG,UAAU,CAACM,SAAS,CAAC+F,eAAe,GAAG,UAAUpF,CAAC,EAAE;IAChD,IAAID,CAAC;IACL,IAAI6C,CAAC,GAAG,IAAI,CAACtC,GAAG,CAAC,CAAC;IAClB,IAAIsC,CAAC,CAAC5C,CAAC,IAAI,CAAC,IAAI4C,CAAC,CAAC,CAAC,CAAC,IAAIhE,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,EAAE;MACrD,KAAKiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,SAAS,CAACE,MAAM,EAAE,EAAEiB,CAAC,EAAE;QACnC,IAAI6C,CAAC,CAAC,CAAC,CAAC,IAAIhE,SAAS,CAACmB,CAAC,CAAC,EAAE;UACtB,OAAO,IAAI;QACf;MACJ;MACA,OAAO,KAAK;IAChB;IACA,IAAI6C,CAAC,CAAC5B,MAAM,CAAC,CAAC,EAAE;MACZ,OAAO,KAAK;IAChB;IACAjB,CAAC,GAAG,CAAC;IACL,OAAOA,CAAC,GAAGnB,SAAS,CAACE,MAAM,EAAE;MACzB,IAAIe,CAAC,GAAGjB,SAAS,CAACmB,CAAC,CAAC;MACpB,IAAI+C,CAAC,GAAG/C,CAAC,GAAG,CAAC;MACb,OAAO+C,CAAC,GAAGlE,SAAS,CAACE,MAAM,IAAIe,CAAC,GAAGhB,KAAK,EAAE;QACtCgB,CAAC,IAAIjB,SAAS,CAACkE,CAAC,EAAE,CAAC;MACvB;MACAjD,CAAC,GAAG+C,CAAC,CAACyC,MAAM,CAACxF,CAAC,CAAC;MACf,OAAOE,CAAC,GAAG+C,CAAC,EAAE;QACV,IAAIjD,CAAC,GAAGjB,SAAS,CAACmB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;UACzB,OAAO,KAAK;QAChB;MACJ;IACJ;IACA,OAAO6C,CAAC,CAAC0C,WAAW,CAACtF,CAAC,CAAC;EAC3B,CAAC;EACD;EACA;EACA;EACA;EACAjB,UAAU,CAACM,SAAS,CAACgC,MAAM,GAAG,UAAUvB,CAAC,EAAE;IACvC,KAAK,IAAIC,CAAC,GAAG,IAAI,CAACC,CAAC,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAClCD,CAAC,CAACC,CAAC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;IAClB;IACAD,CAAC,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC;IACZF,CAAC,CAACP,CAAC,GAAG,IAAI,CAACA,CAAC;EAChB,CAAC;EACD;EACA;EACAR,UAAU,CAACM,SAAS,CAACkG,OAAO,GAAG,UAAU3C,CAAC,EAAE;IACxC,IAAI,CAAC5C,CAAC,GAAG,CAAC;IACV,IAAI,CAACT,CAAC,GAAIqD,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG,CAAC;IACzB,IAAIA,CAAC,GAAG,CAAC,EAAE;MACP,IAAI,CAAC,CAAC,CAAC,GAAGA,CAAC;IACf,CAAC,MACI,IAAIA,CAAC,GAAG,CAAC,CAAC,EAAE;MACb,IAAI,CAAC,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACrB,EAAE;IACzB,CAAC,MACI;MACD,IAAI,CAACvB,CAAC,GAAG,CAAC;IACd;EACJ,CAAC;EACD;EACA;EACAjB,UAAU,CAACM,SAAS,CAACD,UAAU,GAAG,UAAUG,CAAC,EAAEN,CAAC,EAAE;IAC9C,IAAIQ,CAAC;IACL,IAAIR,CAAC,IAAI,EAAE,EAAE;MACTQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIR,CAAC,IAAI,CAAC,EAAE;MACbQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIR,CAAC,IAAI,GAAG,EAAE;MACfQ,CAAC,GAAG,CAAC;MACL;IACJ,CAAC,MACI,IAAIR,CAAC,IAAI,CAAC,EAAE;MACbQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIR,CAAC,IAAI,EAAE,EAAE;MACdQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIR,CAAC,IAAI,CAAC,EAAE;MACbQ,CAAC,GAAG,CAAC;IACT,CAAC,MACI;MACD,IAAI,CAAC+F,SAAS,CAACjG,CAAC,EAAEN,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACe,CAAC,GAAG,CAAC;IACV,IAAI,CAACT,CAAC,GAAG,CAAC;IACV,IAAIQ,CAAC,GAAGR,CAAC,CAACT,MAAM;IAChB,IAAI2G,EAAE,GAAG,KAAK;IACd,IAAIC,EAAE,GAAG,CAAC;IACV,OAAO,EAAE3F,CAAC,IAAI,CAAC,EAAE;MACb,IAAI6C,CAAC,GAAInD,CAAC,IAAI,CAAC,GAAK,CAACF,CAAC,CAACQ,CAAC,CAAC,GAAI,IAAI,GAAG4F,KAAK,CAACpG,CAAC,EAAEQ,CAAC,CAAC;MAC/C,IAAI6C,CAAC,GAAG,CAAC,EAAE;QACP,IAAIrD,CAAC,CAACqG,MAAM,CAAC7F,CAAC,CAAC,IAAI,GAAG,EAAE;UACpB0F,EAAE,GAAG,IAAI;QACb;QACA;MACJ;MACAA,EAAE,GAAG,KAAK;MACV,IAAIC,EAAE,IAAI,CAAC,EAAE;QACT,IAAI,CAAC,IAAI,CAAC1F,CAAC,EAAE,CAAC,GAAG4C,CAAC;MACtB,CAAC,MACI,IAAI8C,EAAE,GAAGjG,CAAC,GAAG,IAAI,CAACS,EAAE,EAAE;QACvB,IAAI,CAAC,IAAI,CAACF,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC4C,CAAC,GAAI,CAAC,CAAC,IAAK,IAAI,CAAC1C,EAAE,GAAGwF,EAAG,IAAI,CAAE,KAAKA,EAAE;QAC3D,IAAI,CAAC,IAAI,CAAC1F,CAAC,EAAE,CAAC,GAAI4C,CAAC,IAAK,IAAI,CAAC1C,EAAE,GAAGwF,EAAI;MAC1C,CAAC,MACI;QACD,IAAI,CAAC,IAAI,CAAC1F,CAAC,GAAG,CAAC,CAAC,IAAI4C,CAAC,IAAI8C,EAAE;MAC/B;MACAA,EAAE,IAAIjG,CAAC;MACP,IAAIiG,EAAE,IAAI,IAAI,CAACxF,EAAE,EAAE;QACfwF,EAAE,IAAI,IAAI,CAACxF,EAAE;MACjB;IACJ;IACA,IAAIT,CAAC,IAAI,CAAC,IAAI,CAAE,CAACF,CAAC,CAAC,CAAC,CAAC,GAAI,IAAI,KAAK,CAAC,EAAE;MACjC,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC;MACX,IAAImG,EAAE,GAAG,CAAC,EAAE;QACR,IAAI,CAAC,IAAI,CAAC1F,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC,IAAK,IAAI,CAACE,EAAE,GAAGwF,EAAG,IAAI,CAAC,IAAKA,EAAE;MACzD;IACJ;IACA,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,IAAIJ,EAAE,EAAE;MACJ1G,UAAU,CAACqB,IAAI,CAACC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;IACrC;EACJ,CAAC;EACD;EACA;EACAtB,UAAU,CAACM,SAAS,CAACwG,KAAK,GAAG,YAAY;IACrC,IAAI3G,CAAC,GAAG,IAAI,CAACK,CAAC,GAAG,IAAI,CAACmB,EAAE;IACxB,OAAO,IAAI,CAACV,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC,IAAId,CAAC,EAAE;MACxC,EAAE,IAAI,CAACc,CAAC;IACZ;EACJ,CAAC;EACD;EACA;EACAjB,UAAU,CAACM,SAAS,CAACyG,SAAS,GAAG,UAAUxD,CAAC,EAAExC,CAAC,EAAE;IAC7C,IAAIC,CAAC;IACL,KAAKA,CAAC,GAAG,IAAI,CAACC,CAAC,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC9BD,CAAC,CAACC,CAAC,GAAGuC,CAAC,CAAC,GAAG,IAAI,CAACvC,CAAC,CAAC;IACtB;IACA,KAAKA,CAAC,GAAGuC,CAAC,GAAG,CAAC,EAAEvC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzBD,CAAC,CAACC,CAAC,CAAC,GAAG,CAAC;IACZ;IACAD,CAAC,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGsC,CAAC;IAChBxC,CAAC,CAACP,CAAC,GAAG,IAAI,CAACA,CAAC;EAChB,CAAC;EACD;EACA;EACAR,UAAU,CAACM,SAAS,CAAC0G,SAAS,GAAG,UAAUzD,CAAC,EAAExC,CAAC,EAAE;IAC7C,KAAK,IAAIC,CAAC,GAAGuC,CAAC,EAAEvC,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC7BD,CAAC,CAACC,CAAC,GAAGuC,CAAC,CAAC,GAAG,IAAI,CAACvC,CAAC,CAAC;IACtB;IACAD,CAAC,CAACE,CAAC,GAAG+C,IAAI,CAACjB,GAAG,CAAC,IAAI,CAAC9B,CAAC,GAAGsC,CAAC,EAAE,CAAC,CAAC;IAC7BxC,CAAC,CAACP,CAAC,GAAG,IAAI,CAACA,CAAC;EAChB,CAAC;EACD;EACA;EACAR,UAAU,CAACM,SAAS,CAACmD,QAAQ,GAAG,UAAUF,CAAC,EAAExC,CAAC,EAAE;IAC5C,IAAIkG,EAAE,GAAG1D,CAAC,GAAG,IAAI,CAACpC,EAAE;IACpB,IAAI+F,GAAG,GAAG,IAAI,CAAC/F,EAAE,GAAG8F,EAAE;IACtB,IAAIE,EAAE,GAAG,CAAC,CAAC,IAAID,GAAG,IAAI,CAAC;IACvB,IAAIE,EAAE,GAAGpD,IAAI,CAACC,KAAK,CAACV,CAAC,GAAG,IAAI,CAACpC,EAAE,CAAC;IAChC,IAAIhB,CAAC,GAAI,IAAI,CAACK,CAAC,IAAIyG,EAAE,GAAI,IAAI,CAACtF,EAAE;IAChC,KAAK,IAAIX,CAAC,GAAG,IAAI,CAACC,CAAC,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAClCD,CAAC,CAACC,CAAC,GAAGoG,EAAE,GAAG,CAAC,CAAC,GAAI,IAAI,CAACpG,CAAC,CAAC,IAAIkG,GAAG,GAAI/G,CAAC;MACpCA,CAAC,GAAG,CAAC,IAAI,CAACa,CAAC,CAAC,GAAGmG,EAAE,KAAKF,EAAE;IAC5B;IACA,KAAK,IAAIjG,CAAC,GAAGoG,EAAE,GAAG,CAAC,EAAEpG,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC9BD,CAAC,CAACC,CAAC,CAAC,GAAG,CAAC;IACZ;IACAD,CAAC,CAACqG,EAAE,CAAC,GAAGjH,CAAC;IACTY,CAAC,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGmG,EAAE,GAAG,CAAC;IACrBrG,CAAC,CAACP,CAAC,GAAG,IAAI,CAACA,CAAC;IACZO,CAAC,CAAC+F,KAAK,CAAC,CAAC;EACb,CAAC;EACD;EACA;EACA9G,UAAU,CAACM,SAAS,CAACkD,QAAQ,GAAG,UAAUD,CAAC,EAAExC,CAAC,EAAE;IAC5CA,CAAC,CAACP,CAAC,GAAG,IAAI,CAACA,CAAC;IACZ,IAAI4G,EAAE,GAAGpD,IAAI,CAACC,KAAK,CAACV,CAAC,GAAG,IAAI,CAACpC,EAAE,CAAC;IAChC,IAAIiG,EAAE,IAAI,IAAI,CAACnG,CAAC,EAAE;MACdF,CAAC,CAACE,CAAC,GAAG,CAAC;MACP;IACJ;IACA,IAAIgG,EAAE,GAAG1D,CAAC,GAAG,IAAI,CAACpC,EAAE;IACpB,IAAI+F,GAAG,GAAG,IAAI,CAAC/F,EAAE,GAAG8F,EAAE;IACtB,IAAIE,EAAE,GAAG,CAAC,CAAC,IAAIF,EAAE,IAAI,CAAC;IACtBlG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACqG,EAAE,CAAC,IAAIH,EAAE;IACrB,KAAK,IAAIjG,CAAC,GAAGoG,EAAE,GAAG,CAAC,EAAEpG,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE,EAAED,CAAC,EAAE;MAClCD,CAAC,CAACC,CAAC,GAAGoG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAACpG,CAAC,CAAC,GAAGmG,EAAE,KAAKD,GAAG;MACtCnG,CAAC,CAACC,CAAC,GAAGoG,EAAE,CAAC,GAAG,IAAI,CAACpG,CAAC,CAAC,IAAIiG,EAAE;IAC7B;IACA,IAAIA,EAAE,GAAG,CAAC,EAAE;MACRlG,CAAC,CAAC,IAAI,CAACE,CAAC,GAAGmG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC5G,CAAC,GAAG2G,EAAE,KAAKD,GAAG;IAC9C;IACAnG,CAAC,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGmG,EAAE;IACjBrG,CAAC,CAAC+F,KAAK,CAAC,CAAC;EACb,CAAC;EACD;EACA;EACA9G,UAAU,CAACM,SAAS,CAACgB,KAAK,GAAG,UAAUrB,CAAC,EAAEc,CAAC,EAAE;IACzC,IAAIC,CAAC,GAAG,CAAC;IACT,IAAIb,CAAC,GAAG,CAAC;IACT,IAAIW,CAAC,GAAGkD,IAAI,CAAClB,GAAG,CAAC7C,CAAC,CAACgB,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC;IAC7B,OAAOD,CAAC,GAAGF,CAAC,EAAE;MACVX,CAAC,IAAI,IAAI,CAACa,CAAC,CAAC,GAAGf,CAAC,CAACe,CAAC,CAAC;MACnBD,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGb,CAAC,GAAG,IAAI,CAACwB,EAAE;MACpBxB,CAAC,KAAK,IAAI,CAACgB,EAAE;IACjB;IACA,IAAIlB,CAAC,CAACgB,CAAC,GAAG,IAAI,CAACA,CAAC,EAAE;MACdd,CAAC,IAAIF,CAAC,CAACO,CAAC;MACR,OAAOQ,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE;QACfd,CAAC,IAAI,IAAI,CAACa,CAAC,CAAC;QACZD,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGb,CAAC,GAAG,IAAI,CAACwB,EAAE;QACpBxB,CAAC,KAAK,IAAI,CAACgB,EAAE;MACjB;MACAhB,CAAC,IAAI,IAAI,CAACK,CAAC;IACf,CAAC,MACI;MACDL,CAAC,IAAI,IAAI,CAACK,CAAC;MACX,OAAOQ,CAAC,GAAGf,CAAC,CAACgB,CAAC,EAAE;QACZd,CAAC,IAAIF,CAAC,CAACe,CAAC,CAAC;QACTD,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGb,CAAC,GAAG,IAAI,CAACwB,EAAE;QACpBxB,CAAC,KAAK,IAAI,CAACgB,EAAE;MACjB;MACAhB,CAAC,IAAIF,CAAC,CAACO,CAAC;IACZ;IACAO,CAAC,CAACP,CAAC,GAAIL,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG,CAAC;IACtB,IAAIA,CAAC,GAAG,CAAC,CAAC,EAAE;MACRY,CAAC,CAACC,CAAC,EAAE,CAAC,GAAG,IAAI,CAACwB,EAAE,GAAGrC,CAAC;IACxB,CAAC,MACI,IAAIA,CAAC,GAAG,CAAC,EAAE;MACZY,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGb,CAAC;IACd;IACAY,CAAC,CAACE,CAAC,GAAGD,CAAC;IACPD,CAAC,CAAC+F,KAAK,CAAC,CAAC;EACb,CAAC;EACD;EACA;EACA;EACA9G,UAAU,CAACM,SAAS,CAACoE,UAAU,GAAG,UAAUzE,CAAC,EAAEc,CAAC,EAAE;IAC9C,IAAI8C,CAAC,GAAG,IAAI,CAACtC,GAAG,CAAC,CAAC;IAClB,IAAI6E,CAAC,GAAGnG,CAAC,CAACsB,GAAG,CAAC,CAAC;IACf,IAAIP,CAAC,GAAG6C,CAAC,CAAC5C,CAAC;IACXF,CAAC,CAACE,CAAC,GAAGD,CAAC,GAAGoF,CAAC,CAACnF,CAAC;IACb,OAAO,EAAED,CAAC,IAAI,CAAC,EAAE;MACbD,CAAC,CAACC,CAAC,CAAC,GAAG,CAAC;IACZ;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoF,CAAC,CAACnF,CAAC,EAAE,EAAED,CAAC,EAAE;MACtBD,CAAC,CAACC,CAAC,GAAG6C,CAAC,CAAC5C,CAAC,CAAC,GAAG4C,CAAC,CAACwD,EAAE,CAAC,CAAC,EAAEjB,CAAC,CAACpF,CAAC,CAAC,EAAED,CAAC,EAAEC,CAAC,EAAE,CAAC,EAAE6C,CAAC,CAAC5C,CAAC,CAAC;IAC5C;IACAF,CAAC,CAACP,CAAC,GAAG,CAAC;IACPO,CAAC,CAAC+F,KAAK,CAAC,CAAC;IACT,IAAI,IAAI,CAACtG,CAAC,IAAIP,CAAC,CAACO,CAAC,EAAE;MACfR,UAAU,CAACqB,IAAI,CAACC,KAAK,CAACP,CAAC,EAAEA,CAAC,CAAC;IAC/B;EACJ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACgH,QAAQ,GAAG,UAAUvG,CAAC,EAAE;IACzC,IAAI8C,CAAC,GAAG,IAAI,CAACtC,GAAG,CAAC,CAAC;IAClB,IAAIP,CAAC,GAAGD,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG4C,CAAC,CAAC5C,CAAC;IACrB,OAAO,EAAED,CAAC,IAAI,CAAC,EAAE;MACbD,CAAC,CAACC,CAAC,CAAC,GAAG,CAAC;IACZ;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,CAAC,CAAC5C,CAAC,GAAG,CAAC,EAAE,EAAED,CAAC,EAAE;MAC1B,IAAIb,CAAC,GAAG0D,CAAC,CAACwD,EAAE,CAACrG,CAAC,EAAE6C,CAAC,CAAC7C,CAAC,CAAC,EAAED,CAAC,EAAE,CAAC,GAAGC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrC,IAAI,CAACD,CAAC,CAACC,CAAC,GAAG6C,CAAC,CAAC5C,CAAC,CAAC,IAAI4C,CAAC,CAACwD,EAAE,CAACrG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG6C,CAAC,CAAC7C,CAAC,CAAC,EAAED,CAAC,EAAE,CAAC,GAAGC,CAAC,GAAG,CAAC,EAAEb,CAAC,EAAE0D,CAAC,CAAC5C,CAAC,GAAGD,CAAC,GAAG,CAAC,CAAC,KAAK6C,CAAC,CAACrB,EAAE,EAAE;QAC7EzB,CAAC,CAACC,CAAC,GAAG6C,CAAC,CAAC5C,CAAC,CAAC,IAAI4C,CAAC,CAACrB,EAAE;QAClBzB,CAAC,CAACC,CAAC,GAAG6C,CAAC,CAAC5C,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACtB;IACJ;IACA,IAAIF,CAAC,CAACE,CAAC,GAAG,CAAC,EAAE;MACTF,CAAC,CAACA,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI4C,CAAC,CAACwD,EAAE,CAACrG,CAAC,EAAE6C,CAAC,CAAC7C,CAAC,CAAC,EAAED,CAAC,EAAE,CAAC,GAAGC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/C;IACAD,CAAC,CAACP,CAAC,GAAG,CAAC;IACPO,CAAC,CAAC+F,KAAK,CAAC,CAAC;EACb,CAAC;EACD;EACA;EACA;EACA9G,UAAU,CAACM,SAAS,CAACuB,QAAQ,GAAG,UAAUf,CAAC,EAAEgE,CAAC,EAAE/D,CAAC,EAAE;IAC/C,IAAIwG,EAAE,GAAGzG,CAAC,CAACS,GAAG,CAAC,CAAC;IAChB,IAAIgG,EAAE,CAACtG,CAAC,IAAI,CAAC,EAAE;MACX;IACJ;IACA,IAAIuG,EAAE,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;IACnB,IAAIiG,EAAE,CAACvG,CAAC,GAAGsG,EAAE,CAACtG,CAAC,EAAE;MACb,IAAI6D,CAAC,IAAI,IAAI,EAAE;QACXA,CAAC,CAAC0B,OAAO,CAAC,CAAC,CAAC;MAChB;MACA,IAAIzF,CAAC,IAAI,IAAI,EAAE;QACX,IAAI,CAACuB,MAAM,CAACvB,CAAC,CAAC;MAClB;MACA;IACJ;IACA,IAAIA,CAAC,IAAI,IAAI,EAAE;MACXA,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb;IACA,IAAIgF,CAAC,GAAGhF,GAAG,CAAC,CAAC;IACb,IAAIqG,EAAE,GAAG,IAAI,CAACjH,CAAC;IACf,IAAIkH,EAAE,GAAG5G,CAAC,CAACN,CAAC;IACZ,IAAImH,GAAG,GAAG,IAAI,CAACxG,EAAE,GAAGO,KAAK,CAAC6F,EAAE,CAACA,EAAE,CAACtG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,IAAI0G,GAAG,GAAG,CAAC,EAAE;MACTJ,EAAE,CAAC9D,QAAQ,CAACkE,GAAG,EAAEvB,CAAC,CAAC;MACnBoB,EAAE,CAAC/D,QAAQ,CAACkE,GAAG,EAAE5G,CAAC,CAAC;IACvB,CAAC,MACI;MACDwG,EAAE,CAACjF,MAAM,CAAC8D,CAAC,CAAC;MACZoB,EAAE,CAAClF,MAAM,CAACvB,CAAC,CAAC;IAChB;IACA,IAAI6G,EAAE,GAAGxB,CAAC,CAACnF,CAAC;IACZ,IAAI4G,EAAE,GAAGzB,CAAC,CAACwB,EAAE,GAAG,CAAC,CAAC;IAClB,IAAIC,EAAE,IAAI,CAAC,EAAE;MACT;IACJ;IACA,IAAIC,EAAE,GAAGD,EAAE,IAAI,CAAC,IAAI,IAAI,CAACE,EAAE,CAAC,IAAKH,EAAE,GAAG,CAAC,GAAIxB,CAAC,CAACwB,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAACI,EAAE,GAAG,CAAC,CAAC;IACpE,IAAIC,EAAE,GAAG,IAAI,CAACC,EAAE,GAAGJ,EAAE;IACrB,IAAIK,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAACJ,EAAE,IAAID,EAAE;IAC5B,IAAI/F,CAAC,GAAG,CAAC,IAAI,IAAI,CAACiG,EAAE;IACpB,IAAIhH,CAAC,GAAGD,CAAC,CAACE,CAAC;IACX,IAAI8C,CAAC,GAAG/C,CAAC,GAAG4G,EAAE;IACd,IAAI3G,CAAC,GAAI6D,CAAC,IAAI,IAAI,GAAI1D,GAAG,CAAC,CAAC,GAAG0D,CAAC;IAC/BsB,CAAC,CAACW,SAAS,CAAChD,CAAC,EAAE9C,CAAC,CAAC;IACjB,IAAIF,CAAC,CAACS,SAAS,CAACP,CAAC,CAAC,IAAI,CAAC,EAAE;MACrBF,CAAC,CAACA,CAAC,CAACE,CAAC,EAAE,CAAC,GAAG,CAAC;MACZF,CAAC,CAACO,KAAK,CAACL,CAAC,EAAEF,CAAC,CAAC;IACjB;IACAf,UAAU,CAACgG,GAAG,CAACe,SAAS,CAACa,EAAE,EAAE3G,CAAC,CAAC;IAC/BA,CAAC,CAACK,KAAK,CAAC8E,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC;IACf,OAAOA,CAAC,CAACnF,CAAC,GAAG2G,EAAE,EAAE;MACbxB,CAAC,CAACA,CAAC,CAACnF,CAAC,EAAE,CAAC,GAAG,CAAC;IAChB;IACA,OAAO,EAAE8C,CAAC,IAAI,CAAC,EAAE;MACb;MACA,IAAIqE,EAAE,GAAIrH,CAAC,CAAC,EAAEC,CAAC,CAAC,IAAI6G,EAAE,GAAI,IAAI,CAAClG,EAAE,GAAGqC,IAAI,CAACC,KAAK,CAAClD,CAAC,CAACC,CAAC,CAAC,GAAGiH,EAAE,GAAG,CAAClH,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGe,CAAC,IAAIoG,EAAE,CAAC;MAC/E,IAAI,CAACpH,CAAC,CAACC,CAAC,CAAC,IAAIoF,CAAC,CAACiB,EAAE,CAAC,CAAC,EAAEe,EAAE,EAAErH,CAAC,EAAEgD,CAAC,EAAE,CAAC,EAAE6D,EAAE,CAAC,IAAIQ,EAAE,EAAE;QAAE;QAC3ChC,CAAC,CAACW,SAAS,CAAChD,CAAC,EAAE9C,CAAC,CAAC;QACjBF,CAAC,CAACO,KAAK,CAACL,CAAC,EAAEF,CAAC,CAAC;QACb,OAAOA,CAAC,CAACC,CAAC,CAAC,GAAG,EAAEoH,EAAE,EAAE;UAChBrH,CAAC,CAACO,KAAK,CAACL,CAAC,EAAEF,CAAC,CAAC;QACjB;MACJ;IACJ;IACA,IAAI+D,CAAC,IAAI,IAAI,EAAE;MACX/D,CAAC,CAACiG,SAAS,CAACY,EAAE,EAAE9C,CAAC,CAAC;MAClB,IAAI2C,EAAE,IAAIC,EAAE,EAAE;QACV1H,UAAU,CAACqB,IAAI,CAACC,KAAK,CAACwD,CAAC,EAAEA,CAAC,CAAC;MAC/B;IACJ;IACA/D,CAAC,CAACE,CAAC,GAAG2G,EAAE;IACR7G,CAAC,CAAC+F,KAAK,CAAC,CAAC;IACT,IAAIa,GAAG,GAAG,CAAC,EAAE;MACT5G,CAAC,CAACyC,QAAQ,CAACmE,GAAG,EAAE5G,CAAC,CAAC;IACtB,CAAC,CAAC;IACF,IAAI0G,EAAE,GAAG,CAAC,EAAE;MACRzH,UAAU,CAACqB,IAAI,CAACC,KAAK,CAACP,CAAC,EAAEA,CAAC,CAAC;IAC/B;EACJ,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAf,UAAU,CAACM,SAAS,CAAC+H,QAAQ,GAAG,YAAY;IACxC,IAAI,IAAI,CAACpH,CAAC,GAAG,CAAC,EAAE;MACZ,OAAO,CAAC;IACZ;IACA,IAAI4C,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACf,IAAI,CAACA,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MACd,OAAO,CAAC;IACZ;IACA,IAAIuC,CAAC,GAAGvC,CAAC,GAAG,CAAC,CAAC,CAAC;IACfuC,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAG,CAACvC,CAAC,GAAG,GAAG,IAAIuC,CAAC,CAAC,GAAI,GAAG,CAAC,CAAC;IACrCA,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAG,CAACvC,CAAC,GAAG,IAAI,IAAIuC,CAAC,CAAC,GAAI,IAAI,CAAC,CAAC;IACvCA,CAAC,GAAIA,CAAC,IAAI,CAAC,IAAK,CAACvC,CAAC,GAAG,MAAM,IAAIuC,CAAC,GAAI,MAAM,CAAC,CAAC,GAAI,MAAM,CAAC,CAAC;IACxD;IACA;IACAA,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAGvC,CAAC,GAAGuC,CAAC,GAAG,IAAI,CAAC5D,EAAE,CAAC,GAAI,IAAI,CAACA,EAAE,CAAC,CAAC;IAC3C;IACA,OAAQ4D,CAAC,GAAG,CAAC,GAAI,IAAI,CAAC5D,EAAE,GAAG4D,CAAC,GAAG,CAACA,CAAC;EACrC,CAAC;EACD;EACA;EACApG,UAAU,CAACM,SAAS,CAAC2B,MAAM,GAAG,YAAY;IACtC,OAAO,CAAE,IAAI,CAAChB,CAAC,GAAG,CAAC,GAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAI,IAAI,CAACT,CAAC,KAAK,CAAC;EACvD,CAAC;EACD;EACA;EACAR,UAAU,CAACM,SAAS,CAAC8B,GAAG,GAAG,UAAUL,CAAC,EAAEC,CAAC,EAAE;IACvC,IAAID,CAAC,GAAG,UAAU,IAAIA,CAAC,GAAG,CAAC,EAAE;MACzB,OAAO/B,UAAU,CAACgG,GAAG;IACzB;IACA,IAAIjF,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAIsE,EAAE,GAAGtE,GAAG,CAAC,CAAC;IACd,IAAI8D,CAAC,GAAGlD,CAAC,CAACoD,OAAO,CAAC,IAAI,CAAC;IACvB,IAAIpE,CAAC,GAAGU,KAAK,CAACK,CAAC,CAAC,GAAG,CAAC;IACpBmD,CAAC,CAAC5C,MAAM,CAACvB,CAAC,CAAC;IACX,OAAO,EAAEC,CAAC,IAAI,CAAC,EAAE;MACbgB,CAAC,CAACsD,KAAK,CAACvE,CAAC,EAAE2E,EAAE,CAAC;MACd,IAAI,CAAC3D,CAAC,GAAI,CAAC,IAAIf,CAAE,IAAI,CAAC,EAAE;QACpBgB,CAAC,CAACuD,KAAK,CAACG,EAAE,EAAER,CAAC,EAAEnE,CAAC,CAAC;MACrB,CAAC,MACI;QACD,IAAIE,CAAC,GAAGF,CAAC;QACTA,CAAC,GAAG2E,EAAE;QACNA,EAAE,GAAGzE,CAAC;MACV;IACJ;IACA,OAAOe,CAAC,CAAC2D,MAAM,CAAC5E,CAAC,CAAC;EACtB,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACgI,SAAS,GAAG,UAAUvH,CAAC,EAAE;IAC1C,OAAOiD,IAAI,CAACC,KAAK,CAACD,IAAI,CAACuE,GAAG,GAAG,IAAI,CAACpH,EAAE,GAAG6C,IAAI,CAACwE,GAAG,CAACzH,CAAC,CAAC,CAAC;EACvD,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACK,OAAO,GAAG,UAAUT,CAAC,EAAE;IACxC,IAAIA,CAAC,IAAI,IAAI,EAAE;MACXA,CAAC,GAAG,EAAE;IACV;IACA,IAAI,IAAI,CAACyC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAIzC,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,EAAE,EAAE;MACvC,OAAO,GAAG;IACd;IACA,IAAIuI,EAAE,GAAG,IAAI,CAACH,SAAS,CAACpI,CAAC,CAAC;IAC1B,IAAID,CAAC,GAAG+D,IAAI,CAACiC,GAAG,CAAC/F,CAAC,EAAEuI,EAAE,CAAC;IACvB,IAAI5H,CAAC,GAAGmE,GAAG,CAAC/E,CAAC,CAAC;IACd,IAAImG,CAAC,GAAGhF,GAAG,CAAC,CAAC;IACb,IAAIY,CAAC,GAAGZ,GAAG,CAAC,CAAC;IACb,IAAIL,CAAC,GAAG,EAAE;IACV,IAAI,CAACc,QAAQ,CAAChB,CAAC,EAAEuF,CAAC,EAAEpE,CAAC,CAAC;IACtB,OAAOoE,CAAC,CAACzD,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;MACnB5B,CAAC,GAAG,CAACd,CAAC,GAAG+B,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAEhC,QAAQ,CAACL,CAAC,CAAC,CAACwI,MAAM,CAAC,CAAC,CAAC,GAAG3H,CAAC;MAChDqF,CAAC,CAACvE,QAAQ,CAAChB,CAAC,EAAEuF,CAAC,EAAEpE,CAAC,CAAC;IACvB;IACA,OAAOA,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAACL,CAAC,CAAC,GAAGa,CAAC;EACvC,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACmG,SAAS,GAAG,UAAUjG,CAAC,EAAEN,CAAC,EAAE;IAC7C,IAAI,CAACsG,OAAO,CAAC,CAAC,CAAC;IACf,IAAItG,CAAC,IAAI,IAAI,EAAE;MACXA,CAAC,GAAG,EAAE;IACV;IACA,IAAIuI,EAAE,GAAG,IAAI,CAACH,SAAS,CAACpI,CAAC,CAAC;IAC1B,IAAIW,CAAC,GAAGmD,IAAI,CAACiC,GAAG,CAAC/F,CAAC,EAAEuI,EAAE,CAAC;IACvB,IAAI/B,EAAE,GAAG,KAAK;IACd,IAAI3C,CAAC,GAAG,CAAC;IACT,IAAIyB,CAAC,GAAG,CAAC;IACT,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,CAACT,MAAM,EAAE,EAAEiB,CAAC,EAAE;MAC/B,IAAI6C,CAAC,GAAG+C,KAAK,CAACpG,CAAC,EAAEQ,CAAC,CAAC;MACnB,IAAI6C,CAAC,GAAG,CAAC,EAAE;QACP,IAAIrD,CAAC,CAACqG,MAAM,CAAC7F,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC2B,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;UAC1C+D,EAAE,GAAG,IAAI;QACb;QACA;MACJ;MACAlB,CAAC,GAAGtF,CAAC,GAAGsF,CAAC,GAAG3B,CAAC;MACb,IAAI,EAAEE,CAAC,IAAI0E,EAAE,EAAE;QACX,IAAI,CAACE,SAAS,CAAC9H,CAAC,CAAC;QACjB,IAAI,CAAC+H,UAAU,CAACpD,CAAC,EAAE,CAAC,CAAC;QACrBzB,CAAC,GAAG,CAAC;QACLyB,CAAC,GAAG,CAAC;MACT;IACJ;IACA,IAAIzB,CAAC,GAAG,CAAC,EAAE;MACP,IAAI,CAAC4E,SAAS,CAAC3E,IAAI,CAACiC,GAAG,CAAC/F,CAAC,EAAE6D,CAAC,CAAC,CAAC;MAC9B,IAAI,CAAC6E,UAAU,CAACpD,CAAC,EAAE,CAAC,CAAC;IACzB;IACA,IAAIkB,EAAE,EAAE;MACJ1G,UAAU,CAACqB,IAAI,CAACC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;IACrC;EACJ,CAAC;EACD;EACA;EACAtB,UAAU,CAACM,SAAS,CAACF,UAAU,GAAG,UAAUH,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACjD,IAAI,QAAQ,IAAI,OAAOD,CAAC,EAAE;MACtB;MACA,IAAID,CAAC,GAAG,CAAC,EAAE;QACP,IAAI,CAACuG,OAAO,CAAC,CAAC,CAAC;MACnB,CAAC,MACI;QACD,IAAI,CAACpG,UAAU,CAACH,CAAC,EAAEE,CAAC,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC2D,OAAO,CAAC7D,CAAC,GAAG,CAAC,CAAC,EAAE;UACtB;UACA,IAAI,CAACgD,SAAS,CAACjD,UAAU,CAACgG,GAAG,CAAC1C,SAAS,CAACrD,CAAC,GAAG,CAAC,CAAC,EAAET,KAAK,EAAE,IAAI,CAAC;QAChE;QACA,IAAI,IAAI,CAACyC,MAAM,CAAC,CAAC,EAAE;UACf,IAAI,CAAC2G,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;QACzB,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAACvC,eAAe,CAACnG,CAAC,CAAC,EAAE;UAC7B,IAAI,CAAC0I,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;UACrB,IAAI,IAAI,CAACnH,SAAS,CAAC,CAAC,GAAGxB,CAAC,EAAE;YACtB,IAAI,CAACqB,KAAK,CAACtB,UAAU,CAACgG,GAAG,CAAC1C,SAAS,CAACrD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;UACrD;QACJ;MACJ;IACJ,CAAC,MACI;MACD;MACA,IAAI4D,CAAC,GAAG,EAAE;MACV,IAAI5C,CAAC,GAAGhB,CAAC,GAAG,CAAC;MACb4D,CAAC,CAAC9D,MAAM,GAAG,CAACE,CAAC,IAAI,CAAC,IAAI,CAAC;MACvBC,CAAC,CAAC2I,SAAS,CAAChF,CAAC,CAAC;MACd,IAAI5C,CAAC,GAAG,CAAC,EAAE;QACP4C,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,CAAC,IAAI5C,CAAC,IAAI,CAAE;MAC1B,CAAC,MACI;QACD4C,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACZ;MACA,IAAI,CAACxD,UAAU,CAACwD,CAAC,EAAE,GAAG,CAAC;IAC3B;EACJ,CAAC;EACD;EACA;EACA7D,UAAU,CAACM,SAAS,CAAC2C,SAAS,GAAG,UAAUhD,CAAC,EAAE6I,EAAE,EAAE/H,CAAC,EAAE;IACjD,IAAIC,CAAC;IACL,IAAI+H,CAAC;IACL,IAAIjI,CAAC,GAAGkD,IAAI,CAAClB,GAAG,CAAC7C,CAAC,CAACgB,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC;IAC7B,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MACpBD,CAAC,CAACC,CAAC,CAAC,GAAG8H,EAAE,CAAC,IAAI,CAAC9H,CAAC,CAAC,EAAEf,CAAC,CAACe,CAAC,CAAC,CAAC;IAC5B;IACA,IAAIf,CAAC,CAACgB,CAAC,GAAG,IAAI,CAACA,CAAC,EAAE;MACd8H,CAAC,GAAG9I,CAAC,CAACO,CAAC,GAAG,IAAI,CAACmB,EAAE;MACjB,KAAKX,CAAC,GAAGF,CAAC,EAAEE,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE,EAAED,CAAC,EAAE;QACzBD,CAAC,CAACC,CAAC,CAAC,GAAG8H,EAAE,CAAC,IAAI,CAAC9H,CAAC,CAAC,EAAE+H,CAAC,CAAC;MACzB;MACAhI,CAAC,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC;IAChB,CAAC,MACI;MACD8H,CAAC,GAAG,IAAI,CAACvI,CAAC,GAAG,IAAI,CAACmB,EAAE;MACpB,KAAKX,CAAC,GAAGF,CAAC,EAAEE,CAAC,GAAGf,CAAC,CAACgB,CAAC,EAAE,EAAED,CAAC,EAAE;QACtBD,CAAC,CAACC,CAAC,CAAC,GAAG8H,EAAE,CAACC,CAAC,EAAE9I,CAAC,CAACe,CAAC,CAAC,CAAC;MACtB;MACAD,CAAC,CAACE,CAAC,GAAGhB,CAAC,CAACgB,CAAC;IACb;IACAF,CAAC,CAACP,CAAC,GAAGsI,EAAE,CAAC,IAAI,CAACtI,CAAC,EAAEP,CAAC,CAACO,CAAC,CAAC;IACrBO,CAAC,CAAC+F,KAAK,CAAC,CAAC;EACb,CAAC;EACD;EACA;EACA9G,UAAU,CAACM,SAAS,CAAC6D,SAAS,GAAG,UAAUZ,CAAC,EAAEuF,EAAE,EAAE;IAC9C,IAAI/H,CAAC,GAAGf,UAAU,CAACgG,GAAG,CAAC1C,SAAS,CAACC,CAAC,CAAC;IACnC,IAAI,CAACN,SAAS,CAAClC,CAAC,EAAE+H,EAAE,EAAE/H,CAAC,CAAC;IACxB,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACiE,KAAK,GAAG,UAAUtE,CAAC,EAAEc,CAAC,EAAE;IACzC,IAAIC,CAAC,GAAG,CAAC;IACT,IAAIb,CAAC,GAAG,CAAC;IACT,IAAIW,CAAC,GAAGkD,IAAI,CAAClB,GAAG,CAAC7C,CAAC,CAACgB,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC;IAC7B,OAAOD,CAAC,GAAGF,CAAC,EAAE;MACVX,CAAC,IAAI,IAAI,CAACa,CAAC,CAAC,GAAGf,CAAC,CAACe,CAAC,CAAC;MACnBD,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGb,CAAC,GAAG,IAAI,CAACwB,EAAE;MACpBxB,CAAC,KAAK,IAAI,CAACgB,EAAE;IACjB;IACA,IAAIlB,CAAC,CAACgB,CAAC,GAAG,IAAI,CAACA,CAAC,EAAE;MACdd,CAAC,IAAIF,CAAC,CAACO,CAAC;MACR,OAAOQ,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE;QACfd,CAAC,IAAI,IAAI,CAACa,CAAC,CAAC;QACZD,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGb,CAAC,GAAG,IAAI,CAACwB,EAAE;QACpBxB,CAAC,KAAK,IAAI,CAACgB,EAAE;MACjB;MACAhB,CAAC,IAAI,IAAI,CAACK,CAAC;IACf,CAAC,MACI;MACDL,CAAC,IAAI,IAAI,CAACK,CAAC;MACX,OAAOQ,CAAC,GAAGf,CAAC,CAACgB,CAAC,EAAE;QACZd,CAAC,IAAIF,CAAC,CAACe,CAAC,CAAC;QACTD,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGb,CAAC,GAAG,IAAI,CAACwB,EAAE;QACpBxB,CAAC,KAAK,IAAI,CAACgB,EAAE;MACjB;MACAhB,CAAC,IAAIF,CAAC,CAACO,CAAC;IACZ;IACAO,CAAC,CAACP,CAAC,GAAIL,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG,CAAC;IACtB,IAAIA,CAAC,GAAG,CAAC,EAAE;MACPY,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGb,CAAC;IACd,CAAC,MACI,IAAIA,CAAC,GAAG,CAAC,CAAC,EAAE;MACbY,CAAC,CAACC,CAAC,EAAE,CAAC,GAAG,IAAI,CAACwB,EAAE,GAAGrC,CAAC;IACxB;IACAY,CAAC,CAACE,CAAC,GAAGD,CAAC;IACPD,CAAC,CAAC+F,KAAK,CAAC,CAAC;EACb,CAAC;EACD;EACA;EACA9G,UAAU,CAACM,SAAS,CAACqI,SAAS,GAAG,UAAUpF,CAAC,EAAE;IAC1C,IAAI,CAAC,IAAI,CAACtC,CAAC,CAAC,GAAG,IAAI,CAACoG,EAAE,CAAC,CAAC,EAAE9D,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACtC,CAAC,CAAC;IACpD,EAAE,IAAI,CAACA,CAAC;IACR,IAAI,CAAC6F,KAAK,CAAC,CAAC;EAChB,CAAC;EACD;EACA;EACA9G,UAAU,CAACM,SAAS,CAACsI,UAAU,GAAG,UAAUrF,CAAC,EAAEiC,CAAC,EAAE;IAC9C,IAAIjC,CAAC,IAAI,CAAC,EAAE;MACR;IACJ;IACA,OAAO,IAAI,CAACtC,CAAC,IAAIuE,CAAC,EAAE;MAChB,IAAI,CAAC,IAAI,CAACvE,CAAC,EAAE,CAAC,GAAG,CAAC;IACtB;IACA,IAAI,CAACuE,CAAC,CAAC,IAAIjC,CAAC;IACZ,OAAO,IAAI,CAACiC,CAAC,CAAC,IAAI,IAAI,CAAChD,EAAE,EAAE;MACvB,IAAI,CAACgD,CAAC,CAAC,IAAI,IAAI,CAAChD,EAAE;MAClB,IAAI,EAAEgD,CAAC,IAAI,IAAI,CAACvE,CAAC,EAAE;QACf,IAAI,CAAC,IAAI,CAACA,CAAC,EAAE,CAAC,GAAG,CAAC;MACtB;MACA,EAAE,IAAI,CAACuE,CAAC,CAAC;IACb;EACJ,CAAC;EACD;EACA;EACA;EACAxF,UAAU,CAACM,SAAS,CAAC0I,eAAe,GAAG,UAAU/I,CAAC,EAAEsD,CAAC,EAAExC,CAAC,EAAE;IACtD,IAAIC,CAAC,GAAGgD,IAAI,CAAClB,GAAG,CAAC,IAAI,CAAC7B,CAAC,GAAGhB,CAAC,CAACgB,CAAC,EAAEsC,CAAC,CAAC;IACjCxC,CAAC,CAACP,CAAC,GAAG,CAAC,CAAC,CAAC;IACTO,CAAC,CAACE,CAAC,GAAGD,CAAC;IACP,OAAOA,CAAC,GAAG,CAAC,EAAE;MACVD,CAAC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC;IACd;IACA,KAAK,IAAI+C,CAAC,GAAGhD,CAAC,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC,EAAED,CAAC,GAAG+C,CAAC,EAAE,EAAE/C,CAAC,EAAE;MACnCD,CAAC,CAACC,CAAC,GAAG,IAAI,CAACC,CAAC,CAAC,GAAG,IAAI,CAACoG,EAAE,CAAC,CAAC,EAAEpH,CAAC,CAACe,CAAC,CAAC,EAAED,CAAC,EAAEC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IACrD;IACA,KAAK,IAAI8C,CAAC,GAAGC,IAAI,CAAClB,GAAG,CAAC7C,CAAC,CAACgB,CAAC,EAAEsC,CAAC,CAAC,EAAEvC,CAAC,GAAG+C,CAAC,EAAE,EAAE/C,CAAC,EAAE;MACvC,IAAI,CAACqG,EAAE,CAAC,CAAC,EAAEpH,CAAC,CAACe,CAAC,CAAC,EAAED,CAAC,EAAEC,CAAC,EAAE,CAAC,EAAEuC,CAAC,GAAGvC,CAAC,CAAC;IACpC;IACAD,CAAC,CAAC+F,KAAK,CAAC,CAAC;EACb,CAAC;EACD;EACA;EACA;EACA9G,UAAU,CAACM,SAAS,CAAC2I,eAAe,GAAG,UAAUhJ,CAAC,EAAEsD,CAAC,EAAExC,CAAC,EAAE;IACtD,EAAEwC,CAAC;IACH,IAAIvC,CAAC,GAAGD,CAAC,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGhB,CAAC,CAACgB,CAAC,GAAGsC,CAAC;IAC9BxC,CAAC,CAACP,CAAC,GAAG,CAAC,CAAC,CAAC;IACT,OAAO,EAAEQ,CAAC,IAAI,CAAC,EAAE;MACbD,CAAC,CAACC,CAAC,CAAC,GAAG,CAAC;IACZ;IACA,KAAKA,CAAC,GAAGgD,IAAI,CAACjB,GAAG,CAACQ,CAAC,GAAG,IAAI,CAACtC,CAAC,EAAE,CAAC,CAAC,EAAED,CAAC,GAAGf,CAAC,CAACgB,CAAC,EAAE,EAAED,CAAC,EAAE;MAC5CD,CAAC,CAAC,IAAI,CAACE,CAAC,GAAGD,CAAC,GAAGuC,CAAC,CAAC,GAAG,IAAI,CAAC8D,EAAE,CAAC9D,CAAC,GAAGvC,CAAC,EAAEf,CAAC,CAACe,CAAC,CAAC,EAAED,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACE,CAAC,GAAGD,CAAC,GAAGuC,CAAC,CAAC;IACrE;IACAxC,CAAC,CAAC+F,KAAK,CAAC,CAAC;IACT/F,CAAC,CAACiG,SAAS,CAAC,CAAC,EAAEjG,CAAC,CAAC;EACrB,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACgG,MAAM,GAAG,UAAU/C,CAAC,EAAE;IACvC,IAAIA,CAAC,IAAI,CAAC,EAAE;MACR,OAAO,CAAC;IACZ;IACA,IAAI1C,CAAC,GAAG,IAAI,CAAC2B,EAAE,GAAGe,CAAC;IACnB,IAAIxC,CAAC,GAAI,IAAI,CAACP,CAAC,GAAG,CAAC,GAAI+C,CAAC,GAAG,CAAC,GAAG,CAAC;IAChC,IAAI,IAAI,CAACtC,CAAC,GAAG,CAAC,EAAE;MACZ,IAAIJ,CAAC,IAAI,CAAC,EAAE;QACRE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAGwC,CAAC;MACnB,CAAC,MACI;QACD,KAAK,IAAIvC,CAAC,GAAG,IAAI,CAACC,CAAC,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UAClCD,CAAC,GAAG,CAACF,CAAC,GAAGE,CAAC,GAAG,IAAI,CAACC,CAAC,CAAC,IAAIuC,CAAC;QAC7B;MACJ;IACJ;IACA,OAAOxC,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAACiG,WAAW,GAAG,UAAUtF,CAAC,EAAE;IAC5C,IAAIiI,EAAE,GAAG,IAAI,CAAC1E,QAAQ,CAACxE,UAAU,CAACgG,GAAG,CAAC;IACtC,IAAItF,CAAC,GAAGwI,EAAE,CAACvF,eAAe,CAAC,CAAC;IAC5B,IAAIjD,CAAC,IAAI,CAAC,EAAE;MACR,OAAO,KAAK;IAChB;IACA,IAAIK,CAAC,GAAGmI,EAAE,CAACxF,UAAU,CAAChD,CAAC,CAAC;IACxBO,CAAC,GAAIA,CAAC,GAAG,CAAC,IAAK,CAAC;IAChB,IAAIA,CAAC,GAAGpB,SAAS,CAACE,MAAM,EAAE;MACtBkB,CAAC,GAAGpB,SAAS,CAACE,MAAM;IACxB;IACA,IAAIE,CAAC,GAAGmB,GAAG,CAAC,CAAC;IACb,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACxB;MACAf,CAAC,CAACuG,OAAO,CAAC3G,SAAS,CAACmE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACmF,MAAM,CAAC,CAAC,GAAGtJ,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC;MAClE,IAAIqG,CAAC,GAAGnG,CAAC,CAAC8E,MAAM,CAAChE,CAAC,EAAE,IAAI,CAAC;MACzB,IAAIqF,CAAC,CAAC5E,SAAS,CAACxB,UAAU,CAACgG,GAAG,CAAC,IAAI,CAAC,IAAII,CAAC,CAAC5E,SAAS,CAAC0H,EAAE,CAAC,IAAI,CAAC,EAAE;QAC1D,IAAInF,CAAC,GAAG,CAAC;QACT,OAAOA,CAAC,EAAE,GAAGrD,CAAC,IAAI0F,CAAC,CAAC5E,SAAS,CAAC0H,EAAE,CAAC,IAAI,CAAC,EAAE;UACpC9C,CAAC,GAAGA,CAAC,CAACtE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;UACxB,IAAIsE,CAAC,CAAC5E,SAAS,CAACxB,UAAU,CAACgG,GAAG,CAAC,IAAI,CAAC,EAAE;YAClC,OAAO,KAAK;UAChB;QACJ;QACA,IAAII,CAAC,CAAC5E,SAAS,CAAC0H,EAAE,CAAC,IAAI,CAAC,EAAE;UACtB,OAAO,KAAK;QAChB;MACJ;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD;EACA;EACAlJ,UAAU,CAACM,SAAS,CAAC8I,MAAM,GAAG,YAAY;IACtC,IAAIrI,CAAC,GAAGK,GAAG,CAAC,CAAC;IACb,IAAI,CAACkG,QAAQ,CAACvG,CAAC,CAAC;IAChB,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAf,UAAU,CAACM,SAAS,CAAC+I,IAAI,GAAG,UAAUpJ,CAAC,EAAEqJ,QAAQ,EAAE;IAC/C,IAAIzF,CAAC,GAAI,IAAI,CAACrD,CAAC,GAAG,CAAC,GAAI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC4B,KAAK,CAAC,CAAC;IACnD,IAAI+D,CAAC,GAAInG,CAAC,CAACO,CAAC,GAAG,CAAC,GAAIP,CAAC,CAACQ,MAAM,CAAC,CAAC,GAAGR,CAAC,CAACoC,KAAK,CAAC,CAAC;IAC1C,IAAIwB,CAAC,CAACrC,SAAS,CAAC4E,CAAC,CAAC,GAAG,CAAC,EAAE;MACpB,IAAInF,CAAC,GAAG4C,CAAC;MACTA,CAAC,GAAGuC,CAAC;MACLA,CAAC,GAAGnF,CAAC;IACT;IACA,IAAID,CAAC,GAAG6C,CAAC,CAACF,eAAe,CAAC,CAAC;IAC3B,IAAIuB,CAAC,GAAGkB,CAAC,CAACzC,eAAe,CAAC,CAAC;IAC3B,IAAIuB,CAAC,GAAG,CAAC,EAAE;MACPoE,QAAQ,CAACzF,CAAC,CAAC;MACX;IACJ;IACA,IAAI7C,CAAC,GAAGkE,CAAC,EAAE;MACPA,CAAC,GAAGlE,CAAC;IACT;IACA,IAAIkE,CAAC,GAAG,CAAC,EAAE;MACPrB,CAAC,CAACL,QAAQ,CAAC0B,CAAC,EAAErB,CAAC,CAAC;MAChBuC,CAAC,CAAC5C,QAAQ,CAAC0B,CAAC,EAAEkB,CAAC,CAAC;IACpB;IACA;IACA,IAAImD,KAAK,GAAG,SAAAA,CAAA,EAAY;MACpB,IAAI,CAACvI,CAAC,GAAG6C,CAAC,CAACF,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE;QAC/BE,CAAC,CAACL,QAAQ,CAACxC,CAAC,EAAE6C,CAAC,CAAC;MACpB;MACA,IAAI,CAAC7C,CAAC,GAAGoF,CAAC,CAACzC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE;QAC/ByC,CAAC,CAAC5C,QAAQ,CAACxC,CAAC,EAAEoF,CAAC,CAAC;MACpB;MACA,IAAIvC,CAAC,CAACrC,SAAS,CAAC4E,CAAC,CAAC,IAAI,CAAC,EAAE;QACrBvC,CAAC,CAACvC,KAAK,CAAC8E,CAAC,EAAEvC,CAAC,CAAC;QACbA,CAAC,CAACL,QAAQ,CAAC,CAAC,EAAEK,CAAC,CAAC;MACpB,CAAC,MACI;QACDuC,CAAC,CAAC9E,KAAK,CAACuC,CAAC,EAAEuC,CAAC,CAAC;QACbA,CAAC,CAAC5C,QAAQ,CAAC,CAAC,EAAE4C,CAAC,CAAC;MACpB;MACA,IAAI,EAAEvC,CAAC,CAAClB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;QACnB,IAAIuC,CAAC,GAAG,CAAC,EAAE;UACPkB,CAAC,CAAC3C,QAAQ,CAACyB,CAAC,EAAEkB,CAAC,CAAC;QACpB;QACAoD,UAAU,CAAC,YAAY;UAAEF,QAAQ,CAAClD,CAAC,CAAC;QAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,MACI;QACDoD,UAAU,CAACD,KAAK,EAAE,CAAC,CAAC;MACxB;IACJ,CAAC;IACDC,UAAU,CAACD,KAAK,EAAE,EAAE,CAAC;EACzB,CAAC;EACD;EACAvJ,UAAU,CAACM,SAAS,CAACmJ,eAAe,GAAG,UAAUxJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEmJ,QAAQ,EAAE;IAChE,IAAI,QAAQ,IAAI,OAAOpJ,CAAC,EAAE;MACtB,IAAID,CAAC,GAAG,CAAC,EAAE;QACP,IAAI,CAACuG,OAAO,CAAC,CAAC,CAAC;MACnB,CAAC,MACI;QACD,IAAI,CAACpG,UAAU,CAACH,CAAC,EAAEE,CAAC,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC2D,OAAO,CAAC7D,CAAC,GAAG,CAAC,CAAC,EAAE;UACtB,IAAI,CAACgD,SAAS,CAACjD,UAAU,CAACgG,GAAG,CAAC1C,SAAS,CAACrD,CAAC,GAAG,CAAC,CAAC,EAAET,KAAK,EAAE,IAAI,CAAC;QAChE;QACA,IAAI,IAAI,CAACyC,MAAM,CAAC,CAAC,EAAE;UACf,IAAI,CAAC2G,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;QACzB;QACA,IAAIc,KAAK,GAAG,IAAI;QAChB,IAAIC,QAAQ,GAAG,SAAAA,CAAA,EAAY;UACvBD,KAAK,CAACd,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;UACtB,IAAIc,KAAK,CAACjI,SAAS,CAAC,CAAC,GAAGxB,CAAC,EAAE;YACvByJ,KAAK,CAACpI,KAAK,CAACtB,UAAU,CAACgG,GAAG,CAAC1C,SAAS,CAACrD,CAAC,GAAG,CAAC,CAAC,EAAEyJ,KAAK,CAAC;UACvD;UACA,IAAIA,KAAK,CAACrD,eAAe,CAACnG,CAAC,CAAC,EAAE;YAC1BsJ,UAAU,CAAC,YAAY;cAAEF,QAAQ,CAAC,CAAC;YAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,MACI;YACDE,UAAU,CAACG,QAAQ,EAAE,CAAC,CAAC;UAC3B;QACJ,CAAC;QACDH,UAAU,CAACG,QAAQ,EAAE,CAAC,CAAC;MAC3B;IACJ,CAAC,MACI;MACD,IAAI9F,CAAC,GAAG,EAAE;MACV,IAAI5C,CAAC,GAAGhB,CAAC,GAAG,CAAC;MACb4D,CAAC,CAAC9D,MAAM,GAAG,CAACE,CAAC,IAAI,CAAC,IAAI,CAAC;MACvBC,CAAC,CAAC2I,SAAS,CAAChF,CAAC,CAAC;MACd,IAAI5C,CAAC,GAAG,CAAC,EAAE;QACP4C,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,CAAC,IAAI5C,CAAC,IAAI,CAAE;MAC1B,CAAC,MACI;QACD4C,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACZ;MACA,IAAI,CAACxD,UAAU,CAACwD,CAAC,EAAE,GAAG,CAAC;IAC3B;EACJ,CAAC;EACD,OAAO7D,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU;AACnB;AACA;AACA,IAAIkG,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAAA,EAAG,CACnB;EACA;EACAA,OAAO,CAAC5F,SAAS,CAAC8E,OAAO,GAAG,UAAUvB,CAAC,EAAE;IACrC,OAAOA,CAAC;EACZ,CAAC;EACD;EACAqC,OAAO,CAAC5F,SAAS,CAACqF,MAAM,GAAG,UAAU9B,CAAC,EAAE;IACpC,OAAOA,CAAC;EACZ,CAAC;EACD;EACAqC,OAAO,CAAC5F,SAAS,CAACiF,KAAK,GAAG,UAAU1B,CAAC,EAAEuC,CAAC,EAAErF,CAAC,EAAE;IACzC8C,CAAC,CAACa,UAAU,CAAC0B,CAAC,EAAErF,CAAC,CAAC;EACtB,CAAC;EACD;EACAmF,OAAO,CAAC5F,SAAS,CAACgF,KAAK,GAAG,UAAUzB,CAAC,EAAE9C,CAAC,EAAE;IACtC8C,CAAC,CAACyD,QAAQ,CAACvG,CAAC,CAAC;EACjB,CAAC;EACD,OAAOmF,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ;AACA,IAAIhE,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAACpB,CAAC,EAAE;IAChB,IAAI,CAACA,CAAC,GAAGA,CAAC;EACd;EACA;EACAoB,OAAO,CAAC5B,SAAS,CAAC8E,OAAO,GAAG,UAAUvB,CAAC,EAAE;IACrC,IAAIA,CAAC,CAACrD,CAAC,GAAG,CAAC,IAAIqD,CAAC,CAACrC,SAAS,CAAC,IAAI,CAACV,CAAC,CAAC,IAAI,CAAC,EAAE;MACrC,OAAO+C,CAAC,CAACjC,GAAG,CAAC,IAAI,CAACd,CAAC,CAAC;IACxB,CAAC,MACI;MACD,OAAO+C,CAAC;IACZ;EACJ,CAAC;EACD;EACA3B,OAAO,CAAC5B,SAAS,CAACqF,MAAM,GAAG,UAAU9B,CAAC,EAAE;IACpC,OAAOA,CAAC;EACZ,CAAC;EACD;EACA3B,OAAO,CAAC5B,SAAS,CAACsJ,MAAM,GAAG,UAAU/F,CAAC,EAAE;IACpCA,CAAC,CAAChC,QAAQ,CAAC,IAAI,CAACf,CAAC,EAAE,IAAI,EAAE+C,CAAC,CAAC;EAC/B,CAAC;EACD;EACA3B,OAAO,CAAC5B,SAAS,CAACiF,KAAK,GAAG,UAAU1B,CAAC,EAAEuC,CAAC,EAAErF,CAAC,EAAE;IACzC8C,CAAC,CAACa,UAAU,CAAC0B,CAAC,EAAErF,CAAC,CAAC;IAClB,IAAI,CAAC6I,MAAM,CAAC7I,CAAC,CAAC;EAClB,CAAC;EACD;EACAmB,OAAO,CAAC5B,SAAS,CAACgF,KAAK,GAAG,UAAUzB,CAAC,EAAE9C,CAAC,EAAE;IACtC8C,CAAC,CAACyD,QAAQ,CAACvG,CAAC,CAAC;IACb,IAAI,CAAC6I,MAAM,CAAC7I,CAAC,CAAC;EAClB,CAAC;EACD,OAAOmB,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ;AACA;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACrB,CAAC,EAAE;IACnB,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,IAAI,CAAC+I,EAAE,GAAG/I,CAAC,CAACuH,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACyB,GAAG,GAAG,IAAI,CAACD,EAAE,GAAG,MAAM;IAC3B,IAAI,CAACE,GAAG,GAAG,IAAI,CAACF,EAAE,IAAI,EAAE;IACxB,IAAI,CAACG,EAAE,GAAG,CAAC,CAAC,IAAKlJ,CAAC,CAACK,EAAE,GAAG,EAAG,IAAI,CAAC;IAChC,IAAI,CAAC8I,GAAG,GAAG,CAAC,GAAGnJ,CAAC,CAACG,CAAC;EACtB;EACA;EACA;EACAkB,UAAU,CAAC7B,SAAS,CAAC8E,OAAO,GAAG,UAAUvB,CAAC,EAAE;IACxC,IAAI9C,CAAC,GAAGK,GAAG,CAAC,CAAC;IACbyC,CAAC,CAACtC,GAAG,CAAC,CAAC,CAACwF,SAAS,CAAC,IAAI,CAACjG,CAAC,CAACG,CAAC,EAAEF,CAAC,CAAC;IAC9BA,CAAC,CAACc,QAAQ,CAAC,IAAI,CAACf,CAAC,EAAE,IAAI,EAAEC,CAAC,CAAC;IAC3B,IAAI8C,CAAC,CAACrD,CAAC,GAAG,CAAC,IAAIO,CAAC,CAACS,SAAS,CAACxB,UAAU,CAACqB,IAAI,CAAC,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACP,CAAC,CAACQ,KAAK,CAACP,CAAC,EAAEA,CAAC,CAAC;IACtB;IACA,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAoB,UAAU,CAAC7B,SAAS,CAACqF,MAAM,GAAG,UAAU9B,CAAC,EAAE;IACvC,IAAI9C,CAAC,GAAGK,GAAG,CAAC,CAAC;IACbyC,CAAC,CAACvB,MAAM,CAACvB,CAAC,CAAC;IACX,IAAI,CAAC6I,MAAM,CAAC7I,CAAC,CAAC;IACd,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAoB,UAAU,CAAC7B,SAAS,CAACsJ,MAAM,GAAG,UAAU/F,CAAC,EAAE;IACvC,OAAOA,CAAC,CAAC5C,CAAC,IAAI,IAAI,CAACgJ,GAAG,EAAE;MACpB;MACApG,CAAC,CAACA,CAAC,CAAC5C,CAAC,EAAE,CAAC,GAAG,CAAC;IAChB;IACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,CAAC,CAACG,CAAC,EAAE,EAAED,CAAC,EAAE;MAC/B;MACA,IAAI+C,CAAC,GAAGF,CAAC,CAAC7C,CAAC,CAAC,GAAG,MAAM;MACrB,IAAIkJ,EAAE,GAAInG,CAAC,GAAG,IAAI,CAAC+F,GAAG,IAAI,CAAE/F,CAAC,GAAG,IAAI,CAACgG,GAAG,GAAG,CAAClG,CAAC,CAAC7C,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC8I,GAAG,GAAI,IAAI,CAACE,EAAE,KAAK,EAAE,CAAC,GAAInG,CAAC,CAAClC,EAAE;MAC7F;MACAoC,CAAC,GAAG/C,CAAC,GAAG,IAAI,CAACF,CAAC,CAACG,CAAC;MAChB4C,CAAC,CAACE,CAAC,CAAC,IAAI,IAAI,CAACjD,CAAC,CAACuG,EAAE,CAAC,CAAC,EAAE6C,EAAE,EAAErG,CAAC,EAAE7C,CAAC,EAAE,CAAC,EAAE,IAAI,CAACF,CAAC,CAACG,CAAC,CAAC;MAC3C;MACA,OAAO4C,CAAC,CAACE,CAAC,CAAC,IAAIF,CAAC,CAACrB,EAAE,EAAE;QACjBqB,CAAC,CAACE,CAAC,CAAC,IAAIF,CAAC,CAACrB,EAAE;QACZqB,CAAC,CAAC,EAAEE,CAAC,CAAC,EAAE;MACZ;IACJ;IACAF,CAAC,CAACiD,KAAK,CAAC,CAAC;IACTjD,CAAC,CAACmD,SAAS,CAAC,IAAI,CAAClG,CAAC,CAACG,CAAC,EAAE4C,CAAC,CAAC;IACxB,IAAIA,CAAC,CAACrC,SAAS,CAAC,IAAI,CAACV,CAAC,CAAC,IAAI,CAAC,EAAE;MAC1B+C,CAAC,CAACvC,KAAK,CAAC,IAAI,CAACR,CAAC,EAAE+C,CAAC,CAAC;IACtB;EACJ,CAAC;EACD;EACA;EACA1B,UAAU,CAAC7B,SAAS,CAACiF,KAAK,GAAG,UAAU1B,CAAC,EAAEuC,CAAC,EAAErF,CAAC,EAAE;IAC5C8C,CAAC,CAACa,UAAU,CAAC0B,CAAC,EAAErF,CAAC,CAAC;IAClB,IAAI,CAAC6I,MAAM,CAAC7I,CAAC,CAAC;EAClB,CAAC;EACD;EACA;EACAoB,UAAU,CAAC7B,SAAS,CAACgF,KAAK,GAAG,UAAUzB,CAAC,EAAE9C,CAAC,EAAE;IACzC8C,CAAC,CAACyD,QAAQ,CAACvG,CAAC,CAAC;IACb,IAAI,CAAC6I,MAAM,CAAC7I,CAAC,CAAC;EAClB,CAAC;EACD,OAAOoB,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ;AACA;AACA;AACA,IAAI8C,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAACnE,CAAC,EAAE;IAChB,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV;IACA,IAAI,CAAC4E,EAAE,GAAGtE,GAAG,CAAC,CAAC;IACf,IAAI,CAAC+I,EAAE,GAAG/I,GAAG,CAAC,CAAC;IACfpB,UAAU,CAACgG,GAAG,CAACe,SAAS,CAAC,CAAC,GAAGjG,CAAC,CAACG,CAAC,EAAE,IAAI,CAACyE,EAAE,CAAC;IAC1C,IAAI,CAAC0E,EAAE,GAAG,IAAI,CAAC1E,EAAE,CAACf,MAAM,CAAC7D,CAAC,CAAC;EAC/B;EACA;EACAmE,OAAO,CAAC3E,SAAS,CAAC8E,OAAO,GAAG,UAAUvB,CAAC,EAAE;IACrC,IAAIA,CAAC,CAACrD,CAAC,GAAG,CAAC,IAAIqD,CAAC,CAAC5C,CAAC,GAAG,CAAC,GAAG,IAAI,CAACH,CAAC,CAACG,CAAC,EAAE;MAC/B,OAAO4C,CAAC,CAACjC,GAAG,CAAC,IAAI,CAACd,CAAC,CAAC;IACxB,CAAC,MACI,IAAI+C,CAAC,CAACrC,SAAS,CAAC,IAAI,CAACV,CAAC,CAAC,GAAG,CAAC,EAAE;MAC9B,OAAO+C,CAAC;IACZ,CAAC,MACI;MACD,IAAI9C,CAAC,GAAGK,GAAG,CAAC,CAAC;MACbyC,CAAC,CAACvB,MAAM,CAACvB,CAAC,CAAC;MACX,IAAI,CAAC6I,MAAM,CAAC7I,CAAC,CAAC;MACd,OAAOA,CAAC;IACZ;EACJ,CAAC;EACD;EACAkE,OAAO,CAAC3E,SAAS,CAACqF,MAAM,GAAG,UAAU9B,CAAC,EAAE;IACpC,OAAOA,CAAC;EACZ,CAAC;EACD;EACA;EACAoB,OAAO,CAAC3E,SAAS,CAACsJ,MAAM,GAAG,UAAU/F,CAAC,EAAE;IACpCA,CAAC,CAACmD,SAAS,CAAC,IAAI,CAAClG,CAAC,CAACG,CAAC,GAAG,CAAC,EAAE,IAAI,CAACyE,EAAE,CAAC;IAClC,IAAI7B,CAAC,CAAC5C,CAAC,GAAG,IAAI,CAACH,CAAC,CAACG,CAAC,GAAG,CAAC,EAAE;MACpB4C,CAAC,CAAC5C,CAAC,GAAG,IAAI,CAACH,CAAC,CAACG,CAAC,GAAG,CAAC;MAClB4C,CAAC,CAACiD,KAAK,CAAC,CAAC;IACb;IACA,IAAI,CAACsD,EAAE,CAACnB,eAAe,CAAC,IAAI,CAACvD,EAAE,EAAE,IAAI,CAAC5E,CAAC,CAACG,CAAC,GAAG,CAAC,EAAE,IAAI,CAACkJ,EAAE,CAAC;IACvD,IAAI,CAACrJ,CAAC,CAACkI,eAAe,CAAC,IAAI,CAACmB,EAAE,EAAE,IAAI,CAACrJ,CAAC,CAACG,CAAC,GAAG,CAAC,EAAE,IAAI,CAACyE,EAAE,CAAC;IACtD,OAAO7B,CAAC,CAACrC,SAAS,CAAC,IAAI,CAACkE,EAAE,CAAC,GAAG,CAAC,EAAE;MAC7B7B,CAAC,CAAC+E,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC9H,CAAC,CAACG,CAAC,GAAG,CAAC,CAAC;IACjC;IACA4C,CAAC,CAACvC,KAAK,CAAC,IAAI,CAACoE,EAAE,EAAE7B,CAAC,CAAC;IACnB,OAAOA,CAAC,CAACrC,SAAS,CAAC,IAAI,CAACV,CAAC,CAAC,IAAI,CAAC,EAAE;MAC7B+C,CAAC,CAACvC,KAAK,CAAC,IAAI,CAACR,CAAC,EAAE+C,CAAC,CAAC;IACtB;EACJ,CAAC;EACD;EACA;EACAoB,OAAO,CAAC3E,SAAS,CAACiF,KAAK,GAAG,UAAU1B,CAAC,EAAEuC,CAAC,EAAErF,CAAC,EAAE;IACzC8C,CAAC,CAACa,UAAU,CAAC0B,CAAC,EAAErF,CAAC,CAAC;IAClB,IAAI,CAAC6I,MAAM,CAAC7I,CAAC,CAAC;EAClB,CAAC;EACD;EACA;EACAkE,OAAO,CAAC3E,SAAS,CAACgF,KAAK,GAAG,UAAUzB,CAAC,EAAE9C,CAAC,EAAE;IACtC8C,CAAC,CAACyD,QAAQ,CAACvG,CAAC,CAAC;IACb,IAAI,CAAC6I,MAAM,CAAC7I,CAAC,CAAC;EAClB,CAAC;EACD,OAAOkE,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ;AACA;AACA;AACA,OAAO,SAAS7D,GAAGA,CAAA,EAAG;EAAE,OAAO,IAAIpB,UAAU,CAAC,IAAI,CAAC;AAAE;AACrD,OAAO,SAASqK,WAAWA,CAACC,GAAG,EAAEvJ,CAAC,EAAE;EAChC,OAAO,IAAIf,UAAU,CAACsK,GAAG,EAAEvJ,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA,IAAIwJ,SAAS,GAAG,OAAOC,SAAS,KAAK,WAAW;AAChD,IAAID,SAAS,IAAI3K,IAAI,IAAK4K,SAAS,CAACC,OAAO,IAAI,6BAA8B,EAAE;EAC3E;EACA;EACA;EACAzK,UAAU,CAACM,SAAS,CAAC+G,EAAE,GAAG,SAASqD,GAAGA,CAAC1J,CAAC,EAAE6C,CAAC,EAAE2B,CAAC,EAAEzB,CAAC,EAAE5D,CAAC,EAAEoD,CAAC,EAAE;IACrD,IAAIoH,EAAE,GAAG9G,CAAC,GAAG,MAAM;IACnB,IAAI+G,EAAE,GAAG/G,CAAC,IAAI,EAAE;IAChB,OAAO,EAAEN,CAAC,IAAI,CAAC,EAAE;MACb,IAAIsH,CAAC,GAAG,IAAI,CAAC7J,CAAC,CAAC,GAAG,MAAM;MACxB,IAAI8J,CAAC,GAAG,IAAI,CAAC9J,CAAC,EAAE,CAAC,IAAI,EAAE;MACvB,IAAIF,CAAC,GAAG8J,EAAE,GAAGC,CAAC,GAAGC,CAAC,GAAGH,EAAE;MACvBE,CAAC,GAAGF,EAAE,GAAGE,CAAC,IAAI,CAAC/J,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,GAAG0E,CAAC,CAACzB,CAAC,CAAC,IAAI5D,CAAC,GAAG,UAAU,CAAC;MAC3DA,CAAC,GAAG,CAAC0K,CAAC,KAAK,EAAE,KAAK/J,CAAC,KAAK,EAAE,CAAC,GAAG8J,EAAE,GAAGE,CAAC,IAAI3K,CAAC,KAAK,EAAE,CAAC;MACjDqF,CAAC,CAACzB,CAAC,EAAE,CAAC,GAAG8G,CAAC,GAAG,UAAU;IAC3B;IACA,OAAO1K,CAAC;EACZ,CAAC;EACDT,KAAK,GAAG,EAAE;AACd,CAAC,MACI,IAAI6K,SAAS,IAAI3K,IAAI,IAAK4K,SAAS,CAACC,OAAO,IAAI,UAAW,EAAE;EAC7D;EACA;EACA;EACAzK,UAAU,CAACM,SAAS,CAAC+G,EAAE,GAAG,SAAS0D,GAAGA,CAAC/J,CAAC,EAAE6C,CAAC,EAAE2B,CAAC,EAAEzB,CAAC,EAAE5D,CAAC,EAAEoD,CAAC,EAAE;IACrD,OAAO,EAAEA,CAAC,IAAI,CAAC,EAAE;MACb,IAAIwC,CAAC,GAAGlC,CAAC,GAAG,IAAI,CAAC7C,CAAC,EAAE,CAAC,GAAGwE,CAAC,CAACzB,CAAC,CAAC,GAAG5D,CAAC;MAChCA,CAAC,GAAG6D,IAAI,CAACC,KAAK,CAAC8B,CAAC,GAAG,SAAS,CAAC;MAC7BP,CAAC,CAACzB,CAAC,EAAE,CAAC,GAAGgC,CAAC,GAAG,SAAS;IAC1B;IACA,OAAO5F,CAAC;EACZ,CAAC;EACDT,KAAK,GAAG,EAAE;AACd,CAAC,MACI;EAAE;EACH;EACA;EACAM,UAAU,CAACM,SAAS,CAAC+G,EAAE,GAAG,SAAS2D,GAAGA,CAAChK,CAAC,EAAE6C,CAAC,EAAE2B,CAAC,EAAEzB,CAAC,EAAE5D,CAAC,EAAEoD,CAAC,EAAE;IACrD,IAAIoH,EAAE,GAAG9G,CAAC,GAAG,MAAM;IACnB,IAAI+G,EAAE,GAAG/G,CAAC,IAAI,EAAE;IAChB,OAAO,EAAEN,CAAC,IAAI,CAAC,EAAE;MACb,IAAIsH,CAAC,GAAG,IAAI,CAAC7J,CAAC,CAAC,GAAG,MAAM;MACxB,IAAI8J,CAAC,GAAG,IAAI,CAAC9J,CAAC,EAAE,CAAC,IAAI,EAAE;MACvB,IAAIF,CAAC,GAAG8J,EAAE,GAAGC,CAAC,GAAGC,CAAC,GAAGH,EAAE;MACvBE,CAAC,GAAGF,EAAE,GAAGE,CAAC,IAAI,CAAC/J,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,GAAG0E,CAAC,CAACzB,CAAC,CAAC,GAAG5D,CAAC;MAC5CA,CAAC,GAAG,CAAC0K,CAAC,IAAI,EAAE,KAAK/J,CAAC,IAAI,EAAE,CAAC,GAAG8J,EAAE,GAAGE,CAAC;MAClCtF,CAAC,CAACzB,CAAC,EAAE,CAAC,GAAG8G,CAAC,GAAG,SAAS;IAC1B;IACA,OAAO1K,CAAC;EACZ,CAAC;EACDT,KAAK,GAAG,EAAE;AACd;AACAM,UAAU,CAACM,SAAS,CAACa,EAAE,GAAGzB,KAAK;AAC/BM,UAAU,CAACM,SAAS,CAACqB,EAAE,GAAI,CAAC,CAAC,IAAIjC,KAAK,IAAI,CAAE;AAC5CM,UAAU,CAACM,SAAS,CAACkC,EAAE,GAAI,CAAC,IAAI9C,KAAM;AACtC,IAAIuL,KAAK,GAAG,EAAE;AACdjL,UAAU,CAACM,SAAS,CAAC4H,EAAE,GAAGlE,IAAI,CAACiC,GAAG,CAAC,CAAC,EAAEgF,KAAK,CAAC;AAC5CjL,UAAU,CAACM,SAAS,CAACyH,EAAE,GAAGkD,KAAK,GAAGvL,KAAK;AACvCM,UAAU,CAACM,SAAS,CAAC0H,EAAE,GAAG,CAAC,GAAGtI,KAAK,GAAGuL,KAAK;AAC3C;AACA,IAAIC,KAAK,GAAG,EAAE;AACd,IAAIC,EAAE;AACN,IAAIC,EAAE;AACND,EAAE,GAAG,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;AACtB,KAAKD,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAE,EAAEA,EAAE,EAAE;EACxBF,KAAK,CAACC,EAAE,EAAE,CAAC,GAAGC,EAAE;AACpB;AACAD,EAAE,GAAG,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;AACtB,KAAKD,EAAE,GAAG,EAAE,EAAEA,EAAE,GAAG,EAAE,EAAE,EAAEA,EAAE,EAAE;EACzBF,KAAK,CAACC,EAAE,EAAE,CAAC,GAAGC,EAAE;AACpB;AACAD,EAAE,GAAG,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;AACtB,KAAKD,EAAE,GAAG,EAAE,EAAEA,EAAE,GAAG,EAAE,EAAE,EAAEA,EAAE,EAAE;EACzBF,KAAK,CAACC,EAAE,EAAE,CAAC,GAAGC,EAAE;AACpB;AACA,OAAO,SAASxE,KAAKA,CAACpG,CAAC,EAAEQ,CAAC,EAAE;EACxB,IAAIb,CAAC,GAAG+K,KAAK,CAAC1K,CAAC,CAAC6K,UAAU,CAACrK,CAAC,CAAC,CAAC;EAC9B,OAAQb,CAAC,IAAI,IAAI,GAAI,CAAC,CAAC,GAAGA,CAAC;AAC/B;AACA;AACA,OAAO,SAAS6E,GAAGA,CAAChE,CAAC,EAAE;EACnB,IAAID,CAAC,GAAGK,GAAG,CAAC,CAAC;EACbL,CAAC,CAACyF,OAAO,CAACxF,CAAC,CAAC;EACZ,OAAOD,CAAC;AACZ;AACA;AACA,OAAO,SAASW,KAAKA,CAACmC,CAAC,EAAE;EACrB,IAAI9C,CAAC,GAAG,CAAC;EACT,IAAIE,CAAC;EACL,IAAI,CAACA,CAAC,GAAG4C,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;IACrBA,CAAC,GAAG5C,CAAC;IACLF,CAAC,IAAI,EAAE;EACX;EACA,IAAI,CAACE,CAAC,GAAG4C,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IACnBA,CAAC,GAAG5C,CAAC;IACLF,CAAC,IAAI,CAAC;EACV;EACA,IAAI,CAACE,CAAC,GAAG4C,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IACnBA,CAAC,GAAG5C,CAAC;IACLF,CAAC,IAAI,CAAC;EACV;EACA,IAAI,CAACE,CAAC,GAAG4C,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IACnBA,CAAC,GAAG5C,CAAC;IACLF,CAAC,IAAI,CAAC;EACV;EACA,IAAI,CAACE,CAAC,GAAG4C,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IACnBA,CAAC,GAAG5C,CAAC;IACLF,CAAC,IAAI,CAAC;EACV;EACA,OAAOA,CAAC;AACZ;AACA;AACAf,UAAU,CAACqB,IAAI,GAAG2D,GAAG,CAAC,CAAC,CAAC;AACxBhF,UAAU,CAACgG,GAAG,GAAGhB,GAAG,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}