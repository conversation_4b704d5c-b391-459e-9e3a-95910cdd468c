{"ast": null, "code": "import hashClear from './_hashClear.js';\nimport hashDelete from './_hashDelete.js';\nimport hashGet from './_hashGet.js';\nimport hashHas from './_hashHas.js';\nimport hashSet from './_hashSet.js';\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n    length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\nexport default Hash;", "map": {"version": 3, "names": ["hashClear", "hashDelete", "hashGet", "hashHas", "hashSet", "Hash", "entries", "index", "length", "clear", "entry", "set", "prototype", "get", "has"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash-es/_Hash.js"], "sourcesContent": ["import hashClear from './_hashClear.js';\nimport hashDelete from './_hashDelete.js';\nimport hashGet from './_hashGet.js';\nimport hashHas from './_hashHas.js';\nimport hashSet from './_hashSet.js';\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\nexport default Hash;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,OAAO,EAAE;EACrB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA,OAAO,CAACE,MAAM;EAEjD,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,KAAK,GAAGJ,OAAO,CAACC,KAAK,CAAC;IAC1B,IAAI,CAACI,GAAG,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B;AACF;;AAEA;AACAL,IAAI,CAACO,SAAS,CAACH,KAAK,GAAGT,SAAS;AAChCK,IAAI,CAACO,SAAS,CAAC,QAAQ,CAAC,GAAGX,UAAU;AACrCI,IAAI,CAACO,SAAS,CAACC,GAAG,GAAGX,OAAO;AAC5BG,IAAI,CAACO,SAAS,CAACE,GAAG,GAAGX,OAAO;AAC5BE,IAAI,CAACO,SAAS,CAACD,GAAG,GAAGP,OAAO;AAE5B,eAAeC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}