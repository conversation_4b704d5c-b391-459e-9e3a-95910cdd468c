{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { gridTopLevelRowCountSelector } from \"../hooks/features/rows/gridRowsSelector.js\";\nimport { selectedGridRowsCountSelector } from \"../hooks/features/rowSelection/gridRowSelectionSelector.js\";\nimport { gridFilteredTopLevelRowCountSelector } from \"../hooks/features/filter/gridFilterSelector.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridSelectedRowCount } from \"./GridSelectedRowCount.js\";\nimport { GridFooterContainer } from \"./containers/GridFooterContainer.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridFooter = /*#__PURE__*/React.forwardRef(function GridFooter(props, ref) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const totalTopLevelRowCount = useGridSelector(apiRef, gridTopLevelRowCountSelector);\n  const selectedRowCount = useGridSelector(apiRef, selectedGridRowsCountSelector);\n  const visibleTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);\n  const selectedRowCountElement = !rootProps.hideFooterSelectedRowCount && selectedRowCount > 0 ? /*#__PURE__*/_jsx(GridSelectedRowCount, {\n    selectedRowCount: selectedRowCount\n  }) : /*#__PURE__*/_jsx(\"div\", {});\n  const rowCountElement = !rootProps.hideFooterRowCount && !rootProps.pagination ? /*#__PURE__*/_jsx(rootProps.slots.footerRowCount, _extends({}, rootProps.slotProps?.footerRowCount, {\n    rowCount: totalTopLevelRowCount,\n    visibleRowCount: visibleTopLevelRowCount\n  })) : null;\n  const paginationElement = rootProps.pagination && !rootProps.hideFooterPagination && rootProps.slots.pagination && /*#__PURE__*/_jsx(rootProps.slots.pagination, _extends({}, rootProps.slotProps?.pagination));\n  return /*#__PURE__*/_jsxs(GridFooterContainer, _extends({\n    ref: ref\n  }, props, {\n    children: [selectedRowCountElement, rowCountElement, paginationElement]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridFooter.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridFooter };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "useGridSelector", "gridTopLevelRowCountSelector", "selectedGridRowsCountSelector", "gridFilteredTopLevelRowCountSelector", "useGridApiContext", "GridSelectedRowCount", "Grid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useGridRootProps", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "props", "ref", "apiRef", "rootProps", "totalTopLevelRowCount", "selectedRowCount", "visibleTopLevelRowCount", "selectedRowCountElement", "hideFooterSelectedRowCount", "rowCountElement", "hideFooterRowCount", "pagination", "slots", "footerRowCount", "slotProps", "rowCount", "visibleRowCount", "paginationElement", "hideFooterPagination", "children", "process", "env", "NODE_ENV", "propTypes", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/GridFooter.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { gridTopLevelRowCountSelector } from \"../hooks/features/rows/gridRowsSelector.js\";\nimport { selectedGridRowsCountSelector } from \"../hooks/features/rowSelection/gridRowSelectionSelector.js\";\nimport { gridFilteredTopLevelRowCountSelector } from \"../hooks/features/filter/gridFilterSelector.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridSelectedRowCount } from \"./GridSelectedRowCount.js\";\nimport { GridFooterContainer } from \"./containers/GridFooterContainer.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridFooter = /*#__PURE__*/React.forwardRef(function GridFooter(props, ref) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const totalTopLevelRowCount = useGridSelector(apiRef, gridTopLevelRowCountSelector);\n  const selectedRowCount = useGridSelector(apiRef, selectedGridRowsCountSelector);\n  const visibleTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);\n  const selectedRowCountElement = !rootProps.hideFooterSelectedRowCount && selectedRowCount > 0 ? /*#__PURE__*/_jsx(GridSelectedRowCount, {\n    selectedRowCount: selectedRowCount\n  }) : /*#__PURE__*/_jsx(\"div\", {});\n  const rowCountElement = !rootProps.hideFooterRowCount && !rootProps.pagination ? /*#__PURE__*/_jsx(rootProps.slots.footerRowCount, _extends({}, rootProps.slotProps?.footerRowCount, {\n    rowCount: totalTopLevelRowCount,\n    visibleRowCount: visibleTopLevelRowCount\n  })) : null;\n  const paginationElement = rootProps.pagination && !rootProps.hideFooterPagination && rootProps.slots.pagination && /*#__PURE__*/_jsx(rootProps.slots.pagination, _extends({}, rootProps.slotProps?.pagination));\n  return /*#__PURE__*/_jsxs(GridFooterContainer, _extends({\n    ref: ref\n  }, props, {\n    children: [selectedRowCountElement, rowCountElement, paginationElement]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridFooter.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridFooter };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,4BAA4B,QAAQ,4CAA4C;AACzF,SAASC,6BAA6B,QAAQ,4DAA4D;AAC1G,SAASC,oCAAoC,QAAQ,gDAAgD;AACrG,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,UAAU,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,SAASD,UAAUA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC/E,MAAMC,MAAM,GAAGZ,iBAAiB,CAAC,CAAC;EAClC,MAAMa,SAAS,GAAGV,gBAAgB,CAAC,CAAC;EACpC,MAAMW,qBAAqB,GAAGlB,eAAe,CAACgB,MAAM,EAAEf,4BAA4B,CAAC;EACnF,MAAMkB,gBAAgB,GAAGnB,eAAe,CAACgB,MAAM,EAAEd,6BAA6B,CAAC;EAC/E,MAAMkB,uBAAuB,GAAGpB,eAAe,CAACgB,MAAM,EAAEb,oCAAoC,CAAC;EAC7F,MAAMkB,uBAAuB,GAAG,CAACJ,SAAS,CAACK,0BAA0B,IAAIH,gBAAgB,GAAG,CAAC,GAAG,aAAaV,IAAI,CAACJ,oBAAoB,EAAE;IACtIc,gBAAgB,EAAEA;EACpB,CAAC,CAAC,GAAG,aAAaV,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;EACjC,MAAMc,eAAe,GAAG,CAACN,SAAS,CAACO,kBAAkB,IAAI,CAACP,SAAS,CAACQ,UAAU,GAAG,aAAahB,IAAI,CAACQ,SAAS,CAACS,KAAK,CAACC,cAAc,EAAE9B,QAAQ,CAAC,CAAC,CAAC,EAAEoB,SAAS,CAACW,SAAS,EAAED,cAAc,EAAE;IACnLE,QAAQ,EAAEX,qBAAqB;IAC/BY,eAAe,EAAEV;EACnB,CAAC,CAAC,CAAC,GAAG,IAAI;EACV,MAAMW,iBAAiB,GAAGd,SAAS,CAACQ,UAAU,IAAI,CAACR,SAAS,CAACe,oBAAoB,IAAIf,SAAS,CAACS,KAAK,CAACD,UAAU,IAAI,aAAahB,IAAI,CAACQ,SAAS,CAACS,KAAK,CAACD,UAAU,EAAE5B,QAAQ,CAAC,CAAC,CAAC,EAAEoB,SAAS,CAACW,SAAS,EAAEH,UAAU,CAAC,CAAC;EAC/M,OAAO,aAAad,KAAK,CAACL,mBAAmB,EAAET,QAAQ,CAAC;IACtDkB,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,EAAE;IACRmB,QAAQ,EAAE,CAACZ,uBAAuB,EAAEE,eAAe,EAAEQ,iBAAiB;EACxE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,UAAU,CAACyB,SAAS,GAAG;EAC7D;EACA;EACA;EACA;EACAC,EAAE,EAAEvC,SAAS,CAACwC,SAAS,CAAC,CAACxC,SAAS,CAACyC,OAAO,CAACzC,SAAS,CAACwC,SAAS,CAAC,CAACxC,SAAS,CAAC0C,IAAI,EAAE1C,SAAS,CAAC2C,MAAM,EAAE3C,SAAS,CAAC4C,IAAI,CAAC,CAAC,CAAC,EAAE5C,SAAS,CAAC0C,IAAI,EAAE1C,SAAS,CAAC2C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,SAAS9B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}