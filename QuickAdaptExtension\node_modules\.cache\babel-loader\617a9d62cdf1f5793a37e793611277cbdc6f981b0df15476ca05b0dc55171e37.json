{"ast": null, "code": "/**\n * Checks if `value` is in the array cache.\n *\n * @private\n * @name has\n * @memberOf SetCache\n * @param {*} value The value to search for.\n * @returns {number} Returns `true` if `value` is found, else `false`.\n */\nfunction setCacheHas(value) {\n  return this.__data__.has(value);\n}\nmodule.exports = setCacheHas;", "map": {"version": 3, "names": ["setCacheHas", "value", "__data__", "has", "module", "exports"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash/_setCacheHas.js"], "sourcesContent": ["/**\n * Checks if `value` is in the array cache.\n *\n * @private\n * @name has\n * @memberOf SetCache\n * @param {*} value The value to search for.\n * @returns {number} Returns `true` if `value` is found, else `false`.\n */\nfunction setCacheHas(value) {\n  return this.__data__.has(value);\n}\n\nmodule.exports = setCacheHas;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACF,KAAK,CAAC;AACjC;AAEAG,MAAM,CAACC,OAAO,GAAGL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}