{"ast": null, "code": "// If no effect ran after this amount of time, we assume that the render was not committed by React\nconst CLEANUP_TIMER_LOOP_MILLIS = 1000;\nexport class TimerBasedCleanupTracking {\n  constructor() {\n    let timeout = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : CLEANUP_TIMER_LOOP_MILLIS;\n    this.timeouts = new Map();\n    this.cleanupTimeout = CLEANUP_TIMER_LOOP_MILLIS;\n    this.cleanupTimeout = timeout;\n  }\n  register(object, unsubscribe, unregisterToken) {\n    if (!this.timeouts) {\n      this.timeouts = new Map();\n    }\n    const timeout = setTimeout(() => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n      this.timeouts.delete(unregisterToken.cleanupToken);\n    }, this.cleanupTimeout);\n    this.timeouts.set(unregisterToken.cleanupToken, timeout);\n  }\n  unregister(unregisterToken) {\n    const timeout = this.timeouts.get(unregisterToken.cleanupToken);\n    if (timeout) {\n      this.timeouts.delete(unregisterToken.cleanupToken);\n      clearTimeout(timeout);\n    }\n  }\n  reset() {\n    if (this.timeouts) {\n      this.timeouts.forEach((value, key) => {\n        this.unregister({\n          cleanupToken: key\n        });\n      });\n      this.timeouts = undefined;\n    }\n  }\n}", "map": {"version": 3, "names": ["CLEANUP_TIMER_LOOP_MILLIS", "TimerBasedCleanupTracking", "constructor", "timeout", "arguments", "length", "undefined", "timeouts", "Map", "cleanupTimeout", "register", "object", "unsubscribe", "unregisterToken", "setTimeout", "delete", "cleanupToken", "set", "unregister", "get", "clearTimeout", "reset", "for<PERSON>ach", "value", "key"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/utils/cleanupTracking/TimerBasedCleanupTracking.js"], "sourcesContent": ["// If no effect ran after this amount of time, we assume that the render was not committed by React\nconst CLEANUP_TIMER_LOOP_MILLIS = 1000;\nexport class TimerBasedCleanupTracking {\n  constructor(timeout = CLEANUP_TIMER_LOOP_MILLIS) {\n    this.timeouts = new Map();\n    this.cleanupTimeout = CLEANUP_TIMER_LOOP_MILLIS;\n    this.cleanupTimeout = timeout;\n  }\n  register(object, unsubscribe, unregisterToken) {\n    if (!this.timeouts) {\n      this.timeouts = new Map();\n    }\n    const timeout = setTimeout(() => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n      this.timeouts.delete(unregisterToken.cleanupToken);\n    }, this.cleanupTimeout);\n    this.timeouts.set(unregisterToken.cleanupToken, timeout);\n  }\n  unregister(unregisterToken) {\n    const timeout = this.timeouts.get(unregisterToken.cleanupToken);\n    if (timeout) {\n      this.timeouts.delete(unregisterToken.cleanupToken);\n      clearTimeout(timeout);\n    }\n  }\n  reset() {\n    if (this.timeouts) {\n      this.timeouts.forEach((value, key) => {\n        this.unregister({\n          cleanupToken: key\n        });\n      });\n      this.timeouts = undefined;\n    }\n  }\n}"], "mappings": "AAAA;AACA,MAAMA,yBAAyB,GAAG,IAAI;AACtC,OAAO,MAAMC,yBAAyB,CAAC;EACrCC,WAAWA,CAAA,EAAsC;IAAA,IAArCC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGJ,yBAAyB;IAC7C,IAAI,CAACO,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,cAAc,GAAGT,yBAAyB;IAC/C,IAAI,CAACS,cAAc,GAAGN,OAAO;EAC/B;EACAO,QAAQA,CAACC,MAAM,EAAEC,WAAW,EAAEC,eAAe,EAAE;IAC7C,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B;IACA,MAAML,OAAO,GAAGW,UAAU,CAAC,MAAM;MAC/B,IAAI,OAAOF,WAAW,KAAK,UAAU,EAAE;QACrCA,WAAW,CAAC,CAAC;MACf;MACA,IAAI,CAACL,QAAQ,CAACQ,MAAM,CAACF,eAAe,CAACG,YAAY,CAAC;IACpD,CAAC,EAAE,IAAI,CAACP,cAAc,CAAC;IACvB,IAAI,CAACF,QAAQ,CAACU,GAAG,CAACJ,eAAe,CAACG,YAAY,EAAEb,OAAO,CAAC;EAC1D;EACAe,UAAUA,CAACL,eAAe,EAAE;IAC1B,MAAMV,OAAO,GAAG,IAAI,CAACI,QAAQ,CAACY,GAAG,CAACN,eAAe,CAACG,YAAY,CAAC;IAC/D,IAAIb,OAAO,EAAE;MACX,IAAI,CAACI,QAAQ,CAACQ,MAAM,CAACF,eAAe,CAACG,YAAY,CAAC;MAClDI,YAAY,CAACjB,OAAO,CAAC;IACvB;EACF;EACAkB,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACd,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACe,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QACpC,IAAI,CAACN,UAAU,CAAC;UACdF,YAAY,EAAEQ;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACjB,QAAQ,GAAGD,SAAS;IAC3B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}