{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridNoResultsOverlay = /*#__PURE__*/React.forwardRef(function GridNoResultsOverlay(props, ref) {\n  const apiRef = useGridApiContext();\n  const noResultsOverlayLabel = apiRef.current.getLocaleText('noResultsOverlayLabel');\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({\n    ref: ref\n  }, props, {\n    children: noResultsOverlayLabel\n  }));\n});", "map": {"version": 3, "names": ["_extends", "React", "useGridApiContext", "GridOverlay", "jsx", "_jsx", "GridNoResultsOverlay", "forwardRef", "props", "ref", "apiRef", "noResultsOverlayLabel", "current", "getLocaleText", "children"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/GridNoResultsOverlay.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridNoResultsOverlay = /*#__PURE__*/React.forwardRef(function GridNoResultsOverlay(props, ref) {\n  const apiRef = useGridApiContext();\n  const noResultsOverlayLabel = apiRef.current.getLocaleText('noResultsOverlayLabel');\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({\n    ref: ref\n  }, props, {\n    children: noResultsOverlayLabel\n  }));\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,oBAAoB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASD,oBAAoBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC1G,MAAMC,MAAM,GAAGR,iBAAiB,CAAC,CAAC;EAClC,MAAMS,qBAAqB,GAAGD,MAAM,CAACE,OAAO,CAACC,aAAa,CAAC,uBAAuB,CAAC;EACnF,OAAO,aAAaR,IAAI,CAACF,WAAW,EAAEH,QAAQ,CAAC;IAC7CS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,EAAE;IACRM,QAAQ,EAAEH;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}