{"ast": null, "code": "export function throttle(func) {\n  let wait = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 166;\n  let timeout;\n  let lastArgs;\n  const later = () => {\n    timeout = undefined;\n    func(...lastArgs);\n  };\n  function throttled() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    lastArgs = args;\n    if (timeout === undefined) {\n      timeout = setTimeout(later, wait);\n    }\n  }\n  throttled.clear = () => {\n    clearTimeout(timeout);\n    timeout = undefined;\n  };\n  return throttled;\n}", "map": {"version": 3, "names": ["throttle", "func", "wait", "arguments", "length", "undefined", "timeout", "lastArgs", "later", "throttled", "_len", "args", "Array", "_key", "setTimeout", "clear", "clearTimeout"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-internals/throttle/throttle.js"], "sourcesContent": ["export function throttle(func, wait = 166) {\n  let timeout;\n  let lastArgs;\n  const later = () => {\n    timeout = undefined;\n    func(...lastArgs);\n  };\n  function throttled(...args) {\n    lastArgs = args;\n    if (timeout === undefined) {\n      timeout = setTimeout(later, wait);\n    }\n  }\n  throttled.clear = () => {\n    clearTimeout(timeout);\n    timeout = undefined;\n  };\n  return throttled;\n}"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,IAAI,EAAc;EAAA,IAAZC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EACvC,IAAIG,OAAO;EACX,IAAIC,QAAQ;EACZ,MAAMC,KAAK,GAAGA,CAAA,KAAM;IAClBF,OAAO,GAAGD,SAAS;IACnBJ,IAAI,CAAC,GAAGM,QAAQ,CAAC;EACnB,CAAC;EACD,SAASE,SAASA,CAAA,EAAU;IAAA,SAAAC,IAAA,GAAAP,SAAA,CAAAC,MAAA,EAANO,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAV,SAAA,CAAAU,IAAA;IAAA;IACxBN,QAAQ,GAAGI,IAAI;IACf,IAAIL,OAAO,KAAKD,SAAS,EAAE;MACzBC,OAAO,GAAGQ,UAAU,CAACN,KAAK,EAAEN,IAAI,CAAC;IACnC;EACF;EACAO,SAAS,CAACM,KAAK,GAAG,MAAM;IACtBC,YAAY,CAACV,OAAO,CAAC;IACrBA,OAAO,GAAGD,SAAS;EACrB,CAAC;EACD,OAAOI,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}