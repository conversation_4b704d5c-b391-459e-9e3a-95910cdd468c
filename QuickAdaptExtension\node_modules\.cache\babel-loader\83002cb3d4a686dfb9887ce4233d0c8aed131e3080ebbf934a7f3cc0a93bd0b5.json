{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\", \"field\"],\n  _excluded2 = [\"id\", \"field\"];\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { GridEditModes, GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridRowsDataRowIdToIdLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridCellEditStartReasons, GridCellEditStopReasons } from \"../../../models/params/gridEditCellParams.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridCellEditing = (apiRef, props) => {\n  const [cellModesModel, setCellModesModel] = React.useState({});\n  const cellModesModelRef = React.useRef(cellModesModel);\n  const prevCellModesModel = React.useRef({});\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    cellModesModel: cellModesModelProp,\n    onCellModesModelChange\n  } = props;\n  const runIfEditModeIsCell = callback => function () {\n    if (props.editMode === GridEditModes.Cell) {\n      callback(...arguments);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, field, mode) => {\n    if (apiRef.current.getCellMode(id, field) !== mode) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (params.cellMode === GridCellModes.Edit) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('cellEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.View) {\n      return;\n    }\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.View) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStopReasons.cellFocusOut\n    });\n    apiRef.current.publishEvent('cellEditStop', newParams, event);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridCellEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        reason = event.shiftKey ? GridCellEditStopReasons.shiftTabKeyDown : GridCellEditStopReasons.tabKeyDown;\n        event.preventDefault(); // Prevent going to the next element in the tab sequence\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason\n        });\n        apiRef.current.publishEvent('cellEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'cell'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridCellEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridCellEditStartReasons.pasteKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridCellEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason,\n          key: event.key\n        });\n        apiRef.current.publishEvent('cellEditStart', newParams, event);\n      }\n    }\n  }, [apiRef]);\n  const handleCellEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startCellEditModeParams = {\n      id,\n      field\n    };\n    if (reason === GridCellEditStartReasons.printableKeyDown || reason === GridCellEditStartReasons.deleteKeyDown || reason === GridCellEditStartReasons.pasteKeyDown) {\n      startCellEditModeParams.deleteValue = true;\n    }\n    apiRef.current.startCellEditMode(startCellEditModeParams);\n  }, [apiRef]);\n  const handleCellEditStop = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    let cellToFocusAfter;\n    if (reason === GridCellEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridCellEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridCellEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopCellEditMode({\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  const runIfNoFieldErrors = callback => async function () {\n    if (callback) {\n      const {\n        id,\n        field\n      } = arguments.length <= 0 ? undefined : arguments[0];\n      const editRowsState = apiRef.current.state.editRows;\n      const hasFieldErrors = editRowsState[id][field]?.error;\n      if (!hasFieldErrors) {\n        callback(...arguments);\n      }\n    }\n  };\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', runIfEditModeIsCell(handleCellDoubleClick));\n  useGridApiEventHandler(apiRef, 'cellFocusOut', runIfEditModeIsCell(handleCellFocusOut));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfEditModeIsCell(handleCellKeyDown));\n  useGridApiEventHandler(apiRef, 'cellEditStart', runIfEditModeIsCell(handleCellEditStart));\n  useGridApiEventHandler(apiRef, 'cellEditStop', runIfEditModeIsCell(handleCellEditStop));\n  useGridApiOptionHandler(apiRef, 'cellEditStart', props.onCellEditStart);\n  useGridApiOptionHandler(apiRef, 'cellEditStop', runIfNoFieldErrors(props.onCellEditStop));\n  const getCellMode = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const isEditing = editingState[id] && editingState[id][field];\n    return isEditing ? GridCellModes.Edit : GridCellModes.View;\n  }, [apiRef]);\n  const updateCellModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.cellModesModel;\n    if (onCellModesModelChange && isNewModelDifferentFromProp) {\n      onCellModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.cellModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setCellModesModel(newModel);\n    cellModesModelRef.current = newModel;\n    apiRef.current.publishEvent('cellModesModelChange', newModel);\n  });\n  const updateFieldInCellModesModel = React.useCallback((id, field, newProps) => {\n    // We use the ref because it always contain the up-to-date value, different from the state\n    // that needs a rerender to reflect the new value\n    const newModel = _extends({}, cellModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newModel[id], {\n        [field]: _extends({}, newProps)\n      });\n    } else {\n      const _newModel$id = newModel[id],\n        otherFields = _objectWithoutPropertiesLoose(_newModel$id, [field].map(_toPropertyKey)); // Ensure that we have a new object, not a reference\n      newModel[id] = otherFields;\n      if (Object.keys(newModel[id]).length === 0) {\n        delete newModel[id];\n      }\n    }\n    updateCellModesModel(newModel);\n  }, [updateCellModesModel]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const startCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.View);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.Edit\n    }, other));\n  }, [throwIfNotEditable, throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStartCellEditMode = useEventCallback(params => {\n    const {\n      id,\n      field,\n      deleteValue,\n      initialValue\n    } = params;\n    let newValue = apiRef.current.getCellValue(id, field);\n    if (deleteValue) {\n      newValue = getDefaultCellValue(apiRef.current.getColumn(field));\n    } else if (initialValue) {\n      newValue = initialValue;\n    }\n    const newProps = {\n      value: newValue,\n      error: false,\n      isProcessingProps: false\n    };\n    updateOrDeleteFieldState(id, field, newProps);\n    apiRef.current.setCellFocus(id, field);\n  });\n  const stopCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.View\n    }, other));\n  }, [throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStopCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter = 'none'\n    } = params;\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    const finishCellEditMode = () => {\n      updateOrDeleteFieldState(id, field, null);\n      updateFieldInCellModesModel(id, field, null);\n      if (cellToFocusAfter !== 'none') {\n        apiRef.current.moveFocusToRelativeCell(id, field, cellToFocusAfter);\n      }\n    };\n    if (ignoreModifications) {\n      finishCellEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const {\n      error,\n      isProcessingProps\n    } = editingState[id][field];\n    if (error || isProcessingProps) {\n      // Attempt to change cell mode to \"view\" was not successful\n      // Update previous mode to allow another attempt\n      prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n      // Revert the mode in the cellModesModel prop back to \"edit\"\n      updateFieldInCellModesModel(id, field, {\n        mode: GridCellModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field);\n    if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/#server-side-persistence.'], 'error');\n        }\n      };\n      try {\n        const row = apiRef.current.getRow(id);\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishCellEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishCellEditMode();\n    }\n  });\n  const setCellEditingEditCellValue = React.useCallback(async params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef.current.state);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (column.preProcessEditCellProps) {\n      const hasChanged = value !== editingState[id][field].value;\n      newProps = _extends({}, newProps, {\n        isProcessingProps: true\n      });\n      updateOrDeleteFieldState(id, field, newProps);\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps,\n        hasChanged\n      }));\n    }\n\n    // Check again if the cell is in edit mode because the user may have\n    // discarded the changes while the props were being processed.\n    if (apiRef.current.getCellMode(id, field) === GridCellModes.View) {\n      return false;\n    }\n    editingState = gridEditRowsStateSelector(apiRef.current.state);\n    newProps = _extends({}, newProps, {\n      isProcessingProps: false\n    });\n    // We don't update the value with the one coming from the props pre-processing\n    // because when the promise resolves it may be already outdated. The only\n    // exception to this rule is when there's no pre-processing.\n    newProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n    updateOrDeleteFieldState(id, field, newProps);\n    editingState = gridEditRowsStateSelector(apiRef.current.state);\n    return !editingState[id]?.[field]?.error;\n  }, [apiRef, throwIfNotEditable, throwIfNotInMode, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromCellEditing = React.useCallback((id, field) => {\n    const column = apiRef.current.getColumn(field);\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id] || !editingState[id][field]) {\n      return apiRef.current.getRow(id);\n    }\n    const {\n      value\n    } = editingState[id][field];\n    return column.valueSetter ? column.valueSetter(value, row, column, apiRef) : _extends({}, row, {\n      [field]: value\n    });\n  }, [apiRef]);\n  const editingApi = {\n    getCellMode,\n    startCellEditMode,\n    stopCellEditMode\n  };\n  const editingPrivateApi = {\n    setCellEditingEditCellValue,\n    getRowWithUpdatedValuesFromCellEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (cellModesModelProp) {\n      updateCellModesModel(cellModesModelProp);\n    }\n  }, [cellModesModelProp, updateCellModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const idToIdLookup = gridRowsDataRowIdToIdLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopCellEditMode may change it later\n    const copyOfPrevCellModes = prevCellModesModel.current;\n    prevCellModesModel.current = deepClone(cellModesModel); // Do a deep-clone because the attributes might be changed later\n\n    Object.entries(cellModesModel).forEach(_ref => {\n      let [id, fields] = _ref;\n      Object.entries(fields).forEach(_ref2 => {\n        let [field, params] = _ref2;\n        const prevMode = copyOfPrevCellModes[id]?.[field]?.mode || GridCellModes.View;\n        const originalId = idToIdLookup[id] ?? id;\n        if (params.mode === GridCellModes.Edit && prevMode === GridCellModes.View) {\n          updateStateToStartCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        } else if (params.mode === GridCellModes.View && prevMode === GridCellModes.Edit) {\n          updateStateToStopCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        }\n      });\n    });\n  }, [apiRef, cellModesModel, updateStateToStartCellEditMode, updateStateToStopCellEditMode]);\n};", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_extends", "_excluded", "_excluded2", "React", "unstable_useEventCallback", "useEventCallback", "unstable_useEnhancedEffect", "useEnhancedEffect", "warnOnce", "useGridApiEventHandler", "useGridApiOptionHandler", "GridEditModes", "GridCellModes", "useGridApiMethod", "gridEditRowsStateSelector", "isPrintableKey", "isPasteShortcut", "gridRowsDataRowIdToIdLookupSelector", "deepClone", "GridCellEditStartReasons", "GridCellEditStopReasons", "getDefaultCellValue", "useGridCellEditing", "apiRef", "props", "cellModesModel", "setCellModesModel", "useState", "cellModesModelRef", "useRef", "prevCellModesModel", "processRowUpdate", "onProcessRowUpdateError", "cellModesModelProp", "onCellModesModelChange", "runIfEditModeIsCell", "callback", "editMode", "Cell", "arguments", "throwIfNotEditable", "useCallback", "id", "field", "params", "current", "getCellParams", "isCellEditable", "Error", "throwIfNotInMode", "mode", "getCellMode", "handleCellDoubleClick", "event", "isEditable", "cellMode", "Edit", "newParams", "reason", "cellDoubleClick", "publishEvent", "handleCellFocusOut", "View", "cellFocusOut", "handleCellKeyDown", "which", "key", "escapeKeyDown", "enterKeyDown", "shift<PERSON>ey", "shiftTabKeyDown", "tabKeyDown", "preventDefault", "canStartEditing", "unstable_applyPipeProcessors", "cellParams", "printableKeyDown", "pasteKeyDown", "deleteKeyDown", "handleCellEditStart", "startCellEditModeParams", "deleteValue", "startCellEditMode", "handleCellEditStop", "runPendingEditCellValueMutation", "cellToFocusAfter", "ignoreModifications", "stopCellEditMode", "runIfNoFieldErrors", "length", "undefined", "editRowsState", "state", "editRows", "hasFieldErrors", "error", "onCellEditStart", "onCellEditStop", "editingState", "isEditing", "updateCellModesModel", "newModel", "isNewModelDifferentFromProp", "api", "updateFieldInCellModesModel", "newProps", "_newModel$id", "otherFields", "map", "Object", "keys", "updateOrDeleteFieldState", "setState", "newEditingState", "forceUpdate", "other", "updateStateToStartCellEditMode", "initialValue", "newValue", "getCellValue", "getColumn", "value", "isProcessingProps", "setCellFocus", "updateStateToStopCellEditMode", "finishCellEditMode", "moveFocusToRelativeCell", "rowUpdate", "getRowWithUpdatedValuesFromCellEditing", "handleError", "errorThrown", "process", "env", "NODE_ENV", "row", "getRow", "Promise", "resolve", "rowId", "then", "finalRowUpdate", "updateRows", "catch", "setCellEditingEditCellValue", "debounceMs", "unstable_skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column", "parsedValue", "valueParser", "changeReason", "preProcessEditCellProps", "has<PERSON><PERSON>ed", "valueSetter", "editingApi", "editingPrivateApi", "useEffect", "idToIdLookup", "copyOfPrevCellModes", "entries", "for<PERSON>ach", "_ref", "fields", "_ref2", "prevMode", "originalId"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/editing/useGridCellEditing.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\", \"field\"],\n  _excluded2 = [\"id\", \"field\"];\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { GridEditModes, GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridRowsDataRowIdToIdLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridCellEditStartReasons, GridCellEditStopReasons } from \"../../../models/params/gridEditCellParams.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridCellEditing = (apiRef, props) => {\n  const [cellModesModel, setCellModesModel] = React.useState({});\n  const cellModesModelRef = React.useRef(cellModesModel);\n  const prevCellModesModel = React.useRef({});\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    cellModesModel: cellModesModelProp,\n    onCellModesModelChange\n  } = props;\n  const runIfEditModeIsCell = callback => (...args) => {\n    if (props.editMode === GridEditModes.Cell) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, field, mode) => {\n    if (apiRef.current.getCellMode(id, field) !== mode) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (params.cellMode === GridCellModes.Edit) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('cellEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.View) {\n      return;\n    }\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.View) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStopReasons.cellFocusOut\n    });\n    apiRef.current.publishEvent('cellEditStop', newParams, event);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridCellEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        reason = event.shiftKey ? GridCellEditStopReasons.shiftTabKeyDown : GridCellEditStopReasons.tabKeyDown;\n        event.preventDefault(); // Prevent going to the next element in the tab sequence\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason\n        });\n        apiRef.current.publishEvent('cellEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'cell'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridCellEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridCellEditStartReasons.pasteKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridCellEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason,\n          key: event.key\n        });\n        apiRef.current.publishEvent('cellEditStart', newParams, event);\n      }\n    }\n  }, [apiRef]);\n  const handleCellEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startCellEditModeParams = {\n      id,\n      field\n    };\n    if (reason === GridCellEditStartReasons.printableKeyDown || reason === GridCellEditStartReasons.deleteKeyDown || reason === GridCellEditStartReasons.pasteKeyDown) {\n      startCellEditModeParams.deleteValue = true;\n    }\n    apiRef.current.startCellEditMode(startCellEditModeParams);\n  }, [apiRef]);\n  const handleCellEditStop = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    let cellToFocusAfter;\n    if (reason === GridCellEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridCellEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridCellEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopCellEditMode({\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  const runIfNoFieldErrors = callback => async (...args) => {\n    if (callback) {\n      const {\n        id,\n        field\n      } = args[0];\n      const editRowsState = apiRef.current.state.editRows;\n      const hasFieldErrors = editRowsState[id][field]?.error;\n      if (!hasFieldErrors) {\n        callback(...args);\n      }\n    }\n  };\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', runIfEditModeIsCell(handleCellDoubleClick));\n  useGridApiEventHandler(apiRef, 'cellFocusOut', runIfEditModeIsCell(handleCellFocusOut));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfEditModeIsCell(handleCellKeyDown));\n  useGridApiEventHandler(apiRef, 'cellEditStart', runIfEditModeIsCell(handleCellEditStart));\n  useGridApiEventHandler(apiRef, 'cellEditStop', runIfEditModeIsCell(handleCellEditStop));\n  useGridApiOptionHandler(apiRef, 'cellEditStart', props.onCellEditStart);\n  useGridApiOptionHandler(apiRef, 'cellEditStop', runIfNoFieldErrors(props.onCellEditStop));\n  const getCellMode = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const isEditing = editingState[id] && editingState[id][field];\n    return isEditing ? GridCellModes.Edit : GridCellModes.View;\n  }, [apiRef]);\n  const updateCellModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.cellModesModel;\n    if (onCellModesModelChange && isNewModelDifferentFromProp) {\n      onCellModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.cellModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setCellModesModel(newModel);\n    cellModesModelRef.current = newModel;\n    apiRef.current.publishEvent('cellModesModelChange', newModel);\n  });\n  const updateFieldInCellModesModel = React.useCallback((id, field, newProps) => {\n    // We use the ref because it always contain the up-to-date value, different from the state\n    // that needs a rerender to reflect the new value\n    const newModel = _extends({}, cellModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newModel[id], {\n        [field]: _extends({}, newProps)\n      });\n    } else {\n      const _newModel$id = newModel[id],\n        otherFields = _objectWithoutPropertiesLoose(_newModel$id, [field].map(_toPropertyKey)); // Ensure that we have a new object, not a reference\n      newModel[id] = otherFields;\n      if (Object.keys(newModel[id]).length === 0) {\n        delete newModel[id];\n      }\n    }\n    updateCellModesModel(newModel);\n  }, [updateCellModesModel]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const startCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.View);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.Edit\n    }, other));\n  }, [throwIfNotEditable, throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStartCellEditMode = useEventCallback(params => {\n    const {\n      id,\n      field,\n      deleteValue,\n      initialValue\n    } = params;\n    let newValue = apiRef.current.getCellValue(id, field);\n    if (deleteValue) {\n      newValue = getDefaultCellValue(apiRef.current.getColumn(field));\n    } else if (initialValue) {\n      newValue = initialValue;\n    }\n    const newProps = {\n      value: newValue,\n      error: false,\n      isProcessingProps: false\n    };\n    updateOrDeleteFieldState(id, field, newProps);\n    apiRef.current.setCellFocus(id, field);\n  });\n  const stopCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.View\n    }, other));\n  }, [throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStopCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter = 'none'\n    } = params;\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    const finishCellEditMode = () => {\n      updateOrDeleteFieldState(id, field, null);\n      updateFieldInCellModesModel(id, field, null);\n      if (cellToFocusAfter !== 'none') {\n        apiRef.current.moveFocusToRelativeCell(id, field, cellToFocusAfter);\n      }\n    };\n    if (ignoreModifications) {\n      finishCellEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const {\n      error,\n      isProcessingProps\n    } = editingState[id][field];\n    if (error || isProcessingProps) {\n      // Attempt to change cell mode to \"view\" was not successful\n      // Update previous mode to allow another attempt\n      prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n      // Revert the mode in the cellModesModel prop back to \"edit\"\n      updateFieldInCellModesModel(id, field, {\n        mode: GridCellModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field);\n    if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/#server-side-persistence.'], 'error');\n        }\n      };\n      try {\n        const row = apiRef.current.getRow(id);\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishCellEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishCellEditMode();\n    }\n  });\n  const setCellEditingEditCellValue = React.useCallback(async params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef.current.state);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (column.preProcessEditCellProps) {\n      const hasChanged = value !== editingState[id][field].value;\n      newProps = _extends({}, newProps, {\n        isProcessingProps: true\n      });\n      updateOrDeleteFieldState(id, field, newProps);\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps,\n        hasChanged\n      }));\n    }\n\n    // Check again if the cell is in edit mode because the user may have\n    // discarded the changes while the props were being processed.\n    if (apiRef.current.getCellMode(id, field) === GridCellModes.View) {\n      return false;\n    }\n    editingState = gridEditRowsStateSelector(apiRef.current.state);\n    newProps = _extends({}, newProps, {\n      isProcessingProps: false\n    });\n    // We don't update the value with the one coming from the props pre-processing\n    // because when the promise resolves it may be already outdated. The only\n    // exception to this rule is when there's no pre-processing.\n    newProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n    updateOrDeleteFieldState(id, field, newProps);\n    editingState = gridEditRowsStateSelector(apiRef.current.state);\n    return !editingState[id]?.[field]?.error;\n  }, [apiRef, throwIfNotEditable, throwIfNotInMode, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromCellEditing = React.useCallback((id, field) => {\n    const column = apiRef.current.getColumn(field);\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id] || !editingState[id][field]) {\n      return apiRef.current.getRow(id);\n    }\n    const {\n      value\n    } = editingState[id][field];\n    return column.valueSetter ? column.valueSetter(value, row, column, apiRef) : _extends({}, row, {\n      [field]: value\n    });\n  }, [apiRef]);\n  const editingApi = {\n    getCellMode,\n    startCellEditMode,\n    stopCellEditMode\n  };\n  const editingPrivateApi = {\n    setCellEditingEditCellValue,\n    getRowWithUpdatedValuesFromCellEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (cellModesModelProp) {\n      updateCellModesModel(cellModesModelProp);\n    }\n  }, [cellModesModelProp, updateCellModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const idToIdLookup = gridRowsDataRowIdToIdLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopCellEditMode may change it later\n    const copyOfPrevCellModes = prevCellModesModel.current;\n    prevCellModesModel.current = deepClone(cellModesModel); // Do a deep-clone because the attributes might be changed later\n\n    Object.entries(cellModesModel).forEach(([id, fields]) => {\n      Object.entries(fields).forEach(([field, params]) => {\n        const prevMode = copyOfPrevCellModes[id]?.[field]?.mode || GridCellModes.View;\n        const originalId = idToIdLookup[id] ?? id;\n        if (params.mode === GridCellModes.Edit && prevMode === GridCellModes.View) {\n          updateStateToStartCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        } else if (params.mode === GridCellModes.View && prevMode === GridCellModes.Edit) {\n          updateStateToStopCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        }\n      });\n    });\n  }, [apiRef, cellModesModel, updateStateToStartCellEditMode, updateStateToStopCellEditMode]);\n};"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC;EAC/BC,UAAU,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,IAAIC,gBAAgB,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC3H,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,uCAAuC;AACvG,SAASC,aAAa,EAAEC,aAAa,QAAQ,qCAAqC;AAClF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,yBAAyB,QAAQ,2BAA2B;AACrE,SAASC,cAAc,EAAEC,eAAe,QAAQ,iCAAiC;AACjF,SAASC,mCAAmC,QAAQ,6BAA6B;AACjF,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,8CAA8C;AAChH,SAASC,mBAAmB,QAAQ,YAAY;AAChD,OAAO,MAAMC,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACnD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,KAAK,CAACwB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAMC,iBAAiB,GAAGzB,KAAK,CAAC0B,MAAM,CAACJ,cAAc,CAAC;EACtD,MAAMK,kBAAkB,GAAG3B,KAAK,CAAC0B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3C,MAAM;IACJE,gBAAgB;IAChBC,uBAAuB;IACvBP,cAAc,EAAEQ,kBAAkB;IAClCC;EACF,CAAC,GAAGV,KAAK;EACT,MAAMW,mBAAmB,GAAGC,QAAQ,IAAI,YAAa;IACnD,IAAIZ,KAAK,CAACa,QAAQ,KAAK1B,aAAa,CAAC2B,IAAI,EAAE;MACzCF,QAAQ,CAAC,GAAAG,SAAO,CAAC;IACnB;EACF,CAAC;EACD,MAAMC,kBAAkB,GAAGrC,KAAK,CAACsC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC1D,MAAMC,MAAM,GAAGrB,MAAM,CAACsB,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC;IACtD,IAAI,CAACpB,MAAM,CAACsB,OAAO,CAACE,cAAc,CAACH,MAAM,CAAC,EAAE;MAC1C,MAAM,IAAII,KAAK,CAAC,2BAA2BN,EAAE,cAAcC,KAAK,mBAAmB,CAAC;IACtF;EACF,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EACZ,MAAM0B,gBAAgB,GAAG9C,KAAK,CAACsC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAEO,IAAI,KAAK;IAC9D,IAAI3B,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACT,EAAE,EAAEC,KAAK,CAAC,KAAKO,IAAI,EAAE;MAClD,MAAM,IAAIF,KAAK,CAAC,2BAA2BN,EAAE,cAAcC,KAAK,cAAcO,IAAI,QAAQ,CAAC;IAC7F;EACF,CAAC,EAAE,CAAC3B,MAAM,CAAC,CAAC;EACZ,MAAM6B,qBAAqB,GAAGjD,KAAK,CAACsC,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IACjE,IAAI,CAACT,MAAM,CAACU,UAAU,EAAE;MACtB;IACF;IACA,IAAIV,MAAM,CAACW,QAAQ,KAAK3C,aAAa,CAAC4C,IAAI,EAAE;MAC1C;IACF;IACA,MAAMC,SAAS,GAAGzD,QAAQ,CAAC,CAAC,CAAC,EAAE4C,MAAM,EAAE;MACrCc,MAAM,EAAEvC,wBAAwB,CAACwC;IACnC,CAAC,CAAC;IACFpC,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,eAAe,EAAEH,SAAS,EAAEJ,KAAK,CAAC;EAChE,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMsC,kBAAkB,GAAG1D,KAAK,CAACsC,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IAC9D,IAAIT,MAAM,CAACW,QAAQ,KAAK3C,aAAa,CAACkD,IAAI,EAAE;MAC1C;IACF;IACA,IAAIvC,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACP,MAAM,CAACF,EAAE,EAAEE,MAAM,CAACD,KAAK,CAAC,KAAK/B,aAAa,CAACkD,IAAI,EAAE;MAC9E;IACF;IACA,MAAML,SAAS,GAAGzD,QAAQ,CAAC,CAAC,CAAC,EAAE4C,MAAM,EAAE;MACrCc,MAAM,EAAEtC,uBAAuB,CAAC2C;IAClC,CAAC,CAAC;IACFxC,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEJ,KAAK,CAAC;EAC/D,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMyC,iBAAiB,GAAG7D,KAAK,CAACsC,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IAC7D,IAAIT,MAAM,CAACW,QAAQ,KAAK3C,aAAa,CAAC4C,IAAI,EAAE;MAC1C;MACA;MACA,IAAIH,KAAK,CAACY,KAAK,KAAK,GAAG,EAAE;QACvB;MACF;MACA,IAAIP,MAAM;MACV,IAAIL,KAAK,CAACa,GAAG,KAAK,QAAQ,EAAE;QAC1BR,MAAM,GAAGtC,uBAAuB,CAAC+C,aAAa;MAChD,CAAC,MAAM,IAAId,KAAK,CAACa,GAAG,KAAK,OAAO,EAAE;QAChCR,MAAM,GAAGtC,uBAAuB,CAACgD,YAAY;MAC/C,CAAC,MAAM,IAAIf,KAAK,CAACa,GAAG,KAAK,KAAK,EAAE;QAC9BR,MAAM,GAAGL,KAAK,CAACgB,QAAQ,GAAGjD,uBAAuB,CAACkD,eAAe,GAAGlD,uBAAuB,CAACmD,UAAU;QACtGlB,KAAK,CAACmB,cAAc,CAAC,CAAC,CAAC,CAAC;MAC1B;MACA,IAAId,MAAM,EAAE;QACV,MAAMD,SAAS,GAAGzD,QAAQ,CAAC,CAAC,CAAC,EAAE4C,MAAM,EAAE;UACrCc;QACF,CAAC,CAAC;QACFnC,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEJ,KAAK,CAAC;MAC/D;IACF,CAAC,MAAM,IAAIT,MAAM,CAACU,UAAU,EAAE;MAC5B,IAAII,MAAM;MACV,MAAMe,eAAe,GAAGlD,MAAM,CAACsB,OAAO,CAAC6B,4BAA4B,CAAC,iBAAiB,EAAE,IAAI,EAAE;QAC3FrB,KAAK;QACLsB,UAAU,EAAE/B,MAAM;QAClBP,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAACoC,eAAe,EAAE;QACpB;MACF;MACA,IAAI1D,cAAc,CAACsC,KAAK,CAAC,EAAE;QACzBK,MAAM,GAAGvC,wBAAwB,CAACyD,gBAAgB;MACpD,CAAC,MAAM,IAAI5D,eAAe,CAACqC,KAAK,CAAC,EAAE;QACjCK,MAAM,GAAGvC,wBAAwB,CAAC0D,YAAY;MAChD,CAAC,MAAM,IAAIxB,KAAK,CAACa,GAAG,KAAK,OAAO,EAAE;QAChCR,MAAM,GAAGvC,wBAAwB,CAACiD,YAAY;MAChD,CAAC,MAAM,IAAIf,KAAK,CAACa,GAAG,KAAK,WAAW,IAAIb,KAAK,CAACa,GAAG,KAAK,QAAQ,EAAE;QAC9DR,MAAM,GAAGvC,wBAAwB,CAAC2D,aAAa;MACjD;MACA,IAAIpB,MAAM,EAAE;QACV,MAAMD,SAAS,GAAGzD,QAAQ,CAAC,CAAC,CAAC,EAAE4C,MAAM,EAAE;UACrCc,MAAM;UACNQ,GAAG,EAAEb,KAAK,CAACa;QACb,CAAC,CAAC;QACF3C,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,eAAe,EAAEH,SAAS,EAAEJ,KAAK,CAAC;MAChE;IACF;EACF,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMwD,mBAAmB,GAAG5E,KAAK,CAACsC,WAAW,CAACG,MAAM,IAAI;IACtD,MAAM;MACJF,EAAE;MACFC,KAAK;MACLe;IACF,CAAC,GAAGd,MAAM;IACV,MAAMoC,uBAAuB,GAAG;MAC9BtC,EAAE;MACFC;IACF,CAAC;IACD,IAAIe,MAAM,KAAKvC,wBAAwB,CAACyD,gBAAgB,IAAIlB,MAAM,KAAKvC,wBAAwB,CAAC2D,aAAa,IAAIpB,MAAM,KAAKvC,wBAAwB,CAAC0D,YAAY,EAAE;MACjKG,uBAAuB,CAACC,WAAW,GAAG,IAAI;IAC5C;IACA1D,MAAM,CAACsB,OAAO,CAACqC,iBAAiB,CAACF,uBAAuB,CAAC;EAC3D,CAAC,EAAE,CAACzD,MAAM,CAAC,CAAC;EACZ,MAAM4D,kBAAkB,GAAGhF,KAAK,CAACsC,WAAW,CAACG,MAAM,IAAI;IACrD,MAAM;MACJF,EAAE;MACFC,KAAK;MACLe;IACF,CAAC,GAAGd,MAAM;IACVrB,MAAM,CAACsB,OAAO,CAACuC,+BAA+B,CAAC1C,EAAE,EAAEC,KAAK,CAAC;IACzD,IAAI0C,gBAAgB;IACpB,IAAI3B,MAAM,KAAKtC,uBAAuB,CAACgD,YAAY,EAAE;MACnDiB,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI3B,MAAM,KAAKtC,uBAAuB,CAACmD,UAAU,EAAE;MACxDc,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI3B,MAAM,KAAKtC,uBAAuB,CAACkD,eAAe,EAAE;MAC7De,gBAAgB,GAAG,MAAM;IAC3B;IACA,MAAMC,mBAAmB,GAAG5B,MAAM,KAAK,eAAe;IACtDnC,MAAM,CAACsB,OAAO,CAAC0C,gBAAgB,CAAC;MAC9B7C,EAAE;MACFC,KAAK;MACL2C,mBAAmB;MACnBD;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9D,MAAM,CAAC,CAAC;EACZ,MAAMiE,kBAAkB,GAAGpD,QAAQ,IAAI,kBAAmB;IACxD,IAAIA,QAAQ,EAAE;MACZ,MAAM;QACJM,EAAE;QACFC;MACF,CAAC,GAAAJ,SAAA,CAAAkD,MAAA,QAAAC,SAAA,GAAAnD,SAAA,GAAU;MACX,MAAMoD,aAAa,GAAGpE,MAAM,CAACsB,OAAO,CAAC+C,KAAK,CAACC,QAAQ;MACnD,MAAMC,cAAc,GAAGH,aAAa,CAACjD,EAAE,CAAC,CAACC,KAAK,CAAC,EAAEoD,KAAK;MACtD,IAAI,CAACD,cAAc,EAAE;QACnB1D,QAAQ,CAAC,GAAAG,SAAO,CAAC;MACnB;IACF;EACF,CAAC;EACD9B,sBAAsB,CAACc,MAAM,EAAE,iBAAiB,EAAEY,mBAAmB,CAACiB,qBAAqB,CAAC,CAAC;EAC7F3C,sBAAsB,CAACc,MAAM,EAAE,cAAc,EAAEY,mBAAmB,CAAC0B,kBAAkB,CAAC,CAAC;EACvFpD,sBAAsB,CAACc,MAAM,EAAE,aAAa,EAAEY,mBAAmB,CAAC6B,iBAAiB,CAAC,CAAC;EACrFvD,sBAAsB,CAACc,MAAM,EAAE,eAAe,EAAEY,mBAAmB,CAAC4C,mBAAmB,CAAC,CAAC;EACzFtE,sBAAsB,CAACc,MAAM,EAAE,cAAc,EAAEY,mBAAmB,CAACgD,kBAAkB,CAAC,CAAC;EACvFzE,uBAAuB,CAACa,MAAM,EAAE,eAAe,EAAEC,KAAK,CAACwE,eAAe,CAAC;EACvEtF,uBAAuB,CAACa,MAAM,EAAE,cAAc,EAAEiE,kBAAkB,CAAChE,KAAK,CAACyE,cAAc,CAAC,CAAC;EACzF,MAAM9C,WAAW,GAAGhD,KAAK,CAACsC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IACnD,MAAMuD,YAAY,GAAGpF,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAAC+C,KAAK,CAAC;IACpE,MAAMO,SAAS,GAAGD,YAAY,CAACxD,EAAE,CAAC,IAAIwD,YAAY,CAACxD,EAAE,CAAC,CAACC,KAAK,CAAC;IAC7D,OAAOwD,SAAS,GAAGvF,aAAa,CAAC4C,IAAI,GAAG5C,aAAa,CAACkD,IAAI;EAC5D,CAAC,EAAE,CAACvC,MAAM,CAAC,CAAC;EACZ,MAAM6E,oBAAoB,GAAG/F,gBAAgB,CAACgG,QAAQ,IAAI;IACxD,MAAMC,2BAA2B,GAAGD,QAAQ,KAAK7E,KAAK,CAACC,cAAc;IACrE,IAAIS,sBAAsB,IAAIoE,2BAA2B,EAAE;MACzDpE,sBAAsB,CAACmE,QAAQ,EAAE;QAC/BE,GAAG,EAAEhF,MAAM,CAACsB;MACd,CAAC,CAAC;IACJ;IACA,IAAIrB,KAAK,CAACC,cAAc,IAAI6E,2BAA2B,EAAE;MACvD,OAAO,CAAC;IACV;IACA5E,iBAAiB,CAAC2E,QAAQ,CAAC;IAC3BzE,iBAAiB,CAACiB,OAAO,GAAGwD,QAAQ;IACpC9E,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,sBAAsB,EAAEyC,QAAQ,CAAC;EAC/D,CAAC,CAAC;EACF,MAAMG,2BAA2B,GAAGrG,KAAK,CAACsC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAE8D,QAAQ,KAAK;IAC7E;IACA;IACA,MAAMJ,QAAQ,GAAGrG,QAAQ,CAAC,CAAC,CAAC,EAAE4B,iBAAiB,CAACiB,OAAO,CAAC;IACxD,IAAI4D,QAAQ,KAAK,IAAI,EAAE;MACrBJ,QAAQ,CAAC3D,EAAE,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,EAAEqG,QAAQ,CAAC3D,EAAE,CAAC,EAAE;QACxC,CAACC,KAAK,GAAG3C,QAAQ,CAAC,CAAC,CAAC,EAAEyG,QAAQ;MAChC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMC,YAAY,GAAGL,QAAQ,CAAC3D,EAAE,CAAC;QAC/BiE,WAAW,GAAG7G,6BAA6B,CAAC4G,YAAY,EAAE,CAAC/D,KAAK,CAAC,CAACiE,GAAG,CAAC7G,cAAc,CAAC,CAAC,CAAC,CAAC;MAC1FsG,QAAQ,CAAC3D,EAAE,CAAC,GAAGiE,WAAW;MAC1B,IAAIE,MAAM,CAACC,IAAI,CAACT,QAAQ,CAAC3D,EAAE,CAAC,CAAC,CAAC+C,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAOY,QAAQ,CAAC3D,EAAE,CAAC;MACrB;IACF;IACA0D,oBAAoB,CAACC,QAAQ,CAAC;EAChC,CAAC,EAAE,CAACD,oBAAoB,CAAC,CAAC;EAC1B,MAAMW,wBAAwB,GAAG5G,KAAK,CAACsC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAE8D,QAAQ,KAAK;IAC1ElF,MAAM,CAACsB,OAAO,CAACmE,QAAQ,CAACpB,KAAK,IAAI;MAC/B,MAAMqB,eAAe,GAAGjH,QAAQ,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAACC,QAAQ,CAAC;MACpD,IAAIY,QAAQ,KAAK,IAAI,EAAE;QACrBQ,eAAe,CAACvE,EAAE,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,EAAEiH,eAAe,CAACvE,EAAE,CAAC,EAAE;UACtD,CAACC,KAAK,GAAG3C,QAAQ,CAAC,CAAC,CAAC,EAAEyG,QAAQ;QAChC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAOQ,eAAe,CAACvE,EAAE,CAAC,CAACC,KAAK,CAAC;QACjC,IAAIkE,MAAM,CAACC,IAAI,CAACG,eAAe,CAACvE,EAAE,CAAC,CAAC,CAAC+C,MAAM,KAAK,CAAC,EAAE;UACjD,OAAOwB,eAAe,CAACvE,EAAE,CAAC;QAC5B;MACF;MACA,OAAO1C,QAAQ,CAAC,CAAC,CAAC,EAAE4F,KAAK,EAAE;QACzBC,QAAQ,EAAEoB;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF1F,MAAM,CAACsB,OAAO,CAACqE,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAAC3F,MAAM,CAAC,CAAC;EACZ,MAAM2D,iBAAiB,GAAG/E,KAAK,CAACsC,WAAW,CAACG,MAAM,IAAI;IACpD,MAAM;QACFF,EAAE;QACFC;MACF,CAAC,GAAGC,MAAM;MACVuE,KAAK,GAAGrH,6BAA6B,CAAC8C,MAAM,EAAE3C,SAAS,CAAC;IAC1DuC,kBAAkB,CAACE,EAAE,EAAEC,KAAK,CAAC;IAC7BM,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAACkD,IAAI,CAAC;IAC/C0C,2BAA2B,CAAC9D,EAAE,EAAEC,KAAK,EAAE3C,QAAQ,CAAC;MAC9CkD,IAAI,EAAEtC,aAAa,CAAC4C;IACtB,CAAC,EAAE2D,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC3E,kBAAkB,EAAES,gBAAgB,EAAEuD,2BAA2B,CAAC,CAAC;EACvE,MAAMY,8BAA8B,GAAG/G,gBAAgB,CAACuC,MAAM,IAAI;IAChE,MAAM;MACJF,EAAE;MACFC,KAAK;MACLsC,WAAW;MACXoC;IACF,CAAC,GAAGzE,MAAM;IACV,IAAI0E,QAAQ,GAAG/F,MAAM,CAACsB,OAAO,CAAC0E,YAAY,CAAC7E,EAAE,EAAEC,KAAK,CAAC;IACrD,IAAIsC,WAAW,EAAE;MACfqC,QAAQ,GAAGjG,mBAAmB,CAACE,MAAM,CAACsB,OAAO,CAAC2E,SAAS,CAAC7E,KAAK,CAAC,CAAC;IACjE,CAAC,MAAM,IAAI0E,YAAY,EAAE;MACvBC,QAAQ,GAAGD,YAAY;IACzB;IACA,MAAMZ,QAAQ,GAAG;MACfgB,KAAK,EAAEH,QAAQ;MACfvB,KAAK,EAAE,KAAK;MACZ2B,iBAAiB,EAAE;IACrB,CAAC;IACDX,wBAAwB,CAACrE,EAAE,EAAEC,KAAK,EAAE8D,QAAQ,CAAC;IAC7ClF,MAAM,CAACsB,OAAO,CAAC8E,YAAY,CAACjF,EAAE,EAAEC,KAAK,CAAC;EACxC,CAAC,CAAC;EACF,MAAM4C,gBAAgB,GAAGpF,KAAK,CAACsC,WAAW,CAACG,MAAM,IAAI;IACnD,MAAM;QACFF,EAAE;QACFC;MACF,CAAC,GAAGC,MAAM;MACVuE,KAAK,GAAGrH,6BAA6B,CAAC8C,MAAM,EAAE1C,UAAU,CAAC;IAC3D+C,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAAC4C,IAAI,CAAC;IAC/CgD,2BAA2B,CAAC9D,EAAE,EAAEC,KAAK,EAAE3C,QAAQ,CAAC;MAC9CkD,IAAI,EAAEtC,aAAa,CAACkD;IACtB,CAAC,EAAEqD,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAClE,gBAAgB,EAAEuD,2BAA2B,CAAC,CAAC;EACnD,MAAMoB,6BAA6B,GAAGvH,gBAAgB,CAAC,MAAMuC,MAAM,IAAI;IACrE,MAAM;MACJF,EAAE;MACFC,KAAK;MACL2C,mBAAmB;MACnBD,gBAAgB,GAAG;IACrB,CAAC,GAAGzC,MAAM;IACVK,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAAC4C,IAAI,CAAC;IAC/CjC,MAAM,CAACsB,OAAO,CAACuC,+BAA+B,CAAC1C,EAAE,EAAEC,KAAK,CAAC;IACzD,MAAMkF,kBAAkB,GAAGA,CAAA,KAAM;MAC/Bd,wBAAwB,CAACrE,EAAE,EAAEC,KAAK,EAAE,IAAI,CAAC;MACzC6D,2BAA2B,CAAC9D,EAAE,EAAEC,KAAK,EAAE,IAAI,CAAC;MAC5C,IAAI0C,gBAAgB,KAAK,MAAM,EAAE;QAC/B9D,MAAM,CAACsB,OAAO,CAACiF,uBAAuB,CAACpF,EAAE,EAAEC,KAAK,EAAE0C,gBAAgB,CAAC;MACrE;IACF,CAAC;IACD,IAAIC,mBAAmB,EAAE;MACvBuC,kBAAkB,CAAC,CAAC;MACpB;IACF;IACA,MAAM3B,YAAY,GAAGpF,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAAC+C,KAAK,CAAC;IACpE,MAAM;MACJG,KAAK;MACL2B;IACF,CAAC,GAAGxB,YAAY,CAACxD,EAAE,CAAC,CAACC,KAAK,CAAC;IAC3B,IAAIoD,KAAK,IAAI2B,iBAAiB,EAAE;MAC9B;MACA;MACA5F,kBAAkB,CAACe,OAAO,CAACH,EAAE,CAAC,CAACC,KAAK,CAAC,CAACO,IAAI,GAAGtC,aAAa,CAAC4C,IAAI;MAC/D;MACAgD,2BAA2B,CAAC9D,EAAE,EAAEC,KAAK,EAAE;QACrCO,IAAI,EAAEtC,aAAa,CAAC4C;MACtB,CAAC,CAAC;MACF;IACF;IACA,MAAMuE,SAAS,GAAGxG,MAAM,CAACsB,OAAO,CAACmF,sCAAsC,CAACtF,EAAE,EAAEC,KAAK,CAAC;IAClF,IAAIZ,gBAAgB,EAAE;MACpB,MAAMkG,WAAW,GAAGC,WAAW,IAAI;QACjCpG,kBAAkB,CAACe,OAAO,CAACH,EAAE,CAAC,CAACC,KAAK,CAAC,CAACO,IAAI,GAAGtC,aAAa,CAAC4C,IAAI;QAC/D;QACAgD,2BAA2B,CAAC9D,EAAE,EAAEC,KAAK,EAAE;UACrCO,IAAI,EAAEtC,aAAa,CAAC4C;QACtB,CAAC,CAAC;QACF,IAAIxB,uBAAuB,EAAE;UAC3BA,uBAAuB,CAACkG,WAAW,CAAC;QACtC,CAAC,MAAM,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UAChD7H,QAAQ,CAAC,CAAC,wHAAwH,EAAE,iJAAiJ,EAAE,0FAA0F,CAAC,EAAE,OAAO,CAAC;QAC9X;MACF,CAAC;MACD,IAAI;QACF,MAAM8H,GAAG,GAAG/G,MAAM,CAACsB,OAAO,CAAC0F,MAAM,CAAC7F,EAAE,CAAC;QACrC8F,OAAO,CAACC,OAAO,CAAC1G,gBAAgB,CAACgG,SAAS,EAAEO,GAAG,EAAE;UAC/CI,KAAK,EAAEhG;QACT,CAAC,CAAC,CAAC,CAACiG,IAAI,CAACC,cAAc,IAAI;UACzBrH,MAAM,CAACsB,OAAO,CAACgG,UAAU,CAAC,CAACD,cAAc,CAAC,CAAC;UAC3Cf,kBAAkB,CAAC,CAAC;QACtB,CAAC,CAAC,CAACiB,KAAK,CAACb,WAAW,CAAC;MACvB,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpBD,WAAW,CAACC,WAAW,CAAC;MAC1B;IACF,CAAC,MAAM;MACL3G,MAAM,CAACsB,OAAO,CAACgG,UAAU,CAAC,CAACd,SAAS,CAAC,CAAC;MACtCF,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,CAAC;EACF,MAAMkB,2BAA2B,GAAG5I,KAAK,CAACsC,WAAW,CAAC,MAAMG,MAAM,IAAI;IACpE,MAAM;MACJF,EAAE;MACFC,KAAK;MACL8E,KAAK;MACLuB,UAAU;MACVC,wBAAwB,EAAEC;IAC5B,CAAC,GAAGtG,MAAM;IACVJ,kBAAkB,CAACE,EAAE,EAAEC,KAAK,CAAC;IAC7BM,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAAC4C,IAAI,CAAC;IAC/C,MAAM2F,MAAM,GAAG5H,MAAM,CAACsB,OAAO,CAAC2E,SAAS,CAAC7E,KAAK,CAAC;IAC9C,MAAM2F,GAAG,GAAG/G,MAAM,CAACsB,OAAO,CAAC0F,MAAM,CAAC7F,EAAE,CAAC;IACrC,IAAI0G,WAAW,GAAG3B,KAAK;IACvB,IAAI0B,MAAM,CAACE,WAAW,IAAI,CAACH,eAAe,EAAE;MAC1CE,WAAW,GAAGD,MAAM,CAACE,WAAW,CAAC5B,KAAK,EAAEa,GAAG,EAAEa,MAAM,EAAE5H,MAAM,CAAC;IAC9D;IACA,IAAI2E,YAAY,GAAGpF,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAAC+C,KAAK,CAAC;IAClE,IAAIa,QAAQ,GAAGzG,QAAQ,CAAC,CAAC,CAAC,EAAEkG,YAAY,CAACxD,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACnD8E,KAAK,EAAE2B,WAAW;MAClBE,YAAY,EAAEN,UAAU,GAAG,2BAA2B,GAAG;IAC3D,CAAC,CAAC;IACF,IAAIG,MAAM,CAACI,uBAAuB,EAAE;MAClC,MAAMC,UAAU,GAAG/B,KAAK,KAAKvB,YAAY,CAACxD,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC8E,KAAK;MAC1DhB,QAAQ,GAAGzG,QAAQ,CAAC,CAAC,CAAC,EAAEyG,QAAQ,EAAE;QAChCiB,iBAAiB,EAAE;MACrB,CAAC,CAAC;MACFX,wBAAwB,CAACrE,EAAE,EAAEC,KAAK,EAAE8D,QAAQ,CAAC;MAC7CA,QAAQ,GAAG,MAAM+B,OAAO,CAACC,OAAO,CAACU,MAAM,CAACI,uBAAuB,CAAC;QAC9D7G,EAAE;QACF4F,GAAG;QACH9G,KAAK,EAAEiF,QAAQ;QACf+C;MACF,CAAC,CAAC,CAAC;IACL;;IAEA;IACA;IACA,IAAIjI,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACT,EAAE,EAAEC,KAAK,CAAC,KAAK/B,aAAa,CAACkD,IAAI,EAAE;MAChE,OAAO,KAAK;IACd;IACAoC,YAAY,GAAGpF,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAAC+C,KAAK,CAAC;IAC9Da,QAAQ,GAAGzG,QAAQ,CAAC,CAAC,CAAC,EAAEyG,QAAQ,EAAE;MAChCiB,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACF;IACA;IACA;IACAjB,QAAQ,CAACgB,KAAK,GAAG0B,MAAM,CAACI,uBAAuB,GAAGrD,YAAY,CAACxD,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC8E,KAAK,GAAG2B,WAAW;IAC7FrC,wBAAwB,CAACrE,EAAE,EAAEC,KAAK,EAAE8D,QAAQ,CAAC;IAC7CP,YAAY,GAAGpF,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAAC+C,KAAK,CAAC;IAC9D,OAAO,CAACM,YAAY,CAACxD,EAAE,CAAC,GAAGC,KAAK,CAAC,EAAEoD,KAAK;EAC1C,CAAC,EAAE,CAACxE,MAAM,EAAEiB,kBAAkB,EAAES,gBAAgB,EAAE8D,wBAAwB,CAAC,CAAC;EAC5E,MAAMiB,sCAAsC,GAAG7H,KAAK,CAACsC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC9E,MAAMwG,MAAM,GAAG5H,MAAM,CAACsB,OAAO,CAAC2E,SAAS,CAAC7E,KAAK,CAAC;IAC9C,MAAMuD,YAAY,GAAGpF,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAAC+C,KAAK,CAAC;IACpE,MAAM0C,GAAG,GAAG/G,MAAM,CAACsB,OAAO,CAAC0F,MAAM,CAAC7F,EAAE,CAAC;IACrC,IAAI,CAACwD,YAAY,CAACxD,EAAE,CAAC,IAAI,CAACwD,YAAY,CAACxD,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACjD,OAAOpB,MAAM,CAACsB,OAAO,CAAC0F,MAAM,CAAC7F,EAAE,CAAC;IAClC;IACA,MAAM;MACJ+E;IACF,CAAC,GAAGvB,YAAY,CAACxD,EAAE,CAAC,CAACC,KAAK,CAAC;IAC3B,OAAOwG,MAAM,CAACM,WAAW,GAAGN,MAAM,CAACM,WAAW,CAAChC,KAAK,EAAEa,GAAG,EAAEa,MAAM,EAAE5H,MAAM,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEsI,GAAG,EAAE;MAC7F,CAAC3F,KAAK,GAAG8E;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClG,MAAM,CAAC,CAAC;EACZ,MAAMmI,UAAU,GAAG;IACjBvG,WAAW;IACX+B,iBAAiB;IACjBK;EACF,CAAC;EACD,MAAMoE,iBAAiB,GAAG;IACxBZ,2BAA2B;IAC3Bf;EACF,CAAC;EACDnH,gBAAgB,CAACU,MAAM,EAAEmI,UAAU,EAAE,QAAQ,CAAC;EAC9C7I,gBAAgB,CAACU,MAAM,EAAEoI,iBAAiB,EAAE,SAAS,CAAC;EACtDxJ,KAAK,CAACyJ,SAAS,CAAC,MAAM;IACpB,IAAI3H,kBAAkB,EAAE;MACtBmE,oBAAoB,CAACnE,kBAAkB,CAAC;IAC1C;EACF,CAAC,EAAE,CAACA,kBAAkB,EAAEmE,oBAAoB,CAAC,CAAC;;EAE9C;EACA7F,iBAAiB,CAAC,MAAM;IACtB,MAAMsJ,YAAY,GAAG5I,mCAAmC,CAACM,MAAM,CAAC;;IAEhE;IACA,MAAMuI,mBAAmB,GAAGhI,kBAAkB,CAACe,OAAO;IACtDf,kBAAkB,CAACe,OAAO,GAAG3B,SAAS,CAACO,cAAc,CAAC,CAAC,CAAC;;IAExDoF,MAAM,CAACkD,OAAO,CAACtI,cAAc,CAAC,CAACuI,OAAO,CAACC,IAAA,IAAkB;MAAA,IAAjB,CAACvH,EAAE,EAAEwH,MAAM,CAAC,GAAAD,IAAA;MAClDpD,MAAM,CAACkD,OAAO,CAACG,MAAM,CAAC,CAACF,OAAO,CAACG,KAAA,IAAqB;QAAA,IAApB,CAACxH,KAAK,EAAEC,MAAM,CAAC,GAAAuH,KAAA;QAC7C,MAAMC,QAAQ,GAAGN,mBAAmB,CAACpH,EAAE,CAAC,GAAGC,KAAK,CAAC,EAAEO,IAAI,IAAItC,aAAa,CAACkD,IAAI;QAC7E,MAAMuG,UAAU,GAAGR,YAAY,CAACnH,EAAE,CAAC,IAAIA,EAAE;QACzC,IAAIE,MAAM,CAACM,IAAI,KAAKtC,aAAa,CAAC4C,IAAI,IAAI4G,QAAQ,KAAKxJ,aAAa,CAACkD,IAAI,EAAE;UACzEsD,8BAA8B,CAACpH,QAAQ,CAAC;YACtC0C,EAAE,EAAE2H,UAAU;YACd1H;UACF,CAAC,EAAEC,MAAM,CAAC,CAAC;QACb,CAAC,MAAM,IAAIA,MAAM,CAACM,IAAI,KAAKtC,aAAa,CAACkD,IAAI,IAAIsG,QAAQ,KAAKxJ,aAAa,CAAC4C,IAAI,EAAE;UAChFoE,6BAA6B,CAAC5H,QAAQ,CAAC;YACrC0C,EAAE,EAAE2H,UAAU;YACd1H;UACF,CAAC,EAAEC,MAAM,CAAC,CAAC;QACb;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrB,MAAM,EAAEE,cAAc,EAAE2F,8BAA8B,EAAEQ,6BAA6B,CAAC,CAAC;AAC7F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}