{"ast": null, "code": "import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/index.js\";\nexport const useGridStatePersistence = apiRef => {\n  const exportState = React.useCallback(function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const stateToExport = apiRef.current.unstable_applyPipeProcessors('exportState', {}, params);\n    return stateToExport;\n  }, [apiRef]);\n  const restoreState = React.useCallback(stateToRestore => {\n    const response = apiRef.current.unstable_applyPipeProcessors('restoreState', {\n      callbacks: []\n    }, {\n      stateToRestore\n    });\n    response.callbacks.forEach(callback => {\n      callback();\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const statePersistenceApi = {\n    exportState,\n    restoreState\n  };\n  useGridApiMethod(apiRef, statePersistenceApi, 'public');\n};", "map": {"version": 3, "names": ["React", "useGridApiMethod", "useGridStatePersistence", "apiRef", "exportState", "useCallback", "params", "arguments", "length", "undefined", "stateToExport", "current", "unstable_applyPipeProcessors", "restoreState", "stateToRestore", "response", "callbacks", "for<PERSON>ach", "callback", "forceUpdate", "statePersistenceApi"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/statePersistence/useGridStatePersistence.js"], "sourcesContent": ["import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/index.js\";\nexport const useGridStatePersistence = apiRef => {\n  const exportState = React.useCallback((params = {}) => {\n    const stateToExport = apiRef.current.unstable_applyPipeProcessors('exportState', {}, params);\n    return stateToExport;\n  }, [apiRef]);\n  const restoreState = React.useCallback(stateToRestore => {\n    const response = apiRef.current.unstable_applyPipeProcessors('restoreState', {\n      callbacks: []\n    }, {\n      stateToRestore\n    });\n    response.callbacks.forEach(callback => {\n      callback();\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const statePersistenceApi = {\n    exportState,\n    restoreState\n  };\n  useGridApiMethod(apiRef, statePersistenceApi, 'public');\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,OAAO,MAAMC,uBAAuB,GAAGC,MAAM,IAAI;EAC/C,MAAMC,WAAW,GAAGJ,KAAK,CAACK,WAAW,CAAC,YAAiB;IAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAChD,MAAMG,aAAa,GAAGP,MAAM,CAACQ,OAAO,CAACC,4BAA4B,CAAC,aAAa,EAAE,CAAC,CAAC,EAAEN,MAAM,CAAC;IAC5F,OAAOI,aAAa;EACtB,CAAC,EAAE,CAACP,MAAM,CAAC,CAAC;EACZ,MAAMU,YAAY,GAAGb,KAAK,CAACK,WAAW,CAACS,cAAc,IAAI;IACvD,MAAMC,QAAQ,GAAGZ,MAAM,CAACQ,OAAO,CAACC,4BAA4B,CAAC,cAAc,EAAE;MAC3EI,SAAS,EAAE;IACb,CAAC,EAAE;MACDF;IACF,CAAC,CAAC;IACFC,QAAQ,CAACC,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;MACrCA,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;IACFf,MAAM,CAACQ,OAAO,CAACQ,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAAChB,MAAM,CAAC,CAAC;EACZ,MAAMiB,mBAAmB,GAAG;IAC1BhB,WAAW;IACXS;EACF,CAAC;EACDZ,gBAAgB,CAACE,MAAM,EAAEiB,mBAAmB,EAAE,QAAQ,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}