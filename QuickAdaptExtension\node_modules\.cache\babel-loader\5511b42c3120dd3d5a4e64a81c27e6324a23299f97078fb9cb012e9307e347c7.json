{"ast": null, "code": "import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n */\nexport const useGridColumnSpanning = apiRef => {\n  const lookup = React.useRef({});\n  const getCellColSpanInfo = (rowId, columnIndex) => {\n    return lookup.current[rowId]?.[columnIndex];\n  };\n  const resetColSpan = () => {\n    lookup.current = {};\n  };\n\n  // Calculate `colSpan` for each cell in the row\n  const calculateColSpan = React.useCallback(_ref => {\n    let {\n      rowId,\n      minFirstColumn,\n      maxLastColumn,\n      columns\n    } = _ref;\n    for (let i = minFirstColumn; i < maxLastColumn; i += 1) {\n      const cellProps = calculateCellColSpan({\n        apiRef,\n        lookup: lookup.current,\n        columnIndex: i,\n        rowId,\n        minFirstColumnIndex: minFirstColumn,\n        maxLastColumnIndex: maxLastColumn,\n        columns\n      });\n      if (cellProps.colSpan > 1) {\n        i += cellProps.colSpan - 1;\n      }\n    }\n  }, [apiRef]);\n  const columnSpanningPublicApi = {\n    unstable_getCellColSpanInfo: getCellColSpanInfo\n  };\n  const columnSpanningPrivateApi = {\n    resetColSpan,\n    calculateColSpan\n  };\n  useGridApiMethod(apiRef, columnSpanningPublicApi, 'public');\n  useGridApiMethod(apiRef, columnSpanningPrivateApi, 'private');\n  useGridApiEventHandler(apiRef, 'columnOrderChange', resetColSpan);\n};\nfunction calculateCellColSpan(params) {\n  const {\n    apiRef,\n    lookup,\n    columnIndex,\n    rowId,\n    minFirstColumnIndex,\n    maxLastColumnIndex,\n    columns\n  } = params;\n  const columnsLength = columns.length;\n  const column = columns[columnIndex];\n  const row = apiRef.current.getRow(rowId);\n  const value = apiRef.current.getRowValue(row, column);\n  const colSpan = typeof column.colSpan === 'function' ? column.colSpan(value, row, column, apiRef) : column.colSpan;\n  if (!colSpan || colSpan === 1) {\n    setCellColSpanInfo(lookup, rowId, columnIndex, {\n      spannedByColSpan: false,\n      cellProps: {\n        colSpan: 1,\n        width: column.computedWidth\n      }\n    });\n    return {\n      colSpan: 1\n    };\n  }\n  let width = column.computedWidth;\n  for (let j = 1; j < colSpan; j += 1) {\n    const nextColumnIndex = columnIndex + j;\n    // Cells should be spanned only within their column section (left-pinned, right-pinned and unpinned).\n    if (nextColumnIndex >= minFirstColumnIndex && nextColumnIndex < maxLastColumnIndex) {\n      const nextColumn = columns[nextColumnIndex];\n      width += nextColumn.computedWidth;\n      setCellColSpanInfo(lookup, rowId, columnIndex + j, {\n        spannedByColSpan: true,\n        rightVisibleCellIndex: Math.min(columnIndex + colSpan, columnsLength - 1),\n        leftVisibleCellIndex: columnIndex\n      });\n    }\n    setCellColSpanInfo(lookup, rowId, columnIndex, {\n      spannedByColSpan: false,\n      cellProps: {\n        colSpan,\n        width\n      }\n    });\n  }\n  return {\n    colSpan\n  };\n}\nfunction setCellColSpanInfo(lookup, rowId, columnIndex, cellColSpanInfo) {\n  if (!lookup[rowId]) {\n    lookup[rowId] = {};\n  }\n  lookup[rowId][columnIndex] = cellColSpanInfo;\n}", "map": {"version": 3, "names": ["React", "useGridApiMethod", "useGridApiEventHandler", "useGridColumnSpanning", "apiRef", "lookup", "useRef", "getCellColSpanInfo", "rowId", "columnIndex", "current", "resetColSpan", "calculateColSpan", "useCallback", "_ref", "minFirstColumn", "maxLastColumn", "columns", "i", "cellProps", "calculateCellColSpan", "minFirstColumnIndex", "maxLastColumnIndex", "colSpan", "columnSpanningPublicApi", "unstable_getCellColSpanInfo", "columnSpanningPrivateApi", "params", "columnsLength", "length", "column", "row", "getRow", "value", "getRowValue", "setCellColSpanInfo", "spannedByColSpan", "width", "computedWidth", "j", "nextColumnIndex", "nextColumn", "rightVisibleCellIndex", "Math", "min", "leftVisibleCellIndex", "cellColSpanInfo"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/columns/useGridColumnSpanning.js"], "sourcesContent": ["import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n */\nexport const useGridColumnSpanning = apiRef => {\n  const lookup = React.useRef({});\n  const getCellColSpanInfo = (rowId, columnIndex) => {\n    return lookup.current[rowId]?.[columnIndex];\n  };\n  const resetColSpan = () => {\n    lookup.current = {};\n  };\n\n  // Calculate `colSpan` for each cell in the row\n  const calculateColSpan = React.useCallback(({\n    rowId,\n    minFirstColumn,\n    maxLastColumn,\n    columns\n  }) => {\n    for (let i = minFirstColumn; i < maxLastColumn; i += 1) {\n      const cellProps = calculateCellColSpan({\n        apiRef,\n        lookup: lookup.current,\n        columnIndex: i,\n        rowId,\n        minFirstColumnIndex: minFirstColumn,\n        maxLastColumnIndex: maxLastColumn,\n        columns\n      });\n      if (cellProps.colSpan > 1) {\n        i += cellProps.colSpan - 1;\n      }\n    }\n  }, [apiRef]);\n  const columnSpanningPublicApi = {\n    unstable_getCellColSpanInfo: getCellColSpanInfo\n  };\n  const columnSpanningPrivateApi = {\n    resetColSpan,\n    calculateColSpan\n  };\n  useGridApiMethod(apiRef, columnSpanningPublicApi, 'public');\n  useGridApiMethod(apiRef, columnSpanningPrivateApi, 'private');\n  useGridApiEventHandler(apiRef, 'columnOrderChange', resetColSpan);\n};\nfunction calculateCellColSpan(params) {\n  const {\n    apiRef,\n    lookup,\n    columnIndex,\n    rowId,\n    minFirstColumnIndex,\n    maxLastColumnIndex,\n    columns\n  } = params;\n  const columnsLength = columns.length;\n  const column = columns[columnIndex];\n  const row = apiRef.current.getRow(rowId);\n  const value = apiRef.current.getRowValue(row, column);\n  const colSpan = typeof column.colSpan === 'function' ? column.colSpan(value, row, column, apiRef) : column.colSpan;\n  if (!colSpan || colSpan === 1) {\n    setCellColSpanInfo(lookup, rowId, columnIndex, {\n      spannedByColSpan: false,\n      cellProps: {\n        colSpan: 1,\n        width: column.computedWidth\n      }\n    });\n    return {\n      colSpan: 1\n    };\n  }\n  let width = column.computedWidth;\n  for (let j = 1; j < colSpan; j += 1) {\n    const nextColumnIndex = columnIndex + j;\n    // Cells should be spanned only within their column section (left-pinned, right-pinned and unpinned).\n    if (nextColumnIndex >= minFirstColumnIndex && nextColumnIndex < maxLastColumnIndex) {\n      const nextColumn = columns[nextColumnIndex];\n      width += nextColumn.computedWidth;\n      setCellColSpanInfo(lookup, rowId, columnIndex + j, {\n        spannedByColSpan: true,\n        rightVisibleCellIndex: Math.min(columnIndex + colSpan, columnsLength - 1),\n        leftVisibleCellIndex: columnIndex\n      });\n    }\n    setCellColSpanInfo(lookup, rowId, columnIndex, {\n      spannedByColSpan: false,\n      cellProps: {\n        colSpan,\n        width\n      }\n    });\n  }\n  return {\n    colSpan\n  };\n}\nfunction setCellColSpanInfo(lookup, rowId, columnIndex, cellColSpanInfo) {\n  if (!lookup[rowId]) {\n    lookup[rowId] = {};\n  }\n  lookup[rowId][columnIndex] = cellColSpanInfo;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAGC,MAAM,IAAI;EAC7C,MAAMC,MAAM,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B,MAAMC,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IACjD,OAAOJ,MAAM,CAACK,OAAO,CAACF,KAAK,CAAC,GAAGC,WAAW,CAAC;EAC7C,CAAC;EACD,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBN,MAAM,CAACK,OAAO,GAAG,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGZ,KAAK,CAACa,WAAW,CAACC,IAAA,IAKrC;IAAA,IALsC;MAC1CN,KAAK;MACLO,cAAc;MACdC,aAAa;MACbC;IACF,CAAC,GAAAH,IAAA;IACC,KAAK,IAAII,CAAC,GAAGH,cAAc,EAAEG,CAAC,GAAGF,aAAa,EAAEE,CAAC,IAAI,CAAC,EAAE;MACtD,MAAMC,SAAS,GAAGC,oBAAoB,CAAC;QACrChB,MAAM;QACNC,MAAM,EAAEA,MAAM,CAACK,OAAO;QACtBD,WAAW,EAAES,CAAC;QACdV,KAAK;QACLa,mBAAmB,EAAEN,cAAc;QACnCO,kBAAkB,EAAEN,aAAa;QACjCC;MACF,CAAC,CAAC;MACF,IAAIE,SAAS,CAACI,OAAO,GAAG,CAAC,EAAE;QACzBL,CAAC,IAAIC,SAAS,CAACI,OAAO,GAAG,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,CAACnB,MAAM,CAAC,CAAC;EACZ,MAAMoB,uBAAuB,GAAG;IAC9BC,2BAA2B,EAAElB;EAC/B,CAAC;EACD,MAAMmB,wBAAwB,GAAG;IAC/Bf,YAAY;IACZC;EACF,CAAC;EACDX,gBAAgB,CAACG,MAAM,EAAEoB,uBAAuB,EAAE,QAAQ,CAAC;EAC3DvB,gBAAgB,CAACG,MAAM,EAAEsB,wBAAwB,EAAE,SAAS,CAAC;EAC7DxB,sBAAsB,CAACE,MAAM,EAAE,mBAAmB,EAAEO,YAAY,CAAC;AACnE,CAAC;AACD,SAASS,oBAAoBA,CAACO,MAAM,EAAE;EACpC,MAAM;IACJvB,MAAM;IACNC,MAAM;IACNI,WAAW;IACXD,KAAK;IACLa,mBAAmB;IACnBC,kBAAkB;IAClBL;EACF,CAAC,GAAGU,MAAM;EACV,MAAMC,aAAa,GAAGX,OAAO,CAACY,MAAM;EACpC,MAAMC,MAAM,GAAGb,OAAO,CAACR,WAAW,CAAC;EACnC,MAAMsB,GAAG,GAAG3B,MAAM,CAACM,OAAO,CAACsB,MAAM,CAACxB,KAAK,CAAC;EACxC,MAAMyB,KAAK,GAAG7B,MAAM,CAACM,OAAO,CAACwB,WAAW,CAACH,GAAG,EAAED,MAAM,CAAC;EACrD,MAAMP,OAAO,GAAG,OAAOO,MAAM,CAACP,OAAO,KAAK,UAAU,GAAGO,MAAM,CAACP,OAAO,CAACU,KAAK,EAAEF,GAAG,EAAED,MAAM,EAAE1B,MAAM,CAAC,GAAG0B,MAAM,CAACP,OAAO;EAClH,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;IAC7BY,kBAAkB,CAAC9B,MAAM,EAAEG,KAAK,EAAEC,WAAW,EAAE;MAC7C2B,gBAAgB,EAAE,KAAK;MACvBjB,SAAS,EAAE;QACTI,OAAO,EAAE,CAAC;QACVc,KAAK,EAAEP,MAAM,CAACQ;MAChB;IACF,CAAC,CAAC;IACF,OAAO;MACLf,OAAO,EAAE;IACX,CAAC;EACH;EACA,IAAIc,KAAK,GAAGP,MAAM,CAACQ,aAAa;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,OAAO,EAAEgB,CAAC,IAAI,CAAC,EAAE;IACnC,MAAMC,eAAe,GAAG/B,WAAW,GAAG8B,CAAC;IACvC;IACA,IAAIC,eAAe,IAAInB,mBAAmB,IAAImB,eAAe,GAAGlB,kBAAkB,EAAE;MAClF,MAAMmB,UAAU,GAAGxB,OAAO,CAACuB,eAAe,CAAC;MAC3CH,KAAK,IAAII,UAAU,CAACH,aAAa;MACjCH,kBAAkB,CAAC9B,MAAM,EAAEG,KAAK,EAAEC,WAAW,GAAG8B,CAAC,EAAE;QACjDH,gBAAgB,EAAE,IAAI;QACtBM,qBAAqB,EAAEC,IAAI,CAACC,GAAG,CAACnC,WAAW,GAAGc,OAAO,EAAEK,aAAa,GAAG,CAAC,CAAC;QACzEiB,oBAAoB,EAAEpC;MACxB,CAAC,CAAC;IACJ;IACA0B,kBAAkB,CAAC9B,MAAM,EAAEG,KAAK,EAAEC,WAAW,EAAE;MAC7C2B,gBAAgB,EAAE,KAAK;MACvBjB,SAAS,EAAE;QACTI,OAAO;QACPc;MACF;IACF,CAAC,CAAC;EACJ;EACA,OAAO;IACLd;EACF,CAAC;AACH;AACA,SAASY,kBAAkBA,CAAC9B,MAAM,EAAEG,KAAK,EAAEC,WAAW,EAAEqC,eAAe,EAAE;EACvE,IAAI,CAACzC,MAAM,CAACG,KAAK,CAAC,EAAE;IAClBH,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC;EACpB;EACAH,MAAM,CAACG,KAAK,CAAC,CAACC,WAAW,CAAC,GAAGqC,eAAe;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}