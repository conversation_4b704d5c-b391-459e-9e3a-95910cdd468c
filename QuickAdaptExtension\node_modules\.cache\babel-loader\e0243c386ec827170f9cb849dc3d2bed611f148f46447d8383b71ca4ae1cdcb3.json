{"ast": null, "code": "// prng4.js - uses Arcfour as a PRNG\nvar Arcfour = /** @class */function () {\n  function Arcfour() {\n    this.i = 0;\n    this.j = 0;\n    this.S = [];\n  }\n  // Arcfour.prototype.init = ARC4init;\n  // Initialize arcfour context from key, an array of ints, each from [0..255]\n  Arcfour.prototype.init = function (key) {\n    var i;\n    var j;\n    var t;\n    for (i = 0; i < 256; ++i) {\n      this.S[i] = i;\n    }\n    j = 0;\n    for (i = 0; i < 256; ++i) {\n      j = j + this.S[i] + key[i % key.length] & 255;\n      t = this.S[i];\n      this.S[i] = this.S[j];\n      this.S[j] = t;\n    }\n    this.i = 0;\n    this.j = 0;\n  };\n  // Arcfour.prototype.next = ARC4next;\n  Arcfour.prototype.next = function () {\n    var t;\n    this.i = this.i + 1 & 255;\n    this.j = this.j + this.S[this.i] & 255;\n    t = this.S[this.i];\n    this.S[this.i] = this.S[this.j];\n    this.S[this.j] = t;\n    return this.S[t + this.S[this.i] & 255];\n  };\n  return Arcfour;\n}();\nexport { Arcfour };\n// Plug in your RNG constructor here\nexport function prng_newstate() {\n  return new Arcfour();\n}\n// Pool size must be a multiple of 4 and greater than 32.\n// An array of bytes the size of the pool will be passed to init()\nexport var rng_psize = 256;", "map": {"version": 3, "names": ["Arcfour", "i", "j", "S", "prototype", "init", "key", "t", "length", "next", "prng_newstate", "rng_psize"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/jsencrypt/lib/lib/jsbn/prng4.js"], "sourcesContent": ["// prng4.js - uses Arcfour as a PRNG\nvar Arcfour = /** @class */ (function () {\n    function Arcfour() {\n        this.i = 0;\n        this.j = 0;\n        this.S = [];\n    }\n    // Arcfour.prototype.init = ARC4init;\n    // Initialize arcfour context from key, an array of ints, each from [0..255]\n    Arcfour.prototype.init = function (key) {\n        var i;\n        var j;\n        var t;\n        for (i = 0; i < 256; ++i) {\n            this.S[i] = i;\n        }\n        j = 0;\n        for (i = 0; i < 256; ++i) {\n            j = (j + this.S[i] + key[i % key.length]) & 255;\n            t = this.S[i];\n            this.S[i] = this.S[j];\n            this.S[j] = t;\n        }\n        this.i = 0;\n        this.j = 0;\n    };\n    // Arcfour.prototype.next = ARC4next;\n    Arcfour.prototype.next = function () {\n        var t;\n        this.i = (this.i + 1) & 255;\n        this.j = (this.j + this.S[this.i]) & 255;\n        t = this.S[this.i];\n        this.S[this.i] = this.S[this.j];\n        this.S[this.j] = t;\n        return this.S[(t + this.S[this.i]) & 255];\n    };\n    return Arcfour;\n}());\nexport { Arcfour };\n// Plug in your RNG constructor here\nexport function prng_newstate() {\n    return new Arcfour();\n}\n// Pool size must be a multiple of 4 and greater than 32.\n// An array of bytes the size of the pool will be passed to init()\nexport var rng_psize = 256;\n"], "mappings": "AAAA;AACA,IAAIA,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAAA,EAAG;IACf,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,CAAC,GAAG,EAAE;EACf;EACA;EACA;EACAH,OAAO,CAACI,SAAS,CAACC,IAAI,GAAG,UAAUC,GAAG,EAAE;IACpC,IAAIL,CAAC;IACL,IAAIC,CAAC;IACL,IAAIK,CAAC;IACL,KAAKN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;MACtB,IAAI,CAACE,CAAC,CAACF,CAAC,CAAC,GAAGA,CAAC;IACjB;IACAC,CAAC,GAAG,CAAC;IACL,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;MACtBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACC,CAAC,CAACF,CAAC,CAAC,GAAGK,GAAG,CAACL,CAAC,GAAGK,GAAG,CAACE,MAAM,CAAC,GAAI,GAAG;MAC/CD,CAAC,GAAG,IAAI,CAACJ,CAAC,CAACF,CAAC,CAAC;MACb,IAAI,CAACE,CAAC,CAACF,CAAC,CAAC,GAAG,IAAI,CAACE,CAAC,CAACD,CAAC,CAAC;MACrB,IAAI,CAACC,CAAC,CAACD,CAAC,CAAC,GAAGK,CAAC;IACjB;IACA,IAAI,CAACN,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,CAAC,GAAG,CAAC;EACd,CAAC;EACD;EACAF,OAAO,CAACI,SAAS,CAACK,IAAI,GAAG,YAAY;IACjC,IAAIF,CAAC;IACL,IAAI,CAACN,CAAC,GAAI,IAAI,CAACA,CAAC,GAAG,CAAC,GAAI,GAAG;IAC3B,IAAI,CAACC,CAAC,GAAI,IAAI,CAACA,CAAC,GAAG,IAAI,CAACC,CAAC,CAAC,IAAI,CAACF,CAAC,CAAC,GAAI,GAAG;IACxCM,CAAC,GAAG,IAAI,CAACJ,CAAC,CAAC,IAAI,CAACF,CAAC,CAAC;IAClB,IAAI,CAACE,CAAC,CAAC,IAAI,CAACF,CAAC,CAAC,GAAG,IAAI,CAACE,CAAC,CAAC,IAAI,CAACD,CAAC,CAAC;IAC/B,IAAI,CAACC,CAAC,CAAC,IAAI,CAACD,CAAC,CAAC,GAAGK,CAAC;IAClB,OAAO,IAAI,CAACJ,CAAC,CAAEI,CAAC,GAAG,IAAI,CAACJ,CAAC,CAAC,IAAI,CAACF,CAAC,CAAC,GAAI,GAAG,CAAC;EAC7C,CAAC;EACD,OAAOD,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,SAASA,OAAO;AAChB;AACA,OAAO,SAASU,aAAaA,CAAA,EAAG;EAC5B,OAAO,IAAIV,OAAO,CAAC,CAAC;AACxB;AACA;AACA;AACA,OAAO,IAAIW,SAAS,GAAG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}