{"ast": null, "code": "export function getUnprocessedRange(testRange, processedRange) {\n  if (testRange.firstRowIndex >= processedRange.firstRowIndex && testRange.lastRowIndex <= processedRange.lastRowIndex) {\n    return null;\n  }\n  // Overflowing at the end\n  // Example: testRange={ firstRowIndex: 10, lastRowIndex: 20 }, processedRange={ firstRowIndex: 0, lastRowIndex: 15 }\n  // Unprocessed Range={ firstRowIndex: 16, lastRowIndex: 20 }\n  if (testRange.firstRowIndex >= processedRange.firstRowIndex && testRange.lastRowIndex > processedRange.lastRowIndex) {\n    return {\n      firstRowIndex: processedRange.lastRowIndex,\n      lastRowIndex: testRange.lastRowIndex\n    };\n  }\n  // Overflowing at the beginning\n  // Example: testRange={ firstRowIndex: 0, lastRowIndex: 20 }, processedRange={ firstRowIndex: 16, lastRowIndex: 30 }\n  // Unprocessed Range={ firstRowIndex: 0, lastRowIndex: 15 }\n  if (testRange.firstRowIndex < processedRange.firstRowIndex && testRange.lastRowIndex <= processedRange.lastRowIndex) {\n    return {\n      firstRowIndex: testRange.firstRowIndex,\n      lastRowIndex: processedRange.firstRowIndex - 1\n    };\n  }\n  // TODO: Should return two ranges handle overflowing at both ends ?\n  return testRange;\n}\nexport function isRowContextInitialized(renderContext) {\n  return renderContext.firstRowIndex !== 0 || renderContext.lastRowIndex !== 0;\n}\nexport function isRowRangeUpdated(range1, range2) {\n  return range1.firstRowIndex !== range2.firstRowIndex || range1.lastRowIndex !== range2.lastRowIndex;\n}\nexport const getCellValue = (row, colDef, apiRef) => {\n  if (!row) {\n    return null;\n  }\n  let cellValue = row[colDef.field];\n  const valueGetter = colDef.rowSpanValueGetter ?? colDef.valueGetter;\n  if (valueGetter) {\n    cellValue = valueGetter(cellValue, row, colDef, apiRef);\n  }\n  return cellValue;\n};", "map": {"version": 3, "names": ["getUnprocessedRange", "testRange", "processedRange", "firstRowIndex", "lastRowIndex", "isRowContextInitialized", "renderContext", "isRowRangeUpdated", "range1", "range2", "getCellValue", "row", "colDef", "apiRef", "cellValue", "field", "valueGetter", "rowSpanValueGetter"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/rows/gridRowSpanningUtils.js"], "sourcesContent": ["export function getUnprocessedRange(testRange, processedRange) {\n  if (testRange.firstRowIndex >= processedRange.firstRowIndex && testRange.lastRowIndex <= processedRange.lastRowIndex) {\n    return null;\n  }\n  // Overflowing at the end\n  // Example: testRange={ firstRowIndex: 10, lastRowIndex: 20 }, processedRange={ firstRowIndex: 0, lastRowIndex: 15 }\n  // Unprocessed Range={ firstRowIndex: 16, lastRowIndex: 20 }\n  if (testRange.firstRowIndex >= processedRange.firstRowIndex && testRange.lastRowIndex > processedRange.lastRowIndex) {\n    return {\n      firstRowIndex: processedRange.lastRowIndex,\n      lastRowIndex: testRange.lastRowIndex\n    };\n  }\n  // Overflowing at the beginning\n  // Example: testRange={ firstRowIndex: 0, lastRowIndex: 20 }, processedRange={ firstRowIndex: 16, lastRowIndex: 30 }\n  // Unprocessed Range={ firstRowIndex: 0, lastRowIndex: 15 }\n  if (testRange.firstRowIndex < processedRange.firstRowIndex && testRange.lastRowIndex <= processedRange.lastRowIndex) {\n    return {\n      firstRowIndex: testRange.firstRowIndex,\n      lastRowIndex: processedRange.firstRowIndex - 1\n    };\n  }\n  // TODO: Should return two ranges handle overflowing at both ends ?\n  return testRange;\n}\nexport function isRowContextInitialized(renderContext) {\n  return renderContext.firstRowIndex !== 0 || renderContext.lastRowIndex !== 0;\n}\nexport function isRowRangeUpdated(range1, range2) {\n  return range1.firstRowIndex !== range2.firstRowIndex || range1.lastRowIndex !== range2.lastRowIndex;\n}\nexport const getCellValue = (row, colDef, apiRef) => {\n  if (!row) {\n    return null;\n  }\n  let cellValue = row[colDef.field];\n  const valueGetter = colDef.rowSpanValueGetter ?? colDef.valueGetter;\n  if (valueGetter) {\n    cellValue = valueGetter(cellValue, row, colDef, apiRef);\n  }\n  return cellValue;\n};"], "mappings": "AAAA,OAAO,SAASA,mBAAmBA,CAACC,SAAS,EAAEC,cAAc,EAAE;EAC7D,IAAID,SAAS,CAACE,aAAa,IAAID,cAAc,CAACC,aAAa,IAAIF,SAAS,CAACG,YAAY,IAAIF,cAAc,CAACE,YAAY,EAAE;IACpH,OAAO,IAAI;EACb;EACA;EACA;EACA;EACA,IAAIH,SAAS,CAACE,aAAa,IAAID,cAAc,CAACC,aAAa,IAAIF,SAAS,CAACG,YAAY,GAAGF,cAAc,CAACE,YAAY,EAAE;IACnH,OAAO;MACLD,aAAa,EAAED,cAAc,CAACE,YAAY;MAC1CA,YAAY,EAAEH,SAAS,CAACG;IAC1B,CAAC;EACH;EACA;EACA;EACA;EACA,IAAIH,SAAS,CAACE,aAAa,GAAGD,cAAc,CAACC,aAAa,IAAIF,SAAS,CAACG,YAAY,IAAIF,cAAc,CAACE,YAAY,EAAE;IACnH,OAAO;MACLD,aAAa,EAAEF,SAAS,CAACE,aAAa;MACtCC,YAAY,EAAEF,cAAc,CAACC,aAAa,GAAG;IAC/C,CAAC;EACH;EACA;EACA,OAAOF,SAAS;AAClB;AACA,OAAO,SAASI,uBAAuBA,CAACC,aAAa,EAAE;EACrD,OAAOA,aAAa,CAACH,aAAa,KAAK,CAAC,IAAIG,aAAa,CAACF,YAAY,KAAK,CAAC;AAC9E;AACA,OAAO,SAASG,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAChD,OAAOD,MAAM,CAACL,aAAa,KAAKM,MAAM,CAACN,aAAa,IAAIK,MAAM,CAACJ,YAAY,KAAKK,MAAM,CAACL,YAAY;AACrG;AACA,OAAO,MAAMM,YAAY,GAAGA,CAACC,GAAG,EAAEC,MAAM,EAAEC,MAAM,KAAK;EACnD,IAAI,CAACF,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EACA,IAAIG,SAAS,GAAGH,GAAG,CAACC,MAAM,CAACG,KAAK,CAAC;EACjC,MAAMC,WAAW,GAAGJ,MAAM,CAACK,kBAAkB,IAAIL,MAAM,CAACI,WAAW;EACnE,IAAIA,WAAW,EAAE;IACfF,SAAS,GAAGE,WAAW,CAACF,SAAS,EAAEH,GAAG,EAAEC,MAAM,EAAEC,MAAM,CAAC;EACzD;EACA,OAAOC,SAAS;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}