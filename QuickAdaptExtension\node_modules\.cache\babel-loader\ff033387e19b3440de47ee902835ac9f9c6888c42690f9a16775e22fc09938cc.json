{"ast": null, "code": "import { gridFilteredSortedRowIdsSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridRowSpanningHiddenCellsSelector } from \"../rows/gridRowSpanningSelectors.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nexport function enrichPageRowsWithPinnedRows(apiRef, rows) {\n  const pinnedRows = gridPinnedRowsSelector(apiRef) || {};\n  return [...(pinnedRows.top || []), ...rows, ...(pinnedRows.bottom || [])];\n}\nexport const getLeftColumnIndex = _ref => {\n  let {\n    currentColIndex,\n    firstColIndex,\n    lastColIndex,\n    isRtl\n  } = _ref;\n  if (isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  }\n  return null;\n};\nexport const getRightColumnIndex = _ref2 => {\n  let {\n    currentColIndex,\n    firstColIndex,\n    lastColIndex,\n    isRtl\n  } = _ref2;\n  if (isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  }\n  return null;\n};\nexport function findNonRowSpannedCell(apiRef, rowId, field, rowSpanScanDirection) {\n  const rowSpanHiddenCells = gridRowSpanningHiddenCellsSelector(apiRef);\n  if (!rowSpanHiddenCells[rowId]?.[field]) {\n    return rowId;\n  }\n  const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(apiRef);\n  // find closest non row spanned cell in the given `rowSpanScanDirection`\n  let nextRowIndex = filteredSortedRowIds.indexOf(rowId) + (rowSpanScanDirection === 'down' ? 1 : -1);\n  while (nextRowIndex >= 0 && nextRowIndex < filteredSortedRowIds.length) {\n    const nextRowId = filteredSortedRowIds[nextRowIndex];\n    if (!rowSpanHiddenCells[nextRowId]?.[field]) {\n      return nextRowId;\n    }\n    nextRowIndex += rowSpanScanDirection === 'down' ? 1 : -1;\n  }\n  return rowId;\n}", "map": {"version": 3, "names": ["gridFilteredSortedRowIdsSelector", "gridRowSpanningHiddenCellsSelector", "gridPinnedRowsSelector", "enrichPageRowsWithPinnedRows", "apiRef", "rows", "pinnedRows", "top", "bottom", "getLeftColumnIndex", "_ref", "currentColIndex", "firstColIndex", "lastColIndex", "isRtl", "getRightColumnIndex", "_ref2", "findNonRowSpannedCell", "rowId", "field", "rowSpanScanDirection", "rowSpanHiddenCells", "filteredSortedRowIds", "nextRowIndex", "indexOf", "length", "nextRowId"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/keyboardNavigation/utils.js"], "sourcesContent": ["import { gridFilteredSortedRowIdsSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridRowSpanningHiddenCellsSelector } from \"../rows/gridRowSpanningSelectors.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nexport function enrichPageRowsWithPinnedRows(apiRef, rows) {\n  const pinnedRows = gridPinnedRowsSelector(apiRef) || {};\n  return [...(pinnedRows.top || []), ...rows, ...(pinnedRows.bottom || [])];\n}\nexport const getLeftColumnIndex = ({\n  currentColIndex,\n  firstColIndex,\n  lastColIndex,\n  isRtl\n}) => {\n  if (isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  }\n  return null;\n};\nexport const getRightColumnIndex = ({\n  currentColIndex,\n  firstColIndex,\n  lastColIndex,\n  isRtl\n}) => {\n  if (isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  }\n  return null;\n};\nexport function findNonRowSpannedCell(apiRef, rowId, field, rowSpanScanDirection) {\n  const rowSpanHiddenCells = gridRowSpanningHiddenCellsSelector(apiRef);\n  if (!rowSpanHiddenCells[rowId]?.[field]) {\n    return rowId;\n  }\n  const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(apiRef);\n  // find closest non row spanned cell in the given `rowSpanScanDirection`\n  let nextRowIndex = filteredSortedRowIds.indexOf(rowId) + (rowSpanScanDirection === 'down' ? 1 : -1);\n  while (nextRowIndex >= 0 && nextRowIndex < filteredSortedRowIds.length) {\n    const nextRowId = filteredSortedRowIds[nextRowIndex];\n    if (!rowSpanHiddenCells[nextRowId]?.[field]) {\n      return nextRowId;\n    }\n    nextRowIndex += rowSpanScanDirection === 'down' ? 1 : -1;\n  }\n  return rowId;\n}"], "mappings": "AAAA,SAASA,gCAAgC,QAAQ,iCAAiC;AAClF,SAASC,kCAAkC,QAAQ,qCAAqC;AACxF,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAO,SAASC,4BAA4BA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACzD,MAAMC,UAAU,GAAGJ,sBAAsB,CAACE,MAAM,CAAC,IAAI,CAAC,CAAC;EACvD,OAAO,CAAC,IAAIE,UAAU,CAACC,GAAG,IAAI,EAAE,CAAC,EAAE,GAAGF,IAAI,EAAE,IAAIC,UAAU,CAACE,MAAM,IAAI,EAAE,CAAC,CAAC;AAC3E;AACA,OAAO,MAAMC,kBAAkB,GAAGC,IAAA,IAK5B;EAAA,IAL6B;IACjCC,eAAe;IACfC,aAAa;IACbC,YAAY;IACZC;EACF,CAAC,GAAAJ,IAAA;EACC,IAAII,KAAK,EAAE;IACT,IAAIH,eAAe,GAAGE,YAAY,EAAE;MAClC,OAAOF,eAAe,GAAG,CAAC;IAC5B;EACF,CAAC,MAAM,IAAI,CAACG,KAAK,EAAE;IACjB,IAAIH,eAAe,GAAGC,aAAa,EAAE;MACnC,OAAOD,eAAe,GAAG,CAAC;IAC5B;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,MAAMI,mBAAmB,GAAGC,KAAA,IAK7B;EAAA,IAL8B;IAClCL,eAAe;IACfC,aAAa;IACbC,YAAY;IACZC;EACF,CAAC,GAAAE,KAAA;EACC,IAAIF,KAAK,EAAE;IACT,IAAIH,eAAe,GAAGC,aAAa,EAAE;MACnC,OAAOD,eAAe,GAAG,CAAC;IAC5B;EACF,CAAC,MAAM,IAAI,CAACG,KAAK,EAAE;IACjB,IAAIH,eAAe,GAAGE,YAAY,EAAE;MAClC,OAAOF,eAAe,GAAG,CAAC;IAC5B;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,SAASM,qBAAqBA,CAACb,MAAM,EAAEc,KAAK,EAAEC,KAAK,EAAEC,oBAAoB,EAAE;EAChF,MAAMC,kBAAkB,GAAGpB,kCAAkC,CAACG,MAAM,CAAC;EACrE,IAAI,CAACiB,kBAAkB,CAACH,KAAK,CAAC,GAAGC,KAAK,CAAC,EAAE;IACvC,OAAOD,KAAK;EACd;EACA,MAAMI,oBAAoB,GAAGtB,gCAAgC,CAACI,MAAM,CAAC;EACrE;EACA,IAAImB,YAAY,GAAGD,oBAAoB,CAACE,OAAO,CAACN,KAAK,CAAC,IAAIE,oBAAoB,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACnG,OAAOG,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAGD,oBAAoB,CAACG,MAAM,EAAE;IACtE,MAAMC,SAAS,GAAGJ,oBAAoB,CAACC,YAAY,CAAC;IACpD,IAAI,CAACF,kBAAkB,CAACK,SAAS,CAAC,GAAGP,KAAK,CAAC,EAAE;MAC3C,OAAOO,SAAS;IAClB;IACAH,YAAY,IAAIH,oBAAoB,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1D;EACA,OAAOF,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}