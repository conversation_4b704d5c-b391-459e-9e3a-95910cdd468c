{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport MenuItem from '@mui/material/MenuItem';\nimport ListItemIcon from '@mui/material/ListItemIcon';\nimport ListItemText from '@mui/material/ListItemText';\nimport { GridPreferencePanelsValue } from \"../../../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridColumnMenuManageItem(props) {\n  const {\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const showColumns = React.useCallback(event => {\n    onClick(event); // hide column menu\n    apiRef.current.showPreferences(GridPreferencePanelsValue.columns);\n  }, [apiRef, onClick]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(MenuItem, {\n    onClick: showColumns,\n    children: [/*#__PURE__*/_jsx(ListItemIcon, {\n      children: /*#__PURE__*/_jsx(rootProps.slots.columnMenuManageColumnsIcon, {\n        fontSize: \"small\"\n      })\n    }), /*#__PURE__*/_jsx(ListItemText, {\n      children: apiRef.current.getLocaleText('columnMenuManageColumns')\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuManageItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuManageItem };", "map": {"version": 3, "names": ["React", "PropTypes", "MenuItem", "ListItemIcon", "ListItemText", "GridPreferencePanelsValue", "useGridApiContext", "useGridRootProps", "jsx", "_jsx", "jsxs", "_jsxs", "GridColumnMenuManageItem", "props", "onClick", "apiRef", "rootProps", "showColumns", "useCallback", "event", "current", "showPreferences", "columns", "disableColumnSelector", "children", "slots", "columnMenuManageColumnsIcon", "fontSize", "getLocaleText", "process", "env", "NODE_ENV", "propTypes", "colDef", "object", "isRequired", "func"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/menu/columnMenu/menuItems/GridColumnMenuManageItem.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport MenuItem from '@mui/material/MenuItem';\nimport ListItemIcon from '@mui/material/ListItemIcon';\nimport ListItemText from '@mui/material/ListItemText';\nimport { GridPreferencePanelsValue } from \"../../../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridColumnMenuManageItem(props) {\n  const {\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const showColumns = React.useCallback(event => {\n    onClick(event); // hide column menu\n    apiRef.current.showPreferences(GridPreferencePanelsValue.columns);\n  }, [apiRef, onClick]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(MenuItem, {\n    onClick: showColumns,\n    children: [/*#__PURE__*/_jsx(ListItemIcon, {\n      children: /*#__PURE__*/_jsx(rootProps.slots.columnMenuManageColumnsIcon, {\n        fontSize: \"small\"\n      })\n    }), /*#__PURE__*/_jsx(ListItemText, {\n      children: apiRef.current.getLocaleText('columnMenuManageColumns')\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuManageItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuManageItem };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,yBAAyB,QAAQ,0EAA0E;AACpH,SAASC,iBAAiB,QAAQ,8CAA8C;AAChF,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,SAASC,wBAAwBA,CAACC,KAAK,EAAE;EACvC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,MAAM,GAAGT,iBAAiB,CAAC,CAAC;EAClC,MAAMU,SAAS,GAAGT,gBAAgB,CAAC,CAAC;EACpC,MAAMU,WAAW,GAAGjB,KAAK,CAACkB,WAAW,CAACC,KAAK,IAAI;IAC7CL,OAAO,CAACK,KAAK,CAAC,CAAC,CAAC;IAChBJ,MAAM,CAACK,OAAO,CAACC,eAAe,CAAChB,yBAAyB,CAACiB,OAAO,CAAC;EACnE,CAAC,EAAE,CAACP,MAAM,EAAED,OAAO,CAAC,CAAC;EACrB,IAAIE,SAAS,CAACO,qBAAqB,EAAE;IACnC,OAAO,IAAI;EACb;EACA,OAAO,aAAaZ,KAAK,CAACT,QAAQ,EAAE;IAClCY,OAAO,EAAEG,WAAW;IACpBO,QAAQ,EAAE,CAAC,aAAaf,IAAI,CAACN,YAAY,EAAE;MACzCqB,QAAQ,EAAE,aAAaf,IAAI,CAACO,SAAS,CAACS,KAAK,CAACC,2BAA2B,EAAE;QACvEC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,CAAC,EAAE,aAAalB,IAAI,CAACL,YAAY,EAAE;MAClCoB,QAAQ,EAAET,MAAM,CAACK,OAAO,CAACQ,aAAa,CAAC,yBAAyB;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnB,wBAAwB,CAACoB,SAAS,GAAG;EAC3E;EACA;EACA;EACA;EACAC,MAAM,EAAEhC,SAAS,CAACiC,MAAM,CAACC,UAAU;EACnCrB,OAAO,EAAEb,SAAS,CAACmC,IAAI,CAACD;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAASvB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}