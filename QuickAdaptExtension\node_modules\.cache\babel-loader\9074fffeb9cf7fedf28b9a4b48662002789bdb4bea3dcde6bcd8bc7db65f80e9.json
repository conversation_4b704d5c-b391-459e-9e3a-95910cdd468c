{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"variant\", \"noRowsVariant\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport LinearProgress from '@mui/material/LinearProgress';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { GridSkeletonLoadingOverlay } from \"./GridSkeletonLoadingOverlay.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { gridRowCountSelector, useGridSelector } from \"../hooks/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst LOADING_VARIANTS = {\n  'circular-progress': {\n    component: CircularProgress,\n    style: {}\n  },\n  'linear-progress': {\n    component: LinearProgress,\n    style: {\n      display: 'block'\n    }\n  },\n  skeleton: {\n    component: GridSkeletonLoadingOverlay,\n    style: {\n      display: 'block'\n    }\n  }\n};\nconst GridLoadingOverlay = /*#__PURE__*/React.forwardRef(function GridLoadingOverlay(props, ref) {\n  const {\n      variant = 'circular-progress',\n      noRowsVariant = 'circular-progress',\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rowsCount = useGridSelector(apiRef, gridRowCountSelector);\n  const activeVariant = LOADING_VARIANTS[rowsCount === 0 ? noRowsVariant : variant];\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({\n    ref: ref,\n    style: _extends({}, activeVariant.style, style)\n  }, other, {\n    children: /*#__PURE__*/_jsx(activeVariant.component, {})\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridLoadingOverlay.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The variant of the overlay when no rows are displayed.\n   * @default 'circular-progress'\n   */\n  noRowsVariant: PropTypes.oneOf(['circular-progress', 'linear-progress', 'skeleton']),\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant of the overlay.\n   * @default 'circular-progress'\n   */\n  variant: PropTypes.oneOf(['circular-progress', 'linear-progress', 'skeleton'])\n} : void 0;\nexport { GridLoadingOverlay };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "LinearProgress", "CircularProgress", "GridOverlay", "GridSkeletonLoadingOverlay", "useGridApiContext", "gridRowCountSelector", "useGridSelector", "jsx", "_jsx", "LOADING_VARIANTS", "component", "style", "display", "skeleton", "GridLoadingOverlay", "forwardRef", "props", "ref", "variant", "noRowsVariant", "other", "apiRef", "rowsCount", "activeVariant", "children", "process", "env", "NODE_ENV", "propTypes", "oneOf", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/GridLoadingOverlay.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"variant\", \"noRowsVariant\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport LinearProgress from '@mui/material/LinearProgress';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { GridSkeletonLoadingOverlay } from \"./GridSkeletonLoadingOverlay.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { gridRowCountSelector, useGridSelector } from \"../hooks/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst LOADING_VARIANTS = {\n  'circular-progress': {\n    component: CircularProgress,\n    style: {}\n  },\n  'linear-progress': {\n    component: LinearProgress,\n    style: {\n      display: 'block'\n    }\n  },\n  skeleton: {\n    component: GridSkeletonLoadingOverlay,\n    style: {\n      display: 'block'\n    }\n  }\n};\nconst GridLoadingOverlay = /*#__PURE__*/React.forwardRef(function GridLoadingOverlay(props, ref) {\n  const {\n      variant = 'circular-progress',\n      noRowsVariant = 'circular-progress',\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rowsCount = useGridSelector(apiRef, gridRowCountSelector);\n  const activeVariant = LOADING_VARIANTS[rowsCount === 0 ? noRowsVariant : variant];\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({\n    ref: ref,\n    style: _extends({}, activeVariant.style, style)\n  }, other, {\n    children: /*#__PURE__*/_jsx(activeVariant.component, {})\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridLoadingOverlay.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The variant of the overlay when no rows are displayed.\n   * @default 'circular-progress'\n   */\n  noRowsVariant: PropTypes.oneOf(['circular-progress', 'linear-progress', 'skeleton']),\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant of the overlay.\n   * @default 'circular-progress'\n   */\n  variant: PropTypes.oneOf(['circular-progress', 'linear-progress', 'skeleton'])\n} : void 0;\nexport { GridLoadingOverlay };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,eAAe,EAAE,OAAO,CAAC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,oBAAoB,EAAEC,eAAe,QAAQ,mBAAmB;AACzE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,gBAAgB,GAAG;EACvB,mBAAmB,EAAE;IACnBC,SAAS,EAAET,gBAAgB;IAC3BU,KAAK,EAAE,CAAC;EACV,CAAC;EACD,iBAAiB,EAAE;IACjBD,SAAS,EAAEV,cAAc;IACzBW,KAAK,EAAE;MACLC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,QAAQ,EAAE;IACRH,SAAS,EAAEP,0BAA0B;IACrCQ,KAAK,EAAE;MACLC,OAAO,EAAE;IACX;EACF;AACF,CAAC;AACD,MAAME,kBAAkB,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,SAASD,kBAAkBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC/F,MAAM;MACFC,OAAO,GAAG,mBAAmB;MAC7BC,aAAa,GAAG,mBAAmB;MACnCR;IACF,CAAC,GAAGK,KAAK;IACTI,KAAK,GAAGxB,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACzD,MAAMwB,MAAM,GAAGjB,iBAAiB,CAAC,CAAC;EAClC,MAAMkB,SAAS,GAAGhB,eAAe,CAACe,MAAM,EAAEhB,oBAAoB,CAAC;EAC/D,MAAMkB,aAAa,GAAGd,gBAAgB,CAACa,SAAS,KAAK,CAAC,GAAGH,aAAa,GAAGD,OAAO,CAAC;EACjF,OAAO,aAAaV,IAAI,CAACN,WAAW,EAAEP,QAAQ,CAAC;IAC7CsB,GAAG,EAAEA,GAAG;IACRN,KAAK,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,aAAa,CAACZ,KAAK,EAAEA,KAAK;EAChD,CAAC,EAAES,KAAK,EAAE;IACRI,QAAQ,EAAE,aAAahB,IAAI,CAACe,aAAa,CAACb,SAAS,EAAE,CAAC,CAAC;EACzD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGb,kBAAkB,CAACc,SAAS,GAAG;EACrE;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACET,aAAa,EAAEpB,SAAS,CAAC8B,KAAK,CAAC,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,UAAU,CAAC,CAAC;EACpFC,EAAE,EAAE/B,SAAS,CAACgC,SAAS,CAAC,CAAChC,SAAS,CAACiC,OAAO,CAACjC,SAAS,CAACgC,SAAS,CAAC,CAAChC,SAAS,CAACkC,IAAI,EAAElC,SAAS,CAACmC,MAAM,EAAEnC,SAAS,CAACoC,IAAI,CAAC,CAAC,CAAC,EAAEpC,SAAS,CAACkC,IAAI,EAAElC,SAAS,CAACmC,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEhB,OAAO,EAAEnB,SAAS,CAAC8B,KAAK,CAAC,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,UAAU,CAAC;AAC/E,CAAC,GAAG,KAAK,CAAC;AACV,SAASf,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}