{"ast": null, "code": "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar WeakMap = getNative(root, 'WeakMap');\nexport default WeakMap;", "map": {"version": 3, "names": ["getNative", "root", "WeakMap"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash-es/_WeakMap.js"], "sourcesContent": ["import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar WeakMap = getNative(root, 'WeakMap');\n\nexport default WeakMap;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,OAAO,GAAGF,SAAS,CAACC,IAAI,EAAE,SAAS,CAAC;AAExC,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}