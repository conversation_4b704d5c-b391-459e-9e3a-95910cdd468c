{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { lruMemoize } from 'reselect';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useLazyRef } from \"../../utils/useLazyRef.js\";\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnLookupSelector } from \"../columns/gridColumnsSelector.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/gridPreferencePanelsValue.js\";\nimport { getDefaultGridFilterModel } from \"./gridFilterState.js\";\nimport { gridFilterModelSelector } from \"./gridFilterSelector.js\";\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nimport { gridRowsLookupSelector } from \"../rows/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/index.js\";\nimport { buildAggregatedFilterApplier, sanitizeFilterModel, mergeStateWithFilterModel, cleanFilterItem, passFilterLogic, shouldQuickFilterExcludeHiddenColumns } from \"./gridFilterUtils.js\";\nimport { isDeepEqual } from \"../../../utils/utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const filterStateInitializer = (state, props, apiRef) => {\n  const filterModel = props.filterModel ?? props.initialState?.filter?.filterModel ?? getDefaultGridFilterModel();\n  return _extends({}, state, {\n    filter: {\n      filterModel: sanitizeFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef),\n      filteredRowsLookup: {},\n      filteredChildrenCountLookup: {},\n      filteredDescendantCountLookup: {}\n    },\n    visibleRowsLookup: {}\n  });\n};\nconst getVisibleRowsLookup = params => {\n  // For flat tree, the `visibleRowsLookup` and the `filteredRowsLookup` are equals since no row is collapsed.\n  return params.filteredRowsLookup;\n};\nfunction getVisibleRowsLookupState(apiRef, state) {\n  return apiRef.current.applyStrategyProcessor('visibleRowsLookupCreation', {\n    tree: state.rows.tree,\n    filteredRowsLookup: state.filter.filteredRowsLookup\n  });\n}\nfunction createMemoizedValues() {\n  return lruMemoize(Object.values);\n}\n\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n * @requires useGridRows (event)\n */\nexport const useGridFilter = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFilter');\n  apiRef.current.registerControlState({\n    stateId: 'filter',\n    propModel: props.filterModel,\n    propOnChange: props.onFilterModelChange,\n    stateSelector: gridFilterModelSelector,\n    changeEvent: 'filterModelChange'\n  });\n  const updateFilteredRows = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const filterModel = gridFilterModelSelector(state, apiRef.current.instanceId);\n      const filterState = apiRef.current.getFilterState(filterModel);\n      const newState = _extends({}, state, {\n        filter: _extends({}, state.filter, filterState)\n      });\n      const visibleRowsLookupState = getVisibleRowsLookupState(apiRef, newState);\n      return _extends({}, newState, {\n        visibleRowsLookup: visibleRowsLookupState\n      });\n    });\n    apiRef.current.publishEvent('filteredRowsSet');\n  }, [apiRef]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.filterable === false || props.disableColumnFilter) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuFilterItem'];\n  }, [props.disableColumnFilter]);\n\n  /**\n   * API METHODS\n   */\n  const applyFilters = React.useCallback(() => {\n    updateFilteredRows();\n    apiRef.current.forceUpdate();\n  }, [apiRef, updateFilteredRows]);\n  const upsertFilterItem = React.useCallback(item => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = [...filterModel.items];\n    const itemIndex = items.findIndex(filterItem => filterItem.id === item.id);\n    if (itemIndex === -1) {\n      items.push(item);\n    } else {\n      items[itemIndex] = item;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'upsertFilterItem');\n  }, [apiRef]);\n  const upsertFilterItems = React.useCallback(items => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const existingItems = [...filterModel.items];\n    items.forEach(item => {\n      const itemIndex = existingItems.findIndex(filterItem => filterItem.id === item.id);\n      if (itemIndex === -1) {\n        existingItems.push(item);\n      } else {\n        existingItems[itemIndex] = item;\n      }\n    });\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items: existingItems\n    }), 'upsertFilterItems');\n  }, [apiRef]);\n  const deleteFilterItem = React.useCallback(itemToDelete => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = filterModel.items.filter(item => item.id !== itemToDelete.id);\n    if (items.length === filterModel.items.length) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'deleteFilterItem');\n  }, [apiRef]);\n  const showFilterPanel = React.useCallback((targetColumnField, panelId, labelId) => {\n    logger.debug('Displaying filter panel');\n    if (targetColumnField) {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterItemsWithValue = filterModel.items.filter(item => {\n        if (item.value !== undefined) {\n          // Some filters like `isAnyOf` support array as `item.value`.\n          // If array is empty, we want to remove it from the filter model.\n          if (Array.isArray(item.value) && item.value.length === 0) {\n            return false;\n          }\n          return true;\n        }\n        const column = apiRef.current.getColumn(item.field);\n        const filterOperator = column.filterOperators?.find(operator => operator.value === item.operator);\n        const requiresFilterValue = typeof filterOperator?.requiresFilterValue === 'undefined' ? true : filterOperator?.requiresFilterValue;\n\n        // Operators like `isEmpty` don't have and don't require `item.value`.\n        // So we don't want to remove them from the filter model if `item.value === undefined`.\n        // See https://github.com/mui/mui-x/issues/5402\n        if (requiresFilterValue) {\n          return false;\n        }\n        return true;\n      });\n      let newFilterItems;\n      const filterItemOnTarget = filterItemsWithValue.find(item => item.field === targetColumnField);\n      const targetColumn = apiRef.current.getColumn(targetColumnField);\n      if (filterItemOnTarget) {\n        newFilterItems = filterItemsWithValue;\n      } else if (props.disableMultipleColumnsFiltering) {\n        newFilterItems = [cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      } else {\n        newFilterItems = [...filterItemsWithValue, cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      }\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n    apiRef.current.showPreferences(GridPreferencePanelsValue.filters, panelId, labelId);\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const hideFilterPanel = React.useCallback(() => {\n    logger.debug('Hiding filter panel');\n    apiRef.current.hidePreferences();\n  }, [apiRef, logger]);\n  const setFilterLogicOperator = React.useCallback(logicOperator => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.logicOperator === logicOperator) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      logicOperator\n    }), 'changeLogicOperator');\n  }, [apiRef]);\n  const setQuickFilterValues = React.useCallback(values => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (isDeepEqual(filterModel.quickFilterValues, values)) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      quickFilterValues: [...values]\n    }));\n  }, [apiRef]);\n  const setFilterModel = React.useCallback((model, reason) => {\n    const currentModel = gridFilterModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug('Setting filter model');\n      apiRef.current.updateControlState('filter', mergeStateWithFilterModel(model, props.disableMultipleColumnsFiltering, apiRef), reason);\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const getFilterState = React.useCallback(inputFilterModel => {\n    const filterModel = sanitizeFilterModel(inputFilterModel, props.disableMultipleColumnsFiltering, apiRef);\n    const isRowMatchingFilters = props.filterMode === 'client' ? buildAggregatedFilterApplier(filterModel, apiRef, props.disableEval) : null;\n    const filterResult = apiRef.current.applyStrategyProcessor('filtering', {\n      isRowMatchingFilters,\n      filterModel: filterModel ?? getDefaultGridFilterModel()\n    });\n    return _extends({}, filterResult, {\n      filterModel\n    });\n  }, [props.disableMultipleColumnsFiltering, props.filterMode, props.disableEval, apiRef]);\n  const filterApi = {\n    setFilterLogicOperator,\n    unstable_applyFilters: applyFilters,\n    deleteFilterItem,\n    upsertFilterItem,\n    upsertFilterItems,\n    setFilterModel,\n    showFilterPanel,\n    hideFilterPanel,\n    setQuickFilterValues,\n    ignoreDiacritics: props.ignoreDiacritics,\n    getFilterState\n  };\n  useGridApiMethod(apiRef, filterApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const filterModelToExport = gridFilterModelSelector(apiRef);\n    const shouldExportFilterModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.filterModel != null ||\n    // Always export if the model has been initialized\n    props.initialState?.filter?.filterModel != null ||\n    // Export if the model is not equal to the default value\n    !isDeepEqual(filterModelToExport, getDefaultGridFilterModel());\n    if (!shouldExportFilterModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      filter: {\n        filterModel: filterModelToExport\n      }\n    });\n  }, [apiRef, props.filterModel, props.initialState?.filter?.filterModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const filterModel = context.stateToRestore.filter?.filterModel;\n    if (filterModel == null) {\n      return params;\n    }\n    apiRef.current.updateControlState('filter', mergeStateWithFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef), 'restoreState');\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.unstable_applyFilters]\n    });\n  }, [apiRef, props.disableMultipleColumnsFiltering]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.filters) {\n      const FilterPanel = props.slots.filterPanel;\n      return /*#__PURE__*/_jsx(FilterPanel, _extends({}, props.slotProps?.filterPanel));\n    }\n    return initialValue;\n  }, [props.slots.filterPanel, props.slotProps?.filterPanel]);\n  const {\n    getRowId\n  } = props;\n  const getRowsRef = useLazyRef(createMemoizedValues);\n  const flatFilteringMethod = React.useCallback(params => {\n    if (props.filterMode !== 'client' || !params.isRowMatchingFilters) {\n      return {\n        filteredRowsLookup: {},\n        filteredChildrenCountLookup: {},\n        filteredDescendantCountLookup: {}\n      };\n    }\n    const dataRowIdToModelLookup = gridRowsLookupSelector(apiRef);\n    const filteredRowsLookup = {};\n    const {\n      isRowMatchingFilters\n    } = params;\n    const filterCache = {};\n    const result = {\n      passingFilterItems: null,\n      passingQuickFilterValues: null\n    };\n    const rows = getRowsRef.current(apiRef.current.state.rows.dataRowIdToModelLookup);\n    for (let i = 0; i < rows.length; i += 1) {\n      const row = rows[i];\n      const id = getRowId ? getRowId(row) : row.id;\n      isRowMatchingFilters(row, undefined, result);\n      const isRowPassing = passFilterLogic([result.passingFilterItems], [result.passingQuickFilterValues], params.filterModel, apiRef, filterCache);\n      filteredRowsLookup[id] = isRowPassing;\n    }\n    const footerId = 'auto-generated-group-footer-root';\n    const footer = dataRowIdToModelLookup[footerId];\n    if (footer) {\n      filteredRowsLookup[footerId] = true;\n    }\n    return {\n      filteredRowsLookup,\n      filteredChildrenCountLookup: {},\n      filteredDescendantCountLookup: {}\n    };\n  }, [apiRef, props.filterMode, getRowId, getRowsRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'filtering', flatFilteringMethod);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'visibleRowsLookupCreation', getVisibleRowsLookup);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnsChange = React.useCallback(() => {\n    logger.debug('onColUpdated - GridColumns changed, applying filters');\n    const filterModel = gridFilterModelSelector(apiRef);\n    const columnsLookup = gridColumnLookupSelector(apiRef);\n    const newFilterItems = filterModel.items.filter(item => item.field && columnsLookup[item.field]);\n    if (newFilterItems.length < filterModel.items.length) {\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n  }, [apiRef, logger]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'filtering') {\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef]);\n  const updateVisibleRowsLookupState = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        visibleRowsLookup: getVisibleRowsLookupState(apiRef, state)\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n\n  // Do not call `apiRef.current.forceUpdate` to avoid re-render before updating the sorted rows.\n  // Otherwise, the state is not consistent during the render\n  useGridApiEventHandler(apiRef, 'rowsSet', updateFilteredRows);\n  useGridApiEventHandler(apiRef, 'columnsChange', handleColumnsChange);\n  useGridApiEventHandler(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridApiEventHandler(apiRef, 'rowExpansionChange', updateVisibleRowsLookupState);\n  useGridApiEventHandler(apiRef, 'columnVisibilityModelChange', () => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.quickFilterValues && shouldQuickFilterExcludeHiddenColumns(filterModel)) {\n      // re-apply filters because the quick filter results may have changed\n      apiRef.current.unstable_applyFilters();\n    }\n  });\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    apiRef.current.unstable_applyFilters();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.filterModel !== undefined) {\n      apiRef.current.setFilterModel(props.filterModel);\n    }\n  }, [apiRef, logger, props.filterModel]);\n};", "map": {"version": 3, "names": ["_extends", "React", "lruMemoize", "unstable_useEnhancedEffect", "useEnhancedEffect", "useLazyRef", "useGridApiEventHandler", "useGridApiMethod", "useGridLogger", "gridColumnLookupSelector", "GridPreferencePanelsValue", "getDefaultGridFilterModel", "gridFilterModelSelector", "useFirstRender", "gridRowsLookupSelector", "useGridRegisterPipeProcessor", "GRID_DEFAULT_STRATEGY", "useGridRegisterStrategyProcessor", "buildAggregatedFilterApplier", "sanitizeFilterModel", "mergeStateWithFilterModel", "cleanFilterItem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shouldQuickFilterExcludeHiddenColumns", "isDeepEqual", "jsx", "_jsx", "filterStateInitializer", "state", "props", "apiRef", "filterModel", "initialState", "filter", "disableMultipleColumnsFiltering", "filteredRowsLookup", "filteredChildrenCountLookup", "filteredDescendantCountLookup", "visibleRowsLookup", "getVisibleRowsLookup", "params", "getVisibleRowsLookupState", "current", "applyStrategyProcessor", "tree", "rows", "createMemoizedValues", "Object", "values", "useGridFilter", "logger", "registerControlState", "stateId", "propModel", "propOnChange", "onFilterModelChange", "stateSelector", "changeEvent", "updateFilteredRows", "useCallback", "setState", "instanceId", "filterState", "getFilterState", "newState", "visibleRowsLookupState", "publishEvent", "addColumnMenuItem", "columnMenuItems", "colDef", "filterable", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "applyFilters", "forceUpdate", "upsertFilterItem", "item", "items", "itemIndex", "findIndex", "filterItem", "id", "push", "setFilterModel", "upsertFilterItems", "existingItems", "for<PERSON>ach", "deleteFilterItem", "itemToDelete", "length", "showFilterPanel", "targetColumnField", "panelId", "labelId", "debug", "filterItemsWithValue", "value", "undefined", "Array", "isArray", "column", "getColumn", "field", "filterOperator", "filterOperators", "find", "operator", "requiresFilterValue", "newFilterItems", "filterItemOnTarget", "targetColumn", "showPreferences", "filters", "hideFilterPanel", "hidePreferences", "setFilterLogicOperator", "logicOperator", "setQuickFilter<PERSON><PERSON><PERSON>", "quickFilterV<PERSON>ues", "model", "reason", "currentModel", "updateControlState", "unstable_applyFilters", "inputFilterModel", "isRowMatchingFilters", "filterMode", "disableEval", "filterResult", "filterApi", "ignoreDiacritics", "stateExportPreProcessing", "prevState", "context", "filterModelToExport", "shouldExportFilterModel", "exportOnlyDirtyModels", "stateRestorePreProcessing", "stateToRestore", "callbacks", "preferencePanelPreProcessing", "initialValue", "FilterPanel", "slots", "filterPanel", "slotProps", "getRowId", "getRowsRef", "flatFilteringMethod", "dataRowIdToModelLookup", "filterCache", "result", "passingFilterItems", "passingQuickF<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "row", "isRowPassing", "footerId", "footer", "handleColumnsChange", "columnsLookup", "handleStrategyProcessorChange", "methodName", "updateVisibleRowsLookupState"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/filter/useGridFilter.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { lruMemoize } from 'reselect';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useLazyRef } from \"../../utils/useLazyRef.js\";\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnLookupSelector } from \"../columns/gridColumnsSelector.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/gridPreferencePanelsValue.js\";\nimport { getDefaultGridFilterModel } from \"./gridFilterState.js\";\nimport { gridFilterModelSelector } from \"./gridFilterSelector.js\";\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nimport { gridRowsLookupSelector } from \"../rows/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/index.js\";\nimport { buildAggregatedFilterApplier, sanitizeFilterModel, mergeStateWithFilterModel, cleanFilterItem, passFilterLogic, shouldQuickFilterExcludeHiddenColumns } from \"./gridFilterUtils.js\";\nimport { isDeepEqual } from \"../../../utils/utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const filterStateInitializer = (state, props, apiRef) => {\n  const filterModel = props.filterModel ?? props.initialState?.filter?.filterModel ?? getDefaultGridFilterModel();\n  return _extends({}, state, {\n    filter: {\n      filterModel: sanitizeFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef),\n      filteredRowsLookup: {},\n      filteredChildrenCountLookup: {},\n      filteredDescendantCountLookup: {}\n    },\n    visibleRowsLookup: {}\n  });\n};\nconst getVisibleRowsLookup = params => {\n  // For flat tree, the `visibleRowsLookup` and the `filteredRowsLookup` are equals since no row is collapsed.\n  return params.filteredRowsLookup;\n};\nfunction getVisibleRowsLookupState(apiRef, state) {\n  return apiRef.current.applyStrategyProcessor('visibleRowsLookupCreation', {\n    tree: state.rows.tree,\n    filteredRowsLookup: state.filter.filteredRowsLookup\n  });\n}\nfunction createMemoizedValues() {\n  return lruMemoize(Object.values);\n}\n\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n * @requires useGridRows (event)\n */\nexport const useGridFilter = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFilter');\n  apiRef.current.registerControlState({\n    stateId: 'filter',\n    propModel: props.filterModel,\n    propOnChange: props.onFilterModelChange,\n    stateSelector: gridFilterModelSelector,\n    changeEvent: 'filterModelChange'\n  });\n  const updateFilteredRows = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const filterModel = gridFilterModelSelector(state, apiRef.current.instanceId);\n      const filterState = apiRef.current.getFilterState(filterModel);\n      const newState = _extends({}, state, {\n        filter: _extends({}, state.filter, filterState)\n      });\n      const visibleRowsLookupState = getVisibleRowsLookupState(apiRef, newState);\n      return _extends({}, newState, {\n        visibleRowsLookup: visibleRowsLookupState\n      });\n    });\n    apiRef.current.publishEvent('filteredRowsSet');\n  }, [apiRef]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.filterable === false || props.disableColumnFilter) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuFilterItem'];\n  }, [props.disableColumnFilter]);\n\n  /**\n   * API METHODS\n   */\n  const applyFilters = React.useCallback(() => {\n    updateFilteredRows();\n    apiRef.current.forceUpdate();\n  }, [apiRef, updateFilteredRows]);\n  const upsertFilterItem = React.useCallback(item => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = [...filterModel.items];\n    const itemIndex = items.findIndex(filterItem => filterItem.id === item.id);\n    if (itemIndex === -1) {\n      items.push(item);\n    } else {\n      items[itemIndex] = item;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'upsertFilterItem');\n  }, [apiRef]);\n  const upsertFilterItems = React.useCallback(items => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const existingItems = [...filterModel.items];\n    items.forEach(item => {\n      const itemIndex = existingItems.findIndex(filterItem => filterItem.id === item.id);\n      if (itemIndex === -1) {\n        existingItems.push(item);\n      } else {\n        existingItems[itemIndex] = item;\n      }\n    });\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items: existingItems\n    }), 'upsertFilterItems');\n  }, [apiRef]);\n  const deleteFilterItem = React.useCallback(itemToDelete => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = filterModel.items.filter(item => item.id !== itemToDelete.id);\n    if (items.length === filterModel.items.length) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'deleteFilterItem');\n  }, [apiRef]);\n  const showFilterPanel = React.useCallback((targetColumnField, panelId, labelId) => {\n    logger.debug('Displaying filter panel');\n    if (targetColumnField) {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterItemsWithValue = filterModel.items.filter(item => {\n        if (item.value !== undefined) {\n          // Some filters like `isAnyOf` support array as `item.value`.\n          // If array is empty, we want to remove it from the filter model.\n          if (Array.isArray(item.value) && item.value.length === 0) {\n            return false;\n          }\n          return true;\n        }\n        const column = apiRef.current.getColumn(item.field);\n        const filterOperator = column.filterOperators?.find(operator => operator.value === item.operator);\n        const requiresFilterValue = typeof filterOperator?.requiresFilterValue === 'undefined' ? true : filterOperator?.requiresFilterValue;\n\n        // Operators like `isEmpty` don't have and don't require `item.value`.\n        // So we don't want to remove them from the filter model if `item.value === undefined`.\n        // See https://github.com/mui/mui-x/issues/5402\n        if (requiresFilterValue) {\n          return false;\n        }\n        return true;\n      });\n      let newFilterItems;\n      const filterItemOnTarget = filterItemsWithValue.find(item => item.field === targetColumnField);\n      const targetColumn = apiRef.current.getColumn(targetColumnField);\n      if (filterItemOnTarget) {\n        newFilterItems = filterItemsWithValue;\n      } else if (props.disableMultipleColumnsFiltering) {\n        newFilterItems = [cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      } else {\n        newFilterItems = [...filterItemsWithValue, cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      }\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n    apiRef.current.showPreferences(GridPreferencePanelsValue.filters, panelId, labelId);\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const hideFilterPanel = React.useCallback(() => {\n    logger.debug('Hiding filter panel');\n    apiRef.current.hidePreferences();\n  }, [apiRef, logger]);\n  const setFilterLogicOperator = React.useCallback(logicOperator => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.logicOperator === logicOperator) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      logicOperator\n    }), 'changeLogicOperator');\n  }, [apiRef]);\n  const setQuickFilterValues = React.useCallback(values => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (isDeepEqual(filterModel.quickFilterValues, values)) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      quickFilterValues: [...values]\n    }));\n  }, [apiRef]);\n  const setFilterModel = React.useCallback((model, reason) => {\n    const currentModel = gridFilterModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug('Setting filter model');\n      apiRef.current.updateControlState('filter', mergeStateWithFilterModel(model, props.disableMultipleColumnsFiltering, apiRef), reason);\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const getFilterState = React.useCallback(inputFilterModel => {\n    const filterModel = sanitizeFilterModel(inputFilterModel, props.disableMultipleColumnsFiltering, apiRef);\n    const isRowMatchingFilters = props.filterMode === 'client' ? buildAggregatedFilterApplier(filterModel, apiRef, props.disableEval) : null;\n    const filterResult = apiRef.current.applyStrategyProcessor('filtering', {\n      isRowMatchingFilters,\n      filterModel: filterModel ?? getDefaultGridFilterModel()\n    });\n    return _extends({}, filterResult, {\n      filterModel\n    });\n  }, [props.disableMultipleColumnsFiltering, props.filterMode, props.disableEval, apiRef]);\n  const filterApi = {\n    setFilterLogicOperator,\n    unstable_applyFilters: applyFilters,\n    deleteFilterItem,\n    upsertFilterItem,\n    upsertFilterItems,\n    setFilterModel,\n    showFilterPanel,\n    hideFilterPanel,\n    setQuickFilterValues,\n    ignoreDiacritics: props.ignoreDiacritics,\n    getFilterState\n  };\n  useGridApiMethod(apiRef, filterApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const filterModelToExport = gridFilterModelSelector(apiRef);\n    const shouldExportFilterModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.filterModel != null ||\n    // Always export if the model has been initialized\n    props.initialState?.filter?.filterModel != null ||\n    // Export if the model is not equal to the default value\n    !isDeepEqual(filterModelToExport, getDefaultGridFilterModel());\n    if (!shouldExportFilterModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      filter: {\n        filterModel: filterModelToExport\n      }\n    });\n  }, [apiRef, props.filterModel, props.initialState?.filter?.filterModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const filterModel = context.stateToRestore.filter?.filterModel;\n    if (filterModel == null) {\n      return params;\n    }\n    apiRef.current.updateControlState('filter', mergeStateWithFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef), 'restoreState');\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.unstable_applyFilters]\n    });\n  }, [apiRef, props.disableMultipleColumnsFiltering]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.filters) {\n      const FilterPanel = props.slots.filterPanel;\n      return /*#__PURE__*/_jsx(FilterPanel, _extends({}, props.slotProps?.filterPanel));\n    }\n    return initialValue;\n  }, [props.slots.filterPanel, props.slotProps?.filterPanel]);\n  const {\n    getRowId\n  } = props;\n  const getRowsRef = useLazyRef(createMemoizedValues);\n  const flatFilteringMethod = React.useCallback(params => {\n    if (props.filterMode !== 'client' || !params.isRowMatchingFilters) {\n      return {\n        filteredRowsLookup: {},\n        filteredChildrenCountLookup: {},\n        filteredDescendantCountLookup: {}\n      };\n    }\n    const dataRowIdToModelLookup = gridRowsLookupSelector(apiRef);\n    const filteredRowsLookup = {};\n    const {\n      isRowMatchingFilters\n    } = params;\n    const filterCache = {};\n    const result = {\n      passingFilterItems: null,\n      passingQuickFilterValues: null\n    };\n    const rows = getRowsRef.current(apiRef.current.state.rows.dataRowIdToModelLookup);\n    for (let i = 0; i < rows.length; i += 1) {\n      const row = rows[i];\n      const id = getRowId ? getRowId(row) : row.id;\n      isRowMatchingFilters(row, undefined, result);\n      const isRowPassing = passFilterLogic([result.passingFilterItems], [result.passingQuickFilterValues], params.filterModel, apiRef, filterCache);\n      filteredRowsLookup[id] = isRowPassing;\n    }\n    const footerId = 'auto-generated-group-footer-root';\n    const footer = dataRowIdToModelLookup[footerId];\n    if (footer) {\n      filteredRowsLookup[footerId] = true;\n    }\n    return {\n      filteredRowsLookup,\n      filteredChildrenCountLookup: {},\n      filteredDescendantCountLookup: {}\n    };\n  }, [apiRef, props.filterMode, getRowId, getRowsRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'filtering', flatFilteringMethod);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'visibleRowsLookupCreation', getVisibleRowsLookup);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnsChange = React.useCallback(() => {\n    logger.debug('onColUpdated - GridColumns changed, applying filters');\n    const filterModel = gridFilterModelSelector(apiRef);\n    const columnsLookup = gridColumnLookupSelector(apiRef);\n    const newFilterItems = filterModel.items.filter(item => item.field && columnsLookup[item.field]);\n    if (newFilterItems.length < filterModel.items.length) {\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n  }, [apiRef, logger]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'filtering') {\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef]);\n  const updateVisibleRowsLookupState = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        visibleRowsLookup: getVisibleRowsLookupState(apiRef, state)\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n\n  // Do not call `apiRef.current.forceUpdate` to avoid re-render before updating the sorted rows.\n  // Otherwise, the state is not consistent during the render\n  useGridApiEventHandler(apiRef, 'rowsSet', updateFilteredRows);\n  useGridApiEventHandler(apiRef, 'columnsChange', handleColumnsChange);\n  useGridApiEventHandler(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridApiEventHandler(apiRef, 'rowExpansionChange', updateVisibleRowsLookupState);\n  useGridApiEventHandler(apiRef, 'columnVisibilityModelChange', () => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.quickFilterValues && shouldQuickFilterExcludeHiddenColumns(filterModel)) {\n      // re-apply filters because the quick filter results may have changed\n      apiRef.current.unstable_applyFilters();\n    }\n  });\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    apiRef.current.unstable_applyFilters();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.filterModel !== undefined) {\n      apiRef.current.setFilterModel(props.filterModel);\n    }\n  }, [apiRef, logger, props.filterModel]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC5E,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,yBAAyB,QAAQ,sBAAsB;AAChE,SAASC,uBAAuB,QAAQ,yBAAyB;AACjE,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,sBAAsB,QAAQ,kBAAkB;AACzD,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,qBAAqB,EAAEC,gCAAgC,QAAQ,wCAAwC;AAChH,SAASC,4BAA4B,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,qCAAqC,QAAQ,sBAAsB;AAC5L,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC9D,MAAMC,WAAW,GAAGF,KAAK,CAACE,WAAW,IAAIF,KAAK,CAACG,YAAY,EAAEC,MAAM,EAAEF,WAAW,IAAIpB,yBAAyB,CAAC,CAAC;EAC/G,OAAOX,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACzBK,MAAM,EAAE;MACNF,WAAW,EAAEZ,mBAAmB,CAACY,WAAW,EAAEF,KAAK,CAACK,+BAA+B,EAAEJ,MAAM,CAAC;MAC5FK,kBAAkB,EAAE,CAAC,CAAC;MACtBC,2BAA2B,EAAE,CAAC,CAAC;MAC/BC,6BAA6B,EAAE,CAAC;IAClC,CAAC;IACDC,iBAAiB,EAAE,CAAC;EACtB,CAAC,CAAC;AACJ,CAAC;AACD,MAAMC,oBAAoB,GAAGC,MAAM,IAAI;EACrC;EACA,OAAOA,MAAM,CAACL,kBAAkB;AAClC,CAAC;AACD,SAASM,yBAAyBA,CAACX,MAAM,EAAEF,KAAK,EAAE;EAChD,OAAOE,MAAM,CAACY,OAAO,CAACC,sBAAsB,CAAC,2BAA2B,EAAE;IACxEC,IAAI,EAAEhB,KAAK,CAACiB,IAAI,CAACD,IAAI;IACrBT,kBAAkB,EAAEP,KAAK,CAACK,MAAM,CAACE;EACnC,CAAC,CAAC;AACJ;AACA,SAASW,oBAAoBA,CAAA,EAAG;EAC9B,OAAO5C,UAAU,CAAC6C,MAAM,CAACC,MAAM,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAACnB,MAAM,EAAED,KAAK,KAAK;EAC9C,MAAMqB,MAAM,GAAG1C,aAAa,CAACsB,MAAM,EAAE,eAAe,CAAC;EACrDA,MAAM,CAACY,OAAO,CAACS,oBAAoB,CAAC;IAClCC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAExB,KAAK,CAACE,WAAW;IAC5BuB,YAAY,EAAEzB,KAAK,CAAC0B,mBAAmB;IACvCC,aAAa,EAAE5C,uBAAuB;IACtC6C,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGzD,KAAK,CAAC0D,WAAW,CAAC,MAAM;IACjD7B,MAAM,CAACY,OAAO,CAACkB,QAAQ,CAAChC,KAAK,IAAI;MAC/B,MAAMG,WAAW,GAAGnB,uBAAuB,CAACgB,KAAK,EAAEE,MAAM,CAACY,OAAO,CAACmB,UAAU,CAAC;MAC7E,MAAMC,WAAW,GAAGhC,MAAM,CAACY,OAAO,CAACqB,cAAc,CAAChC,WAAW,CAAC;MAC9D,MAAMiC,QAAQ,GAAGhE,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;QACnCK,MAAM,EAAEjC,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,CAACK,MAAM,EAAE6B,WAAW;MAChD,CAAC,CAAC;MACF,MAAMG,sBAAsB,GAAGxB,yBAAyB,CAACX,MAAM,EAAEkC,QAAQ,CAAC;MAC1E,OAAOhE,QAAQ,CAAC,CAAC,CAAC,EAAEgE,QAAQ,EAAE;QAC5B1B,iBAAiB,EAAE2B;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFnC,MAAM,CAACY,OAAO,CAACwB,YAAY,CAAC,iBAAiB,CAAC;EAChD,CAAC,EAAE,CAACpC,MAAM,CAAC,CAAC;EACZ,MAAMqC,iBAAiB,GAAGlE,KAAK,CAAC0D,WAAW,CAAC,CAACS,eAAe,EAAEC,MAAM,KAAK;IACvE,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACC,UAAU,KAAK,KAAK,IAAIzC,KAAK,CAAC0C,mBAAmB,EAAE;MAC9E,OAAOH,eAAe;IACxB;IACA,OAAO,CAAC,GAAGA,eAAe,EAAE,sBAAsB,CAAC;EACrD,CAAC,EAAE,CAACvC,KAAK,CAAC0C,mBAAmB,CAAC,CAAC;;EAE/B;AACF;AACA;EACE,MAAMC,YAAY,GAAGvE,KAAK,CAAC0D,WAAW,CAAC,MAAM;IAC3CD,kBAAkB,CAAC,CAAC;IACpB5B,MAAM,CAACY,OAAO,CAAC+B,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAAC3C,MAAM,EAAE4B,kBAAkB,CAAC,CAAC;EAChC,MAAMgB,gBAAgB,GAAGzE,KAAK,CAAC0D,WAAW,CAACgB,IAAI,IAAI;IACjD,MAAM5C,WAAW,GAAGnB,uBAAuB,CAACkB,MAAM,CAAC;IACnD,MAAM8C,KAAK,GAAG,CAAC,GAAG7C,WAAW,CAAC6C,KAAK,CAAC;IACpC,MAAMC,SAAS,GAAGD,KAAK,CAACE,SAAS,CAACC,UAAU,IAAIA,UAAU,CAACC,EAAE,KAAKL,IAAI,CAACK,EAAE,CAAC;IAC1E,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBD,KAAK,CAACK,IAAI,CAACN,IAAI,CAAC;IAClB,CAAC,MAAM;MACLC,KAAK,CAACC,SAAS,CAAC,GAAGF,IAAI;IACzB;IACA7C,MAAM,CAACY,OAAO,CAACwC,cAAc,CAAClF,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtD6C;IACF,CAAC,CAAC,EAAE,kBAAkB,CAAC;EACzB,CAAC,EAAE,CAAC9C,MAAM,CAAC,CAAC;EACZ,MAAMqD,iBAAiB,GAAGlF,KAAK,CAAC0D,WAAW,CAACiB,KAAK,IAAI;IACnD,MAAM7C,WAAW,GAAGnB,uBAAuB,CAACkB,MAAM,CAAC;IACnD,MAAMsD,aAAa,GAAG,CAAC,GAAGrD,WAAW,CAAC6C,KAAK,CAAC;IAC5CA,KAAK,CAACS,OAAO,CAACV,IAAI,IAAI;MACpB,MAAME,SAAS,GAAGO,aAAa,CAACN,SAAS,CAACC,UAAU,IAAIA,UAAU,CAACC,EAAE,KAAKL,IAAI,CAACK,EAAE,CAAC;MAClF,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBO,aAAa,CAACH,IAAI,CAACN,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLS,aAAa,CAACP,SAAS,CAAC,GAAGF,IAAI;MACjC;IACF,CAAC,CAAC;IACF7C,MAAM,CAACY,OAAO,CAACwC,cAAc,CAAClF,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtD6C,KAAK,EAAEQ;IACT,CAAC,CAAC,EAAE,mBAAmB,CAAC;EAC1B,CAAC,EAAE,CAACtD,MAAM,CAAC,CAAC;EACZ,MAAMwD,gBAAgB,GAAGrF,KAAK,CAAC0D,WAAW,CAAC4B,YAAY,IAAI;IACzD,MAAMxD,WAAW,GAAGnB,uBAAuB,CAACkB,MAAM,CAAC;IACnD,MAAM8C,KAAK,GAAG7C,WAAW,CAAC6C,KAAK,CAAC3C,MAAM,CAAC0C,IAAI,IAAIA,IAAI,CAACK,EAAE,KAAKO,YAAY,CAACP,EAAE,CAAC;IAC3E,IAAIJ,KAAK,CAACY,MAAM,KAAKzD,WAAW,CAAC6C,KAAK,CAACY,MAAM,EAAE;MAC7C;IACF;IACA1D,MAAM,CAACY,OAAO,CAACwC,cAAc,CAAClF,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtD6C;IACF,CAAC,CAAC,EAAE,kBAAkB,CAAC;EACzB,CAAC,EAAE,CAAC9C,MAAM,CAAC,CAAC;EACZ,MAAM2D,eAAe,GAAGxF,KAAK,CAAC0D,WAAW,CAAC,CAAC+B,iBAAiB,EAAEC,OAAO,EAAEC,OAAO,KAAK;IACjF1C,MAAM,CAAC2C,KAAK,CAAC,yBAAyB,CAAC;IACvC,IAAIH,iBAAiB,EAAE;MACrB,MAAM3D,WAAW,GAAGnB,uBAAuB,CAACkB,MAAM,CAAC;MACnD,MAAMgE,oBAAoB,GAAG/D,WAAW,CAAC6C,KAAK,CAAC3C,MAAM,CAAC0C,IAAI,IAAI;QAC5D,IAAIA,IAAI,CAACoB,KAAK,KAAKC,SAAS,EAAE;UAC5B;UACA;UACA,IAAIC,KAAK,CAACC,OAAO,CAACvB,IAAI,CAACoB,KAAK,CAAC,IAAIpB,IAAI,CAACoB,KAAK,CAACP,MAAM,KAAK,CAAC,EAAE;YACxD,OAAO,KAAK;UACd;UACA,OAAO,IAAI;QACb;QACA,MAAMW,MAAM,GAAGrE,MAAM,CAACY,OAAO,CAAC0D,SAAS,CAACzB,IAAI,CAAC0B,KAAK,CAAC;QACnD,MAAMC,cAAc,GAAGH,MAAM,CAACI,eAAe,EAAEC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACV,KAAK,KAAKpB,IAAI,CAAC8B,QAAQ,CAAC;QACjG,MAAMC,mBAAmB,GAAG,OAAOJ,cAAc,EAAEI,mBAAmB,KAAK,WAAW,GAAG,IAAI,GAAGJ,cAAc,EAAEI,mBAAmB;;QAEnI;QACA;QACA;QACA,IAAIA,mBAAmB,EAAE;UACvB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAIC,cAAc;MAClB,MAAMC,kBAAkB,GAAGd,oBAAoB,CAACU,IAAI,CAAC7B,IAAI,IAAIA,IAAI,CAAC0B,KAAK,KAAKX,iBAAiB,CAAC;MAC9F,MAAMmB,YAAY,GAAG/E,MAAM,CAACY,OAAO,CAAC0D,SAAS,CAACV,iBAAiB,CAAC;MAChE,IAAIkB,kBAAkB,EAAE;QACtBD,cAAc,GAAGb,oBAAoB;MACvC,CAAC,MAAM,IAAIjE,KAAK,CAACK,+BAA+B,EAAE;QAChDyE,cAAc,GAAG,CAACtF,eAAe,CAAC;UAChCgF,KAAK,EAAEX,iBAAiB;UACxBe,QAAQ,EAAEI,YAAY,CAACN,eAAe,CAAC,CAAC,CAAC,CAACR;QAC5C,CAAC,EAAEjE,MAAM,CAAC,CAAC;MACb,CAAC,MAAM;QACL6E,cAAc,GAAG,CAAC,GAAGb,oBAAoB,EAAEzE,eAAe,CAAC;UACzDgF,KAAK,EAAEX,iBAAiB;UACxBe,QAAQ,EAAEI,YAAY,CAACN,eAAe,CAAC,CAAC,CAAC,CAACR;QAC5C,CAAC,EAAEjE,MAAM,CAAC,CAAC;MACb;MACAA,MAAM,CAACY,OAAO,CAACwC,cAAc,CAAClF,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;QACtD6C,KAAK,EAAE+B;MACT,CAAC,CAAC,CAAC;IACL;IACA7E,MAAM,CAACY,OAAO,CAACoE,eAAe,CAACpG,yBAAyB,CAACqG,OAAO,EAAEpB,OAAO,EAAEC,OAAO,CAAC;EACrF,CAAC,EAAE,CAAC9D,MAAM,EAAEoB,MAAM,EAAErB,KAAK,CAACK,+BAA+B,CAAC,CAAC;EAC3D,MAAM8E,eAAe,GAAG/G,KAAK,CAAC0D,WAAW,CAAC,MAAM;IAC9CT,MAAM,CAAC2C,KAAK,CAAC,qBAAqB,CAAC;IACnC/D,MAAM,CAACY,OAAO,CAACuE,eAAe,CAAC,CAAC;EAClC,CAAC,EAAE,CAACnF,MAAM,EAAEoB,MAAM,CAAC,CAAC;EACpB,MAAMgE,sBAAsB,GAAGjH,KAAK,CAAC0D,WAAW,CAACwD,aAAa,IAAI;IAChE,MAAMpF,WAAW,GAAGnB,uBAAuB,CAACkB,MAAM,CAAC;IACnD,IAAIC,WAAW,CAACoF,aAAa,KAAKA,aAAa,EAAE;MAC/C;IACF;IACArF,MAAM,CAACY,OAAO,CAACwC,cAAc,CAAClF,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtDoF;IACF,CAAC,CAAC,EAAE,qBAAqB,CAAC;EAC5B,CAAC,EAAE,CAACrF,MAAM,CAAC,CAAC;EACZ,MAAMsF,oBAAoB,GAAGnH,KAAK,CAAC0D,WAAW,CAACX,MAAM,IAAI;IACvD,MAAMjB,WAAW,GAAGnB,uBAAuB,CAACkB,MAAM,CAAC;IACnD,IAAIN,WAAW,CAACO,WAAW,CAACsF,iBAAiB,EAAErE,MAAM,CAAC,EAAE;MACtD;IACF;IACAlB,MAAM,CAACY,OAAO,CAACwC,cAAc,CAAClF,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtDsF,iBAAiB,EAAE,CAAC,GAAGrE,MAAM;IAC/B,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAClB,MAAM,CAAC,CAAC;EACZ,MAAMoD,cAAc,GAAGjF,KAAK,CAAC0D,WAAW,CAAC,CAAC2D,KAAK,EAAEC,MAAM,KAAK;IAC1D,MAAMC,YAAY,GAAG5G,uBAAuB,CAACkB,MAAM,CAAC;IACpD,IAAI0F,YAAY,KAAKF,KAAK,EAAE;MAC1BpE,MAAM,CAAC2C,KAAK,CAAC,sBAAsB,CAAC;MACpC/D,MAAM,CAACY,OAAO,CAAC+E,kBAAkB,CAAC,QAAQ,EAAErG,yBAAyB,CAACkG,KAAK,EAAEzF,KAAK,CAACK,+BAA+B,EAAEJ,MAAM,CAAC,EAAEyF,MAAM,CAAC;MACpIzF,MAAM,CAACY,OAAO,CAACgF,qBAAqB,CAAC,CAAC;IACxC;EACF,CAAC,EAAE,CAAC5F,MAAM,EAAEoB,MAAM,EAAErB,KAAK,CAACK,+BAA+B,CAAC,CAAC;EAC3D,MAAM6B,cAAc,GAAG9D,KAAK,CAAC0D,WAAW,CAACgE,gBAAgB,IAAI;IAC3D,MAAM5F,WAAW,GAAGZ,mBAAmB,CAACwG,gBAAgB,EAAE9F,KAAK,CAACK,+BAA+B,EAAEJ,MAAM,CAAC;IACxG,MAAM8F,oBAAoB,GAAG/F,KAAK,CAACgG,UAAU,KAAK,QAAQ,GAAG3G,4BAA4B,CAACa,WAAW,EAAED,MAAM,EAAED,KAAK,CAACiG,WAAW,CAAC,GAAG,IAAI;IACxI,MAAMC,YAAY,GAAGjG,MAAM,CAACY,OAAO,CAACC,sBAAsB,CAAC,WAAW,EAAE;MACtEiF,oBAAoB;MACpB7F,WAAW,EAAEA,WAAW,IAAIpB,yBAAyB,CAAC;IACxD,CAAC,CAAC;IACF,OAAOX,QAAQ,CAAC,CAAC,CAAC,EAAE+H,YAAY,EAAE;MAChChG;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACF,KAAK,CAACK,+BAA+B,EAAEL,KAAK,CAACgG,UAAU,EAAEhG,KAAK,CAACiG,WAAW,EAAEhG,MAAM,CAAC,CAAC;EACxF,MAAMkG,SAAS,GAAG;IAChBd,sBAAsB;IACtBQ,qBAAqB,EAAElD,YAAY;IACnCc,gBAAgB;IAChBZ,gBAAgB;IAChBS,iBAAiB;IACjBD,cAAc;IACdO,eAAe;IACfuB,eAAe;IACfI,oBAAoB;IACpBa,gBAAgB,EAAEpG,KAAK,CAACoG,gBAAgB;IACxClE;EACF,CAAC;EACDxD,gBAAgB,CAACuB,MAAM,EAAEkG,SAAS,EAAE,QAAQ,CAAC;;EAE7C;AACF;AACA;EACE,MAAME,wBAAwB,GAAGjI,KAAK,CAAC0D,WAAW,CAAC,CAACwE,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMC,mBAAmB,GAAGzH,uBAAuB,CAACkB,MAAM,CAAC;IAC3D,MAAMwG,uBAAuB;IAC7B;IACA,CAACF,OAAO,CAACG,qBAAqB;IAC9B;IACA1G,KAAK,CAACE,WAAW,IAAI,IAAI;IACzB;IACAF,KAAK,CAACG,YAAY,EAAEC,MAAM,EAAEF,WAAW,IAAI,IAAI;IAC/C;IACA,CAACP,WAAW,CAAC6G,mBAAmB,EAAE1H,yBAAyB,CAAC,CAAC,CAAC;IAC9D,IAAI,CAAC2H,uBAAuB,EAAE;MAC5B,OAAOH,SAAS;IAClB;IACA,OAAOnI,QAAQ,CAAC,CAAC,CAAC,EAAEmI,SAAS,EAAE;MAC7BlG,MAAM,EAAE;QACNF,WAAW,EAAEsG;MACf;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvG,MAAM,EAAED,KAAK,CAACE,WAAW,EAAEF,KAAK,CAACG,YAAY,EAAEC,MAAM,EAAEF,WAAW,CAAC,CAAC;EACxE,MAAMyG,yBAAyB,GAAGvI,KAAK,CAAC0D,WAAW,CAAC,CAACnB,MAAM,EAAE4F,OAAO,KAAK;IACvE,MAAMrG,WAAW,GAAGqG,OAAO,CAACK,cAAc,CAACxG,MAAM,EAAEF,WAAW;IAC9D,IAAIA,WAAW,IAAI,IAAI,EAAE;MACvB,OAAOS,MAAM;IACf;IACAV,MAAM,CAACY,OAAO,CAAC+E,kBAAkB,CAAC,QAAQ,EAAErG,yBAAyB,CAACW,WAAW,EAAEF,KAAK,CAACK,+BAA+B,EAAEJ,MAAM,CAAC,EAAE,cAAc,CAAC;IAClJ,OAAO9B,QAAQ,CAAC,CAAC,CAAC,EAAEwC,MAAM,EAAE;MAC1BkG,SAAS,EAAE,CAAC,GAAGlG,MAAM,CAACkG,SAAS,EAAE5G,MAAM,CAACY,OAAO,CAACgF,qBAAqB;IACvE,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC5F,MAAM,EAAED,KAAK,CAACK,+BAA+B,CAAC,CAAC;EACnD,MAAMyG,4BAA4B,GAAG1I,KAAK,CAAC0D,WAAW,CAAC,CAACiF,YAAY,EAAE7C,KAAK,KAAK;IAC9E,IAAIA,KAAK,KAAKrF,yBAAyB,CAACqG,OAAO,EAAE;MAC/C,MAAM8B,WAAW,GAAGhH,KAAK,CAACiH,KAAK,CAACC,WAAW;MAC3C,OAAO,aAAarH,IAAI,CAACmH,WAAW,EAAE7I,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACmH,SAAS,EAAED,WAAW,CAAC,CAAC;IACnF;IACA,OAAOH,YAAY;EACrB,CAAC,EAAE,CAAC/G,KAAK,CAACiH,KAAK,CAACC,WAAW,EAAElH,KAAK,CAACmH,SAAS,EAAED,WAAW,CAAC,CAAC;EAC3D,MAAM;IACJE;EACF,CAAC,GAAGpH,KAAK;EACT,MAAMqH,UAAU,GAAG7I,UAAU,CAACyC,oBAAoB,CAAC;EACnD,MAAMqG,mBAAmB,GAAGlJ,KAAK,CAAC0D,WAAW,CAACnB,MAAM,IAAI;IACtD,IAAIX,KAAK,CAACgG,UAAU,KAAK,QAAQ,IAAI,CAACrF,MAAM,CAACoF,oBAAoB,EAAE;MACjE,OAAO;QACLzF,kBAAkB,EAAE,CAAC,CAAC;QACtBC,2BAA2B,EAAE,CAAC,CAAC;QAC/BC,6BAA6B,EAAE,CAAC;MAClC,CAAC;IACH;IACA,MAAM+G,sBAAsB,GAAGtI,sBAAsB,CAACgB,MAAM,CAAC;IAC7D,MAAMK,kBAAkB,GAAG,CAAC,CAAC;IAC7B,MAAM;MACJyF;IACF,CAAC,GAAGpF,MAAM;IACV,MAAM6G,WAAW,GAAG,CAAC,CAAC;IACtB,MAAMC,MAAM,GAAG;MACbC,kBAAkB,EAAE,IAAI;MACxBC,wBAAwB,EAAE;IAC5B,CAAC;IACD,MAAM3G,IAAI,GAAGqG,UAAU,CAACxG,OAAO,CAACZ,MAAM,CAACY,OAAO,CAACd,KAAK,CAACiB,IAAI,CAACuG,sBAAsB,CAAC;IACjF,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5G,IAAI,CAAC2C,MAAM,EAAEiE,CAAC,IAAI,CAAC,EAAE;MACvC,MAAMC,GAAG,GAAG7G,IAAI,CAAC4G,CAAC,CAAC;MACnB,MAAMzE,EAAE,GAAGiE,QAAQ,GAAGA,QAAQ,CAACS,GAAG,CAAC,GAAGA,GAAG,CAAC1E,EAAE;MAC5C4C,oBAAoB,CAAC8B,GAAG,EAAE1D,SAAS,EAAEsD,MAAM,CAAC;MAC5C,MAAMK,YAAY,GAAGrI,eAAe,CAAC,CAACgI,MAAM,CAACC,kBAAkB,CAAC,EAAE,CAACD,MAAM,CAACE,wBAAwB,CAAC,EAAEhH,MAAM,CAACT,WAAW,EAAED,MAAM,EAAEuH,WAAW,CAAC;MAC7IlH,kBAAkB,CAAC6C,EAAE,CAAC,GAAG2E,YAAY;IACvC;IACA,MAAMC,QAAQ,GAAG,kCAAkC;IACnD,MAAMC,MAAM,GAAGT,sBAAsB,CAACQ,QAAQ,CAAC;IAC/C,IAAIC,MAAM,EAAE;MACV1H,kBAAkB,CAACyH,QAAQ,CAAC,GAAG,IAAI;IACrC;IACA,OAAO;MACLzH,kBAAkB;MAClBC,2BAA2B,EAAE,CAAC,CAAC;MAC/BC,6BAA6B,EAAE,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAACP,MAAM,EAAED,KAAK,CAACgG,UAAU,EAAEoB,QAAQ,EAAEC,UAAU,CAAC,CAAC;EACpDnI,4BAA4B,CAACe,MAAM,EAAE,YAAY,EAAEqC,iBAAiB,CAAC;EACrEpD,4BAA4B,CAACe,MAAM,EAAE,aAAa,EAAEoG,wBAAwB,CAAC;EAC7EnH,4BAA4B,CAACe,MAAM,EAAE,cAAc,EAAE0G,yBAAyB,CAAC;EAC/EzH,4BAA4B,CAACe,MAAM,EAAE,iBAAiB,EAAE6G,4BAA4B,CAAC;EACrF1H,gCAAgC,CAACa,MAAM,EAAEd,qBAAqB,EAAE,WAAW,EAAEmI,mBAAmB,CAAC;EACjGlI,gCAAgC,CAACa,MAAM,EAAEd,qBAAqB,EAAE,2BAA2B,EAAEuB,oBAAoB,CAAC;;EAElH;AACF;AACA;EACE,MAAMuH,mBAAmB,GAAG7J,KAAK,CAAC0D,WAAW,CAAC,MAAM;IAClDT,MAAM,CAAC2C,KAAK,CAAC,sDAAsD,CAAC;IACpE,MAAM9D,WAAW,GAAGnB,uBAAuB,CAACkB,MAAM,CAAC;IACnD,MAAMiI,aAAa,GAAGtJ,wBAAwB,CAACqB,MAAM,CAAC;IACtD,MAAM6E,cAAc,GAAG5E,WAAW,CAAC6C,KAAK,CAAC3C,MAAM,CAAC0C,IAAI,IAAIA,IAAI,CAAC0B,KAAK,IAAI0D,aAAa,CAACpF,IAAI,CAAC0B,KAAK,CAAC,CAAC;IAChG,IAAIM,cAAc,CAACnB,MAAM,GAAGzD,WAAW,CAAC6C,KAAK,CAACY,MAAM,EAAE;MACpD1D,MAAM,CAACY,OAAO,CAACwC,cAAc,CAAClF,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;QACtD6C,KAAK,EAAE+B;MACT,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAAC7E,MAAM,EAAEoB,MAAM,CAAC,CAAC;EACpB,MAAM8G,6BAA6B,GAAG/J,KAAK,CAAC0D,WAAW,CAACsG,UAAU,IAAI;IACpE,IAAIA,UAAU,KAAK,WAAW,EAAE;MAC9BnI,MAAM,CAACY,OAAO,CAACgF,qBAAqB,CAAC,CAAC;IACxC;EACF,CAAC,EAAE,CAAC5F,MAAM,CAAC,CAAC;EACZ,MAAMoI,4BAA4B,GAAGjK,KAAK,CAAC0D,WAAW,CAAC,MAAM;IAC3D7B,MAAM,CAACY,OAAO,CAACkB,QAAQ,CAAChC,KAAK,IAAI;MAC/B,OAAO5B,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;QACzBU,iBAAiB,EAAEG,yBAAyB,CAACX,MAAM,EAAEF,KAAK;MAC5D,CAAC,CAAC;IACJ,CAAC,CAAC;IACFE,MAAM,CAACY,OAAO,CAAC+B,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAAC3C,MAAM,CAAC,CAAC;;EAEZ;EACA;EACAxB,sBAAsB,CAACwB,MAAM,EAAE,SAAS,EAAE4B,kBAAkB,CAAC;EAC7DpD,sBAAsB,CAACwB,MAAM,EAAE,eAAe,EAAEgI,mBAAmB,CAAC;EACpExJ,sBAAsB,CAACwB,MAAM,EAAE,+BAA+B,EAAEkI,6BAA6B,CAAC;EAC9F1J,sBAAsB,CAACwB,MAAM,EAAE,oBAAoB,EAAEoI,4BAA4B,CAAC;EAClF5J,sBAAsB,CAACwB,MAAM,EAAE,6BAA6B,EAAE,MAAM;IAClE,MAAMC,WAAW,GAAGnB,uBAAuB,CAACkB,MAAM,CAAC;IACnD,IAAIC,WAAW,CAACsF,iBAAiB,IAAI9F,qCAAqC,CAACQ,WAAW,CAAC,EAAE;MACvF;MACAD,MAAM,CAACY,OAAO,CAACgF,qBAAqB,CAAC,CAAC;IACxC;EACF,CAAC,CAAC;;EAEF;AACF;AACA;EACE7G,cAAc,CAAC,MAAM;IACnBiB,MAAM,CAACY,OAAO,CAACgF,qBAAqB,CAAC,CAAC;EACxC,CAAC,CAAC;;EAEF;AACF;AACA;EACEtH,iBAAiB,CAAC,MAAM;IACtB,IAAIyB,KAAK,CAACE,WAAW,KAAKiE,SAAS,EAAE;MACnClE,MAAM,CAACY,OAAO,CAACwC,cAAc,CAACrD,KAAK,CAACE,WAAW,CAAC;IAClD;EACF,CAAC,EAAE,CAACD,MAAM,EAAEoB,MAAM,EAAErB,KAAK,CAACE,WAAW,CAAC,CAAC;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}