{"ast": null, "code": "import Symbol from './_Symbol.js';\nimport arrayMap from './_arrayMap.js';\nimport isArray from './isArray.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n  symbolToString = symbolProto ? symbolProto.toString : undefined;\n\n/**\n * The base implementation of `_.toString` which doesn't convert nullish\n * values to empty strings.\n *\n * @private\n * @param {*} value The value to process.\n * @returns {string} Returns the string.\n */\nfunction baseToString(value) {\n  // Exit early for strings to avoid a performance hit in some environments.\n  if (typeof value == 'string') {\n    return value;\n  }\n  if (isArray(value)) {\n    // Recursively convert values (susceptible to call stack limits).\n    return arrayMap(value, baseToString) + '';\n  }\n  if (isSymbol(value)) {\n    return symbolToString ? symbolToString.call(value) : '';\n  }\n  var result = value + '';\n  return result == '0' && 1 / value == -INFINITY ? '-0' : result;\n}\nexport default baseToString;", "map": {"version": 3, "names": ["Symbol", "arrayMap", "isArray", "isSymbol", "INFINITY", "symbol<PERSON>roto", "prototype", "undefined", "symbolToString", "toString", "baseToString", "value", "call", "result"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/lodash-es/_baseToString.js"], "sourcesContent": ["import Symbol from './_Symbol.js';\nimport arrayMap from './_arrayMap.js';\nimport isArray from './isArray.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolToString = symbolProto ? symbolProto.toString : undefined;\n\n/**\n * The base implementation of `_.toString` which doesn't convert nullish\n * values to empty strings.\n *\n * @private\n * @param {*} value The value to process.\n * @returns {string} Returns the string.\n */\nfunction baseToString(value) {\n  // Exit early for strings to avoid a performance hit in some environments.\n  if (typeof value == 'string') {\n    return value;\n  }\n  if (isArray(value)) {\n    // Recursively convert values (susceptible to call stack limits).\n    return arrayMap(value, baseToString) + '';\n  }\n  if (isSymbol(value)) {\n    return symbolToString ? symbolToString.call(value) : '';\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\nexport default baseToString;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,QAAQ,GAAG,CAAC,GAAG,CAAC;;AAEpB;AACA,IAAIC,WAAW,GAAGL,MAAM,GAAGA,MAAM,CAACM,SAAS,GAAGC,SAAS;EACnDC,cAAc,GAAGH,WAAW,GAAGA,WAAW,CAACI,QAAQ,GAAGF,SAAS;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,YAAYA,CAACC,KAAK,EAAE;EAC3B;EACA,IAAI,OAAOA,KAAK,IAAI,QAAQ,EAAE;IAC5B,OAAOA,KAAK;EACd;EACA,IAAIT,OAAO,CAACS,KAAK,CAAC,EAAE;IAClB;IACA,OAAOV,QAAQ,CAACU,KAAK,EAAED,YAAY,CAAC,GAAG,EAAE;EAC3C;EACA,IAAIP,QAAQ,CAACQ,KAAK,CAAC,EAAE;IACnB,OAAOH,cAAc,GAAGA,cAAc,CAACI,IAAI,CAACD,KAAK,CAAC,GAAG,EAAE;EACzD;EACA,IAAIE,MAAM,GAAIF,KAAK,GAAG,EAAG;EACzB,OAAQE,MAAM,IAAI,GAAG,IAAK,CAAC,GAAGF,KAAK,IAAK,CAACP,QAAQ,GAAI,IAAI,GAAGS,MAAM;AACpE;AAEA,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}