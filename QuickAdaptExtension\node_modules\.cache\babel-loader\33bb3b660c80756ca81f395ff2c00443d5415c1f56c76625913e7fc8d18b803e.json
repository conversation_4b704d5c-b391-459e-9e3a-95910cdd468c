{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useEventCallback as useEventcallback } from '@mui/utils';\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { gridFocusCellSelector, gridFocusColumnGroupHeaderSelector } from \"./gridFocusStateSelector.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nexport const focusStateInitializer = state => _extends({}, state, {\n  focus: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  },\n  tabIndex: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  }\n});\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridRows (method)\n * @requires useGridEditing (event)\n */\nexport const useGridFocus = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFocus');\n  const lastClickedCell = React.useRef(null);\n  const hasRootReference = apiRef.current.rootElementRef.current !== null;\n  const publishCellFocusOut = React.useCallback((cell, event) => {\n    if (cell) {\n      // The row might have been deleted\n      if (apiRef.current.getRow(cell.id)) {\n        apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n      }\n    }\n  }, [apiRef]);\n  const setCellFocus = React.useCallback((id, field) => {\n    const focusedCell = gridFocusCellSelector(apiRef);\n    if (focusedCell?.id === id && focusedCell?.field === field) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on cell with id=${id} and field=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n    apiRef.current.forceUpdate();\n\n    // The row might have been deleted\n    if (!apiRef.current.getRow(id)) {\n      return;\n    }\n    if (focusedCell) {\n      // There's a focused cell but another cell was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, {});\n    }\n    apiRef.current.publishEvent('cellFocusIn', apiRef.current.getCellParams(id, field));\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFocus = React.useCallback(function (field) {\n    let event = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFilterFocus = React.useCallback(function (field) {\n    let event = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header filter with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnGroupHeaderFocus = React.useCallback(function (field, depth) {\n    let event = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell) {\n      apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        },\n        focus: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        }\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const getColumnGroupHeaderFocus = React.useCallback(() => gridFocusColumnGroupHeaderSelector(apiRef), [apiRef]);\n  const moveFocusToRelativeCell = React.useCallback((id, field, direction) => {\n    let columnIndexToFocus = apiRef.current.getColumnIndex(field);\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const pinnedRows = gridPinnedRowsSelector(apiRef);\n\n    // Include pinned rows as well\n    const currentPageRows = [].concat(pinnedRows.top || [], currentPage.rows, pinnedRows.bottom || []);\n    let rowIndexToFocus = currentPageRows.findIndex(row => row.id === id);\n    if (direction === 'right') {\n      columnIndexToFocus += 1;\n    } else if (direction === 'left') {\n      columnIndexToFocus -= 1;\n    } else {\n      rowIndexToFocus += 1;\n    }\n    if (columnIndexToFocus >= visibleColumns.length) {\n      // Go to next row if we are after the last column\n      rowIndexToFocus += 1;\n      if (rowIndexToFocus < currentPageRows.length) {\n        // Go to first column of the next row if there's one more row\n        columnIndexToFocus = 0;\n      }\n    } else if (columnIndexToFocus < 0) {\n      // Go to previous row if we are before the first column\n      rowIndexToFocus -= 1;\n      if (rowIndexToFocus >= 0) {\n        // Go to last column of the previous if there's one more row\n        columnIndexToFocus = visibleColumns.length - 1;\n      }\n    }\n    rowIndexToFocus = clamp(rowIndexToFocus, 0, currentPageRows.length - 1);\n    const rowToFocus = currentPageRows[rowIndexToFocus];\n    if (!rowToFocus) {\n      return;\n    }\n    const colSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowToFocus.id, columnIndexToFocus);\n    if (colSpanInfo && colSpanInfo.spannedByColSpan) {\n      if (direction === 'left' || direction === 'below') {\n        columnIndexToFocus = colSpanInfo.leftVisibleCellIndex;\n      } else if (direction === 'right') {\n        columnIndexToFocus = colSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    columnIndexToFocus = clamp(columnIndexToFocus, 0, visibleColumns.length - 1);\n    const columnToFocus = visibleColumns[columnIndexToFocus];\n    apiRef.current.setCellFocus(rowToFocus.id, columnToFocus.field);\n  }, [apiRef, props.pagination, props.paginationMode]);\n  const handleCellDoubleClick = React.useCallback(_ref => {\n    let {\n      id,\n      field\n    } = _ref;\n    apiRef.current.setCellFocus(id, field);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // GRID_CELL_NAVIGATION_KEY_DOWN handles the focus on Enter, Tab and navigation keys\n    if (event.key === 'Enter' || event.key === 'Tab' || event.key === 'Shift' || isNavigationKey(event.key)) {\n      return;\n    }\n    apiRef.current.setCellFocus(params.id, params.field);\n  }, [apiRef]);\n  const handleColumnHeaderFocus = React.useCallback((_ref2, event) => {\n    let {\n      field\n    } = _ref2;\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef]);\n  const handleColumnGroupHeaderFocus = React.useCallback((_ref3, event) => {\n    let {\n      fields,\n      depth\n    } = _ref3;\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup !== null && focusedColumnGroup.depth === depth && fields.includes(focusedColumnGroup.field)) {\n      // This group cell has already been focused\n      return;\n    }\n    apiRef.current.setColumnGroupHeaderFocus(fields[0], depth, event);\n  }, [apiRef]);\n  const handleBlur = React.useCallback((_, event) => {\n    if (event.relatedTarget?.getAttribute('class')?.includes(gridClasses.columnHeader)) {\n      return;\n    }\n    logger.debug(`Clearing focus`);\n    apiRef.current.setState(state => _extends({}, state, {\n      focus: {\n        cell: null,\n        columnHeader: null,\n        columnHeaderFilter: null,\n        columnGroupHeader: null\n      }\n    }));\n  }, [logger, apiRef]);\n  const handleCellMouseDown = React.useCallback(params => {\n    lastClickedCell.current = params;\n  }, []);\n  const handleDocumentClick = React.useCallback(event => {\n    const cellParams = lastClickedCell.current;\n    lastClickedCell.current = null;\n    const focusedCell = gridFocusCellSelector(apiRef);\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    if (!focusedCell) {\n      if (cellParams) {\n        apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n      }\n      return;\n    }\n    if (cellParams?.id === focusedCell.id && cellParams?.field === focusedCell.field) {\n      return;\n    }\n    const cellElement = apiRef.current.getCellElement(focusedCell.id, focusedCell.field);\n    if (cellElement?.contains(event.target)) {\n      return;\n    }\n    if (cellParams) {\n      apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n    } else {\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: null,\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n      apiRef.current.forceUpdate();\n\n      // There's a focused cell but another element (not a cell) was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, event);\n    }\n  }, [apiRef, publishCellFocusOut]);\n  const handleCellModeChange = React.useCallback(params => {\n    if (params.cellMode === 'view') {\n      return;\n    }\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell?.id !== params.id || cell?.field !== params.field) {\n      apiRef.current.setCellFocus(params.id, params.field);\n    }\n  }, [apiRef]);\n  const handleRowSet = React.useCallback(() => {\n    const cell = gridFocusCellSelector(apiRef);\n\n    // If the focused cell is in a row which does not exist anymore, then remove the focus\n    if (cell && !apiRef.current.getRow(cell.id)) {\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: null,\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n    }\n  }, [apiRef]);\n  const handlePaginationModelChange = useEventcallback(() => {\n    const currentFocusedCell = gridFocusCellSelector(apiRef);\n    if (!currentFocusedCell) {\n      return;\n    }\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const rowIsInCurrentPage = currentPage.rows.find(row => row.id === currentFocusedCell.id);\n    if (rowIsInCurrentPage) {\n      return;\n    }\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id: currentPage.rows[0].id,\n            field: visibleColumns[0].field\n          },\n          columnGroupHeader: null,\n          columnHeader: null,\n          columnHeaderFilter: null\n        }\n      });\n    });\n  });\n  const focusApi = {\n    setCellFocus,\n    setColumnHeaderFocus,\n    setColumnHeaderFilterFocus\n  };\n  const focusPrivateApi = {\n    moveFocusToRelativeCell,\n    setColumnGroupHeaderFocus,\n    getColumnGroupHeaderFocus\n  };\n  useGridApiMethod(apiRef, focusApi, 'public');\n  useGridApiMethod(apiRef, focusPrivateApi, 'private');\n  React.useEffect(() => {\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.addEventListener('mouseup', handleDocumentClick);\n    return () => {\n      doc.removeEventListener('mouseup', handleDocumentClick);\n    };\n  }, [apiRef, hasRootReference, handleDocumentClick]);\n  useGridApiEventHandler(apiRef, 'columnHeaderBlur', handleBlur);\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', handleCellDoubleClick);\n  useGridApiEventHandler(apiRef, 'cellMouseDown', handleCellMouseDown);\n  useGridApiEventHandler(apiRef, 'cellKeyDown', handleCellKeyDown);\n  useGridApiEventHandler(apiRef, 'cellModeChange', handleCellModeChange);\n  useGridApiEventHandler(apiRef, 'columnHeaderFocus', handleColumnHeaderFocus);\n  useGridApiEventHandler(apiRef, 'columnGroupHeaderFocus', handleColumnGroupHeaderFocus);\n  useGridApiEventHandler(apiRef, 'rowsSet', handleRowSet);\n  useGridApiEventHandler(apiRef, 'paginationModelChange', handlePaginationModelChange);\n};", "map": {"version": 3, "names": ["_extends", "React", "unstable_ownerDocument", "ownerDocument", "unstable_useEventCallback", "useEventcallback", "gridClasses", "useGridApiMethod", "useGridLogger", "useGridApiEventHandler", "isNavigationKey", "gridFocusCellSelector", "gridFocusColumnGroupHeaderSelector", "gridVisibleColumnDefinitionsSelector", "getVisibleRows", "clamp", "gridPinnedRowsSelector", "focusStateInitializer", "state", "focus", "cell", "columnHeader", "columnHeaderFilter", "columnGroupHeader", "tabIndex", "useGridFocus", "apiRef", "props", "logger", "lastClickedCell", "useRef", "hasRootReference", "current", "rootElementRef", "publishCellFocusOut", "useCallback", "event", "getRow", "id", "publishEvent", "getCellParams", "field", "setCellFocus", "focusedCell", "setState", "debug", "forceUpdate", "setColumnHeaderFocus", "arguments", "length", "undefined", "setColumnHeaderFilterFocus", "setColumnGroupHeaderFocus", "depth", "getColumnGroupHeaderFocus", "moveFocusToRelativeCell", "direction", "columnIndexToFocus", "getColumnIndex", "visibleColumns", "currentPage", "pagination", "paginationMode", "pinnedRows", "currentPageRows", "concat", "top", "rows", "bottom", "rowIndexToFocus", "findIndex", "row", "rowToFocus", "colSpanInfo", "unstable_getCellColSpanInfo", "spannedByColSpan", "leftVisibleCellIndex", "rightVisibleCellIndex", "columnToFocus", "handleCellDoubleClick", "_ref", "handleCellKeyDown", "params", "key", "handleColumnHeaderFocus", "_ref2", "target", "currentTarget", "handleColumnGroupHeaderFocus", "_ref3", "fields", "focusedColumnGroup", "includes", "handleBlur", "_", "relatedTarget", "getAttribute", "handleCellMouseDown", "handleDocumentClick", "cellParams", "canUpdateFocus", "unstable_applyPipeProcessors", "cellElement", "getCellElement", "contains", "handleCellModeChange", "cellMode", "handleRowSet", "handlePaginationModelChange", "currentFocusedCell", "rowIsInCurrentPage", "find", "focusApi", "focusPrivateApi", "useEffect", "doc", "addEventListener", "removeEventListener"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/hooks/features/focus/useGridFocus.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useEventCallback as useEventcallback } from '@mui/utils';\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridApiEventHandler } from \"../../utils/useGridApiEventHandler.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { gridFocusCellSelector, gridFocusColumnGroupHeaderSelector } from \"./gridFocusStateSelector.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nexport const focusStateInitializer = state => _extends({}, state, {\n  focus: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  },\n  tabIndex: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  }\n});\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridRows (method)\n * @requires useGridEditing (event)\n */\nexport const useGridFocus = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFocus');\n  const lastClickedCell = React.useRef(null);\n  const hasRootReference = apiRef.current.rootElementRef.current !== null;\n  const publishCellFocusOut = React.useCallback((cell, event) => {\n    if (cell) {\n      // The row might have been deleted\n      if (apiRef.current.getRow(cell.id)) {\n        apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n      }\n    }\n  }, [apiRef]);\n  const setCellFocus = React.useCallback((id, field) => {\n    const focusedCell = gridFocusCellSelector(apiRef);\n    if (focusedCell?.id === id && focusedCell?.field === field) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on cell with id=${id} and field=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n    apiRef.current.forceUpdate();\n\n    // The row might have been deleted\n    if (!apiRef.current.getRow(id)) {\n      return;\n    }\n    if (focusedCell) {\n      // There's a focused cell but another cell was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, {});\n    }\n    apiRef.current.publishEvent('cellFocusIn', apiRef.current.getCellParams(id, field));\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFocus = React.useCallback((field, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFilterFocus = React.useCallback((field, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header filter with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnGroupHeaderFocus = React.useCallback((field, depth, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell) {\n      apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        },\n        focus: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        }\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const getColumnGroupHeaderFocus = React.useCallback(() => gridFocusColumnGroupHeaderSelector(apiRef), [apiRef]);\n  const moveFocusToRelativeCell = React.useCallback((id, field, direction) => {\n    let columnIndexToFocus = apiRef.current.getColumnIndex(field);\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const pinnedRows = gridPinnedRowsSelector(apiRef);\n\n    // Include pinned rows as well\n    const currentPageRows = [].concat(pinnedRows.top || [], currentPage.rows, pinnedRows.bottom || []);\n    let rowIndexToFocus = currentPageRows.findIndex(row => row.id === id);\n    if (direction === 'right') {\n      columnIndexToFocus += 1;\n    } else if (direction === 'left') {\n      columnIndexToFocus -= 1;\n    } else {\n      rowIndexToFocus += 1;\n    }\n    if (columnIndexToFocus >= visibleColumns.length) {\n      // Go to next row if we are after the last column\n      rowIndexToFocus += 1;\n      if (rowIndexToFocus < currentPageRows.length) {\n        // Go to first column of the next row if there's one more row\n        columnIndexToFocus = 0;\n      }\n    } else if (columnIndexToFocus < 0) {\n      // Go to previous row if we are before the first column\n      rowIndexToFocus -= 1;\n      if (rowIndexToFocus >= 0) {\n        // Go to last column of the previous if there's one more row\n        columnIndexToFocus = visibleColumns.length - 1;\n      }\n    }\n    rowIndexToFocus = clamp(rowIndexToFocus, 0, currentPageRows.length - 1);\n    const rowToFocus = currentPageRows[rowIndexToFocus];\n    if (!rowToFocus) {\n      return;\n    }\n    const colSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowToFocus.id, columnIndexToFocus);\n    if (colSpanInfo && colSpanInfo.spannedByColSpan) {\n      if (direction === 'left' || direction === 'below') {\n        columnIndexToFocus = colSpanInfo.leftVisibleCellIndex;\n      } else if (direction === 'right') {\n        columnIndexToFocus = colSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    columnIndexToFocus = clamp(columnIndexToFocus, 0, visibleColumns.length - 1);\n    const columnToFocus = visibleColumns[columnIndexToFocus];\n    apiRef.current.setCellFocus(rowToFocus.id, columnToFocus.field);\n  }, [apiRef, props.pagination, props.paginationMode]);\n  const handleCellDoubleClick = React.useCallback(({\n    id,\n    field\n  }) => {\n    apiRef.current.setCellFocus(id, field);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // GRID_CELL_NAVIGATION_KEY_DOWN handles the focus on Enter, Tab and navigation keys\n    if (event.key === 'Enter' || event.key === 'Tab' || event.key === 'Shift' || isNavigationKey(event.key)) {\n      return;\n    }\n    apiRef.current.setCellFocus(params.id, params.field);\n  }, [apiRef]);\n  const handleColumnHeaderFocus = React.useCallback(({\n    field\n  }, event) => {\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef]);\n  const handleColumnGroupHeaderFocus = React.useCallback(({\n    fields,\n    depth\n  }, event) => {\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup !== null && focusedColumnGroup.depth === depth && fields.includes(focusedColumnGroup.field)) {\n      // This group cell has already been focused\n      return;\n    }\n    apiRef.current.setColumnGroupHeaderFocus(fields[0], depth, event);\n  }, [apiRef]);\n  const handleBlur = React.useCallback((_, event) => {\n    if (event.relatedTarget?.getAttribute('class')?.includes(gridClasses.columnHeader)) {\n      return;\n    }\n    logger.debug(`Clearing focus`);\n    apiRef.current.setState(state => _extends({}, state, {\n      focus: {\n        cell: null,\n        columnHeader: null,\n        columnHeaderFilter: null,\n        columnGroupHeader: null\n      }\n    }));\n  }, [logger, apiRef]);\n  const handleCellMouseDown = React.useCallback(params => {\n    lastClickedCell.current = params;\n  }, []);\n  const handleDocumentClick = React.useCallback(event => {\n    const cellParams = lastClickedCell.current;\n    lastClickedCell.current = null;\n    const focusedCell = gridFocusCellSelector(apiRef);\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    if (!focusedCell) {\n      if (cellParams) {\n        apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n      }\n      return;\n    }\n    if (cellParams?.id === focusedCell.id && cellParams?.field === focusedCell.field) {\n      return;\n    }\n    const cellElement = apiRef.current.getCellElement(focusedCell.id, focusedCell.field);\n    if (cellElement?.contains(event.target)) {\n      return;\n    }\n    if (cellParams) {\n      apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n    } else {\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: null,\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n      apiRef.current.forceUpdate();\n\n      // There's a focused cell but another element (not a cell) was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, event);\n    }\n  }, [apiRef, publishCellFocusOut]);\n  const handleCellModeChange = React.useCallback(params => {\n    if (params.cellMode === 'view') {\n      return;\n    }\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell?.id !== params.id || cell?.field !== params.field) {\n      apiRef.current.setCellFocus(params.id, params.field);\n    }\n  }, [apiRef]);\n  const handleRowSet = React.useCallback(() => {\n    const cell = gridFocusCellSelector(apiRef);\n\n    // If the focused cell is in a row which does not exist anymore, then remove the focus\n    if (cell && !apiRef.current.getRow(cell.id)) {\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: null,\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n    }\n  }, [apiRef]);\n  const handlePaginationModelChange = useEventcallback(() => {\n    const currentFocusedCell = gridFocusCellSelector(apiRef);\n    if (!currentFocusedCell) {\n      return;\n    }\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const rowIsInCurrentPage = currentPage.rows.find(row => row.id === currentFocusedCell.id);\n    if (rowIsInCurrentPage) {\n      return;\n    }\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id: currentPage.rows[0].id,\n            field: visibleColumns[0].field\n          },\n          columnGroupHeader: null,\n          columnHeader: null,\n          columnHeaderFilter: null\n        }\n      });\n    });\n  });\n  const focusApi = {\n    setCellFocus,\n    setColumnHeaderFocus,\n    setColumnHeaderFilterFocus\n  };\n  const focusPrivateApi = {\n    moveFocusToRelativeCell,\n    setColumnGroupHeaderFocus,\n    getColumnGroupHeaderFocus\n  };\n  useGridApiMethod(apiRef, focusApi, 'public');\n  useGridApiMethod(apiRef, focusPrivateApi, 'private');\n  React.useEffect(() => {\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.addEventListener('mouseup', handleDocumentClick);\n    return () => {\n      doc.removeEventListener('mouseup', handleDocumentClick);\n    };\n  }, [apiRef, hasRootReference, handleDocumentClick]);\n  useGridApiEventHandler(apiRef, 'columnHeaderBlur', handleBlur);\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', handleCellDoubleClick);\n  useGridApiEventHandler(apiRef, 'cellMouseDown', handleCellMouseDown);\n  useGridApiEventHandler(apiRef, 'cellKeyDown', handleCellKeyDown);\n  useGridApiEventHandler(apiRef, 'cellModeChange', handleCellModeChange);\n  useGridApiEventHandler(apiRef, 'columnHeaderFocus', handleColumnHeaderFocus);\n  useGridApiEventHandler(apiRef, 'columnGroupHeaderFocus', handleColumnGroupHeaderFocus);\n  useGridApiEventHandler(apiRef, 'rowsSet', handleRowSet);\n  useGridApiEventHandler(apiRef, 'paginationModelChange', handlePaginationModelChange);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AACnH,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,qBAAqB,EAAEC,kCAAkC,QAAQ,6BAA6B;AACvG,SAASC,oCAAoC,QAAQ,mCAAmC;AACxF,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAO,MAAMC,qBAAqB,GAAGC,KAAK,IAAIlB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;EAChEC,KAAK,EAAE;IACLC,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,QAAQ,EAAE;IACRJ,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,YAAY,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC7C,MAAMC,MAAM,GAAGpB,aAAa,CAACkB,MAAM,EAAE,cAAc,CAAC;EACpD,MAAMG,eAAe,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAMC,gBAAgB,GAAGL,MAAM,CAACM,OAAO,CAACC,cAAc,CAACD,OAAO,KAAK,IAAI;EACvE,MAAME,mBAAmB,GAAGjC,KAAK,CAACkC,WAAW,CAAC,CAACf,IAAI,EAAEgB,KAAK,KAAK;IAC7D,IAAIhB,IAAI,EAAE;MACR;MACA,IAAIM,MAAM,CAACM,OAAO,CAACK,MAAM,CAACjB,IAAI,CAACkB,EAAE,CAAC,EAAE;QAClCZ,MAAM,CAACM,OAAO,CAACO,YAAY,CAAC,cAAc,EAAEb,MAAM,CAACM,OAAO,CAACQ,aAAa,CAACpB,IAAI,CAACkB,EAAE,EAAElB,IAAI,CAACqB,KAAK,CAAC,EAAEL,KAAK,CAAC;MACvG;IACF;EACF,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EACZ,MAAMgB,YAAY,GAAGzC,KAAK,CAACkC,WAAW,CAAC,CAACG,EAAE,EAAEG,KAAK,KAAK;IACpD,MAAME,WAAW,GAAGhC,qBAAqB,CAACe,MAAM,CAAC;IACjD,IAAIiB,WAAW,EAAEL,EAAE,KAAKA,EAAE,IAAIK,WAAW,EAAEF,KAAK,KAAKA,KAAK,EAAE;MAC1D;IACF;IACAf,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/BU,MAAM,CAACiB,KAAK,CAAC,4BAA4BP,EAAE,cAAcG,KAAK,EAAE,CAAC;MACjE,OAAOzC,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRJ,IAAI,EAAE;YACJkB,EAAE;YACFG;UACF,CAAC;UACDpB,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBC,iBAAiB,EAAE;QACrB,CAAC;QACDJ,KAAK,EAAE;UACLC,IAAI,EAAE;YACJkB,EAAE;YACFG;UACF,CAAC;UACDpB,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBC,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFG,MAAM,CAACM,OAAO,CAACc,WAAW,CAAC,CAAC;;IAE5B;IACA,IAAI,CAACpB,MAAM,CAACM,OAAO,CAACK,MAAM,CAACC,EAAE,CAAC,EAAE;MAC9B;IACF;IACA,IAAIK,WAAW,EAAE;MACf;MACA;MACAT,mBAAmB,CAACS,WAAW,EAAE,CAAC,CAAC,CAAC;IACtC;IACAjB,MAAM,CAACM,OAAO,CAACO,YAAY,CAAC,aAAa,EAAEb,MAAM,CAACM,OAAO,CAACQ,aAAa,CAACF,EAAE,EAAEG,KAAK,CAAC,CAAC;EACrF,CAAC,EAAE,CAACf,MAAM,EAAEE,MAAM,EAAEM,mBAAmB,CAAC,CAAC;EACzC,MAAMa,oBAAoB,GAAG9C,KAAK,CAACkC,WAAW,CAAC,UAACM,KAAK,EAAiB;IAAA,IAAfL,KAAK,GAAAY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC/D,MAAM5B,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;IAC1CQ,mBAAmB,CAACd,IAAI,EAAEgB,KAAK,CAAC;IAChCV,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/BU,MAAM,CAACiB,KAAK,CAAC,2CAA2CJ,KAAK,EAAE,CAAC;MAChE,OAAOzC,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRH,YAAY,EAAE;YACZoB;UACF,CAAC;UACDnB,kBAAkB,EAAE,IAAI;UACxBF,IAAI,EAAE,IAAI;UACVG,iBAAiB,EAAE;QACrB,CAAC;QACDJ,KAAK,EAAE;UACLE,YAAY,EAAE;YACZoB;UACF,CAAC;UACDnB,kBAAkB,EAAE,IAAI;UACxBF,IAAI,EAAE,IAAI;UACVG,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFG,MAAM,CAACM,OAAO,CAACc,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACpB,MAAM,EAAEE,MAAM,EAAEM,mBAAmB,CAAC,CAAC;EACzC,MAAMiB,0BAA0B,GAAGlD,KAAK,CAACkC,WAAW,CAAC,UAACM,KAAK,EAAiB;IAAA,IAAfL,KAAK,GAAAY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACrE,MAAM5B,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;IAC1CQ,mBAAmB,CAACd,IAAI,EAAEgB,KAAK,CAAC;IAChCV,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/BU,MAAM,CAACiB,KAAK,CAAC,kDAAkDJ,KAAK,EAAE,CAAC;MACvE,OAAOzC,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRH,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE;YAClBmB;UACF,CAAC;UACDrB,IAAI,EAAE,IAAI;UACVG,iBAAiB,EAAE;QACrB,CAAC;QACDJ,KAAK,EAAE;UACLE,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE;YAClBmB;UACF,CAAC;UACDrB,IAAI,EAAE,IAAI;UACVG,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFG,MAAM,CAACM,OAAO,CAACc,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACpB,MAAM,EAAEE,MAAM,EAAEM,mBAAmB,CAAC,CAAC;EACzC,MAAMkB,yBAAyB,GAAGnD,KAAK,CAACkC,WAAW,CAAC,UAACM,KAAK,EAAEY,KAAK,EAAiB;IAAA,IAAfjB,KAAK,GAAAY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC3E,MAAM5B,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;IAC1C,IAAIN,IAAI,EAAE;MACRM,MAAM,CAACM,OAAO,CAACO,YAAY,CAAC,cAAc,EAAEb,MAAM,CAACM,OAAO,CAACQ,aAAa,CAACpB,IAAI,CAACkB,EAAE,EAAElB,IAAI,CAACqB,KAAK,CAAC,EAAEL,KAAK,CAAC;IACvG;IACAV,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/B,OAAOlB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRD,iBAAiB,EAAE;YACjBkB,KAAK;YACLY;UACF,CAAC;UACDhC,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBF,IAAI,EAAE;QACR,CAAC;QACDD,KAAK,EAAE;UACLI,iBAAiB,EAAE;YACjBkB,KAAK;YACLY;UACF,CAAC;UACDhC,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBF,IAAI,EAAE;QACR;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFM,MAAM,CAACM,OAAO,CAACc,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EACZ,MAAM4B,yBAAyB,GAAGrD,KAAK,CAACkC,WAAW,CAAC,MAAMvB,kCAAkC,CAACc,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC/G,MAAM6B,uBAAuB,GAAGtD,KAAK,CAACkC,WAAW,CAAC,CAACG,EAAE,EAAEG,KAAK,EAAEe,SAAS,KAAK;IAC1E,IAAIC,kBAAkB,GAAG/B,MAAM,CAACM,OAAO,CAAC0B,cAAc,CAACjB,KAAK,CAAC;IAC7D,MAAMkB,cAAc,GAAG9C,oCAAoC,CAACa,MAAM,CAAC;IACnE,MAAMkC,WAAW,GAAG9C,cAAc,CAACY,MAAM,EAAE;MACzCmC,UAAU,EAAElC,KAAK,CAACkC,UAAU;MAC5BC,cAAc,EAAEnC,KAAK,CAACmC;IACxB,CAAC,CAAC;IACF,MAAMC,UAAU,GAAG/C,sBAAsB,CAACU,MAAM,CAAC;;IAEjD;IACA,MAAMsC,eAAe,GAAG,EAAE,CAACC,MAAM,CAACF,UAAU,CAACG,GAAG,IAAI,EAAE,EAAEN,WAAW,CAACO,IAAI,EAAEJ,UAAU,CAACK,MAAM,IAAI,EAAE,CAAC;IAClG,IAAIC,eAAe,GAAGL,eAAe,CAACM,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjC,EAAE,KAAKA,EAAE,CAAC;IACrE,IAAIkB,SAAS,KAAK,OAAO,EAAE;MACzBC,kBAAkB,IAAI,CAAC;IACzB,CAAC,MAAM,IAAID,SAAS,KAAK,MAAM,EAAE;MAC/BC,kBAAkB,IAAI,CAAC;IACzB,CAAC,MAAM;MACLY,eAAe,IAAI,CAAC;IACtB;IACA,IAAIZ,kBAAkB,IAAIE,cAAc,CAACV,MAAM,EAAE;MAC/C;MACAoB,eAAe,IAAI,CAAC;MACpB,IAAIA,eAAe,GAAGL,eAAe,CAACf,MAAM,EAAE;QAC5C;QACAQ,kBAAkB,GAAG,CAAC;MACxB;IACF,CAAC,MAAM,IAAIA,kBAAkB,GAAG,CAAC,EAAE;MACjC;MACAY,eAAe,IAAI,CAAC;MACpB,IAAIA,eAAe,IAAI,CAAC,EAAE;QACxB;QACAZ,kBAAkB,GAAGE,cAAc,CAACV,MAAM,GAAG,CAAC;MAChD;IACF;IACAoB,eAAe,GAAGtD,KAAK,CAACsD,eAAe,EAAE,CAAC,EAAEL,eAAe,CAACf,MAAM,GAAG,CAAC,CAAC;IACvE,MAAMuB,UAAU,GAAGR,eAAe,CAACK,eAAe,CAAC;IACnD,IAAI,CAACG,UAAU,EAAE;MACf;IACF;IACA,MAAMC,WAAW,GAAG/C,MAAM,CAACM,OAAO,CAAC0C,2BAA2B,CAACF,UAAU,CAAClC,EAAE,EAAEmB,kBAAkB,CAAC;IACjG,IAAIgB,WAAW,IAAIA,WAAW,CAACE,gBAAgB,EAAE;MAC/C,IAAInB,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;QACjDC,kBAAkB,GAAGgB,WAAW,CAACG,oBAAoB;MACvD,CAAC,MAAM,IAAIpB,SAAS,KAAK,OAAO,EAAE;QAChCC,kBAAkB,GAAGgB,WAAW,CAACI,qBAAqB;MACxD;IACF;IACApB,kBAAkB,GAAG1C,KAAK,CAAC0C,kBAAkB,EAAE,CAAC,EAAEE,cAAc,CAACV,MAAM,GAAG,CAAC,CAAC;IAC5E,MAAM6B,aAAa,GAAGnB,cAAc,CAACF,kBAAkB,CAAC;IACxD/B,MAAM,CAACM,OAAO,CAACU,YAAY,CAAC8B,UAAU,CAAClC,EAAE,EAAEwC,aAAa,CAACrC,KAAK,CAAC;EACjE,CAAC,EAAE,CAACf,MAAM,EAAEC,KAAK,CAACkC,UAAU,EAAElC,KAAK,CAACmC,cAAc,CAAC,CAAC;EACpD,MAAMiB,qBAAqB,GAAG9E,KAAK,CAACkC,WAAW,CAAC6C,IAAA,IAG1C;IAAA,IAH2C;MAC/C1C,EAAE;MACFG;IACF,CAAC,GAAAuC,IAAA;IACCtD,MAAM,CAACM,OAAO,CAACU,YAAY,CAACJ,EAAE,EAAEG,KAAK,CAAC;EACxC,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;EACZ,MAAMuD,iBAAiB,GAAGhF,KAAK,CAACkC,WAAW,CAAC,CAAC+C,MAAM,EAAE9C,KAAK,KAAK;IAC7D;IACA,IAAIA,KAAK,CAAC+C,GAAG,KAAK,OAAO,IAAI/C,KAAK,CAAC+C,GAAG,KAAK,KAAK,IAAI/C,KAAK,CAAC+C,GAAG,KAAK,OAAO,IAAIzE,eAAe,CAAC0B,KAAK,CAAC+C,GAAG,CAAC,EAAE;MACvG;IACF;IACAzD,MAAM,CAACM,OAAO,CAACU,YAAY,CAACwC,MAAM,CAAC5C,EAAE,EAAE4C,MAAM,CAACzC,KAAK,CAAC;EACtD,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;EACZ,MAAM0D,uBAAuB,GAAGnF,KAAK,CAACkC,WAAW,CAAC,CAAAkD,KAAA,EAE/CjD,KAAK,KAAK;IAAA,IAFsC;MACjDK;IACF,CAAC,GAAA4C,KAAA;IACC,IAAIjD,KAAK,CAACkD,MAAM,KAAKlD,KAAK,CAACmD,aAAa,EAAE;MACxC;IACF;IACA7D,MAAM,CAACM,OAAO,CAACe,oBAAoB,CAACN,KAAK,EAAEL,KAAK,CAAC;EACnD,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EACZ,MAAM8D,4BAA4B,GAAGvF,KAAK,CAACkC,WAAW,CAAC,CAAAsD,KAAA,EAGpDrD,KAAK,KAAK;IAAA,IAH2C;MACtDsD,MAAM;MACNrC;IACF,CAAC,GAAAoC,KAAA;IACC,IAAIrD,KAAK,CAACkD,MAAM,KAAKlD,KAAK,CAACmD,aAAa,EAAE;MACxC;IACF;IACA,MAAMI,kBAAkB,GAAG/E,kCAAkC,CAACc,MAAM,CAAC;IACrE,IAAIiE,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,CAACtC,KAAK,KAAKA,KAAK,IAAIqC,MAAM,CAACE,QAAQ,CAACD,kBAAkB,CAAClD,KAAK,CAAC,EAAE;MAClH;MACA;IACF;IACAf,MAAM,CAACM,OAAO,CAACoB,yBAAyB,CAACsC,MAAM,CAAC,CAAC,CAAC,EAAErC,KAAK,EAAEjB,KAAK,CAAC;EACnE,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EACZ,MAAMmE,UAAU,GAAG5F,KAAK,CAACkC,WAAW,CAAC,CAAC2D,CAAC,EAAE1D,KAAK,KAAK;IACjD,IAAIA,KAAK,CAAC2D,aAAa,EAAEC,YAAY,CAAC,OAAO,CAAC,EAAEJ,QAAQ,CAACtF,WAAW,CAACe,YAAY,CAAC,EAAE;MAClF;IACF;IACAO,MAAM,CAACiB,KAAK,CAAC,gBAAgB,CAAC;IAC9BnB,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAIlB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;MACnDC,KAAK,EAAE;QACLC,IAAI,EAAE,IAAI;QACVC,YAAY,EAAE,IAAI;QAClBC,kBAAkB,EAAE,IAAI;QACxBC,iBAAiB,EAAE;MACrB;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACK,MAAM,EAAEF,MAAM,CAAC,CAAC;EACpB,MAAMuE,mBAAmB,GAAGhG,KAAK,CAACkC,WAAW,CAAC+C,MAAM,IAAI;IACtDrD,eAAe,CAACG,OAAO,GAAGkD,MAAM;EAClC,CAAC,EAAE,EAAE,CAAC;EACN,MAAMgB,mBAAmB,GAAGjG,KAAK,CAACkC,WAAW,CAACC,KAAK,IAAI;IACrD,MAAM+D,UAAU,GAAGtE,eAAe,CAACG,OAAO;IAC1CH,eAAe,CAACG,OAAO,GAAG,IAAI;IAC9B,MAAMW,WAAW,GAAGhC,qBAAqB,CAACe,MAAM,CAAC;IACjD,MAAM0E,cAAc,GAAG1E,MAAM,CAACM,OAAO,CAACqE,4BAA4B,CAAC,gBAAgB,EAAE,IAAI,EAAE;MACzFjE,KAAK;MACLhB,IAAI,EAAE+E;IACR,CAAC,CAAC;IACF,IAAI,CAACC,cAAc,EAAE;MACnB;IACF;IACA,IAAI,CAACzD,WAAW,EAAE;MAChB,IAAIwD,UAAU,EAAE;QACdzE,MAAM,CAACM,OAAO,CAACU,YAAY,CAACyD,UAAU,CAAC7D,EAAE,EAAE6D,UAAU,CAAC1D,KAAK,CAAC;MAC9D;MACA;IACF;IACA,IAAI0D,UAAU,EAAE7D,EAAE,KAAKK,WAAW,CAACL,EAAE,IAAI6D,UAAU,EAAE1D,KAAK,KAAKE,WAAW,CAACF,KAAK,EAAE;MAChF;IACF;IACA,MAAM6D,WAAW,GAAG5E,MAAM,CAACM,OAAO,CAACuE,cAAc,CAAC5D,WAAW,CAACL,EAAE,EAAEK,WAAW,CAACF,KAAK,CAAC;IACpF,IAAI6D,WAAW,EAAEE,QAAQ,CAACpE,KAAK,CAACkD,MAAM,CAAC,EAAE;MACvC;IACF;IACA,IAAIa,UAAU,EAAE;MACdzE,MAAM,CAACM,OAAO,CAACU,YAAY,CAACyD,UAAU,CAAC7D,EAAE,EAAE6D,UAAU,CAAC1D,KAAK,CAAC;IAC9D,CAAC,MAAM;MACLf,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAIlB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;QACnDC,KAAK,EAAE;UACLC,IAAI,EAAE,IAAI;UACVC,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBC,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC,CAAC;MACHG,MAAM,CAACM,OAAO,CAACc,WAAW,CAAC,CAAC;;MAE5B;MACA;MACAZ,mBAAmB,CAACS,WAAW,EAAEP,KAAK,CAAC;IACzC;EACF,CAAC,EAAE,CAACV,MAAM,EAAEQ,mBAAmB,CAAC,CAAC;EACjC,MAAMuE,oBAAoB,GAAGxG,KAAK,CAACkC,WAAW,CAAC+C,MAAM,IAAI;IACvD,IAAIA,MAAM,CAACwB,QAAQ,KAAK,MAAM,EAAE;MAC9B;IACF;IACA,MAAMtF,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;IAC1C,IAAIN,IAAI,EAAEkB,EAAE,KAAK4C,MAAM,CAAC5C,EAAE,IAAIlB,IAAI,EAAEqB,KAAK,KAAKyC,MAAM,CAACzC,KAAK,EAAE;MAC1Df,MAAM,CAACM,OAAO,CAACU,YAAY,CAACwC,MAAM,CAAC5C,EAAE,EAAE4C,MAAM,CAACzC,KAAK,CAAC;IACtD;EACF,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;EACZ,MAAMiF,YAAY,GAAG1G,KAAK,CAACkC,WAAW,CAAC,MAAM;IAC3C,MAAMf,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;;IAE1C;IACA,IAAIN,IAAI,IAAI,CAACM,MAAM,CAACM,OAAO,CAACK,MAAM,CAACjB,IAAI,CAACkB,EAAE,CAAC,EAAE;MAC3CZ,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAIlB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;QACnDC,KAAK,EAAE;UACLC,IAAI,EAAE,IAAI;UACVC,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBC,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACG,MAAM,CAAC,CAAC;EACZ,MAAMkF,2BAA2B,GAAGvG,gBAAgB,CAAC,MAAM;IACzD,MAAMwG,kBAAkB,GAAGlG,qBAAqB,CAACe,MAAM,CAAC;IACxD,IAAI,CAACmF,kBAAkB,EAAE;MACvB;IACF;IACA,MAAMjD,WAAW,GAAG9C,cAAc,CAACY,MAAM,EAAE;MACzCmC,UAAU,EAAElC,KAAK,CAACkC,UAAU;MAC5BC,cAAc,EAAEnC,KAAK,CAACmC;IACxB,CAAC,CAAC;IACF,MAAMgD,kBAAkB,GAAGlD,WAAW,CAACO,IAAI,CAAC4C,IAAI,CAACxC,GAAG,IAAIA,GAAG,CAACjC,EAAE,KAAKuE,kBAAkB,CAACvE,EAAE,CAAC;IACzF,IAAIwE,kBAAkB,EAAE;MACtB;IACF;IACA,MAAMnD,cAAc,GAAG9C,oCAAoC,CAACa,MAAM,CAAC;IACnEA,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/B,OAAOlB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRJ,IAAI,EAAE;YACJkB,EAAE,EAAEsB,WAAW,CAACO,IAAI,CAAC,CAAC,CAAC,CAAC7B,EAAE;YAC1BG,KAAK,EAAEkB,cAAc,CAAC,CAAC,CAAC,CAAClB;UAC3B,CAAC;UACDlB,iBAAiB,EAAE,IAAI;UACvBF,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE;QACtB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM0F,QAAQ,GAAG;IACftE,YAAY;IACZK,oBAAoB;IACpBI;EACF,CAAC;EACD,MAAM8D,eAAe,GAAG;IACtB1D,uBAAuB;IACvBH,yBAAyB;IACzBE;EACF,CAAC;EACD/C,gBAAgB,CAACmB,MAAM,EAAEsF,QAAQ,EAAE,QAAQ,CAAC;EAC5CzG,gBAAgB,CAACmB,MAAM,EAAEuF,eAAe,EAAE,SAAS,CAAC;EACpDhH,KAAK,CAACiH,SAAS,CAAC,MAAM;IACpB,MAAMC,GAAG,GAAGhH,aAAa,CAACuB,MAAM,CAACM,OAAO,CAACC,cAAc,CAACD,OAAO,CAAC;IAChEmF,GAAG,CAACC,gBAAgB,CAAC,SAAS,EAAElB,mBAAmB,CAAC;IACpD,OAAO,MAAM;MACXiB,GAAG,CAACE,mBAAmB,CAAC,SAAS,EAAEnB,mBAAmB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,CAACxE,MAAM,EAAEK,gBAAgB,EAAEmE,mBAAmB,CAAC,CAAC;EACnDzF,sBAAsB,CAACiB,MAAM,EAAE,kBAAkB,EAAEmE,UAAU,CAAC;EAC9DpF,sBAAsB,CAACiB,MAAM,EAAE,iBAAiB,EAAEqD,qBAAqB,CAAC;EACxEtE,sBAAsB,CAACiB,MAAM,EAAE,eAAe,EAAEuE,mBAAmB,CAAC;EACpExF,sBAAsB,CAACiB,MAAM,EAAE,aAAa,EAAEuD,iBAAiB,CAAC;EAChExE,sBAAsB,CAACiB,MAAM,EAAE,gBAAgB,EAAE+E,oBAAoB,CAAC;EACtEhG,sBAAsB,CAACiB,MAAM,EAAE,mBAAmB,EAAE0D,uBAAuB,CAAC;EAC5E3E,sBAAsB,CAACiB,MAAM,EAAE,wBAAwB,EAAE8D,4BAA4B,CAAC;EACtF/E,sBAAsB,CAACiB,MAAM,EAAE,SAAS,EAAEiF,YAAY,CAAC;EACvDlG,sBAAsB,CAACiB,MAAM,EAAE,uBAAuB,EAAEkF,2BAA2B,CAAC;AACtF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}