{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { GridScrollArea } from \"../GridScrollArea.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { gridDimensionsSelector } from \"../../hooks/features/dimensions/index.js\";\nimport { useGridVirtualScroller } from \"../../hooks/features/virtualization/useGridVirtualScroller.js\";\nimport { useGridOverlays } from \"../../hooks/features/overlays/useGridOverlays.js\";\nimport { GridOverlays as Overlays } from \"../base/GridOverlays.js\";\nimport { GridHeaders } from \"../GridHeaders.js\";\nimport { GridMainContainer as Container } from \"./GridMainContainer.js\";\nimport { GridTopContainer as TopContainer } from \"./GridTopContainer.js\";\nimport { GridBottomContainer as BottomContainer } from \"./GridBottomContainer.js\";\nimport { GridVirtualScrollerContent as Content } from \"./GridVirtualScrollerContent.js\";\nimport { GridVirtualScrollerFiller as SpaceFiller } from \"./GridVirtualScrollerFiller.js\";\nimport { GridVirtualScrollerRenderZone as RenderZone } from \"./GridVirtualScrollerRenderZone.js\";\nimport { GridVirtualScrollbar as Scrollbar } from \"./GridVirtualScrollbar.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = (ownerState, dimensions, loadingOverlayVariant) => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['main', dimensions.rightPinnedWidth > 0 && 'main--hasPinnedRight', loadingOverlayVariant === 'skeleton' && 'main--hasSkeletonLoadingOverlay'],\n    scroller: ['virtualScroller', dimensions.hasScrollX && 'virtualScroller--hasScrollX']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst Scroller = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScroller',\n  overridesResolver: (props, styles) => styles.virtualScroller\n})({\n  position: 'relative',\n  height: '100%',\n  flexGrow: 1,\n  overflow: 'scroll',\n  scrollbarWidth: 'none' /* Firefox */,\n  '&::-webkit-scrollbar': {\n    display: 'none' /* Safari and Chrome */\n  },\n  '@media print': {\n    overflow: 'hidden'\n  },\n  // See https://github.com/mui/mui-x/issues/10547\n  zIndex: 0\n});\nfunction GridVirtualScroller(props) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  const overlaysProps = useGridOverlays();\n  const classes = useUtilityClasses(rootProps, dimensions, overlaysProps.loadingOverlayVariant);\n  const virtualScroller = useGridVirtualScroller();\n  const {\n    getContainerProps,\n    getScrollerProps,\n    getContentProps,\n    getRenderZoneProps,\n    getScrollbarVerticalProps,\n    getScrollbarHorizontalProps,\n    getRows\n  } = virtualScroller;\n  const rows = getRows();\n  return /*#__PURE__*/_jsxs(Container, _extends({\n    className: classes.root\n  }, getContainerProps(), {\n    children: [/*#__PURE__*/_jsx(GridScrollArea, {\n      scrollDirection: \"left\"\n    }), /*#__PURE__*/_jsx(GridScrollArea, {\n      scrollDirection: \"right\"\n    }), /*#__PURE__*/_jsxs(Scroller, _extends({\n      className: classes.scroller\n    }, getScrollerProps(), {\n      ownerState: rootProps,\n      children: [/*#__PURE__*/_jsxs(TopContainer, {\n        children: [/*#__PURE__*/_jsx(GridHeaders, {}), /*#__PURE__*/_jsx(rootProps.slots.pinnedRows, {\n          position: \"top\",\n          virtualScroller: virtualScroller\n        })]\n      }), /*#__PURE__*/_jsx(Overlays, _extends({}, overlaysProps)), /*#__PURE__*/_jsx(Content, _extends({}, getContentProps(), {\n        children: /*#__PURE__*/_jsxs(RenderZone, _extends({}, getRenderZoneProps(), {\n          children: [rows, /*#__PURE__*/_jsx(rootProps.slots.detailPanels, {\n            virtualScroller: virtualScroller\n          })]\n        }))\n      })), /*#__PURE__*/_jsx(SpaceFiller, {\n        rowsLength: rows.length\n      }), /*#__PURE__*/_jsx(BottomContainer, {\n        children: /*#__PURE__*/_jsx(rootProps.slots.pinnedRows, {\n          position: \"bottom\",\n          virtualScroller: virtualScroller\n        })\n      })]\n    })), dimensions.hasScrollY && /*#__PURE__*/_jsx(Scrollbar, _extends({\n      position: \"vertical\"\n    }, getScrollbarVerticalProps())), dimensions.hasScrollX && /*#__PURE__*/_jsx(Scrollbar, _extends({\n      position: \"horizontal\"\n    }, getScrollbarHorizontalProps())), props.children]\n  }));\n}\nexport { GridVirtualScroller };", "map": {"version": 3, "names": ["_extends", "React", "styled", "composeClasses", "GridScrollArea", "useGridRootProps", "useGridApiContext", "useGridSelector", "getDataGridUtilityClass", "gridDimensionsSelector", "useGridVirtualScroller", "useGridOverlays", "GridOverlays", "Overlays", "GridHeaders", "Grid<PERSON>ain<PERSON><PERSON><PERSON>", "Container", "GridTopContainer", "TopContainer", "GridBottomContainer", "BottomContainer", "GridVirtualScrollerContent", "Content", "GridVirtualScrollerFiller", "SpaceFiller", "GridVirtualScrollerRenderZone", "RenderZone", "GridVirtualScrollbar", "Sc<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "dimensions", "loadingOverlayVariant", "classes", "slots", "root", "right<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scroller", "hasScrollX", "<PERSON><PERSON><PERSON>", "name", "slot", "overridesResolver", "props", "styles", "virtualScroller", "position", "height", "flexGrow", "overflow", "scrollbarWidth", "display", "zIndex", "GridVirtualScroller", "apiRef", "rootProps", "overlaysProps", "getContainerProps", "getScrollerProps", "getContentProps", "getRenderZoneProps", "getScrollbarVerticalProps", "getScrollbarHorizontalProps", "getRows", "rows", "className", "children", "scrollDirection", "pinnedRows", "detailPanels", "rows<PERSON><PERSON><PERSON>", "length", "hasScrollY"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/virtualization/GridVirtualScroller.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { GridScrollArea } from \"../GridScrollArea.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { gridDimensionsSelector } from \"../../hooks/features/dimensions/index.js\";\nimport { useGridVirtualScroller } from \"../../hooks/features/virtualization/useGridVirtualScroller.js\";\nimport { useGridOverlays } from \"../../hooks/features/overlays/useGridOverlays.js\";\nimport { GridOverlays as Overlays } from \"../base/GridOverlays.js\";\nimport { GridHeaders } from \"../GridHeaders.js\";\nimport { GridMainContainer as Container } from \"./GridMainContainer.js\";\nimport { GridTopContainer as TopContainer } from \"./GridTopContainer.js\";\nimport { GridBottomContainer as BottomContainer } from \"./GridBottomContainer.js\";\nimport { GridVirtualScrollerContent as Content } from \"./GridVirtualScrollerContent.js\";\nimport { GridVirtualScrollerFiller as SpaceFiller } from \"./GridVirtualScrollerFiller.js\";\nimport { GridVirtualScrollerRenderZone as RenderZone } from \"./GridVirtualScrollerRenderZone.js\";\nimport { GridVirtualScrollbar as Scrollbar } from \"./GridVirtualScrollbar.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = (ownerState, dimensions, loadingOverlayVariant) => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['main', dimensions.rightPinnedWidth > 0 && 'main--hasPinnedRight', loadingOverlayVariant === 'skeleton' && 'main--hasSkeletonLoadingOverlay'],\n    scroller: ['virtualScroller', dimensions.hasScrollX && 'virtualScroller--hasScrollX']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst Scroller = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScroller',\n  overridesResolver: (props, styles) => styles.virtualScroller\n})({\n  position: 'relative',\n  height: '100%',\n  flexGrow: 1,\n  overflow: 'scroll',\n  scrollbarWidth: 'none' /* Firefox */,\n  '&::-webkit-scrollbar': {\n    display: 'none' /* Safari and Chrome */\n  },\n  '@media print': {\n    overflow: 'hidden'\n  },\n  // See https://github.com/mui/mui-x/issues/10547\n  zIndex: 0\n});\nfunction GridVirtualScroller(props) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  const overlaysProps = useGridOverlays();\n  const classes = useUtilityClasses(rootProps, dimensions, overlaysProps.loadingOverlayVariant);\n  const virtualScroller = useGridVirtualScroller();\n  const {\n    getContainerProps,\n    getScrollerProps,\n    getContentProps,\n    getRenderZoneProps,\n    getScrollbarVerticalProps,\n    getScrollbarHorizontalProps,\n    getRows\n  } = virtualScroller;\n  const rows = getRows();\n  return /*#__PURE__*/_jsxs(Container, _extends({\n    className: classes.root\n  }, getContainerProps(), {\n    children: [/*#__PURE__*/_jsx(GridScrollArea, {\n      scrollDirection: \"left\"\n    }), /*#__PURE__*/_jsx(GridScrollArea, {\n      scrollDirection: \"right\"\n    }), /*#__PURE__*/_jsxs(Scroller, _extends({\n      className: classes.scroller\n    }, getScrollerProps(), {\n      ownerState: rootProps,\n      children: [/*#__PURE__*/_jsxs(TopContainer, {\n        children: [/*#__PURE__*/_jsx(GridHeaders, {}), /*#__PURE__*/_jsx(rootProps.slots.pinnedRows, {\n          position: \"top\",\n          virtualScroller: virtualScroller\n        })]\n      }), /*#__PURE__*/_jsx(Overlays, _extends({}, overlaysProps)), /*#__PURE__*/_jsx(Content, _extends({}, getContentProps(), {\n        children: /*#__PURE__*/_jsxs(RenderZone, _extends({}, getRenderZoneProps(), {\n          children: [rows, /*#__PURE__*/_jsx(rootProps.slots.detailPanels, {\n            virtualScroller: virtualScroller\n          })]\n        }))\n      })), /*#__PURE__*/_jsx(SpaceFiller, {\n        rowsLength: rows.length\n      }), /*#__PURE__*/_jsx(BottomContainer, {\n        children: /*#__PURE__*/_jsx(rootProps.slots.pinnedRows, {\n          position: \"bottom\",\n          virtualScroller: virtualScroller\n        })\n      })]\n    })), dimensions.hasScrollY && /*#__PURE__*/_jsx(Scrollbar, _extends({\n      position: \"vertical\"\n    }, getScrollbarVerticalProps())), dimensions.hasScrollX && /*#__PURE__*/_jsx(Scrollbar, _extends({\n      position: \"horizontal\"\n    }, getScrollbarHorizontalProps())), props.children]\n  }));\n}\nexport { GridVirtualScroller };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,sBAAsB,QAAQ,0CAA0C;AACjF,SAASC,sBAAsB,QAAQ,+DAA+D;AACtG,SAASC,eAAe,QAAQ,kDAAkD;AAClF,SAASC,YAAY,IAAIC,QAAQ,QAAQ,yBAAyB;AAClE,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,iBAAiB,IAAIC,SAAS,QAAQ,wBAAwB;AACvE,SAASC,gBAAgB,IAAIC,YAAY,QAAQ,uBAAuB;AACxE,SAASC,mBAAmB,IAAIC,eAAe,QAAQ,0BAA0B;AACjF,SAASC,0BAA0B,IAAIC,OAAO,QAAQ,iCAAiC;AACvF,SAASC,yBAAyB,IAAIC,WAAW,QAAQ,gCAAgC;AACzF,SAASC,6BAA6B,IAAIC,UAAU,QAAQ,oCAAoC;AAChG,SAASC,oBAAoB,IAAIC,SAAS,QAAQ,2BAA2B;AAC7E,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGA,CAACC,UAAU,EAAEC,UAAU,EAAEC,qBAAqB,KAAK;EAC3E,MAAM;IACJC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEJ,UAAU,CAACK,gBAAgB,GAAG,CAAC,IAAI,sBAAsB,EAAEJ,qBAAqB,KAAK,UAAU,IAAI,iCAAiC,CAAC;IACpJK,QAAQ,EAAE,CAAC,iBAAiB,EAAEN,UAAU,CAACO,UAAU,IAAI,6BAA6B;EACtF,CAAC;EACD,OAAOvC,cAAc,CAACmC,KAAK,EAAE9B,uBAAuB,EAAE6B,OAAO,CAAC;AAChE,CAAC;AACD,MAAMM,QAAQ,GAAGzC,MAAM,CAAC,KAAK,EAAE;EAC7B0C,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,iBAAiB;EACvBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;AAC/C,CAAC,CAAC,CAAC;EACDC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,MAAM;EACdC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,QAAQ;EAClBC,cAAc,EAAE,MAAM,CAAC;EACvB,sBAAsB,EAAE;IACtBC,OAAO,EAAE,MAAM,CAAC;EAClB,CAAC;EACD,cAAc,EAAE;IACdF,QAAQ,EAAE;EACZ,CAAC;EACD;EACAG,MAAM,EAAE;AACV,CAAC,CAAC;AACF,SAASC,mBAAmBA,CAACV,KAAK,EAAE;EAClC,MAAMW,MAAM,GAAGpD,iBAAiB,CAAC,CAAC;EAClC,MAAMqD,SAAS,GAAGtD,gBAAgB,CAAC,CAAC;EACpC,MAAM8B,UAAU,GAAG5B,eAAe,CAACmD,MAAM,EAAEjD,sBAAsB,CAAC;EAClE,MAAMmD,aAAa,GAAGjD,eAAe,CAAC,CAAC;EACvC,MAAM0B,OAAO,GAAGJ,iBAAiB,CAAC0B,SAAS,EAAExB,UAAU,EAAEyB,aAAa,CAACxB,qBAAqB,CAAC;EAC7F,MAAMa,eAAe,GAAGvC,sBAAsB,CAAC,CAAC;EAChD,MAAM;IACJmD,iBAAiB;IACjBC,gBAAgB;IAChBC,eAAe;IACfC,kBAAkB;IAClBC,yBAAyB;IACzBC,2BAA2B;IAC3BC;EACF,CAAC,GAAGlB,eAAe;EACnB,MAAMmB,IAAI,GAAGD,OAAO,CAAC,CAAC;EACtB,OAAO,aAAanC,KAAK,CAAChB,SAAS,EAAEhB,QAAQ,CAAC;IAC5CqE,SAAS,EAAEhC,OAAO,CAACE;EACrB,CAAC,EAAEsB,iBAAiB,CAAC,CAAC,EAAE;IACtBS,QAAQ,EAAE,CAAC,aAAaxC,IAAI,CAAC1B,cAAc,EAAE;MAC3CmE,eAAe,EAAE;IACnB,CAAC,CAAC,EAAE,aAAazC,IAAI,CAAC1B,cAAc,EAAE;MACpCmE,eAAe,EAAE;IACnB,CAAC,CAAC,EAAE,aAAavC,KAAK,CAACW,QAAQ,EAAE3C,QAAQ,CAAC;MACxCqE,SAAS,EAAEhC,OAAO,CAACI;IACrB,CAAC,EAAEqB,gBAAgB,CAAC,CAAC,EAAE;MACrB5B,UAAU,EAAEyB,SAAS;MACrBW,QAAQ,EAAE,CAAC,aAAatC,KAAK,CAACd,YAAY,EAAE;QAC1CoD,QAAQ,EAAE,CAAC,aAAaxC,IAAI,CAAChB,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,aAAagB,IAAI,CAAC6B,SAAS,CAACrB,KAAK,CAACkC,UAAU,EAAE;UAC3FtB,QAAQ,EAAE,KAAK;UACfD,eAAe,EAAEA;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC,EAAE,aAAanB,IAAI,CAACjB,QAAQ,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAE4D,aAAa,CAAC,CAAC,EAAE,aAAa9B,IAAI,CAACR,OAAO,EAAEtB,QAAQ,CAAC,CAAC,CAAC,EAAE+D,eAAe,CAAC,CAAC,EAAE;QACvHO,QAAQ,EAAE,aAAatC,KAAK,CAACN,UAAU,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAEgE,kBAAkB,CAAC,CAAC,EAAE;UAC1EM,QAAQ,EAAE,CAACF,IAAI,EAAE,aAAatC,IAAI,CAAC6B,SAAS,CAACrB,KAAK,CAACmC,YAAY,EAAE;YAC/DxB,eAAe,EAAEA;UACnB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,EAAE,aAAanB,IAAI,CAACN,WAAW,EAAE;QAClCkD,UAAU,EAAEN,IAAI,CAACO;MACnB,CAAC,CAAC,EAAE,aAAa7C,IAAI,CAACV,eAAe,EAAE;QACrCkD,QAAQ,EAAE,aAAaxC,IAAI,CAAC6B,SAAS,CAACrB,KAAK,CAACkC,UAAU,EAAE;UACtDtB,QAAQ,EAAE,QAAQ;UAClBD,eAAe,EAAEA;QACnB,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,EAAEd,UAAU,CAACyC,UAAU,IAAI,aAAa9C,IAAI,CAACF,SAAS,EAAE5B,QAAQ,CAAC;MAClEkD,QAAQ,EAAE;IACZ,CAAC,EAAEe,yBAAyB,CAAC,CAAC,CAAC,CAAC,EAAE9B,UAAU,CAACO,UAAU,IAAI,aAAaZ,IAAI,CAACF,SAAS,EAAE5B,QAAQ,CAAC;MAC/FkD,QAAQ,EAAE;IACZ,CAAC,EAAEgB,2BAA2B,CAAC,CAAC,CAAC,CAAC,EAAEnB,KAAK,CAACuB,QAAQ;EACpD,CAAC,CAAC,CAAC;AACL;AACA,SAASb,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}