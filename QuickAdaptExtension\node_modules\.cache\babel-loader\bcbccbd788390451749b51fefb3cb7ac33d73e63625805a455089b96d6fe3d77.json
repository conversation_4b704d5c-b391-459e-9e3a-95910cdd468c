{"ast": null, "code": "import * as React from 'react';\nimport { styled } from '@mui/system';\nimport { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { useOnMount } from \"../../hooks/utils/useOnMount.js\";\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { gridDimensionsSelector, useGridSelector } from \"../../hooks/index.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = (ownerState, position) => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['scrollbar', `scrollbar--${position}`],\n    content: ['scrollbarContent']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst Scrollbar = styled('div')({\n  position: 'absolute',\n  display: 'inline-block',\n  zIndex: 6,\n  // In macOS Safari and Gnome Web, scrollbars are overlaid and don't affect the layout. So we consider\n  // their size to be 0px throughout all the calculations, but the floating scrollbar container does need\n  // to appear and have a real size. We set it to 14px because it seems like an acceptable value and we\n  // don't have a method to find the required size for scrollbars on those platforms.\n  '--size': 'calc(max(var(--DataGrid-scrollbarSize), 14px))'\n});\nconst ScrollbarVertical = styled(Scrollbar)({\n  width: 'var(--size)',\n  height: 'calc(var(--DataGrid-hasScrollY) * (100% - var(--DataGrid-topContainerHeight) - var(--DataGrid-bottomContainerHeight) - var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize)))',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // Disable focus-visible style, it's a scrollbar.\n  outline: 0,\n  '& > div': {\n    width: 'var(--size)'\n  },\n  top: 'var(--DataGrid-topContainerHeight)',\n  right: '0px'\n});\nconst ScrollbarHorizontal = styled(Scrollbar)({\n  width: '100%',\n  height: 'var(--size)',\n  overflowY: 'hidden',\n  overflowX: 'auto',\n  // Disable focus-visible style, it's a scrollbar.\n  outline: 0,\n  '& > div': {\n    height: 'var(--size)'\n  },\n  bottom: '0px'\n});\nconst GridVirtualScrollbar = /*#__PURE__*/React.forwardRef(function GridVirtualScrollbar(props, ref) {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const isLocked = React.useRef(false);\n  const lastPosition = React.useRef(0);\n  const scrollbarRef = React.useRef(null);\n  const contentRef = React.useRef(null);\n  const classes = useUtilityClasses(rootProps, props.position);\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  const propertyDimension = props.position === 'vertical' ? 'height' : 'width';\n  const propertyScroll = props.position === 'vertical' ? 'scrollTop' : 'scrollLeft';\n  const hasScroll = props.position === 'vertical' ? dimensions.hasScrollX : dimensions.hasScrollY;\n  const contentSize = dimensions.minimumSize[propertyDimension] + (hasScroll ? dimensions.scrollbarSize : 0);\n  const scrollbarSize = props.position === 'vertical' ? dimensions.viewportInnerSize.height : dimensions.viewportOuterSize.width;\n  const scrollbarInnerSize = scrollbarSize * (contentSize / dimensions.viewportOuterSize[propertyDimension]);\n  const onScrollerScroll = useEventCallback(() => {\n    const scroller = apiRef.current.virtualScrollerRef.current;\n    const scrollbar = scrollbarRef.current;\n    if (scroller[propertyScroll] === lastPosition.current) {\n      return;\n    }\n    if (isLocked.current) {\n      isLocked.current = false;\n      return;\n    }\n    isLocked.current = true;\n    const value = scroller[propertyScroll] / contentSize;\n    scrollbar[propertyScroll] = value * scrollbarInnerSize;\n    lastPosition.current = scroller[propertyScroll];\n  });\n  const onScrollbarScroll = useEventCallback(() => {\n    const scroller = apiRef.current.virtualScrollerRef.current;\n    const scrollbar = scrollbarRef.current;\n    if (isLocked.current) {\n      isLocked.current = false;\n      return;\n    }\n    isLocked.current = true;\n    const value = scrollbar[propertyScroll] / scrollbarInnerSize;\n    scroller[propertyScroll] = value * contentSize;\n  });\n  useOnMount(() => {\n    const scroller = apiRef.current.virtualScrollerRef.current;\n    const scrollbar = scrollbarRef.current;\n    scroller.addEventListener('scroll', onScrollerScroll, {\n      capture: true\n    });\n    scrollbar.addEventListener('scroll', onScrollbarScroll, {\n      capture: true\n    });\n    return () => {\n      scroller.removeEventListener('scroll', onScrollerScroll, {\n        capture: true\n      });\n      scrollbar.removeEventListener('scroll', onScrollbarScroll, {\n        capture: true\n      });\n    };\n  });\n  React.useEffect(() => {\n    const content = contentRef.current;\n    content.style.setProperty(propertyDimension, `${scrollbarInnerSize}px`);\n  }, [scrollbarInnerSize, propertyDimension]);\n  const Container = props.position === 'vertical' ? ScrollbarVertical : ScrollbarHorizontal;\n  return /*#__PURE__*/_jsx(Container, {\n    ref: useForkRef(ref, scrollbarRef),\n    className: classes.root,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    children: /*#__PURE__*/_jsx(\"div\", {\n      ref: contentRef,\n      className: classes.content\n    })\n  });\n});\nexport { GridVirtualScrollbar };", "map": {"version": 3, "names": ["React", "styled", "unstable_composeClasses", "composeClasses", "unstable_useForkRef", "useForkRef", "unstable_useEventCallback", "useEventCallback", "useOnMount", "useGridPrivateApiContext", "gridDimensionsSelector", "useGridSelector", "useGridRootProps", "getDataGridUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "position", "classes", "slots", "root", "content", "Sc<PERSON><PERSON>", "display", "zIndex", "ScrollbarVertical", "width", "height", "overflowY", "overflowX", "outline", "top", "right", "ScrollbarHorizontal", "bottom", "GridVirtualScrollbar", "forwardRef", "props", "ref", "apiRef", "rootProps", "isLocked", "useRef", "lastPosition", "scrollbarRef", "contentRef", "dimensions", "propertyDimension", "propertyScroll", "hasScroll", "hasScrollX", "hasScrollY", "contentSize", "minimumSize", "scrollbarSize", "viewportInnerSize", "viewportOuterSize", "scrollbarInnerSize", "onScrollerScroll", "scroller", "current", "virtualScrollerRef", "scrollbar", "value", "onScrollbarScroll", "addEventListener", "capture", "removeEventListener", "useEffect", "style", "setProperty", "Container", "className", "tabIndex", "children"], "sources": ["E:/Code/QADPT Bugs DEV/quickadapt/QuickAdaptExtension/node_modules/@mui/x-data-grid/components/virtualization/GridVirtualScrollbar.js"], "sourcesContent": ["import * as React from 'react';\nimport { styled } from '@mui/system';\nimport { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { useOnMount } from \"../../hooks/utils/useOnMount.js\";\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { gridDimensionsSelector, useGridSelector } from \"../../hooks/index.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = (ownerState, position) => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['scrollbar', `scrollbar--${position}`],\n    content: ['scrollbarContent']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst Scrollbar = styled('div')({\n  position: 'absolute',\n  display: 'inline-block',\n  zIndex: 6,\n  // In macOS Safari and Gnome Web, scrollbars are overlaid and don't affect the layout. So we consider\n  // their size to be 0px throughout all the calculations, but the floating scrollbar container does need\n  // to appear and have a real size. We set it to 14px because it seems like an acceptable value and we\n  // don't have a method to find the required size for scrollbars on those platforms.\n  '--size': 'calc(max(var(--DataGrid-scrollbarSize), 14px))'\n});\nconst ScrollbarVertical = styled(Scrollbar)({\n  width: 'var(--size)',\n  height: 'calc(var(--DataGrid-hasScrollY) * (100% - var(--DataGrid-topContainerHeight) - var(--DataGrid-bottomContainerHeight) - var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize)))',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // Disable focus-visible style, it's a scrollbar.\n  outline: 0,\n  '& > div': {\n    width: 'var(--size)'\n  },\n  top: 'var(--DataGrid-topContainerHeight)',\n  right: '0px'\n});\nconst ScrollbarHorizontal = styled(Scrollbar)({\n  width: '100%',\n  height: 'var(--size)',\n  overflowY: 'hidden',\n  overflowX: 'auto',\n  // Disable focus-visible style, it's a scrollbar.\n  outline: 0,\n  '& > div': {\n    height: 'var(--size)'\n  },\n  bottom: '0px'\n});\nconst GridVirtualScrollbar = /*#__PURE__*/React.forwardRef(function GridVirtualScrollbar(props, ref) {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const isLocked = React.useRef(false);\n  const lastPosition = React.useRef(0);\n  const scrollbarRef = React.useRef(null);\n  const contentRef = React.useRef(null);\n  const classes = useUtilityClasses(rootProps, props.position);\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  const propertyDimension = props.position === 'vertical' ? 'height' : 'width';\n  const propertyScroll = props.position === 'vertical' ? 'scrollTop' : 'scrollLeft';\n  const hasScroll = props.position === 'vertical' ? dimensions.hasScrollX : dimensions.hasScrollY;\n  const contentSize = dimensions.minimumSize[propertyDimension] + (hasScroll ? dimensions.scrollbarSize : 0);\n  const scrollbarSize = props.position === 'vertical' ? dimensions.viewportInnerSize.height : dimensions.viewportOuterSize.width;\n  const scrollbarInnerSize = scrollbarSize * (contentSize / dimensions.viewportOuterSize[propertyDimension]);\n  const onScrollerScroll = useEventCallback(() => {\n    const scroller = apiRef.current.virtualScrollerRef.current;\n    const scrollbar = scrollbarRef.current;\n    if (scroller[propertyScroll] === lastPosition.current) {\n      return;\n    }\n    if (isLocked.current) {\n      isLocked.current = false;\n      return;\n    }\n    isLocked.current = true;\n    const value = scroller[propertyScroll] / contentSize;\n    scrollbar[propertyScroll] = value * scrollbarInnerSize;\n    lastPosition.current = scroller[propertyScroll];\n  });\n  const onScrollbarScroll = useEventCallback(() => {\n    const scroller = apiRef.current.virtualScrollerRef.current;\n    const scrollbar = scrollbarRef.current;\n    if (isLocked.current) {\n      isLocked.current = false;\n      return;\n    }\n    isLocked.current = true;\n    const value = scrollbar[propertyScroll] / scrollbarInnerSize;\n    scroller[propertyScroll] = value * contentSize;\n  });\n  useOnMount(() => {\n    const scroller = apiRef.current.virtualScrollerRef.current;\n    const scrollbar = scrollbarRef.current;\n    scroller.addEventListener('scroll', onScrollerScroll, {\n      capture: true\n    });\n    scrollbar.addEventListener('scroll', onScrollbarScroll, {\n      capture: true\n    });\n    return () => {\n      scroller.removeEventListener('scroll', onScrollerScroll, {\n        capture: true\n      });\n      scrollbar.removeEventListener('scroll', onScrollbarScroll, {\n        capture: true\n      });\n    };\n  });\n  React.useEffect(() => {\n    const content = contentRef.current;\n    content.style.setProperty(propertyDimension, `${scrollbarInnerSize}px`);\n  }, [scrollbarInnerSize, propertyDimension]);\n  const Container = props.position === 'vertical' ? ScrollbarVertical : ScrollbarHorizontal;\n  return /*#__PURE__*/_jsx(Container, {\n    ref: useForkRef(ref, scrollbarRef),\n    className: classes.root,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    children: /*#__PURE__*/_jsx(\"div\", {\n      ref: contentRef,\n      className: classes.content\n    })\n  });\n});\nexport { GridVirtualScrollbar };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,uBAAuB,IAAIC,cAAc,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AACxJ,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,EAAEC,eAAe,QAAQ,sBAAsB;AAC9E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAACC,UAAU,EAAEC,QAAQ,KAAK;EAClD,MAAM;IACJC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,WAAW,EAAE,cAAcH,QAAQ,EAAE,CAAC;IAC7CI,OAAO,EAAE,CAAC,kBAAkB;EAC9B,CAAC;EACD,OAAOnB,cAAc,CAACiB,KAAK,EAAEP,uBAAuB,EAAEM,OAAO,CAAC;AAChE,CAAC;AACD,MAAMI,SAAS,GAAGtB,MAAM,CAAC,KAAK,CAAC,CAAC;EAC9BiB,QAAQ,EAAE,UAAU;EACpBM,OAAO,EAAE,cAAc;EACvBC,MAAM,EAAE,CAAC;EACT;EACA;EACA;EACA;EACA,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMC,iBAAiB,GAAGzB,MAAM,CAACsB,SAAS,CAAC,CAAC;EAC1CI,KAAK,EAAE,aAAa;EACpBC,MAAM,EAAE,qLAAqL;EAC7LC,SAAS,EAAE,MAAM;EACjBC,SAAS,EAAE,QAAQ;EACnB;EACAC,OAAO,EAAE,CAAC;EACV,SAAS,EAAE;IACTJ,KAAK,EAAE;EACT,CAAC;EACDK,GAAG,EAAE,oCAAoC;EACzCC,KAAK,EAAE;AACT,CAAC,CAAC;AACF,MAAMC,mBAAmB,GAAGjC,MAAM,CAACsB,SAAS,CAAC,CAAC;EAC5CI,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,aAAa;EACrBC,SAAS,EAAE,QAAQ;EACnBC,SAAS,EAAE,MAAM;EACjB;EACAC,OAAO,EAAE,CAAC;EACV,SAAS,EAAE;IACTH,MAAM,EAAE;EACV,CAAC;EACDO,MAAM,EAAE;AACV,CAAC,CAAC;AACF,MAAMC,oBAAoB,GAAG,aAAapC,KAAK,CAACqC,UAAU,CAAC,SAASD,oBAAoBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACnG,MAAMC,MAAM,GAAG/B,wBAAwB,CAAC,CAAC;EACzC,MAAMgC,SAAS,GAAG7B,gBAAgB,CAAC,CAAC;EACpC,MAAM8B,QAAQ,GAAG1C,KAAK,CAAC2C,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMC,YAAY,GAAG5C,KAAK,CAAC2C,MAAM,CAAC,CAAC,CAAC;EACpC,MAAME,YAAY,GAAG7C,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMG,UAAU,GAAG9C,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMxB,OAAO,GAAGH,iBAAiB,CAACyB,SAAS,EAAEH,KAAK,CAACpB,QAAQ,CAAC;EAC5D,MAAM6B,UAAU,GAAGpC,eAAe,CAAC6B,MAAM,EAAE9B,sBAAsB,CAAC;EAClE,MAAMsC,iBAAiB,GAAGV,KAAK,CAACpB,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,OAAO;EAC5E,MAAM+B,cAAc,GAAGX,KAAK,CAACpB,QAAQ,KAAK,UAAU,GAAG,WAAW,GAAG,YAAY;EACjF,MAAMgC,SAAS,GAAGZ,KAAK,CAACpB,QAAQ,KAAK,UAAU,GAAG6B,UAAU,CAACI,UAAU,GAAGJ,UAAU,CAACK,UAAU;EAC/F,MAAMC,WAAW,GAAGN,UAAU,CAACO,WAAW,CAACN,iBAAiB,CAAC,IAAIE,SAAS,GAAGH,UAAU,CAACQ,aAAa,GAAG,CAAC,CAAC;EAC1G,MAAMA,aAAa,GAAGjB,KAAK,CAACpB,QAAQ,KAAK,UAAU,GAAG6B,UAAU,CAACS,iBAAiB,CAAC5B,MAAM,GAAGmB,UAAU,CAACU,iBAAiB,CAAC9B,KAAK;EAC9H,MAAM+B,kBAAkB,GAAGH,aAAa,IAAIF,WAAW,GAAGN,UAAU,CAACU,iBAAiB,CAACT,iBAAiB,CAAC,CAAC;EAC1G,MAAMW,gBAAgB,GAAGpD,gBAAgB,CAAC,MAAM;IAC9C,MAAMqD,QAAQ,GAAGpB,MAAM,CAACqB,OAAO,CAACC,kBAAkB,CAACD,OAAO;IAC1D,MAAME,SAAS,GAAGlB,YAAY,CAACgB,OAAO;IACtC,IAAID,QAAQ,CAACX,cAAc,CAAC,KAAKL,YAAY,CAACiB,OAAO,EAAE;MACrD;IACF;IACA,IAAInB,QAAQ,CAACmB,OAAO,EAAE;MACpBnB,QAAQ,CAACmB,OAAO,GAAG,KAAK;MACxB;IACF;IACAnB,QAAQ,CAACmB,OAAO,GAAG,IAAI;IACvB,MAAMG,KAAK,GAAGJ,QAAQ,CAACX,cAAc,CAAC,GAAGI,WAAW;IACpDU,SAAS,CAACd,cAAc,CAAC,GAAGe,KAAK,GAAGN,kBAAkB;IACtDd,YAAY,CAACiB,OAAO,GAAGD,QAAQ,CAACX,cAAc,CAAC;EACjD,CAAC,CAAC;EACF,MAAMgB,iBAAiB,GAAG1D,gBAAgB,CAAC,MAAM;IAC/C,MAAMqD,QAAQ,GAAGpB,MAAM,CAACqB,OAAO,CAACC,kBAAkB,CAACD,OAAO;IAC1D,MAAME,SAAS,GAAGlB,YAAY,CAACgB,OAAO;IACtC,IAAInB,QAAQ,CAACmB,OAAO,EAAE;MACpBnB,QAAQ,CAACmB,OAAO,GAAG,KAAK;MACxB;IACF;IACAnB,QAAQ,CAACmB,OAAO,GAAG,IAAI;IACvB,MAAMG,KAAK,GAAGD,SAAS,CAACd,cAAc,CAAC,GAAGS,kBAAkB;IAC5DE,QAAQ,CAACX,cAAc,CAAC,GAAGe,KAAK,GAAGX,WAAW;EAChD,CAAC,CAAC;EACF7C,UAAU,CAAC,MAAM;IACf,MAAMoD,QAAQ,GAAGpB,MAAM,CAACqB,OAAO,CAACC,kBAAkB,CAACD,OAAO;IAC1D,MAAME,SAAS,GAAGlB,YAAY,CAACgB,OAAO;IACtCD,QAAQ,CAACM,gBAAgB,CAAC,QAAQ,EAAEP,gBAAgB,EAAE;MACpDQ,OAAO,EAAE;IACX,CAAC,CAAC;IACFJ,SAAS,CAACG,gBAAgB,CAAC,QAAQ,EAAED,iBAAiB,EAAE;MACtDE,OAAO,EAAE;IACX,CAAC,CAAC;IACF,OAAO,MAAM;MACXP,QAAQ,CAACQ,mBAAmB,CAAC,QAAQ,EAAET,gBAAgB,EAAE;QACvDQ,OAAO,EAAE;MACX,CAAC,CAAC;MACFJ,SAAS,CAACK,mBAAmB,CAAC,QAAQ,EAAEH,iBAAiB,EAAE;QACzDE,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;EACFnE,KAAK,CAACqE,SAAS,CAAC,MAAM;IACpB,MAAM/C,OAAO,GAAGwB,UAAU,CAACe,OAAO;IAClCvC,OAAO,CAACgD,KAAK,CAACC,WAAW,CAACvB,iBAAiB,EAAE,GAAGU,kBAAkB,IAAI,CAAC;EACzE,CAAC,EAAE,CAACA,kBAAkB,EAAEV,iBAAiB,CAAC,CAAC;EAC3C,MAAMwB,SAAS,GAAGlC,KAAK,CAACpB,QAAQ,KAAK,UAAU,GAAGQ,iBAAiB,GAAGQ,mBAAmB;EACzF,OAAO,aAAanB,IAAI,CAACyD,SAAS,EAAE;IAClCjC,GAAG,EAAElC,UAAU,CAACkC,GAAG,EAAEM,YAAY,CAAC;IAClC4B,SAAS,EAAEtD,OAAO,CAACE,IAAI;IACvBqD,QAAQ,EAAE,CAAC,CAAC;IACZ,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAE,aAAa5D,IAAI,CAAC,KAAK,EAAE;MACjCwB,GAAG,EAAEO,UAAU;MACf2B,SAAS,EAAEtD,OAAO,CAACG;IACrB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,SAASc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}